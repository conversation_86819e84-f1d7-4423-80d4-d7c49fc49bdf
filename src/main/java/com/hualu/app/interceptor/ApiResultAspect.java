package com.hualu.app.interceptor;

import com.hualu.app.comm.RestResult;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.mybatisplus.utils.MiniuiPage;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * 设置统一返回值
 */
@Slf4j
@Aspect
public class ApiResultAspect {

    @Pointcut("execution(* *..controller.*.*(..)) || @annotation(org.springframework.web.bind.annotation.ExceptionHandler)")
    public void resultAop(){}


    @Around("resultAop()")
    public Object arround(ProceedingJoinPoint pjp) throws Throwable{
        try{
            Object obj =  pjp.proceed();
            if (obj instanceof ApiResult){
                return initResult((ApiResult) obj);
            }else if (obj instanceof MiniuiPage){
                MiniuiPage page = (MiniuiPage) obj;
                RestResult result = RestResult.success("操作成功");
                result.setData(page.getData());
                result.setPage(page.getPageIndex());
                result.setPageSize(page.getPageSize());
                result.setTotal(page.getTotal());
                return result;
            }else if (obj instanceof  RestResult){
                return obj;
            }else{
                RestResult restResult = RestResult.success("操作成功");
                restResult.setData(obj);
                return restResult;
            }
        }catch (Exception e){
            e.printStackTrace();
            //log.error(e.getMessage(),e.getCause());
            //return RestResult.error(e.getMessage());
            throw new BaseException(e.getMessage());
        }
    }

    public RestResult initResult(ApiResult apiResult){
        RestResult restResult = null;
        if (apiResult.getCode() == 200){
            restResult = RestResult.success(apiResult.getMsg());
            restResult.setData(apiResult.getData());
        }else {
            restResult = RestResult.error(apiResult.getMsg());
            restResult.setCode(500==apiResult.getCode()?0: apiResult.getCode());
        }
        return restResult;
    }
}
