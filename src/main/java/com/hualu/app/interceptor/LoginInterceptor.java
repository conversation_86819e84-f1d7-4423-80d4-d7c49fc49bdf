package com.hualu.app.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.eos.workflow.api.BPSServiceClientFactory;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.utils.H_JWTHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.context.H_UserContextHelper;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Order(-1)
public class LoginInterceptor extends HandlerInterceptorAdapter {

    private static final String AUTHORIZATION = "Authorization";
    private static final String SA_TOKEN = "satoken";
    private static final String USER_CODE = "userCode";
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = getToken(request,AUTHORIZATION);
        String satoken = getToken(request,SA_TOKEN);
        if (StrUtil.isNotBlank(token)){
            appLogin(token,response);
        } else if (StrUtil.isNotBlank(satoken)) {
            saTokenLogin(request, response);
        } else {
            pcLogin(request,response);
        }
        //流程用户登录
        BPSServiceClientFactory.getLoginManager().setCurrentUser(CustomRequestContextHolder.getUserCode(), CustomRequestContextHolder.getUserName());
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CustomRequestContextHolder.remove();
        super.afterCompletion(request, response, handler, ex);
    }


    private String getToken(HttpServletRequest request,String tokenName){
        String token = request.getHeader(tokenName);
        if (StrUtil.isBlank(token)){
            token = request.getParameter(tokenName);
        }
        return token;
    }

    /**
     * app登录验证
     * @param token
     */
    private void appLogin(String token,HttpServletResponse response){
        Map unsign = H_JWTHelper.unsign(token, Map.class);
        if (unsign == null){
            response.addHeader("user_not_auth", "0");
            throw new BaseException(-2,"用户授权认证没有通过!");
        }
        //ORG_EN = YG DCCODE=10212
        CustomRequestContextHolder.setUserId(StrUtil.utf8Str(unsign.get("USER_ID")));
        CustomRequestContextHolder.setUserName(StrUtil.utf8Str(unsign.get("USER_NAME")));
        CustomRequestContextHolder.setUserCode(StrUtil.utf8Str(unsign.get("USER_CODE")));
        CustomRequestContextHolder.setOrgId(StrUtil.utf8Str(unsign.get("ORG_ID")));
        CustomRequestContextHolder.setOrgName(StrUtil.utf8Str(unsign.get("ORG_FULLNAME")));
        CustomRequestContextHolder.set("ORG_EN",StrUtil.utf8Str(unsign.get("ORG_EN")));
        CustomRequestContextHolder.set(C_Constant.DCCODE,StrUtil.utf8Str(unsign.get("DCCODE")));
    }

    /**
     * saToken登录验证
     * @param request
     * @param response
     */
    private void saTokenLogin(HttpServletRequest request,HttpServletResponse response){
        boolean login = StpUtil.isLogin();
        if (!login){
            response.addHeader("user_not_auth", "0");
            throw new BaseException(-2,"用户授权认证没有通过!客户端请求参数中无satoken信息");
        }
        H_UserContextHelper.initUserContext();
    }

    /**
     * pc端登录验证
     * @param request
     * @param response
     */
    private void pcLogin(HttpServletRequest request,HttpServletResponse response){
        String userCode = request.getParameter(USER_CODE);
        if (StrUtil.isBlank(userCode)){
            userCode = request.getHeader(USER_CODE);
        }
        if (StrUtil.isBlank(userCode)){
            response.addHeader("user_not_auth", "0");
            throw new BaseException(-2,"用户授权认证没有通过!客户端请求参数中无token信息");
        }
        //根据当前用户userId获取用户信息
        FwRightUserService bean = CustomApplicationContextHolder.getBean(FwRightUserService.class);
        UserLoginDto user = bean.loginUser(userCode);
        if (user == null){
            response.addHeader("user_not_auth", "0");
            throw new BaseException(-2,"用户授权认证没有通过!");
        }
        CustomRequestContextHolder.setUserId(user.getUserId());
        CustomRequestContextHolder.setUserName(user.getUserName());
        CustomRequestContextHolder.setUserCode(user.getUserCode());
        CustomRequestContextHolder.setOrgId(user.getOrgId());
        CustomRequestContextHolder.setOrgName(user.getOrgFullname());
        CustomRequestContextHolder.set("ORG_EN",user.getOrgEn());
        CustomRequestContextHolder.set(C_Constant.DCCODE,user.getDccode());
    }
}
