package com.hualu.app.interceptor;

import com.eos.workflow.api.BPSServiceClientFactory;
import com.eos.workflow.api.IBPSServiceClient;
import com.primeton.bps.disttrans.api.IClientGlobalTransactionManager;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;

/**
 * 流程事务控制
 */
@Order(2)
@Slf4j
@Aspect
public class BpsTransactionalAspect {

    @Pointcut("@annotation(com.hualu.app.module.workflow.anno.BpsTransactionalAnno)")
    public void bpsAop(){}

    @Around("bpsAop()")
    public Object arround(ProceedingJoinPoint pjp) throws Throwable{

        IBPSServiceClient bpsClient = BPSServiceClientFactory.getDefaultClient();
        // 获取分布式全局事务管理器
        IClientGlobalTransactionManager globalTxMgr = null;
        try {
            globalTxMgr = bpsClient.getClientGlobalTransactionManager();
            globalTxMgr.begin();
            Object obj =  pjp.proceed();
            globalTxMgr.commit();
            return obj;
        }catch (Exception e){
            globalTxMgr.rollback();
            log.error("流程回滚",e.getCause());
            throw new BaseException("流程回滚："+e.getMessage());
        }
    }
}
