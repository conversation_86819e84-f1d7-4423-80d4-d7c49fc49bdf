package com.hualu.app.cas;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.util.context.H_UserContextHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Slf4j
@Api(tags = "登录鉴权接口", value = "登录鉴权接口")
@RestController
@RequestMapping("/cas")
public class CasController {

    @Autowired
    CasProperties casProperties;

    @Autowired
    HttpServletRequest request;

    /**
     * 重定向到前端页面
     * @param
     */
    @RequestMapping("callback")
    @SneakyThrows
    public void casCallback(String ticket,HttpServletRequest request, HttpServletResponse response){
        StringBuffer requestURL = request.getRequestURL();
        System.out.println(requestURL.toString());
        // 只获取相对路径及查询参数，重定向给前端
        response.sendRedirect(getVueRedirectUrl(casProperties.getCallBackPagePrefixUrl())+"?ticket="+ticket);
    }

    @SneakyThrows
    @ApiRegister(value = "checkAuthStatus",businessType = BusinessType.LOGIN)
    @ApiOperation("验证用户是否还在登录中")
    @GetMapping("/checkAuthStatus" )
    @ApiResponses(@ApiResponse(code = 200, message = "success"))
    public RestResult<Object> checkAuthStatus(HttpServletRequest request, HttpServletResponse response){
        if (StpUtil.isLogin()){
            return RestResult.success("success");
        }
        throw new BaseException(-2,"用户未登录");
    }


    @ApiRegister(value = "getUserInfo",businessType = BusinessType.LOGIN)
    @ApiOperation("登录鉴权接口")
    @GetMapping("/getUserInfo" )
    public RestResult getUserInfo(String ticket,HttpServletResponse response,HttpServletRequest request){
        String userCode = CustomRequestContextHolder.getUserCode();
        if (StrUtil.isNotBlank(ticket)){
            String casserviceUrl = casProperties.getServiceValidateUrl() + "?service=" + getServiceRedirectUrl() + "&ticket=" + ticket;
            log.info("单点登录验证服务地址："+casserviceUrl);
            String xml = HttpUtil.get(casserviceUrl);
            Map<String, Object> xmMap = XmlUtil.xmlToMap(xml);
            Map successMap = (Map) xmMap.get("cas:authenticationSuccess");
            if (successMap != null){
                userCode = (String) successMap.get("cas:user");
            }
        }
        if (StrUtil.isNotBlank(userCode)){
            FwRightUserService bean = CustomApplicationContextHolder.getBean(FwRightUserService.class);
            UserLoginDto user = bean.loginUser(userCode);
            if (user == null){
                //throw new BaseException(403,"用户未授权");
                return new RestResult(403,"用户未授权!",null);
            }
            StpUtil.login(user.getUserCode());
            Map<String,Object> resMap = Maps.newHashMap();
            resMap.put("userCode",user.getUserCode());
            resMap.put("userName",user.getUserName());
            resMap.put("orgId",user.getOrgId());
            resMap.put("tokenValue",StpUtil.getTokenValue());


            H_UserContextHelper.setUserId(user.getUserId());
            H_UserContextHelper.setUserName(user.getUserName());
            H_UserContextHelper.setUserCode(user.getUserCode());
            H_UserContextHelper.setOrgName(user.getOrgFullname());
            H_UserContextHelper.set("currentOrgId",user.getOrgId());
            H_UserContextHelper.set("ORG_EN",StrUtil.isNotBlank(user.getOrgEn())?user.getOrgEn():"");
            //H_UserContextHelper.set(C_Constant.DCCODE,user.getDccode());
            //添加登录日志
            return RestResult.success(resMap);
        }
        response.addHeader("user_not_auth", "0");
        //throw new BaseException(-2,"用户授权认证没有通过!");
        return new RestResult(-2,"用户授权认证没有通过!",null);
    }

    /**
     * 转换目标地址
     * @param sourceUrl
     * @return
     */
    private String getVueRedirectUrl(String sourceUrl){
        log.info(casProperties.dynamicServer + "");
        if (casProperties.dynamicServer){
            return getProtocol()+"://" + request.getServerName() + ":" + request.getServerPort() + sourceUrl;
        }
        return sourceUrl;
    }

    private String getServiceRedirectUrl(){
        log.info(casProperties.dynamicServer + "");
        if (casProperties.dynamicServer){
            return getProtocol()+"://" + request.getServerName() + ":" + request.getServerPort() + casProperties.getClientPrefixUrl() + CasProperties.CLIENT_GROUP_SERVICE_URI;
        }
        return casProperties.getService();
    }


    /**
     * 获取协议
     * nginx转发层必须设置 X-Forwarded-Proto 请求头为原始请求的协议
     * proxy_set_header X-Forwarded-Proto $scheme;
     * 如果涉及到多个nginx转发，后面的nginx需设置  proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto
     * @return
     */
    private String getProtocol(){
        String protocol = ServletUtil.getHeader(request, "X-Forwarded-Proto", "UTF-8");
        System.out.println("protocol协议："+protocol+"            serverName():"+request.getServerName());
        if (StrUtil.isNotBlank(protocol)){
            return protocol;
        }
        return request.getScheme();
    }
}
