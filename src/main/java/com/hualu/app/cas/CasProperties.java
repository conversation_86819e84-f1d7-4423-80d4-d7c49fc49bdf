package com.hualu.app.cas;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

@Configuration
@Data
@ConfigurationProperties(prefix = "hualu.cas")
public class CasProperties implements Serializable {

    private static final String SERVER_SERVICE_VALIDATE_URI = "/serviceValidate";

    /**
     * 集团cas server回调的地址
     */
    public static final String CLIENT_GROUP_SERVICE_URI = "/cas/callback";

    /**
     * 后端将ticket返回给前端的地址
     */
    private static final String CALL_BACK_CLIENT_SERVICE_URI = "#/";

    Boolean dynamicServer = false;

    //是否启用单点登录配置
    Boolean casEnable;

    //前端vue链接地址
    String callBackPagePrefixUrl;

    //cas 登录地址
    String clientPrefixUrl;

    //后端java项目地址
    String serverPrefixUrl;

    //ticket 校验地址
    String serviceValidateUrl;


    /**
     * 获取校验ticket的url
     *
     * @return
     */
    public String getServiceValidateUrl() {
        return this.serverPrefixUrl + SERVER_SERVICE_VALIDATE_URI;
    }

    /**
     * 获取service
     *
     * @return
     */
    public String getService() {
        String serviceUri = CLIENT_GROUP_SERVICE_URI;
        return this.clientPrefixUrl + serviceUri;
    }

    /**
     * 获取后端将ticket返回给前端的地址
     *
     * @return
     */
    public String getCallBackPagePrefixUrl() {
        return this.callBackPagePrefixUrl;
    }
}
