package com.hualu.app;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import io.micrometer.core.instrument.MeterRegistry;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;

@EnableRabbit
@EnableScheduling
@EnableTransactionManagement
@SpringBootApplication(exclude={DruidDataSourceAutoConfigure.class})
@MapperScan({"com.hualu.**.mapper"})
public class HighwayMemsApplication {

    public static void main(String[] args)
    {
        SpringApplication.run(HighwayMemsApplication.class, args);
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName) {
        return registry -> registry.config().commonTags("application", applicationName);
    }
}