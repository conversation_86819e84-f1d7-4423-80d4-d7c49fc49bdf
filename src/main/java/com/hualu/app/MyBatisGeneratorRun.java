package com.hualu.app;


import com.tg.dev.mybatisplus.generator.OracleGenerator;
import org.junit.Test;

/**
 *  运行此方法生成mybatis代码
 *  生成代码自动放入对应目录
 * <AUTHOR>
 * @create 2019/10/18
 */
public class MyBatisGeneratorRun {

     @Test
     public void generateOracleCode() {
          //作者名称
          String author  = "ttg";
          //数据库连接地址
          String dbUrl = "******************************************";
          //账号名称
          String username = "memsdb";
          //账号密码
          String password ="Mems!@#1234";
          //生成包名地址
          String packageName = "com.hualu.app.module.mems.nminsp";
          //生成表集合
          String[] tableNames = new String[]{"NM_FINSP"};
          OracleGenerator generator = new OracleGenerator(author,dbUrl,username,password,packageName);
          generator.setTableNames(tableNames);
          generator.setCustomCreate(true);
          //generator.setCustomGen(true);
          //generator.generateCode();
          generator.generateEntityCode();
     }
}