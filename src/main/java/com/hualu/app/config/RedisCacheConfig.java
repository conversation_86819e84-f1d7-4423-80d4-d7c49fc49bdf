package com.hualu.app.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

@Configuration
@EnableCaching
public class RedisCacheConfig {
    
    // 缓存的前缀
    public static final String SITUATION_CACHE_PREFIX = "situation";
    // 缓存的key：日常巡查情况
    public static final String DINSP_SITUATION_CACHE_KEY = SITUATION_CACHE_PREFIX + ":dinsp:%s:%d";
    // 缓存的key：经常检查情况
    public static final String FINSP_SITUATION_CACHE_KEY = SITUATION_CACHE_PREFIX + ":finsp:%s:%d";
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        // 设置缓存有效期为30分钟
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();
                
        return RedisCacheManager.builder(factory)
                .cacheDefaults(config)
                .build();
    }
} 