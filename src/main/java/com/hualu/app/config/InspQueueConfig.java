package com.hualu.app.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 巡查单队列
 */
@Configuration
public class InspQueueConfig {
    // 巡查单队列相关配置
    public static final String DINSP_QUEUE_NAME = "dinspQueue";
    public static final String DINSP_DEAD_LETTER_QUEUE_NAME = "dinspDeadLetterQueue";
    public static final String DINSP_EXCHANGE_NAME = "dinspExchange";
    public static final String DINSP_DEAD_LETTER_EXCHANGE_NAME = "dinspDeadLetterExchange";
    public static final String DINSP_ROUTING_KEY = "dinspRoutingKey";
    public static final String DINSP_DEAD_LETTER_ROUTING_KEY = "dinspDeadLetterRoutingKey";

    // 经常检查单队列相关配置
    public static final String FINSP_QUEUE_NAME = "finspQueue";
    public static final String FINSP_DEAD_LETTER_QUEUE_NAME = "finspDeadLetterQueue";
    public static final String FINSP_EXCHANGE_NAME = "finspExchange";
    public static final String FINSP_DEAD_LETTER_EXCHANGE_NAME = "finspDeadLetterExchange";
    public static final String FINSP_ROUTING_KEY = "finspRoutingKey";
    public static final String FINSP_DEAD_LETTER_ROUTING_KEY = "finspDeadLetterRoutingKey";

    //GPS队列相关配置
    public static final String GPS_QUEUE_NAME = "gpsQueue";
    public static final String GPS_DEAD_LETTER_QUEUE_NAME = "gpsDeadLetterQueue";
    public static final String GPS_EXCHANGE_NAME = "gpsExchange";
    public static final String GPS_DEAD_LETTER_EXCHANGE_NAME = "gpsDeadLetterExchange";
    public static final String GPS_ROUTING_KEY = "gpsRoutingKey";
    public static final String GPS_DEAD_LETTER_ROUTING_KEY = "gpsDeadLetterRoutingKey";

    // 优先级设置
    public static final int RETRY_COUNT = 1;

    // 优先级别
    private static final int MAX_PRIORITY = 2;

    // 死信存活时间（毫秒）
    public static final long RETRY_INTERVAL = 5000;

    // 巡查单正常队列
    @Bean
    public Queue dinspQueue() {
        return QueueBuilder.durable(DINSP_QUEUE_NAME)
                .withArgument("x-dead-letter-exchange", DINSP_DEAD_LETTER_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", DINSP_DEAD_LETTER_ROUTING_KEY)
                .withArgument("x-max-priority", MAX_PRIORITY)
                .build();
    }

    // 巡查单死信队列
    @Bean
    public Queue dinspDeadLetterQueue() {
        return QueueBuilder.durable(DINSP_DEAD_LETTER_QUEUE_NAME)
                .withArgument("x-dead-letter-exchange", DINSP_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", DINSP_ROUTING_KEY)
                .build();
    }

    // 巡查单正常交换机
    @Bean
    public DirectExchange dinspExchange() {
        return new DirectExchange(DINSP_EXCHANGE_NAME);
    }

    // 巡查单死信交换机
    @Bean
    public DirectExchange dinspDeadLetterExchange() {
        return new DirectExchange(DINSP_DEAD_LETTER_EXCHANGE_NAME);
    }

    // 巡查单正常队列与正常交换机绑定
    @Bean
    public Binding dinspBinding(Queue dinspQueue, DirectExchange dinspExchange) {
        return BindingBuilder.bind(dinspQueue).to(dinspExchange).with(DINSP_ROUTING_KEY);
    }

    // 巡查单死信队列与死信交换机绑定
    @Bean
    public Binding dinspDeadLetterBinding(Queue dinspDeadLetterQueue, DirectExchange dinspDeadLetterExchange) {
        return BindingBuilder.bind(dinspDeadLetterQueue).to(dinspDeadLetterExchange).with(DINSP_DEAD_LETTER_ROUTING_KEY);
    }

    // 经常检查单正常队列
    @Bean
    public Queue finspQueue() {
        return QueueBuilder.durable(FINSP_QUEUE_NAME)
                .withArgument("x-dead-letter-exchange", FINSP_DEAD_LETTER_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", FINSP_DEAD_LETTER_ROUTING_KEY)
                .build();
    }

    // 经常检查单死信队列
    @Bean
    public Queue finspDeadLetterQueue() {
        return QueueBuilder.durable(FINSP_DEAD_LETTER_QUEUE_NAME)
                .withArgument("x-dead-letter-exchange", FINSP_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", FINSP_ROUTING_KEY)
                .build();
    }

    // 经常检查单正常交换机
    @Bean
    public DirectExchange finspExchange() {
        return new DirectExchange(FINSP_EXCHANGE_NAME);
    }

    // 经常检查单死信交换机
    @Bean
    public DirectExchange finspDeadLetterExchange() {
        return new DirectExchange(FINSP_DEAD_LETTER_EXCHANGE_NAME);
    }

    // 经常检查单正常队列与正常交换机绑定
    @Bean
    public Binding finspBinding(Queue finspQueue, DirectExchange finspExchange) {
        return BindingBuilder.bind(finspQueue).to(finspExchange).with(FINSP_ROUTING_KEY);
    }

    // 经常检查单死信队列与死信交换机绑定
    @Bean
    public Binding finspDeadLetterBinding(Queue finspDeadLetterQueue, DirectExchange finspDeadLetterExchange) {
        return BindingBuilder.bind(finspDeadLetterQueue).to(finspDeadLetterExchange).with(FINSP_DEAD_LETTER_ROUTING_KEY);
    }

    // GPS正常队列
    @Bean
    public Queue gpsQueue() {
        return QueueBuilder.durable(GPS_QUEUE_NAME)
                .withArgument("x-dead-letter-exchange", GPS_DEAD_LETTER_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", GPS_DEAD_LETTER_ROUTING_KEY)
                .build();
    }

    // GPS死信队列
    @Bean
    public Queue gpsDeadLetterQueue() {
        return QueueBuilder.durable(GPS_DEAD_LETTER_QUEUE_NAME)
                .withArgument("x-dead-letter-exchange", GPS_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", GPS_ROUTING_KEY)
                .build();
    }

    // GPS正常交换机
    @Bean
    public DirectExchange gpsExchange() {
        return new DirectExchange(GPS_EXCHANGE_NAME);
    }

    // GPS死信交换机
    @Bean
    public DirectExchange gpsDeadLetterExchange() {
        return new DirectExchange(GPS_DEAD_LETTER_EXCHANGE_NAME);
    }

    // GPS正常队列与正常交换机绑定
    @Bean
    public Binding gpsBinding(Queue gpsQueue, DirectExchange gpsExchange) {
        return BindingBuilder.bind(gpsQueue).to(gpsExchange).with(GPS_ROUTING_KEY);
    }

    // GPS死信队列与死信交换机绑定
    @Bean
    public Binding gpsDeadLetterBinding(Queue gpsDeadLetterQueue, DirectExchange gpsDeadLetterExchange) {
        return BindingBuilder.bind(gpsDeadLetterQueue).to(gpsDeadLetterExchange).with(GPS_DEAD_LETTER_ROUTING_KEY);
    }

    @Bean(name = "gpsQueueContainerFactory")
    public SimpleRabbitListenerContainerFactory batchConsumeFactory(ConnectionFactory connectionFactory, MessageConverter messageConverter) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.AUTO);
        factory.setAutoStartup(true);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setPrefetchCount(1);
        //factory.setMessageConverter(messageConverter);
        // 设置预取计数为 500
        //factory.setPrefetchCount(100);
        // 开启手动确认
        //factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }
}    