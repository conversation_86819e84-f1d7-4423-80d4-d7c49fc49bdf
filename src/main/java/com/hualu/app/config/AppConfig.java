package com.hualu.app.config;

import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
import com.hualu.app.interceptor.ApiResultAspect;
import com.hualu.app.interceptor.BpsTransactionalAspect;
import com.hualu.app.interceptor.LoginInterceptor;
import com.hualu.app.utils.H_BasedataHepler;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class AppConfig implements WebMvcConfigurer {

    @Value("${fileReposity}")
    private String fileReposity;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        String[] swaggerExcluds = new String[]{"/appUpdate/**","/fileResposity/**","/swagger-ui.html","/swagger-resources/**","/webjars/**"
                ,"/**/previewImage/**","/**/previewFile/**","/"+ H_BasedataHepler.HUALU_API +"/**","/error","/cas/**","/backup/**"
                ,"/admin/**","/","actuator/**","/instances/**"};
        registry.addInterceptor(getLoginInterceptor()).addPathPatterns("/**").excludePathPatterns(swaggerExcluds).order(0);
        WebMvcConfigurer.super.addInterceptors(registry);
    }

    @Override public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/appUpdate/**").addResourceLocations("file:c:/highwayApp/apk/");
        registry.addResourceHandler("/fileResposity/**").addResourceLocations("file:"+fileReposity);
        WebMvcConfigurer.super.addResourceHandlers(registry);
    }

    @Bean
    public LoginInterceptor getLoginInterceptor(){
        return new LoginInterceptor();
    }

    @Bean
    public ApiResultAspect getApiResultInterceptor(){
        return new ApiResultAspect();
    }

    @Bean
    public BpsTransactionalAspect getBpsTranscationAspect(){return new BpsTransactionalAspect();}

    /**
     * 配置逻辑删除插件
     * @return
     */
    @Bean
    public ISqlInjector sqlInjector(){
        return new LogicSqlInjector();
    }
}
