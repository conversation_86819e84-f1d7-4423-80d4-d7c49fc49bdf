package com.hualu.app.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 巡查情况MQ配置
 */
@Configuration
public class SituationMQConfig {

    // 队列名称
    public static final String SITUATION_REFRESH_QUEUE = "situation.refresh.queue";
    
    // 交换机名称
    public static final String SITUATION_REFRESH_EXCHANGE = "situation.refresh.exchange";
    
    // 路由键
    public static final String SITUATION_REFRESH_ROUTING_KEY = "situation.refresh";
    
    @Autowired
    private MessageConverter messageConverter;
    
    /**
     * 创建队列
     */
    @Bean
    public Queue situationRefreshQueue() {
        return QueueBuilder.durable(SITUATION_REFRESH_QUEUE).build();
    }
    
    /**
     * 创建交换机
     */
    @Bean
    public DirectExchange situationRefreshExchange() {
        return new DirectExchange(SITUATION_REFRESH_EXCHANGE, true, false);
    }
    
    /**
     * 绑定队列和交换机
     */
    @Bean
    public Binding situationRefreshBinding(Queue situationRefreshQueue, DirectExchange situationRefreshExchange) {
        return BindingBuilder.bind(situationRefreshQueue)
                .to(situationRefreshExchange)
                .with(SITUATION_REFRESH_ROUTING_KEY);
    }
    
    /**
     * 为刷新缓存队列创建专用的消费者容器工厂，限制消息消费速率
     */
    @Bean
    public SimpleRabbitListenerContainerFactory situationRefreshContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        // 设置预取值为1，每次只消费一条消息
        factory.setPrefetchCount(1);
        // 设置并发消费者数量为1
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        // 设置消费者自动启动
        factory.setAutoStartup(true);
        // 设置消息确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.AUTO);
        return factory;
    }
} 