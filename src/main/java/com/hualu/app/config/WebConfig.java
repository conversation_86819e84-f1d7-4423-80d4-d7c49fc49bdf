package com.hualu.app.config;

import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 映射逻辑路径 "/external-img/**" 到JAR内部资源目录 "classpath:/images/"
        registry.addResourceHandler("/external-img/**")
                .addResourceLocations("classpath:/images/");  // :ml-citation{ref="9" data="citationList"}
    }

}
