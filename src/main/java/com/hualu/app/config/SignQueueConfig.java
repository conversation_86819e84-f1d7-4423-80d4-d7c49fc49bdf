package com.hualu.app.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SignQueueConfig {

    @Value("${spring.rabbitmq.host:localhost}")
    private String host;

    @Value("${spring.rabbitmq.port:5672}")
    private int port;

    @Value("${spring.rabbitmq.username:guest}")
    private String username;

    @Value("${spring.rabbitmq.password:guest}")
    private String password;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    // 队列名称
    private static final String SIGN_TASK_QUEUE = "sign.task.queue";
    private static final String SIGN_RETRY_QUEUE = "sign.retry.queue";
    private static final String SIGN_DEAD_QUEUE = "sign.dead.queue";

    // 交换机名称
    private static final String SIGN_EXCHANGE = "sign.exchange";
    private static final String SIGN_RETRY_EXCHANGE = "sign.retry.exchange";
    private static final String SIGN_DEAD_EXCHANGE = "sign.dead.exchange";

    @Bean
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory factory = new CachingConnectionFactory();
        factory.setHost(host);
        factory.setPort(port);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);
        // 设置连接超时
        factory.setConnectionTimeout(30000);
        return factory;
    }

    @Bean
    public MessageConverter jsonMessageConverter() {
        /*ObjectMapper objectMapper = new ObjectMapper();
        // 注册 JavaTimeModule
        objectMapper.registerModule(new JavaTimeModule());
        // 禁用对日期时间类型使用时间戳格式
        objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        return new Jackson2JsonMessageConverter(objectMapper);*/
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(jsonMessageConverter());
        return template;
    }

    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(jsonMessageConverter());
        // 设置并发消费者数量
        factory.setConcurrentConsumers(3);
        factory.setMaxConcurrentConsumers(5);
        // 设置消费者自动启动
        factory.setAutoStartup(true);
        // 设置消息确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.AUTO);
        return factory;
    }

    // 主任务队列
    @Bean
    public Queue signTaskQueue() {
        return QueueBuilder.durable(SIGN_TASK_QUEUE)
                .withArgument("x-dead-letter-exchange", SIGN_RETRY_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", SIGN_RETRY_QUEUE)
                .build();
    }

    // 重试队列
    @Bean
    public Queue signRetryQueue() {
        return QueueBuilder.durable(SIGN_RETRY_QUEUE)
                .withArgument("x-dead-letter-exchange", SIGN_DEAD_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", SIGN_DEAD_QUEUE)
                .withArgument("x-message-ttl", 5000) // 5秒后重试
                .build();
    }

    // 死信队列
    @Bean
    public Queue signDeadQueue() {
        return QueueBuilder.durable(SIGN_DEAD_QUEUE).build();
    }

    // 交换机
    @Bean
    public DirectExchange signTaskExchange() {
        return new DirectExchange("sign.task.exchange", true, false);
    }

    @Bean
    public DirectExchange signRetryExchange() {
        return new DirectExchange("sign.retry.exchange", true, false);
    }

    @Bean
    public DirectExchange signDeadExchange() {
        return new DirectExchange("sign.dead.exchange", true, false);
    }

    // 绑定关系
    @Bean
    public Binding signTaskBinding() {
        return BindingBuilder.bind(signTaskQueue())
                .to(signTaskExchange())
                .with(SIGN_TASK_QUEUE);
    }

    @Bean
    public Binding signRetryBinding() {
        return BindingBuilder.bind(signRetryQueue())
                .to(signRetryExchange())
                .with(SIGN_RETRY_QUEUE);
    }

    @Bean
    public Binding signDeadBinding() {
        return BindingBuilder.bind(signDeadQueue())
                .to(signDeadExchange())
                .with(SIGN_DEAD_QUEUE);
    }
} 