package com.hualu.app.module.workflow.type;

import com.hualu.app.module.workflow.service.OrderInfoHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 各类单服务枚举
 */
public enum ServiceEnum {
    DINSP("gdcg.emdc.mems.dm.DInspWorkFlow", "dmDinspServiceImpl"),
    FINSP("gdcg.emdc.mems.dm.DFinspWorkFlow", "dmFinspServiceImpl"),
    NOTICE("gdcg.emdc.mems.dm.DmNoticeWorkFlow", "dmNoticeServiceImpl"),
    TASK("gdcg.emdc.mems.dm.DmTaskWorkflow", "dmTaskServiceImpl"),
    ACCPT("gdcg.emdc.mems.dm.DmTaskAcceptWorkFlow", "dmTaskAccptServiceImpl"),
    NMDINSP("gdcg.emdc.mems.dm.NDinspWorkFlow", "nmDinspServiceImpl"),
    NMFINSP("gdcg.emdc.mems.dm.NFinspWorkFlow", "nmFinspServiceImpl");

    private final String code;
    private final String beanName;
    private static Map<String, OrderInfoHandler> handlerMap = new HashMap<>();

    ServiceEnum(String code, String beanName) {
        this.code = code;
        this.beanName = beanName;
    }

    // 通过 Spring 的依赖注入初始化 handlerMap
    @Component
    public static class ServiceEnumInjector {
        private final Map<String, OrderInfoHandler> handlerBeans;

        @Autowired
        public ServiceEnumInjector(Map<String, OrderInfoHandler> handlerBeans) {
            this.handlerBeans = handlerBeans;
        }

        @PostConstruct
        public void init() {
            for (ServiceEnum service : ServiceEnum.values()) {
                OrderInfoHandler handler = handlerBeans.get(service.beanName);
                if (handler == null) {
                    throw new IllegalStateException("Bean not found: " + service.beanName);
                }
                handlerMap.put(service.code, handler);
            }
        }
    }

    public static ServiceEnum fromCode(String code) {
        for (ServiceEnum service : values()) {
            if (service.code.equalsIgnoreCase(code)) {
                return service;
            }
        }
        throw new IllegalArgumentException("未知的服务标识符: " + code);
    }

    public OrderInfoHandler getHandler() {
        return handlerMap.get(this.code);
    }
}