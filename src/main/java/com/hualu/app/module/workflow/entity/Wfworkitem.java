package com.hualu.app.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Wfworkitem对象", description="")
public class Wfworkitem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("WORKITEMID")
    private Long workitemid;

    @TableField("WORKITEMNAME")
    private String workitemname;

    @TableField("WORKITEMTYPE")
    private String workitemtype;

    @TableField("WORKITEMDESC")
    private String workitemdesc;

    @TableField("CURRENTSTATE")
    private Integer currentstate;

    @TableField("PARTICIPANT")
    private String participant;

    @TableField("PARTINAME")
    private String partiname;

    @TableField("PRIORITY")
    private Integer priority;

    @TableField("ISTIMEOUT")
    private String istimeout;

    @TableField("LIMITNUM")
    private Double limitnum;

    @TableField("LIMITNUMDESC")
    private String limitnumdesc;

    @TableField("CREATETIME")
    private LocalDateTime createtime;

    @TableField("STARTTIME")
    private LocalDateTime starttime;

    @TableField("ENDTIME")
    private LocalDateTime endtime;

    @TableField("FINALTIME")
    private LocalDateTime finaltime;

    @TableField("REMINDTIME")
    private LocalDateTime remindtime;

    @TableField("ACTIONURL")
    private String actionurl;

    @TableField("PROCESSINSTID")
    private Long processinstid;

    @TableField("ACTIVITYINSTID")
    private Long activityinstid;

    @TableField("STATESLIST")
    private String stateslist;

    @TableField("TIMEOUTNUM")
    private Long timeoutnum;

    @TableField("TIMEOUTNUMDESC")
    private String timeoutnumdesc;

    @TableField("PROCESSINSTNAME")
    private String processinstname;

    @TableField("ACTIVITYINSTNAME")
    private String activityinstname;

    @TableField("PROCESSDEFID")
    private Long processdefid;

    @TableField("PROCESSDEFNAME")
    private String processdefname;

    @TableField("PROCESSCHNAME")
    private String processchname;

    @TableField("ACTIVITYDEFID")
    private String activitydefid;

    @TableField("ASSISTANT")
    private String assistant;

    @TableField("ASSISTANTNAME")
    private String assistantname;

    @TableField("BIZSTATE")
    private Long bizstate;

    @TableField("ALLOWAGENT")
    private String allowagent;

    @TableField("ROOTPROCINSTID")
    private Long rootprocinstid;

    @TableField("ACTIONMASK")
    private String actionmask;

    @TableField("URLTYPE")
    private String urltype;

    @TableField("DEALRESULT")
    private String dealresult;

    @TableField("DEALOPINION")
    private String dealopinion;

    @TableField("EXTEND1")
    private String extend1;

    @TableField("EXTEND2")
    private String extend2;

    @TableField("EXTEND3")
    private String extend3;

    @TableField("EXTEND4")
    private String extend4;

    @TableField("EXTEND5")
    private String extend5;

    @TableField("EXTEND6")
    private String extend6;

    @TableField("EXTEND7")
    private Double extend7;

    @TableField("CATALOGUUID")
    private String cataloguuid;

    @TableField("CATALOGNAME")
    private String catalogname;

    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;


}
