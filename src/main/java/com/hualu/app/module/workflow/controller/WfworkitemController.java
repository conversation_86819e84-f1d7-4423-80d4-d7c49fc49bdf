package com.hualu.app.module.workflow.controller;


import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.platform.entity.FwRightUser;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.dto.WorkActDto;
import com.hualu.app.module.workflow.dto.WorkApprovalDto;
import com.hualu.app.module.workflow.service.WfworkitemService;
import com.hualu.app.module.workflow.type.ServiceEnum;
import com.hualu.app.utils.mems.SignUtils;
import com.tg.dev.api.context.CustomRequestContextHolder;
import io.swagger.annotations.Api;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Api("流程_工作项")
@RestController
@RequestMapping("/wfworkitem")
public class WfworkitemController {


    @Autowired
    FwRightUserService userService;

    @Autowired
    WfworkitemService wfService;

    @Autowired
    FwRightOrgService orgService;

    @Autowired
    private SignUtils signUtils;

    /**
     * 查询审批意见
     * @param processInstId 流程实例ID
     * @return
     */
    @GetMapping("selectApproval")
    public RestResult<List<WorkApprovalDto>> selectApproval(String processInstId){
        List<WorkApprovalDto> workApprovalDtos = wfService.selectApproval(processInstId);
        return RestResult.success(workApprovalDtos);
    }

    /**
     * 流程审批（通用审批）
     * @param processInstId 流程实例ID
     * @param nextAction 下一位岗位
     * @param nextUserIds 下一节点审批人
     * @param apprOpinion 审批意见
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @BpsTransactionalAnno
    @PostMapping("submitApprove")
    public RestResult<String> submitApprove(String processInstId,
                                            String nextAction, String nextUserIds,@RequestParam(required = false) String apprOpinion
            ,@RequestParam(required = false) String appvOpinion){

        String enNextAction = H_WorkFlowHelper.getActName(nextAction);
        Set<Long> processInstIdSet = StrUtil.split(processInstId, ",").stream()
                .map(Long::parseLong)
                .collect(Collectors.toSet());
        //临时审批意见
        String tempOpinion = StrUtil.isNotBlank(apprOpinion)?apprOpinion:appvOpinion;
        String userCode = CustomRequestContextHolder.getUserCode();
        processInstIdSet.forEach(instId->{
            String processDefName = H_WorkFlowHelper.submitWorkItem(instId, enNextAction, nextUserIds, tempOpinion);
            if(orgService.isAuth(CustomRequestContextHolder.getOrgIdString())){
                List<OrderInfo> infos = ServiceEnum.fromCode(processDefName).getHandler().queryOrderInfo(instId);
                OrderInfo o = infos.get(0);
                Integer xcType = o.getXcType();
                if(null != xcType && xcType == 2){
                    return;
                }
                if(infos.size() > 1){
                    infos.forEach(orderInfo -> {
                        signUtils.asyncBatchPdfSign(orderInfo.getOrderId(), orderInfo.getCat(),
                            orderInfo.getOrderType(), orderInfo.getStatus().toString(), userCode);
                    });
                }else{
                    OrderInfo orderInfo = infos.get(0);
                    signUtils.syncPdfSign(orderInfo.getOrderId(), orderInfo.getCat(),
                        orderInfo.getOrderType(), orderInfo.getStatus().toString());
                }
            }
        });
        return RestResult.success("办理成功");
    }

    /**
     * 查询流程审批用户
     * @param roleName 岗位名称（角色名称）
     * @param userName 用户名称(模糊查询使用)
     * @return
     */
    @PostMapping("selectBpsUsers")
    public RestResult<List<FwRightUser>> selectBpsUser(@RequestParam(value = "roleName",required = true) String roleName,
                                                       @RequestParam(value = "userName",required = false) String userName){
        //获取数据库真实流程名称
        String actName = H_WorkFlowHelper.getActName(roleName);
        List<FwRightUser> users = userService.selectBpsUserByRoleName(CustomRequestContextHolder.getOrgIdString(), actName, userName);
        return RestResult.success(users);
    }
    /**
     * 查询下一岗
     * @param processInstId 流程ID
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/loadNextActivitys")
    public RestResult<List<WorkActDto>> loadNextActivitys(@RequestParam(value="processInstID") long processInstId) throws Exception {
        Map<String,String> activityMap = H_WorkFlowHelper.activityMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue,Map.Entry::getKey));

        int i = H_WorkFlowHelper.getHideStatus();

        List<Map> map = H_WorkFlowHelper.getNextActivitys(processInstId);

        if (i == 1) {//如果为施工或养护单位，则去除病害入库、部门领导审核、分管领导审核节点
            for (int m = 0; m < map.size(); m++) {
                if (map.get(m).get("id").equals("病害入库")
                        || map.get(m).get("id").equals("部门领导审核")
                        || map.get(m).get("id").equals("分管领导审核")) {
                    map.remove(map.get(m));
                    m--;
                }
            }
        }

        for (int n = 0; n < map.size(); n++) {
            if (activityMap.get(map.get(n).get("name")) != null) {
                map.get(n).put("id", activityMap.get(map.get(n).get("name")));
            }

        }
        //养护计划退回到经办人，添加同意选项
        if (i == 5 && map.size() == 1) {
            Map m = new HashMap<String, Object>();
            m.put("isSelectUser", "0");
            m.put("id", "return");
            m.put("name", "同意");
            map.add(m);
        }
        List<WorkActDto> actDtos = Lists.newArrayList();
        map.forEach(item->{
            WorkActDto dto = new WorkActDto();
            dto.setIsSelcetUser(item.get("isSelectUser").toString());
            dto.setId(item.get("id").toString());
            dto.setName(item.get("name").toString());
            actDtos.add(dto);
        });

        return  RestResult.success(actDtos);
    }
}
