<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.workflow.mapper.WfworkflowstepMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.workflow.entity.Wfworkflowstep">
        <id column="STEPID" property="stepid" />
        <result column="BUSINESSID" property="businessid" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="WORKITEMID" property="workitemid" />
        <result column="APPROVESTATUS" property="approvestatus" />
        <result column="APPROVESUGGESTION" property="approvesuggestion" />
        <result column="APPROVER" property="approver" />
        <result column="APPROVETIME" property="approvetime" />
        <result column="APPROVEDEPTID" property="approvedeptid" />
        <result column="APPROVEDEPTNAME" property="approvedeptname" />
        <result column="REMARK" property="remark" />
        <result column="BUSINESSTABLENAME" property="businesstablename" />
        <result column="BUSINESSDETAILURL" property="businessdetailurl" />
        <result column="CURRENTOPERATIME" property="currentoperatime" />
        <result column="RECIVEUSER" property="reciveuser" />
        <result column="DATASOURCE" property="datasource" />
        <result column="WORKITEMNAME" property="workitemname" />
        <result column="BUSINESSDEALURL" property="businessdealurl" />
        <result column="BUSINESSNODEURL" property="businessnodeurl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        STEPID, BUSINESSID, PROCESSINSTID, WORKITEMID, APPROVESTATUS, APPROVESUGGESTION, APPROVER, APPROVETIME, APPROVEDEPTID, APPROVEDEPTNAME, REMARK, BUSINESSTABLENAME, BUSINESSDETAILURL, CURRENTOPERATIME, RECIVEUSER, DATASOURCE, WORKITEMNAME, BUSINESSDEALURL, BUSINESSNODEURL
    </sql>

</mapper>
