package com.hualu.app.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Wfprocessinst对象", description="")
public class Wfprocessinst implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("PROCESSINSTID")
    private Long processinstid;

    @TableField("PROCESSINSTNAME")
    private String processinstname;

    @TableField("PROCESSINSTDESC")
    private String processinstdesc;

    @TableField("CREATOR")
    private String creator;

    @TableField("OWNER")
    private String owner;

    @TableField("CURRENTSTATE")
    private Integer currentstate;

    @TableField("PRIORITY")
    private Integer priority;

    @TableField("RELATEDATA")
    private String relatedata;

    @TableField("RELATEDATAVCHR")
    private String relatedatavchr;

    @TableField("LIMITNUM")
    private Long limitnum;

    @TableField("LIMITNUMDESC")
    private String limitnumdesc;

    @TableField("CREATETIME")
    private LocalDateTime createtime;

    @TableField("STARTTIME")
    private LocalDateTime starttime;

    @TableField("ENDTIME")
    private LocalDateTime endtime;

    @TableField("FINALTIME")
    private LocalDateTime finaltime;

    @TableField("REMINDTIME")
    private LocalDateTime remindtime;

    @TableField("PARENTPROCID")
    private Long parentprocid;

    @TableField("PARENTACTID")
    private Long parentactid;

    @TableField("PROCESSDEFID")
    private Long processdefid;

    @TableField("ISTIMEOUT")
    private String istimeout;

    @TableField("TIMEOUTNUM")
    private Long timeoutnum;

    @TableField("TIMEOUTNUMDESC")
    private String timeoutnumdesc;

    @TableField("UPDATEVERSION")
    private String updateversion;

    @TableField("PROCESSDEFNAME")
    private String processdefname;

    @TableField("EXTEND1")
    private String extend1;

    @TableField("EXTEND2")
    private String extend2;

    @TableField("EXTEND3")
    private String extend3;

    @TableField("EXTEND4")
    private String extend4;

    @TableField("EXTEND5")
    private String extend5;

    @TableField("EXTEND6")
    private String extend6;

    @TableField("EXTEND7")
    private Long extend7;

    @TableField("CATALOGUUID")
    private String cataloguuid;

    @TableField("CATALOGNAME")
    private String catalogname;

    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;


}
