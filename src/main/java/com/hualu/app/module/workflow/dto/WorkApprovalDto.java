package com.hualu.app.module.workflow.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 流程办理信息
 */
@Data
public class WorkApprovalDto implements Serializable {

    private Long workitemid;//工作项ID

    private String workitemname;//节点名称

    private String approvedeptname;//部门名称

    private String partiname;//办理人姓名

    private String orgname;//公司名称

    private String approvesuggestion;//审批意见

    private String processinstname;//流程实例名称

    private String currentstate;//流程状态

    private String startTime;//接收时间

    private String endTime;//办理时间
}
