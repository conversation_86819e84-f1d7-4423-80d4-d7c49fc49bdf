package com.hualu.app.module.workflow.service;

import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 流程事件处理接口类
 */
public interface IWorkItemEventHandler {

    /**
     * 设置流程的
     * @return
     */
    Map<String,String> initAction();

    /**
     * 返回操作节点步骤
     * @param activityDefId 活动定义ID
     * @param actionName 下一审批流程步骤
     * @return
     */
    default String getAction(String activityDefId,String actionName){
        return initAction().get(activityDefId+actionName);
    }

    /**
     * 流程定义名称
     * @return
     */
    String getProcessDefName();

    /**
     * 流程事件方法
     * @param processInstId 流程实例ID
     * @param isEnd 是否办结
     * @param nextAction 下一个节点操作
     * @param actId 活动定义ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void savedoEvent(long processInstId,boolean isEnd,String nextAction,String actId);
}
