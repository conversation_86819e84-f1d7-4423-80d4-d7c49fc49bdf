package com.hualu.app.module.workflow.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.workflow.dto.WorkApprovalDto;
import com.hualu.app.module.workflow.dto.WorkPartinameDto;
import com.hualu.app.module.workflow.entity.Wfworkitem;
import com.hualu.app.module.workflow.mapper.WfworkitemMapper;
import com.hualu.app.module.workflow.service.WfworkitemService;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@DS("bpsDs")
@Service
public class WfworkitemServiceImpl extends ServiceImpl<WfworkitemMapper, Wfworkitem> implements WfworkitemService {

    final String YHDW_DEF_ID = "manualActivity1";
    final String leaderTxt = "{}:{}";

    @Override
    public List<WorkApprovalDto> selectApproval(String processInstId) {
        List<WorkApprovalDto> workApprovalDtos = baseMapper.selectApproval(processInstId);
        workApprovalDtos.forEach(item->{
            item.setCurrentstate("10".equals(item.getCurrentstate())?"待办":"已办");
        });
        return workApprovalDtos;
    }

    @Override
    public String selectApprovalPerson(Long processInstId) {
        LambdaQueryWrapper<Wfworkitem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Wfworkitem::getProcessinstid,processInstId);
        queryWrapper.orderByDesc(Wfworkitem::getWorkitemid);
        queryWrapper.select(Wfworkitem::getPartiname);

        List<Wfworkitem> wfworkitems = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(wfworkitems)){
            return wfworkitems.get(0).getPartiname();
        }
        return null;
    }

    @Override
    public Map<Long, WorkPartinameDto> resolveApproverMap(Set<Long> processInstIds, String activitydefid) {
        if (CollectionUtil.isEmpty(processInstIds)){
            return Maps.newHashMap();
        }
        ArrayList<String> defIds = Lists.newArrayList(activitydefid, YHDW_DEF_ID);
        List<Wfworkitem> wfworkitemList = Lists.newArrayList();
        ListUtil.partition(Lists.newArrayList(processInstIds),500).forEach(rows->{
            wfworkitemList.addAll(lambdaQuery()
                    .in(Wfworkitem::getProcessinstid, processInstIds)
                    .in(Wfworkitem::getActivitydefid, defIds)
                    .select(Wfworkitem::getProcessinstid,Wfworkitem::getPartiname,Wfworkitem::getActivitydefid)
                    .list());
        });
        Map<Long, WorkPartinameDto> processMap = wfworkitemList.stream()
                .filter(e -> e.getActivitydefid().equals(YHDW_DEF_ID) || e.getActivitydefid().equals(activitydefid))
                .collect(Collectors.toMap(
                        Wfworkitem::getProcessinstid,
                        e -> {
                            String partiname = StringUtils.hasText(e.getPartiname()) ? e.getPartiname() : "";
                            if (e.getActivitydefid().equals(YHDW_DEF_ID)) {
                                return new WorkPartinameDto().setProcessinstId(e.getProcessinstid()).setPartiname(partiname).setLeaderTitle("负责人");
                            } else {
                                return new WorkPartinameDto().setProcessinstId(e.getProcessinstid()).setPartiname(partiname).setLeaderTitle("复核人");
                            }
                        },
                        (existing, replacement) -> existing
                ));

        return processMap;
    }

    @Override
    public Map<Integer, List<Long>> selectTaskMapByNmInsp(Set<Long> processInstIds, boolean isDm) {
        if (CollectionUtil.isEmpty(processInstIds)){
            return Collections.emptyMap();
        }
        List<List<Long>> partition = ListUtil.partition(Lists.newArrayList(processInstIds), 500);
        List<Wfworkitem> allList = Lists.newArrayList();
        partition.forEach(item->{
            List<Wfworkitem> list = lambdaQuery().in(Wfworkitem::getProcessinstid, item)
                    .in(Wfworkitem::getCurrentstate, "4", "10")
                    .groupBy(Wfworkitem::getActivitydefid, Wfworkitem::getProcessinstid)
                    .select(Wfworkitem::getActivitydefid, Wfworkitem::getProcessinstid).list();
            allList.addAll(list);
        });

        Map<String, List<Long>> processMap = allList.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(Wfworkitem::getActivitydefid))
                .entrySet()
                .stream().
                collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(Wfworkitem::getProcessinstid).collect(Collectors.toList())
                ));
        Map<Integer,List<Long>> resMap = Maps.newHashMap();
        resMap.put(0,processMap.getOrDefault("manualActivity",Lists.newArrayList()));
        resMap.put(1,processMap.getOrDefault("manualActivity"+(isDm?"3":"2"),Lists.newArrayList()));
        return resMap;
    }

    @Override
    public void updateUserInfo(Long processInstId, String userCode, String userName) {
        baseMapper.updateWfprocessinst(processInstId,userCode);
        baseMapper.updateWfwiparticipant(processInstId,userCode,userName);
        baseMapper.updateWfworkitem(processInstId,userCode,userName);
    }
}
