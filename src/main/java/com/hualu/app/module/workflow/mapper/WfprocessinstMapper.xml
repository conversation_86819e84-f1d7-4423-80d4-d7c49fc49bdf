<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.workflow.mapper.WfprocessinstMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.workflow.entity.Wfprocessinst">
        <id column="PROCESSINSTID" property="processinstid" />
        <result column="PROCESSINSTNAME" property="processinstname" />
        <result column="PROCESSINSTDESC" property="processinstdesc" />
        <result column="CREATOR" property="creator" />
        <result column="OWNER" property="owner" />
        <result column="CURRENTSTATE" property="currentstate" />
        <result column="PRIORITY" property="priority" />
        <result column="RELATEDATA" property="relatedata" />
        <result column="RELATEDATAVCHR" property="relatedatavchr" />
        <result column="LIMITNUM" property="limitnum" />
        <result column="LIMITNUMDESC" property="limitnumdesc" />
        <result column="CREATETIME" property="createtime" />
        <result column="STARTTIME" property="starttime" />
        <result column="ENDTIME" property="endtime" />
        <result column="FINALTIME" property="finaltime" />
        <result column="REMINDTIME" property="remindtime" />
        <result column="PARENTPROCID" property="parentprocid" />
        <result column="PARENTACTID" property="parentactid" />
        <result column="PROCESSDEFID" property="processdefid" />
        <result column="ISTIMEOUT" property="istimeout" />
        <result column="TIMEOUTNUM" property="timeoutnum" />
        <result column="TIMEOUTNUMDESC" property="timeoutnumdesc" />
        <result column="UPDATEVERSION" property="updateversion" />
        <result column="PROCESSDEFNAME" property="processdefname" />
        <result column="EXTEND1" property="extend1" />
        <result column="EXTEND2" property="extend2" />
        <result column="EXTEND3" property="extend3" />
        <result column="EXTEND4" property="extend4" />
        <result column="EXTEND5" property="extend5" />
        <result column="EXTEND6" property="extend6" />
        <result column="EXTEND7" property="extend7" />
        <result column="CATALOGUUID" property="cataloguuid" />
        <result column="CATALOGNAME" property="catalogname" />
        <result column="TENANT_ID" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        PROCESSINSTID, PROCESSINSTNAME, PROCESSINSTDESC, CREATOR, OWNER, CURRENTSTATE, PRIORITY, RELATEDATA, RELATEDATAVCHR, LIMITNUM, LIMITNUMDESC, CREATETIME, STARTTIME, ENDTIME, FINALTIME, REMINDTIME, PARENTPROCID, PARENTACTID, PROCESSDEFID, ISTIMEOUT, TIMEOUTNUM, TIMEOUTNUMDESC, UPDATEVERSION, PROCESSDEFNAME, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6, EXTEND7, CATALOGUUID, CATALOGNAME, TENANT_ID
    </sql>
    <select id="getWorkTaskNum" resultType="com.hualu.app.module.workflow.vo.WorkTaskVo">
            select count(1) as task_num from ${tableName} where exists(${sql})
        and to_char(CREATE_TIME,'yyyy-MM') = to_char(sysdate,'yyyy-MM')
        <if test="facCat != null">
            and facility_cat=#{facCat}
        </if>
    </select>
</mapper>
