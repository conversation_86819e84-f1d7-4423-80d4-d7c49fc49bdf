package com.hualu.app.module.workflow.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class WorkPartinameDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long processinstId;

    /**
     * 负责人、审核人
     */
    private String leaderTitle = "负责人";

    /**
     * 审批人员
     */
    private String partiname = "";
}
