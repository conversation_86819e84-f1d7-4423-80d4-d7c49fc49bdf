package com.hualu.app.module.workflow.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.workflow.entity.Wfworkflowstep;
import com.hualu.app.module.workflow.mapper.WfworkflowstepMapper;
import com.hualu.app.module.workflow.service.WfworkflowstepService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 流程审核步骤表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@DS("bpsDs")
@Service
public class WfworkflowstepServiceImpl extends ServiceImpl<WfworkflowstepMapper, Wfworkflowstep> implements WfworkflowstepService {

}
