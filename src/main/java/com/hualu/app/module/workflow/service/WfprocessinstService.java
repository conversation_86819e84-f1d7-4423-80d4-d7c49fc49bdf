package com.hualu.app.module.workflow.service;

import com.hualu.app.module.workflow.entity.Wfprocessinst;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface WfprocessinstService extends IService<Wfprocessinst> {

    /**
     * 查询流程实例创建人
     * @param processInstId
     * @return
     */
    String getProcessCreateUserName(long processInstId);
}
