package com.hualu.app.module.workflow.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.workflow.dto.WorkApprovalDto;
import com.hualu.app.module.workflow.entity.Wfworkitem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface WfworkitemMapper extends BaseMapper<Wfworkitem> {

    /**
     * 查询年度养护计划的工作项
     * @param processinstid
     * @return
     */
    List<Wfworkitem> selectWorkitemsByYearPlan(Long processinstid);


    List<WorkApprovalDto> selectApproval(String processInstId);

    void updateWfprocessinst(@Param("processInstId") Long processInstId,@Param("userCode") String userCode);

    void updateWfwiparticipant(@Param("processInstId")Long processInstId,@Param("userCode") String userCode,@Param("userName") String userName);

    void updateWfworkitem(@Param("processInstId")Long processInstId,@Param("userCode") String userCode,@Param("userName") String userName);
}
