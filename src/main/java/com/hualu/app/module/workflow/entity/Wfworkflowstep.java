package com.hualu.app.module.workflow.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 流程审核步骤表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Wfworkflowstep对象", description="流程审核步骤表")
public class Wfworkflowstep implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "步骤ID（主键）")
    @TableId("STEPID")
    private String stepid;

    @ApiModelProperty(value = "业务ID(对应业务表数据主键)")
    @TableField("BUSINESSID")
    private String businessid;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "工作项ID")
    @TableField("WORKITEMID")
    private Long workitemid;

    @ApiModelProperty(value = "审核状态(用于判断流程走哪个分支)")
    @TableField("APPROVESTATUS")
    private String approvestatus;

    @ApiModelProperty(value = "审批意见")
    @TableField("APPROVESUGGESTION")
    private String approvesuggestion;

    @ApiModelProperty(value = "审批人")
    @TableField("APPROVER")
    private String approver;

    @ApiModelProperty(value = "审批时间")
    @TableField("APPROVETIME")
    private LocalDateTime approvetime;

    @ApiModelProperty(value = "审批部门ID")
    @TableField("APPROVEDEPTID")
    private String approvedeptid;

    @ApiModelProperty(value = "审批部门名称")
    @TableField("APPROVEDEPTNAME")
    private String approvedeptname;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "业务表名")
    @TableField("BUSINESSTABLENAME")
    private String businesstablename;

    @ApiModelProperty(value = "业务详细页面路径")
    @TableField("BUSINESSDETAILURL")
    private String businessdetailurl;

    @ApiModelProperty(value = "当前操作的时间")
    @TableField("CURRENTOPERATIME")
    private LocalDateTime currentoperatime;

    @ApiModelProperty(value = "接收人")
    @TableField("RECIVEUSER")
    private String reciveuser;

    @ApiModelProperty(value = "数据源Service")
    @TableField("DATASOURCE")
    private String datasource;

    @ApiModelProperty(value = "工作项名称")
    @TableField("WORKITEMNAME")
    private String workitemname;

    @ApiModelProperty(value = "业务操作页面路径")
    @TableField("BUSINESSDEALURL")
    private String businessdealurl;

    @ApiModelProperty(value = "业务历史审批信息页面路径")
    @TableField("BUSINESSNODEURL")
    private String businessnodeurl;


}
