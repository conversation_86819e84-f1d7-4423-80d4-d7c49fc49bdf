package com.hualu.app.module.workflow.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class WorkFlowDto implements Serializable {

    //流程定义名称
    private String processDefName;
    //流程实例名称
    private String processInstName;
    //流程描述
    private String processInstDesc;
    //流程实例ID
    private Long processInstID;
    //流程活动实例ID
    private Long activityInstID;

    private Long workItemID;//工作项ID



}
