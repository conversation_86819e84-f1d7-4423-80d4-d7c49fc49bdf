package com.hualu.app.module.workflow.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.workflow.entity.Wfprocessinst;
import com.hualu.app.module.workflow.mapper.WfprocessinstMapper;
import com.hualu.app.module.workflow.service.WfprocessinstService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
@DS("bpsDs")
@Service
public class WfprocessinstServiceImpl extends ServiceImpl<WfprocessinstMapper, Wfprocessinst> implements WfprocessinstService {

    @Override
    public String getProcessCreateUserName(long processInstId) {
        return baseMapper.getProcessCreateUserName(processInstId);
    }
}
