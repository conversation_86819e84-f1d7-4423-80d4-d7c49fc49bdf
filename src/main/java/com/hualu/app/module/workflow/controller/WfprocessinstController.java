package com.hualu.app.module.workflow.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.mapper.WfprocessinstMapper;
import com.hualu.app.module.workflow.vo.WorkTaskTypeVo;
import com.hualu.app.module.workflow.vo.WorkTaskVo;
import com.hualu.app.utils.H_FutureHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作流-待办任务
 * <AUTHOR>
 * @since 2023-04-19
 */
@RestController
@RequestMapping("/wfprocessinst")
public class WfprocessinstController {

    @Autowired
    WfprocessinstMapper wfprocessinstMapper;

    private static List<WorkTaskTypeVo> workTaskTypeVoList = new ArrayList<WorkTaskTypeVo>();

    static {
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("dmNotice").setTableName("memsdb.DM_NOTICE").setTaskTypeName("通知书"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("dmTask").setTableName("memsdb.dm_task").setTaskTypeName("任务单"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("dmTaskAccpt").setTableName("memsdb.dm_task_accpt").setTaskTypeName("验收单"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newQlFinsp").setTableName("memsdb.nm_finsp").setFacCat("QL").setTaskTypeName("桥梁经常检查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newHdFinsp").setTableName("memsdb.nm_finsp").setFacCat("HD").setTaskTypeName("涵洞经常检查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newBpFinsp").setTableName("memsdb.nm_finsp").setFacCat("BP").setTaskTypeName("边坡经常检查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newSdFinsp").setTableName("memsdb.nm_finsp").setFacCat("SD").setTaskTypeName("隧道经常检查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newJaFinsp").setTableName("memsdb.nm_finsp").setFacCat("JA").setTaskTypeName("交安经常检查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newLmFinsp").setTableName("memsdb.nm_finsp").setFacCat("LM").setTaskTypeName("路面经常检查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newLmDinsp").setTableName("memsdb.nm_dinsp").setFacCat("LM").setTaskTypeName("路面日常巡查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newQlDinsp").setTableName("memsdb.nm_dinsp").setFacCat("QL").setTaskTypeName("桥梁日常巡查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newSdDinsp").setTableName("memsdb.nm_dinsp").setFacCat("SD").setTaskTypeName("隧道日常巡查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newBpDinsp").setTableName("memsdb.nm_dinsp").setFacCat("BP").setTaskTypeName("边坡日常巡查"));
        workTaskTypeVoList.add(new WorkTaskTypeVo().setTaskType("newJaDinsp").setTableName("memsdb.nm_dinsp").setFacCat("JA").setTaskTypeName("交安日常巡查"));
    }

    /**
     * 获取待办任务数量
     * @return
     */
    @GetMapping("getWorkTask")
    public RestResult<List<WorkTaskVo>> getWorkTask() {
        List<CompletableFuture<WorkTaskVo>> futureList = new ArrayList<>();
        Map<String, WorkTaskTypeVo> taskMap = workTaskTypeVoList.stream().collect(Collectors.toMap(WorkTaskTypeVo::getTaskType, Function.identity()));
        String userCode = CustomRequestContextHolder.getUserCode();
        String userId = CustomRequestContextHolder.getUserId();
        taskMap.forEach((k, v) -> {
            CompletableFuture<WorkTaskVo> taskVo = CompletableFuture.supplyAsync(() -> {
                //异步线程需要重新设置用户账号
                CustomRequestContextHolder.setUserCode(userCode);
                CustomRequestContextHolder.setUserId(userId);
                String userTaskSql = H_WorkFlowHelper.getUserTaskSql(0, v.getTableName().replace("memsdb.", ""));
                WorkTaskVo workTaskNum = wfprocessinstMapper.getWorkTaskNum(userTaskSql, v.getTableName(),v.getFacCat());
                workTaskNum.setTaskType(v.getTaskType());
                workTaskNum.setTaskTypeName(v.getTaskTypeName());
                return workTaskNum;
            });
            futureList.add(taskVo);
        });
        List<WorkTaskVo> join = H_FutureHelper.sequence(futureList).join();
        return RestResult.success(join);
    }
}
