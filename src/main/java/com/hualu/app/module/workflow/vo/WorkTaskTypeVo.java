package com.hualu.app.module.workflow.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class WorkTaskTypeVo implements Serializable {

    /**
     * 待办任务类型
     */
    private String taskType;

    /**
     * 待办任务类型名称
     */
    private String taskTypeName;

    /**
     * 对应的表名称
     */
    private String tableName;

    /**
     * 设施类型
     */
    private String facCat;
}
