package com.hualu.app.module.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.workflow.dto.WorkApprovalDto;
import com.hualu.app.module.workflow.dto.WorkPartinameDto;
import com.hualu.app.module.workflow.entity.Wfworkitem;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface WfworkitemService extends IService<Wfworkitem> {

    /**
     * 查询流程审批意见
     * @param processInstId
     * @return
     */
    List<WorkApprovalDto> selectApproval(String processInstId);

    /**
     * 查询流程审批人
     * @param processInstId
     * @return
     */
    String selectApprovalPerson(Long processInstId);

    /**
     * 获取节点对应流程审批人
     * @param processInstIds
     * @param activitydefid
     * @return
     */
    Map<Long, WorkPartinameDto> resolveApproverMap(Set<Long> processInstIds, String activitydefid);

    /**
     * 查询待办清单，用于批量办理
     * @param processInstIds
     * @param isDm true:日常巡查，false:经常检查
     * @return
     */
    Map<Integer,List<Long>> selectTaskMapByNmInsp(Set<Long> processInstIds, boolean isDm);

    /**
     * 替换流程创建人信息
     * @param processInstId
     * @param userCode
     * @param userName
     */
    void updateUserInfo(Long processInstId, String userCode,String userName);
}
