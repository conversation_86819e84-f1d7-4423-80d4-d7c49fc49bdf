package com.hualu.app.module.workflow;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.eos.das.entity.DASManager;
import com.eos.das.entity.ExpressionHelper;
import com.eos.das.entity.IDASCriteria;
import com.eos.workflow.api.*;
import com.eos.workflow.data.*;
import com.eos.workflow.omservice.WFParticipant;
import com.hualu.app.module.platform.entity.FwRightUser;
import com.hualu.app.module.platform.service.FwPlSystemParamsService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.entity.Wfworkflowstep;
import com.hualu.app.module.workflow.entity.Wfworkitem;
import com.hualu.app.module.workflow.mapper.WfworkitemMapper;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.WfworkflowstepService;
import com.primeton.workflow.api.PageCond;
import com.primeton.workflow.api.WFServiceException;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程工具类
 */
@Slf4j
public class H_WorkFlowHelper {

    private static final String PERSON = "PERSON";
    private final static String RETURN = "退回";
    private static IBPSServiceClient client = null;

    public static Map<String,String> activityMap = new  HashMap<String, String>();

    static {
        try {
            client = BPSServiceClientFactory.getDefaultClient();
        } catch (WFServiceException e) {
            e.printStackTrace();
            log.error("获取流程工程类失败", e.getCause());
        }
        activityMap.put( "leaderAudit","分管领导审核");
        activityMap.put("departmentalLeaderAudit","部门领导审核" );
        activityMap.put("supervisorAudit","业务主管审核" );
        activityMap.put("yhdwLeaderAudit","养护单位负责人审核");
        activityMap.put("supervisorAudit2","业务主管（串行）");
        activityMap.put("ZJPD","直接派单" );
        activityMap.put("PD","派单" );
        activityMap.put( "noticeCheck","路政通知复核");
        activityMap.put("noticeAudit","路政通知审核" );
        activityMap.put("pointCheck","施工点复核" );
        activityMap.put("pointAudit","施工点审核" );
        activityMap.put("monthCheck","月度计划复核");
        activityMap.put( "monthAudit","月度计划审核");
        activityMap.put("accptAudit","维修验收审核" );
        activityMap.put("accptCheck","维修验收复核" );
        activityMap.put("leadingAudit","领导审核" );
        activityMap.put("secondOwnerAudit","二级业主审核" );
        activityMap.put("secondOwnerAudit2","其他二级业主审核");
        activityMap.put("groupAudit","集团审核" );
        activityMap.put("otherLeaderAudit","其他领导审核");
        activityMap.put( "return","退回");
        activityMap.put("dataCenterAudit","数据中心审核" );
        activityMap.put("returnOrg","退回项目公司");
        activityMap.put("end","办结");
        activityMap.put("putDss","病害入库");
        activityMap.put("get","接收");

    }


    /**
     * 获取流程节点名称
     * @param nextAction
     * @return
     */
    public static String getActName(String nextAction){
        return activityMap.getOrDefault(nextAction,"");
    }


    @SneakyThrows
    public static String submitWorkItem(long processInstId,
                                      String nextAction, String nextUserIds, String apprOpinion) {
        if (nextAction.contains(",")) {
            String[] data = nextAction.split(",");
            String actDefID = data[0];//目标活动定义ID
            String connectorValue = data[1];
            nextAction = data[2];//设置nextAction=退回
            WFActivityInst inst = queryActivityInstByProcessInstID(processInstId, actDefID);
            WFParticipant[] person = queryHasWorkItemParticipants(inst.getActivityInstID());
            setRelativeData(processInstId, "userList", person);
            setRelativeData(processInstId, "nextAction", connectorValue);
        } else {
            setRelativeData(processInstId, "nextAction", nextAction);
        }
        //设置审批人相关数据
        if (StrUtil.isNotBlank(nextUserIds)) {
            List<WFParticipant> wfParticipants = setWFParticipantsByPerson(nextUserIds);
            if (CollectionUtil.isEmpty(wfParticipants)){
                throw new BaseException("下一岗审批人员不能为空");
            }
            WFParticipant[] userArray = wfParticipants.toArray(new WFParticipant[0]);
            setRelativeData(processInstId, "userList", userArray);
        }

        //取得流程实例对应的用户工作项
        IWFWorklistQueryManager worklistQueryManager = client.getWorklistQueryManager();
        IDASCriteria criteria = DASManager.createCriteria("com.eos.workflow.data.WFWorkItem");
        criteria.add(ExpressionHelper.eq("processInstID", processInstId));
        PageCond pageCond = new PageCond(10);

        java.util.List<WFWorkItem> workItemList =
                worklistQueryManager.queryPersonWorkItemsCriteria(CustomRequestContextHolder.getUserCode(), "ALL", "ALL", criteria, pageCond);

        if (workItemList == null || workItemList.size() == 0) {
            throw new Exception("未找到用户[" + CustomRequestContextHolder.getUserCode() + "]对应流程实例[" + processInstId + "]对应的工作项");
        }

        WFWorkItem workItem = workItemList.get(0);
        IWFDefinitionQueryManager processInstManager = client.getDefinitionQueryManager();

        WFActivityDefine currentActivity = processInstManager.getActivity(workItem.getProcessDefID(), workItem.getActivityDefID());

        if (currentActivity == null) {
            throw new Exception("未找到[" + workItem.getActivityDefID() + "]对应的活动节点定义");
        }

        IWFWorkItemManager workItemManager = client.getWorkItemManager();
        //判断当前节点是否多工作项节点
        if (currentActivity.isMultiWorkItem()) {
            if ("A".equalsIgnoreCase(nextAction)) {
                workItemManager.finishWorkItem(workItem.getWorkItemID(), false);
            } else {
                if (nextAction.equals("退回")) {
                    //结束活动实例
                    IWFActivityInstManager activityInstManager = client.getActivityInstManager();
                    activityInstManager.finishActivityInstance(workItem.getActivityInstID());
                    //4586608
                } else {
                    workItemManager.finishWorkItem(workItem.getWorkItemID(), false);
                }
            }
        } else {
            workItemManager.finishWorkItem(workItem.getWorkItemID(), false);
        }

        Wfworkflowstep wfworkflowstep = initWfworkflowstep(workItem, apprOpinion);
        WfworkflowstepService stepService = CustomApplicationContextHolder.getBean(WfworkflowstepService.class);
        //保存审批意见
        stepService.save(wfworkflowstep);
        WFProcessDefine wfProcessDefine = processInstManager.queryProcessByID(workItem.getProcessDefID());
        String processDefName = wfProcessDefine.getProcessDefName();
        IWorkItemEventHandler eventHandler = CustomApplicationContextHolder.getBeansOfType(IWorkItemEventHandler.class).values().stream().filter(e -> e.getProcessDefName().equals(processDefName)).findFirst().orElse(null);

        if (isFinshActivity(currentActivity, nextAction)) {
            eventHandler.savedoEvent(processInstId,true,nextAction,currentActivity.getId());
        }else {
            eventHandler.savedoEvent(processInstId,false,nextAction,currentActivity.getId());
        }
        return processDefName;
    }


    /**
     * <p>功能描述：根据业务流程ID，和活动定义ID查询活动的后继活动连线信息。</p>
     *
     * @param processDefID 业务流程ID
     * @param activityID   活动定义ID
     * @return <p>创建日期:2015-7-22 下午12:33:33。</p>
     * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
     */
    @SneakyThrows
    public static List<WFConnector> queryNextTransition(long processDefID, String activityID) {
        IWFDefinitionQueryManager queryManager = client.getDefinitionQueryManager();
        List<WFConnector> connectorList = connectorList = queryManager.queryNextTransition(processDefID, activityID);
        return connectorList;
    }


    /**
     * <p>功能描述：查询已经分配工作项的参与者数组。</p>
     *
     * @param activityInstID 活动实例
     * @return
     * @since JDK1.6。
     * <p>创建日期:2015-7-22 下午2:39:28。</p>
     * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
     */
    public static WFParticipant[] queryHasWorkItemParticipants(long activityInstID) {
        //获取当前已分配工作项的用户数组
        WFParticipant[] cyParticipants = null;
        IWFWorkItemManager itemManager = client.getWorkItemManager();
        try {
            List<WFWorkItem> itemList = itemManager.queryWorkItemsByActivityInstID(activityInstID, null);
            cyParticipants = new WFParticipant[itemList.size()];
            for (int i = 0; i < itemList.size(); i++) {
                WFWorkItem workitem = itemList.get(i);
                cyParticipants[i] = new WFParticipant(workitem.getParticipant(), workitem.getPartiName(), PERSON);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return cyParticipants;
    }

    /**
     * <p>功能描述：查询一个流程的某个活动定义对应的活动实例。</p>
     *
     * @param processInstID
     * @param actDefID
     * @return
     * @throws Exception
     * @since JDK1.6。
     * <p>创建日期:2016-5-18 下午7:03:43。</p>
     * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
     */
    public static WFActivityInst queryActivityInstByProcessInstID(long processInstID, String actDefID) throws Exception {
        IWFActivityInstManager instManager = client.getActivityInstManager();
        List<WFActivityInst> instList = instManager.queryActivityInstsByActivityID(processInstID, actDefID, null);
        if (instList == null) {
            return null;
        }
        return instList.get(instList.size() - 1);
    }

    /**
     * 获取流程标记（用于隐藏下一岗对应的节点）
     *
     * @return
     */
    public static int getHideStatus() {
        String roleTypes = "";
        FwRightUserService bean = CustomApplicationContextHolder.getBean(FwRightUserService.class);
        List<FwRightUser> users = bean.selectRoleTypeByUserCode(CustomRequestContextHolder.getUserCode());
        if (CollectionUtil.isNotEmpty(users)) {
            Set<String> roles = users.stream().map(FwRightUser::getRoleType).collect(Collectors.toSet());
            roleTypes = StrUtil.join(",", roles);
        }
        int is = 0;

        //如果是养护单位或者施工单位，则隐藏病害入库
        if (roleTypes.indexOf("01") >= 0 || roleTypes.indexOf("02") >= 0) {
            is = 1;
        }
        //PC端在养护计划设置，app的经常检查单用不到
        /*if ("1".equals(back)) {//二级单位标记
            i=5;
        }
        if("1".equals(isAdjust)){//调整阶段
            i= 10;
        }else if("0".equals(isAdjust)){//编制阶段
            i=5;
        }*/
        return is;
    }

    /**
     * 查询流程是否办结
     *
     * @param processInstId
     * @return
     */
    public static boolean isFinish(long processInstId) {
        boolean bResult = false;
        IWFProcessInstManager processInstManager = client.getProcessInstManager();
        try {
            int instState = processInstManager.getProcessInstState(processInstId);
            if (instState == 7) {
                bResult = true;
            }
        } catch (WFServiceException e) {
            e.printStackTrace();
            log.error("查询流程失败:" + processInstId, e);
            throw new BaseException("查询流程失败");
        }
        return bResult;
    }

    /**
     * 是否结束活动项
     *
     * @param activitDefine
     * @param nextAction
     * @return
     */
    public static boolean isFinshActivity(WFActivityDefine activitDefine, String nextAction) {
        boolean bResult = false;
        List<WFConnector> list = queryNextTransition(activitDefine.getProcessDefId(), activitDefine.getId());
        for (WFConnector conn : list) {
            System.out.println(conn.getDestActID());
            if ("finishActivity".equals(conn.getDestActID()) && nextAction.equals(conn.getDisplayName())) {
                bResult = true;
                break;
            }
        }
        return bResult;
    }

    /**
     * 获取流程任务sql语句
     *
     * @param status
     * @param alias
     * @return
     */
    public static String getUserTaskSql(int status, String alias) {
        StringBuffer sql = new StringBuffer();
        //待办任务
        if (0 == status) {
            sql.append("select 1 from BPS.WFWIPARTICIPANT w where w.CURRENTSTATE IN (4, 10) and w.PROCESSINSTID={}.PROCESSINSTID and w.PARTICIPANTID = '{}'");
           /* sql.append(" union ");
            sql.append("select 1 from BPS.WFWIPARTICIPANT y inner join gdgs.FW_RIGHT_ROLE_USER_REF r on y.PARTICIPANTID = r.ROLE_ID where y.CURRENTSTATE IN (4, 10) and y.PROCESSINSTID={}.PROCESSINSTID and R.USER_ID='{}'");
            return StrUtil.format(sql.toString(), alias, CustomRequestContextHolder.getUserCode(), alias, CustomRequestContextHolder.getUserId());*/
            return StrUtil.format(sql.toString(),alias,CustomRequestContextHolder.getUserCode());
        }
        //已办任务
        if (1 == status) {
            /*sql.append("select 1 from bps.wfprocessinst pi,(select wf.processinstid, wf.participant,min(wf.currentstate) as currentstate,max(wf.workitemid) as workitemid ");
            sql.append(" from BPS.wfworkitem wf where wf.participant = '{}' ");
            sql.append(" group by wf.participant, wf.processinstid) db where pi.processinstid = db.processinstid ");
            sql.append(" and {}.PROCESSINSTID = pi.processinstid and pi.currentstate = 2 ");
            sql.append(" and (db.currentstate = 12 or db.currentstate = 13) ");*/
            //todo 效率更高，可能会存在待办和已办列表都存在的情况
            sql.append(" select 1 from bps.wfprocessinst pi inner join BPS.wfworkitem wf on pi.processinstid = wf.processinstid");
            sql.append(" where  {}.PROCESSINSTID = pi.processinstid");
            sql.append(" and pi.currentstate = 2 and wf.participant='{}' and wf.CURRENTSTATE in (12,13)");
            return StrUtil.format(sql.toString(), alias,CustomRequestContextHolder.getUserCode());
        }
        //办结任务
        if (2 == status) {
            sql.append(" select 1 from BPS.Wfworkitem w,BPS.WFPROCESSINST p where w.CURRENTSTATE=12")
                    .append(" and w.participant='{}' and w.PROCESSINSTID={}.PROCESSINSTID")
                    .append(" and w.PROCESSINSTID=").append("p.PROCESSINSTID and p.CURRENTSTATE=7 ");
            return StrUtil.format(sql.toString(), CustomRequestContextHolder.getUserCode(), alias);
        }
        return null;
    }

    /**
     * 获取业务流程或者活动定义的扩展属性XML字符串
     *
     * @param processDefID  流程定义ID
     * @param activityDefID 活动定义ID
     * @return
     */
    private static String getExtendAttribute(long processDefID,
                                             java.lang.String activityDefID) {
        IWFDefinitionQueryManager definitionQueryManager = client.getDefinitionQueryManager();
        try {
            String attribute = definitionQueryManager.getExtendAttribute(processDefID, activityDefID);
            return attribute;
        } catch (WFServiceException e) {
            e.printStackTrace();
            log.error("获取业务流程或者活动定义的扩展属性XML字符串失败", e);
            throw new BaseException("获取业务流程或者活动定义的扩展属性XML字符串失败");
        }
    }


    /**
     * 获取下一岗的活动定义
     *
     * @param processInstId
     */
    public static List<Map> getNextActivitys(long processInstId) throws Exception {
        //取得流程实例对应的用户工作项
        IWFWorklistQueryManager worklistQueryManager = client.getWorklistQueryManager();
        IWFRelativeDataManager relativeDataManager = client.getRelativeDataManager();

        IDASCriteria criteria = DASManager.createCriteria("com.eos.workflow.data.WFWorkItem");
        // activityDefID为WFWorkItem实例的属性名
        criteria.add(ExpressionHelper.eq("processInstID", processInstId));

        PageCond pageCond = new PageCond(10);

        java.util.List<WFWorkItem> workItemList = worklistQueryManager.queryPersonWorkItemsCriteria(CustomRequestContextHolder.getUserCode(), "ALL", "ALL", criteria, pageCond);
        if (workItemList == null || workItemList.size() == 0) {
            throw new BaseException("未找到用户[" + CustomRequestContextHolder.getUserCode() + "]对应流程实例[" + processInstId + "]对应的工作项");
        }

        WFWorkItem workItem = workItemList.get(0);
        //取得对应活动实例的后续活动
        IWFDefinitionQueryManager processInstManager = client.getDefinitionQueryManager();
        IWFProcessInstManager proInstManager = client.getProcessInstManager();
        WFActivityDefine currentActivity = processInstManager.getActivity(workItem.getProcessDefID(), workItem.getActivityDefID());

        if (currentActivity == null) {
            throw new BaseException("未找到[" + workItem.getActivityDefID() + "]对应的活动节点定义");
        }
        List<Map> actityList = Lists.newArrayList();

        //判断当前节点是否多工作项节点
        if (currentActivity.isMultiWorkItem()) {
            WFParticipant[] wfParticipants = null;
            //用户参与者参数名
            String extendAttribute = getExtendAttribute(currentActivity.getProcessDefId(), currentActivity.getId());
            Object wfPerson = relativeDataManager.getRelativeData(workItem.getProcessInstID(), "userList");
            if (wfPerson instanceof Object[]) {
                wfParticipants = (WFParticipant[]) wfPerson;
            } else if(wfPerson instanceof List){
                wfParticipants = new WFParticipant[1];
                wfParticipants[0] = (WFParticipant) ((List<?>) wfPerson).get(0);
            }else {
                wfParticipants = new WFParticipant[1];
                wfParticipants[0] = (WFParticipant) wfPerson;
            }
            //取得下一个参与者
            WFParticipant nextParticipant = null;
            for (int i = 0; i < wfParticipants.length; i++) {
                if (wfParticipants[i] != null) {
                    if (CustomRequestContextHolder.getUserCode().equalsIgnoreCase(wfParticipants[i].getId())) {
                        if (i + 1 < wfParticipants.length) {
                            nextParticipant = wfParticipants[i + 1];
                        }
                        break;
                    }
                }
            }
            if (nextParticipant != null) {
                Map activity = new HashMap();
                activity.put("id", "A");
                activity.put("name", "提交[" + nextParticipant.getName() + "]处理");
                activity.put("isSelectUser", "0");

                actityList.add(activity);

                activity = new HashMap();
                activity.put("id", "退回");
                activity.put("name", "退回");
                activity.put("isSelectUser", "0");

                actityList.add(activity);
            }
        }

        if (actityList.size() <= 0) {
            List<WFConnector> connectors = processInstManager.queryNextTransition(workItem.getProcessDefID(), workItem.getActivityDefID());
            Object[] actDefIdArray = new Object[]{};
            FwPlSystemParamsService paramsService = CustomApplicationContextHolder.getBean(FwPlSystemParamsService.class);
            String processDefID = paramsService.selectSystemParamByParamCode("FW_WORKFLOW");
            boolean isTrue = processDefID.contains(workItem.getProcessDefName());
            if (isTrue) {
                actDefIdArray = queryActivityDefIdByProcessInstID(workItem, connectors);
            }
            for (WFConnector connector : connectors) {
                Map activity = new HashMap();
                if (connector.getDisplayName().indexOf(RETURN) != -1) {
                    if (isTrue) {
                        boolean flag = false;
                        for (Object actDefId : actDefIdArray) {
                            if (actDefId.equals(connector.getDestActID())) {
                                flag = true;
                                break;
                            }
                        }
                        if (flag) {
                            //当操作为退回时，id="退回目标活动实例的ID"+"相关参数值"+"退回"，“退回”是关键字，会设置任务单对应的状态
                            activity.put("id", connector.getDestActID() + "," + connector.getRightValue() + "," + RETURN);
                            activity.put("name", connector.getDisplayName());
                        } else {
                            continue;
                        }
                    } else {
                        //当操作为退回时，id="退回目标活动实例的ID"+"相关参数值"+"退回"，“退回”是关键字，会设置任务单对应的状态
                        activity.put("id", connector.getDestActID() + "," + connector.getRightValue() + "," + RETURN);
                        activity.put("name", connector.getDisplayName());
                    }
                } else {
                    activity.put("id", StringUtils.isBlank(connector.getDisplayName()) ? connector.getDestActID() : connector.getDisplayName());
                    activity.put("name", StringUtils.isBlank(connector.getDisplayName()) ? connector.getDestActName() : connector.getDisplayName());
                }
                //取得目标节点的定义ID
                String destActId = connector.getDestActID();

                WFActivityDefine activityDefine = processInstManager.getActivity(workItem.getProcessDefID(), destActId);
                String isSelectUser = "0";    //0 不选择用户  1：选择用户
                if ("manual".equalsIgnoreCase(activityDefine.getType())) {
                    //人工服务
                    if ("relevantdata".equalsIgnoreCase(activityDefine.getParticipantType()) && connector.getDisplayName().indexOf("退回") == -1) {
                        isSelectUser = "1";
                    } else if ("organization-list".equalsIgnoreCase(activityDefine.getParticipantType())
                            && (connector.getDisplayName().indexOf("数据中心审核") != -1 || connector.getDisplayName().indexOf("养护部审核") != -1)) {
                        isSelectUser = "1";
                    }
                    if ("manualActivity111".equals(destActId) || "manualActivity11".equals(destActId) || "manualActivity211".equals(destActId) || "manualActivity21".equals(destActId)) {
                        isSelectUser = "1";
                    }
                    //提交给二级业主不用选人
                    if ("manualActivity2".equals(destActId) && connector.getDestActName().indexOf("二级单位计划编制") > -1) {
                        isSelectUser = "0";
                        //List<Object[]> defaultPerson = frameBaseDao.findBySql("select wi.participant, wi.partiname from bps.wfworkitem wi where wi.processinstid = (select s.processinstid from memsdb.year_plan_s s inner join memsdb.year_plan p on s.main_year_plan_id = p.main_year_plan_id and s.plan_year = p.plan_year and s.org_id = p.p_org_id and p.processinstid = ?) and wi.workitemname = '二级业主审核'", new Object[]{processInstId});
                        WfworkitemMapper bean = CustomApplicationContextHolder.getBean(WfworkitemMapper.class);
                        List<Wfworkitem> wfworkitems = bean.selectWorkitemsByYearPlan(processInstId);
                        if (wfworkitems != null && wfworkitems.size() > 0) {
                            activity.put("defaultUser", wfworkitems.get(0).getPartiname() + "(" + wfworkitems.get(0).getParticipant() + ")");
                        }
                    }
                }
                log.info(activityDefine.getName() + ":" + activityDefine.getType() + ":" + activityDefine.getParticipantType());
                activity.put("isSelectUser", isSelectUser);
                actityList.add(activity);
            }
        }
        return actityList;
    }

    /**
     * <p>功能描述：根据当前活动实例ID，获取前驱活动实例。</p>
     *
     * @param activityInstID
     * @return
     * @throws Exception
     * @since JDK1.6。
     * <p>创建日期:2016-5-18 下午3:02:49。</p>
     * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
     */
    private static WFActivityInst queryPreviousActivityInstances(long activityInstID) throws Exception {
        IWFActivityInstManager instManger = client.getActivityInstManager();
        List<WFActivityInst> instList = instManger.queryPreviousActivityInstances(activityInstID);
        if (instList == null || instList.size() == 0) {
            return null;
        }
        WFActivityInst inst = instList.get(0);
        return inst;
    }

    /**
     * 根据流程实例ID，查询已经实例化的活动定义ID
     *
     * @param item
     * @param connectors
     * @return
     * @throws Exception
     */
    public static Object[] queryActivityDefIdByProcessInstID(WFWorkItem item, List<WFConnector> connectors) throws Exception {
        IWFActivityInstManager instManager = client.getActivityInstManager();
        Set<String> actDefId = new HashSet<String>();
        List<WFActivityInst> instList = instManager.queryActivityInstsByProcessInstID(item.getProcessInstID(), null);
        String dectDefId = null;
        Object[] defArray = null;
        //获取当前节点的前驱活动定义ID
        for (WFActivityInst inst : instList) {
            if (inst.getActivityDefID().equals(item.getActivityDefID())) {
                WFActivityInst instEntity = queryPreviousActivityInstances(inst.getActivityInstID());
                actDefId.add(instEntity.getActivityDefID());
            }
        }
        defArray = actDefId.toArray();

        int size = instList.size();
        boolean falg = false;
        //最近实例化活动定义和actDefId相比较，第一个符合条件的就是回退目标的活动定义ID
        for (int i = 0; i < size; i++) {
            WFActivityInst inst = instList.get(size - 1 - i);
            if (!inst.getActivityDefID().equals(item.getActivityDefID())) {
                for (Object obj : defArray) {
                    if (inst.getActivityDefID().equals(obj)) {
                        for (WFConnector connetor : connectors) {
                            String displayName = connetor.getDisplayName();
                            if (RETURN.equals(displayName) && connetor.getDestActID().equals(inst.getActivityDefID())) {
                                dectDefId = inst.getActivityDefID();
                                falg = true;
                                break;
                            }
                        }
                    }
                }
            }
            if (falg) {
                break;
            }
        }

        return dectDefId == null ? new Object[]{} : new Object[]{dectDefId};
    }


    /**
     * 创建流程实例，并启动流程实例
     *
     * @param processDefName
     * @param processInstName
     * @param processInstDesc
     * @return
     * @throws Exception
     */
    public static long createWorkItem(String processDefName, String processInstName, String processInstDesc) throws Exception {
        long processInstId = 0;
        IWFProcessInstManager processInstManager = client.getProcessInstManager();
        //创建流程实例
        processInstId = processInstManager.createProcessInstance(processDefName, processInstName, processInstDesc);
        //启动流程实例
        processInstManager.startProcessInstance(processInstId);
        return processInstId;
    }


    /**
     * 完成工作项
     *
     * @param processInstId 流程实例ID
     * @param opinion       审批意见
     */
    public static void finshWorkItem(long processInstId, String opinion) {
        try {
            WfworkflowstepService wfworkflowstepService = CustomApplicationContextHolder.getBean(WfworkflowstepService.class);
            IWFWorkItemManager manager = client.getWorkItemManager();
            IWFWorklistQueryManager queryManager = client.getWorklistQueryManager();
            List<WFWorkItem> userWorkItems = queryManager.queryPersonWorkItems(CustomRequestContextHolder.getUserCode(), "ALL", "ALL", null);
            for (WFWorkItem workItem : userWorkItems) {
                if (workItem.getProcessInstID() == processInstId) {
                    manager.finishWorkItem(workItem.getWorkItemID(), false);
                    //填写办理意见
                    Wfworkflowstep workflowStep = new Wfworkflowstep();
                    workflowStep.setWorkitemname(workItem.getWorkItemName());
                    workflowStep.setApprover(CustomRequestContextHolder.getUserCode());
                    workflowStep.setApprovetime(LocalDateTime.now());
                    workflowStep.setProcessinstid(processInstId);
                    workflowStep.setWorkitemid(workItem.getWorkItemID());
                    workflowStep.setApprovesuggestion(opinion);
                    wfworkflowstepService.save(workflowStep);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("完成工作项失败", e.getCause());
        }
    }

    /**
     * 删除流程实例
     *
     * @param processInstId
     * @throws Exception
     */
    public static void deleteProcessInstance(long processInstId)
            throws Exception {
        IWFProcessInstManager processInstManager = client.getProcessInstManager();
        processInstManager.deleteProcessInstance(processInstId);
    }

    /**
     * 设置流程相关数据
     *
     * @param processInstId
     * @param xpath
     * @param value
     */
    public static void setRelativeData(long processInstId, String xpath, Object value) {
        IWFRelativeDataManager relativeDataManager = client.getRelativeDataManager();
        try {
            relativeDataManager.setRelativeData(processInstId, xpath, value);
        } catch (WFServiceException e) {
            e.printStackTrace();
            throw new BaseException("设置流程相关数据失败", e.getCause());
        }
    }


    /**
     * 设置审批意见
     *
     * @param workItem
     * @param opinion
     * @return
     */
    public static Wfworkflowstep initWfworkflowstep(WFWorkItem workItem, String opinion) {
        //填写办理意见
        Wfworkflowstep workflowStep = new Wfworkflowstep();
        workflowStep.setWorkitemname(workItem.getWorkItemName());
        workflowStep.setApprover(CustomRequestContextHolder.getUserCode());
        workflowStep.setApprovetime(LocalDateTime.now());
        workflowStep.setProcessinstid(workItem.getProcessInstID());
        workflowStep.setWorkitemid(workItem.getWorkItemID());
        workflowStep.setApprovesuggestion(opinion);
        return workflowStep;
    }

    /**
     * 设置流程审批人
     *
     * @param nextUserIds
     * @return
     */
    public static List<WFParticipant> setWFParticipantsByPerson(String nextUserIds) {
        List<WFParticipant> participants = Lists.newArrayList();
        if (StrUtil.isBlank(nextUserIds)) {
            return participants;
        }
        List<String> userIds = StrUtil.split(nextUserIds, ",");
        return selectUserDtos(userIds);
    }

    /**
     * 查询后台用户信息
     *
     * @param userIds
     * @return
     */
    private static List<WFParticipant> selectUserDtos(List<String> userIds) {
        List<WFParticipant> participants = Lists.newArrayList();
        FwRightUserService userService = CustomApplicationContextHolder.getBean(FwRightUserService.class);
        List<FwRightUser> users = userService.selectUsersByCode(userIds);
        if (CollectionUtil.isEmpty(users)){
            return participants;
        }
        userIds.forEach(item->{
            FwRightUser user = users.stream().filter(u -> u.getUserCode().equalsIgnoreCase(item) || u.getId().equalsIgnoreCase(item)).findFirst().orElse(null);
            if (user != null){
                WFParticipant row = new WFParticipant();
                row.setId(user.getUserCode());
                row.setName(user.getUserName());
                row.setTypeCode(PERSON);
                participants.add(row);
            }
        });
        /*users.forEach(u -> {
            WFParticipant row = new WFParticipant();
            row.setId(u.getUserCode());
            row.setName(u.getUserName());
            row.setTypeCode(PERSON);
            participants.add(row);
        });*/
        return participants;
    }
}
