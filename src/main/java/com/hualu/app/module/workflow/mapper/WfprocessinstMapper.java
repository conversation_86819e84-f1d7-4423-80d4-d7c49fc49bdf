package com.hualu.app.module.workflow.mapper;

import com.hualu.app.module.workflow.entity.Wfprocessinst;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.workflow.vo.WorkTaskVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-19
 */
public interface WfprocessinstMapper extends BaseMapper<Wfprocessinst> {

    @Select("select u.user_name from bps.wfprocessinst t left join gdgs.fw_right_user u on u.user_code=t.creator where t.processinstid=#{processInstId}")
    String getProcessCreateUserName(long processInstId);

    /**
     * 查询待办任务个数
     * @param sql
     * @param tableName
     * @return
     */
    WorkTaskVo getWorkTaskNum(@Param("sql") String sql,@Param("tableName") String tableName,@Param("facCat") String facCat);
}
