<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.workflow.mapper.WfworkitemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.workflow.entity.Wfworkitem">
        <id column="WORKITEMID" property="workitemid" />
        <result column="WORKITEMNAME" property="workitemname" />
        <result column="WORKITEMTYPE" property="workitemtype" />
        <result column="WORKITEMDESC" property="workitemdesc" />
        <result column="CURRENTSTATE" property="currentstate" />
        <result column="PARTICIPANT" property="participant" />
        <result column="PARTINAME" property="partiname" />
        <result column="PRIORITY" property="priority" />
        <result column="ISTIMEOUT" property="istimeout" />
        <result column="LIMITNUM" property="limitnum" />
        <result column="LIMITNUMDESC" property="limitnumdesc" />
        <result column="CREATETIME" property="createtime" />
        <result column="STARTTIME" property="starttime" />
        <result column="ENDTIME" property="endtime" />
        <result column="FINALTIME" property="finaltime" />
        <result column="REMINDTIME" property="remindtime" />
        <result column="ACTIONURL" property="actionurl" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="ACTIVITYINSTID" property="activityinstid" />
        <result column="STATESLIST" property="stateslist" />
        <result column="TIMEOUTNUM" property="timeoutnum" />
        <result column="TIMEOUTNUMDESC" property="timeoutnumdesc" />
        <result column="PROCESSINSTNAME" property="processinstname" />
        <result column="ACTIVITYINSTNAME" property="activityinstname" />
        <result column="PROCESSDEFID" property="processdefid" />
        <result column="PROCESSDEFNAME" property="processdefname" />
        <result column="PROCESSCHNAME" property="processchname" />
        <result column="ACTIVITYDEFID" property="activitydefid" />
        <result column="ASSISTANT" property="assistant" />
        <result column="ASSISTANTNAME" property="assistantname" />
        <result column="BIZSTATE" property="bizstate" />
        <result column="ALLOWAGENT" property="allowagent" />
        <result column="ROOTPROCINSTID" property="rootprocinstid" />
        <result column="ACTIONMASK" property="actionmask" />
        <result column="URLTYPE" property="urltype" />
        <result column="DEALRESULT" property="dealresult" />
        <result column="DEALOPINION" property="dealopinion" />
        <result column="EXTEND1" property="extend1" />
        <result column="EXTEND2" property="extend2" />
        <result column="EXTEND3" property="extend3" />
        <result column="EXTEND4" property="extend4" />
        <result column="EXTEND5" property="extend5" />
        <result column="EXTEND6" property="extend6" />
        <result column="EXTEND7" property="extend7" />
        <result column="CATALOGUUID" property="cataloguuid" />
        <result column="CATALOGNAME" property="catalogname" />
        <result column="TENANT_ID" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        WORKITEMID, WORKITEMNAME, WORKITEMTYPE, WORKITEMDESC, CURRENTSTATE, PARTICIPANT, PARTINAME, PRIORITY, ISTIMEOUT, LIMITNUM, LIMITNUMDESC, CREATETIME, STARTTIME, ENDTIME, FINALTIME, REMINDTIME, ACTIONURL, PROCESSINSTID, ACTIVITYINSTID, STATESLIST, TIMEOUTNUM, TIMEOUTNUMDESC, PROCESSINSTNAME, ACTIVITYINSTNAME, PROCESSDEFID, PROCESSDEFNAME, PROCESSCHNAME, ACTIVITYDEFID, ASSISTANT, ASSISTANTNAME, BIZSTATE, ALLOWAGENT, ROOTPROCINSTID, ACTIONMASK, URLTYPE, DEALRESULT, DEALOPINION, EXTEND1, EXTEND2, EXTEND3, EXTEND4, EXTEND5, EXTEND6, EXTEND7, CATALOGUUID, CATALOGNAME, TENANT_ID
    </sql>
    <update id="updateWfprocessinst">
        update bps.WFPROCESSINST set CREATOR = #{userCode} where PROCESSINSTID = #{processInstId}
    </update>
    <update id="updateWfwiparticipant">
        update bps.WFWIPARTICIPANT set PARTICIPANTID=#{userCode},PARTICIPANTNAME=#{userName},GLOBALID = 'P{' || #{userCode} || '}' where PROCESSINSTID = #{processInstId} and ACTIVITYDEFID = 'manualActivity'
    </update>
    <update id="updateWfworkitem">
        update bps.WFWORKITEM set PARTICIPANT=#{userCode}, PARTINAME=#{userName} where PROCESSINSTID = #{processInstId}
    </update>
    <select id="selectWorkitemsByYearPlan" resultType="com.hualu.app.module.workflow.entity.Wfworkitem">
        SELECT wi.participant,
               wi.partiname
        FROM bps.wfworkitem wi
        WHERE wi.processinstid = (
            SELECT s.processinstid
            FROM memsdb.year_plan_s s
                     INNER JOIN memsdb.year_plan p ON s.main_year_plan_id = p.main_year_plan_id
                AND s.plan_year = p.plan_year
                AND s.org_id = p.p_org_id
                AND p.processinstid = #{processinstid}
        )
          AND wi.workitemname = '二级业主审核'
    </select>
    <select id="selectApproval" resultType="com.hualu.app.module.workflow.dto.WorkApprovalDto">
        select *
        from (select wi.workitemid,
                     wi.processinstname,
                     wi.currentstate,
                     wi.partiname,
                     TO_CHAR(wi.endtime, 'yyyy-MM-dd HH24:mi:ss')   AS endtime,
                     TO_CHAR(wi.starttime, 'yyyy-MM-dd HH24:mi:ss') AS starttime,
                     wi.workitemname,
                     fo.org_name                                    as approvedeptname,
                     fg.org_name as orgname,
                     WS.stepid,
                     WS.approvesuggestion,
                     WS.remark
              from bps.wfworkitem wi
                       left join bps.WFWORKFLOWSTEP WS on wi.workitemid = ws.workitemid
                       left join gdgs.fw_right_user fu on fu.user_code = wi.participant
                       left join gdgs.fw_right_org fg on fg.id = fu.org_id
                       left join gdgs.fw_right_org fo on fo.id = fu.depart_id
              where wi.processinstid = #{processInstId} order by wi.workitemid) v
    </select>

</mapper>
