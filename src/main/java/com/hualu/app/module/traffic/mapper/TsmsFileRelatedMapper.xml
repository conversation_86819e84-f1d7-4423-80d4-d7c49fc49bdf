<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.traffic.mapper.TsmsFileRelatedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.traffic.entity.TsmsFileRelated">
        <id column="ID" property="id" />
        <result column="FILE_ID" property="fileId" />
        <result column="FACILITIES_ID" property="facilitiesId" />
        <result column="FACILITIES_TYPE" property="facilitiesType" />
        <result column="IS_DELETE" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, FILE_ID, FACILITIES_ID, FACILITIES_TYPE, IS_DELETE
    </sql>

</mapper>
