<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.traffic.mapper.TsmsSignboardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.traffic.entity.TsmsSignboard">
        <id column="ID" property="id" />
        <result column="RL_INTRVL_ID" property="rlIntrvlId" />
        <result column="CONTENT" property="content" />
        <result column="SUPPORT_TYPE" property="supportType" />
        <result column="SIGNBOARD_TYPE" property="signboardType" />
        <result column="SET_COMPANY" property="setCompany" />
        <result column="PAGE_MATERIAL" property="pageMaterial" />
        <result column="SPECIFICATIONS" property="specifications" />
        <result column="SIGNBOARD_POSITION" property="signboardPosition" />
        <result column="REFLECTIVE_FG" property="reflectiveFg" />
        <result column="GALVANIZING_THICKNESS" property="galvanizingThickness" />
        <result column="OPRT_ORG_CODE" property="oprtOrgCode" />
        <result column="PRJ_ORG_CODE" property="prjOrgCode" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="MAINLINE_ID" property="mainlineId" />
        <result column="START_OFFSET" property="startOffset" />
        <result column="MAIN_RAMP_LINE_ID" property="mainRampLineId" />
        <result column="THREAD_RPID" property="threadRpid" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="GIS_ID" property="gisId" />
        <result column="GIS_X" property="gisX" />
        <result column="GIS_Y" property="gisY" />
        <result column="GIS_BDX" property="gisBdx" />
        <result column="GIS_BDY" property="gisBdy" />
        <result column="REMARK" property="remark" />
        <result column="COMPLETION_TIME" property="completionTime" />
        <result column="STAKE" property="stake" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ASSOCIATEID" property="associateid" />
        <result column="PAGE_CSS" property="pageCss" />
        <result column="MODEL_GRADE" property="modelGrade" />
        <result column="BOTTOM_GRADE" property="bottomGrade" />
        <result column="NET_HEIGHT" property="netHeight" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="PHYSICS_CNTR_STAKE" property="physicsCntrStake" />
        <result column="LOGIC_CNTR_STAKES" property="logicCntrStakes" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="ROUTE_VERSION" property="routeVersion" />
        <result column="LOGIC_RAMP_CNTR_STAKE" property="logicRampCntrStake" />
        <result column="LOGIC_START_STAKE" property="logicStartStake" />
        <result column="LOGIC_END_STAKE" property="logicEndStake" />
        <result column="LOGIC_CNTR_STAKE" property="logicCntrStake" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, RL_INTRVL_ID, CONTENT, SUPPORT_TYPE, SIGNBOARD_TYPE, SET_COMPANY, PAGE_MATERIAL, SPECIFICATIONS, SIGNBOARD_POSITION, REFLECTIVE_FG, GALVANIZING_THICKNESS, OPRT_ORG_CODE, PRJ_ORG_CODE, RP_INTRVL_ID, MAINLINE_ID, START_OFFSET, MAIN_RAMP_LINE_ID, THREAD_RPID, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, IS_DELETED, GIS_ID, GIS_X, GIS_Y, GIS_BDX, GIS_BDY, REMARK, COMPLETION_TIME, STAKE, LINE_DIRECT, ASSOCIATEID, PAGE_CSS, MODEL_GRADE, BOTTOM_GRADE, NET_HEIGHT, LINE_CODE, PHYSICS_CNTR_STAKE, LOGIC_CNTR_STAKES, ROUTE_CODE, ROUTE_VERSION, LOGIC_RAMP_CNTR_STAKE, LOGIC_START_STAKE, LOGIC_END_STAKE, LOGIC_CNTR_STAKE
    </sql>

</mapper>
