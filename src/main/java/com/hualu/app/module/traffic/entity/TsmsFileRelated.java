package com.hualu.app.module.traffic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 设立依据中间表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2023-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TsmsFileRelated对象", description="设立依据中间表")
public class TsmsFileRelated implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编号")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "文件ID")
    @TableField("FILE_ID")
    private String fileId;

    @ApiModelProperty(value = "设施ID")
    @TableField("FACILITIES_ID")
    private String facilitiesId;

    @ApiModelProperty(value = "设施类型")
    @TableField("FACILITIES_TYPE")
    private String facilitiesType;

    @ApiModelProperty(value = "是否已删除（0 未删除 1 已删除）")
    @TableField("IS_DELETE")
    private String isDelete;


}
