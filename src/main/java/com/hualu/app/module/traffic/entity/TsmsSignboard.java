package com.hualu.app.module.traffic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标志牌
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TsmsSignboard对象", description="标志牌")
public class TsmsSignboard implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "UUID")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "营运路段区间编码")
    @TableField("RL_INTRVL_ID")
    private String rlIntrvlId;

    @ApiModelProperty(value = "标志牌内容")
    @TableField("CONTENT")
    private String content;

    @ApiModelProperty(value = "支撑方式")
    @TableField("SUPPORT_TYPE")
    private String supportType;

    @ApiModelProperty(value = "标志牌类型")
    @TableField("SIGNBOARD_TYPE")
    private String signboardType;

    @ApiModelProperty(value = "设置单位")
    @TableField("SET_COMPANY")
    private String setCompany;

    @ApiModelProperty(value = "版面材料")
    @TableField("PAGE_MATERIAL")
    private String pageMaterial;

    @ApiModelProperty(value = "规格")
    @TableField("SPECIFICATIONS")
    private String specifications;

    @ApiModelProperty(value = "位置")
    @TableField("SIGNBOARD_POSITION")
    private String signboardPosition;

    @ApiModelProperty(value = "反光膜等级")
    @TableField("REFLECTIVE_FG")
    private String reflectiveFg;

    @ApiModelProperty(value = "镀锌厚度")
    @TableField("GALVANIZING_THICKNESS")
    private Long galvanizingThickness;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "归属项目公司编码")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "营运路段区间ID")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "主路线ID")
    @TableField("MAINLINE_ID")
    private String mainlineId;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "主线桩号对应的主线路段区间ID")
    @TableField("THREAD_RPID")
    private String threadRpid;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除（0）未删除（1）已删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "对应空间数据ID")
    @TableField("GIS_ID")
    private String gisId;

    @ApiModelProperty(value = "经度")
    @TableField("GIS_X")
    private Double gisX;

    @ApiModelProperty(value = "纬度")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "百度坐标经度")
    @TableField("GIS_BDX")
    private Double gisBdx;

    @ApiModelProperty(value = "百度坐标纬度")
    @TableField("GIS_BDY")
    private Double gisBdy;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "设置时间")
    @TableField("COMPLETION_TIME")
    private String completionTime;

    @ApiModelProperty(value = "桩号")
    @TableField("STAKE")
    private Double stake;

    @ApiModelProperty(value = "方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "设置依据ID")
    @TableField("ASSOCIATEID")
    private String associateid;

    @ApiModelProperty(value = "版面样式")
    @TableField("PAGE_CSS")
    private String pageCss;

    @ApiModelProperty(value = "字膜等级")
    @TableField("MODEL_GRADE")
    private String modelGrade;

    @ApiModelProperty(value = "底模等级")
    @TableField("BOTTOM_GRADE")
    private String bottomGrade;

    @ApiModelProperty(value = "净高")
    @TableField("NET_HEIGHT")
    private String netHeight;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "物理中心桩号")
    @TableField("PHYSICS_CNTR_STAKE")
    private Double physicsCntrStake;

    @ApiModelProperty(value = "逻辑中心桩号")
    @TableField("LOGIC_CNTR_STAKES")
    private Double logicCntrStakes;

    @ApiModelProperty(value = "物理路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "路线版本")
    @TableField("ROUTE_VERSION")
    private String routeVersion;

    @ApiModelProperty(value = "匝道逻辑桩号")
    @TableField("LOGIC_RAMP_CNTR_STAKE")
    private Double logicRampCntrStake;

    @ApiModelProperty(value = "逻辑起点桩号")
    @TableField("LOGIC_START_STAKE")
    private Double logicStartStake;

    @ApiModelProperty(value = "逻辑终点桩号")
    @TableField("LOGIC_END_STAKE")
    private Double logicEndStake;

    @ApiModelProperty(value = "逻辑中心桩号值")
    @TableField("LOGIC_CNTR_STAKE")
    private String logicCntrStake;


}
