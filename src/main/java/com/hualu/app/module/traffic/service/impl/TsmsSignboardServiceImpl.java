package com.hualu.app.module.traffic.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.traffic.entity.TsmsSignboard;
import com.hualu.app.module.traffic.mapper.TsmsSignboardMapper;
import com.hualu.app.module.traffic.service.TsmsSignboardService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 标志牌 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@DS("gdgsDs")
@Service
public class TsmsSignboardServiceImpl extends ServiceImpl<TsmsSignboardMapper, TsmsSignboard> implements TsmsSignboardService {

}
