package com.hualu.app.module.traffic.dto.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.tg.dev.excelimport.dto.H_ValidateDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
* <p>
* 设立依据中间表 excel导入对象
* </p>
* <AUTHOR>
* @since 2023-08-18
*/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="TsmsFileRelated对象", description="设立依据中间表")
public class TsmsFileRelatedExcelDto extends H_ValidateDto {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "编号")
    @Excel(name="编号")
    private String id;

    @ApiModelProperty(value = "文件ID")
    @Excel(name="文件ID")
    private String fileId;

    @ApiModelProperty(value = "设施ID")
    @Excel(name="SIGN_BOARD_ID")
    private String facilitiesId;

    @Excel(name="SIGN_BOARD_NAME")
    private String picName;

    @ApiModelProperty(value = "设施类型")
    @Excel(name="设施类型")
    private String facilitiesType;

    @ApiModelProperty(value = "是否已删除（0 未删除 1 已删除）")
    @Excel(name="是否已删除（0 未删除 1 已删除）")
    private String isDelete;
}
