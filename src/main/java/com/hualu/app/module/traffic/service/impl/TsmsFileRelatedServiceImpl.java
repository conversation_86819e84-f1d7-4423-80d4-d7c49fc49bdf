package com.hualu.app.module.traffic.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.traffic.entity.TsmsFileRelated;
import com.hualu.app.module.traffic.mapper.TsmsFileRelatedMapper;
import com.hualu.app.module.traffic.service.TsmsFileRelatedService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设立依据中间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@DS("gdgsDs")
@Service
public class TsmsFileRelatedServiceImpl extends ServiceImpl<TsmsFileRelatedMapper, TsmsFileRelated> implements TsmsFileRelatedService {

}
