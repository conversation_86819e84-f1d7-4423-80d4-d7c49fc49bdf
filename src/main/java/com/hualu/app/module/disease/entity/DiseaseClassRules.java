package com.hualu.app.module.disease.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 病害分级规则表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DiseaseClassRules对象", description="病害分级规则表")
public class DiseaseClassRules implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则ID")
    @TableId("RULE_ID")
    private String ruleId;

    @ApiModelProperty(value = "规则名称")
    @TableField("RULE_NAME")
    private String ruleName;

    @ApiModelProperty(value = "分级模式")
    @TableField("CLASS_MODE")
    private String classMode;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "是否已删除")
    @TableField("IS_DELETED")
    private String isDeleted;


}
