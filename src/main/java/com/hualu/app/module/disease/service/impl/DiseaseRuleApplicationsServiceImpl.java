package com.hualu.app.module.disease.service.impl;

import com.hualu.app.module.disease.entity.DiseaseRuleApplications;
import com.hualu.app.module.disease.mapper.DiseaseRuleApplicationsMapper;
import com.hualu.app.module.disease.service.DiseaseRuleApplicationsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 规则应用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class DiseaseRuleApplicationsServiceImpl extends ServiceImpl<DiseaseRuleApplicationsMapper, DiseaseRuleApplications> implements DiseaseRuleApplicationsService {

}
