<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.disease.mapper.DiseaseClassRulesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.disease.entity.DiseaseClassRules">
        <id column="RULE_ID" property="ruleId" />
        <result column="RULE_NAME" property="ruleName" />
        <result column="CLASS_MODE" property="classMode" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="REMARK" property="remark" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RULE_ID, RULE_NAME, CLASS_MODE, CREATE_TIME, REMARK, IS_DELETED
    </sql>

</mapper>
