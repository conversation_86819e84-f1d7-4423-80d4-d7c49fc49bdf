package com.hualu.app.module.disease.mapper;

import com.hualu.app.module.disease.entity.DiseaseAnalysisRuleDetails;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 病害分析规则明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface DiseaseAnalysisRuleDetailsMapper extends BaseMapper<DiseaseAnalysisRuleDetails> {

    List<DiseaseAnalysisRuleDetails> queryRuleDetailsByOrg(String orgId);
}
