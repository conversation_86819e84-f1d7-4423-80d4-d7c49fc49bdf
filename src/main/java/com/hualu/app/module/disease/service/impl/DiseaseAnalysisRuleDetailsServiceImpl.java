package com.hualu.app.module.disease.service.impl;

import com.hualu.app.module.disease.entity.DiseaseAnalysisRuleDetails;
import com.hualu.app.module.disease.mapper.DiseaseAnalysisRuleDetailsMapper;
import com.hualu.app.module.disease.service.DiseaseAnalysisRuleDetailsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 病害分析规则明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
public class DiseaseAnalysisRuleDetailsServiceImpl extends ServiceImpl<DiseaseAnalysisRuleDetailsMapper, DiseaseAnalysisRuleDetails> implements DiseaseAnalysisRuleDetailsService {

    @Override
    public List<DiseaseAnalysisRuleDetails> queryRuleDetailsByOrg(String orgId) {
        return this.baseMapper.queryRuleDetailsByOrg(orgId);
    }
}
