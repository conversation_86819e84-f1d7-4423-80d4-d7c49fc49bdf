package com.hualu.app.module.disease.service;

import com.hualu.app.module.disease.entity.DiseaseAnalysisRuleDetails;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 病害分析规则明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface DiseaseAnalysisRuleDetailsService extends IService<DiseaseAnalysisRuleDetails> {

    List<DiseaseAnalysisRuleDetails> queryRuleDetailsByOrg(String orgId);
}
