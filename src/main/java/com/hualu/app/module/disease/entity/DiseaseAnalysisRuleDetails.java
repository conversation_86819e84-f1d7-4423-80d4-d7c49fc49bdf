package com.hualu.app.module.disease.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 病害分析规则明细表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DiseaseAnalysisRuleDetails对象", description="病害分析规则明细表")
public class DiseaseAnalysisRuleDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("DETAIL_ID")
    private String detailId;

    @ApiModelProperty(value = "规则ID")
    @TableField("RULE_ID")
    private String ruleId;

    @ApiModelProperty(value = "设施分类")
    @TableField("FACILITY_TYPE")
    private String facilityType;

    @ApiModelProperty(value = "病害类型名称")
    @TableField("DISEASE_TYPE")
    private String diseaseType;

    @ApiModelProperty(value = "病害类型编码")
    @TableField("DSS_TYPE")
    private String dssType;

    @ApiModelProperty(value = "最小值")
    @TableField("MIN_VALUE")
    private BigDecimal minValue;

    @ApiModelProperty(value = "最大值")
    @TableField("MAX_VALUE")
    private BigDecimal maxValue;

    @ApiModelProperty(value = "优先级1建议")
    @TableField("FIRST_PRIORITY_SUGGESTION")
    private String firstPrioritySuggestion;

    @ApiModelProperty(value = "优先级1颜色")
    @TableField("FIRST_PRIORITY_COLOR")
    private String firstPriorityColor;

    @ApiModelProperty(value = "优先级2建议")
    @TableField("SECOND_PRIORITY_SUGGESTION")
    private String secondPrioritySuggestion;

    @ApiModelProperty(value = "优先级2颜色")
    @TableField("SECOND_PRIORITY_COLOR")
    private String secondPriorityColor;

    @ApiModelProperty(value = "优先级3建议")
    @TableField("THIRD_PRIORITY_SUGGESTION")
    private String thirdPrioritySuggestion;

    @ApiModelProperty(value = "优先级3颜色")
    @TableField("THIRD_PRIORITY_COLOR")
    private String thirdPriorityColor;

    @ApiModelProperty(value = "单位")
    @TableField("UNIT")
    private String unit;
    @TableField(exist = false)
    private int orderNo;
    @TableField(exist = false)
    private String dssTypeName;

}
