package com.hualu.app.module.disease.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 规则应用表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DiseaseRuleApplications对象", description="规则应用表")
public class DiseaseRuleApplications implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "所属规则ID")
    @TableField("RULE_ID")
    private String ruleId;

    @ApiModelProperty(value = "机构ID")
    @TableField("ORG_ID")
    private String orgId;

    @ApiModelProperty(value = "应用场景名称")
    @TableField("SCENARIO_NAME")
    private String scenarioName;


}
