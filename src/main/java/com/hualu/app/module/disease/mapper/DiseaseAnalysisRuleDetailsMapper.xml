<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.disease.mapper.DiseaseAnalysisRuleDetailsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.disease.entity.DiseaseAnalysisRuleDetails">
        <id column="DETAIL_ID" property="detailId" />
        <result column="RULE_ID" property="ruleId" />
        <result column="FACILITY_TYPE" property="facilityType" />
        <result column="DISEASE_TYPE" property="diseaseType" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="MIN_VALUE" property="minValue" />
        <result column="MAX_VALUE" property="maxValue" />
        <result column="FIRST_PRIORITY_SUGGESTION" property="firstPrioritySuggestion" />
        <result column="FIRST_PRIORITY_COLOR" property="firstPriorityColor" />
        <result column="SECOND_PRIORITY_SUGGESTION" property="secondPrioritySuggestion" />
        <result column="SECOND_PRIORITY_COLOR" property="secondPriorityColor" />
        <result column="THIRD_PRIORITY_SUGGESTION" property="thirdPrioritySuggestion" />
        <result column="THIRD_PRIORITY_COLOR" property="thirdPriorityColor" />
        <result column="UNIT" property="unit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DETAIL_ID, RULE_ID, FACILITY_TYPE, DISEASE_TYPE, DSS_TYPE, MIN_VALUE, MAX_VALUE, FIRST_PRIORITY_SUGGESTION, FIRST_PRIORITY_COLOR, SECOND_PRIORITY_SUGGESTION, SECOND_PRIORITY_COLOR, THIRD_PRIORITY_SUGGESTION, THIRD_PRIORITY_COLOR, UNIT
    </sql>
    <select id="queryRuleDetailsByOrg"
            resultType="com.hualu.app.module.disease.entity.DiseaseAnalysisRuleDetails">
        select a.* from DISEASE_ANALYSIS_RULE_DETAILS a
        inner join DISEASE_RULE_APPLICATIONS b on a.RULE_ID = b.RULE_ID
        where b.ORG_ID = #{orgId}
    </select>

</mapper>
