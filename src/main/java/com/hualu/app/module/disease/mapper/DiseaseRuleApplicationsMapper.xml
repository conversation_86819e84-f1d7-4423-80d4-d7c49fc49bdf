<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.disease.mapper.DiseaseRuleApplicationsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.disease.entity.DiseaseRuleApplications">
        <id column="ID" property="id" />
        <result column="RULE_ID" property="ruleId" />
        <result column="ORG_ID" property="orgId" />
        <result column="SCENARIO_NAME" property="scenarioName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, RULE_ID, ORG_ID, SCENARIO_NAME
    </sql>

</mapper>
