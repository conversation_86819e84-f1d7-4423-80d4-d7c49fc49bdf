package com.hualu.app.module.check.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.check.domain.MtmsDssinfoProject;
import com.hualu.app.module.check.domain.TunnelEvaluation;
import com.hualu.app.module.check.domain.TunnelEvaluationFile;
import com.hualu.app.module.check.domain.TunnelEvaluationRange;
import com.hualu.app.module.check.service.TunnelEvaluationFileService;
import com.hualu.app.module.check.service.TunnelEvaluationRangeService;
import com.hualu.app.module.check.service.TunnelEvaluationService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.hualu.app.module.check.vo.TunnelDiseaseVO;
import org.springframework.util.StringUtils;
import com.hualu.app.module.check.domain.DssType;

@Api(tags = "隧道定检项目管理")
@Validated
@RestController
@RequestMapping("/tunnelEvaluation")
@Slf4j
public class TunnelEvaluationController {

    @Autowired
    private TunnelEvaluationService evaluationService;

    @Autowired
    private TunnelEvaluationRangeService rangeService;

    @Autowired
    private TunnelEvaluationFileService fileService;

    @Autowired
    private FwRightOrgService fwRightOrgService;

    @ApiRegister(value = "tunnelEvaluation:create", businessType = BusinessType.INSERT)
    @ApiOperation("创建隧道定检项目")
    @PostMapping("/create")
    public RestResult<TunnelEvaluation> createEvaluation(@RequestBody TunnelEvaluation evaluation) {
        // 设置创建信息
        evaluation.setCreateUser(CustomRequestContextHolder.getUserCode());
        evaluation.setCreateTime(new Date());
        evaluation.setIsDeleted(0);
        evaluation.setId(StringUtil.getUUID());
        evaluationService.save(evaluation);
        return RestResult.success(evaluation);
    }

    @ApiRegister(value = "tunnelEvaluation:update", businessType = BusinessType.UPDATE)
    @ApiOperation("更新隧道定检项目")
    @PutMapping("/update")
    public RestResult<TunnelEvaluation> updateEvaluation(@RequestBody TunnelEvaluation evaluation) {
        // 设置更新信息
        evaluation.setUpdateUser(CustomRequestContextHolder.getUserCode());
        evaluation.setUpdateTime(new Date());
        
        // 使用UpdateWrapper来更新，包括null值
        UpdateWrapper<TunnelEvaluation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", evaluation.getId())
                    .eq("IS_DELETED", 0);
                    
        // 设置所有可能的字段，包括null值
        updateWrapper.set("PROJECT_CODE", evaluation.getProjectCode())
                    .set("PROJECT_TYPE", evaluation.getProjectType())
                    .set("PROJECT_NAME", evaluation.getProjectName())
                    .set("CLASSIFY", evaluation.getClassify())
                    .set("PROJECT_BRIEF", evaluation.getProjectBrief())
                    .set("PROJECT_START_DATE", evaluation.getProjectStartDate())
                    .set("PROJECT_END_DATE", evaluation.getProjectEndDate())
                    .set("PROJECT_YEAR", evaluation.getProjectYear())
                    .set("ENGINEERING_GRADE", evaluation.getEngineeringGrade())
                    .set("IMPLEMENTATION_MODE", evaluation.getImplementationMode())
                    .set("TOTAL_COST", evaluation.getTotalCost())
                    .set("APPROVAL_DOC_NO", evaluation.getApprovalDocNo())
                    .set("CHECKER", evaluation.getChecker())
                    .set("REVIEW_UNIT", evaluation.getReviewUnit())
                    .set("REVIEWER", evaluation.getReviewer())
                    .set("REVIEW_DATE", evaluation.getReviewDate())
                    .set("INSPECTION_UNIT_NAME", evaluation.getInspectionUnitName())
                    .set("INSPECTION_UNIT_CODE", evaluation.getInspectionUnitCode())
                    .set("MAINTENANCE_UNIT_NAME", evaluation.getMaintenanceUnitName())
                    .set("MAINTENANCE_UNIT_CODE", evaluation.getMaintenanceUnitCode())
                    .set("MAINTENANCE_ROUTE_NAME", evaluation.getMaintenanceRouteName())
                    .set("REMARKS", evaluation.getRemarks())
                    .set("UPDATE_TIME", evaluation.getUpdateTime())
                    .set("UPDATE_USER", evaluation.getUpdateUser());
        
        boolean success = evaluationService.update(updateWrapper);
        if (!success) {
            return RestResult.error("更新失败");
        }
        
        return RestResult.success(evaluation);
    }

    @ApiRegister(value = "tunnelEvaluation:delete", businessType = BusinessType.DELETE)
    @ApiOperation("删除隧道定检项目")
    @DeleteMapping("/delete/{id}")
    public RestResult<Void> deleteEvaluation(@PathVariable String id) {
        // 使用UpdateWrapper进行更新
        UpdateWrapper<TunnelEvaluation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                    .eq("IS_DELETED", 0)
                    .set("IS_DELETED", 1)
                    .set("UPDATE_USER", CustomRequestContextHolder.getUserCode())
                    .set("UPDATE_TIME", new Date());
        
        boolean success = evaluationService.update(updateWrapper);
        if (!success) {
            return RestResult.error("删除失败");
        }
        
        return RestResult.success("");
    }

    @ApiRegister(value = "tunnelEvaluation:get", businessType = BusinessType.OTHER)
    @ApiOperation("获取隧道定检项目详情")
    @GetMapping("/get/{id}")
    public RestResult<TunnelEvaluation> getEvaluation(@PathVariable String id) {
        LambdaQueryWrapper<TunnelEvaluation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TunnelEvaluation::getId, id)
                .eq(TunnelEvaluation::getIsDeleted, 0);
        return RestResult.success(evaluationService.getOne(wrapper));
    }

    @ApiRegister(value = "tunnelEvaluation:page", businessType = BusinessType.OTHER)
    @ApiOperation("分页查询隧道定检项目")
    @PostMapping("/page")
    public RestResult<List<TunnelEvaluation>> pageEvaluation(@RequestBody(required = false) Map<String, Object> params) {
        QueryWrapper<TunnelEvaluation> queryWrapper = new QueryWrapper<>();
        // 添加通用查询条件
        if (params != null) {
            // 创建一个新的Map来存储非模糊查询的参数
            Map<String, Object> exactParams = new HashMap<>(params);
            // 移除将要进行模糊查询的参数
            exactParams.remove("projectCode");
            exactParams.remove("projectName");
            
            // 处理其他查询参数（精确匹配）
            H_BatisQuery.setFieldValue2In(queryWrapper, exactParams, TunnelEvaluation.class);
            
            // 处理特定字段的模糊匹配
            if (params.containsKey("projectCode") && params.get("projectCode") != null) {
                queryWrapper.like("PROJECT_CODE", "%" + params.get("projectCode") + "%");
            }
            if (params.containsKey("projectName") && params.get("projectName") != null) {
                queryWrapper.like("PROJECT_NAME", "%" + params.get("projectName") + "%");
            }
            if (params.containsKey("projectYear")) {
                queryWrapper.eq("PROJECT_YEAR", params.get("projectYear"));
            }
            if (params.containsKey("maintenanceUnitCode")) {
                queryWrapper.eq("MAINTENANCE_UNIT_CODE", params.get("maintenanceUnitCode"));
            }
        }
        
        // 添加默认条件：未删除
        queryWrapper.eq("IS_DELETED", 0);
        
        // 添加默认排序
        queryWrapper.orderByDesc("CREATE_TIME");
        
        // 获取分页参数
        int current = 1;
        int size = 10;
        
        if (params != null) {
            if (params.containsKey("pageIndex") && params.get("pageIndex") != null) {
                try {
                    if (params.get("pageIndex") instanceof Number) {
                        current = ((Number) params.get("pageIndex")).intValue() + 1;
                    } else {
                        current = Integer.parseInt(params.get("pageIndex").toString()) + 1;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid current page parameter: {}", params.get("pageIndex"));
                }
            }
            
            if (params.containsKey("pageSize") && params.get("pageSize") != null) {
                try {
                    if (params.get("pageSize") instanceof Number) {
                        size = ((Number) params.get("pageSize")).intValue();
                    } else {
                        size = Integer.parseInt(params.get("pageSize").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid page size parameter: {}", params.get("pageSize"));
                }
            } else if (params.containsKey("size") && params.get("size") != null) {
                try {
                    if (params.get("size") instanceof Number) {
                        size = ((Number) params.get("size")).intValue();
                    } else {
                        size = Integer.parseInt(params.get("size").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid page size parameter: {}", params.get("size"));
                }
            }
        }
        
        // 执行分页查询
        Page<TunnelEvaluation> page = new Page<>(current, size);
        IPage<TunnelEvaluation> resultPage = evaluationService.page(page, queryWrapper);
        
        // 返回结果
        return RestResult.success(
            resultPage.getRecords(), 
            resultPage.getTotal(), 
            resultPage.getCurrent(), 
            resultPage.getSize()
        );
    }

    @ApiRegister(value = "tunnelEvaluation:deleteBatch", businessType = BusinessType.DELETE)
    @ApiOperation("批量删除隧道定检项目")
    @PostMapping("/deleteBatch")
    public RestResult<Void> deleteBatchEvaluation(@RequestBody List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return RestResult.error("请选择要删除的记录");
        }

        // 使用UpdateWrapper进行批量更新
        UpdateWrapper<TunnelEvaluation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                    .eq("IS_DELETED", 0)
                    .set("IS_DELETED", 1)
                    .set("UPDATE_USER", CustomRequestContextHolder.getUserCode())
                    .set("UPDATE_TIME", new Date());
        
        boolean success = evaluationService.update(updateWrapper);
        if (!success) {
            return RestResult.error("批量删除失败");
        }
        
        return RestResult.success("");
    }

    // 隧道定检范围相关接口
    @ApiRegister(value = "tunnelEvaluation:range:create", businessType = BusinessType.INSERT)
    @ApiOperation("添加隧道定检范围")
    @PostMapping("/range/create")
    public RestResult<TunnelEvaluationRange> createRange(@RequestBody TunnelEvaluationRange range) {
        // 检查是否已存在相同的定检项目ID和隧道ID组合
        QueryWrapper<TunnelEvaluationRange> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("TUNNEL_EVALUATION_ID", range.getTunnelEvaluationId())
                   .eq("TUNNEL_ID", range.getTunnelId())
                   .eq("IS_DELETED", 0);
        
        TunnelEvaluationRange existingRange = rangeService.getOne(queryWrapper);
        
        if (existingRange != null) {
            // 如果存在，更新refTime
            existingRange.setRefTime(new Date());
            rangeService.updateById(existingRange);
            return RestResult.success(existingRange);
        }
        
        // 如果不存在，创建新记录
        range.setRefTime(new Date());
        range.setIsDeleted(0);
        range.setId(StringUtil.getUUID());
        rangeService.save(range);
        return RestResult.success(range);
    }

    @ApiRegister(value = "tunnelEvaluation:range:update", businessType = BusinessType.UPDATE)
    @ApiOperation("更新隧道定检范围")
    @PutMapping("/range/update")
    public RestResult<TunnelEvaluationRange> updateRange(@RequestBody TunnelEvaluationRange range) {
        rangeService.updateById(range);
        return RestResult.success(range);
    }

    @ApiRegister(value = "tunnelEvaluation:range:delete", businessType = BusinessType.DELETE)
    @ApiOperation("删除隧道定检范围")
    @DeleteMapping("/range/delete/{id}")
    public RestResult<Void> deleteRange(@PathVariable String id) {
        TunnelEvaluationRange range = new TunnelEvaluationRange();
        range.setId(id);
        range.setIsDeleted(1);
        rangeService.updateById(range);
        return RestResult.success("");
    }

    @ApiRegister(value = "tunnelEvaluation:range:list", businessType = BusinessType.OTHER)
    @ApiOperation("获取隧道定检范围列表")
    @PostMapping("/range/list")
    public RestResult<List<TunnelEvaluationRange>> listRanges(@RequestBody(required = false) Map<String, Object> params) {
        // 获取分页参数
        int current = 1;
        int size = 10;
        if (params != null) {
            if (params.containsKey("pageIndex") && params.get("pageIndex") != null) {
                try {
                    if (params.get("pageIndex") instanceof Number) {
                        current = ((Number) params.get("pageIndex")).intValue() + 1;
                    } else {
                        current = Integer.parseInt(params.get("pageIndex").toString()) + 1;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid current page parameter: {}", params.get("pageIndex"));
                }
            }
            
            if (params.containsKey("pageSize") && params.get("pageSize") != null) {
                try {
                    if (params.get("pageSize") instanceof Number) {
                        size = ((Number) params.get("pageSize")).intValue();
                    } else {
                        size = Integer.parseInt(params.get("pageSize").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid page size parameter: {}", params.get("pageSize"));
                }
            }
        }

        Page<TunnelEvaluationRange> page = new Page<>(current, size);
        // 构建查询条件
        QueryWrapper<TunnelEvaluationRange> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("a.IS_DELETED", 0);
        
        if (params != null) {
            // 添加查询条件
            if (params.containsKey("tunnelEvaluationId")) {
                queryWrapper.eq("a.TUNNEL_EVALUATION_ID", params.get("tunnelEvaluationId"));
            }
            if (params.containsKey("tunnelId")) {
                queryWrapper.eq("a.TUNNEL_ID", params.get("tunnelId"));
            }
        }
        
        // 执行关联查询
        IPage<TunnelEvaluationRange> resultPage = rangeService.pageRangesWithTunnel(page, queryWrapper);

        return RestResult.success(
                resultPage.getRecords(),
                resultPage.getTotal(),
                resultPage.getCurrent(),
                resultPage.getSize()
        );
    }

    @ApiRegister(value = "tunnelEvaluation:range:deleteBatch", businessType = BusinessType.DELETE)
    @ApiOperation("批量删除隧道定检范围")
    @PostMapping("/range/deleteBatch")
    public RestResult<Void> deleteBatchRange(@RequestBody List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return RestResult.error("请选择要删除的记录");
        }

        UpdateWrapper<TunnelEvaluationRange> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                    .eq("IS_DELETED", 0)
                    .set("IS_DELETED", 1);
        
        boolean success = rangeService.update(updateWrapper);
        if (!success) {
            return RestResult.error("批量删除失败");
        }
        
        return RestResult.success("");
    }

    // 隧道定检文件相关接口
    @ApiRegister(value = "tunnelEvaluation:file:create", businessType = BusinessType.INSERT)
    @ApiOperation("批量添加隧道定检文件")
    @PostMapping("/file/create")
    public RestResult<List<TunnelEvaluationFile>> createFile(@RequestBody List<TunnelEvaluationFile> files) {
        if (files == null || files.isEmpty()) {
            return RestResult.error("请选择要添加的文件");
        }
        
        // 设置每个文件的基本信息
        for (TunnelEvaluationFile file : files) {
            file.setIsDeleted(0);
            file.setId(StringUtil.getUUID());
        }
        
        // 批量保存
        fileService.saveBatch(files);
        
        return RestResult.success(files);
    }

    @ApiRegister(value = "tunnelEvaluation:file:update", businessType = BusinessType.UPDATE)
    @ApiOperation("更新隧道定检文件")
    @PutMapping("/file/update")
    public RestResult<TunnelEvaluationFile> updateFile(@RequestBody TunnelEvaluationFile file) {
        fileService.updateById(file);
        return RestResult.success(file);
    }

    @ApiRegister(value = "tunnelEvaluation:file:delete", businessType = BusinessType.DELETE)
    @ApiOperation("删除隧道定检文件")
    @DeleteMapping("/file/delete/{id}")
    public RestResult<Void> deleteFile(@PathVariable String id) {
        UpdateWrapper<TunnelEvaluationFile> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id)
                .eq("IS_DELETED", 0)
                .set("IS_DELETED", 1);
        boolean success = fileService.update(updateWrapper);
        if (!success) {
            return RestResult.error("删除失败");
        }
        return RestResult.success("");
    }

    @ApiRegister(value = "tunnelEvaluation:file:list", businessType = BusinessType.OTHER)
    @ApiOperation("获取隧道定检文件列表")
    @PostMapping("/file/list")
    public RestResult<List<TunnelEvaluationFile>> listFiles(@RequestBody(required = false) Map<String, Object> params) {
        QueryWrapper<TunnelEvaluationFile> queryWrapper = new QueryWrapper<>();
        
        // 添加通用查询条件
        if (params != null) {
            // 处理查询参数
            H_BatisQuery.setFieldValue2In(queryWrapper, params, TunnelEvaluationFile.class);
            
            // 处理特定字段的精确匹配
            if (params.containsKey("rangeId")) {
                queryWrapper.eq("RANGE_ID", params.get("rangeId"));
            }
            
            if (params.containsKey("fileId")) {
                queryWrapper.eq("FILE_ID", params.get("fileId"));
            }
        }
        
        // 添加默认条件：未删除
        queryWrapper.eq("IS_DELETED", 0);
        
        // 添加默认排序
        queryWrapper.orderByDesc("FILE_ID");

        List<TunnelEvaluationFile> resultList = fileService.list(queryWrapper);

        return RestResult.success(resultList);
    }

    @ApiRegister(value = "tunnelEvaluation:file:deleteBatch", businessType = BusinessType.DELETE)
    @ApiOperation("批量删除隧道定检文件")
    @PostMapping("/file/deleteBatch")
    public RestResult<Void> deleteBatchFile(@RequestBody List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return RestResult.error("请选择要删除的记录");
        }

        UpdateWrapper<TunnelEvaluationFile> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", ids)
                .eq("IS_DELETED", 0)
                .set("IS_DELETED", 1);

        boolean success = fileService.update(updateWrapper);
        if (!success) {
            return RestResult.error("批量删除失败");
        }

        return RestResult.success("");
    }

    @ApiRegister(value = "tunnelEvaluation:oprtOrg", businessType = BusinessType.OTHER)
    @ApiOperation("获取管养单位列表")
    @GetMapping("/oprtOrg")
    public RestResult<List<Map<String, String>>> getOprtOrgCodes() {
        String orgCode = CustomRequestContextHolder.getOrgIdString();
        List<Map<String, String>> result = fwRightOrgService.queryOprtOrgCodes(orgCode);
        return RestResult.success(result);
    }

    @ApiRegister(value = "tunnelEvaluation:checkOrg", businessType = BusinessType.OTHER)
    @ApiOperation("获取检测单位列表")
    @GetMapping("/checkOrg")
    public RestResult<List<Map<String, String>>> getCheckOrgCodes() {
        String orgCode = CustomRequestContextHolder.getOrgIdString();
        List<Map<String, String>> result = fwRightOrgService.queryCheckOrgCodes(orgCode);
        return RestResult.success(result);
    }

    @ApiRegister(value = "tunnelEvaluation:route", businessType = BusinessType.OTHER)
    @ApiOperation("获取路段列表")
    @GetMapping("/route")
    public RestResult<List<String>> getRoute(String orgCode) {
        List<String> result = fwRightOrgService.queryRoute(orgCode);
        return RestResult.success(result);
    }

    @ApiRegister(value = "tunnelEvaluation:civilDisease:list", businessType = BusinessType.OTHER)
    @ApiOperation("获取隧道土建病害列表")
    @PostMapping("/civilDisease/list")
    public RestResult<IPage<TunnelDiseaseVO>> listTunnelCivilDiseases(@RequestBody(required = false) Map<String, Object> params) {
        // 获取分页参数
        int current = 1;
        int size = 10;
        
        if (params != null) {
            if (params.containsKey("pageIndex") && params.get("pageIndex") != null) {
                try {
                    if (params.get("pageIndex") instanceof Number) {
                        current = ((Number) params.get("pageIndex")).intValue() + 1;
                    } else {
                        current = Integer.parseInt(params.get("pageIndex").toString()) + 1;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid current page parameter: {}", params.get("pageIndex"));
                }
            }
            
            if (params.containsKey("pageSize") && params.get("pageSize") != null) {
                try {
                    if (params.get("pageSize") instanceof Number) {
                        size = ((Number) params.get("pageSize")).intValue();
                    } else {
                        size = Integer.parseInt(params.get("pageSize").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid page size parameter: {}", params.get("pageSize"));
                }
            }
        }
        
        // 获取查询参数
        String tunnelId = params != null ? (String) params.get("tunnelId") : null;
        String projectId = params != null ? (String) params.get("projectId") : null;
        String dssId = params != null ? (String) params.get("dssId") : null;
        
        if (tunnelId == null || projectId == null) {
            return RestResult.error("隧道ID和项目ID不能为空");
        }
        
        // 执行分页查询
        Page<TunnelDiseaseVO> page = new Page<>(current, size);
        IPage<TunnelDiseaseVO> result = evaluationService.pageTunnelCivilDiseases(page, tunnelId, projectId, dssId);
        
        return RestResult.success(result);
    }

    @ApiRegister(value = "tunnelEvaluation:disease:save", businessType = BusinessType.INSERT)
    @ApiOperation("保存隧道病害信息")
    @PostMapping("/disease/save")
    public RestResult<String> saveTunnelDisease(@RequestBody Map<String, Object> formData) {
        try {
            // 转换为实体对象
            MtmsDssinfoProject mtmsDssinfoProject = convertToEntity(formData);
            // 处理桩号逻辑
            processStakeLogic(mtmsDssinfoProject, formData);
            // 保存病害信息
            evaluationService.saveTunnelDisease(mtmsDssinfoProject);
            return RestResult.success("保存成功");
        } catch (Exception e) {
            log.error("保存病害信息失败", e);
            return RestResult.error(e.getMessage());
        }
    }
    
    /**
     * 将前端表单数据转换为病害实体
     */
    private MtmsDssinfoProject convertToEntity(Map<String, Object> formData) {
        MtmsDssinfoProject entity = new MtmsDssinfoProject();
        
        // 设置基本信息
        if (formData.containsKey("dssId") && StringUtils.hasText(String.valueOf(formData.get("dssId")))) {
            entity.setDssId(String.valueOf(formData.get("dssId")));
        } else {
            entity.setDssId(StringUtil.getUUID());
        }
        
        // 复制基本属性
        if (formData.containsKey("projectId")) entity.setProjectId(String.valueOf(formData.get("projectId")));
        if (formData.containsKey("structId")) entity.setStructId(String.valueOf(formData.get("structId")));
        if (formData.containsKey("structPartId")) entity.setStructPartId(String.valueOf(formData.get("structPartId")));
        if (formData.containsKey("structCompId")) entity.setStructCompId(String.valueOf(formData.get("structCompId")));
        if (formData.containsKey("dssType")) entity.setDssType(String.valueOf(formData.get("dssType")));
        if (formData.containsKey("dssDegree")) entity.setDssDegree(String.valueOf(formData.get("dssDegree")));
        if (formData.containsKey("lineDirect")) entity.setLineDirect(String.valueOf(formData.get("lineDirect")));
        if (formData.containsKey("lane")) entity.setLane(String.valueOf(formData.get("lane")));
        if (formData.containsKey("tunnelMouth")) entity.setTunnelMouth(String.valueOf(formData.get("tunnelMouth")));
        if (formData.containsKey("mntnAdvice")) entity.setMntnAdvice(String.valueOf(formData.get("mntnAdvice")));
        if (formData.containsKey("dssDesc")) entity.setDssDesc(String.valueOf(formData.get("dssDesc")));
        if (formData.containsKey("dssCause")) entity.setDssCause(String.valueOf(formData.get("dssCause")));
        
        // 设置桩号信息
        if (formData.containsKey("stake")) {
            try {
                entity.setStake(Double.valueOf(String.valueOf(formData.get("stake"))));
            } catch (NumberFormatException e) {
                log.warn("无效的桩号值: {}", formData.get("stake"));
            }
        }
        
        if (formData.containsKey("startStakeNum")) {
            try {
                entity.setStartStakeNum(Double.valueOf(String.valueOf(formData.get("startStakeNum"))));
            } catch (NumberFormatException e) {
                log.warn("无效的起始桩号值: {}", formData.get("startStakeNum"));
            }
        }
        
        if (formData.containsKey("endStakeNum")) {
            try {
                entity.setEndStakeNum(Double.valueOf(String.valueOf(formData.get("endStakeNum"))));
            } catch (NumberFormatException e) {
                log.warn("无效的结束桩号值: {}", formData.get("endStakeNum"));
            }
        }
        
        if (formData.containsKey("stakeHigh")) {
            try {
                entity.setStakeHigh(Double.valueOf(String.valueOf(formData.get("stakeHigh"))));
            } catch (NumberFormatException e) {
                log.warn("无效的桩号高度值: {}", formData.get("stakeHigh"));
            }
        }
        
        if (formData.containsKey("startHigh")) {
            try {
                entity.setStartHigh(Double.valueOf(String.valueOf(formData.get("startHigh"))));
            } catch (NumberFormatException e) {
                log.warn("无效的起始高度值: {}", formData.get("startHigh"));
            }
        }
        
        if (formData.containsKey("endHigh")) {
            try {
                entity.setEndHigh(Double.valueOf(String.valueOf(formData.get("endHigh"))));
            } catch (NumberFormatException e) {
                log.warn("无效的结束高度值: {}", formData.get("endHigh"));
            }
        }
        
        // 设置尺寸信息
        processSizeFields(formData, entity);
        
        // 设置默认字段
        entity.setIsphone(0); // 非移动端
        entity.setFindDssUserName(CustomRequestContextHolder.getUserName()); // 发现病害的人
        entity.setFoundDate(new Date()); // 发现日期
        
        return entity;
    }
    
    /**
     * 处理尺寸相关字段
     */
    private void processSizeFields(Map<String, Object> formData, MtmsDssinfoProject entity) {
        // 长度
        if (formData.containsKey("dssL")) {
            try {
                entity.setDssL(Double.valueOf(String.valueOf(formData.get("dssL"))));
            } catch (NumberFormatException e) {
                log.warn("无效的长度值: {}", formData.get("dssL"));
            }
        }
        if (formData.containsKey("dssLUnit")) entity.setDssLUnit(String.valueOf(formData.get("dssLUnit")));
        
        // 宽度
        if (formData.containsKey("dssW")) {
            try {
                entity.setDssW(Double.valueOf(String.valueOf(formData.get("dssW"))));
            } catch (NumberFormatException e) {
                log.warn("无效的宽度值: {}", formData.get("dssW"));
            }
        }
        if (formData.containsKey("dssWUnit")) entity.setDssWUnit(String.valueOf(formData.get("dssWUnit")));
        
        // 深度
        if (formData.containsKey("dssD")) {
            try {
                entity.setDssD(Double.valueOf(String.valueOf(formData.get("dssD"))));
            } catch (NumberFormatException e) {
                log.warn("无效的深度值: {}", formData.get("dssD"));
            }
        }
        if (formData.containsKey("dssDUnit")) entity.setDssDUnit(String.valueOf(formData.get("dssDUnit")));
        
        // 数量
        if (formData.containsKey("dssN")) {
            try {
                entity.setDssN(Double.valueOf(String.valueOf(formData.get("dssN"))));
            } catch (NumberFormatException e) {
                log.warn("无效的数量值: {}", formData.get("dssN"));
            }
        }
        if (formData.containsKey("dssNUnit")) entity.setDssNUnit(String.valueOf(formData.get("dssNUnit")));
        
        // 面积
        if (formData.containsKey("dssA")) {
            try {
                entity.setDssA(Double.valueOf(String.valueOf(formData.get("dssA"))));
            } catch (NumberFormatException e) {
                log.warn("无效的面积值: {}", formData.get("dssA"));
            }
        }
        if (formData.containsKey("dssAUnit")) entity.setDssAUnit(String.valueOf(formData.get("dssAUnit")));
        
        // 体积
        if (formData.containsKey("dssV")) {
            try {
                entity.setDssV(Double.valueOf(String.valueOf(formData.get("dssV"))));
            } catch (NumberFormatException e) {
                log.warn("无效的体积值: {}", formData.get("dssV"));
            }
        }
        if (formData.containsKey("dssVUnit")) entity.setDssVUnit(String.valueOf(formData.get("dssVUnit")));
        
        // 百分比
        if (formData.containsKey("dssP")) {
            try {
                entity.setDssP(Double.valueOf(String.valueOf(formData.get("dssP"))));
            } catch (NumberFormatException e) {
                log.warn("无效的百分比值: {}", formData.get("dssP"));
            }
        }
        
        // 其他参数
        if (formData.containsKey("dssG")) {
            try {
                entity.setDssG(Double.valueOf(String.valueOf(formData.get("dssG"))));
            } catch (NumberFormatException e) {
                log.warn("无效的参数值: {}", formData.get("dssG"));
            }
        }
    }
    
    /**
     * 处理桩号逻辑转换
     */
    private void processStakeLogic(MtmsDssinfoProject entity, Map<String, Object> formData) throws Exception {
        String tunnelId = entity.getStructId();
        if (tunnelId == null) {
            throw new Exception("隧道ID不能为空");
        }
        
        // 判断是否是横洞
        boolean isHorizontalTunnel = "yes".equalsIgnoreCase(String.valueOf(formData.getOrDefault("HDdecide", "")));
        
        // 获取隧道桩号区间信息
        List<Map<String, Object>> tunnelStakeInfoList = evaluationService.findTunnelStakeInfo(tunnelId);
        if (tunnelStakeInfoList == null || tunnelStakeInfoList.isEmpty()) {
            throw new Exception("未找到隧道桩号信息");
        }
        
        Double stake = entity.getStake();
        Double startStake = entity.getStartStakeNum();
        Double endStake = entity.getEndStakeNum();
        
        // 设置逻辑桩号
        entity.setLogicStake(stake);
        entity.setLogicStartStake(startStake);
        entity.setLogicEndStake(endStake);
        
        if (isHorizontalTunnel) {
            // 横洞处理逻辑
            processHorizontalTunnelStake(entity, tunnelStakeInfoList);
        } else {
            // 主洞处理逻辑
            processMainTunnelStake(entity, tunnelStakeInfoList, formData);
        }
    }
    
    /**
     * 处理横洞桩号
     */
    private void processHorizontalTunnelStake(MtmsDssinfoProject entity, List<Map<String, Object>> tunnelStakeInfoList) throws Exception {
        Double stake = entity.getStake();
        if (stake == null || stake == 0) {
            throw new Exception("无主线桩号信息，请填入主线桩号");
        }
        
        // 找到包含当前桩号的区间
        Map<String, Object> stakeInfo = findStakeRangeInfo(stake, tunnelStakeInfoList);
        if (stakeInfo == null) {
            throw new Exception("桩号超出隧道范围");
        }
        
        // 逻辑桩号转物理桩号
        Double rlstart = Double.valueOf(stakeInfo.get("RLSTARTNUM").toString());
        Double rlend = Double.valueOf(stakeInfo.get("RLENDNUM").toString());
        Double rpstart = Double.valueOf(stakeInfo.get("RPSTARTNUM").toString());
        Double rpend = Double.valueOf(stakeInfo.get("RPENDNUM").toString());
        
        // 计算物理桩号
        Double rpStake = calculatePhysicalStake(stake, rlstart, rlend, rpstart, rpend);
        entity.setRpStake(rpStake);
    }
    
    /**
     * 处理主洞桩号
     */
    private void processMainTunnelStake(MtmsDssinfoProject entity, List<Map<String, Object>> tunnelStakeInfoList, Map<String, Object> formData) throws Exception {
        Double stake = entity.getStake();
        Double startStake = entity.getStartStakeNum();
        Double endStake = entity.getEndStakeNum();
        
        // 处理隧道起终点桩号
        Double tunnelStartStake = null;
        Double tunnelEndStake = null;
        if (formData.containsKey("tunnelStartStake")) {
            tunnelStartStake = Double.valueOf(String.valueOf(formData.get("tunnelStartStake")));
        }
        if (formData.containsKey("tunnelEndStake")) {
            tunnelEndStake = Double.valueOf(String.valueOf(formData.get("tunnelEndStake")));
        }
        
        if (tunnelStartStake != null && tunnelEndStake != null) {
            Double jinkouStake = Math.min(tunnelStartStake, tunnelEndStake);
            Double chukouStake = Math.max(tunnelStartStake, tunnelEndStake);
            
            // 根据实际情况设置桩号
            if (stake == null && startStake != null) {
                // 有起点桩号但无主桩号
                entity.setStake(startStake);
                stake = startStake;
            } else if (stake == null && startStake == null && 
                    "7A8B25F05603019CE05380012EC8019C".equalsIgnoreCase(entity.getStructCompId())) {
                // 进口
                entity.setStake(jinkouStake);
                stake = jinkouStake;
            } else if (stake == null && startStake == null && 
                    "7A8B25F05604019CE05380012EC8019C".equalsIgnoreCase(entity.getStructCompId())) {
                // 出口
                entity.setStake(chukouStake);
                stake = chukouStake;
            }
        }
        
        // 验证桩号有效性
        if (stake == null && startStake == null && endStake == null) {
            throw new Exception("无桩号信息，请填入桩号");
        }
        
        // 确定要使用的桩号值
        Double stakeForTransform = stake != null ? stake : (startStake != null ? startStake : endStake);
        
        // 找到包含当前桩号的区间
        Map<String, Object> stakeInfo = findStakeRangeInfo(stakeForTransform, tunnelStakeInfoList);
        if (stakeInfo == null) {
            throw new Exception("桩号超出隧道范围");
        }
        
        // 逻辑桩号转物理桩号
        Double rlstart = Double.valueOf(stakeInfo.get("RLSTARTNUM").toString());
        Double rlend = Double.valueOf(stakeInfo.get("RLENDNUM").toString());
        Double rpstart = Double.valueOf(stakeInfo.get("RPSTARTNUM").toString());
        Double rpend = Double.valueOf(stakeInfo.get("RPENDNUM").toString());
        
        // 计算所有的物理桩号
        if (stake != null) {
            Double rpStake = calculatePhysicalStake(stake, rlstart, rlend, rpstart, rpend);
            entity.setRpStake(rpStake);
        }
        
        if (startStake != null) {
            Double rpStartStake = calculatePhysicalStake(startStake, rlstart, rlend, rpstart, rpend);
            entity.setRpStartStake(rpStartStake);
        }
        
        if (endStake != null) {
            Double rpEndStake = calculatePhysicalStake(endStake, rlstart, rlend, rpstart, rpend);
            entity.setRpEndStake(rpEndStake);
        }
    }
    
    /**
     * 找到包含指定桩号的区间信息
     */
    private Map<String, Object> findStakeRangeInfo(Double stake, List<Map<String, Object>> tunnelStakeInfoList) {
        for (Map<String, Object> stakeInfo : tunnelStakeInfoList) {
            Double rlstart = Double.valueOf(stakeInfo.get("RLSTARTNUM").toString());
            Double rlend = Double.valueOf(stakeInfo.get("RLENDNUM").toString());
            if (stake >= rlstart && stake <= rlend) {
                return stakeInfo;
            }
        }
        return null;
    }
    
    /**
     * 计算物理桩号
     */
    private Double calculatePhysicalStake(Double logicStake, Double rlstart, Double rlend, Double rpstart, Double rpend) {
        if (rlend - rlstart == 0) {
            return rpstart;
        }
        return rpstart + (logicStake - rlstart) * (rpend - rpstart) / (rlend - rlstart);
    }

    /**
     * <p>功能描述：遍历隧道灾害的部件和构件,用在灾害的添加和修改页面的部件。</p>
     * @param structPartId 部件ID
     * @return 部件和构件列表
     */
    @ApiRegister(value = "tunnelEvaluation:findStructPart", businessType = BusinessType.OTHER)
    @ApiOperation("获取隧道部件和构件列表")
    @RequestMapping("findStructPartByTun")
    @ResponseBody
    public RestResult<List<Map<String, Object>>> findStructPartByTun(@RequestParam(required = false) String structPartId) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("structPartId", structPartId);
        List<Map<String, Object>> result = evaluationService.findStructPartByTun(paramMap);
        return RestResult.success(result);
    }

    /**
     * <p>功能描述：通过隧道灾害的构件查询病害类型,用在灾害的添加和修改页面的病害类型。</p>
     * @param structCompId 构件ID
     * @return 病害类型列表
     */
    @ApiRegister(value = "tunnelEvaluation:findDssType", businessType = BusinessType.OTHER)
    @ApiOperation("获取隧道病害类型列表")
    @RequestMapping("findDssTypeByTun")
    @ResponseBody
    public RestResult<List<Map<String, Object>>> findDssTypeByTun(@RequestParam(required = false) String structCompId) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("structCompId", structCompId);
        List<Map<String, Object>> result = evaluationService.findDssTypeByPartId(paramMap);
        return RestResult.success(result);
    }

    /**
     * <p>功能描述：根据子部件的ID查询相关的构件。</p>   
     * @param structPartId 子部件ID
     * @return 构件列表
     */
    @ApiRegister(value = "tunnelEvaluation:findCompInfo", businessType = BusinessType.OTHER)
    @ApiOperation("根据子部件ID查询相关构件")
    @RequestMapping("findCompInfoByPartId")
    @ResponseBody
    public RestResult<List<Map<String, Object>>> findCompInfoByPartId(@RequestParam(required = false) String structPartId) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("structPartId", structPartId);
        List<Map<String, Object>> result = evaluationService.findCompInfoByPartId(paramMap);
        return RestResult.success(result);
    }

    /**
     * <p>功能描述：获取病害描述信息</p>
     * @param dssTypeId 病害类型ID
     * @param structCompId 构件ID
     * @return 病害描述列表
     */
    @ApiRegister(value = "tunnelEvaluation:findDssDescribe", businessType = BusinessType.OTHER)
    @ApiOperation("获取病害描述信息")
    @RequestMapping("findDssDescribe")
    @ResponseBody
    public RestResult<List<Map<String, Object>>> findDssDescribe(
            @RequestParam(required = false) String dssTypeId,
            @RequestParam(required = false) String structCompId) {
        List<Map<String, Object>> result = evaluationService.findDssDescribe(dssTypeId, structCompId);
        return RestResult.success(result);
    }

    /**
     * <p>功能描述：根据病害类型编码查询定量信息</p>
     * @param dssType 病害类型编码
     * @param structPartId 构件ID
     * @return 病害类型定量信息
     */
    @ApiRegister(value = "tunnelEvaluation:findDssTypeByCode", businessType = BusinessType.OTHER)
    @ApiOperation("根据病害类型编码查询定量信息")
    @RequestMapping("findDssTypeByCode")
    @ResponseBody
    public RestResult<DssType> findDssTypeByCode(
            @RequestParam(required = false) String dssType,
            @RequestParam(required = false) String structPartId) {
        DssType result = evaluationService.findDssTypeByCode(dssType, structPartId);
        return RestResult.success(result);
    }
} 