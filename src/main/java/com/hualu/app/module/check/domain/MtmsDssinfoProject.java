package com.hualu.app.module.check.domain;

import javax.validation.constraints.Size;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

/**
 * 隧道病害信息表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("MTMSDB.MTMS_DSSINFO_PROJECT")
@ApiModel(value="MtmsDssinfoProject对象", description="隧道病害信息表")
public class MtmsDssinfoProject implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "DSS_ID", type = IdType.INPUT)
    @Size(max= 40, message="病害ID长度不能超过40")
    @ApiModelProperty("病害ID")
    private String dssId;

    @TableField("PROJECT_ID")
    @Size(max= 40, message="项目ID长度不能超过40")
    @ApiModelProperty("项目ID")
    private String projectId;

    @TableField("LINE_DIRECT")
    @Size(max= 20, message="线路方向长度不能超过20")
    @ApiModelProperty("线路方向")
    private String lineDirect;

    @TableField("STAKE")
    @ApiModelProperty("桩号")
    private Double stake;

    @TableField("STRUCT_PART_ID")
    @Size(max= 100, message="结构部分ID长度不能超过100")
    @ApiModelProperty("结构部分ID")
    private String structPartId;

    @TableField("STRUCT_COMP_ID")
    @Size(max= 100, message="结构组件ID长度不能超过100")
    @ApiModelProperty("结构组件ID")
    private String structCompId;

    @TableField("DSS_TYPE")
    @Size(max= 40, message="病害类型长度不能超过40")
    @ApiModelProperty("病害类型")
    private String dssType;

    @TableField("DSS_DEGREE")
    @Size(max= 20, message="病害程度长度不能超过20")
    @ApiModelProperty("病害程度")
    private String dssDegree;

    @TableField("MNTN_ADVICE")
    @Size(max= 500, message="维修建议长度不能超过500")
    @ApiModelProperty("维修建议")
    private String mntnAdvice;

    @TableField("LANE")
    @Size(max= 20, message="车道长度不能超过20")
    @ApiModelProperty("车道")
    private String lane;

    @TableField("DSS_POSITION")
    @Size(max= 100, message="病害位置长度不能超过100")
    @ApiModelProperty("病害位置")
    private String dssPosition;

    @TableField("DSS_DESC")
    @Size(max= 1000, message="病害描述长度不能超过1000")
    @ApiModelProperty("病害描述")
    private String dssDesc;

    @TableField("DSS_L")
    @ApiModelProperty("长度")
    private Double dssL;

    @TableField("DSS_L_UNIT")
    @Size(max= 20, message="长度单位长度不能超过20")
    @ApiModelProperty("长度单位")
    private String dssLUnit;

    @TableField("DSS_W")
    @ApiModelProperty("宽度")
    private Double dssW;

    @TableField("DSS_W_UNIT")
    @Size(max= 20, message="宽度单位长度不能超过20")
    @ApiModelProperty("宽度单位")
    private String dssWUnit;

    @TableField("DSS_D")
    @ApiModelProperty("深度")
    private Double dssD;

    @TableField("DSS_D_UNIT")
    @Size(max= 20, message="深度单位长度不能超过20")
    @ApiModelProperty("深度单位")
    private String dssDUnit;

    @TableField("DSS_N")
    @ApiModelProperty("数量")
    private Double dssN;

    @TableField("DSS_N_UNIT")
    @Size(max= 20, message="数量单位长度不能超过20")
    @ApiModelProperty("数量单位")
    private String dssNUnit;

    @TableField("DSS_A")
    @ApiModelProperty("面积")
    private Double dssA;

    @TableField("DSS_A_UNIT")
    @Size(max= 20, message="面积单位长度不能超过20")
    @ApiModelProperty("面积单位")
    private String dssAUnit;

    @TableField("DSS_V")
    @ApiModelProperty("体积")
    private Double dssV;

    @TableField("DSS_V_UNIT")
    @Size(max= 20, message="体积单位长度不能超过20")
    @ApiModelProperty("体积单位")
    private String dssVUnit;

    @TableField("DSS_P")
    @ApiModelProperty("百分比")
    private Double dssP;

    @TableField("DSS_G")
    @ApiModelProperty("其他参数")
    private Double dssG;

    @TableField("DSS_IMP_FLAG")
    @ApiModelProperty("重要标志")
    private Integer dssImpFlag;

    @TableField("DSS_QUALITY")
    @ApiModelProperty("病害质量")
    private Integer dssQuality;

    @TableField("HIS_DSS_ID")
    @Size(max= 40, message="历史病害ID长度不能超过40")
    @ApiModelProperty("历史病害ID")
    private String hisDssId;

    @TableField("DSS_CAUSE")
    @Size(max= 1000, message="病害原因长度不能超过1000")
    @ApiModelProperty("病害原因")
    private String dssCause;

    @TableField("X")
    @ApiModelProperty("X坐标")
    private Double x;

    @TableField("Y")
    @ApiModelProperty("Y坐标")
    private Double y;

    @TableField("ISPHONE")
    @ApiModelProperty("是否移动端")
    private Integer isphone;

    @TableField("RAMP_ID")
    @Size(max= 40, message="匝道ID长度不能超过40")
    @ApiModelProperty("匝道ID")
    private String rampId;

    @TableField("STRUCT_ID")
    @Size(max= 40, message="结构物ID长度不能超过40")
    @ApiModelProperty("结构物ID")
    private String structId;

    @TableField("L_DSS_ID")
    @Size(max= 40, message="关联病害ID长度不能超过40")
    @ApiModelProperty("关联病害ID")
    private String LDssId;

    @TableField("LINING_STRUCTURE")
    @Size(max= 10, message="衬砌结构长度不能超过10")
    @ApiModelProperty("衬砌结构")
    private String liningStructure;

    @TableField("MARKING_LINE_POSITION")
    @Size(max= 10, message="标线位置长度不能超过10")
    @ApiModelProperty("标线位置")
    private String markingLinePosition;

    @TableField("TUNNEL_MOUTH")
    @Size(max= 50, message="隧道口长度不能超过50")
    @ApiModelProperty("隧道口")
    private String tunnelMouth;

    @TableField("STAKE_HIGH")
    @ApiModelProperty("桩号高度")
    private Double stakeHigh;

    @TableField("START_STAKE_NUM")
    @ApiModelProperty("起始桩号")
    private Double startStakeNum;

    @TableField("END_STAKE_NUM")
    @ApiModelProperty("结束桩号")
    private Double endStakeNum;

    @TableField("START_HIGH")
    @ApiModelProperty("起始高度")
    private Double startHigh;

    @TableField("END_HIGH")
    @ApiModelProperty("结束高度")
    private Double endHigh;

    @TableField("RP_INTRVL_ID")
    @Size(max= 100, message="路段区间ID长度不能超过100")
    @ApiModelProperty("路段区间ID")
    private String rpIntrvlId;

    @TableField("FOUND_DATE")
    @ApiModelProperty("发现日期")
    private Date foundDate;

    @TableField("FIND_DSS_USER_NAME")
    @Size(max= 100, message="发现病害用户名长度不能超过100")
    @ApiModelProperty("发现病害用户名")
    private String findDssUserName;

    @TableField("MAIN_ROAD_ID")
    @Size(max= 100, message="主线ID长度不能超过100")
    @ApiModelProperty("主线ID")
    private String mainRoadId;

    @TableField("RP_START_STAKE")
    @ApiModelProperty("物理起始桩号")
    private Double rpStartStake;

    @TableField("RP_END_STAKE")
    @ApiModelProperty("物理结束桩号")
    private Double rpEndStake;

    @TableField("RP_STAKE")
    @ApiModelProperty("物理桩号")
    private Double rpStake;

    @TableField("LOGIC_START_STAKE")
    @ApiModelProperty("逻辑起始桩号")
    private Double logicStartStake;

    @TableField("LOGIC_END_STAKE")
    @ApiModelProperty("逻辑结束桩号")
    private Double logicEndStake;

    @TableField("LOGIC_STAKE")
    @ApiModelProperty("逻辑桩号")
    private Double logicStake;
}