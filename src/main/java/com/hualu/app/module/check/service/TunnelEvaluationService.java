package com.hualu.app.module.check.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.check.domain.MtmsDssinfoProject;
import com.hualu.app.module.check.domain.TunnelEvaluation;
import com.hualu.app.module.check.vo.TunnelDiseaseVO;
import java.util.List;
import java.util.Map;
import com.hualu.app.module.check.domain.DssType;

/**
 * 隧道定检服务接口
 */
public interface TunnelEvaluationService extends IService<TunnelEvaluation> {

    /**
     * 查询隧道土建病害列表
     * 
     * @param tunnelId 隧道ID
     * @param projectId 项目ID
     * @param dssId 病害ID（可选）
     * @return 病害列表
     */
    List<TunnelDiseaseVO> listTunnelCivilDiseases(String tunnelId, String projectId, String dssId);

    /**
     * 分页查询隧道土建病害列表
     * 
     * @param page 分页参数
     * @param tunnelId 隧道ID
     * @param projectId 项目ID
     * @param dssId 病害ID（可选）
     * @return 分页结果
     */
    IPage<TunnelDiseaseVO> pageTunnelCivilDiseases(IPage<TunnelDiseaseVO> page, String tunnelId, String projectId, String dssId);

    /**
     * 保存隧道病害信息
     * 
     * @param mtmsDssinfoProject 病害信息
     * @return 是否保存成功
     * @throws Exception 异常信息
     */
    boolean saveTunnelDisease(MtmsDssinfoProject mtmsDssinfoProject) throws Exception;
    
    /**
     * 查询隧道桩号区间信息
     * 
     * @param tunnelId 隧道ID
     * @return 桩号区间信息列表
     * @throws Exception 异常信息
     */
    List<Map<String, Object>> findTunnelStakeInfo(String tunnelId) throws Exception;

    /**
     * 遍历隧道灾害的部件和构件
     * @param paramMap 参数
     * @return 部件和构件列表
     */
    List<Map<String, Object>> findStructPartByTun(Map<String, String> paramMap);
    
    /**
     * 通过构件ID查询病害类型
     * @param paramMap 参数，包含structCompId
     * @return 病害类型列表
     */
    List<Map<String, Object>> findDssTypeByPartId(Map<String, String> paramMap);

    /**
     * 根据子部件ID查询相关构件
     * @param paramMap 参数，包含structPartId
     * @return 构件列表
     */
    List<Map<String, Object>> findCompInfoByPartId(Map<String, String> paramMap);
    
    /**
     * 获取病害描述信息
     * @param dssTypeId 病害类型ID
     * @param structCompId 构件ID
     * @return 病害描述列表
     */
    List<Map<String, Object>> findDssDescribe(String dssTypeId, String structCompId);

    /**
     * 根据病害类型编码查询定量信息
     * @param code 病害类型编码
     * @param structPartId 构件ID
     * @return 病害类型定量信息
     */
    DssType findDssTypeByCode(String code, String structPartId);

}
