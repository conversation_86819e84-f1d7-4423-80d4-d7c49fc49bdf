<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.check.mapper.MtmsDssinfoProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.check.domain.MtmsDssinfoProject">
        <id column="DSS_ID" property="dssId" />
        <result column="PROJECT_ID" property="projectId" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="STAKE" property="stake" />
        <result column="STRUCT_PART_ID" property="structPartId" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_DEGREE" property="dssDegree" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="LANE" property="lane" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="DSS_L" property="dssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="DSS_W" property="dssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="DSS_D" property="dssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="DSS_N" property="dssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="DSS_A" property="dssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="DSS_V" property="dssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="DSS_P" property="dssP" />
        <result column="DSS_G" property="dssG" />
        <result column="DSS_IMP_FLAG" property="dssImpFlag" />
        <result column="DSS_QUALITY" property="dssQuality" />
        <result column="HIS_DSS_ID" property="hisDssId" />
        <result column="DSS_CAUSE" property="dssCause" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="ISPHONE" property="isphone" />
        <result column="RAMP_ID" property="rampId" />
        <result column="STRUCT_ID" property="structId" />
        <result column="L_DSS_ID" property="LDssId" />
        <result column="LINING_STRUCTURE" property="liningStructure" />
        <result column="MARKING_LINE_POSITION" property="markingLinePosition" />
        <result column="TUNNEL_MOUTH" property="tunnelMouth" />
        <result column="STAKE_HIGH" property="stakeHigh" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="START_HIGH" property="startHigh" />
        <result column="END_HIGH" property="endHigh" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="FOUND_DATE" property="foundDate" />
        <result column="FIND_DSS_USER_NAME" property="findDssUserName" />
        <result column="MAIN_ROAD_ID" property="mainRoadId" />
        <result column="RP_START_STAKE" property="rpStartStake" />
        <result column="RP_END_STAKE" property="rpEndStake" />
        <result column="RP_STAKE" property="rpStake" />
        <result column="LOGIC_START_STAKE" property="logicStartStake" />
        <result column="LOGIC_END_STAKE" property="logicEndStake" />
        <result column="LOGIC_STAKE" property="logicStake" />
    </resultMap>
    
    <!-- 根据ID查询病害信息 -->
    <select id="selectDssInfoById" resultMap="BaseResultMap">
        SELECT * FROM MTMSDB.MTMS_DSSINFO_PROJECT 
        WHERE DSS_ID = #{dssId}
    </select>
    
    <!-- 插入病害信息 -->
    <insert id="insertDssInfo" parameterType="com.hualu.app.module.check.domain.MtmsDssinfoProject">
        INSERT INTO MTMSDB.MTMS_DSSINFO_PROJECT (
            DSS_ID, PROJECT_ID, LINE_DIRECT, STAKE, STRUCT_PART_ID, STRUCT_COMP_ID, 
            DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, LANE, DSS_POSITION, DSS_DESC, 
            DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, 
            DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V, DSS_V_UNIT, 
            DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY, HIS_DSS_ID, DSS_CAUSE, 
            X, Y, ISPHONE, RAMP_ID, STRUCT_ID, L_DSS_ID, 
            LINING_STRUCTURE, MARKING_LINE_POSITION, TUNNEL_MOUTH, STAKE_HIGH, 
            START_STAKE_NUM, END_STAKE_NUM, START_HIGH, END_HIGH, RP_INTRVL_ID, 
            FOUND_DATE, FIND_DSS_USER_NAME, MAIN_ROAD_ID, RP_START_STAKE, 
            RP_END_STAKE, RP_STAKE, LOGIC_START_STAKE, LOGIC_END_STAKE, LOGIC_STAKE
        ) VALUES (
            #{dssId}, #{projectId}, #{lineDirect}, #{stake}, #{structPartId}, #{structCompId}, 
            #{dssType}, #{dssDegree}, #{mntnAdvice}, #{lane}, #{dssPosition}, #{dssDesc}, 
            #{dssL}, #{dssLUnit}, #{dssW}, #{dssWUnit}, #{dssD}, #{dssDUnit}, 
            #{dssN}, #{dssNUnit}, #{dssA}, #{dssAUnit}, #{dssV}, #{dssVUnit}, 
            #{dssP}, #{dssG}, #{dssImpFlag}, #{dssQuality}, #{hisDssId}, #{dssCause}, 
            #{x}, #{y}, #{isphone}, #{rampId}, #{structId}, #{LDssId}, 
            #{liningStructure}, #{markingLinePosition}, #{tunnelMouth}, #{stakeHigh}, 
            #{startStakeNum}, #{endStakeNum}, #{startHigh}, #{endHigh}, #{rpIntrvlId}, 
            #{foundDate}, #{findDssUserName}, #{mainRoadId}, #{rpStartStake}, 
            #{rpEndStake}, #{rpStake}, #{logicStartStake}, #{logicEndStake}, #{logicStake}
        )
    </insert>
    
    <!-- 更新病害信息 -->
    <update id="updateDssInfoById" parameterType="com.hualu.app.module.check.domain.MtmsDssinfoProject">
        UPDATE MTMSDB.MTMS_DSSINFO_PROJECT
        <set>
            <if test="projectId != null">PROJECT_ID = #{projectId},</if>
            <if test="lineDirect != null">LINE_DIRECT = #{lineDirect},</if>
            <if test="stake != null">STAKE = #{stake},</if>
            <if test="structPartId != null">STRUCT_PART_ID = #{structPartId},</if>
            <if test="structCompId != null">STRUCT_COMP_ID = #{structCompId},</if>
            <if test="dssType != null">DSS_TYPE = #{dssType},</if>
            <if test="dssDegree != null">DSS_DEGREE = #{dssDegree},</if>
            <if test="mntnAdvice != null">MNTN_ADVICE = #{mntnAdvice},</if>
            <if test="lane != null">LANE = #{lane},</if>
            <if test="dssPosition != null">DSS_POSITION = #{dssPosition},</if>
            <if test="dssDesc != null">DSS_DESC = #{dssDesc},</if>
            <if test="dssL != null">DSS_L = #{dssL},</if>
            <if test="dssLUnit != null">DSS_L_UNIT = #{dssLUnit},</if>
            <if test="dssW != null">DSS_W = #{dssW},</if>
            <if test="dssWUnit != null">DSS_W_UNIT = #{dssWUnit},</if>
            <if test="dssD != null">DSS_D = #{dssD},</if>
            <if test="dssDUnit != null">DSS_D_UNIT = #{dssDUnit},</if>
            <if test="dssN != null">DSS_N = #{dssN},</if>
            <if test="dssNUnit != null">DSS_N_UNIT = #{dssNUnit},</if>
            <if test="dssA != null">DSS_A = #{dssA},</if>
            <if test="dssAUnit != null">DSS_A_UNIT = #{dssAUnit},</if>
            <if test="dssV != null">DSS_V = #{dssV},</if>
            <if test="dssVUnit != null">DSS_V_UNIT = #{dssVUnit},</if>
            <if test="dssP != null">DSS_P = #{dssP},</if>
            <if test="dssG != null">DSS_G = #{dssG},</if>
            <if test="dssImpFlag != null">DSS_IMP_FLAG = #{dssImpFlag},</if>
            <if test="dssQuality != null">DSS_QUALITY = #{dssQuality},</if>
            <if test="hisDssId != null">HIS_DSS_ID = #{hisDssId},</if>
            <if test="dssCause != null">DSS_CAUSE = #{dssCause},</if>
            <if test="x != null">X = #{x},</if>
            <if test="y != null">Y = #{y},</if>
            <if test="isphone != null">ISPHONE = #{isphone},</if>
            <if test="rampId != null">RAMP_ID = #{rampId},</if>
            <if test="structId != null">STRUCT_ID = #{structId},</if>
            <if test="LDssId != null">L_DSS_ID = #{LDssId},</if>
            <if test="liningStructure != null">LINING_STRUCTURE = #{liningStructure},</if>
            <if test="markingLinePosition != null">MARKING_LINE_POSITION = #{markingLinePosition},</if>
            <if test="tunnelMouth != null">TUNNEL_MOUTH = #{tunnelMouth},</if>
            <if test="stakeHigh != null">STAKE_HIGH = #{stakeHigh},</if>
            <if test="startStakeNum != null">START_STAKE_NUM = #{startStakeNum},</if>
            <if test="endStakeNum != null">END_STAKE_NUM = #{endStakeNum},</if>
            <if test="startHigh != null">START_HIGH = #{startHigh},</if>
            <if test="endHigh != null">END_HIGH = #{endHigh},</if>
            <if test="rpIntrvlId != null">RP_INTRVL_ID = #{rpIntrvlId},</if>
            <if test="foundDate != null">FOUND_DATE = #{foundDate},</if>
            <if test="findDssUserName != null">FIND_DSS_USER_NAME = #{findDssUserName},</if>
            <if test="mainRoadId != null">MAIN_ROAD_ID = #{mainRoadId},</if>
            <if test="rpStartStake != null">RP_START_STAKE = #{rpStartStake},</if>
            <if test="rpEndStake != null">RP_END_STAKE = #{rpEndStake},</if>
            <if test="rpStake != null">RP_STAKE = #{rpStake},</if>
            <if test="logicStartStake != null">LOGIC_START_STAKE = #{logicStartStake},</if>
            <if test="logicEndStake != null">LOGIC_END_STAKE = #{logicEndStake},</if>
            <if test="logicStake != null">LOGIC_STAKE = #{logicStake},</if>
        </set>
        WHERE DSS_ID = #{dssId}
    </update>
    
</mapper> 