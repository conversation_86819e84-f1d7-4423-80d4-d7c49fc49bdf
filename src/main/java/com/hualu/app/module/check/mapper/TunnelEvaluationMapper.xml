<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.check.mapper.TunnelEvaluationMapper">

    <!-- 查询隧道土建病害列表 -->
    <select id="listTunnelCivilDiseases" resultType="com.hualu.app.module.check.vo.TunnelDiseaseVO">
        select ('K'||trunc(to_char(dssinfo.LOGIC_STAKE,'fm99999990.000'),0)||'+'||substr(trim(to_char(dssinfo.LOGIC_STAKE,'9999999.999')),instr(trim(to_char(dssinfo.LOGIC_STAKE,'9999999.999')),'.',1,1)+1)) as "stake",
        to_char(dssinfo.LOGIC_STAKE,'fm99999990.000') as "stake1",
        dssinfo.isphone as "isPhone",
        comp1.struct_comp_name as "structPartName",
        dic1.attribute_value as "lineDirect",
        dssinfo.dss_id as "dssId",
        dssinfo.dss_cause as "dssCause",
        dssinfo.mntn_advice as "mntnAdvice",
        dssinfo.dss_desc as "dssDesc",
        to_char(dssinfo.FOUND_DATE,'yyyy-MM-dd') as "foundDate",
        comattr.comp_name||dd.attribute_value || decode(showcomp.struct_comp_name,'边墙','边墙','拱部','拱部',null) as "laneName",
        dssinfo.start_stake_num as "startStakeNum",
        dssinfo.end_stake_num as "endStakeNum",
        dsstype.DSS_TYPE_NAME as "dssTypeName",
        dssinfo.struct_id as "structId",
        item.item_id as "itemId",
        item.item_name as "itemName",
        decode(dssinfo.dss_l,null,null,'长:' || dssinfo.dss_l || dssinfo.dss_l_unit) ||
        decode(dssinfo.dss_w,null,null,'宽:' || dssinfo.dss_w || dssinfo.dss_w_unit) ||
        decode(dssinfo.dss_d,null,null,'深:' || dssinfo.dss_d || dssinfo.dss_d_unit) ||
        decode(dssinfo.dss_n,null,null,'数量:' || dssinfo.dss_n || dssinfo.dss_n_unit) ||
        decode(dssinfo.dss_a,null,null,'面积:' || dssinfo.dss_a || dssinfo.dss_a_unit) ||
        decode(dssinfo.dss_v,null,null,'体积:' || dssinfo.dss_v || dssinfo.dss_v_unit) ||
        decode(dssinfo.dss_p, null, null, '百分比:' || dssinfo.dss_p) ||
        decode(dssinfo.dss_g, null, null, '' || dssinfo.dss_g) as "dssNum",
        decode(rds.dss_type_show,
        '1','('||to_char(dssinfo.start_stake_num,'fm9999999990.000')||rds.comp_file||dssinfo.stake_high||'m)('||to_char(dssinfo.end_stake_num,'fm9999999990.000')||rds.comp_file||dssinfo.end_high||'m)',
        '2',rds.comp_file||dssinfo.stake_high||'m,'||to_char(dssinfo.start_stake_num,'fm9999999990.000')||'--'||to_char(dssinfo.end_stake_num,'fm9999999990.000'),
        '3',rds.comp_file||dssinfo.stake_high,
        '4',rds.comp_file||dssinfo.start_high||'~'||rds.comp_file||dssinfo.end_high||'m',
        '5',rds.comp_file||dssinfo.start_high||'~'||rds.comp_file||dssinfo.end_high||'m,'||to_char(dssinfo.start_stake_num,'fm9999999990.000')||'~'||to_char(dssinfo.end_stake_num,'fm9999999990.000'),
        decode(dssinfo.start_stake_num,null,to_char(dssinfo.LOGIC_STAKE,'fm9999999990.000'),to_char(dssinfo.start_stake_num,'fm9999999990.000')||'~'||to_char(dssinfo.end_stake_num,'fm9999999990.000'))) as "dssBw",
        dssinfo.dss_degree as "dssDegree",
        dssinfo.tunnel_mouth as "tunnelMouth",
        dssinfo.struct_part_id as "structPartId",
        dssinfo.struct_comp_id as "structCompId",
        dssinfo.dss_type as "dssTypeTree",
        dssinfo.dss_type as "dssType",
        dssinfo.lane as "lane",
        dssinfo.dss_l as "dssL",
        dssinfo.dss_w as "dssW",
        dssinfo.dss_d as "dssD",
        dssinfo.dss_n as "dssN",
        dssinfo.dss_a as "dssA",
        dssinfo.dss_v as "dssV",
        dssinfo.dss_p as "dssP",
        dssinfo.dss_g as "dssG",
        comattr.comp_id as "compId",
        comattr.part_id as "partId",
        dssinfo.stake_high as "stakeHigh",
        dssinfo.start_high as "startHigh",
        dssinfo.end_high as "endHigh"
        from mtmsdb.mtms_dssinfo_project dssinfo
        inner join mtmsdb.mtms_tunnel_basic tb on tb.tunnel_id=dssinfo.struct_id
        inner join mtmsdb.mtms_inspect_item item on item.sum_type='1' and item.dss_type=dssinfo.dss_type and item.parts_id=dssinfo.struct_part_id
        left join gdgs.base_datathird_dic dd on dd.attribute_item=('LANE'||tb.tunnel_line_direct||tb.lane_num) and dssinfo.lane=dd.attribute_code
        left join memsdb.dss_type dsstype on dsstype.dss_type=dssinfo.dss_type and dssinfo.struct_part_id=dsstype.STRUCT_COMP_ID
        left join mtmsdb.mtms_rel_dsstype_show rds on rds.dss_type=dssinfo.dss_type and rds.struct_comp_id = dssinfo.struct_part_id
        left join memsdb.base_struct_comp comp1 on comp1.struct_comp_id=dssinfo.tunnel_mouth
        left join gdgs.base_datathird_dic dic1 on dic1.attribute_item='LINE_DIRECT' and dssinfo.line_direct=dic1.attribute_code
        left join memsdb.base_struct_comp comp on comp.struct_comp_id=dssinfo.struct_part_id
        left join mtmsdb.MTMS_TUNNEL_COMPATTR comattr on comattr.comp_id=dssinfo.struct_comp_id and comattr.part_id=dssinfo.struct_part_id
        left join memsdb.base_struct_comp showcomp on decode(comp.struct_comp_name,'边墙',comp.struct_comp_id,'拱部',comp.struct_comp_id,comp.p_struct_comp_id)=showcomp.struct_comp_id
        where dssinfo.struct_id = #{tunnelId}
        and dssinfo.project_id = #{projectId}
        <if test="dssId != null and dssId != ''">
            and dssinfo.dss_id = #{dssId}
        </if>
        and decode(comattr.comp_id,null,substr(item.item_name,0,2),comattr.comp_name)=substr(item.item_name,0,2)
        order by dssinfo.LOGIC_STAKE
    </select>

    <!-- 分页查询隧道土建病害列表 -->
    <select id="pageTunnelCivilDiseases" resultType="com.hualu.app.module.check.vo.TunnelDiseaseVO">
        select ('K'||trunc(to_char(dssinfo.LOGIC_STAKE,'fm99999990.000'),0)||'+'||substr(trim(to_char(dssinfo.LOGIC_STAKE,'9999999.999')),instr(trim(to_char(dssinfo.LOGIC_STAKE,'9999999.999')),'.',1,1)+1)) as "stake",
        to_char(dssinfo.LOGIC_STAKE,'fm99999990.000') as "stake1",
        dssinfo.isphone as "isPhone",
        comp1.struct_comp_name as "structPartName",
        dic1.attribute_value as "lineDirect",
        dssinfo.dss_id as "dssId",
        dssinfo.dss_cause as "dssCause",
        dssinfo.mntn_advice as "mntnAdvice",
        dssinfo.dss_desc as "dssDesc",
        to_char(dssinfo.FOUND_DATE,'yyyy-MM-dd') as "foundDate",
        comattr.comp_name||dd.attribute_value || decode(showcomp.struct_comp_name,'边墙','边墙','拱部','拱部',null) as "laneName",
        dssinfo.start_stake_num as "startStakeNum",
        dssinfo.end_stake_num as "endStakeNum",
        dsstype.DSS_TYPE_NAME as "dssTypeName",
        dssinfo.struct_id as "structId",
        item.item_id as "itemId",
        item.item_name as "itemName",
        decode(dssinfo.dss_l,null,null,'长:' || dssinfo.dss_l || dssinfo.dss_l_unit) ||
        decode(dssinfo.dss_w,null,null,'宽:' || dssinfo.dss_w || dssinfo.dss_w_unit) ||
        decode(dssinfo.dss_d,null,null,'深:' || dssinfo.dss_d || dssinfo.dss_d_unit) ||
        decode(dssinfo.dss_n,null,null,'数量:' || dssinfo.dss_n || dssinfo.dss_n_unit) ||
        decode(dssinfo.dss_a,null,null,'面积:' || dssinfo.dss_a || dssinfo.dss_a_unit) ||
        decode(dssinfo.dss_v,null,null,'体积:' || dssinfo.dss_v || dssinfo.dss_v_unit) ||
        decode(dssinfo.dss_p, null, null, '百分比:' || dssinfo.dss_p) ||
        decode(dssinfo.dss_g, null, null, '' || dssinfo.dss_g) as "dssNum",
        decode(rds.dss_type_show,
        '1','('||to_char(dssinfo.start_stake_num,'fm9999999990.000')||rds.comp_file||dssinfo.stake_high||'m)('||to_char(dssinfo.end_stake_num,'fm9999999990.000')||rds.comp_file||dssinfo.end_high||'m)',
        '2',rds.comp_file||dssinfo.stake_high||'m,'||to_char(dssinfo.start_stake_num,'fm9999999990.000')||'--'||to_char(dssinfo.end_stake_num,'fm9999999990.000'),
        '3',rds.comp_file||dssinfo.stake_high,
        '4',rds.comp_file||dssinfo.start_high||'~'||rds.comp_file||dssinfo.end_high||'m',
        '5',rds.comp_file||dssinfo.start_high||'~'||rds.comp_file||dssinfo.end_high||'m,'||to_char(dssinfo.start_stake_num,'fm9999999990.000')||'~'||to_char(dssinfo.end_stake_num,'fm9999999990.000'),
        decode(dssinfo.start_stake_num,null,to_char(dssinfo.LOGIC_STAKE,'fm9999999990.000'),to_char(dssinfo.start_stake_num,'fm9999999990.000')||'~'||to_char(dssinfo.end_stake_num,'fm9999999990.000'))) as "dssBw",
        dssinfo.dss_degree as "dssDegree",
        dssinfo.tunnel_mouth as "tunnelMouth",
        dssinfo.struct_part_id as "structPartId",
        dssinfo.struct_comp_id as "structCompId",
        dssinfo.dss_type as "dssTypeTree",
        dssinfo.dss_type as "dssType",
        dssinfo.lane as "lane",
        dssinfo.dss_l as "dssL",
        dssinfo.dss_w as "dssW",
        dssinfo.dss_d as "dssD",
        dssinfo.dss_n as "dssN",
        dssinfo.dss_a as "dssA",
        dssinfo.dss_v as "dssV",
        dssinfo.dss_p as "dssP",
        dssinfo.dss_g as "dssG",
        comattr.comp_id as "compId",
        comattr.part_id as "partId",
        dssinfo.stake_high as "stakeHigh",
        dssinfo.start_high as "startHigh",
        dssinfo.end_high as "endHigh"
        from mtmsdb.mtms_dssinfo_project dssinfo
        inner join mtmsdb.mtms_tunnel_basic tb on tb.tunnel_id=dssinfo.struct_id
        inner join mtmsdb.mtms_inspect_item item on item.sum_type='1' and item.dss_type=dssinfo.dss_type and item.parts_id=dssinfo.struct_part_id
        left join gdgs.base_datathird_dic dd on dd.attribute_item=('LANE'||tb.tunnel_line_direct||tb.lane_num) and dssinfo.lane=dd.attribute_code
        left join memsdb.dss_type dsstype on dsstype.dss_type=dssinfo.dss_type and dssinfo.struct_part_id=dsstype.STRUCT_COMP_ID
        left join mtmsdb.mtms_rel_dsstype_show rds on rds.dss_type=dssinfo.dss_type and rds.struct_comp_id = dssinfo.struct_part_id
        left join memsdb.base_struct_comp comp1 on comp1.struct_comp_id=dssinfo.tunnel_mouth
        left join gdgs.base_datathird_dic dic1 on dic1.attribute_item='LINE_DIRECT' and dssinfo.line_direct=dic1.attribute_code
        left join memsdb.base_struct_comp comp on comp.struct_comp_id=dssinfo.struct_part_id
        left join mtmsdb.MTMS_TUNNEL_COMPATTR comattr on comattr.comp_id=dssinfo.struct_comp_id and comattr.part_id=dssinfo.struct_part_id
        left join memsdb.base_struct_comp showcomp on decode(comp.struct_comp_name,'边墙',comp.struct_comp_id,'拱部',comp.struct_comp_id,comp.p_struct_comp_id)=showcomp.struct_comp_id
        where dssinfo.struct_id = #{tunnelId}
        and dssinfo.project_id = #{projectId}
        <if test="dssId != null and dssId != ''">
            and dssinfo.dss_id = #{dssId}
        </if>
        and decode(comattr.comp_id,null,substr(item.item_name,0,2),comattr.comp_name)=substr(item.item_name,0,2)
        order by dssinfo.LOGIC_STAKE
    </select>

    <!-- 查询隧道桩号区间信息 -->
    <select id="findTunnelStakeInfo" resultType="java.util.Map">
        SELECT
            tb.TUNNEL_ID AS STRUCTID,
            tb.RP_INTRVL_ID,
            tb.LOGIC_START_STAKE AS RLSTARTNUM,
            tb.LOGIC_END_STAKE AS RLENDNUM,
            tb.START_STAKE_NUM AS RPSTARTNUM,
            tb.END_STAKE_NUM AS RPENDNUM
        FROM
            MTMSDB.MTMS_TUNNEL_BASIC tb
        WHERE
            tb.TUNNEL_ID = #{tunnelId}
    </select>
    
    <!-- 查询隧道部件和构件 -->
    <select id="findStructPartByTun" parameterType="java.util.Map" resultType="java.util.Map">
        <choose>
            <when test="structPartId == null or structPartId == ''">
                select 
                    comp.struct_comp_id as "partId",
                    comp.struct_comp_code as "partCode",
                    comp.struct_comp_name as "partName",
                    comp.p_struct_comp_id as "partPid",
                    comp.STRUCT_COMP_ID AS "parts_code",
                    comp.STRUCT_COMP_NAME AS "partstype_name",
                    comp.STRUCT_COMP_ID AS "code_id",
                    comp.STRUCT_COMP_NAME AS "compattr_code",
                    comp.STRUCT_COMP_CODE
                from 
                    MEMSDB.BASE_STRUCT_COMP comp
                where 
                    comp.p_struct_comp_id = 'B216847AC44D4CE5BC238EDC22E4F8E3'
            </when>
            <otherwise>
                select 
                    comp.struct_comp_id as "partId",
                    comp.struct_comp_code as "partCode",
                    comp.struct_comp_name as "partName",
                    comp.p_struct_comp_id as "partPid",
                    comp.STRUCT_COMP_ID AS "parts_code",
                    comp.STRUCT_COMP_NAME AS "partstype_name",
                    comp.STRUCT_COMP_ID AS "code_id",
                    comp.STRUCT_COMP_NAME AS "compattr_code",
                    comp.STRUCT_COMP_CODE
                from 
                    MEMSDB.BASE_STRUCT_COMP comp
                start with 
                    comp.p_struct_comp_id = #{structPartId}
                connect by 
                    NOCYCLE prior comp.struct_comp_id = comp.p_struct_comp_id
            </otherwise>
        </choose>
    </select>
    
    <!-- 通过构件ID查询病害类型 -->
    <select id="findDssTypeByPartId" parameterType="java.util.Map" resultType="java.util.Map">
        select 
            distinct dssgroup.group_id as "dssType",
            dssgroup.group_nm as "dssTypeName",
            null as "dssTypePid" 
        from 
            memsdb.dss_type dsstype
        inner join
            mtmsdb.dss_type_group dssgroup on dsstype.dss_type=dssgroup.dss_type
        where 
            dsstype.STRUCT_COMP_ID=#{structCompId}
        and exists (
            select 1 
            from mtmsdb.comp_dsstype_group cg 
            where cg.struct_comp_id=dsstype.STRUCT_COMP_ID 
            and cg.group_id=dssgroup.group_id
        )
        
        union
        
        select 
            distinct dsstype.dss_type as "dssType",
            dsstype.DSS_TYPE_NAME as "dssTypeName",
            cgp.group_id as "dssTypePid"
        from 
            memsdb.dss_type dsstype
        left join (
            select 
                distinct cg.struct_comp_id,
                cg.group_id,
                dssgroup.dss_type 
            from 
                mtmsdb.comp_dsstype_group cg
            inner join 
                mtmsdb.dss_type_group dssgroup on cg.group_id=dssgroup.group_id
        ) cgp on cgp.struct_comp_id=dsstype.STRUCT_COMP_ID and dsstype.dss_type=cgp.dss_type
        where 
            dsstype.STRUCT_COMP_ID=#{structCompId}
    </select>
    
    <!-- 根据子部件ID查询相关构件 -->
    <select id="findCompInfoByPartId" parameterType="java.util.Map" resultType="java.util.Map">
        select 
            tc.part_id as "partId",
            tc.comp_name as "compName",
            tc.comp_id as "compId"
        from 
            mtmsdb.mtms_tunnel_compattr tc
        where 
            tc.part_id = #{structPartId}
    </select>
    
    <!-- 获取病害描述信息 -->
    <select id="findDssDescribe" resultType="java.util.Map">
        select distinct 
            mdd.id as "id",
            mdd.dss_describe_name as "name",
            mdd.seqno
        from  
            mtmsdb.MTMS_REL_DSSTYPE_DESCRIBE dd
        inner join 
            mtmsdb.MTMS_DSS_DESCRIBE mdd on mdd.id = dd.dss_describe_id
        where 
            dd.DSS_TYPE = #{dssTypeId} 
            and dd.struct_comp_id = #{structCompId}
        order by 
            mdd.seqno asc
    </select>
    
    <!-- 根据病害类型编码查询定量信息 -->
    <select id="findDssTypeByCode" resultType="com.hualu.app.module.check.domain.DssType">
        select 
            dt.*
        from 
            memsdb.dss_type dt
        where 
            dt.dss_type = #{code}
            and dt.STRUCT_COMP_ID = #{structPartId}
    </select>
</mapper> 