package com.hualu.app.module.check.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.check.domain.TunnelEvaluationRange;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【TUNNEL_EVALUATION_RANGE(隧道定检项目检测范围)】的数据库操作Mapper
* @createDate 2025-04-08 11:22:48
* @Entity com.hualu.app.module.check.domain.TunnelEvaluationRange
*/
public interface TunnelEvaluationRangeMapper extends BaseMapper<TunnelEvaluationRange> {
    /**
     * 分页查询定检范围列表，关联隧道基本信息
     */
    IPage<TunnelEvaluationRange> pageRangesWithTunnel(IPage<TunnelEvaluationRange> page, @Param("ew") QueryWrapper<TunnelEvaluationRange> wrapper);
}




