package com.hualu.app.module.check.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 病害类型实体类
 */
@Data
@TableName("MEMSDB.DSS_TYPE")
public class DssType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病害类型ID
     */
    @TableId("DSS_TYPE")
    private String dssType;

    /**
     * 病害类型名称
     */
    @TableField("DSS_TYPE_NAME")
    private String dssTypeName;

    /**
     * 构件ID
     */
    @TableField("STRUCT_COMP_ID")
    private String structCompId;

    /**
     * 是否强制拍照
     */
    @TableField("IS_FORCE_PHOTO")
    private String isForcePhoto;

    /**
     * 长度单位
     */
    @TableField("L_UNIT")
    private String lUnit;

    /**
     * 宽度单位
     */
    @TableField("W_UNIT")
    private String wUnit;

    /**
     * 深度单位
     */
    @TableField("D_UNIT")
    private String dUnit;

    /**
     * 数量单位
     */
    @TableField("N_UNIT")
    private String nUnit;

    /**
     * 面积单位
     */
    @TableField("A_UNIT")
    private String aUnit;

    /**
     * 体积单位
     */
    @TableField("V_UNIT")
    private String vUnit;

    /**
     * 是否已删除
     */
    @TableField("IS_DELETED")
    private String isDeleted;

    /**
     * 定量方式:1长2宽3深4数5面6体7度8%
     */
    @TableField("QUANT_WAY")
    private String quantWay;
} 