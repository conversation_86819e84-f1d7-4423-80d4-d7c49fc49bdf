package com.hualu.app.module.check.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.experimental.Accessors;

/**
* 隧道定检项目检测范围
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("MTMSDB.TUNNEL_EVALUATION_RANGE")
@ApiModel(value="TunnelEvaluationRange对象", description="隧道定检项目检测范围")
public class TunnelEvaluationRange implements Serializable {

    
    @NotBlank(message="[主键ID]不能为空")
    @Size(max= 36,message="编码长度不能超过36")
    @ApiModelProperty("主键ID")
    @TableField("ID")
    private String id;
    
    @NotBlank(message="[项目ID]不能为空")
    @Size(max= 36,message="编码长度不能超过36")
    @ApiModelProperty("项目ID")
    @TableField("TUNNEL_EVALUATION_ID")
    private String tunnelEvaluationId;
    
    @NotBlank(message="[隧道ID]不能为空")
    @Size(max= 36,message="编码长度不能超过36")
    @ApiModelProperty("隧道ID")
    @TableField("TUNNEL_ID")
    private String tunnelId;
    
    @NotNull(message="[关联日期]不能为空")
    @ApiModelProperty("关联日期")
    @TableField("REF_TIME")
    private Date refTime;
    
    @NotNull(message="[是否删除]不能为空")
    @ApiModelProperty("是否删除")
    @TableField("IS_DELETED")
    @TableLogic
    private Integer isDeleted;

    @TableField(exist = false)
    private String lineCode;

    @TableField(exist = false)
    private String tunnelName;

    @TableField(exist = false)
    private String tunnelCode;
}
