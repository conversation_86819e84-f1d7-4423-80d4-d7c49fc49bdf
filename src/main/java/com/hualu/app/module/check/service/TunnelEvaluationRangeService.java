package com.hualu.app.module.check.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.check.domain.TunnelEvaluationRange;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【TUNNEL_EVALUATION_RANGE(隧道定检项目检测范围)】的数据库操作Service
* @createDate 2025-04-08 11:22:48
*/
public interface TunnelEvaluationRangeService extends IService<TunnelEvaluationRange> {
    /**
     * 分页查询定检范围列表，关联隧道基本信息
     *
     * @param page 分页参数
     * @param wrapper 查询条件
     * @return 分页结果
     */
    IPage<TunnelEvaluationRange> pageRangesWithTunnel(IPage<TunnelEvaluationRange> page, QueryWrapper<TunnelEvaluationRange> wrapper);
}
