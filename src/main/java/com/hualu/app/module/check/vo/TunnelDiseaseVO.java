package com.hualu.app.module.check.vo;

import lombok.Data;

/**
 * 隧道病害信息VO
 */
@Data
public class TunnelDiseaseVO {
    private String stake;           // 桩号显示格式(K+)
    private String stake1;          // 桩号原始值
    private String isPhone;         // 是否手机录入
    private String structPartName;  // 结构部件名称
    private String lineDirect;      // 线路方向
    
    private String dssId;           // 病害ID
    private String dssCause;        // 病害成因
    private String mntnAdvice;      // 维护建议
    private String dssDesc;         // 病害描述
    private String foundDate;       // 发现日期
    
    private String laneName;        // 车道名称
    private String startStakeNum;   // 起始桩号
    private String endStakeNum;     // 结束桩号
    private String dssTypeName;     // 病害类型名称
    private String structId;        // 结构物ID
    
    private String itemId;          // 项目ID
    private String itemName;        // 项目名称
    private String dssNum;          // 病害尺寸信息
    private String dssBw;           // 病害位置信息
    private String dssDegree;       // 病害程度
    
    private String tunnelMouth;     // 隧道口
    private String structPartId;    // 结构部件ID
    private String structCompId;    // 结构构件ID
    private String dssTypeTree;     // 病害类型树
    private String dssType;         // 病害类型
    
    private String lane;            // 车道
    private String dssL;            // 长度
    private String dssW;            // 宽度
    private String dssD;            // 深度
    private String dssN;            // 数量
    
    private String dssA;            // 面积
    private String dssV;            // 体积
    private String dssP;            // 百分比
    private String dssG;            // 其他尺寸
    private String compId;          // 构件ID
    
    private String partId;          // 部件ID
    private String stakeHigh;       // 桩号高度
    private String startHigh;       // 起点高度
    private String endHigh;         // 终点高度
} 