<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.check.mapper.TunnelEvaluationRangeMapper">

    <select id="pageRangesWithTunnel" resultType="com.hualu.app.module.check.domain.TunnelEvaluationRange">
        select a.id,
               b.LINE_CODE as lineCode,
               b.TUNNEL_NAME as tunnelName,
               b.TUNNEL_CODE as tunnelCode,
               a.TUNNEL_ID as tunnelId,
               a.REF_TIME as refTime
        from MTMSDB.TUNNEL_EVALUATION_RANGE a
        inner join MTMSDB.MTMS_TUNNEL_BASIC b on a.TUNNEL_ID = b.TUNNEL_ID
        ${ew.customSqlSegment}
    </select>

</mapper> 