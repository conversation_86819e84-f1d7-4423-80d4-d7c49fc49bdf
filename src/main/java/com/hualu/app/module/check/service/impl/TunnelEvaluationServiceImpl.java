package com.hualu.app.module.check.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.check.domain.TunnelEvaluation;
import com.hualu.app.module.check.domain.MtmsDssinfoProject;
import com.hualu.app.module.check.domain.DssType;
import com.hualu.app.module.check.service.TunnelEvaluationService;
import com.hualu.app.module.check.mapper.TunnelEvaluationMapper;
import com.hualu.app.module.check.mapper.MtmsDssinfoProjectMapper;
import com.hualu.app.module.check.vo.TunnelDiseaseVO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【TUNNEL_EVALUATION(新版隧道评定基本信息表)】的数据库操作Service实现
* @createDate 2025-04-08 11:22:48
*/
@Service
public class TunnelEvaluationServiceImpl extends ServiceImpl<TunnelEvaluationMapper, TunnelEvaluation>
    implements TunnelEvaluationService{
    
    @Autowired
    private MtmsDssinfoProjectMapper mtmsDssinfoProjectMapper;

    @Override
    public List<TunnelDiseaseVO> listTunnelCivilDiseases(String tunnelId, String projectId, String dssId) {
        return baseMapper.listTunnelCivilDiseases(tunnelId, projectId, dssId);
    }

    @Override
    public IPage<TunnelDiseaseVO> pageTunnelCivilDiseases(IPage<TunnelDiseaseVO> page, String tunnelId, String projectId, String dssId) {
        return baseMapper.pageTunnelCivilDiseases(page, tunnelId, projectId, dssId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTunnelDisease(MtmsDssinfoProject mtmsDssinfoProject) throws Exception {
        try {
            // 查询是否已存在该病害信息
            MtmsDssinfoProject existEntity = mtmsDssinfoProjectMapper.selectDssInfoById(mtmsDssinfoProject.getDssId());
            
            if (existEntity != null) {
                // 已存在，执行更新操作
                int result = mtmsDssinfoProjectMapper.updateDssInfoById(mtmsDssinfoProject);
                if (result <= 0) {
                    throw new Exception("更新隧道病害信息失败");
                }
            } else {
                // 不存在，执行插入操作
                int result = mtmsDssinfoProjectMapper.insertDssInfo(mtmsDssinfoProject);
                if (result <= 0) {
                    throw new Exception("插入隧道病害信息失败");
                }
            }
            
            return true;
        } catch (Exception e) {
            throw new Exception("保存隧道病害信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> findTunnelStakeInfo(String tunnelId) throws Exception {
        try {
            // 使用MyBatis方式查询隧道桩号信息
            List<Map<String, Object>> resultList = baseMapper.findTunnelStakeInfo(tunnelId);
            
            // 对查询结果进行处理
            if (resultList == null || resultList.isEmpty()) {
                return new ArrayList<>();
            }
            
            return resultList;
        } catch (Exception e) {
            throw new Exception("查询隧道桩号信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> findStructPartByTun(Map<String, String> paramMap) {
        return baseMapper.findStructPartByTun(paramMap);
    }

    @Override
    public List<Map<String, Object>> findDssTypeByPartId(Map<String, String> paramMap) {
        return baseMapper.findDssTypeByPartId(paramMap);
    }

    @Override
    public List<Map<String, Object>> findCompInfoByPartId(Map<String, String> paramMap) {
        return baseMapper.findCompInfoByPartId(paramMap);
    }

    @Override
    public List<Map<String, Object>> findDssDescribe(String dssTypeId, String structCompId) {
        return baseMapper.findDssDescribe(dssTypeId, structCompId);
    }

    @Override
    public DssType findDssTypeByCode(String code, String structPartId) {
        return baseMapper.findDssTypeByCode(code, structPartId);
    }
}




