package com.hualu.app.module.check.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.check.domain.MtmsDssinfoProject;
import org.apache.ibatis.annotations.Param;

/**
 * 隧道病害信息Mapper接口
 */
public interface MtmsDssinfoProjectMapper extends BaseMapper<MtmsDssinfoProject> {
    
    /**
     * 插入病害信息
     * 
     * @param entity 病害信息实体
     * @return 影响行数
     */
    int insertDssInfo(MtmsDssinfoProject entity);
    
    /**
     * 更新病害信息
     * 
     * @param entity 病害信息实体
     * @return 影响行数
     */
    int updateDssInfoById(MtmsDssinfoProject entity);
    
    /**
     * 根据ID查询病害信息
     * 
     * @param dssId 病害ID
     * @return 病害信息
     */
    MtmsDssinfoProject selectDssInfoById(@Param("dssId") String dssId);
} 