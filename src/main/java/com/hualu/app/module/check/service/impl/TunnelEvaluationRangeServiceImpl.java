package com.hualu.app.module.check.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.check.domain.TunnelEvaluationRange;
import com.hualu.app.module.check.service.TunnelEvaluationRangeService;
import com.hualu.app.module.check.mapper.TunnelEvaluationRangeMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【TUNNEL_EVALUATION_RANGE(隧道定检项目检测范围)】的数据库操作Service实现
* @createDate 2025-04-08 11:22:48
*/
@Service
public class TunnelEvaluationRangeServiceImpl extends ServiceImpl<TunnelEvaluationRangeMapper, TunnelEvaluationRange>
    implements TunnelEvaluationRangeService{

    @Override
    public IPage<TunnelEvaluationRange> pageRangesWithTunnel(IPage<TunnelEvaluationRange> page, QueryWrapper<TunnelEvaluationRange> wrapper) {
        return baseMapper.pageRangesWithTunnel(page, wrapper);
    }
}




