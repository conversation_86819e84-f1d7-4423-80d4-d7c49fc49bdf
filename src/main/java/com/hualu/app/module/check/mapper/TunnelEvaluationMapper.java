package com.hualu.app.module.check.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.check.domain.TunnelEvaluation;
import com.hualu.app.module.check.domain.DssType;
import com.hualu.app.module.check.vo.TunnelDiseaseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 隧道定检Mapper
 */
public interface TunnelEvaluationMapper extends BaseMapper<TunnelEvaluation> {
    
    /**
     * 查询隧道土建病害列表
     * 
     * @param tunnelId 隧道ID
     * @param projectId 项目ID
     * @param dssId 病害ID（可选）
     * @return 病害列表
     */
    List<TunnelDiseaseVO> listTunnelCivilDiseases(
            @Param("tunnelId") String tunnelId, 
            @Param("projectId") String projectId, 
            @Param("dssId") String dssId);

    /**
     * 分页查询隧道土建病害列表
     * 
     * @param page 分页参数
     * @param tunnelId 隧道ID
     * @param projectId 项目ID
     * @param dssId 病害ID（可选）
     * @return 分页结果
     */
    IPage<TunnelDiseaseVO> pageTunnelCivilDiseases(
            IPage<TunnelDiseaseVO> page,
            @Param("tunnelId") String tunnelId, 
            @Param("projectId") String projectId, 
            @Param("dssId") String dssId);

    /**
     * 查询隧道桩号区间信息
     * 
     * @param tunnelId 隧道ID
     * @return 桩号区间信息列表
     */
    List<Map<String, Object>> findTunnelStakeInfo(@Param("tunnelId") String tunnelId);
    
    /**
     * 查询隧道部件和构件
     * 
     * @param paramMap 参数，包含structPartId
     * @return 部件和构件列表
     */
    List<Map<String, Object>> findStructPartByTun(Map<String, String> paramMap);
    
    /**
     * 通过构件ID查询病害类型
     * 
     * @param paramMap 参数，包含structCompId
     * @return 病害类型列表
     */
    List<Map<String, Object>> findDssTypeByPartId(Map<String, String> paramMap);
    
    /**
     * 根据子部件ID查询相关构件
     * 
     * @param paramMap 参数，包含structPartId
     * @return 构件列表
     */
    List<Map<String, Object>> findCompInfoByPartId(Map<String, String> paramMap);
    
    /**
     * 获取病害描述信息
     * 
     * @param dssTypeId 病害类型ID
     * @param structCompId 构件ID
     * @return 病害描述列表
     */
    List<Map<String, Object>> findDssDescribe(@Param("dssTypeId") String dssTypeId, @Param("structCompId") String structCompId);
    
    /**
     * 根据病害类型编码查询定量信息
     * 
     * @param code 病害类型编码
     * @param structPartId 构件ID
     * @return 病害类型定量信息
     */
    DssType findDssTypeByCode(@Param("code") String code, @Param("structPartId") String structPartId);
}




