package com.hualu.app.module.check.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.experimental.Accessors;

/**
* 项目附件
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("MTMSDB.TUNNEL_EVALUATION_FILE")
@ApiModel(value="TunnelEvaluationFile对象", description="项目附件")
public class TunnelEvaluationFile implements Serializable {

    
    @NotBlank(message="[主键]不能为空")
    @Size(max= 36,message="编码长度不能超过36")
    @ApiModelProperty("主键")
    @TableField("ID")
    private String id;
    
    @Size(max= 36,message="编码长度不能超过36")
    @ApiModelProperty("检测范围ID")
    @TableField("RANGE_ID")
    private String rangeId;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("文件名")
    @TableField("FILE_NAME")
    private String fileName;

    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("文件ID")
    @TableField("FILE_ID")
    private String fileId;
    
    @NotNull(message="[是否删除]不能为空")
    @ApiModelProperty("是否删除")
    @TableField("IS_DELETED")
    @TableLogic
    private Integer isDeleted;
}
