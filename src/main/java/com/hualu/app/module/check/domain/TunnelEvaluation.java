package com.hualu.app.module.check.domain;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableLogic;

/**
* 新版隧道评定基本信息表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("MTMSDB.TUNNEL_EVALUATION")
@ApiModel(value="TunnelEvaluation对象", description="新版隧道评定基本信息表")
public class TunnelEvaluation implements Serializable {

    
    @NotBlank(message="[主键ID]不能为空")
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("主键ID")
    @TableField("ID")
    private String id;
    
    @NotBlank(message="[项目编号]不能为空")
    @Size(max= 80,message="编码长度不能超过80")
    @ApiModelProperty("项目编号")
    @TableField("PROJECT_CODE")
    private String projectCode;

    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("项目类型")
    @TableField("PROJECT_TYPE")
    private String projectType;
    
    @NotBlank(message="[项目名称]不能为空")
    @Size(max= 400,message="编码长度不能超过400")
    @ApiModelProperty("项目名称")
    @TableField("PROJECT_NAME")
    private String projectName;

    @NotBlank(message="[检查类型]不能为空")
    @Size(max= 40,message="检查类型不能超过40")
    @ApiModelProperty("检查类型")
    @TableField("CLASSIFY")
    private String classify;
    
    @Size(max= 2000,message="编码长度不能超过2000")
    @ApiModelProperty("项目概述")
    @TableField("PROJECT_BRIEF")
    private String projectBrief;
    
    @NotNull(message="[项目开始日期]不能为空")
    @ApiModelProperty("项目开始日期")
    @TableField("PROJECT_START_DATE")
    private Date projectStartDate;
    
    @NotNull(message="[项目结束日期/ 计划完成日期]不能为空")
    @ApiModelProperty("项目结束日期/ 计划完成日期")
    @TableField("PROJECT_END_DATE")
    private Date projectEndDate;
    
    @NotNull(message="[项目所属年度]不能为空")
    @ApiModelProperty("项目所属年度")
    @TableField("PROJECT_YEAR")
    private Long projectYear;
    
    @NotBlank(message="[工程等级分类]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("工程等级分类")
    @TableField("ENGINEERING_GRADE")
    private String engineeringGrade;
    
    @NotBlank(message="[实施方式]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("实施方式")
    @TableField("IMPLEMENTATION_MODE")
    private String implementationMode;
    
    @ApiModelProperty("工程合同价(万元)")
    @TableField("TOTAL_COST")
    private BigDecimal totalCost;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("批复文件号")
    @TableField("APPROVAL_DOC_NO")
    private String approvalDocNo;

    @NotBlank(message="[检查人]不能为空")
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("检查人")
    @TableField("CHECKER")
    private String checker;

    @Size(max= 100,message="编码长度不能超过200")
    @ApiModelProperty("复核单位")
    @TableField("REVIEW_UNIT")
    private String reviewUnit;

    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("复核人")
    @TableField("REVIEWER")
    private String reviewer;

    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("复核时间")
    @TableField("REVIEW_DATE")
    private Date reviewDate;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("检测单位")
    @TableField("INSPECTION_UNIT_NAME")
    private String inspectionUnitName;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("检测单位编码")
    @TableField("INSPECTION_UNIT_CODE")
    private String inspectionUnitCode;
    
    @NotBlank(message="[管养单位]不能为空")
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("管养单位")
    @TableField("MAINTENANCE_UNIT_NAME")
    private String maintenanceUnitName;
    
    @NotBlank(message="[管养单位编码]不能为空")
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("管养单位编码")
    @TableField("MAINTENANCE_UNIT_CODE")
    private String maintenanceUnitCode;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("管养路段")
    @TableField("MAINTENANCE_ROUTE_NAME")
    private String maintenanceRouteName;
    
    @Size(max= 2000,message="编码长度不能超过2000")
    @ApiModelProperty("备注")
    @TableField("REMARKS")
    private String remarks;
    
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("创建人")
    @TableField("CREATE_USER")
    private String createUser;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("更新人")
    @TableField("UPDATE_USER")
    private String updateUser;
    
    @NotNull(message="[是否删除]不能为空")
    @ApiModelProperty("是否删除")
    @TableField("IS_DELETED")
    @TableLogic
    private Integer isDeleted;
}
