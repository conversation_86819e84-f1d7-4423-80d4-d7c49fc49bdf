<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.check.mapper.TunnelEvaluationFileMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.check.domain.TunnelEvaluationFile">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="rangeId" column="RANGE_ID" jdbcType="VARCHAR"/>
            <result property="fileId" column="FILE_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,RANGE_ID,FILE_ID
    </sql>
</mapper>
