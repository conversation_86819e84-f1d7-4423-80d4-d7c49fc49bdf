package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 隧道基本信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MtmsTunnelBasic对象", description="隧道基本信息表")
public class MtmsTunnelBasic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "隧道ID")
    @TableId("TUNNEL_ID")
    private String tunnelId;

    @ApiModelProperty(value = "隧道编号")
    @TableField("TUNNEL_CODE")
    private String tunnelCode;

    @ApiModelProperty(value = "隧道名称")
    @TableField("TUNNEL_NAME")
    private String tunnelName;

    @ApiModelProperty(value = "隧道路段类型")
    @TableField("TUNNEL_LINE_DIRECT")
    private String tunnelLineDirect;

    @ApiModelProperty(value = "土建技术等级")
    @TableField("TUNNEL_RANGE")
    private String tunnelRange;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE")
    private String cntrStake;

    @ApiModelProperty(value = "中心桩号值")
    @TableField("CNTR_STAKE_NUM")
    private Double cntrStakeNum;

    @ApiModelProperty(value = "中心桩号链值")
    @TableField("CNTR_SENSSION_NUM")
    private Double cntrSenssionNum;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "设计起点桩号")
    @TableField("DESIGN_START_STAKE_NUM")
    private Double designStartStakeNum;

    @ApiModelProperty(value = "设计止点桩号")
    @TableField("DESIGN_END_STAKE_NUM")
    private Double designEndStakeNum;

    @ApiModelProperty(value = "隧道长度")
    @TableField("TUNNEL_LENGTH")
    private Double tunnelLength;

    @ApiModelProperty(value = "管养公司")
    @TableField("TUBE_CULTURE_UNIT")
    private String tubeCultureUnit;

    @ApiModelProperty(value = "通车时间")
    @TableField("COMMUT_DATE")
    private LocalDateTime commutDate;

    @ApiModelProperty(value = "隧道净宽")
    @TableField("TUNNEL_WIDTH")
    private Double tunnelWidth;

    @ApiModelProperty(value = "隧道净高")
    @TableField("TUNNEL_HEIGHT")
    private Double tunnelHeight;

    @ApiModelProperty(value = "衬砌材料")
    @TableField("MSNR_MTRL")
    private String msnrMtrl;

    @ApiModelProperty(value = "隧道入口形式（字典项为“端墙式”、“柱式”、“台阶式”、“环框式”、“削竹 式”、“翼墙式”、“喇叭口式”、“遮阳棚式”、“其他”）")
    @TableField("IN_HOLE_STRUCT")
    private String inHoleStruct;

    @ApiModelProperty(value = "隧道出口形式（字典项为“端墙式”、“柱式”、“台阶式”、“环框式”、“削竹 式”、“翼墙式”、“喇叭口式”、“遮阳棚式”、“其他”）")
    @TableField("OUT_HOLE_STRUCT")
    private String outHoleStruct;

    @ApiModelProperty(value = "路面类型")
    @TableField("ROAD_SURFACE")
    private String roadSurface;

    @ApiModelProperty(value = "消防设施")
    @TableField("FIRE_FACILITIES")
    private String fireFacilities;

    @ApiModelProperty(value = "洞内照明形式")
    @TableField("CAVE_LIGHT_FORM")
    private String caveLightForm;

    @ApiModelProperty(value = "洞内通风形式")
    @TableField("CAVE_VNTLT_FORM")
    private String caveVntltForm;

    @ApiModelProperty(value = "是否分离隧道")
    @TableField("IS_SEP_TYPE")
    private String isSepType;

    @ApiModelProperty(value = "是否水下隧道")
    @TableField("IS_UNDERWATER_FLAG")
    private String isUnderwaterFlag;

    @ApiModelProperty(value = "断面形式")
    @TableField("SECTION_FORM")
    private String sectionForm;

    @ApiModelProperty(value = "隧道电子设备")
    @TableField("ELECTRON_DEVICE")
    private String electronDevice;

    @ApiModelProperty(value = "隧道排水类型")
    @TableField("DRAINAGE_TYPE")
    private String drainageType;

    @ApiModelProperty(value = "安全通道数量")
    @TableField("SAFE_ACCESS_NUM")
    private Double safeAccessNum;

    @ApiModelProperty(value = "人行道宽")
    @TableField("SIDEWALK")
    private Double sidewalk;

    @ApiModelProperty(value = "车行道宽")
    @TableField("ROADWAY_WIDE")
    private Double roadwayWide;

    @ApiModelProperty(value = "建设单位名称")
    @TableField("BUILD_ORG_NAME")
    private String buildOrgName;

    @ApiModelProperty(value = "设计单位名称")
    @TableField("DESIGN_ORG_NAME")
    private String designOrgName;

    @ApiModelProperty(value = "施工单位名称")
    @TableField("CONSTRUCTION_ORG_NAME")
    private String constructionOrgName;

    @ApiModelProperty(value = "监理单位名称")
    @TableField("SUPERVISOR_ORG_NAME")
    private String supervisorOrgName;

    @ApiModelProperty(value = "评定日期")
    @TableField("EVALUATE_DATA")
    private String evaluateData;

    @ApiModelProperty(value = "评定单位")
    @TableField("EVALUATION_UNIT")
    private String evaluationUnit;

    @ApiModelProperty(value = "改造完工日期")
    @TableField("COMPLETION_DATE")
    private String completionDate;

    @ApiModelProperty(value = "改造部位")
    @TableField("REMOULD_PART")
    private String remouldPart;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "是否启用（0）未启用（1）已启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "隧道分类")
    @TableField("TUNNEL_CLASSIFIED")
    private String tunnelClassified;

    @ApiModelProperty(value = "隧道中心桩号所在行政区划")
    @TableField("DSTRCT_CODE")
    private String dstrctCode;

    @ApiModelProperty(value = "工程性质")
    @TableField("ENGIN_PROPERTY")
    private String enginProperty;

    @ApiModelProperty(value = "是否重点标注")
    @TableField("IS_KEY_LABEL")
    private String isKeyLabel;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "归属项目公司编码")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "营运路段区间ID")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "主路线ID")
    @TableField("MAINLINE_ID")
    private String mainlineId;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "主线桩号对应的主线路段区间ID")
    @TableField("THREAD_RPID")
    private String threadRpid;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除（0）未删除（1）已删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "对应空间数据ID")
    @TableField("GIS_ID")
    private String gisId;

    @ApiModelProperty(value = "经度")
    @TableField("GIS_X")
    private Double gisX;

    @ApiModelProperty(value = "纬度")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "百度坐标经度")
    @TableField("GIS_BDX")
    private Double gisBdx;

    @ApiModelProperty(value = "百度坐标纬度")
    @TableField("GIS_BDY")
    private Double gisBdy;

    @ApiModelProperty(value = "隧道养护等级")
    @TableField("TUNNEL_MAINTAIN_GRADE")
    private String tunnelMaintainGrade;

    @ApiModelProperty(value = "建成时间")
    @TableField("BUILT_DATE")
    private LocalDateTime builtDate;

    @ApiModelProperty(value = "车道数")
    @TableField("LANE_NUM")
    private Integer laneNum;

    @ApiModelProperty(value = "检测项目ID")
    @TableField("PRJ_ID")
    private String prjId;

    @ApiModelProperty(value = "机电技术状况等级")
    @TableField("MECHATRONICS_GRADE")
    private Integer mechatronicsGrade;

    @ApiModelProperty(value = "隧道整体评定等级")
    @TableField("TUNNEL_TC_GRADE")
    private String tunnelTcGrade;

    @ApiModelProperty(value = "上报编号")
    @TableField("REPORT_NUMBER")
    private String reportNumber;

    @ApiModelProperty(value = "交通设施配置等级")
    @TableField("TRAFFIC_FAC_GRADE")
    private String trafficFacGrade;

    @ApiModelProperty(value = "土建评定单位/评定人")
    @TableField("RANGE_EVALUATOR")
    private String rangeEvaluator;

    @ApiModelProperty(value = "土建评定日期")
    @TableField("RANGE_GRADE_DATE")
    private LocalDateTime rangeGradeDate;

    @ApiModelProperty(value = "机电评定单位/评定人")
    @TableField("MECHATRONICS_EVALUATOR")
    private String mechatronicsEvaluator;

    @ApiModelProperty(value = "机电评定日期")
    @TableField("MECHATRONICS_DATE")
    private LocalDateTime mechatronicsDate;

    @ApiModelProperty(value = "其它工程等级")
    @TableField("OTHER_GRADE")
    private String otherGrade;

    @ApiModelProperty(value = "其它工程评定单位/评定人")
    @TableField("OTHER_EVALUATOR")
    private String otherEvaluator;

    @ApiModelProperty(value = "其他工程评定日期")
    @TableField("OTHER_GRADE_DATE")
    private LocalDateTime otherGradeDate;

    @ApiModelProperty(value = "是否在长大隧道目录中")
    @TableField("IS_TUNNEL")
    private String isTunnel;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "物理中心桩号")
    @TableField("PHYSICS_CNTR_STAKE")
    private Double physicsCntrStake;

    @ApiModelProperty(value = "逻辑中心桩号")
    @TableField("LOGIC_CNTR_STAKE")
    private Double logicCntrStake;

    @ApiModelProperty(value = "物理路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "逻辑路段版本")
    @TableField("ROUTE_VERSION")
    private String routeVersion;

    @ApiModelProperty(value = "主要病害(上报公路局添加)")
    @TableField("DSS_TYPE")
    private String dssType;

    @ApiModelProperty(value = "当前主要病害-描述(上报公路局添加)")
    @TableField("DSS_POSITION")
    private String dssPosition;

    @ApiModelProperty(value = "路线名称(上报公路局添加)")
    @TableField("LINE_NAME")
    private String lineName;

    @ApiModelProperty(value = "改造完工年份")
    @TableField("COMPLETION_YEAR")
    private String completionYear;

    @ApiModelProperty(value = "路线技术等级(上报公路局添加)")
    @TableField("ROAD_LVL")
    private String roadLvl;

    @ApiModelProperty(value = "监管单位名称(上报公路局添加)")
    @TableField("SUPERVISOR_ORG")
    private String supervisorOrg;

    @TableField("JD_BAK")
    private Double jdBak;

    @TableField("WD_BAK")
    private Double wdBak;

    @ApiModelProperty(value = "逻辑起点桩号")
    @TableField("LOGIC_START_STAKE")
    private Double logicStartStake;

    @ApiModelProperty(value = "逻辑止点桩号")
    @TableField("LOGIC_END_STAKE")
    private Double logicEndStake;

    @ApiModelProperty(value = "公路局主键")
    @TableField("YHID")
    private String yhid;

    @ApiModelProperty(value = "竣工桩号")
    @TableField("DESIGN_START_STAKE")
    private String designStartStake;

    @ApiModelProperty(value = "竣工桩号")
    @TableField("DESIGN_END_STAKE")
    private String designEndStake;

    @ApiModelProperty(value = "匝道逻辑中心桩号")
    @TableField("LOGIC_RAMP_CNTR_STAKE")
    private Double logicRampCntrStake;

    @ApiModelProperty(value = "路段名称")
    @TableField("ROUTE_NAME")
    private String routeName;

    @ApiModelProperty(value = "是否跨省(0:是,1:否)")
    @TableField("IS_PROVINCIAL")
    private String isProvincial;

    @ApiModelProperty(value = "最新一次检测项目ID")
    @TableField("TCC_PRJ_ID")
    private String tccPrjId;

    @ApiModelProperty(value = "隧道分类（单洞，座）")
    @TableField("TUNNEL_TYPE")
    private String tunnelType;

    @TableField("IS_ADMIN")
    private String isAdmin;

    @ApiModelProperty(value = "设计时速(60-100)")
    @TableField("DESIGN_SPEED")
    private Double designSpeed;

    @ApiModelProperty(value = "施工方法（字典项为：矿山法、掘进机法、明挖法、盖挖法、 浅埋暗挖法、盾构法、沉管法、其他，以上可多选）")
    @TableField("CONSTRUCTION_METHOD")
    private String constructionMethod;

    @ApiModelProperty(value = "入口GCJ-02经度")
    @TableField("IN_GCJ_X")
    private Double inGcjX;

    @ApiModelProperty(value = "入口GCJ-02纬度")
    @TableField("IN_GCJ_Y")
    private Double inGcjY;

    @ApiModelProperty(value = "出口GCJ-02经度")
    @TableField("OUT_GCJ_X")
    private Double outGcjX;

    @ApiModelProperty(value = "出口GCJ-02纬度")
    @TableField("OUT_GCJ_Y")
    private Double outGcjY;

    @ApiModelProperty(value = "单车道宽度(m),如有车道宽度不一致时填写最小值")
    @TableField("SINGLE_LANE_WIDTH")
    private Double singleLaneWidth;

    @ApiModelProperty(value = "车道总宽度（m）,包含行车道和应急车道")
    @TableField("TOTAL_LANE_WIDTH")
    private Double totalLaneWidth;

    @ApiModelProperty(value = "检修道(有，无)")
    @TableField("OVERHAUL_LANE")
    private String overhaulLane;

    @ApiModelProperty(value = "检修道宽度(m)")
    @TableField("OVERHAUL_LANE_WIDTH")
    private Double overhaulLaneWidth;

    @ApiModelProperty(value = "坡比")
    @TableField("SLOPE_RADIO")
    private Double slopeRadio;

    @ApiModelProperty(value = "紧急停车带数量")
    @TableField("EMERGENCY_PARKING_STRIP")
    private Double emergencyParkingStrip;

    @ApiModelProperty(value = "是否有逃生通道")
    @TableField("ESCAPE_TRUNK")
    private String escapeTrunk;

    @ApiModelProperty(value = "衬砌类型(字典项为：“整体式衬砌”、“复合式衬砌”、“喷锚衬砌”、“明 洞衬砌”、“无衬砌”，以上可多选)")
    @TableField("LININGFORM_TYPE")
    private String liningformType;

    @ApiModelProperty(value = "人行横洞数量")
    @TableField("FOOTWAY")
    private Double footway;

    @ApiModelProperty(value = "车行横洞数量")
    @TableField("ROADWAY")
    private Double roadway;

    @ApiModelProperty(value = "防洪")
    @TableField("FLOOD_PROTECTION")
    private String floodProtection;

    @ApiModelProperty(value = "抗震")
    @TableField("ANTI_SEISMIC")
    private String antiSeismic;

    @ApiModelProperty(value = "监管单位")
    @TableField("SUPERVISION")
    private String supervision;


}
