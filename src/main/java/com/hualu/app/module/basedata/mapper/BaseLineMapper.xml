<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.BaseLineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.BaseLine">
        <id column="LINE_ID" property="lineId" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_ALLNAME" property="lineAllname" />
        <result column="LINE_SNAME" property="lineSname" />
        <result column="LINE_DESC" property="lineDesc" />
        <result column="REMARK" property="remark" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="LINE_COUNTER_CODE" property="lineCounterCode" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="STAKE_TYPE" property="stakeType" />
        <result column="LINE_LENGTH" property="lineLength" />
        <result column="IS_NEW_GGW" property="isNewGgw" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        LINE_ID, LINE_CODE, LINE_ALLNAME, LINE_SNAME, LINE_DESC, REMARK, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, LINE_COUNTER_CODE, IS_ENABLE, IS_DELETED, STAKE_TYPE, LINE_LENGTH, IS_NEW_GGW
    </sql>
    <select id="selectLine" resultType="com.hualu.app.module.basedata.entity.BaseLine">
        select l.* from GDGS.V_BASE_LINE l where (line_code=#{lineId} or line_id=#{lineId}) and rownum &lt; 2
        <if test="isNew != null">
            and IS_NEW_GGW = 1
        </if>
    </select>

    <select id="getRampStakeRange" resultType="com.hualu.app.module.basedata.dto.StakeRangeDto">
        select min(ramplogic.start_stake_num) as startStake,max(ramplogic.end_stake_num) as endStake
        from gdgs.base_route_intrvl_logic bril
           ,gdgs.fw_right_data_permission frdp
           ,gdgs.base_ramp_line rampline
           ,gdgs.base_ramp_intrvl_logic ramplogic
        where ramplogic.line_id = rampline.line_id
          and ramplogic.rp_intrvl_id = bril.rp_intrvl_id
          and frdp.route_code = bril.route_code
          and rampline.line_id = #{lineId}
          and (exists(select 1 from gdgs.fw_right_org o
                      where frdp.oprt_org_code = o.org_code
                      start with id = (select u.org_id from gdgs.fw_right_user u
                                       where u.user_code = #{userCode}
                ) connect by prior id = parent_id))
    </select>

    <select id="getStakeRange" resultType="com.hualu.app.module.basedata.dto.StakeRangeDto">
        select min(m.sk) startStake, max(m.ek) endStake from (
        with ORG AS (select o.id, o.PARENT_ID
        from gdgs.fw_right_org o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with id = (select u.org_id
        from gdgs.fw_right_user u
        where u.user_code = #{userCode})
        connect by prior id = parent_id)
        select least(l.START_STAKE, l.END_STAKE) sk, GREATEST(l.START_STAKE, l.END_STAKE) ek
        from GDGS.BASE_ROUTE_LOGIC l
        where exists(
        select 1
        from GDGS.FW_RIGHT_DATA_PERMISSION p
        inner join ORG o on p.OPRT_ORG_CODE = o.ID
        where p.ROUTE_CODE = l.ROUTE_CODE
        )
        and (l.LINE_ID = #{lineId} or l.lINE_CODE = #{lineId})
        <if test="lineDirect != null and lineDirect != ''">
            and l.LINE_DIRECT = #{lineDirect}
        </if>
        ) m
    </select>
</mapper>
