<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.BaseRouteIntrvlLogicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.BaseRouteIntrvlLogic">
        <id column="RL_INTRVL_ID" property="rlIntrvlId" />
        <result column="LINE_ID" property="lineId" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="RL_INTRVL_CODE" property="rlIntrvlCode" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="STAKE_TYPE" property="stakeType" />
        <result column="START_STAKE" property="startStake" />
        <result column="END_STAKE" property="endStake" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="START_SENSSION_NUM" property="startSenssionNum" />
        <result column="END_SENSSION_NUM" property="endSenssionNum" />
        <result column="START_PLACE" property="startPlace" />
        <result column="END_PLACE" property="endPlace" />
        <result column="START_DSTRCT_CODE" property="startDstrctCode" />
        <result column="END_DSTRCT_CODE" property="endDstrctCode" />
        <result column="WAYS_DSTRCT_CODE" property="waysDstrctCode" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="REMARK" property="remark" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="BUILT_DATE" property="builtDate" />
        <result column="COMMUT_DATE" property="commutDate" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="GIS_ID" property="gisId" />
        <result column="GIS_XY" property="gisXy" />
        <result column="RP_START_STAKE_NUM" property="rpStartStakeNum" />
        <result column="RP_END_STAKE_NUM" property="rpEndStakeNum" />
        <result column="IS_SHOW" property="isShow" />
        <result column="PRJORGCODE" property="prjorgcode" />
        <result column="OPRTORGCODE" property="oprtorgcode" />
        <result column="TR_LOGIC_ID" property="trLogicId" />
        <result column="LINENO" property="lineno" />
        <result column="DESIGN_SPEED" property="designSpeed" />
        <result column="IS_CF" property="isCf" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="ROUTE_VERSION" property="routeVersion" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RL_INTRVL_ID, LINE_ID, RP_INTRVL_ID, RL_INTRVL_CODE, LINE_CODE, STAKE_TYPE, START_STAKE, END_STAKE, START_STAKE_NUM, END_STAKE_NUM, START_SENSSION_NUM, END_SENSSION_NUM, START_PLACE, END_PLACE, START_DSTRCT_CODE, END_DSTRCT_CODE, WAYS_DSTRCT_CODE, LINE_DIRECT, REMARK, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, BUILT_DATE, COMMUT_DATE, IS_ENABLE, IS_DELETED, GIS_ID, GIS_XY, RP_START_STAKE_NUM, RP_END_STAKE_NUM, IS_SHOW, PRJORGCODE, OPRTORGCODE, TR_LOGIC_ID, LINENO, DESIGN_SPEED, IS_CF, ROUTE_CODE, ROUTE_VERSION
    </sql>

</mapper>
