package com.hualu.app.module.basedata.controller;

import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.StakeRangeDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 路线 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Api(tags = "路线信息",description = "路线信息 前端控制器")
@RestController
@RequestMapping("/baseLine")
public class BaseLineController {

  @Resource
  private BaseLineService baseLineService;

  /**
   * 查询路线桩号范围
   * @param lineId
   * @param lineDirect
   * @return
   */
  @GetMapping("/getLineStakeRange")
  public RestResult<StakeRangeDto> getLineStakeRange(String lineId, String lineDirect) {
    return RestResult.success(baseLineService.getStakeRange(lineId, lineDirect), "操作成功");
  }

  /**
   * 权限范围内的路线数据（前端下拉框展示，lineCode、lineAllname）
   * @return
   */
  @GetMapping("selectGrantLines")
  public RestResult<List<BaseLine>> selectGrantLines(){
    return RestResult.success(baseLineService.selectGrantLines(),"操作成功");
  }
}
