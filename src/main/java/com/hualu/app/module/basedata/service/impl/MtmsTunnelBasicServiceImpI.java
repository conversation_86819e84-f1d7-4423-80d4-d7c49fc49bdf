package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.MtmsTunnelBasic;
import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.hualu.app.module.basedata.mapper.MtmsTunnelBasicMapper;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.basedata.service.MtmsTunnelBasicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 隧道基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@DS("mtmsDs")
@Service
public class MtmsTunnelBasicServiceImpI extends ServiceImpl<MtmsTunnelBasicMapper, MtmsTunnelBasic> implements MtmsTunnelBasicService, IBaseStructFace {

    @Override
    public BaseStructDto getId(String structId) {
        MtmsTunnelBasic mtmsTunnelBasic = baseMapper.selectById(structId);
        return initDto(mtmsTunnelBasic);
    }

    @Override
    public String getFacilityCat() {
        return "SD";
    }

    @Override
    public Object selectStructPage() {
        Map<String, Object> paramsMap = getStructParamMap("tunnelName","tunnelId");
        Object appLineCode = paramsMap.get("appLineCode");
        Set<String> routeCodes = null;
        if (ObjectUtil.isNotEmpty(appLineCode)){
            routeCodes = H_DataAuthHelper.selectRouteCodeAuthByLineCode(appLineCode.toString());
            paramsMap.remove("lineId");
            paramsMap.remove("lineCode");
        }else {
            routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        }
        initParam(paramsMap);
        QueryWrapper<MtmsTunnelBasic> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);
        H_BatisQuery.setFieldValue2In(queryWrapper,paramsMap,MtmsTunnelBasic.class);
        queryWrapper.lambda().orderByAsc(MtmsTunnelBasic::getLineCode,MtmsTunnelBasic::getLogicCntrStake);
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();

        if (iPage.getRecords().size() != 0){
            iPage.getRecords().forEach(row->{
                MtmsTunnelBasic item = (MtmsTunnelBasic) row;
                BaseStructDto dto = initDto(item);
                dtos.add(dto);
            });
        }
        iPage.setRecords(dtos);
        return returnPage(iPage);
    }

    @Override
    public List<BaseStructDto> listByRouteCodes(Set<String> routeCodes) {
        QueryWrapper<MtmsTunnelBasic> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);
        queryWrapper.lambda().select(MtmsTunnelBasic::getTunnelId,MtmsTunnelBasic::getTunnelName,MtmsTunnelBasic::getRouteCode)
                .orderByAsc(MtmsTunnelBasic::getLineCode,MtmsTunnelBasic::getLogicCntrStake);

        List<MtmsTunnelBasic> mtmsTunnelBasics = baseMapper.selectList(queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();
        mtmsTunnelBasics.forEach(row->{
            BaseStructDto dto = new BaseStructDto().setStructId(row.getTunnelId()).setStructName(row.getTunnelName()).setRouteCode(row.getRouteCode());
            dtos.add(dto);
        });
        return dtos;
    }

    private void initWrapper(QueryWrapper<MtmsTunnelBasic> queryWrapper,Set<String> routeCodes) {
        queryWrapper.eq("IS_ENABLE",1);
        queryWrapper.eq("IS_DELETED",0);
        queryWrapper.in(CollectionUtil.isNotEmpty(routeCodes),"route_code",routeCodes);
    }

    @Override
    public List<BaseStructDto> listByStructIds(List<String> structIds) {
        List<MtmsTunnelBasic> allList = Lists.newArrayList();
        List<List<String>> partition = ListUtil.partition(structIds, 1000);
        partition.forEach(rows->{
            List<MtmsTunnelBasic> slopeInfoList = lambdaQuery().in(MtmsTunnelBasic::getTunnelId, rows)
                    .orderByAsc(MtmsTunnelBasic::getLineCode,MtmsTunnelBasic::getLogicCntrStake)
                    .list();
            allList.addAll(slopeInfoList);
        });
        List<BaseStructDto> dtos = Lists.newArrayList();
        for (MtmsTunnelBasic info : allList) {
            BaseStructDto baseStructDto = initDto(info);
            dtos.add(baseStructDto);
        }
        return dtos;
    }

    private void initParam(Map<String,Object> paramsMap){
        String lineId = paramsMap.getOrDefault("lineId", "").toString();
        if (StrUtil.isNotBlank(lineId)){
            paramsMap.put("MAINLINE_ID",lineId);
            paramsMap.remove("lineId");
        }

        String lineDirect = paramsMap.getOrDefault("lineDirect","").toString();
        if (StrUtil.isNotBlank(lineDirect)){
            paramsMap.put("TUNNEL_LINE_DIRECT",lineDirect);
            paramsMap.remove("lineDirect");
        }
    }

    private BaseStructDto initDto(MtmsTunnelBasic item){
        if (item == null){
            throw new BaseException("未找到结构物");
        }
        BaseStructDto dto = new BaseStructDto();
        dto.setStructId(item.getTunnelId());
        dto.setStructCode(item.getTunnelCode());
        dto.setRlCntrStake(item.getLogicCntrStake());
        dto.setStructName(item.getTunnelName()+"("+dto.getDisplayStake()+")");
        dto.setStructLength(item.getTunnelLength());
        dto.setRpIntrvlId(item.getRpIntrvlId());
        dto.setRampId(item.getMainRampLineId());
        dto.setRouteCode(item.getRouteCode());
        dto.setRouteVersion(item.getRouteVersion());
        dto.setLogicStartStake(ObjectUtil.isNotNull(item.getLogicStartStake())?new DecimalFormat("#.###").format(item.getLogicCntrStake()):null);
        dto.setLogicEndStake(ObjectUtil.isNotNull(item.getLogicEndStake())?new DecimalFormat("#.###").format(item.getLogicEndStake()):null);
        dto.setX(item.getGisX());
        dto.setY(item.getGisY());
        dto.setOprtOrgCode(item.getOprtOrgCode());
        return dto;
    }
}
