package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

/**
    * 边坡打卡点
    */
@ApiModel(description="边坡打卡点")
@TableName(value = "MEMSDB.SLOPE_ATTENTION_EXTRA")
public class SlopeAttentionExtra {
    @TableId(value = "SLOPE_ID", type = IdType.INPUT)
    @ApiModelProperty(value="")
    private String slopeId;

    @TableField(value = "SLOPE_NAME")
    @ApiModelProperty(value="")
    private String slopeName;

    @TableField(value = "LINE_NAME")
    @ApiModelProperty(value="")
    private String lineName;

    @TableField(value = "LINE_DIRECT")
    @ApiModelProperty(value="")
    private String lineDirect;

    @TableField(value = "CENTER_STAKE")
    @ApiModelProperty(value="")
    private String centerStake;

    @TableField(value = "SLOPE_LENGTH")
    @ApiModelProperty(value="")
    private BigDecimal slopeLength;

    @TableField(value = "SLOPE_TYPE")
    @ApiModelProperty(value="")
    private String slopeType;

    @TableField(value = "MAINTENANCE_GRADE")
    @ApiModelProperty(value="")
    private String maintenanceGrade;

    @TableField(value = "WHETHER_IMPORTANT")
    @ApiModelProperty(value="")
    private String whetherImportant;

    @TableField(value = "SLOPE_LEVEL")
    @ApiModelProperty(value="")
    private String slopeLevel;

    @TableField(value = "REFORM")
    @ApiModelProperty(value="")
    private String reform;

    @TableField(value = "EVAL_GRADE")
    @ApiModelProperty(value="")
    private String evalGrade;

    @TableField(value = "JXD")
    @ApiModelProperty(value="")
    private String jxd;

    @TableField(value = "MANAGE_COMPANY")
    @ApiModelProperty(value="")
    private String manageCompany;

    @TableField(value = "LONGITUDE")
    @ApiModelProperty(value="")
    private BigDecimal longitude;

    @TableField(value = "LATITUDE")
    @ApiModelProperty(value="")
    private BigDecimal latitude;

    public static final String COL_SLOPE_ID = "SLOPE_ID";

    public static final String COL_SLOPE_NAME = "SLOPE_NAME";

    public static final String COL_LINE_NAME = "LINE_NAME";

    public static final String COL_LINE_DIRECT = "LINE_DIRECT";

    public static final String COL_CENTER_STAKE = "CENTER_STAKE";

    public static final String COL_SLOPE_LENGTH = "SLOPE_LENGTH";

    public static final String COL_SLOPE_TYPE = "SLOPE_TYPE";

    public static final String COL_MAINTENANCE_GRADE = "MAINTENANCE_GRADE";

    public static final String COL_WHETHER_IMPORTANT = "WHETHER_IMPORTANT";

    public static final String COL_SLOPE_LEVEL = "SLOPE_LEVEL";

    public static final String COL_REFORM = "REFORM";

    public static final String COL_EVAL_GRADE = "EVAL_GRADE";

    public static final String COL_JXD = "JXD";

    public static final String COL_MANAGE_COMPANY = "MANAGE_COMPANY";

    public static final String COL_LONGITUDE = "LONGITUDE";

    public static final String COL_LATITUDE = "LATITUDE";

    /**
     * @return SLOPE_ID
     */
    public String getSlopeId() {
        return slopeId;
    }

    /**
     * @param slopeId
     */
    public void setSlopeId(String slopeId) {
        this.slopeId = slopeId;
    }

    /**
     * @return SLOPE_NAME
     */
    public String getSlopeName() {
        return slopeName;
    }

    /**
     * @param slopeName
     */
    public void setSlopeName(String slopeName) {
        this.slopeName = slopeName;
    }

    /**
     * @return LINE_NAME
     */
    public String getLineName() {
        return lineName;
    }

    /**
     * @param lineName
     */
    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    /**
     * @return LINE_DIRECT
     */
    public String getLineDirect() {
        return lineDirect;
    }

    /**
     * @param lineDirect
     */
    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    /**
     * @return CENTER_STAKE
     */
    public String getCenterStake() {
        return centerStake;
    }

    /**
     * @param centerStake
     */
    public void setCenterStake(String centerStake) {
        this.centerStake = centerStake;
    }

    /**
     * @return SLOPE_LENGTH
     */
    public BigDecimal getSlopeLength() {
        return slopeLength;
    }

    /**
     * @param slopeLength
     */
    public void setSlopeLength(BigDecimal slopeLength) {
        this.slopeLength = slopeLength;
    }

    /**
     * @return SLOPE_TYPE
     */
    public String getSlopeType() {
        return slopeType;
    }

    /**
     * @param slopeType
     */
    public void setSlopeType(String slopeType) {
        this.slopeType = slopeType;
    }

    /**
     * @return MAINTENANCE_GRADE
     */
    public String getMaintenanceGrade() {
        return maintenanceGrade;
    }

    /**
     * @param maintenanceGrade
     */
    public void setMaintenanceGrade(String maintenanceGrade) {
        this.maintenanceGrade = maintenanceGrade;
    }

    /**
     * @return WHETHER_IMPORTANT
     */
    public String getWhetherImportant() {
        return whetherImportant;
    }

    /**
     * @param whetherImportant
     */
    public void setWhetherImportant(String whetherImportant) {
        this.whetherImportant = whetherImportant;
    }

    /**
     * @return SLOPE_LEVEL
     */
    public String getSlopeLevel() {
        return slopeLevel;
    }

    /**
     * @param slopeLevel
     */
    public void setSlopeLevel(String slopeLevel) {
        this.slopeLevel = slopeLevel;
    }

    /**
     * @return REFORM
     */
    public String getReform() {
        return reform;
    }

    /**
     * @param reform
     */
    public void setReform(String reform) {
        this.reform = reform;
    }

    /**
     * @return EVAL_GRADE
     */
    public String getEvalGrade() {
        return evalGrade;
    }

    /**
     * @param evalGrade
     */
    public void setEvalGrade(String evalGrade) {
        this.evalGrade = evalGrade;
    }

    /**
     * @return JXD
     */
    public String getJxd() {
        return jxd;
    }

    /**
     * @param jxd
     */
    public void setJxd(String jxd) {
        this.jxd = jxd;
    }

    /**
     * @return MANAGE_COMPANY
     */
    public String getManageCompany() {
        return manageCompany;
    }

    /**
     * @param manageCompany
     */
    public void setManageCompany(String manageCompany) {
        this.manageCompany = manageCompany;
    }

    /**
     * @return LONGITUDE
     */
    public BigDecimal getLongitude() {
        return longitude;
    }

    /**
     * @param longitude
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * @return LATITUDE
     */
    public BigDecimal getLatitude() {
        return latitude;
    }

    /**
     * @param latitude
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
}