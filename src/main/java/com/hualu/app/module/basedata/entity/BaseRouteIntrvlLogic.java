package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 营运路段区间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseRouteIntrvlLogic对象", description="营运路段区间")
public class BaseRouteIntrvlLogic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "营运路段逻辑区间ID")
    @TableId("RL_INTRVL_ID")
    private String rlIntrvlId;

    @ApiModelProperty(value = "路线ID")
    @TableField("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "营运路段物理区间ID")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "营运路段区间逻辑编码")
    @TableField("RL_INTRVL_CODE")
    private String rlIntrvlCode;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "桩号体系编码（C类）")
    @TableField("STAKE_TYPE")
    private String stakeType;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE")
    private String startStake;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE")
    private String endStake;

    @ApiModelProperty(value = "起点桩号值")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "止点桩号值")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "起点桩号链值")
    @TableField("START_SENSSION_NUM")
    private Double startSenssionNum;

    @ApiModelProperty(value = "止点桩号链值")
    @TableField("END_SENSSION_NUM")
    private Double endSenssionNum;

    @ApiModelProperty(value = "起点地名")
    @TableField("START_PLACE")
    private String startPlace;

    @ApiModelProperty(value = "止点地名")
    @TableField("END_PLACE")
    private String endPlace;

    @ApiModelProperty(value = "起点行政区域")
    @TableField("START_DSTRCT_CODE")
    private String startDstrctCode;

    @ApiModelProperty(value = "止点行政区域")
    @TableField("END_DSTRCT_CODE")
    private String endDstrctCode;

    @ApiModelProperty(value = "途径市级行政区域")
    @TableField("WAYS_DSTRCT_CODE")
    private String waysDstrctCode;

    @ApiModelProperty(value = "线路方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "建成时间")
    @TableField("BUILT_DATE")
    private Integer builtDate;

    @ApiModelProperty(value = "通车时间")
    @TableField("COMMUT_DATE")
    private Integer commutDate;

    @ApiModelProperty(value = "是否启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "gisid（gps系统的id）")
    @TableField("GIS_ID")
    private String gisId;

    @ApiModelProperty(value = "GIS_XY")
    @TableField("GIS_XY")
    private String gisXy;

    @ApiModelProperty(value = "物理区间桩号")
    @TableField("RP_START_STAKE_NUM")
    private Double rpStartStakeNum;

    @ApiModelProperty(value = "逻辑区间桩号")
    @TableField("RP_END_STAKE_NUM")
    private Double rpEndStakeNum;

    @ApiModelProperty(value = "重复国高网线路是否显示")
    @TableField("IS_SHOW")
    private Integer isShow;

    @ApiModelProperty(value = "项目公司编码")
    @TableField("PRJORGCODE")
    private String prjorgcode;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRTORGCODE")
    private String oprtorgcode;

    @ApiModelProperty(value = "和TRANSFORM表关联ID")
    @TableField("TR_LOGIC_ID")
    private String trLogicId;

    @ApiModelProperty(value = "车道数")
    @TableField("LINENO")
    private Long lineno;

    @ApiModelProperty(value = "设计时速")
    @TableField("DESIGN_SPEED")
    private String designSpeed;

    @ApiModelProperty(value = "是否重复")
    @TableField("IS_CF")
    private String isCf;

    @ApiModelProperty(value = "物理路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "路段版本")
    @TableField("ROUTE_VERSION")
    private String routeVersion;


}
