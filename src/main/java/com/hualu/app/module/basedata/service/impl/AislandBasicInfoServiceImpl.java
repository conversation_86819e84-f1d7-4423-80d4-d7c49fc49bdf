package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.AislandBasicInfo;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.basedata.mapper.AislandBasicInfoMapper;
import com.hualu.app.module.basedata.service.AislandBasicInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 人工岛基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@DS("hsmsDs")
@Service
public class AislandBasicInfoServiceImpl extends ServiceImpl<AislandBasicInfoMapper, AislandBasicInfo> implements AislandBasicInfoService, IBaseStructFace {

    @Lazy
    @Autowired
    BaseDatathirdDicService dicService;

    @Override
    public BaseStructDto getId(String structId) {
        AislandBasicInfo aislandBasicInfo = baseMapper.selectById(structId);
        return initDto(aislandBasicInfo);
    }

    @Override
    public String getFacilityCat() {
        return H_BasedataHepler.RGD;
    }

    private BaseStructDto initDto(AislandBasicInfo item){
        BaseStructDto dto = new BaseStructDto();

        if (item == null){
            throw new BaseException("未找到结构物");
        }
        dto.setStructId(item.getIslandId());
        dto.setRlCntrStake(item.getCenterStakeNum());
        dto.setStructName(item.getIslandName()+"("+item.getCenterStakeCn()+")");
        dto.setCntrStake(item.getCenterStakeNum());
        dto.setLogicStartStake(ObjectUtil.isNotNull(item.getStartStakeNum())?new DecimalFormat("#.###").format(item.getStartStakeNum()):null);
        dto.setLogicEndStake(ObjectUtil.isNotNull(item.getEndStakeNum())?new DecimalFormat("#.###").format(item.getEndStakeNum()):null);
        dto.setRouteCode(item.getRouteCode());
        dto.setX(item.getGisX());
        dto.setY(item.getGisY());
        dto.setOprtOrgCode(item.getOptOrgId());
        return dto;
    }

    @Override
    public Object selectStructPage() {
        Map<String, Object> paramsMap = getStructParamMap("islandName","islandId");
        Object appLineCode = paramsMap.get("appLineCode");
        Set<String> routeCodes = null;
        if (ObjectUtil.isNotEmpty(appLineCode)){
            routeCodes = H_DataAuthHelper.selectRouteCodeAuthByLineCode(appLineCode.toString());
            paramsMap.remove("lineId");
            paramsMap.remove("lineCode");
        }else {
            routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        }
        QueryWrapper<AislandBasicInfo> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);

        String lineId = paramsMap.getOrDefault("lineId","").toString();
        if (StrUtil.isNotBlank(lineId)){
            paramsMap.remove("lineId");
            queryWrapper.lambda().eq(AislandBasicInfo::getLineCode,lineId);
        }

        // 根据路线编码、及桩号排序
        queryWrapper.lambda().orderByAsc(AislandBasicInfo::getLineCode,AislandBasicInfo::getCenterStakeNum);
        H_BatisQuery.setFieldValue2In(queryWrapper,paramsMap,AislandBasicInfo.class);
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();

        if (iPage.getRecords().size() != 0){
            iPage.getRecords().forEach(row->{
                AislandBasicInfo item = (AislandBasicInfo) row;
                BaseStructDto dto = initDto(item);
                dtos.add(dto);
            });
        }
        iPage.setRecords(dtos);
        return returnPage(iPage);
    }

    private void initWrapper(QueryWrapper<AislandBasicInfo> queryWrapper,Set<String> routeCodes) {
        queryWrapper.in(CollectionUtil.isNotEmpty(routeCodes),"route_code",routeCodes);
    }
}
