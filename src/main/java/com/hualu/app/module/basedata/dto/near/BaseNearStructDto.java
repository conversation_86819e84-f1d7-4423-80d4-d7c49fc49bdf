package com.hualu.app.module.basedata.dto.near;

import com.hualu.app.utils.H_StakeHelper;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 附近结构物
 */
@Accessors(chain = true)
@Data
public class BaseNearStructDto implements Serializable {

    //管养单位
    private String oprtOrgCode;

    //路线ID
    private String lineId;

    //结构物ID
    private String structId;

    //结构物编码
    private String structCode;

    //结构物名称
    private String structName;

    // 结构类型（边坡：路堤、路堑；涵洞类型）
    private String structType;

    //物理区间
    private String rpIntrvlId;

    //桥幅数
    private String frameNum;

    //中心桩号
    private Double cntrStake;

    //逻辑桩号
    private Double rlCntrStake;

    //中心桩号显示(K100+123)
    private String displayStake;

    //中心桩号值(100.123)
    private Double cntrSenssion;

    //长度
    private Double structLength;

    //匝道区间ID
    private String rampId;
    //路段编码
    private String routeCode;
    //路段版本号
    private String routeVersion;

    //起点桩号
    private Double logicStartStake;

    //终点桩号
    private Double logicEndStake;

    //边坡级数
    private Long slopeLevel;
    private String slopeType;//边坡类型（路堤、路堑、隧道仰坡、桥台锥坡）
    private String slopeTypeName;//边坡类型名称
    private String slopePosition;//边坡位置（左、右）
    private String slopePositionName;//边坡位置名称

    //结构物桩号
    private String structStake;
    //结构物级数（边坡层级,桥梁跨径,涵洞跨径）
    private String structLevel;

    // 路线方向
    private String lineDirect;

    /**
     * 是否附近查询 1：是，0：否
     */
    @NotBlank(message = "是否附近搜索不能为空")
    private Integer isNear = 0;
    /**
     * 1：已检查，0：未检查 ,2：全部
     */
    private Integer checked = 0;

    /**
     * 是否包含原始轨迹 1:包含，0：未包含
     */
    private Integer hasSourceTrack = 0;

    /**
     * 设施类型
     */
    @NotBlank(message = "设施类型不能为空")
    private String facilityCat;

    /**
     * 单位类型（1：定检单位，2：养护单位），结构物专项检查使用
     */
    private String mntType;

    //病害数量
    private Integer dssNum=0;

    //是否已复核  1：已复核，0：未复核
    private Integer reviewFlag = 0;

    //结构物经度(打卡需要)
    private Double x;

    //结构物纬度(打卡需要)
    private Double y;

    public void setRlCntrStake(Double rlCntrStake) {
        this.rlCntrStake = rlCntrStake;
        this.displayStake = H_StakeHelper.convertCnStake(rlCntrStake==null? "" : rlCntrStake.toString());
    }
}
