package com.hualu.app.module.basedata.dto;

import com.hualu.app.utils.H_StakeHelper;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class BaseStructDto implements Serializable {

    private static final long serialVersionUID = 7477229161006778538L;

    //管养单位
    private String oprtOrgCode;

    //路线ID
    private String lineId;

    //结构物ID
    private String structId;

    //结构物编码
    private String structCode;

    //结构物名称
    private String structName;

    //物理区间
    private String rpIntrvlId;

    //桥幅数
    private String frameNum;

    //中心桩号
    private Double cntrStake;

    //逻辑桩号
    private Double rlCntrStake;

    //中心桩号显示(K100+123)
    private String displayStake;

    //中心桩号值(100.123)
    private Double cntrSenssion;

    //长度
    private Double structLength;

    //匝道区间ID
    private String rampId;
    //路段编码
    private String routeCode;
    //路段版本号
    private String routeVersion;

    //起点桩号，保留三位小数
    private String logicStartStake;

    //终点桩号
    private String logicEndStake;

    //边坡级数
    private Long slopeLevel;
    private String slopeType;//边坡类型（路堤、路堑、隧道仰坡、桥台锥坡）
    private String slopeTypeName;//边坡类型名称
    private String slopePosition;//边坡位置（左、右）
    private String slopePositionName;//边坡位置名称

    //结构物经度(打卡需要)
    private Double x;

    //结构物纬度(打卡需要)
    private Double y;

    public void setRlCntrStake(Double rlCntrStake) {
        this.rlCntrStake = rlCntrStake;
        this.displayStake = H_StakeHelper.convertCnStake(rlCntrStake==null? "" : rlCntrStake.toString());
    }
}
