package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.mapper.BaseRouteLogicMapper;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.utils.H_DataAuthHelper;

import java.util.*;

import org.springframework.stereotype.Service;

import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * <p>
 * 逻辑路段表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@DS("gdgsDs")
@Service
public class BaseRouteLogicServiceImpl extends ServiceImpl<BaseRouteLogicMapper, BaseRouteLogic> implements BaseRouteLogicService {

    @Override
    public BaseRouteLogic getRouteByLineCode(String lineCode) {
        LambdaQueryWrapper<BaseRouteLogic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BaseRouteLogic::getRouteCode, H_DataAuthHelper.selectRouteCodeAuth());
        queryWrapper.eq(BaseRouteLogic::getLineCode, lineCode).eq(BaseRouteLogic::getLineDirect,"1")
                .eq(BaseRouteLogic::getIsEnable,1).orderByAsc(BaseRouteLogic::getRouteCode);
        return getOne(queryWrapper,false);
    }

    @Override public List<BaseRouteLogic> getRouteListByLineCode(String lineCode) {
        LambdaQueryWrapper<BaseRouteLogic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BaseRouteLogic::getRouteCode, H_DataAuthHelper.selectRouteCodeAuth());
        queryWrapper.eq(BaseRouteLogic::getLineCode, lineCode)
            .eq(BaseRouteLogic::getIsEnable,1);
        return list(queryWrapper);
    }

    @Override
    public BaseRouteLogic getRouteByRouteCode(String routeCode) {
        if (StrUtil.isBlank(routeCode)){
            return null;
        }
        LambdaQueryWrapper<BaseRouteLogic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BaseRouteLogic::getRouteCode, H_DataAuthHelper.selectRouteCodeAuth());
        queryWrapper.eq(BaseRouteLogic::getRouteCode, routeCode)
                .eq(BaseRouteLogic::getIsEnable,1).orderByAsc(BaseRouteLogic::getRouteCode);
        return getOne(queryWrapper,false);
    }

    @Override
    public Map<String, BaseRouteLogic> resolveRouteNamesByCodes(Set<String> routeCodeSet) {
        return lambdaQuery()
                .select(BaseRouteLogic::getRouteCode, BaseRouteLogic::getRouteName,BaseRouteLogic::getLineCode)  // 明确查询字段
                .in(BaseRouteLogic::getRouteCode, routeCodeSet)
                .eq(BaseRouteLogic::getIsEnable, 1)
                .list()  // MyBatis-Plus 查询列表
                .stream()
                .collect(toMap(
                        BaseRouteLogic::getRouteCode,
                        Function.identity(),
                        (existing, replacement) -> existing  // 处理重复键冲突
                ));
    }

    @Override
    public BaseRouteLogic getRouteByLineCodeOrRouteCode(String lineCode, String routeCode) {
        LambdaQueryWrapper<BaseRouteLogic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BaseRouteLogic::getRouteCode, H_DataAuthHelper.selectRouteCodeAuth())
                .eq(BaseRouteLogic::getLineCode,lineCode)
                .eq(StrUtil.isNotBlank(routeCode), BaseRouteLogic::getRouteCode, routeCode)
                .eq(BaseRouteLogic::getIsEnable, 1)
                .orderByAsc(BaseRouteLogic::getRouteCode);
        return getOne(queryWrapper,false);
    }

    @Override
    public List<BaseRouteLogic> listRoad(String lineCode) {
        List<BaseRouteLogic> logics = lambdaQuery().in(BaseRouteLogic::getRouteCode, H_DataAuthHelper.selectRouteCodeAuth())
                .eq(BaseRouteLogic::getLineCode, lineCode)
                .eq(BaseRouteLogic::getIsEnable, 1)
                .eq(BaseRouteLogic::getLineDirect, 1)
                .isNotNull(BaseRouteLogic::getRoadName)
                .orderByAsc(BaseRouteLogic::getRouteCode)
                .select(BaseRouteLogic::getRoadName,BaseRouteLogic::getRouteName, BaseRouteLogic::getRouteCode).list();

        Map<String, String> roadMap = logics.stream().collect(toMap(BaseRouteLogic::getRoadName, BaseRouteLogic::getRouteCode, (existing, replacement) -> existing));

        List<BaseRouteLogic> result = new ArrayList<>();
        roadMap.forEach((k,v)->{
            BaseRouteLogic logic = new BaseRouteLogic();
            logic.setRouteCode(v);
            logic.setRoadName(k);
            result.add(logic);
        });
        return result;
    }
}
