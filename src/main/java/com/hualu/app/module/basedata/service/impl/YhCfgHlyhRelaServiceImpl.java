package com.hualu.app.module.basedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.basedata.entity.YhCfgHlyhRela;
import com.hualu.app.module.basedata.mapper.YhCfgHlyhRelaMapper;
import com.hualu.app.module.basedata.service.YhCfgHlyhRelaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 养护路段与收费路段关联关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@DS("gdgsDs")
@Service
public class YhCfgHlyhRelaServiceImpl extends ServiceImpl<YhCfgHlyhRelaMapper, YhCfgHlyhRela> implements YhCfgHlyhRelaService {

}
