package com.hualu.app.module.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 边坡基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface HsmsSlopeInfoMapper extends BaseMapper<HsmsSlopeInfo> {

  List<SlopeBatchAttentionDto> listByStakeRange(
      @Param("lineCode") String lineCode,
      @Param("startStake") double startStake,
      @Param("endStake") double endStake
  );

}
