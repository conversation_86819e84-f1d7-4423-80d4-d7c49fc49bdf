package com.hualu.app.module.basedata.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.api.client.util.Lists;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseDssSzDto;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.dto.RoadPropertyDto;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.FwRightDataPermissionService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.basedata.service.TBrdgBrdgrecogService;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import com.hualu.app.module.facility.impl.FacBpImpl;
import com.hualu.app.module.facility.impl.FacQlImpl;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.comm.entity.BaseStructComp;
import com.hualu.app.module.mems.comm.mapper.BaseStructCompMapper;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.utils.*;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.service.PageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Validated
@Api(tags = "结构物信息接口")
@RestController
@RequestMapping("basedata/struct")
public class BaseStructController extends M_MyBatisController<BaseStructComp, BaseStructCompMapper> {

    @Autowired
    BaseLineService lineService;

    @Autowired
    DssTypeNewService dssTypeNewService;

    @Autowired
    PageService pageService;

    @Autowired
    TBrdgBrdgrecogService brdgrecogService;

    @Autowired
    FwRightDataPermissionService permissionService;

    @Autowired
    FacBpImpl bpImp;


    /**
     * 获取边坡级数
     * @param structId
     * @return
     */
    @GetMapping("getSlopeLevel")
    public RestResult<List<BaseDatathirdDic>> getSlopeLevel(String structId){
        RestResult<BaseStructDto> bp = getByStructId(structId, "BP");
        List<BaseDatathirdDic> dicList = Lists.newArrayList();
        if (bp != null && bp.getData() != null){
            BaseStructDto data = bp.getData();
            Long slopeLevel = data.getSlopeLevel();
            for (int i = 1; i <=slopeLevel; i++) {
                BaseDatathirdDic dir = new BaseDatathirdDic().setAttributeCode(i + "")
                        .setAttributeValue(NumberChineseFormatter.format(i, false) + "级");
                dicList.add(dir);
            }
            return RestResult.success(dicList);
        }
        return RestResult.success(Lists.newArrayList());
    }

    /**
     * 获取结构物信息
     *
     * @param structId
     * @param facilityCat
     * @return
     */
    @GetMapping("getByStructId")
    public RestResult<BaseStructDto> getByStructId(String structId, @RequestParam(value = "facilityCat", defaultValue = "BP") String facilityCat) {
        Map<String, IBaseStructFace> beans = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class);
        BaseStructDto dto = null;
        for (IBaseStructFace key : beans.values()) {
            if (facilityCat.equals(key.getFacilityCat())) {
                dto = key.getId(structId);
                break;
            }
        }
        return RestResult.success(dto);
    }


    /**
     * 附近巡查结构物
     * @param facilityCat 设施类型 BP
     * @param isNear  是否附近查询 1：是，0：否
     * @param checked  1：已检查，0：未检查 ,2：全部
     * @param x 经度
     * @param y 纬度
     * @param structName 结构物名称，用于模糊搜索
     * @return
     */
    @PostMapping("getNearByStruct")
    public RestResult<Map<String,Object>> getNearByStruct(@RequestParam(name = "facilityCat",required = true) String facilityCat
            ,@RequestParam(required = true,defaultValue = "0") Integer isNear,@RequestParam(required = true,defaultValue = "2") Integer checked
            ,Double x,Double y,String structName){
        Map reqParam = getReqParam();
        BaseNearStructDto dto = BeanUtil.toBean(reqParam, BaseNearStructDto.class);
        if (dto.getIsNear().equals(1) && (dto.getX() == null || dto.getY() == null)){
            throw new BaseException("经纬度信息不能为空");
        }
        RestResult<Map<String,Object>> restResult = bpImp.getNearByDmFinsp(dto);
        return restResult;
    }

    /**
     * 路产数量
     * @return
     */
    @GetMapping("getRoadAndStructNum")
    public RestResult<List<RoadPropertyDto>> getRoadAndStructNum(){
        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        // 获取各管养公司的结构物个数
        Map<String, IFinspStatBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFinspStatBase.class);

        List<RoadPropertyDto> roadDtos = permissionService.getRoadLength(Lists.newArrayList(routeCodes));

        List<CompletableFuture<List<FacilityNumDto>>> futureList = new ArrayList<>();
        beansOfType.values().forEach(bean->{
            CompletableFuture<List<FacilityNumDto>> future = CompletableFuture.supplyAsync(() -> {
                return bean.countNum(routeCodes);
            });
            futureList.add(future);
        });
        List<FacilityNumDto> numDtos = H_FutureHelper.sequenceList(futureList).join();
        Map<String, List<FacilityNumDto>> facilityCatMap = numDtos.stream().collect(Collectors.groupingBy(FacilityNumDto::getFacilityCat));

        roadDtos.forEach(item->{
            List<String> codes = StrUtil.split(item.getRouteCodes(), ",");
            facilityCatMap.forEach((k,v)->{
                int sum = v.stream().filter(e -> codes.contains(e.getRouteCode())).map(FacilityNumDto::getNum).mapToInt(Integer::intValue).sum();
                ReflectUtil.setFieldValue(item,k.toLowerCase()+"Num",sum);
            });
        });
        return RestResult.success(roadDtos);
    }

    /**
     * 视野范围内结构查询
     * @param paramDto 视野范围内参数
     * @return
     */
    @PostMapping("geometry")
    public RestResult<List<FinspStatFacDto>> getGeometryStructs(@RequestBody @Validated GeometryParamDto paramDto){

        //如果层级小于16，不显示结构物数据
        boolean flag = paramDto.getLevel() < 18;
        if (flag){
            return RestResult.success(Lists.newArrayList());
        }

        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        // 09坐标转换为w84坐标
        double[] minXy = H_CoordinateTransformUtil.bd09towgs84(paramDto.getMinX(), paramDto.getMinY());
        double[] maxXy = H_CoordinateTransformUtil.bd09towgs84(paramDto.getMaxX(), paramDto.getMaxY());
        paramDto.setMinX(minXy[0]).setMinY(minXy[1]).setMaxX(maxXy[0]).setMaxY(maxXy[1]);

        Map<String, IFinspStatBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFinspStatBase.class);
        List<CompletableFuture<List<FinspStatFacDto>>> futureList = Lists.newArrayList();
        beansOfType.values().forEach(bean->{
            CompletableFuture<List<FinspStatFacDto>> future = CompletableFuture.supplyAsync(() -> {
                return bean.getGeometryStat(Lists.newArrayList(routeCodes), paramDto);
            });
            futureList.add(future);
        });
        List<FinspStatFacDto> dtoList = H_FutureHelper.sequenceList(futureList).join();
        // w84转09
        dtoList.forEach(item->{
            double[] doubles = H_CoordinateTransformUtil.wgs84tobd09(item.getGisX(), item.getGisY());
            item.setGisX(doubles[0]).setGisY(doubles[1]);
        });
        //Map<String, List<FinspStatFacDto>> mapRes = dtoList.stream().collect(Collectors.groupingBy(FinspStatFacDto::getFacilityCat));
        return RestResult.success(dtoList);
    }

    /**
     * 查询病害性质
     * @return
     */
    @GetMapping("loadBhxz")
    public RestResult<List<BaseDssSzDto>> loadBhxz(){
        List<BaseDssSzDto> dtos = H_BasedataHepler.baseDssSzDtos;
        return RestResult.success(dtos);
    }

    /**
     * 查询病害定量信息
     * @param dssType 病害类型编码
     * @return
     */
    @GetMapping("loadDssDlxx")
    public RestResult<DssTypeNew> loadDssDlxx(String dssType){
        return RestResult.success(dssTypeNewService.selectByDssType(dssType));
    }

    @ApiOperation("查询授权路线")
    @GetMapping(value = "/loadGrantedLines")
    public RestResult<List<BaseLine>> loadGrantedLines(){
        return RestResult.success(lineService.selectGrantLines());
    }

    @ApiOperation("查询结构物")
    @PostMapping(value = "/loadStructs")
    public RestResult<List<BaseStructDto>> loadLineStructs(@RequestParam("facilityCat") String facilityCat,@RequestParam(value = "pBrdgType",required = false) String pBrdgType){
        Map<String, IBaseStructFace> beansOfType = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class);
        for (IBaseStructFace bean : beansOfType.values()) {
            if (facilityCat.equals(bean.getFacilityCat())){
                return H_PageHelper.getPageResult(bean.selectStructPage());
            }
        }
        return null;
    }

    /**
     * 查询桥幅数据
     * @param structId 桥梁ID
     * @return
     */
    @GetMapping("loadBrdgQf")
    public RestResult<List<BaseStructDto>> loadBrdgQf(@RequestParam("structId") String structId){
        List<BaseStructDto> baseStructDtos = brdgrecogService.selectQf(structId);
        return RestResult.success(baseStructDtos);
    }

    /**
     * 查询部件
     * @param finspId 经常检查单ID（用于区分不同经常检查版本）
     * @param facilityCat 结构物分类
     * @param structId 结构物ID
     * @param structPartId 部件ID（隧道查询子部件需要用到）
     * @return
     */
    @PostMapping(value = "/loadPartstype")
    public RestResult<List<FacPartTypeDto>> loadPartstype(@RequestParam(value = "finspId",required = false) String finspId,@RequestParam(value = "facilityCat",required = true) String facilityCat, @RequestParam(value = "structId",required = true) String structId,
                                                          @RequestParam(value = "structPartId",required = false) String structPartId){
        FacQueryDto dto = H_FacQueryHelper.initFacQueryDto(getReqParam());
        Map<String, IFacBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
        for (IFacBase bean : beansOfType.values()) {
            if (facilityCat.equals(bean.getFacilityCat())){
                return RestResult.success(bean.selectPartTypes(dto));
            }
        }
        return null;
    }

    /**
     * 查询构件
     * @param finspId 经常检查单ID（用于区分不同经常检查版本）
     * @param facilityCat 结构物分类
     * @param structId 结构物ID
     * @param structPartId 部件ID
     * @param compattrCode 构件编码（桥梁构件用于模块查询）
     * @return
     */
    @PostMapping(value = "/loadCompTypes")
    public RestResult<List<FacCompTypeDto>> loadCompTypes(@RequestParam(value = "finspId",required = false) String finspId,@RequestParam("facilityCat") String facilityCat, @RequestParam("structId") String structId,
                                                          @RequestParam("structPartId") String structPartId,@RequestParam(value = "compattrCode",required = false) String compattrCode){
        FacQueryDto dto = H_FacQueryHelper.initFacQueryDto(getReqParam());
        //设置经常检查，用户桥梁涵洞分页显示构建
        dto.setAction("JCJC");

        Map<String, IFacBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
        for (IFacBase bean : beansOfType.values()) {
            if (facilityCat.equals(bean.getFacilityCat())){
                IPage<FacCompTypeDto> compTypes = bean.selectCompTypes(dto);
                return H_PageHelper.getPageResult(pageService.returnPageResult(compTypes));
            }
        }
        return null;
    }

    /**
     * 查询病害类型
     * @param facilityCat 结构物类型
     * @param structId 结构物ID
     * @param structPartId 部件ID
     * @param structCompId 构件ID
     * @param dssTypePrefix 病害类型前缀（用于南粤边坡经常检查）：BPJC-NY
     * @return
     */
    @GetMapping(value = "/loadDsstype")
    public RestResult<List<FacDssTypeDto>> loadDsstype(@RequestParam("facilityCat") String facilityCat, @RequestParam("structId") String structId,
                                                       @RequestParam("structPartId") String structPartId,@RequestParam("structCompId") String structCompId
            ,@RequestParam(value = "dssTypePrefix",required = false) String dssTypePrefix){
        FacQueryDto queryDto = new FacQueryDto();
        BeanUtil.fillBeanWithMap(getReqParam(),queryDto,true);

        Map<String, IFacBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
        for (IFacBase bean : beansOfType.values()) {
            if (queryDto.getFacilityCat().equals(bean.getFacilityCat())){
                return RestResult.success(bean.selectDssType(queryDto));
            }
        }
        return null;
    }
    /**
     * 查询病害部位
     * @param facilityCat 结构物类型
     * @param structId 结构物ID
     * @param structPartId 部件ID
     * @param structCompId 构件ID
     * @return
     */
    @RequestMapping(value = "/loadPartpost")
    public RestResult<List<FacPartpostDto>> loadPartpost(@RequestParam(value = "facilityCat",required = true) String facilityCat, @RequestParam(value = "structId",required = true) String structId,
                                                         @RequestParam(value = "structPartId",required = true) String structPartId,@RequestParam(value = "structCompId",required = true) String structCompId){
        FacQueryDto queryDto = new FacQueryDto();
        BeanUtil.fillBeanWithMap(getReqParam(),queryDto,true);
        FacQlImpl bean = CustomApplicationContextHolder.getBean(FacQlImpl.class);
        return RestResult.success(bean.selectPartpost(queryDto));
    }

    /**
     * 查询病害严重程度
     * @param dssType 病害类型编码
     * @param facilityCat 结构物类型
     * @return
     */
    @RequestMapping(value = "/loadDssDegree/{dssType}")
    public RestResult<List<DsstypeDssdegree> > loadDssDegree(@PathVariable String dssType,@RequestParam("facilityCat") String facilityCat){
        FacQueryDto queryDto = new FacQueryDto();
        BeanUtil.fillBeanWithMap(getReqParam(),queryDto,true);
        Map<String, IFacBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
        for (IFacBase bean : beansOfType.values()) {
            if (queryDto.getFacilityCat().equals(bean.getFacilityCat())){
                return RestResult.success(bean.selectDssDegress(dssType));
            }
        }
        return null;
    }



    @ApiOperation("查询授权的桥梁类型（梁式桥，拱桥，斜拉桥，悬索桥）")
    @PostMapping(value = "/loadGrantedBrdgTypes")
    public RestResult<List<BridgeTypeDto>> loadGrantedBrdgTypes(@RequestParam(value = "lineCode",required = true) String lineCode){
        return RestResult.success(brdgrecogService.selectGrantedBrdgTypes(lineCode));
    }

}
