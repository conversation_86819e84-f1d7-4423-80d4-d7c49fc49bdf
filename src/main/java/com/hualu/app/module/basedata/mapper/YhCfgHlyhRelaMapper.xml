<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.YhCfgHlyhRelaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.YhCfgHlyhRela">
        <result column="SECTIONID" property="sectionid" />
        <result column="SECTIONNAME" property="sectionname" />
        <result column="SE_UNIT_CODE" property="seUnitCode" />
        <result column="SE_UNIT_NAME" property="seUnitName" />
        <result column="AREACODE" property="areacode" />
        <result column="AREANAME" property="areaname" />
        <result column="IS_GROUP" property="isGroup" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="LINE_DIRECT" property="lineDirect" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SECTIONID, SECTIONNAME, SE_UNIT_CODE, SE_UNIT_NAME, AREACODE, AREANAME, IS_GROUP, ROUTE_CODE, LINE_DIRECT
    </sql>

</mapper>
