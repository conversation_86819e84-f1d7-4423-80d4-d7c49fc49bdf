package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 收费路段信息表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="YhCfgInfo对象", description="收费路段信息表")
public class YhCfgInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收费站路段ID")
    @TableField("OBJECTID")
    private String objectid;

    @ApiModelProperty(value = "国家高速名称")
    @TableField("NATION_NAME")
    private String nationName;

    @ApiModelProperty(value = "路段编号")
    @TableField("NATION_RD_NO")
    private String nationRdNo;

    @ApiModelProperty(value = "路段名称")
    @TableField("ROAD_NAME")
    private String roadName;

    @ApiModelProperty(value = "路段编号")
    @TableField("ROAD_NO")
    private String roadNo;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STACK")
    private String startStack;

    @ApiModelProperty(value = "终点桩号")
    @TableField("END_STACK")
    private String endStack;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否在建")
    @TableField("IS_BUILT")
    private Double isBuilt;

    @ApiModelProperty(value = "是否集团内")
    @TableField("IS_JITUAN")
    private String isJituan;

    @ApiModelProperty(value = "路径地址")
    @TableField("ROAD_PATH")
    private String roadPath;


}
