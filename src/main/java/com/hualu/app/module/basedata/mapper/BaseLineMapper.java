package com.hualu.app.module.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.basedata.dto.StakeRangeDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 路线 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
public interface BaseLineMapper extends BaseMapper<BaseLine> {

    BaseLine selectLine(@Param("lineId") String lineId, @Param("isNew") String isNew);

    StakeRangeDto getRampStakeRange(
        @Param("lineId") String lineId,
        @Param("lineDirect") String lineDirect,
        @Param("userCode") String userCode
    );

    StakeRangeDto getStakeRange(
        @Param("lineId") String lineId,
        @Param("lineDirect") String lineDirect,
        @Param("userCode") String userCode
    );
}
