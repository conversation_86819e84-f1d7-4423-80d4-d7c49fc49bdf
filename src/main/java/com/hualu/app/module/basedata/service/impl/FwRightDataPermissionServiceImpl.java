package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.dto.RoadPropertyDto;
import com.hualu.app.module.basedata.entity.FwRightDataPermission;
import com.hualu.app.module.basedata.mapper.FwRightDataPermissionMapper;
import com.hualu.app.module.basedata.service.FwRightDataPermissionService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 数据权限中间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@DS("gdgsDs")
@Service
public class FwRightDataPermissionServiceImpl extends ServiceImpl<FwRightDataPermissionMapper, FwRightDataPermission> implements FwRightDataPermissionService {

    @Override
    public BaseRouteDto getRouteRpStake(String lineId, String lineDirect, Double rlStake) {
        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        if (!"4".equals(lineDirect)) {
            List<BaseRouteDto> baseRouteDtos = baseMapper.selectMainRoutes(Lists.newArrayList(routeCodes), lineId, lineDirect, rlStake);
            if (CollectionUtil.isEmpty(baseRouteDtos)) {
                throw new BaseException("桩号不在范围内");
            }
            BaseRouteDto routeDto = baseRouteDtos.get(0);
            double cnStake = routeDto.getRpStartStake() + (rlStake - routeDto.getStartStake()) / (routeDto.getEndStake() - routeDto.getStartStake()) * (routeDto.getRpEndStake() - routeDto.getRpStartStake());
            BigDecimal tempStake = new BigDecimal(cnStake).setScale(3, BigDecimal.ROUND_HALF_UP);
            routeDto.setPhysicsCntrStake(tempStake.doubleValue());
            return routeDto;
        }
        List<BaseRouteDto> baseRouteDtos = baseMapper.selectRampRoutes(Lists.newArrayList(routeCodes), lineId, rlStake);
        if (CollectionUtil.isEmpty(baseRouteDtos)){
            throw new BaseException("桩号不在范围内");
        }
        return baseRouteDtos.get(0);
    }

    @Override
    public List<BaseRouteDto> selectRouteInfo(Set<String> routeCodes) {
        if (CollectionUtil.isEmpty(routeCodes)){
            return Lists.newArrayList();
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("rt.route_code",routeCodes);
        return baseMapper.selectRouteInfo(queryWrapper);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public List<FwRightDataPermission> selectPermission(LambdaQueryWrapper queryWrapper) {
        return list(queryWrapper);
    }

    @Override
    public List<RoadPropertyDto> getRoadLength(List<String> routeCodes) {
        List<RoadPropertyDto> roadLength = baseMapper.getRoadLength(routeCodes);
        return roadLength;
    }
}
