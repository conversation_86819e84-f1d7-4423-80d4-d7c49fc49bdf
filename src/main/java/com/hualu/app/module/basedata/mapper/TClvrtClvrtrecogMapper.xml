<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.TClvrtClvrtrecogMapper">
  <select id="getCulvertTypeAndPlace"
    resultType="com.hualu.app.module.basedata.entity.TClvrtClvrtrecog">
    select (select bt.CLVRT_TYPE_NAME
            from BCTCMSDB.T_CLVRT_CONSSHAPE tc
                   inner join BCTCMSDB.T_CLVRT_CLVRTTYPE bt on tc.STRUCT_FORM = bt.CLVRT_TYPE_CODE
            where tc.CLVRTRECOG_ID = c.CLVRTRECOG_ID
              and tc.VALID_FLAG = 1
              and ROWNUM = 1) culvert_type,
           (select d.DSTRCT_NAME
            from gdgs.BASE_DSTRCT d
            where d.DSTRCT_CODE = c.PLACE
               or d.DSTRCT_ID = c.PLACE and ROWNUM = 1)
                              place,
           c.CLVRTRECOG_ID,
           c.LOGIC_CNTR_STAKE
    from BCTCMSDB.T_CLVRT_CLVRTRECOG c
    where c.VALID_FLAG = 1 and c.CLVRTRECOG_ID in
      <foreach collection="culvertIds" separator="," item="item" open="(" close=")">
        #{item}
      </foreach>
  </select>
</mapper>
