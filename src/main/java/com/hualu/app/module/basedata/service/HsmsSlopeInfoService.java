package com.hualu.app.module.basedata.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 边坡基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@DS("hsmsDs")
public interface HsmsSlopeInfoService extends IService<HsmsSlopeInfo> {

  List<SlopeBatchAttentionDto> listByStakeRange(String lineCode, double startStake, double endStake);

  /**
   * 获取边坡简介
   * @param slopeIds
   * @return
   */
  Map<String,String> resolveSlopeProfileMap(List<String> slopeIds);
}
