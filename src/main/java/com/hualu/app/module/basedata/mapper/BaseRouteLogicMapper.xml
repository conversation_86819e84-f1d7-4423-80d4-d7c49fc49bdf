<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.BaseRouteLogicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.BaseRouteLogic">
        <id column="LOGIC_ROUTE_ID" property="logicRouteId" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="ROUTE_LENGTH" property="routeLength" />
        <result column="OPRT_ORG_CODE" property="oprtOrgCode" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ROUTE_VERSION" property="routeVersion" />
        <result column="CREATE_VERSION_TIME" property="createVersionTime" />
        <result column="PRJ_ORG_CODE" property="prjOrgCode" />
        <result column="START_STAKE" property="startStake" />
        <result column="END_STAKE" property="endStake" />
        <result column="RP_START_STAKE" property="rpStartStake" />
        <result column="RP_END_STAKE" property="rpEndStake" />
        <result column="OLD_LINE_CODE" property="oldLineCode" />
        <result column="OLD_LINE_DIRECT" property="oldLineDirect" />
        <result column="OLD_VERSION" property="oldVersion" />
        <result column="OLD_START_STAKE" property="oldStartStake" />
        <result column="OLD_END_STAKE" property="oldEndStake" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="LINE_ID" property="lineId" />
        <result column="CF_LINE_ID" property="cfLineId" />
        <result column="CF_LINE_CODE" property="cfLineCode" />
        <result column="CF_LINE_LDLX" property="cfLineLdlx" />
        <result column="CF_START_STAKE" property="cfStartStake" />
        <result column="CF_END_STAKE" property="cfEndStake" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="IS_NEW" property="isNew" />
        <result column="RMAL_NUMBER" property="rmalNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        LOGIC_ROUTE_ID, ROUTE_CODE, ROUTE_LENGTH, OPRT_ORG_CODE, LINE_CODE, LINE_DIRECT, ROUTE_VERSION, CREATE_VERSION_TIME, PRJ_ORG_CODE, START_STAKE, END_STAKE, RP_START_STAKE, RP_END_STAKE, OLD_LINE_CODE, OLD_LINE_DIRECT, OLD_VERSION, OLD_START_STAKE, OLD_END_STAKE, IS_ENABLE, LINE_ID, CF_LINE_ID, CF_LINE_CODE, CF_LINE_LDLX, CF_START_STAKE, CF_END_STAKE, ROUTE_NAME, IS_NEW, RMAL_NUMBER
    </sql>

</mapper>
