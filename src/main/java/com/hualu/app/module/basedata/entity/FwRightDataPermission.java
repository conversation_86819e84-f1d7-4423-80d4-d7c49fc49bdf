package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 数据权限中间表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="FwRightDataPermission对象", description="数据权限中间表")
public class FwRightDataPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "路段序号")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "营运公司")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "项目公司")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "路线ID")
    @TableField("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE")
    private Double startStake;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE")
    private Double endStake;

    @ApiModelProperty(value = "方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "权限主键")
    @TableId("PRE_ID")
    private String preId;

    @ApiModelProperty(value = "路段版本(权限表中存储最新路段版本)")
    @TableField("ROUTE_VERSION")
    private String routeVersion;


}
