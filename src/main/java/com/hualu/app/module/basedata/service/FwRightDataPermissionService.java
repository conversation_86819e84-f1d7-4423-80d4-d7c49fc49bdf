package com.hualu.app.module.basedata.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.dto.RoadPropertyDto;
import com.hualu.app.module.basedata.entity.FwRightDataPermission;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 数据权限中间表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface FwRightDataPermissionService extends IService<FwRightDataPermission> {

    /**
     * 查询物理桩号
     * @param lineId
     * @param lineDirect
     * @param rlStake
     * @return
     */
    BaseRouteDto getRouteRpStake(String lineId,String lineDirect,Double rlStake);

    /**
     * 查询路段相关信息
     * @param routeCodes
     * @return
     */
    List<BaseRouteDto> selectRouteInfo(Set<String> routeCodes);

    /**
     * 查询数据（用于新事务）
     * @param queryWrapper
     * @return
     */
    List<FwRightDataPermission> selectPermission(LambdaQueryWrapper queryWrapper);

    /**
     * 获取路段里程
     * @param routeCodes
     * @return
     */
    List<RoadPropertyDto> getRoadLength(List<String> routeCodes);
}
