package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.entity.HsmsParts;
import com.hualu.app.module.basedata.mapper.HsmsPartsMapper;
import com.hualu.app.module.basedata.service.HsmsPartsService;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 几何信息及支挡加固防护信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@DS("hsmsDs")
@Service
public class HsmsPartsServiceImpl extends ServiceImpl<HsmsPartsMapper, HsmsParts> implements HsmsPartsService {

    @Override
    public Map<String, String> resolveSlopeProfileMap(List<String> slopeIds) {
        LinkedHashMap<String, List<HsmsParts>> slopeMap = lambdaQuery()
                .select(HsmsParts::getSlopeId, HsmsParts::getRate, HsmsParts::getSeries, HsmsParts::getFhPartName, HsmsParts::getZdPartName)
                .in(HsmsParts::getSlopeId, slopeIds)
                .orderByAsc(HsmsParts::getSlopeId, HsmsParts::getSeries)
                .list()
                .stream()
                .filter(e -> ObjectUtil.isNotEmpty(e.getSeries()))
                .collect(Collectors.groupingBy(HsmsParts::getSlopeId, LinkedHashMap::new, Collectors.toList()));

        Map<String, String> map = new LinkedHashMap<>();
        slopeMap.forEach((k, v) -> {
            for (HsmsParts part : v) {
                StringBuffer stringBuffer = new StringBuffer();
                if (ObjectUtil.isNotEmpty(part.getRate())) {
                    stringBuffer.append(part.getSeries()).append("边坡坡率").append("1:").append(part.getRate());
                }
                if (ObjectUtil.isNotEmpty(part.getFhPartName()) && ObjectUtil.isNotEmpty(part.getZdPartName())) {
                    stringBuffer.append("，采用").append(part.getZdPartName()).append("+").append(part.getFhPartName());
                }
                map.put(k, stringBuffer.toString());
            }
        });
        return map;
    }
}
