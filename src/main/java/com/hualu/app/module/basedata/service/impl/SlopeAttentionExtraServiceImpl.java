package com.hualu.app.module.basedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.SlopeAttentionExtra;
import com.hualu.app.module.basedata.mapper.SlopeAttentionExtraMapper;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.basedata.service.SlopeAttentionExtraService;
import com.tg.dev.api.core.global.exception.BaseException;
import org.springframework.stereotype.Service;

@DS("mtmsDs")
@Service
public class SlopeAttentionExtraServiceImpl
    extends ServiceImpl<SlopeAttentionExtraMapper, SlopeAttentionExtra>
    implements SlopeAttentionExtraService, IBaseStructFace {

  @Override public BaseStructDto getId(String structId) {
    SlopeAttentionExtra slopeAttentionExtra = baseMapper.selectById(structId);
    return initDto(slopeAttentionExtra);
  }

  @Override public String getFacilityCat() {
    return "BP_EXTRA";
  }

  @Override public Object selectStructPage() {
    return null;
  }

  private BaseStructDto initDto(SlopeAttentionExtra item){
    BaseStructDto dto = new BaseStructDto();

    if (item == null){
      throw new BaseException("未找到结构物");
    }
    dto.setStructId(item.getSlopeId());
    dto.setStructCode(item.getSlopeId());
    dto.setStructName(item.getSlopeName());
    dto.setSlopeType(item.getSlopeType());
    dto.setX(item.getLongitude().doubleValue());
    dto.setY(item.getLatitude().doubleValue());
    return dto;
  }
}
