package com.hualu.app.module.basedata.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@Data
public class NmDinspStsTreeDto {
  private int year;
  private String month;
  private int structNum;
  private int checkNum;
  private int dis;
  private String num;

  public NmDinspStsTreeDto() {
  }

  public NmDinspStsTreeDto(int year, String month, int structNum, int checkNum, int dis) {
    this.year = year;
    this.month = month;
    this.structNum = structNum;
    this.checkNum = checkNum;
    this.dis = dis;
  }
}
