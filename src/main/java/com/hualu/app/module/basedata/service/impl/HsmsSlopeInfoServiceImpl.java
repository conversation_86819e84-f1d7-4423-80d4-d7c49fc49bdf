package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.basedata.mapper.HsmsSlopeInfoMapper;
import com.hualu.app.module.basedata.service.HsmsPartsService;
import com.hualu.app.module.basedata.service.HsmsSlopeInfoService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.*;

/**
 * <p>
 * 边坡基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@DS("hsmsDs")
@Service
public class HsmsSlopeInfoServiceImpl extends ServiceImpl<HsmsSlopeInfoMapper, HsmsSlopeInfo> implements HsmsSlopeInfoService, IBaseStructFace {

    @Lazy
    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    HsmsPartsService partsService;


    @Override
    public BaseStructDto getId(String structId) {
        HsmsSlopeInfo slopeInfo = baseMapper.selectById(structId);
        return initDto(slopeInfo);
    }

    @Override
    public String getFacilityCat() {
        return "BP";
    }

    @Override
    public Object selectStructPage() {
        Map<String, Object> paramsMap = getStructParamMap("slopeName","slopeId");
        Object appLineCode = paramsMap.get("appLineCode");
        Set<String> routeCodes = null;
        if (ObjectUtil.isNotEmpty(appLineCode)){
            routeCodes = H_DataAuthHelper.selectRouteCodeAuthByLineCode(appLineCode.toString());
            paramsMap.remove("lineId");
            paramsMap.remove("lineCode");
        }else {
            routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        }
        QueryWrapper<HsmsSlopeInfo> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);

        String lineId = paramsMap.getOrDefault("lineId","").toString();
        if (StrUtil.isNotBlank(lineId)){
            paramsMap.remove("lineId");
            queryWrapper.lambda().and(e->e.eq(HsmsSlopeInfo::getLineId,lineId).or(b->b.eq(HsmsSlopeInfo::getLineCode,lineId)));
        }

        String sortField = (String) paramsMap.getOrDefault("sortField","");
        String sortOrder = (String) paramsMap.getOrDefault("sortOrder","");

        if(StrUtil.isNotBlank(sortField) && StrUtil.isNotBlank(sortOrder)){
            String tempField = sortField.replaceAll("slopePositionName", "slopePosition").replaceAll("slopeTypeName", "slopeType");
            H_BatisQuery.setWrapperSortParam(queryWrapper,tempField,sortOrder);
        }else {
            // 根据路线编码、及桩号排序
            queryWrapper.lambda().orderByAsc(HsmsSlopeInfo::getLineCode,HsmsSlopeInfo::getLogicCntrStake);
        }
        H_BatisQuery.setFieldValue2In(queryWrapper,paramsMap,HsmsSlopeInfo.class);
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();

        if (iPage.getRecords().size() != 0){
            List<BaseDatathirdDic> positionDics = dicService.listDicByItem(H_BasedataHepler.DIC_BP_POSITION);
            List<BaseDatathirdDic> pbTypeDics = dicService.listDicByItem(H_BasedataHepler.DIC_BP_TYPE);
            iPage.getRecords().forEach(row->{
                HsmsSlopeInfo item = (HsmsSlopeInfo) row;
                BaseStructDto dto = initDto(positionDics,pbTypeDics,item);
                dtos.add(dto);
            });
        }
        iPage.setRecords(dtos);
        return returnPage(iPage);
    }

    @Override
    public List<BaseStructDto> listByRouteCodes(Set<String> routeCodes) {
        QueryWrapper<HsmsSlopeInfo> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);
        queryWrapper.lambda()
                .select(HsmsSlopeInfo::getSlopeId,HsmsSlopeInfo::getSlopeName,HsmsSlopeInfo::getRouteCode,HsmsSlopeInfo::getLogicCntrStake)
                .orderByAsc(HsmsSlopeInfo::getLineCode,HsmsSlopeInfo::getLogicCntrStake);
        List<HsmsSlopeInfo> hsmsSlopeInfos = baseMapper.selectList(queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();
        hsmsSlopeInfos.forEach(row->{
            BaseStructDto dto = new BaseStructDto()
                    .setStructId(row.getSlopeId()).setRouteCode(row.getRouteCode());
            dto.setRlCntrStake(row.getLogicCntrStake());
            dto.setStructName(row.getSlopeName()+"("+dto.getDisplayStake()+")");
            dtos.add(dto);
        });
        return dtos;
    }

    private void initWrapper(QueryWrapper<HsmsSlopeInfo> queryWrapper,Set<String> routeCodes) {
        queryWrapper.eq("is_deleted",0);
        queryWrapper.in(CollectionUtil.isNotEmpty(routeCodes),"route_code",routeCodes);
    }

    @Override
    public List<BaseStructDto> listByStructIds(List<String> structIds) {
        List<HsmsSlopeInfo> allList = Lists.newArrayList();
        List<List<String>> partition = ListUtil.partition(structIds, 1000);
        partition.forEach(rows->{
            List<HsmsSlopeInfo> slopeInfoList = lambdaQuery()
                    .select(HsmsSlopeInfo::getLineId,HsmsSlopeInfo::getSlopeId,
                            HsmsSlopeInfo::getSlopeCode,HsmsSlopeInfo::getSlopeName,
                            HsmsSlopeInfo::getLogicCntrStake,HsmsSlopeInfo::getRpIntrvlId
                            ,HsmsSlopeInfo::getSlopeLength,HsmsSlopeInfo::getLogicStartStake,
                            HsmsSlopeInfo::getLogicEndStake,HsmsSlopeInfo::getRouteCode,
                            HsmsSlopeInfo::getRouteVersion,HsmsSlopeInfo::getSlopeLevel,
                            HsmsSlopeInfo::getSlopeLevel,HsmsSlopeInfo::getSlopePosition,
                            HsmsSlopeInfo::getSlopeType,HsmsSlopeInfo::getGisX,
                            HsmsSlopeInfo::getGisY,HsmsSlopeInfo::getOptOrgId).in(HsmsSlopeInfo::getSlopeId, rows)
                    .orderByAsc(HsmsSlopeInfo::getLineCode,HsmsSlopeInfo::getLogicCntrStake)
                    .list();
            allList.addAll(slopeInfoList);
        });
        List<BaseStructDto> dtos = Lists.newArrayList();
        for (HsmsSlopeInfo info : allList) {
            BaseStructDto baseStructDto = initDto(info);
            dtos.add(baseStructDto);
        }
        return dtos;
    }

    private BaseStructDto initDto(HsmsSlopeInfo item){
        BaseStructDto dto = new BaseStructDto();

        if (item == null){
            throw new BaseException("未找到结构物");
        }
        dto.setLineId(item.getLineId());
        dto.setStructId(item.getSlopeId());
        dto.setStructCode(item.getSlopeCode());
        dto.setRlCntrStake(item.getLogicCntrStake());
        //todo 边坡方向需要显示成中文
        dto.setStructName(item.getSlopeName()+"("+dto.getDisplayStake()+")");
        dto.setRpIntrvlId(item.getRpIntrvlId());
        dto.setStructLength(item.getSlopeLength());
        dto.setCntrStake(item.getLogicCntrStake());
        dto.setLogicStartStake(ObjectUtil.isNotNull(item.getLogicStartStake())?new DecimalFormat("#.###").format(item.getLogicStartStake()):null);
        dto.setLogicEndStake(ObjectUtil.isNotNull(item.getLogicEndStake())?new DecimalFormat("#.###").format(item.getLogicEndStake()):null);
        dto.setRouteCode(item.getRouteCode());
        dto.setRouteVersion(item.getRouteVersion());
        dto.setSlopeLevel(item.getSlopeLevel());
        dto.setSlopePosition(item.getSlopePosition());
        dto.setSlopePositionName(dicService.getDicName(H_BasedataHepler.DIC_BP_POSITION,dto.getSlopePosition()));
        dto.setSlopeType(item.getSlopeType());
        dto.setSlopeTypeName(dicService.getDicName(H_BasedataHepler.DIC_BP_TYPE,dto.getSlopeType()));
        dto.setX(item.getGisX());
        dto.setY(item.getGisY());
        dto.setOprtOrgCode(item.getOptOrgId());
        return dto;
    }

    private BaseStructDto initDto(List<BaseDatathirdDic> positionDics,List<BaseDatathirdDic> bpTypeDics,HsmsSlopeInfo item){
        BaseStructDto dto = new BaseStructDto();

        if (item == null){
            throw new BaseException("未找到结构物");
        }
        dto.setLineId(item.getLineId());
        dto.setStructId(item.getSlopeId());
        dto.setStructCode(item.getSlopeCode());
        dto.setRlCntrStake(item.getLogicCntrStake());
        //todo 边坡方向需要显示成中文
        dto.setStructName(item.getSlopeName()+"("+dto.getDisplayStake()+")");
        dto.setRpIntrvlId(item.getRpIntrvlId());
        dto.setStructLength(item.getSlopeLength());
        dto.setCntrStake(item.getLogicCntrStake());
        dto.setLogicStartStake(ObjectUtil.isNotNull(item.getLogicStartStake())?new DecimalFormat("#.###").format(item.getLogicStartStake()):null);
        dto.setLogicEndStake(ObjectUtil.isNotNull(item.getLogicEndStake())?new DecimalFormat("#.###").format(item.getLogicEndStake()):null);
        dto.setRouteCode(item.getRouteCode());
        dto.setRouteVersion(item.getRouteVersion());
        dto.setSlopeLevel(item.getSlopeLevel());
        dto.setSlopePosition(item.getSlopePosition());
        dto.setSlopePositionName(dicService.getDicName(positionDics,dto.getSlopePosition()));
        dto.setSlopeType(item.getSlopeType());
        dto.setSlopeTypeName(dicService.getDicName(bpTypeDics,dto.getSlopeType()));
        dto.setX(item.getGisX());
        dto.setY(item.getGisY());
        dto.setOprtOrgCode(item.getOptOrgId());
        return dto;
    }

    @Override
    public List<SlopeBatchAttentionDto> listByStakeRange(
        String lineCode,
        double startStake,
        double endStake
    ) {
        double min = Math.min(startStake,endStake);
        double max = Math.max(startStake,endStake);
        return getBaseMapper().listByStakeRange(lineCode, min, max);
    }

    @Override
    public Map<String, String> resolveSlopeProfileMap(List<String> slopeIds) {
        Map<String,String> profileMap = new HashMap<String,String>();
        Map<String, String> partMap = partsService.resolveSlopeProfileMap(slopeIds);
        // 获取边坡的级数及路堤还是路堑
        List<HsmsSlopeInfo> slopeInfoList = lambdaQuery().select(HsmsSlopeInfo::getSlopeLevel, HsmsSlopeInfo::getSlopeType,HsmsSlopeInfo::getSlopeId).in(HsmsSlopeInfo::getSlopeId, slopeIds).list();
        slopeInfoList.forEach(item->{
            String dicName = dicService.getDicName(H_BasedataHepler.DIC_BP_TYPE, item.getSlopeType());
            String desc = item.getSlopeLevel() + "级" + dicName;

            String partDesc = partMap.getOrDefault(item.getSlopeId(), "");
            if (StrUtil.isNotBlank(partDesc)){
                desc = desc + "，" +partDesc;
            }
            profileMap.put(item.getSlopeId(),desc);
        });
        return profileMap;
    }
}
