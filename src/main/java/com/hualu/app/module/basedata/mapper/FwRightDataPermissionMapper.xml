<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.FwRightDataPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.FwRightDataPermission">
        <id column="PRE_ID" property="preId" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="OPRT_ORG_CODE" property="oprtOrgCode" />
        <result column="PRJ_ORG_CODE" property="prjOrgCode" />
        <result column="LINE_ID" property="lineId" />
        <result column="START_STAKE" property="startStake" />
        <result column="END_STAKE" property="endStake" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ROUTE_VERSION" property="routeVersion" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ROUTE_CODE, OPRT_ORG_CODE, PRJ_ORG_CODE, LINE_ID, START_STAKE, END_STAKE, LINE_DIRECT, PRE_ID, ROUTE_VERSION
    </sql>
    <select id="selectMainRoutes" resultType="com.hualu.app.module.basedata.dto.BaseRouteDto">
        select p.start_stake
             , p.end_stake
             , r.rp_start_stake
             , r.rp_end_stake
             , r.route_length
             , r.route_code
             , r.route_version
             , r.line_code
             , p.oprt_org_code
             , p.prj_org_code
        from gdgs.fw_right_data_permission p
        inner join gdgs.base_route_logic r on p.route_code = r.route_code and
                p.line_id = r.line_id
        where p.line_id = #{lineId}
          and p.line_direct = #{lineDirect}
          and (#{rlStake} - r.start_stake) * (#{rlStake} - r.end_stake) &lt;= 0
          and r.is_new = '1'
          <if test="codes != null and codes.size > 0">
              and p.route_code in
              <foreach collection="codes" item="id" index="index" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>
    </select>
    <select id="selectRampRoutes" resultType="com.hualu.app.module.basedata.dto.BaseRouteDto">
        select (#{rlStake} - ramplogic.start_stake_num) as physics_cntr_stake, logic.rp_intrvl_id, logic.route_code, logic.route_version
        from gdgs.base_ramp_intrvl_logic ramplogic
                 inner join gdgs.base_route_intrvl_logic logic on logic.rp_intrvl_id = ramplogic.rp_intrvl_id
        where ramplogic.line_id = #{lienId}
          and logic.line_direct = '4'
          and (ramplogic.start_stake_num - #{rlStake}) * (ramplogic.end_stake_num - #{rlStake}) &lt;= 0
        <if test="codes != null and codes.size > 0">
            and logic.route_code in
            <foreach collection="codes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectRouteInfo" resultType="com.hualu.app.module.basedata.dto.BaseRouteDto">
        select rt.ROUTE_CODE,rt.ROUTE_NAME,o.ORG_FULLNAME as oprt_org_name from gdgs.BASE_ROUTE_LOGIC rt join gdgs.FW_RIGHT_ORG o on rt.OPRT_ORG_CODE = o.ORG_CODE ${ew.customSqlSegment}
    </select>
    <select id="getRoadLength" resultType="com.hualu.app.module.basedata.dto.RoadPropertyDto">
        select route_name,sum(decode(LINE_DIRECT,'1',(END_STAKE-START_STAKE),0)) as road_length,wm_concat(route_code) as route_codes from gdgs.BASE_ROUTE_LOGIC where 1=1
        <if test="codes != null and codes.size > 0">
            and route_code in
            <foreach collection="codes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        group by route_name
    </select>
</mapper>
