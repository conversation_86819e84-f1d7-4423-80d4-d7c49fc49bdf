package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 边坡图片表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HsmsSlopePic对象", description="边坡图片表")
public class HsmsSlopePic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "边坡ID")
    @TableField("SLOPE_ID")
    private String slopeId;

    @ApiModelProperty(value = "1:正面照，2：地形地貌，3：侧面照，4：其它, 5.1:立面图, 5.2:测点布设图, 5.3:断面照片, 5.4:细部照片")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "图片ID")
    @TableField("FILE_ENTITY_ID")
    private String fileEntityId;

    @ApiModelProperty(value = "图片上传时间")
    @TableField("UPLOAD_DATE")
    private LocalDateTime uploadDate;

    @ApiModelProperty(value = "文件名，配合推数据用")
    @TableField("FILE_NAME")
    private String fileName;


}
