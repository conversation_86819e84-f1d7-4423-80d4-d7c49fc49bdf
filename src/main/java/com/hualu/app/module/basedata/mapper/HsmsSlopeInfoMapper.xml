<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.HsmsSlopeInfoMapper">

    <select id="listByStakeRange" resultType="com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto">
        select
            d.SLOPE_ID,
            d.SLOPE_NAME,
            d.LINE_CODE,
            d.LOGIC_CNTR_STAKE STAKE,
            HSMSDB.GETDICVALUE(d.SLOPE_TYPE, 'SLOPE_TYPE', 'sys') SLOPE_TYPE_NAME,
            HSMSDB.GETMULTIDICVALUE(d.TYPICAL_SECTION_TYPE, 'TYPICAL_SECTION_TYPE', 'sys') SECTION_TYPE_NAME,
            decode(d.IS_IMPORTANT, 1,'是', 0, '否') IMPORTANT_NAME
        from HSMSDB.HSMS_SLOPE_INFO d
        where d.IS_DELETED = 0 and d.LINE_CODE = #{lineCode}
          and d.LOGIC_CNTR_STAKE between #{startStake} and #{endStake}
    </select>
</mapper>
