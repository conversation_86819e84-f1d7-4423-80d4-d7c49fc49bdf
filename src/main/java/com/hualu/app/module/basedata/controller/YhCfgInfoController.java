package com.hualu.app.module.basedata.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.YhCfgStatVo;
import com.hualu.app.module.basedata.entity.YhCfgInfo;
import com.hualu.app.module.basedata.mapper.YhCfgInfoMapper;
import com.hualu.app.module.basedata.service.YhCfgInfoService;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 收费路段信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@Api(tags = "收费路段信息表_接口")
@RestController
@RequestMapping("/yhCfgInfo")
public class YhCfgInfoController extends M_MyBatisController<YhCfgInfo,YhCfgInfoMapper>{

    @Autowired
    private YhCfgInfoService service;

    /**
    * 获取管养单位路线及结构物数量(APP)
    * @return
    */
    @ApiRegister(value = "yhCfgInfo:list",businessType = BusinessType.OTHER)
    @ApiOperation("获取管养单位路线及结构物数量")
    @GetMapping("YhCfgStatVo")
    public RestResult<List<YhCfgStatVo>> getYhCfgInfoDto() {
        List<YhCfgStatVo> yhCfgInfoVo = service.getYhCfgInfoVo();
        return RestResult.success(yhCfgInfoVo);
    }
}