package com.hualu.app.module.basedata.mapper;

import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 涵洞行政识 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface TClvrtClvrtrecogMapper extends BaseMapper<TClvrtClvrtrecog> {

  List<TClvrtClvrtrecog> getCulvertTypeAndPlace(@Param("culvertIds") List<String> culvertIds);
}
