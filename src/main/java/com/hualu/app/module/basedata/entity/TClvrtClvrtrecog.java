package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 涵洞行政识
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TClvrtClvrtrecog对象", description="涵洞行政识")
public class TClvrtClvrtrecog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "涵洞行政识ID")
    @TableId("CLVRTRECOG_ID")
    private String clvrtrecogId;

    @ApiModelProperty(value = "技术等级")
    @TableField("TECH_GRADE")
    private String techGrade;

    @ApiModelProperty(value = "评价时间")
    @TableField("JUDGE_DATE")
    private LocalDateTime judgeDate;

    @ApiModelProperty(value = "1.有效 2.无效")
    @TableField("VALID_FLAG")
    private Integer validFlag;

    @ApiModelProperty(value = "路线编号")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "路线名称")
    @TableField("LINE_NAME")
    private String lineName;

    @ApiModelProperty(value = "路线等级")
    @TableField("ROUTE_LVL")
    private String routeLvl;

    @ApiModelProperty(value = "所在地")
    @TableField("PLACE")
    private String place;

    @ApiModelProperty(value = "顺序号")
    @TableField("SEQ_NO")
    private String seqNo;

    @ApiModelProperty(value = "施工桩号")
    @TableField("CNSTRCT_STAKE")
    private String cnstrctStake;

    @ApiModelProperty(value = "设计桩号")
    @TableField("DSGN_STAKE")
    private String dsgnStake;

    @ApiModelProperty(value = "旧国高网桩号")
    @TableField("OLD_HIGHWAY_STAKE")
    private String oldHighwayStake;

    @ApiModelProperty(value = "功能类型")
    @TableField("FUNC_TYPE")
    private String funcType;

    @ApiModelProperty(value = "经度")
    @TableField("LONGITUDE")
    private Double longitude;

    @ApiModelProperty(value = "设计荷载")
    @TableField("DSGN_LOAD")
    private String dsgnLoad;

    @ApiModelProperty(value = "与路线正斜交")
    @TableField("LINE_HETEROTROPIC_QUADRATURE")
    private String lineHeterotropicQuadrature;

    @ApiModelProperty(value = "纬度")
    @TableField("LATITUDE")
    private Double latitude;

    @ApiModelProperty(value = "进水口位置")
    @TableField("WATER_INLET_POSITION")
    private String waterInletPosition;

    @ApiModelProperty(value = "节段数")
    @TableField("STAGE")
    private Integer stage;

    @ApiModelProperty(value = "建成年限")
    @TableField("COMPLETED_YEARS")
    private String completedYears;

    @ApiModelProperty(value = "设计单位")
    @TableField("DSGN_ORG")
    private String dsgnOrg;

    @ApiModelProperty(value = "施工单位")
    @TableField("CNSTRCT_ORG")
    private String cnstrctOrg;

    @ApiModelProperty(value = "监理单位")
    @TableField("SUPER_ORG")
    private String superOrg;

    @ApiModelProperty(value = "养护单位")
    @TableField("CBMS_ORG")
    private String cbmsOrg;

    @ApiModelProperty(value = "监管单位")
    @TableField("REGULATORY_ORG")
    private String regulatoryOrg;

    @ApiModelProperty(value = "涵洞状态")
    @TableField("CLVRT_STATUS")
    private Integer clvrtStatus;

    @ApiModelProperty(value = "涵洞名称")
    @TableField("CLVRT_NAME")
    private String clvrtName;

    @ApiModelProperty(value = "营运路段区间编码")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "涵洞编码")
    @TableField("CLVRT_CODE")
    private String clvrtCode;

    @ApiModelProperty(value = "涵洞路线类型")
    @TableField("CLVRT_LINE_TYPE")
    private String clvrtLineType;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE")
    private String cntrStake;

    @ApiModelProperty(value = "中心桩号值")
    @TableField("CNTR_STAKE_NUM")
    private Double cntrStakeNum;

    @ApiModelProperty(value = "中心桩号链值")
    @TableField("CNTR_SENSSION_NUM")
    private Double cntrSenssionNum;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_CODE", fill = FieldFill.INSERT)
    private String createUserCode;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField(value = "UPDATE_USER_CODE", fill = FieldFill.INSERT_UPDATE)
    private String updateUserCode;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "0.错识 1.正常")
    @TableField("DATA_STATUS")
    private Integer dataStatus;

    @ApiModelProperty(value = "结构形构")
    @TableField("STRUCT_FORM")
    private String structForm;

    @ApiModelProperty(value = "最近检测时间")
    @TableField("CHECK_TIME")
    private LocalDateTime checkTime;

    @ApiModelProperty(value = "线路ID")
    @TableField("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "匝道桩号值")
    @TableField("RAMP_STAKE")
    private String rampStake;

    @ApiModelProperty(value = "与路线角度")
    @TableField("ANGLE")
    private Double angle;

    @ApiModelProperty(value = "逻辑桩号（显示）")
    @TableField("LOGIC_CNTR_STAKE")
    private String logicCntrStake;

    @ApiModelProperty(value = "逻辑桩号值")
    @TableField("LOGIC_CNTR_STAKE_NUM")
    private Double logicCntrStakeNum;

    @ApiModelProperty(value = "高德经度")
    @TableField("JD")
    private Double jd;

    @ApiModelProperty(value = "高德纬度")
    @TableField("WD")
    private Double wd;

    @ApiModelProperty(value = "涵洞旧编码")
    @TableField("OLD_CLVRT_CODE")
    private String oldClvrtCode;

    @ApiModelProperty(value = "是否倒虹吸")
    @TableField("ISP")
    private Double isp;

    @ApiModelProperty(value = "项目公司编码")
    @TableField("PRJ_CODE")
    private String prjCode;

    @ApiModelProperty(value = "项目公司名称")
    @TableField("PRJ_NAME")
    private String prjName;

    @ApiModelProperty(value = "逻辑路线版本")
    @TableField("ROUTE_CODE_VERSION")
    private String routeCodeVersion;

    @ApiModelProperty(value = "路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "涵洞编码备份")
    @TableField("CLVRT_CODE_BAK")
    private String clvrtCodeBak;

    @TableField("BRDG_ROUTE_CODE_BAK")
    private String brdgRouteCodeBak;

    @ApiModelProperty(value = "删除时间")
    @TableField("DEL_TIME")
    private LocalDateTime delTime;

    @ApiModelProperty(value = "删除人")
    @TableField("DEL_PERSON")
    private String delPerson;

    @ApiModelProperty(value = "恢复时间")
    @TableField("RECOVER_TIME")
    private LocalDateTime recoverTime;

    @ApiModelProperty(value = "恢复人")
    @TableField("RECOVER_PERSON")
    private String recoverPerson;

    @ApiModelProperty(value = "是否浆砌片石")
    @TableField("MORTARRUBBLE")
    private Double mortarrubble;

    @ApiModelProperty(value = "涵台加固形式")
    @TableField("REINFORCEMENTFORM")
    private String reinforcementform;

    @ApiModelProperty(value = "加固形式补充")
    @TableField("REINFORCEMENTFORMEXTRA")
    private String reinforcementformextra;

    @TableField("PLACE_BAK")
    private String placeBak;

    @ApiModelProperty(value = "是否有档案")
    @TableField("HASAV")
    private String hasav;

    @TableField(exist = false)
    private String culvertType;
}
