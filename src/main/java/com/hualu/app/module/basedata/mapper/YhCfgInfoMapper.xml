<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.YhCfgInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.YhCfgInfo">
        <result column="OBJECTID" property="objectid" />
        <result column="NATION_NAME" property="nationName" />
        <result column="NATION_RD_NO" property="nationRdNo" />
        <result column="ROAD_NAME" property="roadName" />
        <result column="ROAD_NO" property="roadNo" />
        <result column="START_STACK" property="startStack" />
        <result column="END_STACK" property="endStack" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_BUILT" property="isBuilt" />
        <result column="IS_JITUAN" property="isJituan" />
        <result column="ROAD_PATH" property="roadPath"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        OBJECTID, NATION_NAME, NATION_RD_NO, ROAD_NAME, ROAD_NO, START_STACK, END_STACK, UPDATE_TIME, IS_BUILT, IS_JITUAN, ROAD_PATH
    </sql>
    <select id="selectYhCfgInfo" resultType="com.hualu.app.module.basedata.dto.YhCfgInfoDto">
        SELECT
            DISTINCT
            B.objectid,
            B.NATION_NAME,
            B.NATION_RD_NO,
            B.ROAD_NAME,
            B.ROAD_NO,
            B.START_STACK,
            B.END_STACK,
            C.OPRT_ORG_CODE,
            o.ORG_FULLNAME as OPRT_ORG_NAME
        FROM
            YH_CFG_HLYH_RELA A join YH_CFG_INFO B ON A.SECTIONID = B.ROAD_NO
            JOIN FW_RIGHT_DATA_PERMISSION C on A.route_code = C.route_code
            join FW_RIGHT_ORG o on c.oprt_org_code = o.id
        where
            A.ROUTE_CODE IN
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
