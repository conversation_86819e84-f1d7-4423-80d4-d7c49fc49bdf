package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 服务区
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseServiceArea对象", description="服务区")
public class BaseServiceArea implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务区编码")
    @TableId("SERVICE_ID")
    private String serviceId;

    @ApiModelProperty(value = "营运路段ID")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "服务区编码")
    @TableField("SERVICE_CODE")
    private String serviceCode;

    @ApiModelProperty(value = "服务区名称")
    @TableField("SERVICE_NAME")
    private String serviceName;

    @ApiModelProperty(value = "服务区路段类型")
    @TableField("UPDOWN_PSTN")
    private String updownPstn;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE")
    private String cntrStake;

    @ApiModelProperty(value = "中心桩号值")
    @TableField("CNTR_STAKE_NUM")
    private Double cntrStakeNum;

    @ApiModelProperty(value = "中心桩号链值")
    @TableField("CNTR_SENSSION_NUM")
    private Double cntrSenssionNum;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "是否启用（0）未启用（1）已启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @TableField("GIS_ID")
    private String gisId;

    @TableField("GIS_X")
    private Double gisX;

    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "行政区")
    @TableField("AREA_ZOME")
    private String areaZome;

    @ApiModelProperty(value = "主路线ID")
    @TableField("MAINLINE_ID")
    private String mainlineId;

    @ApiModelProperty(value = "服务区类型（1上行服务区,2下行服务区）")
    @TableField("SERVICE_TYPE")
    private Integer serviceType;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "服务区位置")
    @TableField("SERVICE_LOCATION")
    private String serviceLocation;

    @ApiModelProperty(value = "归属项目公司编码")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "主线桩号对应的主线路段区间ID")
    @TableField("THREAD_RPID")
    private String threadRpid;

    @ApiModelProperty(value = "逻辑桩号（显示）")
    @TableField("LOGIC_CNTR_STAKE")
    private String logicCntrStake;

    @ApiModelProperty(value = "逻辑桩号值")
    @TableField("LOGIC_CNTR_STAKE_NUM")
    private Double logicCntrStakeNum;

    @ApiModelProperty(value = "公路局上报编码")
    @TableField("REPORT_CODE")
    private String reportCode;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "路线名称")
    @TableField("LINE_ALLNAME")
    private String lineAllname;

    @ApiModelProperty(value = "物理中心桩号")
    @TableField("PHYSICS_CNTR_STAKE")
    private Double physicsCntrStake;

    @ApiModelProperty(value = "物理路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "路线版本")
    @TableField("ROUTE_VERSION")
    private String routeVersion;

    @ApiModelProperty(value = "匝道逻辑桩号")
    @TableField("LOGIC_RAMP_CNTR_STAKE")
    private Double logicRampCntrStake;

    @ApiModelProperty(value = "公路局关联主键")
    @TableField("YHID")
    private String yhid;

    @TableField("IS_SERVICE")
    private String isService;


}
