package com.hualu.app.module.basedata.controller;


import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import javax.validation.Valid;
import java.util.List;
import org.assertj.core.util.Lists;
import com.hualu.app.module.basedata.entity.AislandBasicInfo;
import com.hualu.app.module.basedata.mapper.AislandBasicInfoMapper;
import com.hualu.app.module.basedata.service.AislandBasicInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;

/**
 * 人工岛基础信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Api(tags = "AislandBasicInfoController",description = "人工岛基础信息表 前端控制器")
@RestController
@RequestMapping("/aislandBasicInfo")
public class AislandBasicInfoController extends M_MyBatisController<AislandBasicInfo,AislandBasicInfoMapper>{

    @Autowired
    private AislandBasicInfoService service;

    /**
    * 分页查询
    * @return
    */
    @ApiRegister(value = "aislandBasicInfo:page",businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    @Override
    public Object selectPage() {
        return super.selectPage();
    }

    /**
    * 数据查询
    * @return
    */
    @ApiRegister(value = "aislandBasicInfo:list",businessType = BusinessType.OTHER)
    @ApiOperation("数据查询")
    @PostMapping("list")
    public Object list() {
        QueryWrapper queryWrapper = initQueryWrapper(getReqParam());
        return service.list(queryWrapper);
    }

    /**
    * 根据主键查询
    * @return
    */
    @ApiRegister(value = "aislandBasicInfo:getById",businessType = BusinessType.OTHER)
    @ApiOperation("根据主键查询")
    @GetMapping("getById/{id}")
    public Object getById(@PathVariable String id) {
        return service.getById(id);
    }

    /**
    * 添加
    * @param entity
    * @return
    */
    @Override
    @ApiRegister(value = "aislandBasicInfo:save",businessType = BusinessType.INSERT)
    @ApiOperation("添加")
    @PostMapping("save")
    public ApiResult save(@RequestBody @Valid AislandBasicInfo entity) {
        service.save(entity);
        return ApiResult.ok();
    }

    /**
    * 修改
    * @param entity
    * @return
    */
    @Override
    @ApiRegister(value = "aislandBasicInfo:update",businessType = BusinessType.UPDATE)
    @ApiOperation("修改")
    @PostMapping("update")
    public ApiResult update(@RequestBody AislandBasicInfo entity) {
        service.updateById(entity);
        return ApiResult.ok();
    }


    /**
    * 添加或者修改
    * @param entity
    * @return
    */
    @ApiRegister(value = "aislandBasicInfo:saveOrUpdate",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改")
    @PostMapping("saveOrUpdate")
    public ApiResult saveOrUpdate(@RequestBody @Valid AislandBasicInfo entity) {
        service.saveOrUpdate(entity);
        return ApiResult.ok();
    }

    /**
    * 删除
    */
    @ApiRegister(value = "aislandBasicInfo:del",businessType = BusinessType.DELETE)
    @ApiOperation("删除")
    @PostMapping("del")
    public ApiResult delete(@RequestParam(value = "ids[]")List<String> ids){
        service.removeByIds(ids);
        return ApiResult.ok();
    }

    /**
    * 删除
    */
    @ApiRegister(value = "aislandBasicInfo:delIds",businessType = BusinessType.DELETE)
    @ApiOperation("删除（ids为字符串，以逗号分割）")
    @PostMapping("delIds")
    public ApiResult delIds(@RequestParam(value = "ids")String ids){
        String[] split = StringUtils.split(ids, ",");
        List<Long> tempIds = Lists.newArrayList();
        for (String s : split) {
            tempIds.add(Long.valueOf(s));
        }
        service.removeByIds(tempIds);
        return ApiResult.ok();
    }

    /**
    * 批量删除
    */
    @ApiRegister(value = "aislandBasicInfo:delAll",businessType = BusinessType.DELETE)
    @ApiOperation("批量删除")
    @PostMapping("delAll")
    public ApiResult deleteAll(){
        QueryWrapper queryWrapper = initQueryWrapper(getReqParam());
        service.remove(queryWrapper);
        return ApiResult.ok();
    }
}