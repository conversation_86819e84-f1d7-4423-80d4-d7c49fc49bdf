package com.hualu.app.module.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.facility.dto.BridgeTypeDto;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 桥梁行政识别 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface TBrdgBrdgrecogService extends IService<TBrdgBrdgrecog> {

    List<BaseStructDto> selectQf(String structId);

    List<BridgeTypeDto> selectGrantedBrdgTypes(String lineCode);

}
