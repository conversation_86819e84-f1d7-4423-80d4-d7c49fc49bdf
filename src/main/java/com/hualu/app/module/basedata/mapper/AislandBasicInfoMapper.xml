<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.AislandBasicInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.AislandBasicInfo">
        <id column="ISLAND_ID" property="islandId" />
        <result column="ISLAND_NAME" property="islandName" />
        <result column="ISLAND_AREA" property="islandArea" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="OPT_ORG_ID" property="optOrgId" />
        <result column="PRJ_ORG_ID" property="prjOrgId" />
        <result column="GIS_X" property="gisX" />
        <result column="GIS_Y" property="gisY" />
        <result column="START_STAKE_CN" property="startStakeCn" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_CN" property="endStakeCn" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="CENTER_STAKE_CN" property="centerStakeCn" />
        <result column="CENTER_STAKE_NUM" property="centerStakeNum" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ISLAND_ID, ISLAND_NAME, ISLAND_AREA, LINE_CODE, LINE_DIRECT, ROUTE_CODE, OPT_ORG_ID, PRJ_ORG_ID, GIS_X, GIS_Y, START_STAKE_CN, START_STAKE_NUM, END_STAKE_CN, END_STAKE_NUM, CENTER_STAKE_CN, CENTER_STAKE_NUM, CREATE_USER_ID, UPDATE_USER_ID, CREATE_TIME, UPDATE_TIME, DEL_FLAG
    </sql>

</mapper>
