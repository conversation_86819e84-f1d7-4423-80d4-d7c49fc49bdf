package com.hualu.app.module.basedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.basedata.entity.BaseRouteIntrvlLogic;
import com.hualu.app.module.basedata.mapper.BaseRouteIntrvlLogicMapper;
import com.hualu.app.module.basedata.service.BaseRouteIntrvlLogicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 营运路段区间 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@DS("gdgsDs")
@Service
public class BaseRouteIntrvlLogicServiceImpl extends ServiceImpl<BaseRouteIntrvlLogicMapper, BaseRouteIntrvlLogic> implements BaseRouteIntrvlLogicService {

}
