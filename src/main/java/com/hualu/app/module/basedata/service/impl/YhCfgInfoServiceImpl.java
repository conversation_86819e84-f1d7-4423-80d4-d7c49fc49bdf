package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.YhCfgInfoDto;
import com.hualu.app.module.basedata.dto.YhCfgStatVo;
import com.hualu.app.module.basedata.entity.YhCfgInfo;
import com.hualu.app.module.basedata.mapper.YhCfgInfoMapper;
import com.hualu.app.module.basedata.service.YhCfgInfoService;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.FacilityNumDto;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_FutureHelper;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 * 收费路段信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
@DS("gdgsDs")
@Service
public class YhCfgInfoServiceImpl extends ServiceImpl<YhCfgInfoMapper, YhCfgInfo> implements YhCfgInfoService {

    private static List<String> facilityCatList = Lists.newArrayList("QL","SD","BP","HD");

    @Override
    public List<YhCfgStatVo> getYhCfgInfoVo() {
        // 获取当前用户的路段编号集合
        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        //机构管养对应的路段数据
        Map<String, Set<String>> oprtRouteMap = H_DataAuthHelper.selectOprtOrgCodeRouteInfo();

        // 获取收费路段信息
        List<YhCfgInfoDto> yhCfgInfoDtos = getYhCfgInfoDtos(routeCodes);
        Map<String, List<YhCfgInfoDto>> yhCfgMap = yhCfgInfoDtos.stream().collect(Collectors.groupingBy(YhCfgInfoDto::getOprtOrgCode));

        // 获取各管养公司的结构物个数
        Map<String, IFinspStatBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFinspStatBase.class);

        List<CompletableFuture<List<FacilityNumDto>>> futureList = new ArrayList<>();
        beansOfType.values().forEach(bean->{
            CompletableFuture<List<FacilityNumDto>> future = CompletableFuture.supplyAsync(() -> {
                return bean.countNum(routeCodes);
            });
            futureList.add(future);
        });
        List<FacilityNumDto> numDtos = H_FutureHelper.sequenceList(futureList).join();

        List<YhCfgStatVo> statVos = Lists.newArrayList();
        //根据结构物分组
        Map<String, List<FacilityNumDto>> facilityCatMap = numDtos.stream().collect(Collectors.groupingBy(FacilityNumDto::getFacilityCat));
        oprtRouteMap.forEach((k,v)->{
            YhCfgStatVo vo = new YhCfgStatVo();
            vo.setOprtOrgCode(k);
            setFacilityNum(facilityCatMap,v,k,vo);
            List<YhCfgInfoDto> roadDtos = yhCfgMap.get(k);
            if (CollectionUtil.isNotEmpty(roadDtos)){
                vo.setYhCfgInfoDtos(roadDtos);
                vo.setOprtOrgName(roadDtos.get(0).getOprtOrgName());
            }
            statVos.add(vo);
        });
        return statVos;
    }


    /**
     * 设置收费路段轨迹及里程长度
     * @param routeCodes
     * @return
     */
    private List<YhCfgInfoDto> getYhCfgInfoDtos(Set<String> routeCodes){
        List<YhCfgInfoDto> yhCfgInfoDtos = baseMapper.selectYhCfgInfo(routeCodes);

        LambdaQueryWrapper<YhCfgInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(YhCfgInfo::getRoadNo,yhCfgInfoDtos.stream().map(YhCfgInfoDto::getRoadNo).collect(Collectors.toSet()));
        queryWrapper.select(YhCfgInfo::getRoadNo,YhCfgInfo::getRoadPath,YhCfgInfo::getObjectid);
        List<YhCfgInfo> routeDtos = list(queryWrapper);
        Map<String, String> roadMap = routeDtos.stream().collect(Collectors.toMap(YhCfgInfo::getObjectid, YhCfgInfo::getRoadPath));

        for (YhCfgInfoDto cfgInfoDto : yhCfgInfoDtos) {
            String roadPath = roadMap.get(cfgInfoDto.getObjectid());
            cfgInfoDto.setRoadPath(roadPath);

            String startStake = H_StakeHelper.converGlStake(cfgInfoDto.getStartStack());
            String endStake = H_StakeHelper.converGlStake(cfgInfoDto.getEndStack());
            double v = Double.valueOf(endStake).doubleValue() - Double.valueOf(startStake).doubleValue();
            cfgInfoDto.setRouteLength(NumberUtil.round(Math.abs(v),2).doubleValue());
        }
        return yhCfgInfoDtos;
    }

    /**
     * 根据公司，设置结构数量
     * @param facilityCatMap
     * @param routeCodes
     * @param oprtOrgCode
     * @param vo
     * @return
     */
    private void setFacilityNum(Map<String, List<FacilityNumDto>> facilityCatMap, Set<String> routeCodes, String oprtOrgCode, YhCfgStatVo vo) {
        facilityCatList.forEach(fac->{
            List<FacilityNumDto> facilityNumDtos = facilityCatMap.get(fac);
            if (CollectionUtil.isNotEmpty(facilityNumDtos)){
                int sum = facilityNumDtos.stream().filter(e -> routeCodes.contains(e.getRouteCode())).map(FacilityNumDto::getNum)
                        .collect(Collectors.toList()).stream().mapToInt(Integer::intValue).sum();
                ReflectUtil.setFieldValue(vo,fac.toLowerCase()+"Num",sum);
            }
        });
    }
}
