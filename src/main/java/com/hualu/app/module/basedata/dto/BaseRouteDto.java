package com.hualu.app.module.basedata.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class BaseRouteDto {

    private String rpIntrvlId;
    private String routeCode;
    private String routeVersion;
    private Double physicsCntrStake;

    //路段名称
    private String routeName;
    //管养单位
    private String oprtOrgName;

    @JsonIgnore
    private Double startStake;

    @JsonIgnore
    private Double endStake;

    @JsonIgnore
    private Double rpStartStake;

    @JsonIgnore
    private Double rpEndStake;

    @JsonIgnore
    private Double routeLength;

    @JsonIgnore
    private String lineCode;

    @JsonIgnore
    private String oprtOrgCode;

    @JsonIgnore
    private String prjOrgCode;

}
