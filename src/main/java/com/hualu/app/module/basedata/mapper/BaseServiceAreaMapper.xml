<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.BaseServiceAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.BaseServiceArea">
        <id column="SERVICE_ID" property="serviceId" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="SERVICE_CODE" property="serviceCode" />
        <result column="SERVICE_NAME" property="serviceName" />
        <result column="UPDOWN_PSTN" property="updownPstn" />
        <result column="CNTR_STAKE" property="cntrStake" />
        <result column="CNTR_STAKE_NUM" property="cntrStakeNum" />
        <result column="CNTR_SENSSION_NUM" property="cntrSenssionNum" />
        <result column="OPRT_ORG_CODE" property="oprtOrgCode" />
        <result column="REMARK" property="remark" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="GIS_ID" property="gisId" />
        <result column="GIS_X" property="gisX" />
        <result column="GIS_Y" property="gisY" />
        <result column="AREA_ZOME" property="areaZome" />
        <result column="MAINLINE_ID" property="mainlineId" />
        <result column="SERVICE_TYPE" property="serviceType" />
        <result column="START_OFFSET" property="startOffset" />
        <result column="MAIN_RAMP_LINE_ID" property="mainRampLineId" />
        <result column="SERVICE_LOCATION" property="serviceLocation" />
        <result column="PRJ_ORG_CODE" property="prjOrgCode" />
        <result column="THREAD_RPID" property="threadRpid" />
        <result column="LOGIC_CNTR_STAKE" property="logicCntrStake" />
        <result column="LOGIC_CNTR_STAKE_NUM" property="logicCntrStakeNum" />
        <result column="REPORT_CODE" property="reportCode" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_ALLNAME" property="lineAllname" />
        <result column="PHYSICS_CNTR_STAKE" property="physicsCntrStake" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="ROUTE_VERSION" property="routeVersion" />
        <result column="LOGIC_RAMP_CNTR_STAKE" property="logicRampCntrStake" />
        <result column="YHID" property="yhid" />
        <result column="IS_SERVICE" property="isService" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SERVICE_ID, RP_INTRVL_ID, SERVICE_CODE, SERVICE_NAME, UPDOWN_PSTN, CNTR_STAKE, CNTR_STAKE_NUM, CNTR_SENSSION_NUM, OPRT_ORG_CODE, REMARK, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, IS_DELETED, IS_ENABLE, GIS_ID, GIS_X, GIS_Y, AREA_ZOME, MAINLINE_ID, SERVICE_TYPE, START_OFFSET, MAIN_RAMP_LINE_ID, SERVICE_LOCATION, PRJ_ORG_CODE, THREAD_RPID, LOGIC_CNTR_STAKE, LOGIC_CNTR_STAKE_NUM, REPORT_CODE, LINE_CODE, LINE_ALLNAME, PHYSICS_CNTR_STAKE, ROUTE_CODE, ROUTE_VERSION, LOGIC_RAMP_CNTR_STAKE, YHID, IS_SERVICE
    </sql>

</mapper>
