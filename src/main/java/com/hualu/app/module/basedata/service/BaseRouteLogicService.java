package com.hualu.app.module.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 逻辑路段表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface BaseRouteLogicService extends IService<BaseRouteLogic> {

    /**
     * 获取路段信息
     * @param lineCode
     * @return
     */
    BaseRouteLogic getRouteByLineCode(String lineCode);

    List<BaseRouteLogic> getRouteListByLineCode(String lineCode);

    BaseRouteLogic getRouteByRouteCode(String routeCode);

    /**
     * 根据路段编码集合解析对应的路段名称映射
     * @param routeCodeSet 路段编码集合（如：道路ID、行政区划编码等）
     * @return 路段编码与名称的映射关系（Key: 路段编码，Value: 路段名称）
     */
    Map<String, BaseRouteLogic> resolveRouteNamesByCodes(Set<String> routeCodeSet);

    /**
     * 根据路线编码，或者路段编码，查询路段信息
     * @param lineCode
     * @param routeCode
     * @return
     */
    BaseRouteLogic getRouteByLineCodeOrRouteCode(String lineCode, String routeCode);

    List<BaseRouteLogic> listRoad(String lineCode);
}
