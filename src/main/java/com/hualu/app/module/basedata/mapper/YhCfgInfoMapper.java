package com.hualu.app.module.basedata.mapper;

import com.hualu.app.module.basedata.dto.YhCfgInfoDto;
import com.hualu.app.module.basedata.entity.YhCfgInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 收费路段信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-14
 */
public interface YhCfgInfoMapper extends BaseMapper<YhCfgInfo> {

    List<YhCfgInfoDto> selectYhCfgInfo(@Param("routeCodes") Set<String> routeCodes);
}
