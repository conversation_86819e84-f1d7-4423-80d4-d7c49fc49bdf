package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 几何信息及支挡加固防护信息 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HsmsParts对象", description="几何信息及支挡加固防护信息")
public class HsmsParts implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部件ID")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "边坡ID")
    @TableField("SLOPE_ID")
    private String slopeId;

    @ApiModelProperty(value = "主要支档加固形式")
    @TableField("ZD_PARTS_INFO_ID")
    private String zdPartsInfoId;

    @ApiModelProperty(value = "防护措施")
    @TableField("FH_PARTS_INFO_ID")
    private String fhPartsInfoId;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE")
    private Double startStake;

    @ApiModelProperty(value = "终点桩号")
    @TableField("END_STAKE")
    private Double endStake;

    @ApiModelProperty(value = "边坡级数")
    @TableField("SERIES")
    private Long series;

    @ApiModelProperty(value = "支档加固长度")
    @TableField("LENGTH")
    private Double length;

    @ApiModelProperty(value = "边坡坡率")
    @TableField("RATE")
    private Double rate;

    @ApiModelProperty(value = "边坡高度")
    @TableField("HEIGHT")
    private Double height;

    @ApiModelProperty(value = "平台宽度")
    @TableField("PLATFORM_WIDTH")
    private Double platformWidth;

    @ApiModelProperty(value = "支挡加固名称")
    @TableField("ZD_PART_NAME")
    private String zdPartName;

    @ApiModelProperty(value = "防护措施名称")
    @TableField("FH_PART_NAME")
    private String fhPartName;

    @TableField("STAKE")
    private Double stake;


}
