package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.hualu.app.module.basedata.mapper.TClvrtClvrtrecogMapper;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.basedata.service.TClvrtClvrtrecogService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import java.util.Collections;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 涵洞行政识 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@DS("bctcmsDs")
@Service
public class TClvrtClvrtrecogServiceImpl extends ServiceImpl<TClvrtClvrtrecogMapper, TClvrtClvrtrecog> implements TClvrtClvrtrecogService, IBaseStructFace {

    @Override
    public BaseStructDto getId(String structId) {
        TClvrtClvrtrecog clvrtrecog = baseMapper.selectById(structId);
        return initDto(clvrtrecog);
    }

    @Override
    public String getFacilityCat() {
        return "HD";
    }

    @Override
    public Object selectStructPage() {
        Map<String, Object> paramsMap = getStructParamMap("clvrtName","clvrtrecogId");
        Object appLineCode = paramsMap.get("appLineCode");
        Set<String> routeCodes = null;
        if (ObjectUtil.isNotEmpty(appLineCode)){
            routeCodes = H_DataAuthHelper.selectRouteCodeAuthByLineCode(appLineCode.toString());
            paramsMap.remove("lineId");
            paramsMap.remove("lineCode");
        }else {
            routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        }
        QueryWrapper<TClvrtClvrtrecog> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);
        H_BatisQuery.setFieldValue2In(queryWrapper,paramsMap,TClvrtClvrtrecog.class);
        queryWrapper.lambda().orderByAsc(TClvrtClvrtrecog::getLineCode,TClvrtClvrtrecog::getLogicCntrStakeNum);
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();

        if (iPage.getRecords().size() != 0){
            iPage.getRecords().forEach(row->{
                TClvrtClvrtrecog item = (TClvrtClvrtrecog) row;
                BaseStructDto dto = initDto(item);
                dtos.add(dto);
            });
        }
        iPage.setRecords(dtos);
        return returnPage(iPage);
    }

    @Override
    public List<BaseStructDto> listByRouteCodes(Set<String> routeCodes) {
        QueryWrapper<TClvrtClvrtrecog> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);
        queryWrapper.lambda().select(TClvrtClvrtrecog::getClvrtrecogId,TClvrtClvrtrecog::getClvrtName,TClvrtClvrtrecog::getRouteCode)
                .orderByAsc(TClvrtClvrtrecog::getLineCode,TClvrtClvrtrecog::getLogicCntrStakeNum);
        List<TClvrtClvrtrecog> list = baseMapper.selectList(queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();
        list.forEach(row->{
            BaseStructDto dto = new BaseStructDto().setStructId(row.getClvrtrecogId()).setStructName(row.getClvrtName()).setRouteCode(row.getRouteCode());
            dtos.add(dto);
        });
        return dtos;
    }

    private void initWrapper(QueryWrapper<TClvrtClvrtrecog> queryWrapper,Set<String> routeCodes) {
        queryWrapper.eq("VALID_FLAG",1);
        queryWrapper.in(CollectionUtil.isNotEmpty(routeCodes),"route_code",routeCodes);
    }

    @Override
    public List<BaseStructDto> listByStructIds(List<String> structIds) {
        List<TClvrtClvrtrecog> allList = Lists.newArrayList();
        List<List<String>> partition = ListUtil.partition(structIds, 1000);
        partition.forEach(rows->{
            List<TClvrtClvrtrecog> slopeInfoList = lambdaQuery().in(TClvrtClvrtrecog::getClvrtrecogId, rows)
                    .orderByAsc(TClvrtClvrtrecog::getLineCode,TClvrtClvrtrecog::getLogicCntrStakeNum)
                    .list();
            allList.addAll(slopeInfoList);
        });
        List<BaseStructDto> dtos = Lists.newArrayList();
        for (TClvrtClvrtrecog info : allList) {
            BaseStructDto baseStructDto = initDto(info);
            dtos.add(baseStructDto);
        }
        return dtos;
    }

    BaseStructDto initDto(TClvrtClvrtrecog item){
        if (item == null){
            throw new BaseException("未找到结构物");
        }
        BaseStructDto dto = new BaseStructDto();
        dto.setLineId(item.getLineId());
        dto.setStructId(item.getClvrtrecogId());
        dto.setRlCntrStake(item.getLogicCntrStakeNum());
        dto.setStructName(item.getClvrtName()+"("+dto.getDisplayStake()+")");
        dto.setDisplayStake(item.getLogicCntrStake());
        dto.setRpIntrvlId(item.getRpIntrvlId());
        dto.setRampId(item.getMainRampLineId());
        dto.setRouteVersion(item.getRouteCodeVersion());
        dto.setRouteCode(item.getRouteCode());
        dto.setX(item.getLongitude());
        dto.setY(item.getLatitude());
        dto.setOprtOrgCode(item.getOprtOrgCode());
        return dto;
    }

    @Override public List<TClvrtClvrtrecog> getCulvertTypeAndPlace(List<String> culvertIds) {

        List<TClvrtClvrtrecog> allList = Lists.newArrayList();
        ListUtil.partition(culvertIds,500).forEach(items->{
            allList.addAll(getBaseMapper().getCulvertTypeAndPlace(items));
        });
        return allList;
    }
}
