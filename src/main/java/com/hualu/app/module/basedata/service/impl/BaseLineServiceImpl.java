package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.StakeRangeDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.mapper.BaseLineMapper;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 路线 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@DS("gdgsDs")
@Service
public class BaseLineServiceImpl extends ServiceImpl<BaseLineMapper, BaseLine> implements BaseLineService {

    @Override
    public BaseLine selectLine(String lineId) {
        BaseLine line = baseMapper.selectLine(lineId, "1");
        if (line == null){
            line = baseMapper.selectLine(lineId,null);
        }
        return line;
    }

    @Override
    public List<BaseLine> listAllLines() {
        LambdaQueryWrapper<BaseLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseLine::getIsNewGgw,1)
                .eq(BaseLine::getIsDeleted,0)
                .eq(BaseLine::getIsEnable,1);
        queryWrapper.select(BaseLine::getLineId,BaseLine::getLineCode,BaseLine::getLineAllname,BaseLine::getLineSname);
        return list(queryWrapper);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public Map<String, BaseLine> getAllLineMap() {
        String key = "pt:baseline";
        synchronized (key){
            List<BaseLine> baseLines = (List<BaseLine>) CustomRequestContextHolder.get(key);
            if (CollectionUtil.isEmpty(baseLines)){
                baseLines = listAllLines();
                CustomRequestContextHolder.set(key,baseLines);
            }
            Map<String, BaseLine> lineMap = baseLines.stream().collect(Collectors.toMap(BaseLine::getLineId, Function.identity()));
            Map<String, BaseLine> codeMap = baseLines.stream().filter(e -> StrUtil.isNotBlank(e.getLineCode())).collect(Collectors.toMap(BaseLine::getLineCode, Function.identity()));
            codeMap.forEach((k,v)->{
                lineMap.put(k,v);
            });
            return lineMap;
        }
    }

    @Override
    public List<BaseLine> selectGrantLines() {
        LambdaQueryWrapper<BaseLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseLine::getIsNewGgw,1).eq(BaseLine::getIsDeleted,0).eq(BaseLine::getIsEnable,1);
        queryWrapper.in(BaseLine::getLineId, H_DataAuthHelper.selectGrantLineIds());
        queryWrapper.orderByAsc(BaseLine::getLineCode);
        List<BaseLine> lines = list(queryWrapper);
        lines.forEach(item->{
            item.setLineAllname(item.getLineAllname()+"("+item.getLineCode()+")");
        });
        return lines;
    }

    @Override public StakeRangeDto getStakeRange(String lineId, String lineDirect) {
        String userCode = CustomRequestContextHolder.getUserCode();
        StakeRangeDto rangeDto;
        if (StrUtil.isBlank(lineDirect)){
            rangeDto = getBaseMapper().getStakeRange(lineId, null, userCode);
            return rangeDto;
        }
        switch (lineDirect) {
            case "4": {
                rangeDto = getBaseMapper().getRampStakeRange(lineId, lineDirect, userCode);
                break;
            }
            default:{
                //当方向不为上下行时，方向设置为空
                if (!("1".equals(lineDirect) || "2".equals(lineDirect))){
                    lineDirect = null;
                }
                rangeDto = getBaseMapper().getStakeRange(lineId, lineDirect, userCode);
                break;
            }
        }

        return rangeDto;
    }

    @Override
    public String getLineName(String lineCode) {
        if (StrUtil.isBlank(lineCode)) {
            return null;
        }
        LambdaQueryWrapper<BaseLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseLine::getIsNewGgw, 1)
                .eq(BaseLine::getIsDeleted, 0)
                .eq(BaseLine::getIsEnable, 1)
                .and(e -> e.eq(BaseLine::getLineCode, lineCode).or().eq(BaseLine::getLineId, lineCode));
        BaseLine one = getOne(queryWrapper, false);
        if (one != null) {
            return one.getLineAllname();
        }
        return null;
    }

    @Override
    public Map<String, String> resolveLineNamesByCodes(Set<String> codes) {
        List<BaseLine> lineList = lambdaQuery().select(BaseLine::getLineCode, BaseLine::getLineAllname)
                .in(BaseLine::getLineCode, codes)
                .eq(BaseLine::getIsNewGgw, 1)
                .eq(BaseLine::getIsDeleted, 0)
                .eq(BaseLine::getIsEnable, 1).list();
        Map<String, String> lineMap = lineList.stream().collect(Collectors.toMap(BaseLine::getLineCode, BaseLine::getLineAllname));
        return lineMap;
    }


}
