package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 养护路段与收费路段关联关系 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-09-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="YhCfgHlyhRela对象", description="养护路段与收费路段关联关系")
public class YhCfgHlyhRela implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收费路段ID")
    @TableField("SECTIONID")
    private String sectionid;

    @ApiModelProperty(value = "收费路段名称")
    @TableField("SECTIONNAME")
    private String sectionname;

    @ApiModelProperty(value = "二级单位编码")
    @TableField("SE_UNIT_CODE")
    private String seUnitCode;

    @ApiModelProperty(value = "二级单位名称")
    @TableField("SE_UNIT_NAME")
    private String seUnitName;

    @ApiModelProperty(value = "片区编码")
    @TableField("AREACODE")
    private String areacode;

    @ApiModelProperty(value = "片区名称")
    @TableField("AREANAME")
    private String areaname;

    @ApiModelProperty(value = "是否集团内")
    @TableField("IS_GROUP")
    private String isGroup;

    @ApiModelProperty(value = "养护路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "路段方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;


}
