package com.hualu.app.module.basedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.basedata.entity.HsmsSlopePic;
import com.hualu.app.module.basedata.mapper.HsmsSlopePicMapper;
import com.hualu.app.module.basedata.service.HsmsSlopePicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 边坡图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@DS("hsmsDs")
@Service
public class HsmsSlopePicServiceImpl extends ServiceImpl<HsmsSlopePicMapper, HsmsSlopePic> implements HsmsSlopePicService {

    @Override
    public String getPic(String slopeId) {
        LambdaQueryWrapper<HsmsSlopePic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HsmsSlopePic::getSlopeId, slopeId);
        queryWrapper.eq(HsmsSlopePic::getType,1);
        queryWrapper.apply("rownum <= 1");
        HsmsSlopePic one = getOne(queryWrapper);
        if ( one != null ) {
            return one.getFileEntityId();
        }
        return "";
    }
}
