package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 边坡基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="HsmsSlopeInfo对象", description="边坡基础信息表")
public class HsmsSlopeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "边坡主键")
    @TableId("SLOPE_ID")
    private String slopeId;

    @ApiModelProperty(value = "边坡类型")
    @TableField("SLOPE_TYPE")
    private String slopeType;

    @ApiModelProperty(value = "边坡编码")
    @TableField("SLOPE_CODE")
    private String slopeCode;

    @ApiModelProperty(value = "边坡名称")
    @TableField("SLOPE_NAME")
    private String slopeName;

    @ApiModelProperty(value = "养护等级")
    @TableField("MAINTAIN_SERIES")
    private String maintainSeries;

    @ApiModelProperty(value = "是否重点")
    @TableField("IS_IMPORTANT")
    private String isImportant;

    @ApiModelProperty(value = "边坡位置")
    @TableField("SLOPE_POSITION")
    private String slopePosition;

    @ApiModelProperty(value = "边坡坡率")
    @TableField("SLOPE_RATE")
    private String slopeRate;

    @ApiModelProperty(value = "边坡坡级")
    @TableField("SLOPE_LEVEL")
    private Long slopeLevel;

    @ApiModelProperty(value = "边坡长度")
    @TableField("SLOPE_LENGTH")
    private Double slopeLength;

    @ApiModelProperty(value = "路线ID")
    @TableField("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "路线方向")
    @TableField("LINE_DIRECTION")
    private String lineDirection;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE_OLD")
    private Double startStakeOld;

    @ApiModelProperty(value = "终点桩号")
    @TableField("END_STAKE_OLD")
    private Double endStakeOld;

    @ApiModelProperty(value = "主要支档加固形式")
    @TableField("SLOPE_RETAINING_AND_REINFORCE")
    private String slopeRetainingAndReinforce;

    @ApiModelProperty(value = "竣工时间")
    @TableField("COMPLETION_TIME")
    private String completionTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "修改人")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "X坐标")
    @TableField("GIS_X")
    private Double gisX;

    @ApiModelProperty(value = "Y坐标")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "百度X坐标")
    @TableField("BD_GIS_X")
    private Double bdGisX;

    @ApiModelProperty(value = "百度Y坐标")
    @TableField("BD_GIS_Y")
    private Double bdGisY;

    @ApiModelProperty(value = "管养公司")
    @TableField("OPT_ORG_ID")
    private String optOrgId;

    @ApiModelProperty(value = "营运公司")
    @TableField("PRJ_ORG_ID")
    private String prjOrgId;

    @ApiModelProperty(value = "是否主线（0是，1不是）")
    @TableField("IS_MAIN_LINE")
    private String isMainLine;

    @ApiModelProperty(value = "营运区间ID")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE_NUM_OLD")
    private Double cntrStakeNumOld;

    @ApiModelProperty(value = "评定日期")
    @TableField("EVALUATE_DATE")
    private LocalDateTime evaluateDate;

    @ApiModelProperty(value = "评定单位")
    @TableField("EVALUATION_UNIT")
    private String evaluationUnit;

    @ApiModelProperty(value = "检测项目ID")
    @TableField("PRJ_ID")
    private String prjId;

    @ApiModelProperty(value = "边坡评定等级")
    @TableField("SLOPE_TC_GRADE")
    private String slopeTcGrade;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET_OLD")
    private Double startOffsetOld;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "主线桩号对应的主线路段区间ID")
    @TableField("THREAD_RPID")
    private String threadRpid;

    @ApiModelProperty(value = "起点x坐标")
    @TableField("GIS_SX")
    private Double gisSx;

    @ApiModelProperty(value = "起点y坐标")
    @TableField("GIS_SY")
    private Double gisSy;

    @ApiModelProperty(value = "止点x坐标")
    @TableField("GIS_EX")
    private Double gisEx;

    @ApiModelProperty(value = "止点y坐标")
    @TableField("GIS_EY")
    private Double gisEy;

    @ApiModelProperty(value = "起点营运区间id")
    @TableField("START_RP_INTRVL_ID")
    private String startRpIntrvlId;

    @ApiModelProperty(value = "止点营运区间id")
    @TableField("END_RP_INTRVL_ID")
    private String endRpIntrvlId;

    @ApiModelProperty(value = "是否有检修道（1有，2无）")
    @TableField("ROADWAY")
    private String roadway;

    @ApiModelProperty(value = "是否定检边坡（1是，0否）")
    @TableField("IS_SPECIAL_SLOPE")
    private String isSpecialSlope;

    @ApiModelProperty(value = "设计起点桩号")
    @TableField("DESIGN_START_STAKE")
    private Double designStartStake;

    @ApiModelProperty(value = "设计终点桩号")
    @TableField("DESIGN_END_STAKE")
    private Double designEndStake;

    @ApiModelProperty(value = "重点监测坡")
    @TableField("RED_SLOPE")
    private String redSlope;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "物理中心桩号值")
    @TableField("PHYSICS_CNTR_STAKE")
    private Double physicsCntrStake;

    @ApiModelProperty(value = "逻辑中心桩号值")
    @TableField("LOGIC_CNTR_STAKE")
    private Double logicCntrStake;

    @ApiModelProperty(value = "物理路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "路线版本")
    @TableField("ROUTE_VERSION")
    private String routeVersion;

    @ApiModelProperty(value = "逻辑匝道中心桩号")
    @TableField("LOGIC_RAMP_CNTR_STAKE")
    private Double logicRampCntrStake;

    @ApiModelProperty(value = "逻辑终点桩号")
    @TableField("LOGIC_END_STAKE")
    private Double logicEndStake;

    @ApiModelProperty(value = "逻辑起点桩号")
    @TableField("LOGIC_START_STAKE")
    private Double logicStartStake;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE")
    private Double startStake;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE")
    private Double endStake;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE_NUM")
    private Double cntrStakeNum;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "逻辑起点桩号(文本)")
    @TableField("LOGIC_END_STRSTAKE")
    private String logicEndStrstake;

    @ApiModelProperty(value = "逻辑止点桩号(文本)")
    @TableField("LOGIC_START_STRSTAKE")
    private String logicStartStrstake;

    @ApiModelProperty(value = "中心桩号(文本)")
    @TableField("LOGIC_CNTR_STRSTAKE")
    private String logicCntrStrstake;

    @ApiModelProperty(value = "设计起点桩号(文本)")
    @TableField("DESIGN_START_STRSTAKE")
    private String designStartStrstake;

    @ApiModelProperty(value = "设计终点桩号(文本)")
    @TableField("DESIGN_END_STRSTAKE")
    private String designEndStrstake;

    @ApiModelProperty(value = "行政区划")
    @TableField("PLACE")
    private String place;


}
