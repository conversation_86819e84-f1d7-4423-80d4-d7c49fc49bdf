package com.hualu.app.module.basedata.dto.near;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 附件结构物统计
 */
@Accessors(chain = true)
@Data
public class BaseNearStructStatDto implements Serializable {

    private List<String> structIds;

    /**
     * 已检查结构物数量
     */
    private Integer checkedNum;

    /**
     * 未检查结构物数量
     */
    private Integer notCheckedNum;
}
