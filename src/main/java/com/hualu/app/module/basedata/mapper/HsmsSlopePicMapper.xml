<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.HsmsSlopePicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.HsmsSlopePic">
        <id column="ID" property="id" />
        <result column="SLOPE_ID" property="slopeId" />
        <result column="TYPE" property="type" />
        <result column="FILE_ENTITY_ID" property="fileEntityId" />
        <result column="UPLOAD_DATE" property="uploadDate" />
        <result column="FILE_NAME" property="fileName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SLOPE_ID, TYPE, FILE_ENTITY_ID, UPLOAD_DATE, FILE_NAME
    </sql>

</mapper>
