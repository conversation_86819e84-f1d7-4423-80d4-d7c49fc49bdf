<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.HsmsPartsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.HsmsParts">
        <id column="ID" property="id" />
        <result column="SLOPE_ID" property="slopeId" />
        <result column="ZD_PARTS_INFO_ID" property="zdPartsInfoId" />
        <result column="FH_PARTS_INFO_ID" property="fhPartsInfoId" />
        <result column="START_STAKE" property="startStake" />
        <result column="END_STAKE" property="endStake" />
        <result column="SERIES" property="series" />
        <result column="LENGTH" property="length" />
        <result column="RATE" property="rate" />
        <result column="HEIGHT" property="height" />
        <result column="PLATFORM_WIDTH" property="platformWidth" />
        <result column="ZD_PART_NAME" property="zdPartName" />
        <result column="FH_PART_NAME" property="fhPartName" />
        <result column="STAKE" property="stake" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, SLOPE_ID, ZD_PARTS_INFO_ID, FH_PARTS_INFO_ID, START_STAKE, END_STAKE, SERIES, LENGTH, RATE, HEIGHT, PLATFORM_WIDTH, ZD_PART_NAME, FH_PART_NAME, STAKE
    </sql>

</mapper>
