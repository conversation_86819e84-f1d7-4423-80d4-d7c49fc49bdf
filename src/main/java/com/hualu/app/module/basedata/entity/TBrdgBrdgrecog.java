package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 桥梁行政识别
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TBrdgBrdgrecog对象", description="桥梁行政识别")
public class TBrdgBrdgrecog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "桥梁行政识别ID")
    @TableId("BRDGRECOG_ID")
    private String brdgrecogId;

    @ApiModelProperty(value = "路线编号")
    @TableField("ROAD_NUM")
    private String roadNum;

    @ApiModelProperty(value = "路线名称")
    @TableField("ROAD_NAME")
    private String roadName;

    @ApiModelProperty(value = "路线等级")
    @TableField("ROUTE_LVL")
    private Integer routeLvl;

    @ApiModelProperty(value = "顺序号")
    @TableField("SEQ_NO")
    private Integer seqNo;

    @ApiModelProperty(value = "旧国高网桩号")
    @TableField("OLD_HIGHWAY_STAKE")
    private String oldHighwayStake;

    @ApiModelProperty(value = "功能类型")
    @TableField("FUNC_TYPE")
    private Integer funcType;

    @ApiModelProperty(value = "施工桩号")
    @TableField("CNSTRCT_STAKE")
    private String cnstrctStake;

    @ApiModelProperty(value = "设计桩号")
    @TableField("DESIGN_STAKE")
    private String designStake;

    @ApiModelProperty(value = "桥梁幅数")
    @TableField("BRDG_NUM")
    private Integer brdgNum;

    @ApiModelProperty(value = "桥梁性质")
    @TableField("BRDG_NATURE")
    private String brdgNature;

    @ApiModelProperty(value = "经度")
    @TableField("LONGITUDE")
    private Double longitude;

    @ApiModelProperty(value = "跨越地物类型")
    @TableField("ACROSS_FTR_TYPE")
    private String acrossFtrType;

    @ApiModelProperty(value = "桥跨组合")
    @TableField("BRDG_SPAN_GROUP")
    private String brdgSpanGroup;

    @ApiModelProperty(value = "桥梁状态")
    @TableField("BRDG_STATUS")
    private String brdgStatus;

    @ApiModelProperty(value = "纬度")
    @TableField("LATITUDE")
    private Double latitude;

    @ApiModelProperty(value = "跨越地物名称")
    @TableField("ACROSS_FTR_NAME")
    private String acrossFtrName;

    @ApiModelProperty(value = "下穿通道(名称)")
    @TableField("PASSAGE_NAME")
    private String passageName;

    @ApiModelProperty(value = "下穿通道桩号")
    @TableField("PASSAGE_NUM")
    private String passageNum;

    @ApiModelProperty(value = "设计荷载")
    @TableField("DSGN_LOAD")
    private String dsgnLoad;

    @ApiModelProperty(value = "通行载重")
    @TableField("TRAFFIC_LOAD")
    private String trafficLoad;

    @ApiModelProperty(value = "弯斜坡度")
    @TableField("BEND_SLOPE")
    private String bendSlope;

    @ApiModelProperty(value = "桥面铺装")
    @TableField("BRDG_DECK")
    private String brdgDeck;

    @ApiModelProperty(value = "次要路线")
    @TableField("SEC_ROUTE")
    private String secRoute;

    @ApiModelProperty(value = "建成时间")
    @TableField("COMP_TIME")
    private LocalDateTime compTime;

    @ApiModelProperty(value = "设计单位")
    @TableField("DSGN_ORG")
    private String dsgnOrg;

    @ApiModelProperty(value = "施工单位")
    @TableField("CNSTRCT_ORG")
    private String cnstrctOrg;

    @ApiModelProperty(value = "监理单位")
    @TableField("SUPER_ORG")
    private String superOrg;

    @ApiModelProperty(value = "养护单位")
    @TableField("CBMS_ORG")
    private String cbmsOrg;

    @ApiModelProperty(value = "监管单位")
    @TableField("REGULATORY_ORG")
    private String regulatoryOrg;

    @ApiModelProperty(value = "桥梁评定等级")
    @TableField("BRDG_RATING")
    private Integer brdgRating;

    @ApiModelProperty(value = "评价日期")
    @TableField("APPR_TIME")
    private LocalDateTime apprTime;

    @ApiModelProperty(value = "桥梁技术评定ID")
    @TableField("RATING_ID")
    private String ratingId;

    @ApiModelProperty(value = "1.有效 2.无效")
    @TableField("VALID_FLAG")
    private Integer validFlag;

    @ApiModelProperty(value = "营运路段区间编码")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "桥梁编码")
    @TableField("BRDG_CODE")
    private String brdgCode;

    @ApiModelProperty(value = "桥梁名称")
    @TableField("BRDG_NAME")
    private String brdgName;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE")
    private String cntrStake;

    @ApiModelProperty(value = "中心桩号值")
    @TableField("CNTR_STAKE_NUM")
    private Double cntrStakeNum;

    @ApiModelProperty(value = "中心桩号链值")
    @TableField("CNTR_SENSSION_NUM")
    private Double cntrSenssionNum;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_CODE", fill = FieldFill.INSERT)
    private String createUserCode;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    @TableField(value = "UPDATE_USER_CODE", fill = FieldFill.INSERT_UPDATE)
    private String updateUserCode;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "线路ID")
    @TableField("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "所在地")
    @TableField("PLACE")
    private String place;

    @ApiModelProperty(value = "路线类型")
    @TableField("BRDG_LINE_TYPE")
    private String brdgLineType;

    @ApiModelProperty(value = "桥梁幅号")
    @TableField("FRAME_NUM")
    private String frameNum;

    @ApiModelProperty(value = "路基形式")
    @TableField("ROAD_TYPE")
    private Integer roadType;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "交通部编号")
    @TableField("TRAFFIC_CODE")
    private String trafficCode;

    @ApiModelProperty(value = "箱室类型")
    @TableField("BOXROOM_TYPE")
    private String boxroomType;

    @ApiModelProperty(value = "箱室类型的存储值")
    @TableField("BOXROOM_TYPE_VALUE")
    private String boxroomTypeValue;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "匝道桩号值")
    @TableField("RAMP_STAKE")
    private String rampStake;

    @ApiModelProperty(value = "0未处理，1已处理")
    @TableField("EXCEL_RESULT")
    private Integer excelResult;

    @ApiModelProperty(value = "国高网起点桩号")
    @TableField("START_STAKE")
    private Double startStake;

    @ApiModelProperty(value = "国高网止点桩号")
    @TableField("END_STAKE")
    private Double endStake;

    @TableField("LAST_PRJ_ID")
    private String lastPrjId;

    @ApiModelProperty(value = "是否属于长大桥梁目录")
    @TableField("IN_CATALOGUE")
    private Integer inCatalogue;

    @ApiModelProperty(value = "逻辑桩号（显示）")
    @TableField("LOGIC_CNTR_STAKE")
    private String logicCntrStake;

    @ApiModelProperty(value = "逻辑桩号值")
    @TableField("LOGIC_CNTR_STAKE_NUM")
    private Double logicCntrStakeNum;

    @ApiModelProperty(value = "管养单位名称")
    @TableField("OPRT_ORG_NAME")
    private String oprtOrgName;

    @ApiModelProperty(value = "主桥ID")
    @TableField("MAIN_ID")
    private String mainId;

    @ApiModelProperty(value = "主桥名称")
    @TableField("MAIN_NAME")
    private String mainName;

    @ApiModelProperty(value = "高德经度")
    @TableField("JD")
    private Double jd;

    @ApiModelProperty(value = "高德纬度")
    @TableField("WD")
    private Double wd;

    @TableField("SSF_SET")
    private String ssfSet;

    @TableField("NEW_ID")
    private String newId;

    @ApiModelProperty(value = "关联公路局系统主键")
    @TableField("YHID")
    private String yhid;

    @TableField("GRADE_2020")
    private Double grade2020;

    @TableField("GRADE_2019")
    private Double grade2019;

    @TableField("GRADE_2018")
    private Double grade2018;

    @TableField("GRADE_2017")
    private Double grade2017;

    @TableField("GRADE_2016")
    private Double grade2016;

    @TableField("BRDG_TYPE")
    private String brdgType;

    @ApiModelProperty(value = "项目公司编码")
    @TableField("PRJ_CODE")
    private String prjCode;

    @ApiModelProperty(value = "项目公司名称")
    @TableField("PRJ_NAME")
    private String prjName;

    @ApiModelProperty(value = "原桥梁编码")
    @TableField("OLD_BRDGCODE")
    private String oldBrdgcode;

    @ApiModelProperty(value = "是否宽路窄桥[{ text: '是', value: '1' },{ text: '否', value: '2' }]")
    @TableField("KUANLZQ")
    private Double kuanlzq;

    @ApiModelProperty(value = "收费性质[{ text: '非收费', value: '1' },{ text: '还贷', value: '2' },{ text: '经营', value: '3' }]")
    @TableField("SFXZDM")
    private Double sfxzdm;

    @ApiModelProperty(value = "弯坡斜特征[{ text: '常规桥', value: '0' },{ text: '弯桥', value: '1' },{ text: '坡桥', value: '2' },{ text: '斜桥', value: '3' },{ text: '弯坡桥', value: '4' },{ text: '弯斜桥', value: '5' },{ text: '坡斜桥', value: '6' },{ text: '弯坡斜桥', value: '7' },{ text: '其它桥', value: '9' }]")
    @TableField("F039")
    private Double f039;

    @ApiModelProperty(value = "改建性质[{ text: '新建', value: '1' },{ text: '中修', value: '2' },{ text: '大修', value: '3' },{ text: '改建', value: '4' },{ text: '重建', value: '5' },{ text: '小修', value: '6' },{ text: '其他', value: '9' }]")
    @TableField("GJXZDM")
    private Double gjxzdm;

    @ApiModelProperty(value = "管养单位性质")
    @TableField("F042")
    private Double f042;

    @ApiModelProperty(value = "是否部补助项目[{ text: '是', value: '1' },{ text: '否', value: '2' }]")
    @TableField("BZXM")
    private Double bzxm;

    @ApiModelProperty(value = "工程性质[{ text: '中修', value: '2' },{ text: '大修', value: '3' },{ text: '改建', value: '4' },{ text: '重建', value: '5' },{ text: '修复养护', value: '6' },{ text: '专项养护', value: '7' },{ text: '应急养护', value: '8' }]")
    @TableField("GCXZDM")
    private Double gcxzdm;

    @ApiModelProperty(value = "改造部位[{ text: '桥面系', value: '1' },{ text: '支座', value: '2' },{ text: '上部承重结构', value: '3' },{ text: '桥墩、桥台', value: '4' },{ text: '基础', value: '5' },{ text: '其他', value: '9' }]")
    @TableField("F099")
    private String f099;

    @ApiModelProperty(value = "墩台防撞设施类型[{ text: '无防护', value: '0' },{ text: '软防护', value: '1' },{ text: '硬防护', value: '2' }]")
    @TableField("F103")
    private Double f103;

    @ApiModelProperty(value = "互通类型[{ text: '非互通', value: '1' },{ text: '一般互通', value: '2' },{ text: '枢纽互通', value: '3' }]")
    @TableField("F107")
    private Double f107;

    @ApiModelProperty(value = "互通形式[{ text: '单喇叭', value: '1' },{ text: '苜蓿叶式', value: '2' },{ text: '双喇叭', value: '3' },{ text: '棱形简易', value: '4' },{ text: '其它', value: '5' }]")
    @TableField("F109")
    private Double f109;

    @ApiModelProperty(value = "互通交叉方式[{ text: '上跨', value: '1' },{ text: '下穿', value: '2' },{ text: '平面交叉', value: '3' }]")
    @TableField("F105")
    private Double f105;

    @ApiModelProperty(value = "是否含有三类构件，1是0否")
    @TableField("HAS_THREE_PART")
    private Double hasThreePart;

    @ApiModelProperty(value = "最近检测日期")
    @TableField("LAST_PRJ_DATE")
    private LocalDateTime lastPrjDate;

    @ApiModelProperty(value = "Water source bridge是否水源桥[{ text: '是', value: '0' },{ text: '否', value: '1' }]")
    @TableField("WSG")
    private Double wsg;

    @ApiModelProperty(value = "桥梁移交或者废弃时间")
    @TableField("TRANS_TIME")
    private LocalDateTime transTime;

    @ApiModelProperty(value = "桥梁移交或者废弃原因")
    @TableField("TRANS_REMARK")
    private String transRemark;

    @ApiModelProperty(value = "桥梁移交或者废弃依据")
    @TableField("TRANS_FILE")
    private String transFile;

    @ApiModelProperty(value = "最近一次竣工日期")
    @TableField("LAST_DISP_COMP_DATE")
    private LocalDateTime lastDispCompDate;

    @ApiModelProperty(value = "最近一次竣工记录")
    @TableField("LAST_DISP_ID")
    private String lastDispId;

    @ApiModelProperty(value = "逻辑路线版本")
    @TableField("ROUTE_CODE_VERSION")
    private String routeCodeVersion;

    @ApiModelProperty(value = "路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "桥梁编码备份")
    @TableField("BRDG_CODE_BAK")
    private String brdgCodeBak;

    @ApiModelProperty(value = "新版路网物理桩号")
    @TableField("PHYSICS_CNTR_STAKE_NUM_NEW")
    private Double physicsCntrStakeNumNew;

    @ApiModelProperty(value = "是否加宽桥  0是,1否")
    @TableField("WIDEN_BRIDGE")
    private Integer widenBridge;

    @ApiModelProperty(value = "桥梁平曲线半径")
    @TableField("RADIUS")
    private Double radius;

    @ApiModelProperty(value = "是否有健康系统")
    @TableField("HAS_HLA")
    private Integer hasHla;

    @ApiModelProperty(value = "是否独柱墩")
    @TableField("HAS_SCP")
    private Double hasScp;

    @TableField("ID")
    private String id;

    @TableField("ROUTE_NAME")
    private String routeName;

    @TableField("HLA_DESC")
    private String hlaDesc;

    @ApiModelProperty(value = "是否有结构监测，1是")
    @TableField("JGJC")
    private Double jgjc;

    @ApiModelProperty(value = "船撞 1是")
    @TableField("CZJC")
    private Double czjc;

    @ApiModelProperty(value = "荷载监测1是")
    @TableField("HZJC")
    private Double hzjc;

    @ApiModelProperty(value = "挠度预警")
    @TableField("UNDERLOADWARN")
    private Integer underloadwarn;

    @ApiModelProperty(value = "湿度预警")
    @TableField("HUMIDITYWARN")
    private Integer humiditywarn;

    @ApiModelProperty(value = "应变预警")
    @TableField("STRAINWARN")
    private Integer strainwarn;

    @ApiModelProperty(value = "加速度预警")
    @TableField("ACCELERATIONWARN")
    private Integer accelerationwarn;

    @TableField("ROUTE_CODE_BAK")
    private String routeCodeBak;

    @ApiModelProperty(value = "删除时间")
    @TableField("DEL_TIME")
    private LocalDateTime delTime;

    @ApiModelProperty(value = "删除人")
    @TableField("DEL_PERSON")
    private String delPerson;

    @ApiModelProperty(value = "恢复时间")
    @TableField("RECOVER_TIME")
    private LocalDateTime recoverTime;

    @ApiModelProperty(value = "恢复人")
    @TableField("RECOVER_PERSON")
    private String recoverPerson;

    @ApiModelProperty(value = "报新桥数据")
    @TableField("XQ_CODE")
    private String xqCode;

    @ApiModelProperty(value = "桥梁编码备份")
    @TableField("BRDG_CODE_20210911")
    private String brdgCode20210911;

    @ApiModelProperty(value = "行车方向 ")
    @TableField("DRIVING_DIRECTION")
    private String drivingDirection;

    @ApiModelProperty(value = "桥梁编号备份")
    @TableField("BRDG_CODE_20210914")
    private String brdgCode20210914;

    @TableField("BRDG_CODE_20210915")
    private String brdgCode20210915;

    @TableField("BW")
    private Double bw;

    @ApiModelProperty(value = "桥梁监测系统中的序号")
    @TableField("JC_CODE")
    private String jcCode;

    @ApiModelProperty(value = "上报的路线编号,一些共线的，如S32,G228")
    @TableField("REPORT_ROAD_NUM")
    private String reportRoadNum;

    @ApiModelProperty(value = "纬度备份")
    @TableField("LATITUE_BAK")
    private Double latitueBak;

    @ApiModelProperty(value = "经度备份")
    @TableField("LONGTITUE_BAK")
    private Double longtitueBak;

    @ApiModelProperty(value = "桥梁是否跨省 0 是 1否")
    @TableField("TRANS_PROVINCIAL")
    private Double transProvincial;

    @TableField("PLACE_BAK")
    private String placeBak;

    @TableField("OLD_YHID")
    private String oldYhid;

    @TableField("DATA_INTEGRITY")
    private Double dataIntegrity;

    @TableField("NEVER_DJ")
    private Double neverDj;

    @TableField("NEVER_DJ_3")
    private Double neverDj3;

    @TableField("TRAFFIC_CODE_BAK")
    private String trafficCodeBak;

    @TableField("TRAFFIC_CODE_BAK_20220120")
    private String trafficCodeBak20220120;

    @TableField("TRAFFIC_CODE_BAK_20220221")
    private String trafficCodeBak20220221;

    @ApiModelProperty(value = "桥台是否浆砌片石")
    @TableField("MORTARRUBBLE")
    private Double mortarrubble;

    @ApiModelProperty(value = "桥台加固形式")
    @TableField("REINFORCEMENTFORM")
    private String reinforcementform;

    @ApiModelProperty(value = "桥台加固形式补充")
    @TableField("REINFORCEMENTFORMEXTRA")
    private String reinforcementformextra;

    @ApiModelProperty(value = "栏杆护栏材料")
    @TableField("LGHL_MTRL")
    private Integer lghlMtrl;

    @ApiModelProperty(value = "养护等级")
    @TableField("MAINTAIN_GRADE")
    private Integer maintainGrade;

    @ApiModelProperty(value = "跨径小于40的孔数")
    @TableField("XIAOYU40")
    private Double xiaoyu40;

    @ApiModelProperty(value = "跨径小于100的孔数")
    @TableField("XIAOYU100")
    private Double xiaoyu100;

    @ApiModelProperty(value = "跨径大于等于100的孔数")
    @TableField("DAYUDENGYU100")
    private Double dayudengyu100;

    @ApiModelProperty(value = "是否有档案")
    @TableField("HASAV")
    private String hasav;

    @ApiModelProperty(value = " 桥梁工程师")
    @TableField("BSE")
    private String bse;

    @ApiModelProperty(value = "定检单位")
    @TableField("INSPECT_COMPANY")
    private String inspectCompany;

    @TableField("IS_MAIN")
    private Double isMain;

    @ApiModelProperty(value = "原桥梁编码")
    @TableField("OLD_ROADBM")
    private String oldRoadbm;

    @ApiModelProperty(value = "1是需处理 2处理完毕 空是未处理或不处理")
    @TableField("JWDGX")
    private Double jwdgx;

    @TableField("JWDGXSJ")
    private String jwdgxsj;

    @ApiModelProperty(value = "GIS库经度")
    @TableField("JD_GIS")
    private Double jdGis;

    @ApiModelProperty(value = "纬度GIS库")
    @TableField("WD_GIS")
    private Double wdGis;

    @ApiModelProperty(value = "桩号计算的经度")
    @TableField("JD_STAKE")
    private Double jdStake;

    @ApiModelProperty(value = "桩号计算的纬度")
    @TableField("WD_STAKE")
    private Double wdStake;

    @ApiModelProperty(value = "gis与桥梁子系统差距")
    @TableField("DIS_XT")
    private Double disXt;

    @ApiModelProperty(value = "gis与现算的差距")
    @TableField("DIS_STAKE")
    private Double disStake;

    @ApiModelProperty(value = "给新桥的行政区划")
    @TableField("XQ_AREA_CODE")
    private String xqAreaCode;

    @TableField("OLD_ROADBM_20221128")
    private String oldRoadbm20221128;

    @ApiModelProperty(value = "年报桥梁编码备份")
    @TableField("TRAFFIC_CODE_20221213")
    private String trafficCode20221213;

    public String getBrdgrecogId() {
        return brdgrecogId;
    }

    public void setBrdgrecogId(String brdgrecogId) {
        this.brdgrecogId = brdgrecogId;
    }

    public String getRoadNum() {
        return roadNum;
    }

    public void setRoadNum(String roadNum) {
        this.roadNum = roadNum;
    }

    public String getRoadName() {
        return roadName;
    }

    public void setRoadName(String roadName) {
        this.roadName = roadName;
    }

    public Integer getRouteLvl() {
        return routeLvl;
    }

    public void setRouteLvl(Integer routeLvl) {
        this.routeLvl = routeLvl;
    }

    public Integer getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(Integer seqNo) {
        this.seqNo = seqNo;
    }

    public String getOldHighwayStake() {
        return oldHighwayStake;
    }

    public void setOldHighwayStake(String oldHighwayStake) {
        this.oldHighwayStake = oldHighwayStake;
    }

    public Integer getFuncType() {
        return funcType;
    }

    public void setFuncType(Integer funcType) {
        this.funcType = funcType;
    }

    public String getCnstrctStake() {
        return cnstrctStake;
    }

    public void setCnstrctStake(String cnstrctStake) {
        this.cnstrctStake = cnstrctStake;
    }

    public String getDesignStake() {
        return designStake;
    }

    public void setDesignStake(String designStake) {
        this.designStake = designStake;
    }

    public Integer getBrdgNum() {
        return brdgNum;
    }

    public void setBrdgNum(Integer brdgNum) {
        this.brdgNum = brdgNum;
    }

    public String getBrdgNature() {
        return brdgNature;
    }

    public void setBrdgNature(String brdgNature) {
        this.brdgNature = brdgNature;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public String getAcrossFtrType() {
        return acrossFtrType;
    }

    public void setAcrossFtrType(String acrossFtrType) {
        this.acrossFtrType = acrossFtrType;
    }

    public String getBrdgSpanGroup() {
        return brdgSpanGroup;
    }

    public void setBrdgSpanGroup(String brdgSpanGroup) {
        this.brdgSpanGroup = brdgSpanGroup;
    }

    public String getBrdgStatus() {
        return brdgStatus;
    }

    public void setBrdgStatus(String brdgStatus) {
        this.brdgStatus = brdgStatus;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getAcrossFtrName() {
        return acrossFtrName;
    }

    public void setAcrossFtrName(String acrossFtrName) {
        this.acrossFtrName = acrossFtrName;
    }

    public String getPassageName() {
        return passageName;
    }

    public void setPassageName(String passageName) {
        this.passageName = passageName;
    }

    public String getPassageNum() {
        return passageNum;
    }

    public void setPassageNum(String passageNum) {
        this.passageNum = passageNum;
    }

    public String getDsgnLoad() {
        return dsgnLoad;
    }

    public void setDsgnLoad(String dsgnLoad) {
        this.dsgnLoad = dsgnLoad;
    }

    public String getTrafficLoad() {
        return trafficLoad;
    }

    public void setTrafficLoad(String trafficLoad) {
        this.trafficLoad = trafficLoad;
    }

    public String getBendSlope() {
        return bendSlope;
    }

    public void setBendSlope(String bendSlope) {
        this.bendSlope = bendSlope;
    }

    public String getBrdgDeck() {
        return brdgDeck;
    }

    public void setBrdgDeck(String brdgDeck) {
        this.brdgDeck = brdgDeck;
    }

    public String getSecRoute() {
        return secRoute;
    }

    public void setSecRoute(String secRoute) {
        this.secRoute = secRoute;
    }

    public LocalDateTime getCompTime() {
        return compTime;
    }

    public void setCompTime(LocalDateTime compTime) {
        this.compTime = compTime;
    }

    public String getDsgnOrg() {
        return dsgnOrg;
    }

    public void setDsgnOrg(String dsgnOrg) {
        this.dsgnOrg = dsgnOrg;
    }

    public String getCnstrctOrg() {
        return cnstrctOrg;
    }

    public void setCnstrctOrg(String cnstrctOrg) {
        this.cnstrctOrg = cnstrctOrg;
    }

    public String getSuperOrg() {
        return superOrg;
    }

    public void setSuperOrg(String superOrg) {
        this.superOrg = superOrg;
    }

    public String getCbmsOrg() {
        return cbmsOrg;
    }

    public void setCbmsOrg(String cbmsOrg) {
        this.cbmsOrg = cbmsOrg;
    }

    public String getRegulatoryOrg() {
        return regulatoryOrg;
    }

    public void setRegulatoryOrg(String regulatoryOrg) {
        this.regulatoryOrg = regulatoryOrg;
    }

    public Integer getBrdgRating() {
        return brdgRating;
    }

    public void setBrdgRating(Integer brdgRating) {
        this.brdgRating = brdgRating;
    }

    public LocalDateTime getApprTime() {
        return apprTime;
    }

    public void setApprTime(LocalDateTime apprTime) {
        this.apprTime = apprTime;
    }

    public String getRatingId() {
        return ratingId;
    }

    public void setRatingId(String ratingId) {
        this.ratingId = ratingId;
    }

    public Integer getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(Integer validFlag) {
        this.validFlag = validFlag;
    }

    public String getRpIntrvlId() {
        return rpIntrvlId;
    }

    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public String getBrdgCode() {
        return brdgCode;
    }

    public void setBrdgCode(String brdgCode) {
        this.brdgCode = brdgCode;
    }

    public String getBrdgName() {
        return brdgName;
    }

    public void setBrdgName(String brdgName) {
        this.brdgName = brdgName;
    }

    public String getCntrStake() {
        return cntrStake;
    }

    public void setCntrStake(String cntrStake) {
        this.cntrStake = cntrStake;
    }

    public Double getCntrStakeNum() {
        return cntrStakeNum;
    }

    public void setCntrStakeNum(Double cntrStakeNum) {
        this.cntrStakeNum = cntrStakeNum;
    }

    public Double getCntrSenssionNum() {
        return cntrSenssionNum;
    }

    public void setCntrSenssionNum(Double cntrSenssionNum) {
        this.cntrSenssionNum = cntrSenssionNum;
    }

    public String getOprtOrgCode() {
        return oprtOrgCode;
    }

    public void setOprtOrgCode(String oprtOrgCode) {
        this.oprtOrgCode = oprtOrgCode;
    }

    public String getCreateUserCode() {
        return createUserCode;
    }

    public void setCreateUserCode(String createUserCode) {
        this.createUserCode = createUserCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserCode() {
        return updateUserCode;
    }

    public void setUpdateUserCode(String updateUserCode) {
        this.updateUserCode = updateUserCode;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getBrdgLineType() {
        return brdgLineType;
    }

    public void setBrdgLineType(String brdgLineType) {
        this.brdgLineType = brdgLineType;
    }

    public String getFrameNum() {
        return frameNum;
    }

    public void setFrameNum(String frameNum) {
        this.frameNum = frameNum;
    }

    public Integer getRoadType() {
        return roadType;
    }

    public void setRoadType(Integer roadType) {
        this.roadType = roadType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTrafficCode() {
        return trafficCode;
    }

    public void setTrafficCode(String trafficCode) {
        this.trafficCode = trafficCode;
    }

    public String getBoxroomType() {
        return boxroomType;
    }

    public void setBoxroomType(String boxroomType) {
        this.boxroomType = boxroomType;
    }

    public String getBoxroomTypeValue() {
        return boxroomTypeValue;
    }

    public void setBoxroomTypeValue(String boxroomTypeValue) {
        this.boxroomTypeValue = boxroomTypeValue;
    }

    public String getMainRampLineId() {
        return mainRampLineId;
    }

    public void setMainRampLineId(String mainRampLineId) {
        this.mainRampLineId = mainRampLineId;
    }

    public Double getStartOffset() {
        return startOffset;
    }

    public void setStartOffset(Double startOffset) {
        this.startOffset = startOffset;
    }

    public String getRampStake() {
        return rampStake;
    }

    public void setRampStake(String rampStake) {
        this.rampStake = rampStake;
    }

    public Integer getExcelResult() {
        return excelResult;
    }

    public void setExcelResult(Integer excelResult) {
        this.excelResult = excelResult;
    }

    public Double getStartStake() {
        return startStake;
    }

    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return endStake;
    }

    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }

    public String getLastPrjId() {
        return lastPrjId;
    }

    public void setLastPrjId(String lastPrjId) {
        this.lastPrjId = lastPrjId;
    }

    public Integer getInCatalogue() {
        return inCatalogue;
    }

    public void setInCatalogue(Integer inCatalogue) {
        this.inCatalogue = inCatalogue;
    }

    public String getLogicCntrStake() {
        return logicCntrStake;
    }

    public void setLogicCntrStake(String logicCntrStake) {
        this.logicCntrStake = logicCntrStake;
    }

    public Double getLogicCntrStakeNum() {
        return logicCntrStakeNum;
    }

    public void setLogicCntrStakeNum(Double logicCntrStakeNum) {
        this.logicCntrStakeNum = logicCntrStakeNum;
    }

    public String getOprtOrgName() {
        return oprtOrgName;
    }

    public void setOprtOrgName(String oprtOrgName) {
        this.oprtOrgName = oprtOrgName;
    }

    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    public String getMainName() {
        return mainName;
    }

    public void setMainName(String mainName) {
        this.mainName = mainName;
    }

    public Double getJd() {
        return jd;
    }

    public void setJd(Double jd) {
        this.jd = jd;
    }

    public Double getWd() {
        return wd;
    }

    public void setWd(Double wd) {
        this.wd = wd;
    }

    public String getSsfSet() {
        return ssfSet;
    }

    public void setSsfSet(String ssfSet) {
        this.ssfSet = ssfSet;
    }

    public String getNewId() {
        return newId;
    }

    public void setNewId(String newId) {
        this.newId = newId;
    }

    public String getYhid() {
        return yhid;
    }

    public void setYhid(String yhid) {
        this.yhid = yhid;
    }

    public Double getGrade2020() {
        return grade2020;
    }

    public void setGrade2020(Double grade2020) {
        this.grade2020 = grade2020;
    }

    public Double getGrade2019() {
        return grade2019;
    }

    public void setGrade2019(Double grade2019) {
        this.grade2019 = grade2019;
    }

    public Double getGrade2018() {
        return grade2018;
    }

    public void setGrade2018(Double grade2018) {
        this.grade2018 = grade2018;
    }

    public Double getGrade2017() {
        return grade2017;
    }

    public void setGrade2017(Double grade2017) {
        this.grade2017 = grade2017;
    }

    public Double getGrade2016() {
        return grade2016;
    }

    public void setGrade2016(Double grade2016) {
        this.grade2016 = grade2016;
    }

    public String getBrdgType() {
        return brdgType;
    }

    public void setBrdgType(String brdgType) {
        this.brdgType = brdgType;
    }

    public String getPrjCode() {
        return prjCode;
    }

    public void setPrjCode(String prjCode) {
        this.prjCode = prjCode;
    }

    public String getPrjName() {
        return prjName;
    }

    public void setPrjName(String prjName) {
        this.prjName = prjName;
    }

    public String getOldBrdgcode() {
        return oldBrdgcode;
    }

    public void setOldBrdgcode(String oldBrdgcode) {
        this.oldBrdgcode = oldBrdgcode;
    }

    public Double getKuanlzq() {
        return kuanlzq;
    }

    public void setKuanlzq(Double kuanlzq) {
        this.kuanlzq = kuanlzq;
    }

    public Double getSfxzdm() {
        return sfxzdm;
    }

    public void setSfxzdm(Double sfxzdm) {
        this.sfxzdm = sfxzdm;
    }

    public Double getF039() {
        return f039;
    }

    public void setF039(Double f039) {
        this.f039 = f039;
    }

    public Double getGjxzdm() {
        return gjxzdm;
    }

    public void setGjxzdm(Double gjxzdm) {
        this.gjxzdm = gjxzdm;
    }

    public Double getF042() {
        return f042;
    }

    public void setF042(Double f042) {
        this.f042 = f042;
    }

    public Double getBzxm() {
        return bzxm;
    }

    public void setBzxm(Double bzxm) {
        this.bzxm = bzxm;
    }

    public Double getGcxzdm() {
        return gcxzdm;
    }

    public void setGcxzdm(Double gcxzdm) {
        this.gcxzdm = gcxzdm;
    }

    public String getF099() {
        return f099;
    }

    public void setF099(String f099) {
        this.f099 = f099;
    }

    public Double getF103() {
        return f103;
    }

    public void setF103(Double f103) {
        this.f103 = f103;
    }

    public Double getF107() {
        return f107;
    }

    public void setF107(Double f107) {
        this.f107 = f107;
    }

    public Double getF109() {
        return f109;
    }

    public void setF109(Double f109) {
        this.f109 = f109;
    }

    public Double getF105() {
        return f105;
    }

    public void setF105(Double f105) {
        this.f105 = f105;
    }

    public Double getHasThreePart() {
        return hasThreePart;
    }

    public void setHasThreePart(Double hasThreePart) {
        this.hasThreePart = hasThreePart;
    }

    public LocalDateTime getLastPrjDate() {
        return lastPrjDate;
    }

    public void setLastPrjDate(LocalDateTime lastPrjDate) {
        this.lastPrjDate = lastPrjDate;
    }

    public Double getWsg() {
        return wsg;
    }

    public void setWsg(Double wsg) {
        this.wsg = wsg;
    }

    public LocalDateTime getTransTime() {
        return transTime;
    }

    public void setTransTime(LocalDateTime transTime) {
        this.transTime = transTime;
    }

    public String getTransRemark() {
        return transRemark;
    }

    public void setTransRemark(String transRemark) {
        this.transRemark = transRemark;
    }

    public String getTransFile() {
        return transFile;
    }

    public void setTransFile(String transFile) {
        this.transFile = transFile;
    }

    public LocalDateTime getLastDispCompDate() {
        return lastDispCompDate;
    }

    public void setLastDispCompDate(LocalDateTime lastDispCompDate) {
        this.lastDispCompDate = lastDispCompDate;
    }

    public String getLastDispId() {
        return lastDispId;
    }

    public void setLastDispId(String lastDispId) {
        this.lastDispId = lastDispId;
    }

    public String getRouteCodeVersion() {
        return routeCodeVersion;
    }

    public void setRouteCodeVersion(String routeCodeVersion) {
        this.routeCodeVersion = routeCodeVersion;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getBrdgCodeBak() {
        return brdgCodeBak;
    }

    public void setBrdgCodeBak(String brdgCodeBak) {
        this.brdgCodeBak = brdgCodeBak;
    }

    public Double getPhysicsCntrStakeNumNew() {
        return physicsCntrStakeNumNew;
    }

    public void setPhysicsCntrStakeNumNew(Double physicsCntrStakeNumNew) {
        this.physicsCntrStakeNumNew = physicsCntrStakeNumNew;
    }

    public Integer getWidenBridge() {
        return widenBridge;
    }

    public void setWidenBridge(Integer widenBridge) {
        this.widenBridge = widenBridge;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    public Integer getHasHla() {
        return hasHla;
    }

    public void setHasHla(Integer hasHla) {
        this.hasHla = hasHla;
    }

    public Double getHasScp() {
        return hasScp;
    }

    public void setHasScp(Double hasScp) {
        this.hasScp = hasScp;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getHlaDesc() {
        return hlaDesc;
    }

    public void setHlaDesc(String hlaDesc) {
        this.hlaDesc = hlaDesc;
    }

    public Double getJgjc() {
        return jgjc;
    }

    public void setJgjc(Double jgjc) {
        this.jgjc = jgjc;
    }

    public Double getCzjc() {
        return czjc;
    }

    public void setCzjc(Double czjc) {
        this.czjc = czjc;
    }

    public Double getHzjc() {
        return hzjc;
    }

    public void setHzjc(Double hzjc) {
        this.hzjc = hzjc;
    }

    public Integer getUnderloadwarn() {
        return underloadwarn;
    }

    public void setUnderloadwarn(Integer underloadwarn) {
        this.underloadwarn = underloadwarn;
    }

    public Integer getHumiditywarn() {
        return humiditywarn;
    }

    public void setHumiditywarn(Integer humiditywarn) {
        this.humiditywarn = humiditywarn;
    }

    public Integer getStrainwarn() {
        return strainwarn;
    }

    public void setStrainwarn(Integer strainwarn) {
        this.strainwarn = strainwarn;
    }

    public Integer getAccelerationwarn() {
        return accelerationwarn;
    }

    public void setAccelerationwarn(Integer accelerationwarn) {
        this.accelerationwarn = accelerationwarn;
    }

    public String getRouteCodeBak() {
        return routeCodeBak;
    }

    public void setRouteCodeBak(String routeCodeBak) {
        this.routeCodeBak = routeCodeBak;
    }

    public LocalDateTime getDelTime() {
        return delTime;
    }

    public void setDelTime(LocalDateTime delTime) {
        this.delTime = delTime;
    }

    public String getDelPerson() {
        return delPerson;
    }

    public void setDelPerson(String delPerson) {
        this.delPerson = delPerson;
    }

    public LocalDateTime getRecoverTime() {
        return recoverTime;
    }

    public void setRecoverTime(LocalDateTime recoverTime) {
        this.recoverTime = recoverTime;
    }

    public String getRecoverPerson() {
        return recoverPerson;
    }

    public void setRecoverPerson(String recoverPerson) {
        this.recoverPerson = recoverPerson;
    }

    public String getXqCode() {
        return xqCode;
    }

    public void setXqCode(String xqCode) {
        this.xqCode = xqCode;
    }

    public String getBrdgCode20210911() {
        return brdgCode20210911;
    }

    public void setBrdgCode20210911(String brdgCode20210911) {
        this.brdgCode20210911 = brdgCode20210911;
    }

    public String getDrivingDirection() {
        return drivingDirection;
    }

    public void setDrivingDirection(String drivingDirection) {
        this.drivingDirection = drivingDirection;
    }

    public String getBrdgCode20210914() {
        return brdgCode20210914;
    }

    public void setBrdgCode20210914(String brdgCode20210914) {
        this.brdgCode20210914 = brdgCode20210914;
    }

    public String getBrdgCode20210915() {
        return brdgCode20210915;
    }

    public void setBrdgCode20210915(String brdgCode20210915) {
        this.brdgCode20210915 = brdgCode20210915;
    }

    public Double getBw() {
        return bw;
    }

    public void setBw(Double bw) {
        this.bw = bw;
    }

    public String getJcCode() {
        return jcCode;
    }

    public void setJcCode(String jcCode) {
        this.jcCode = jcCode;
    }

    public String getReportRoadNum() {
        return reportRoadNum;
    }

    public void setReportRoadNum(String reportRoadNum) {
        this.reportRoadNum = reportRoadNum;
    }

    public Double getLatitueBak() {
        return latitueBak;
    }

    public void setLatitueBak(Double latitueBak) {
        this.latitueBak = latitueBak;
    }

    public Double getLongtitueBak() {
        return longtitueBak;
    }

    public void setLongtitueBak(Double longtitueBak) {
        this.longtitueBak = longtitueBak;
    }

    public Double getTransProvincial() {
        return transProvincial;
    }

    public void setTransProvincial(Double transProvincial) {
        this.transProvincial = transProvincial;
    }

    public String getPlaceBak() {
        return placeBak;
    }

    public void setPlaceBak(String placeBak) {
        this.placeBak = placeBak;
    }

    public String getOldYhid() {
        return oldYhid;
    }

    public void setOldYhid(String oldYhid) {
        this.oldYhid = oldYhid;
    }

    public Double getDataIntegrity() {
        return dataIntegrity;
    }

    public void setDataIntegrity(Double dataIntegrity) {
        this.dataIntegrity = dataIntegrity;
    }

    public Double getNeverDj() {
        return neverDj;
    }

    public void setNeverDj(Double neverDj) {
        this.neverDj = neverDj;
    }

    public Double getNeverDj3() {
        return neverDj3;
    }

    public void setNeverDj3(Double neverDj3) {
        this.neverDj3 = neverDj3;
    }

    public String getTrafficCodeBak() {
        return trafficCodeBak;
    }

    public void setTrafficCodeBak(String trafficCodeBak) {
        this.trafficCodeBak = trafficCodeBak;
    }

    public String getTrafficCodeBak20220120() {
        return trafficCodeBak20220120;
    }

    public void setTrafficCodeBak20220120(String trafficCodeBak20220120) {
        this.trafficCodeBak20220120 = trafficCodeBak20220120;
    }

    public String getTrafficCodeBak20220221() {
        return trafficCodeBak20220221;
    }

    public void setTrafficCodeBak20220221(String trafficCodeBak20220221) {
        this.trafficCodeBak20220221 = trafficCodeBak20220221;
    }

    public Double getMortarrubble() {
        return mortarrubble;
    }

    public void setMortarrubble(Double mortarrubble) {
        this.mortarrubble = mortarrubble;
    }

    public String getReinforcementform() {
        return reinforcementform;
    }

    public void setReinforcementform(String reinforcementform) {
        this.reinforcementform = reinforcementform;
    }

    public String getReinforcementformextra() {
        return reinforcementformextra;
    }

    public void setReinforcementformextra(String reinforcementformextra) {
        this.reinforcementformextra = reinforcementformextra;
    }

    public Integer getLghlMtrl() {
        return lghlMtrl;
    }

    public void setLghlMtrl(Integer lghlMtrl) {
        this.lghlMtrl = lghlMtrl;
    }

    public Integer getMaintainGrade() {
        return maintainGrade;
    }

    public void setMaintainGrade(Integer maintainGrade) {
        this.maintainGrade = maintainGrade;
    }

    public Double getXiaoyu40() {
        return xiaoyu40;
    }

    public void setXiaoyu40(Double xiaoyu40) {
        this.xiaoyu40 = xiaoyu40;
    }

    public Double getXiaoyu100() {
        return xiaoyu100;
    }

    public void setXiaoyu100(Double xiaoyu100) {
        this.xiaoyu100 = xiaoyu100;
    }

    public Double getDayudengyu100() {
        return dayudengyu100;
    }

    public void setDayudengyu100(Double dayudengyu100) {
        this.dayudengyu100 = dayudengyu100;
    }

    public String getHasav() {
        return hasav;
    }

    public void setHasav(String hasav) {
        this.hasav = hasav;
    }

    public String getBse() {
        return bse;
    }

    public void setBse(String bse) {
        this.bse = bse;
    }

    public String getInspectCompany() {
        return inspectCompany;
    }

    public void setInspectCompany(String inspectCompany) {
        this.inspectCompany = inspectCompany;
    }

    public Double getIsMain() {
        return isMain;
    }

    public void setIsMain(Double isMain) {
        this.isMain = isMain;
    }

    public String getOldRoadbm() {
        return oldRoadbm;
    }

    public void setOldRoadbm(String oldRoadbm) {
        this.oldRoadbm = oldRoadbm;
    }

    public Double getJwdgx() {
        return jwdgx;
    }

    public void setJwdgx(Double jwdgx) {
        this.jwdgx = jwdgx;
    }

    public String getJwdgxsj() {
        return jwdgxsj;
    }

    public void setJwdgxsj(String jwdgxsj) {
        this.jwdgxsj = jwdgxsj;
    }

    public Double getJdGis() {
        return jdGis;
    }

    public void setJdGis(Double jdGis) {
        this.jdGis = jdGis;
    }

    public Double getWdGis() {
        return wdGis;
    }

    public void setWdGis(Double wdGis) {
        this.wdGis = wdGis;
    }

    public Double getJdStake() {
        return jdStake;
    }

    public void setJdStake(Double jdStake) {
        this.jdStake = jdStake;
    }

    public Double getWdStake() {
        return wdStake;
    }

    public void setWdStake(Double wdStake) {
        this.wdStake = wdStake;
    }

    public Double getDisXt() {
        return disXt;
    }

    public void setDisXt(Double disXt) {
        this.disXt = disXt;
    }

    public Double getDisStake() {
        return disStake;
    }

    public void setDisStake(Double disStake) {
        this.disStake = disStake;
    }

    public String getXqAreaCode() {
        return xqAreaCode;
    }

    public void setXqAreaCode(String xqAreaCode) {
        this.xqAreaCode = xqAreaCode;
    }

    public String getOldRoadbm20221128() {
        return oldRoadbm20221128;
    }

    public void setOldRoadbm20221128(String oldRoadbm20221128) {
        this.oldRoadbm20221128 = oldRoadbm20221128;
    }

    public String getTrafficCode20221213() {
        return trafficCode20221213;
    }

    public void setTrafficCode20221213(String trafficCode20221213) {
        this.trafficCode20221213 = trafficCode20221213;
    }
}
