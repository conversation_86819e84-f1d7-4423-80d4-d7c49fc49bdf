<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.MtmsTunnelBasicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.MtmsTunnelBasic">
        <id column="TUNNEL_ID" property="tunnelId" />
        <result column="TUNNEL_CODE" property="tunnelCode" />
        <result column="TUNNEL_NAME" property="tunnelName" />
        <result column="TUNNEL_LINE_DIRECT" property="tunnelLineDirect" />
        <result column="TUNNEL_RANGE" property="tunnelRange" />
        <result column="CNTR_STAKE" property="cntrStake" />
        <result column="CNTR_STAKE_NUM" property="cntrStakeNum" />
        <result column="CNTR_SENSSION_NUM" property="cntrSenssionNum" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="DESIGN_START_STAKE_NUM" property="designStartStakeNum" />
        <result column="DESIGN_END_STAKE_NUM" property="designEndStakeNum" />
        <result column="TUNNEL_LENGTH" property="tunnelLength" />
        <result column="TUBE_CULTURE_UNIT" property="tubeCultureUnit" />
        <result column="COMMUT_DATE" property="commutDate" />
        <result column="TUNNEL_WIDTH" property="tunnelWidth" />
        <result column="TUNNEL_HEIGHT" property="tunnelHeight" />
        <result column="MSNR_MTRL" property="msnrMtrl" />
        <result column="IN_HOLE_STRUCT" property="inHoleStruct" />
        <result column="OUT_HOLE_STRUCT" property="outHoleStruct" />
        <result column="ROAD_SURFACE" property="roadSurface" />
        <result column="FIRE_FACILITIES" property="fireFacilities" />
        <result column="CAVE_LIGHT_FORM" property="caveLightForm" />
        <result column="CAVE_VNTLT_FORM" property="caveVntltForm" />
        <result column="IS_SEP_TYPE" property="isSepType" />
        <result column="IS_UNDERWATER_FLAG" property="isUnderwaterFlag" />
        <result column="SECTION_FORM" property="sectionForm" />
        <result column="ELECTRON_DEVICE" property="electronDevice" />
        <result column="DRAINAGE_TYPE" property="drainageType" />
        <result column="SAFE_ACCESS_NUM" property="safeAccessNum" />
        <result column="SIDEWALK" property="sidewalk" />
        <result column="ROADWAY_WIDE" property="roadwayWide" />
        <result column="BUILD_ORG_NAME" property="buildOrgName" />
        <result column="DESIGN_ORG_NAME" property="designOrgName" />
        <result column="CONSTRUCTION_ORG_NAME" property="constructionOrgName" />
        <result column="SUPERVISOR_ORG_NAME" property="supervisorOrgName" />
        <result column="EVALUATE_DATA" property="evaluateData" />
        <result column="EVALUATION_UNIT" property="evaluationUnit" />
        <result column="COMPLETION_DATE" property="completionDate" />
        <result column="REMOULD_PART" property="remouldPart" />
        <result column="REMARK" property="remark" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="TUNNEL_CLASSIFIED" property="tunnelClassified" />
        <result column="DSTRCT_CODE" property="dstrctCode" />
        <result column="ENGIN_PROPERTY" property="enginProperty" />
        <result column="IS_KEY_LABEL" property="isKeyLabel" />
        <result column="OPRT_ORG_CODE" property="oprtOrgCode" />
        <result column="PRJ_ORG_CODE" property="prjOrgCode" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="MAINLINE_ID" property="mainlineId" />
        <result column="START_OFFSET" property="startOffset" />
        <result column="MAIN_RAMP_LINE_ID" property="mainRampLineId" />
        <result column="THREAD_RPID" property="threadRpid" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="GIS_ID" property="gisId" />
        <result column="GIS_X" property="gisX" />
        <result column="GIS_Y" property="gisY" />
        <result column="GIS_BDX" property="gisBdx" />
        <result column="GIS_BDY" property="gisBdy" />
        <result column="TUNNEL_MAINTAIN_GRADE" property="tunnelMaintainGrade" />
        <result column="BUILT_DATE" property="builtDate" />
        <result column="LANE_NUM" property="laneNum" />
        <result column="PRJ_ID" property="prjId" />
        <result column="MECHATRONICS_GRADE" property="mechatronicsGrade" />
        <result column="TUNNEL_TC_GRADE" property="tunnelTcGrade" />
        <result column="REPORT_NUMBER" property="reportNumber" />
        <result column="TRAFFIC_FAC_GRADE" property="trafficFacGrade" />
        <result column="RANGE_EVALUATOR" property="rangeEvaluator" />
        <result column="RANGE_GRADE_DATE" property="rangeGradeDate" />
        <result column="MECHATRONICS_EVALUATOR" property="mechatronicsEvaluator" />
        <result column="MECHATRONICS_DATE" property="mechatronicsDate" />
        <result column="OTHER_GRADE" property="otherGrade" />
        <result column="OTHER_EVALUATOR" property="otherEvaluator" />
        <result column="OTHER_GRADE_DATE" property="otherGradeDate" />
        <result column="IS_TUNNEL" property="isTunnel" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="PHYSICS_CNTR_STAKE" property="physicsCntrStake" />
        <result column="LOGIC_CNTR_STAKE" property="logicCntrStake" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="ROUTE_VERSION" property="routeVersion" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="LINE_NAME" property="lineName" />
        <result column="COMPLETION_YEAR" property="completionYear" />
        <result column="ROAD_LVL" property="roadLvl" />
        <result column="SUPERVISOR_ORG" property="supervisorOrg" />
        <result column="JD_BAK" property="jdBak" />
        <result column="WD_BAK" property="wdBak" />
        <result column="LOGIC_START_STAKE" property="logicStartStake" />
        <result column="LOGIC_END_STAKE" property="logicEndStake" />
        <result column="YHID" property="yhid" />
        <result column="DESIGN_START_STAKE" property="designStartStake" />
        <result column="DESIGN_END_STAKE" property="designEndStake" />
        <result column="LOGIC_RAMP_CNTR_STAKE" property="logicRampCntrStake" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="IS_PROVINCIAL" property="isProvincial" />
        <result column="TCC_PRJ_ID" property="tccPrjId" />
        <result column="TUNNEL_TYPE" property="tunnelType" />
        <result column="IS_ADMIN" property="isAdmin" />
        <result column="DESIGN_SPEED" property="designSpeed" />
        <result column="CONSTRUCTION_METHOD" property="constructionMethod" />
        <result column="IN_GCJ_X" property="inGcjX" />
        <result column="IN_GCJ_Y" property="inGcjY" />
        <result column="OUT_GCJ_X" property="outGcjX" />
        <result column="OUT_GCJ_Y" property="outGcjY" />
        <result column="SINGLE_LANE_WIDTH" property="singleLaneWidth" />
        <result column="TOTAL_LANE_WIDTH" property="totalLaneWidth" />
        <result column="OVERHAUL_LANE" property="overhaulLane" />
        <result column="OVERHAUL_LANE_WIDTH" property="overhaulLaneWidth" />
        <result column="SLOPE_RADIO" property="slopeRadio" />
        <result column="EMERGENCY_PARKING_STRIP" property="emergencyParkingStrip" />
        <result column="ESCAPE_TRUNK" property="escapeTrunk" />
        <result column="LININGFORM_TYPE" property="liningformType" />
        <result column="FOOTWAY" property="footway" />
        <result column="ROADWAY" property="roadway" />
        <result column="FLOOD_PROTECTION" property="floodProtection" />
        <result column="ANTI_SEISMIC" property="antiSeismic" />
        <result column="SUPERVISION" property="supervision" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        TUNNEL_ID, TUNNEL_CODE, TUNNEL_NAME, TUNNEL_LINE_DIRECT, TUNNEL_RANGE, CNTR_STAKE, CNTR_STAKE_NUM, CNTR_SENSSION_NUM, START_STAKE_NUM, END_STAKE_NUM, DESIGN_START_STAKE_NUM, DESIGN_END_STAKE_NUM, TUNNEL_LENGTH, TUBE_CULTURE_UNIT, COMMUT_DATE, TUNNEL_WIDTH, TUNNEL_HEIGHT, MSNR_MTRL, IN_HOLE_STRUCT, OUT_HOLE_STRUCT, ROAD_SURFACE, FIRE_FACILITIES, CAVE_LIGHT_FORM, CAVE_VNTLT_FORM, IS_SEP_TYPE, IS_UNDERWATER_FLAG, SECTION_FORM, ELECTRON_DEVICE, DRAINAGE_TYPE, SAFE_ACCESS_NUM, SIDEWALK, ROADWAY_WIDE, BUILD_ORG_NAME, DESIGN_ORG_NAME, CONSTRUCTION_ORG_NAME, SUPERVISOR_ORG_NAME, EVALUATE_DATA, EVALUATION_UNIT, COMPLETION_DATE, REMOULD_PART, REMARK, IS_ENABLE, TUNNEL_CLASSIFIED, DSTRCT_CODE, ENGIN_PROPERTY, IS_KEY_LABEL, OPRT_ORG_CODE, PRJ_ORG_CODE, RP_INTRVL_ID, MAINLINE_ID, START_OFFSET, MAIN_RAMP_LINE_ID, THREAD_RPID, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, IS_DELETED, GIS_ID, GIS_X, GIS_Y, GIS_BDX, GIS_BDY, TUNNEL_MAINTAIN_GRADE, BUILT_DATE, LANE_NUM, PRJ_ID, MECHATRONICS_GRADE, TUNNEL_TC_GRADE, REPORT_NUMBER, TRAFFIC_FAC_GRADE, RANGE_EVALUATOR, RANGE_GRADE_DATE, MECHATRONICS_EVALUATOR, MECHATRONICS_DATE, OTHER_GRADE, OTHER_EVALUATOR, OTHER_GRADE_DATE, IS_TUNNEL, LINE_CODE, PHYSICS_CNTR_STAKE, LOGIC_CNTR_STAKE, ROUTE_CODE, ROUTE_VERSION, DSS_TYPE, DSS_POSITION, LINE_NAME, COMPLETION_YEAR, ROAD_LVL, SUPERVISOR_ORG, JD_BAK, WD_BAK, LOGIC_START_STAKE, LOGIC_END_STAKE, YHID, DESIGN_START_STAKE, DESIGN_END_STAKE, LOGIC_RAMP_CNTR_STAKE, ROUTE_NAME, IS_PROVINCIAL, TCC_PRJ_ID, TUNNEL_TYPE, IS_ADMIN, DESIGN_SPEED, CONSTRUCTION_METHOD, IN_GCJ_X, IN_GCJ_Y, OUT_GCJ_X, OUT_GCJ_Y, SINGLE_LANE_WIDTH, TOTAL_LANE_WIDTH, OVERHAUL_LANE, OVERHAUL_LANE_WIDTH, SLOPE_RADIO, EMERGENCY_PARKING_STRIP, ESCAPE_TRUNK, LININGFORM_TYPE, FOOTWAY, ROADWAY, FLOOD_PROTECTION, ANTI_SEISMIC, SUPERVISION
    </sql>

</mapper>
