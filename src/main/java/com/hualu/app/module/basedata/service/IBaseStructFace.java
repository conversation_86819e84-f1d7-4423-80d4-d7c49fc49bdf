package com.hualu.app.module.basedata.service;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.util.sp.WebNbUtil;
import com.tg.dev.mybatisplus.service.PageService;
import org.assertj.core.util.Lists;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IBaseStructFace {

    /**
     * 根据主键获取对象集合
     * @param structId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    BaseStructDto getId(String structId);

    /**
     * 设施类型
     * @return
     */
    String getFacilityCat();

    /**
     * 分页查询结构物
     * @return
     */
    Object selectStructPage();


    /**
     * 根据路段，查询所有结构物
     * @param routeCodes
     * @return
     */
    default List<BaseStructDto> listByRouteCodes(Set<String> routeCodes) {return null;}

    /**
     * 分页对象
     * @return
     */
    default Page getPage(){
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        PageService pageService = CustomApplicationContextHolder.getBean(PageService.class);
        return pageService.parsePageParam(request);
    }

    /**
     * 获取结构物集合
     * @param structIds
     * @return
     */
    default List<BaseStructDto> listByStructIds(List<String> structIds){
        return Lists.emptyList();
    }

    /**
     * 查询参数
     * @param nameFieldName 字段名称
     * @return
     */
    default Map<String, Object> getStructParamMap(String nameFieldName,String idFieldName){
        String SN = "structName,cntrStakeLike";
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Map<String, Object> paramsMap = WebNbUtil.getParamsMap2(request);
        Object structName = paramsMap.get("structName");
        if (structName != null){
            paramsMap.remove("structName");
            paramsMap.put(nameFieldName+"Like",structName);
        }
        Object likeName = paramsMap.get(SN);
        if (likeName != null){
            paramsMap.remove(SN);
            paramsMap.put(nameFieldName+",logicCntrStakeLike",likeName);
        }

        Object structId = paramsMap.get("structId");
        if (structId != null){
            paramsMap.put(idFieldName,structId);
        }
        return paramsMap;
    }

    default Object returnPage(IPage page){
        PageService pageService = CustomApplicationContextHolder.getBean(PageService.class);
        return pageService.returnPageResult(page);
    }

    /**
     * 设置区间范围查询
     * @param paramsMap
     * @param stakeFieldName
     */
    default void setStakeScopeQuery(Map paramsMap, String stakeFieldName, QueryWrapper queryWrapper){

        String startStake = paramsMap.getOrDefault("startStake", "").toString();
        if (StrUtil.isNotBlank(startStake) && NumberUtil.isNumber(startStake)){
            queryWrapper.ge(stakeFieldName,startStake);
            paramsMap.remove("startStake");
        }

        String endStake = paramsMap.getOrDefault("endStake", "").toString();
        if (StrUtil.isNotBlank(endStake) && NumberUtil.isNumber(endStake)){
            queryWrapper.le(stakeFieldName,endStake);
            paramsMap.remove("endStake");
        }
    }
}
