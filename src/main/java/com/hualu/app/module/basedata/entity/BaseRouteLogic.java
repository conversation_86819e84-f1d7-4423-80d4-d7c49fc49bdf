package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 逻辑路段表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseRouteLogic对象", description="逻辑路段表")
public class BaseRouteLogic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "逻辑路段ID主键")
    @TableId("LOGIC_ROUTE_ID")
    private String logicRouteId;

    @ApiModelProperty(value = "路段序号")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "长度")
    @TableField("ROUTE_LENGTH")
    private Double routeLength;

    @ApiModelProperty(value = "营运公司")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "线路编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "线路方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "逻辑路段版本号")
    @TableField("ROUTE_VERSION")
    private String routeVersion;

    @ApiModelProperty(value = "生成版本号时间")
    @TableField("CREATE_VERSION_TIME")
    private LocalDateTime createVersionTime;

    @ApiModelProperty(value = "项目公司")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "逻辑起点桩号")
    @TableField("START_STAKE")
    private Double startStake;

    @ApiModelProperty(value = "逻辑止点桩号")
    @TableField("END_STAKE")
    private Double endStake;

    @ApiModelProperty(value = "起点物理桩号")
    @TableField("RP_START_STAKE")
    private Double rpStartStake;

    @ApiModelProperty(value = "止点物理桩号")
    @TableField("RP_END_STAKE")
    private Double rpEndStake;

    @ApiModelProperty(value = "旧路线编码")
    @TableField("OLD_LINE_CODE")
    private String oldLineCode;

    @ApiModelProperty(value = "旧路线方向")
    @TableField("OLD_LINE_DIRECT")
    private String oldLineDirect;

    @ApiModelProperty(value = "旧路线版本")
    @TableField("OLD_VERSION")
    private String oldVersion;

    @ApiModelProperty(value = "旧起点逻辑桩号")
    @TableField("OLD_START_STAKE")
    private Double oldStartStake;

    @ApiModelProperty(value = "旧止点逻辑桩号")
    @TableField("OLD_END_STAKE")
    private Double oldEndStake;

    @ApiModelProperty(value = "是否启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "线路ID")
    @TableField("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "重复路线ID")
    @TableField("CF_LINE_ID")
    private String cfLineId;

    @ApiModelProperty(value = "重复路线编码")
    @TableField("CF_LINE_CODE")
    private String cfLineCode;

    @ApiModelProperty(value = "重复路线路线方向")
    @TableField("CF_LINE_LDLX")
    private String cfLineLdlx;

    @ApiModelProperty(value = "重复路线起点桩号")
    @TableField("CF_START_STAKE")
    private Double cfStartStake;

    @ApiModelProperty(value = "重复路线止点桩号")
    @TableField("CF_END_STAKE")
    private Double cfEndStake;

    @ApiModelProperty(value = "路段名称")
    @TableField("ROUTE_NAME")
    private String routeName;

    @ApiModelProperty(value = "是否最新版本")
    @TableField("IS_NEW")
    private String isNew;

    @ApiModelProperty(value = "轴载次数")
    @TableField("RMAL_NUMBER")
    private Long rmalNumber;


    @TableField("ROAD_NAME")
    private String roadName;
}
