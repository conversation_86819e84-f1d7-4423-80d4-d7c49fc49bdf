package com.hualu.app.module.basedata.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 视野范围内参数
 */
@Accessors(chain = true)
@Data
public class GeometryParamDto implements Serializable {

    private static final long serialVersionUID = 1L;

    //最小经度
    @NotNull(message = "minX不能为空")
    private Double minX;

    //最小纬度
    @NotNull(message = "minY不能为空")
    private Double minY;

    //最大经度
    @NotNull(message = "maxX不能为空")
    private Double maxX;

    //最大纬度
    @NotNull(message = "maxY不能为空")
    private Double maxY;

    //地图层级,层级小于16时，不查询数据
    @NotNull(message = "level不能为空")
    private Integer level;
}
