package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 收费站
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseTollgate对象", description="收费站")
public class BaseTollgate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "收费站ID")
    @TableId("TOLLGATE_ID")
    private String tollgateId;

    @ApiModelProperty(value = "营运路段物理区间ID")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "营运公司编码")
    @TableField("OPRT_ORG_CODE")
    private String oprtOrgCode;

    @ApiModelProperty(value = "收费站编码")
    @TableField("TOLLGATE_CODE")
    private String tollgateCode;

    @ApiModelProperty(value = "收费站名称")
    @TableField("TOLLGATE_NAME")
    private String tollgateName;

    @ApiModelProperty(value = "中心桩号")
    @TableField("CNTR_STAKE")
    private String cntrStake;

    @ApiModelProperty(value = "中心桩号值")
    @TableField("CNTR_STAKE_NUM")
    private Double cntrStakeNum;

    @ApiModelProperty(value = "中心桩号链值")
    @TableField("CNTR_SENSSION_NUM")
    private Double cntrSenssionNum;

    @ApiModelProperty(value = "收费站路段类型")
    @TableField("TOLLGATE_LINE_TYPE")
    private String tollgateLineType;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "是否启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "GIS表ID")
    @TableField("GIS_ID")
    private String gisId;

    @ApiModelProperty(value = "GIS经度")
    @TableField("GIS_X")
    private Double gisX;

    @ApiModelProperty(value = "GIS维度")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "主路线ID")
    @TableField("MAINLINE_ID")
    private String mainlineId;

    @ApiModelProperty(value = "收费站类型（1取卡收费站,2收费收费站）")
    @TableField("TOLLGATE_TYPE")
    private Integer tollgateType;

    @ApiModelProperty(value = "距离该物理路段区间起始点的便宜量")
    @TableField("START_OFFSET")
    private Double startOffset;

    @ApiModelProperty(value = "匝道路线ID")
    @TableField("MAIN_RAMP_LINE_ID")
    private String mainRampLineId;

    @ApiModelProperty(value = "归属项目公司编码")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "收费站编码(对应收费系统)")
    @TableField("STATIONID")
    private String stationid;

    @ApiModelProperty(value = "路线编码(对应收费系统)")
    @TableField("NETROADID")
    private String netroadid;

    @ApiModelProperty(value = "主线桩号对应的主线路段区间ID")
    @TableField("THREAD_RPID")
    private String threadRpid;

    @ApiModelProperty(value = "逻辑中心桩号值")
    @TableField("LOGIC_CNTR_STAKE")
    private String logicCntrStake;

    @ApiModelProperty(value = "逻辑桩号值")
    @TableField("LOGIC_CNTR_STAKE_NUM")
    private Double logicCntrStakeNum;

    @ApiModelProperty(value = "区划编码")
    @TableField("DISTRICT_CODE")
    private String districtCode;

    @ApiModelProperty(value = "公路局上报编码")
    @TableField("REPORT_CODE")
    private String reportCode;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "公司名称")
    @TableField("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "物理中心桩号值")
    @TableField("PHYSICS_CNTR_STAKE")
    private Double physicsCntrStake;

    @ApiModelProperty(value = "物理路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "路段版本")
    @TableField("ROUTE_VERSION")
    private String routeVersion;

    @ApiModelProperty(value = "逻辑匝道中心桩号")
    @TableField("LOGIC_RAMP_CNTR_STAKE")
    private Double logicRampCntrStake;

    @ApiModelProperty(value = "公路局关联主键")
    @TableField("YHID")
    private String yhid;

    @ApiModelProperty(value = "是否主收费站(1是0不是)")
    @TableField("IS_MAIN")
    private String isMain;


}
