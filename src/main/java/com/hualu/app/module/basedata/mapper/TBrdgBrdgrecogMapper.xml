<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.TBrdgBrdgrecogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.TBrdgBrdgrecog">
        <id column="BRDGRECOG_ID" property="brdgrecogId" />
        <result column="ROAD_NUM" property="roadNum" />
        <result column="ROAD_NAME" property="roadName" />
        <result column="ROUTE_LVL" property="routeLvl" />
        <result column="SEQ_NO" property="seqNo" />
        <result column="OLD_HIGHWAY_STAKE" property="oldHighwayStake" />
        <result column="FUNC_TYPE" property="funcType" />
        <result column="CNSTRCT_STAKE" property="cnstrctStake" />
        <result column="DESIGN_STAKE" property="designStake" />
        <result column="BRDG_NUM" property="brdgNum" />
        <result column="BRDG_NATURE" property="brdgNature" />
        <result column="LONGITUDE" property="longitude" />
        <result column="ACROSS_FTR_TYPE" property="acrossFtrType" />
        <result column="BRDG_SPAN_GROUP" property="brdgSpanGroup" />
        <result column="BRDG_STATUS" property="brdgStatus" />
        <result column="LATITUDE" property="latitude" />
        <result column="ACROSS_FTR_NAME" property="acrossFtrName" />
        <result column="PASSAGE_NAME" property="passageName" />
        <result column="PASSAGE_NUM" property="passageNum" />
        <result column="DSGN_LOAD" property="dsgnLoad" />
        <result column="TRAFFIC_LOAD" property="trafficLoad" />
        <result column="BEND_SLOPE" property="bendSlope" />
        <result column="BRDG_DECK" property="brdgDeck" />
        <result column="SEC_ROUTE" property="secRoute" />
        <result column="COMP_TIME" property="compTime" />
        <result column="DSGN_ORG" property="dsgnOrg" />
        <result column="CNSTRCT_ORG" property="cnstrctOrg" />
        <result column="SUPER_ORG" property="superOrg" />
        <result column="CBMS_ORG" property="cbmsOrg" />
        <result column="REGULATORY_ORG" property="regulatoryOrg" />
        <result column="BRDG_RATING" property="brdgRating" />
        <result column="APPR_TIME" property="apprTime" />
        <result column="RATING_ID" property="ratingId" />
        <result column="VALID_FLAG" property="validFlag" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="BRDG_CODE" property="brdgCode" />
        <result column="BRDG_NAME" property="brdgName" />
        <result column="CNTR_STAKE" property="cntrStake" />
        <result column="CNTR_STAKE_NUM" property="cntrStakeNum" />
        <result column="CNTR_SENSSION_NUM" property="cntrSenssionNum" />
        <result column="OPRT_ORG_CODE" property="oprtOrgCode" />
        <result column="CREATE_USER_CODE" property="createUserCode" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_CODE" property="updateUserCode" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="LINE_ID" property="lineId" />
        <result column="PLACE" property="place" />
        <result column="BRDG_LINE_TYPE" property="brdgLineType" />
        <result column="FRAME_NUM" property="frameNum" />
        <result column="ROAD_TYPE" property="roadType" />
        <result column="REMARK" property="remark" />
        <result column="TRAFFIC_CODE" property="trafficCode" />
        <result column="BOXROOM_TYPE" property="boxroomType" />
        <result column="BOXROOM_TYPE_VALUE" property="boxroomTypeValue" />
        <result column="MAIN_RAMP_LINE_ID" property="mainRampLineId" />
        <result column="START_OFFSET" property="startOffset" />
        <result column="RAMP_STAKE" property="rampStake" />
        <result column="EXCEL_RESULT" property="excelResult" />
        <result column="START_STAKE" property="startStake" />
        <result column="END_STAKE" property="endStake" />
        <result column="LAST_PRJ_ID" property="lastPrjId" />
        <result column="IN_CATALOGUE" property="inCatalogue" />
        <result column="LOGIC_CNTR_STAKE" property="logicCntrStake" />
        <result column="LOGIC_CNTR_STAKE_NUM" property="logicCntrStakeNum" />
        <result column="OPRT_ORG_NAME" property="oprtOrgName" />
        <result column="MAIN_ID" property="mainId" />
        <result column="MAIN_NAME" property="mainName" />
        <result column="JD" property="jd" />
        <result column="WD" property="wd" />
        <result column="SSF_SET" property="ssfSet" />
        <result column="NEW_ID" property="newId" />
        <result column="YHID" property="yhid" />
        <result column="GRADE_2020" property="grade2020" />
        <result column="GRADE_2019" property="grade2019" />
        <result column="GRADE_2018" property="grade2018" />
        <result column="GRADE_2017" property="grade2017" />
        <result column="GRADE_2016" property="grade2016" />
        <result column="BRDG_TYPE" property="brdgType" />
        <result column="PRJ_CODE" property="prjCode" />
        <result column="PRJ_NAME" property="prjName" />
        <result column="OLD_BRDGCODE" property="oldBrdgcode" />
        <result column="KUANLZQ" property="kuanlzq" />
        <result column="SFXZDM" property="sfxzdm" />
        <result column="F039" property="f039" />
        <result column="GJXZDM" property="gjxzdm" />
        <result column="F042" property="f042" />
        <result column="BZXM" property="bzxm" />
        <result column="GCXZDM" property="gcxzdm" />
        <result column="F099" property="f099" />
        <result column="F103" property="f103" />
        <result column="F107" property="f107" />
        <result column="F109" property="f109" />
        <result column="F105" property="f105" />
        <result column="HAS_THREE_PART" property="hasThreePart" />
        <result column="LAST_PRJ_DATE" property="lastPrjDate" />
        <result column="WSG" property="wsg" />
        <result column="TRANS_TIME" property="transTime" />
        <result column="TRANS_REMARK" property="transRemark" />
        <result column="TRANS_FILE" property="transFile" />
        <result column="LAST_DISP_COMP_DATE" property="lastDispCompDate" />
        <result column="LAST_DISP_ID" property="lastDispId" />
        <result column="ROUTE_CODE_VERSION" property="routeCodeVersion" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="BRDG_CODE_BAK" property="brdgCodeBak" />
        <result column="PHYSICS_CNTR_STAKE_NUM_NEW" property="physicsCntrStakeNumNew" />
        <result column="WIDEN_BRIDGE" property="widenBridge" />
        <result column="RADIUS" property="radius" />
        <result column="HAS_HLA" property="hasHla" />
        <result column="HAS_SCP" property="hasScp" />
        <result column="ID" property="id" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="HLA_DESC" property="hlaDesc" />
        <result column="JGJC" property="jgjc" />
        <result column="CZJC" property="czjc" />
        <result column="HZJC" property="hzjc" />
        <result column="UNDERLOADWARN" property="underloadwarn" />
        <result column="HUMIDITYWARN" property="humiditywarn" />
        <result column="STRAINWARN" property="strainwarn" />
        <result column="ACCELERATIONWARN" property="accelerationwarn" />
        <result column="ROUTE_CODE_BAK" property="routeCodeBak" />
        <result column="DEL_TIME" property="delTime" />
        <result column="DEL_PERSON" property="delPerson" />
        <result column="RECOVER_TIME" property="recoverTime" />
        <result column="RECOVER_PERSON" property="recoverPerson" />
        <result column="XQ_CODE" property="xqCode" />
        <result column="BRDG_CODE_20210911" property="brdgCode20210911" />
        <result column="DRIVING_DIRECTION" property="drivingDirection" />
        <result column="BRDG_CODE_20210914" property="brdgCode20210914" />
        <result column="BRDG_CODE_20210915" property="brdgCode20210915" />
        <result column="BW" property="bw" />
        <result column="JC_CODE" property="jcCode" />
        <result column="REPORT_ROAD_NUM" property="reportRoadNum" />
        <result column="LATITUE_BAK" property="latitueBak" />
        <result column="LONGTITUE_BAK" property="longtitueBak" />
        <result column="TRANS_PROVINCIAL" property="transProvincial" />
        <result column="PLACE_BAK" property="placeBak" />
        <result column="OLD_YHID" property="oldYhid" />
        <result column="DATA_INTEGRITY" property="dataIntegrity" />
        <result column="NEVER_DJ" property="neverDj" />
        <result column="NEVER_DJ_3" property="neverDj3" />
        <result column="TRAFFIC_CODE_BAK" property="trafficCodeBak" />
        <result column="TRAFFIC_CODE_BAK_20220120" property="trafficCodeBak20220120" />
        <result column="TRAFFIC_CODE_BAK_20220221" property="trafficCodeBak20220221" />
        <result column="MORTARRUBBLE" property="mortarrubble" />
        <result column="REINFORCEMENTFORM" property="reinforcementform" />
        <result column="REINFORCEMENTFORMEXTRA" property="reinforcementformextra" />
        <result column="LGHL_MTRL" property="lghlMtrl" />
        <result column="MAINTAIN_GRADE" property="maintainGrade" />
        <result column="XIAOYU40" property="xiaoyu40" />
        <result column="XIAOYU100" property="xiaoyu100" />
        <result column="DAYUDENGYU100" property="dayudengyu100" />
        <result column="HASAV" property="hasav" />
        <result column="BSE" property="bse" />
        <result column="INSPECT_COMPANY" property="inspectCompany" />
        <result column="IS_MAIN" property="isMain" />
        <result column="OLD_ROADBM" property="oldRoadbm" />
        <result column="JWDGX" property="jwdgx" />
        <result column="JWDGXSJ" property="jwdgxsj" />
        <result column="JD_GIS" property="jdGis" />
        <result column="WD_GIS" property="wdGis" />
        <result column="JD_STAKE" property="jdStake" />
        <result column="WD_STAKE" property="wdStake" />
        <result column="DIS_XT" property="disXt" />
        <result column="DIS_STAKE" property="disStake" />
        <result column="XQ_AREA_CODE" property="xqAreaCode" />
        <result column="OLD_ROADBM_20221128" property="oldRoadbm20221128" />
        <result column="TRAFFIC_CODE_20221213" property="trafficCode20221213" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        BRDGRECOG_ID, ROAD_NUM, ROAD_NAME, ROUTE_LVL, SEQ_NO, OLD_HIGHWAY_STAKE, FUNC_TYPE, CNSTRCT_STAKE, DESIGN_STAKE, BRDG_NUM, BRDG_NATURE, LONGITUDE, ACROSS_FTR_TYPE, BRDG_SPAN_GROUP, BRDG_STATUS, LATITUDE, ACROSS_FTR_NAME, PASSAGE_NAME, PASSAGE_NUM, DSGN_LOAD, TRAFFIC_LOAD, BEND_SLOPE, BRDG_DECK, SEC_ROUTE, COMP_TIME, DSGN_ORG, CNSTRCT_ORG, SUPER_ORG, CBMS_ORG, REGULATORY_ORG, BRDG_RATING, APPR_TIME, RATING_ID, VALID_FLAG, RP_INTRVL_ID, BRDG_CODE, BRDG_NAME, CNTR_STAKE, CNTR_STAKE_NUM, CNTR_SENSSION_NUM, OPRT_ORG_CODE, CREATE_USER_CODE, CREATE_TIME, UPDATE_USER_CODE, UPDATE_TIME, LINE_ID, PLACE, BRDG_LINE_TYPE, FRAME_NUM, ROAD_TYPE, REMARK, TRAFFIC_CODE, BOXROOM_TYPE, BOXROOM_TYPE_VALUE, MAIN_RAMP_LINE_ID, START_OFFSET, RAMP_STAKE, EXCEL_RESULT, START_STAKE, END_STAKE, LAST_PRJ_ID, IN_CATALOGUE, LOGIC_CNTR_STAKE, LOGIC_CNTR_STAKE_NUM, OPRT_ORG_NAME, MAIN_ID, MAIN_NAME, JD, WD, SSF_SET, NEW_ID, YHID, GRADE_2020, GRADE_2019, GRADE_2018, GRADE_2017, GRADE_2016, BRDG_TYPE, PRJ_CODE, PRJ_NAME, OLD_BRDGCODE, KUANLZQ, SFXZDM, F039, GJXZDM, F042, BZXM, GCXZDM, F099, F103, F107, F109, F105, HAS_THREE_PART, LAST_PRJ_DATE, WSG, TRANS_TIME, TRANS_REMARK, TRANS_FILE, LAST_DISP_COMP_DATE, LAST_DISP_ID, ROUTE_CODE_VERSION, ROUTE_CODE, BRDG_CODE_BAK, PHYSICS_CNTR_STAKE_NUM_NEW, WIDEN_BRIDGE, RADIUS, HAS_HLA, HAS_SCP, ID, ROUTE_NAME, HLA_DESC, JGJC, CZJC, HZJC, UNDERLOADWARN, HUMIDITYWARN, STRAINWARN, ACCELERATIONWARN, ROUTE_CODE_BAK, DEL_TIME, DEL_PERSON, RECOVER_TIME, RECOVER_PERSON, XQ_CODE, BRDG_CODE_20210911, DRIVING_DIRECTION, BRDG_CODE_20210914, BRDG_CODE_20210915, BW, JC_CODE, REPORT_ROAD_NUM, LATITUE_BAK, LONGTITUE_BAK, TRANS_PROVINCIAL, PLACE_BAK, OLD_YHID, DATA_INTEGRITY, NEVER_DJ, NEVER_DJ_3, TRAFFIC_CODE_BAK, TRAFFIC_CODE_BAK_20220120, TRAFFIC_CODE_BAK_20220221, MORTARRUBBLE, REINFORCEMENTFORM, REINFORCEMENTFORMEXTRA, LGHL_MTRL, MAINTAIN_GRADE, XIAOYU40, XIAOYU100, DAYUDENGYU100, HASAV, BSE, INSPECT_COMPANY, IS_MAIN, OLD_ROADBM, JWDGX, JWDGXSJ, JD_GIS, WD_GIS, JD_STAKE, WD_STAKE, DIS_XT, DIS_STAKE, XQ_AREA_CODE, OLD_ROADBM_20221128, TRAFFIC_CODE_20221213
    </sql>
    <select id="selectQf" resultType="com.hualu.app.module.basedata.dto.BaseStructDto">
        select s.brdgrecog_id as struct_id, s.brdg_name || '(' || s.brdg_line_type || s.frame_num || ')' as struct_Name
        from BCTCMSDB.T_BRDG_BRDGRECOG s
        where s.brdgrecog_id in (select t.brdgrecog_id
                                 from BCTCMSDB.t_brdg_recogmanage t
                                 where t.main_brdgrecog_id = (select t.main_brdgrecog_id
                                                              from BCTCMSDB.t_brdg_recogmanage t
                                                              where t.brdgrecog_id = #{structId}
                                                                and t.valid_flag = 1))
          and s.valid_flag = 1
    </select>

    <select id="selectGrantedBrdgTypes" resultType="com.hualu.app.module.facility.dto.BridgeTypeDto">
        select p.BRDGTYPE_ID as "BRIDGE_TYPE_CODE",p.BRDG_TYPE_NAME as "BRIDGE_TYPE_NAME" from T_BRDG_BRDGTYPE p where exists(
            select 1 from GDGS.FW_RIGHT_USER u inner join GDGS.FW_RIGHT_ORG f on u.ORG_ID=f.ID
                                               inner join GDGS.BASE_ROUTE_LOGIC b on f.ID=b.OPRT_ORG_CODE and b.IS_ENABLE=1
                                               inner join BCTCMSDB.T_BRDG_BRDGRECOG br on br.ROUTE_CODE=b.ROUTE_CODE and br.VALID_FLAG=1
                                               inner join BCTCMSDB.T_BRDG_TOPTYPE t on t.BRDGRECOG_ID=br.BRDGRECOG_ID and t.VALID_FLAG=1
                                               inner join BCTCMSDB.T_BRDG_BRDGTYPE bt on bt.BRDG_CODE=t.BRDG_TYPE and bt.VALID_FLAG=1
            where u.USER_CODE=#{userCode} and b.LINE_CODE=#{lineCode} and  p.BRDGTYPE_ID=bt.P_BRDGTYPE_ID
        ) and p.VALID_FLAG=1

    </select>
</mapper>
