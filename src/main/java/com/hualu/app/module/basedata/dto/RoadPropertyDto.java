package com.hualu.app.module.basedata.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 路产概况
 */
@Accessors(chain = true)
@Data
public class RoadPropertyDto implements Serializable {

    private static final long serialVersionUID = 1L;

    //路段名称
    private String routeName;

    //桥梁数量
    private Integer qlNum;

    //隧道数量
    private Integer sdNum;

    //涵洞数量
    private Integer hdNum;

    //边坡数量
    private Integer bpNum;

    //里程长度
    private Double roadLength;

    //路段字符串
    private String routeCodes;
}
