package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.MtmsTunnelBasic;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.basedata.mapper.TBrdgBrdgrecogMapper;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.basedata.service.TBrdgBrdgrecogService;
import com.hualu.app.module.facility.dto.BridgeTypeDto;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 桥梁行政识别 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@DS("bctcmsDs")
@Service
public class TBrdgBrdgrecogServiceImpl extends ServiceImpl<TBrdgBrdgrecogMapper, TBrdgBrdgrecog> implements TBrdgBrdgrecogService, IBaseStructFace {

    @Override
    public BaseStructDto getId(String structId) {
        TBrdgBrdgrecog tBrdgBrdgrecog = baseMapper.selectById(structId);
        return initDto(tBrdgBrdgrecog,"");
    }

    @Override
    public String getFacilityCat() {
        return "QL";
    }

    @Override
    public Object selectStructPage() {
        Map<String, Object> paramsMap = getStructParamMap("brdgName","brdgrecogId");
        Object appLineCode = paramsMap.get("appLineCode");
        Set<String> routeCodes = null;
        if (ObjectUtil.isNotEmpty(appLineCode)){
            routeCodes = H_DataAuthHelper.selectRouteCodeAuthByLineCode(appLineCode.toString());
            paramsMap.remove("lineId");
            paramsMap.remove("lineCode");
        }else {
            routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        }
        QueryWrapper<TBrdgBrdgrecog> queryWrapper = new QueryWrapper<>();

        Object lineCode = paramsMap.get("lineCode");
        if (ObjectUtil.isNotEmpty(lineCode)){
            paramsMap.put("roadNum",lineCode);
        }

        initWrapper(queryWrapper,routeCodes);
        Object pBrdgType = paramsMap.get("pBrdgType");
        if(pBrdgType !=null){
            queryWrapper.exists("select 1 from   BCTCMSDB.T_BRDG_TOPTYPE t  inner join BCTCMSDB.T_BRDG_BRDGTYPE bt on bt.BRDG_CODE=t.BRDG_TYPE  and bt.VALID_FLAG=1 where  t.BRDGRECOG_ID=T_BRDG_BRDGRECOG.BRDGRECOG_ID and t.VALID_FLAG=1 and bt.P_BRDGTYPE_ID='"+pBrdgType+"'");
        }
        setStakeScopeQuery(paramsMap,"LOGIC_CNTR_STAKE_NUM",queryWrapper);
        initBrdgLineType(paramsMap);
        H_BatisQuery.setFieldValue2In(queryWrapper,paramsMap,TBrdgBrdgrecog.class);

        queryWrapper.orderByAsc("T_BRDG_BRDGRECOG.ROAD_NUM").orderByAsc("T_BRDG_BRDGRECOG.LOGIC_CNTR_STAKE_NUM");
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();

        String queryType = paramsMap.getOrDefault("queryType","").toString();

        if (iPage.getRecords().size() != 0){
            iPage.getRecords().forEach(row->{
                TBrdgBrdgrecog item = (TBrdgBrdgrecog) row;
                BaseStructDto dto = initDto(item,queryType);
                dtos.add(dto);
            });
        }
        iPage.setRecords(dtos);
        return returnPage(iPage);
    }

    @Override
    public List<BaseStructDto> listByRouteCodes(Set<String> routeCodes) {
        QueryWrapper<TBrdgBrdgrecog> queryWrapper = new QueryWrapper<>();
        initWrapper(queryWrapper,routeCodes);
        queryWrapper.lambda().select(TBrdgBrdgrecog::getBrdgrecogId,TBrdgBrdgrecog::getBrdgName,TBrdgBrdgrecog::getRouteCode).orderByAsc(TBrdgBrdgrecog::getRoadNum,TBrdgBrdgrecog::getLogicCntrStakeNum);
        List<TBrdgBrdgrecog> tBrdgBrdgrecogs = baseMapper.selectList(queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();
        tBrdgBrdgrecogs.forEach(row->{
            BaseStructDto structDto = new BaseStructDto().setStructId(row.getBrdgrecogId()).setStructName(row.getBrdgName()).setRouteCode(row.getRouteCode());
            dtos.add(structDto);
        });
        return dtos;
    }

    public void initWrapper(QueryWrapper<TBrdgBrdgrecog> queryWrapper,Set<String> routeCodes) {
        queryWrapper.eq("VALID_FLAG",1);
        queryWrapper.in(CollectionUtil.isNotEmpty(routeCodes),"route_code",routeCodes);
        queryWrapper.exists("select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.MAIN_BRDGRECOG_ID=m.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID=T_BRDG_BRDGRECOG.BRDGRECOG_ID and m.VALID_FLAG=1");
    }

    @Override
    public List<BaseStructDto> listByStructIds(List<String> structIds) {
        List<TBrdgBrdgrecog> allList = Lists.newArrayList();
        List<List<String>> partition = ListUtil.partition(structIds, 1000);
        partition.forEach(rows->{
            List<TBrdgBrdgrecog> slopeInfoList = lambdaQuery().in(TBrdgBrdgrecog::getBrdgrecogId, rows)
                    .exists("select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.MAIN_BRDGRECOG_ID=m.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID=T_BRDG_BRDGRECOG.BRDGRECOG_ID and m.VALID_FLAG=1").orderByAsc(TBrdgBrdgrecog::getRoadNum,TBrdgBrdgrecog::getLogicCntrStakeNum)
                .list();
            ;
            allList.addAll(slopeInfoList);
        });
        List<BaseStructDto> dtos = Lists.newArrayList();
        for (TBrdgBrdgrecog info : allList) {
            BaseStructDto baseStructDto = initDto(info,null);
            dtos.add(baseStructDto);
        }
        return dtos;
    }


    BaseStructDto initDto(TBrdgBrdgrecog item,String queryType){
        if (item == null){
            throw new BaseException("未找到结构物");
        }
        BaseStructDto dto = new BaseStructDto();
        dto.setLineId(item.getLineId());
        dto.setStructId(item.getBrdgrecogId());
        dto.setStructCode(item.getTrafficCode());
        dto.setRlCntrStake(item.getLogicCntrStakeNum());
        dto.setStructName(item.getBrdgName()+"("+dto.getDisplayStake()+")");
        //dto.setStructName(H_BasedataHepler.zzBrdgName(queryType,item.getBrdgLineType(),item.getBrdgName()));
        dto.setFrameNum(H_BasedataHepler.zzBrdgFrameNum(item.getBrdgLineType(),item.getFrameNum()));
        dto.setRpIntrvlId(item.getRpIntrvlId());
        dto.setRouteCode(item.getRouteCode());
        dto.setRouteVersion(item.getRouteCodeVersion());
        dto.setRampId(item.getMainRampLineId());
        dto.setX(item.getLongitude());
        dto.setY(item.getLatitude());
        dto.setOprtOrgCode(item.getOprtOrgCode());
        return dto;
    }


    /**
     * 初始化桥梁方向参数
     * @param paramsMap
     */
    public void initBrdgLineType(Map paramsMap){
        String lineDirect = paramsMap.getOrDefault("lineDirect", "").toString();
        if (StringUtils.isNotBlank(lineDirect)){
            String brdgLineTypes = "";
            if ("1".equals(lineDirect)){
                brdgLineTypes = "L,K,X";
            }else if ("2".equals(lineDirect)){
                brdgLineTypes = "R,K,X";
            }else if ("4".equals(lineDirect)){
                brdgLineTypes = "Z";
            }
            paramsMap.put("brdgLineTypes",brdgLineTypes);
        }
    }

    @Override
    public List<BaseStructDto> selectQf(String structId) {
        return baseMapper.selectQf(structId);
    }

  @Override
  public List<BridgeTypeDto> selectGrantedBrdgTypes(String lineCode) {
      String userCode = CustomRequestContextHolder.getUserCode();
      return baseMapper.selectGrantedBrdgTypes(userCode,lineCode);
  }
}
