<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.basedata.mapper.SlopeAttentionExtraMapper">
  <resultMap id="BaseResultMap" type="com.hualu.app.module.basedata.entity.SlopeAttentionExtra">
    <!--@mbg.generated-->
    <!--@Table MEMSDB.SLOPE_ATTENTION_EXTRA-->
    <id column="SLOPE_ID" jdbcType="VARCHAR" property="slopeId" />
    <result column="SLOPE_NAME" jdbcType="VARCHAR" property="slopeName" />
    <result column="LINE_NAME" jdbcType="VARCHAR" property="lineName" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="CENTER_STAKE" jdbcType="VARCHAR" property="centerStake" />
    <result column="SLOPE_LENGTH" jdbcType="DECIMAL" property="slopeLength" />
    <result column="SLOPE_TYPE" jdbcType="VARCHAR" property="slopeType" />
    <result column="MAINTENANCE_GRADE" jdbcType="VARCHAR" property="maintenanceGrade" />
    <result column="WHETHER_IMPORTANT" jdbcType="VARCHAR" property="whetherImportant" />
    <result column="SLOPE_LEVEL" jdbcType="VARCHAR" property="slopeLevel" />
    <result column="REFORM" jdbcType="VARCHAR" property="reform" />
    <result column="EVAL_GRADE" jdbcType="VARCHAR" property="evalGrade" />
    <result column="JXD" jdbcType="VARCHAR" property="jxd" />
    <result column="MANAGE_COMPANY" jdbcType="VARCHAR" property="manageCompany" />
    <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude" />
    <result column="LATITUDE" jdbcType="DECIMAL" property="latitude" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SLOPE_ID, SLOPE_NAME, LINE_NAME, LINE_DIRECT, CENTER_STAKE, SLOPE_LENGTH, SLOPE_TYPE, 
    MAINTENANCE_GRADE, WHETHER_IMPORTANT, SLOPE_LEVEL, REFORM, EVAL_GRADE, JXD, MANAGE_COMPANY, 
    LONGITUDE, LATITUDE
  </sql>
</mapper>