package com.hualu.app.module.basedata.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

/**
 * 营运路段区间 前端控制器
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@RestController
@RequestMapping("/baseRouteIntrvlLogic")
public class BaseRouteIntrvlLogicController {

  @Resource
  private BaseRouteLogicService baseRouteLogicService;

  /**
   * 根据路线编码，查询路段名称(routeName)
   * @param lineCode
   * @return
   */
  @GetMapping("/getRouteListByLineCode")
  public RestResult<List<BaseRouteLogic>> getRouteByLineCode(String lineCode) {
    return RestResult.success(baseRouteLogicService.getRouteListByLineCode(lineCode));
  }

  /**
   * 根据路线编码，查询路段线路名称(roadName)
   * @param lineCode
   * @return
   */
  @GetMapping("listRoad")
  public RestResult<List<BaseRouteLogic>> listRoadNameByLineCode(String lineCode) {
    return RestResult.success(baseRouteLogicService.listRoad(lineCode));
  }
}
