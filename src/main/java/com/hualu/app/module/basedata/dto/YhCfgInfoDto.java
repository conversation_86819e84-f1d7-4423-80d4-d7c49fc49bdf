package com.hualu.app.module.basedata.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class YhCfgInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private String objectid;

    @ApiModelProperty(value = "国家高速名称")
    @TableField("NATION_NAME")
    private String nationName;

    @ApiModelProperty(value = "路段编号")
    @TableField("NATION_RD_NO")
    private String nationRdNo;

    @ApiModelProperty(value = "路段名称")
    @TableField("ROAD_NAME")
    private String roadName;

    @ApiModelProperty(value = "路段编号")
    @TableField("ROAD_NO")
    private String roadNo;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STACK")
    private String startStack;

    @ApiModelProperty(value = "终点桩号")
    @TableField("END_STACK")
    private String endStack;

    //路段里程
    private Double routeLength;

    /**
     * 路径地址
     */
    private String roadPath;

    /**
     * 管养单位编码
     */
    private String oprtOrgCode;

    /**
     * 管养单位名称
     */
    private String oprtOrgName;
}
