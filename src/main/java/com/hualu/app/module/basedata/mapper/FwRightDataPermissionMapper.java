package com.hualu.app.module.basedata.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.dto.RoadPropertyDto;
import com.hualu.app.module.basedata.entity.FwRightDataPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 数据权限中间表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface FwRightDataPermissionMapper extends BaseMapper<FwRightDataPermission> {

    /**
     * 查询主线路段
     * @param codes
     * @param lineId
     * @param lineDirect
     * @param rlStake
     * @return
     */
    List<BaseRouteDto> selectMainRoutes(@Param("codes") List<String> codes, String lineId, String lineDirect, Double rlStake);


    List<BaseRouteDto> selectRampRoutes(@Param("codes") List<String> codes, String lineId, Double rlStake);

    List<BaseRouteDto> selectRouteInfo(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<RoadPropertyDto> getRoadLength(@Param("codes") List<String> codes);
}
