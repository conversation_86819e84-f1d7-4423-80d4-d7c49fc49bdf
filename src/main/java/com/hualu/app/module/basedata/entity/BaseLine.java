package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 路线
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseLine对象", description="路线")
public class BaseLine implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "路线ID")
    @TableId("LINE_ID")
    private String lineId;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "路线全称")
    @TableField("LINE_ALLNAME")
    private String lineAllname;

    @ApiModelProperty(value = "路线简称")
    @TableField("LINE_SNAME")
    private String lineSname;

    @ApiModelProperty(value = "具体路线描述")
    @TableField("LINE_DESC")
    private String lineDesc;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "路线对应编码")
    @TableField("LINE_COUNTER_CODE")
    private String lineCounterCode;

    @ApiModelProperty(value = "是否启用（0未启用1启用）")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "是否删除（0未删除1删除）")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "桩号体系编码（C类）")
    @TableField("STAKE_TYPE")
    private String stakeType;

    @ApiModelProperty(value = "路线里程")
    @TableField("LINE_LENGTH")
    private Double lineLength;

    @ApiModelProperty(value = "是否是最新国高网命名路线(1是，0不是)")
    @TableField("IS_NEW_GGW")
    private String isNewGgw;


}
