package com.hualu.app.module.basedata.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.facility.dto.BridgeTypeDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 桥梁行政识别 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface TBrdgBrdgrecogMapper extends BaseMapper<TBrdgBrdgrecog> {

    List<BaseStructDto> selectQf(String structId);

    List<BridgeTypeDto> selectGrantedBrdgTypes(@Param("userCode") String userCode, @Param("lineCode") String lineCode);
}
