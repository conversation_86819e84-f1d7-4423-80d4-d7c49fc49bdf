package com.hualu.app.module.basedata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.dto.StakeRangeDto;
import com.hualu.app.module.basedata.entity.BaseLine;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 路线 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
public interface BaseLineService extends IService<BaseLine> {

    /**
     * 查询路线对象，用于维修工程记录回显
     * @param lineId
     * @return
     */
    BaseLine selectLine(String lineId);

    /**
     * 查询所有路线
     * @return
     */
    List<BaseLine> listAllLines();

    /**
     * 查找所有路线，组装成Map
     * @return
     */
    Map<String,BaseLine> getAllLineMap();
    /**
     * 用户授权路线
     * @return
     */
    List<BaseLine> selectGrantLines();

    /**
     * 获取桩号范围
     * @param lineId
     * @param lineDirect
     * @return
     */
    StakeRangeDto getStakeRange(String lineId, String lineDirect);

    /**
     * 获取路线名称
     * @param lineCode
     * @return
     */
    String getLineName(String lineCode);

    Map<String,String> resolveLineNamesByCodes(Set<String> codes);
}
