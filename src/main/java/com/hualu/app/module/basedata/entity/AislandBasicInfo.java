package com.hualu.app.module.basedata.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 人工岛基础信息表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AislandBasicInfo对象", description="人工岛基础信息表")
public class AislandBasicInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人工岛ID")
    @TableId("ISLAND_ID")
    private String islandId;

    @ApiModelProperty(value = "人工岛名称")
    @TableField("ISLAND_NAME")
    private String islandName;

    @ApiModelProperty(value = "陆域面积(m2）")
    @TableField("ISLAND_AREA")
    private Double islandArea;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "路线方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "管养单位")
    @TableField("OPT_ORG_ID")
    private String optOrgId;

    @ApiModelProperty(value = "项目单位")
    @TableField("PRJ_ORG_ID")
    private String prjOrgId;

    @ApiModelProperty(value = "X坐标")
    @TableField("GIS_X")
    private Double gisX;

    @ApiModelProperty(value = "Y坐标")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "起点桩号（中文）")
    @TableField("START_STAKE_CN")
    private String startStakeCn;

    @ApiModelProperty(value = "起点桩号（数值）")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "终点桩号（中文）")
    @TableField("END_STAKE_CN")
    private String endStakeCn;

    @ApiModelProperty(value = "终点桩号（数值）")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "中心桩号（中文）")
    @TableField("CENTER_STAKE_CN")
    private String centerStakeCn;

    @ApiModelProperty(value = "中心桩号（数值）")
    @TableField("CENTER_STAKE_NUM")
    private Double centerStakeNum;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "修改人")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableLogic
    @ApiModelProperty(value = "删除状态（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;


}
