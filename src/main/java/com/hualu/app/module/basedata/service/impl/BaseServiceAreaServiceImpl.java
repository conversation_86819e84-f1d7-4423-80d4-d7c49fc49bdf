package com.hualu.app.module.basedata.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseServiceArea;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.basedata.entity.MtmsTunnelBasic;
import com.hualu.app.module.basedata.mapper.BaseServiceAreaMapper;
import com.hualu.app.module.basedata.service.BaseServiceAreaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 服务区 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-16
 */
@Service
@DS("gdgsDs")
public class BaseServiceAreaServiceImpl extends ServiceImpl<BaseServiceAreaMapper, BaseServiceArea> implements BaseServiceAreaService, IBaseStructFace {

    @Override
    public BaseStructDto getId(String structId) {
        BaseServiceArea baseServiceArea = baseMapper.selectById(structId);
        return initDto(baseServiceArea);
    }

    @Override
    public String getFacilityCat() {
        return "FWQ";
    }

    @Override
    public Object selectStructPage() {
        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        Map<String, Object> paramsMap = getStructParamMap("slopeName","slopeId");
        QueryWrapper<BaseServiceArea> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_deleted","0");
        queryWrapper.in("route_code",routeCodes);
        queryWrapper.lambda().orderByAsc(BaseServiceArea::getLineCode,BaseServiceArea::getLogicCntrStakeNum);
        H_BatisQuery.setFieldValue(queryWrapper,paramsMap,BaseServiceArea.class);
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        List<BaseStructDto> dtos = Lists.newArrayList();

        if (iPage.getRecords().size() != 0){
            iPage.getRecords().forEach(row->{
                BaseServiceArea item = (BaseServiceArea) row;
                BaseStructDto dto = initDto(item);
                dtos.add(dto);
            });
        }
        iPage.setRecords(dtos);
        return returnPage(iPage);
    }

    private BaseStructDto initDto(BaseServiceArea item) {
        {
            if (item == null) {
                throw new BaseException("未找到结构物");
            }
            BaseStructDto dto = new BaseStructDto();
            dto.setStructId(item.getServiceId());
            dto.setStructCode(item.getServiceCode());
            dto.setRlCntrStake(item.getLogicCntrStakeNum());
            dto.setStructName(item.getServiceName()+"("+dto.getDisplayStake()+")");
            dto.setRpIntrvlId(item.getRpIntrvlId());
            dto.setCntrStake(item.getCntrStakeNum());
            dto.setRouteCode(item.getRouteCode());
            dto.setRouteVersion(item.getRouteVersion());
            dto.setRampId(item.getMainRampLineId());
            dto.setX(item.getGisX());
            dto.setY(item.getGisY());
            return dto;
        }
    }
}
