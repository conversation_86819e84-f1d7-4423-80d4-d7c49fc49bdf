package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
* 单号配置
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("DM_CODE_SET")
@ApiModel(value="DmCodeSet对象", description="单号配置")
public class DmCodeSet implements Serializable {

    
    @NotBlank(message="[]不能为空")
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("")
    @TableField("ID")
    private String id;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("组织机构编码")
    @TableField("ORG_CODE")
    private String orgCode;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("创建人")
    @TableField("CREATE_USER")
    private String createUser;
    
    @ApiModelProperty("创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("最后一次更新者")
    @TableField("UPDATE_USER")
    private String updateUser;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("编号")
    @TableField("CODE")
    private String code;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("应用范围 (日常巡查 、经常检查 、任务单 、验收单)")
    @TableField("RANGE")
    private String range;
    
    @ApiModelProperty("是否已删除")
    @TableField("IS_DELETE")
    private Integer isDelete;
}
