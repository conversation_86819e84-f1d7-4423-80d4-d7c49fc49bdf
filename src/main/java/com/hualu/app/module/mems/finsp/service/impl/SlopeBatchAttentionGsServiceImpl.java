package com.hualu.app.module.mems.finsp.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.entity.SlopeBatchAttentionGs;
import com.hualu.app.module.mems.finsp.mapper.SlopeBatchAttentionGsMapper;
import com.hualu.app.module.mems.finsp.service.SlopeBatchAttentionGsService;
import org.springframework.stereotype.Service;
@Service
@DS("hsmsDs")
public class SlopeBatchAttentionGsServiceImpl extends ServiceImpl<SlopeBatchAttentionGsMapper, SlopeBatchAttentionGs> implements SlopeBatchAttentionGsService{

}
