package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmFinspResult;
import com.hualu.app.module.mems.nminsp.vo.NmFinspRel;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 新版经常检查记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface NmFinspRecordMapper extends BaseMapper<NmFinspRecord> {

    List<NmFinspRecord> selectViewRecord(@Param("dssIds") List<String> dssIds);

  List<Map> findBySql(@Param("sql") String sql);

  void saveOrUpdateBySql(@Param("insertDss") String insertDss);

  List<NmFinspRecord> findNmFinspRecordBySql(@Param("sql") String sql);

  List<NmFinspResult> findNmFinspResultBySql(@Param("sql") String sql);

  List<Map> getLastfinspIdByStruct(@Param("structId") String structId, @Param("facilityCat") String facilityCat);

  void saveNmFinspRecordOfQL(@Param("finspId") String finspId, @Param("oldFinspId") String oldFinspId);

  void saveNmFinspRecordOfOthers(@Param("finspId") String finspId, @Param("oldFinspId") String oldFinspId);

  void copyFinspFetail2NewOne(@Param("oldfinspId") String oldfinspId, @Param("newFinspId") String newFinspId);

  List<Map> findNmFinspResultSet(@Param("orgCode") String orgCode, @Param("userCode") String userCode);

  List<NmFinspRecord> findNmFinspRecord(@Param("facilityCat") String facilityCat, @Param("finspId") String finspId);

  void deleteInspectionPhotoById(@Param("id") String id);

  void saveInspectionPhoto(@Param("finspId") String finspId, @Param("id") String id);

  String getInspectionPhotos(@Param("finspId") String finspId);

  List<Map<String, String>> selectOldPartList(@Param("partsCode") Set<String> partsCode);

  List<Map<String, String>> selectOldQlPoistionList(@Param("collect") Set<String> collect);

  List<NmFinspRecord> selectRecordDtlByFinspId(@Param("finspId") String finspId);

  List<NmFinspRecord> findUndoDssByFinspId(@Param("oldfinspId") String oldfinspId);

  List<NmFinspRel> getLastfinspIdListByStructList(@Param("collectStructIds") String collectStructIds, @Param("facilityCat") String facilityCat);
}
