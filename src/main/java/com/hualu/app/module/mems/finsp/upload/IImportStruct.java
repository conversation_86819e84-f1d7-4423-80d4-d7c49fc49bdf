package com.hualu.app.module.mems.finsp.upload;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.word.WordUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.data.style.Style;
import com.google.common.collect.Maps;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

public interface IImportStruct {

    String getFacilityCat();

    List<PcProjectScope> doImport(PcProject pcProject, File uploadFile);

    List<BaseNearStructDto> toBaseNearStructDto(List<PcProjectScope> pcProjectScopes);

    /**
     * 导出单个word表
     * @param pcFinsp
     * @param qlType ps:桥面排水 ，szz:水中桩
     * @return
     */
    String exportWord( PcFinsp pcFinsp,String qlType);

    /**
     * 导出zip压缩包
     * @param filePath
     * @param finspIds
     * @param prjId
     */
    void exportWordByZip(String filePath,List<String> finspIds,String prjId);


    /**
     * 桥梁和涵洞word导出模版
     * @param fileDir
     * @param dto
     * @param templateType
     * @param name
     */
    @SneakyThrows
    default void exportWordWithQLAndHD(String fileDir, PcProjectExportDto dto, String templateType, String name){
        Map xxMap = BeanUtil.toBean(dto, Map.class);
        initRadio(xxMap);
        String fileName = File.separator + dto.getFinspCode()+name + ".docx";
        //ClassPathResource classPathResource = new ClassPathResource("excel/pcFinsp/"+templateType+".docx");
        InputStream resourceAsStream = WordUtil.class.getClassLoader().getResourceAsStream("excel/pcFinsp/"+templateType+".docx");
        File file = new File(fileDir + fileName);
        if (!FileUtil.exist(file.getParent())){
            FileUtil.mkParentDirs(file);
        }
        ConfigureBuilder builder = Configure.builder();
        XWPFTemplate template = XWPFTemplate.compile(resourceAsStream, builder.build()).render(xxMap);
        try(OutputStream outputStream = Files.newOutputStream(file.toPath())){
            template.writeAndClose(outputStream);
        }
        resourceAsStream.close();
    }

    /**
     * 桥梁和涵洞excel导出模版
     * @param fileDir
     * @param dtos
     * @param templateType
     * @param name
     */
    @SneakyThrows
    default void exportExcelWithQLAndHD(String fileDir, List<PcProjectExportDto> dtos, String templateType, String name){
        String excelTemplatPath = "excel/pcFinsp/"+templateType+".xlsx";
        TemplateExportParams exportParams = new TemplateExportParams(excelTemplatPath,true,null);
        Map<String, Object> xxMap = Maps.newHashMap();
        xxMap.put("recList",dtos);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,xxMap);
        String fileName = File.separator +name + ".xlsx";
        File file = new File(fileDir + fileName);
        if (FileUtil.exist(file.getParent())){
            FileUtil.mkdir(file.getParent());
        }
        try(OutputStream outputStream = Files.newOutputStream(file.toPath())){
            workbook.write(outputStream);
            workbook.close();
        }
    }

    /**
     * 初始化单选框值
     * @param xxMap
     */
    default void initRadio(Map xxMap){
        String attr4 = Convert.toStr(xxMap.get("attr4"),"");
        xxMap.put("attr4Yes",getRadioStr(attr4,true));
        xxMap.put("attr4No",getRadioStr(attr4,false));

        String attr6 = Convert.toStr(xxMap.get("attr6"),"");
        xxMap.put("attr6Yes",getRadioStr(attr6,true));
        xxMap.put("attr6No",getRadioStr(attr6,false));

        String attr9 = Convert.toStr(xxMap.get("attr9"),"");
        xxMap.put("attr9Yes",getRadioStr(attr9,true));
        xxMap.put("attr9No",getRadioStr(attr9,false));

        String attr11 = Convert.toStr(xxMap.get("attr11"),"");
        xxMap.put("attr11Yes",getRadioStr(attr11,true));
        xxMap.put("attr11No",getRadioStr(attr11,false));

        String attr13 = Convert.toStr(xxMap.get("attr13"),"");
        xxMap.put("attr13Yes",getRadioStr(attr13,true));
        xxMap.put("attr13No",getRadioStr(attr13,false));
    }

    default Object getRadioStr(String text,boolean yes){
        if(StrUtil.isBlank(text)){
            return "○";
        }
        if (("否".equals(text) && !yes) || ("是".equals(text) && yes)){
            return new TextRenderData("8",new Style("Wingdings 2",12));
        }
        return "○";
    }
}
