package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.dto.FailDinspDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspCommonMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspCommonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.vo.NmDinspExtraInfo;
import com.hualu.app.module.mems.nminsp.vo.NmDinspFacilityCatGroupVo;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.tg.dev.api.context.CustomRequestContextHolder;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 新版日常巡查公共单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Service
public class NmDinspCommonServiceImpl extends ServiceImpl<NmDinspCommonMapper, NmDinspCommon> implements NmDinspCommonService {

    private static final String INSP_TIME = "09:00:00-18:00:00";

    @Autowired
    private NmDinspRecordService recordService;

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    FwRightUserService userService;

    @Autowired
    DssImageService imageService;

    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateRecord(NmDinspRecord entity) {
        //主单已经存在，直接修改病害信息
        if (StrUtil.isNotBlank(entity.getDinspId())){
            recordService.saveOrUpdateRecordForApp(entity);
            return;
        }
        if (StrUtil.isBlank(entity.getCommonDinspId())){
            throw new BaseException("commonDinspId不能为空");
        }
        String dinspId = getDinspId(entity);
        if (StrUtil.isNotBlank(dinspId)){
            entity.setDinspId(dinspId);
            recordService.saveOrUpdateRecordForApp(entity);
            return;
        }
        // 主单不存在
        NmDinspCommon nmDinspCommon = getById(entity.getCommonDinspId());
        nmDinspCommon.setFacilityCat(entity.getFacilityCat()).setStructName(entity.getStructName()).setStructId(entity.getStructId());
        // 新建主单
        NmDinsp dinsp = nmDinspService.createDinsp(nmDinspCommon);
        entity.setDinspId(dinsp.getDinspId());
        recordService.saveOrUpdateRecordForApp(entity);
    }

    /**
     * 根据病害信息，查询日常巡查主单
     * @param entity
     * @return
     */
    private String getDinspId(NmDinspRecord entity) {
        LambdaQueryWrapper<NmDinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmDinsp::getCommonDinspId,entity.getCommonDinspId())
                .eq(NmDinsp::getStructId,entity.getStructId());
        NmDinsp nmDinsp = nmDinspService.getOne(queryWrapper, false);
        if (nmDinsp == null){
            return null;
        }
        return nmDinsp.getDinspId();
    }

    @Override
    public NmDinspCommon initNmDinspCommon(String orgIdString) {
        NmDinspCommon nmDinsp = baseMapper.lastNmDinspCommon(orgIdString);
        if (nmDinsp == null){
            nmDinsp = new NmDinspCommon().setWeather("01");
        }else {
            nmDinsp.setDinspId(null);
            nmDinsp.setDssNum(0);
            nmDinsp.setInspFrequency(1);
        }
        if (StrUtil.isBlank(nmDinsp.getSearchDept())){
            nmDinsp.setSearchDept(userService.getDeptName(CustomRequestContextHolder.getUserId()));
        }
        nmDinsp.setInspPerson(CustomRequestContextHolder.getUserName());
        nmDinsp.setMntOrgId(orgIdString);
        //路面需要用到巡查时间
        nmDinsp.setInspTime(INSP_TIME);
        nmDinsp.setMntOrgNm(CustomRequestContextHolder.getOrgName());
        nmDinsp.setInspDate(new Date());
        nmDinsp.setCreateTime(null).setUpdateTime(null).setCreateUserId(null).setUpdateUserId(null);
        return nmDinsp;
    }

    @Override
    public String bindDinspCommon(NmDinsp nmDinsp) {
        if (StrUtil.isNotBlank(nmDinsp.getCommonDinspId())){
            return nmDinsp.getCommonDinspId();
        }
        LambdaQueryWrapper<NmDinspCommon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmDinspCommon::getMntOrgId,nmDinsp.getMntOrgId())
                .eq(NmDinspCommon::getLineCode,nmDinsp.getLineCode())
                .eq(NmDinspCommon::getCreateUserId,CustomRequestContextHolder.getUserId())
                .eq(NmDinspCommon::getInspDate,nmDinsp.getInspDate());
        NmDinspCommon one = getOne(queryWrapper,false);
        if (one != null){
            return one.getDinspId();
        }
        return null;
    }

    @SneakyThrows
    @Override
    public List<NmDinspFacilityCatGroupVo> listNmDinspFacilityGroupVo(String commonDinspId) {

        // 总单数统计
        CompletableFuture<List<NmDinspFacilityCatGroupVo>> totalFuture = CompletableFuture.supplyAsync(() -> {
            List<NmDinspFacilityCatGroupVo> groupVos = baseMapper.countTotalCount(commonDinspId);
            return groupVos;
        });

        String userTaskSql = H_WorkFlowHelper.getUserTaskSql(0, "nm_dinsp");
        CompletableFuture<List<NmDinspFacilityCatGroupVo>> todoFuture = CompletableFuture.supplyAsync(() -> {
            List<NmDinspFacilityCatGroupVo> groupVos = baseMapper.countTodoCount(commonDinspId,userTaskSql);
            return groupVos;
        });

        CompletableFuture.allOf(totalFuture, todoFuture).join();

        List<NmDinspFacilityCatGroupVo> totalVos = totalFuture.get();
        List<NmDinspFacilityCatGroupVo> todoVos = todoFuture.get();

        for (NmDinspFacilityCatGroupVo item : totalVos) {
            item.setCommonDinspId(commonDinspId);
            todoVos.stream().filter(e -> item.getFacilityCat().equals(e.getFacilityCat())).findFirst()
                    .ifPresent(e->item.setTodoCount(e.getTodoCount()));
        }
        return totalVos;
    }

    @Override public IPage<NmDinspCommon> selectPageFor(
        Page<NmDinspCommon> page,
        QueryWrapper<NmDinspCommon> queryWrapper,
        String taskSql
    ) {
        queryWrapper.eq("DEL_FLAG", 0);
        IPage<NmDinspCommon> p = baseMapper.selectPageFor(page, queryWrapper, taskSql);
        List<String> ids =
            p.getRecords().stream().map(NmDinspCommon::getDinspId).collect(Collectors.toList());

        Map<String, List<String>> imageMap = null;
        if (ObjectUtil.isNotEmpty(ids)) {
            imageMap = imageService.mapByDssIds(ids.stream().collect(Collectors.toSet()));
        }

        Map<String, NmDinspExtraInfo> map = listNmDinspExtraInfo(ids)
            .stream().collect(Collectors.toMap(NmDinspExtraInfo::getCommonDinspId, s -> s));
        for (NmDinspCommon record : p.getRecords()) {
            NmDinspExtraInfo extraInfo =
                map.getOrDefault(record.getDinspId(), new NmDinspExtraInfo());
            record.setDssNum(extraInfo.getDssNum());
            record.setTodoNum(extraInfo.getTodoCount());
            record.setTotalNum(extraInfo.getTotalCount());
            if (imageMap != null) {
                record.setFileIds(imageMap.getOrDefault(record.getDinspId(), Lists.newArrayList()).stream().collect(Collectors.joining(",")));
            }
        }
        return p;
    }

    @Override
    public List<FailDinspDto> listFailDinsp() {
        List<FailDinspDto> dinspDtos = baseMapper.listFailDinsp();
        return dinspDtos;
    }

    @Override public List<NmDinspExtraInfo> listNmDinspExtraInfo(List<String> commonDinspIdList) {
        if (CollectionUtils.isEmpty(commonDinspIdList)){
            return Collections.emptyList();
        }

        String sql = H_WorkFlowHelper.getUserTaskSql(0, "b");
        Map<String, Integer> todoCountMap =
            baseMapper.getNmDinspCommonToto(commonDinspIdList, sql)
                .stream()
                .collect(Collectors.toMap(NmDinspExtraInfo::getCommonDinspId,
                    NmDinspExtraInfo::getTodoCount));

        Map<String, Integer> totalCountMap =
            baseMapper.getNmDinspCommonTotal(commonDinspIdList)
                .stream()
                .collect(Collectors.toMap(NmDinspExtraInfo::getCommonDinspId,
                    NmDinspExtraInfo::getTotalCount));

        Map<String, Integer> dssNumMap =
            baseMapper.getNmDinspCommonDss(commonDinspIdList)
                .stream()
                .collect(Collectors.toMap(NmDinspExtraInfo::getCommonDinspId,
                    NmDinspExtraInfo::getDssNum));

        List<NmDinspExtraInfo> resultList = new ArrayList<>();
        for (String id : commonDinspIdList) {
            NmDinspExtraInfo item = new NmDinspExtraInfo();
            item.setCommonDinspId(id);
            item.setTotalCount(totalCountMap.getOrDefault(id, 0));
            item.setTodoCount(todoCountMap.getOrDefault(id, 0));
            item.setDssNum(dssNumMap.getOrDefault(id, 0));
            resultList.add(item);
        }
        return resultList;
    }
}
