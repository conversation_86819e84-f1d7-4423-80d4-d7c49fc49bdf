package com.hualu.app.module.mems.finsp.service;

import com.hualu.app.module.mems.finsp.entity.DmFinspResultSet;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DmFinspResultSetService extends IService<DmFinspResultSet> {

    /**
     * 是否拥有修改结论的权限
     * @param mngOrgId
     * @param userId
     * @return
     */
    boolean isUpdateResultSet(String mngOrgId, String userId);
}
