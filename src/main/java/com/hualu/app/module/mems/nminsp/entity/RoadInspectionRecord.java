package com.hualu.app.module.mems.nminsp.entity;

import cn.afterturn.easypoi.entity.ImageEntity;

import java.math.BigDecimal;
import java.util.Date;

public class RoadInspectionRecord {
    // 线路编号
    private String lineCode;

    // 桩号（建议使用BigDecimal处理浮点精度）
    private BigDecimal stake;

    // 检查日期
    private Date inspDate;

    // 路面类型（示例："路面"+DSS_TYPE_NAME）
    private String pavementType;

    // 检查人员
    private String inspPerson;

    // 文件存储路径
    private String fileEntityPath;

    private ImageEntity imageEntity;

    private String dinspCode;

    private String structId;

    private String structStake;


    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public BigDecimal getStake() {
        return stake;
    }

    public void setStake(BigDecimal stake) {
        this.stake = stake;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getPavementType() {
        return pavementType;
    }

    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }

    public String getInspPerson() {
        return inspPerson;
    }

    public void setInspPerson(String inspPerson) {
        this.inspPerson = inspPerson;
    }

    public String getFileEntityPath() {
        return fileEntityPath;
    }

    public void setFileEntityPath(String fileEntityPath) {
        this.fileEntityPath = fileEntityPath;
    }

    public ImageEntity getImageEntity() {
        return imageEntity;
    }

    public void setImageEntity(ImageEntity imageEntity) {
        this.imageEntity = imageEntity;
    }

    public String getDinspCode() {
        return dinspCode;
    }

    public void setDinspCode(String dinspCode) {
        this.dinspCode = dinspCode;
    }

    public String getStructId() {
        return structId;
    }

    public void setStructId(String structId) {
        this.structId = structId;
    }

    public String getStructStake() {
        return structStake;
    }

    public void setStructStake(String structStake) {
        this.structStake = structStake;
    }
}
