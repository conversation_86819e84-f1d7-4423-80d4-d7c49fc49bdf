package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 车辆设备状态信息表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_VEHICLE_DEVICE")
@ApiModel(value="HighwayVehicleDevice对象", description="车辆设备状态信息表")
public class HighwayVehicleDevice implements Serializable {

    
    @NotBlank(message="[唯一ID]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("唯一ID")
    @TableField("ID")
    private String id;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("车牌号码")
    @TableField("PLATE_NO")
    private String plateNo;
    
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("在线状态")
    @TableField("STATUS")
    private String status;
    
    @Size(max= 255,message="编码长度不能超过255")
    @ApiModelProperty("视频预览地址")
    @TableField("VIDEO_URL")
    private String videoUrl;
    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("备注")
    @TableField("REMARK")
    private String remark;
    
    @NotBlank(message="[设备ID]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("设备ID")
    @TableField("CLIENT_ID")
    private String clientId;
    
    @NotBlank(message="[车辆类型，888表示车辆]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("车辆类型，888表示车辆")
    @TableField("AIOT_TYPE")
    private String aiotType;
    
    @NotNull(message="[创建时间]不能为空")
    @ApiModelProperty("创建时间")
    @TableField("CREATED_TIME")
    private Date createdTime;
    
    @NotNull(message="[更新时间]不能为空")
    @ApiModelProperty("更新时间")
    @TableField("UPDATED_TIME")
    private Date updatedTime;
    
    @NotBlank(message="[MEC连接状态(在线E10101/离线E10102/从未上线E10103)]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("MEC连接状态(在线E10101/离线E10102/从未上线E10103)")
    @TableField("SERVER_STATUS")
    private String serverStatus;
    
    @NotBlank(message="[MEC设备状态(正常E10101/异常E10102)]不能为空")
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("MEC设备状态(正常E10101/异常E10102)")
    @TableField("AIOT_STATUS")
    private String aiotStatus;
    
    @NotBlank(message="[终端绑定的平板设备ID]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("终端绑定的平板设备ID")
    @TableField("APP_ID")
    private String appId;
    
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("视频状态")
    @TableField("VIDEO_STATUS")
    private String videoStatus;
    
    @ApiModelProperty("检查标志，0表示未检查，1表示检查中")
    @TableField("INSPECTING_FLAG")
    private Integer inspectingFlag;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("联系人")
    @TableField("CONTACT_PERSON")
    private String contactPerson;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("联系方式")
    @TableField("CONTACT_PHONE")
    private String contactPhone;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("所属单位编码")
    @TableField("ORG_CODE")
    private String orgCode;
}
