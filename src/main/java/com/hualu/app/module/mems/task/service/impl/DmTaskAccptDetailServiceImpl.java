package com.hualu.app.module.mems.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.task.dto.accpt.DmAccptHzDto;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import com.hualu.app.module.mems.task.mapper.DmTaskAccptDetailMapper;
import com.hualu.app.module.mems.task.mapper.DmTaskDetailMapper;
import com.hualu.app.module.mems.task.service.DmTaskAccptDetailService;
import com.hualu.app.module.mems.task.utils.H_TaskDssHelper;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_StakeHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 验收单明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Service
public class DmTaskAccptDetailServiceImpl extends ServiceImpl<DmTaskAccptDetailMapper, DmTaskAccptDetail> implements DmTaskAccptDetailService {


    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    BaseLineService lineService;

    @Resource
    DmTaskDetailMapper taskDetailMapper;

    @Autowired
    private DssImageService imageService;


    @Override
    public void createDmTaskAccptDetails(String mtaskId, List<DmTaskDetailDto> detailDtos) {
        createDmTaskAccptDetails(null,mtaskId,detailDtos);
    }

    @Override
    public void createDmTaskAccptDetails(String accptId, String mtaskId, List<DmTaskDetailDto> detailDtos) {
        List<DmTaskAccptDetail> accptDetails = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(detailDtos)){
            detailDtos.forEach(item->{
                DmTaskAccptDetail row = new DmTaskAccptDetail();
                BeanUtils.copyProperties(item,row);
                //todo 判断业务类型待确定？？？
                //任务单直接生成的都是 1：计划性维修保养（无病害保洁） 2：有病害维修保养
                row.setBusinessType(2);
                //任务单记录
                row.setIsTaskRecord(1);
                row.setLogicStakeNum(item.getStake());
                //设置路线ID
                String lineId = StrUtil.isNotBlank(item.getLineId())?item.getLineId():item.getMainRoadId();
                row.setMainRoadId(lineId);
                //工程数量、申请验收数量、验收数量
                row.setMpitemNum(item.getMpitemAccount());
                row.setMpitemNumBak(item.getMpitemAccount());
                if (StrUtil.isNotBlank(accptId)){
                    row.setMtaskAccptId(accptId);
                }
                row.setRlStake(H_StakeHelper.convertCnStake(item.getRlStake()));
                //0：待修复、1：验收中、2：已验收、-1：已修复
                row.setAcceptStatus(0);
                //维修日期默认为当前日期，维修工程记录默认根据维修日期进行查询，否则查询不到
                row.setRepairDate(LocalDateTime.now());
                accptDetails.add(row);
            });
        }

        if (accptDetails.size() != 0){
            saveBatch(accptDetails);
        }
    }

    @Override
    public List<DmTaskAccptDetailDto> selectPageTaskRecord(Page page, QueryWrapper queryWrapper) {
        List<DmTaskAccptDetailDto> detailDtos = baseMapper.selectPageRecord(page, queryWrapper);
        setView(detailDtos);
        return detailDtos;
    }


    @Override
    public List<DmTaskAccptDetail> selectByMtaskDtlId(List<String> mtaskDtlIds) {
        if (CollectionUtil.isEmpty(mtaskDtlIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<DmTaskAccptDetail> queryWrapper = new LambdaQueryWrapper<>();
        List<List<String>> partition = com.google.common.collect.Lists.partition(mtaskDtlIds, 999);
        for(List<String> list : partition){
            queryWrapper.or().in(DmTaskAccptDetail::getMtaskDtlId,list);
        }
        return list(queryWrapper);
    }

    @Override
    public List<DmTaskAccptDetail> selectByDmTaskAccptId(String dmTaskAccptId) {
        LambdaQueryWrapper<DmTaskAccptDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTaskAccptDetail::getMtaskAccptId,dmTaskAccptId);
        return list(queryWrapper);
    }

    @Override
    public DmAccptHzDto getAcceptHzDto(String mtaskAccptId) {
        DmAccptHzDto hzDto = new DmAccptHzDto();
        hzDto.setMtaskAccptId(mtaskAccptId);
        List<DmTaskDssViewDto> dtos = baseMapper.selectDssNumAndMoney(mtaskAccptId);
        hzDto.setDssViewDtos(dtos);
        if (CollectionUtil.isNotEmpty(dtos)){
            hzDto.setDssNum(dtos.size());
            double totalMoney = dtos.stream().map(DmTaskDssViewDto::getMoney).filter(Objects::nonNull).mapToDouble(Double::doubleValue).sum();
            hzDto.setTotalMoney(totalMoney);
        }
        //病害集合
        List<String> dssIds = dtos.stream().map(DmTaskDssViewDto::getDssId).collect(Collectors.toList());
        List<DmTaskDssViewDto> dssDtos = initTaskDssView(dssIds,mtaskAccptId);
        if (CollectionUtil.isNotEmpty(dssDtos)){
            String contrId = dtos.get(0).getContrId();
            LinkedHashMap<String, List<DmTaskMpItemViewDto>> mpMap = selectMpItemsByDssIds(contrId, mtaskAccptId, dssIds);

            //设置条目回显值
            Map<String, DmTaskDssViewDto> dssMap = dssDtos.stream().collect(Collectors.toMap(DmTaskDssViewDto::getDssId, Function.identity()));
            dtos.forEach(item->{
                DmTaskDssViewDto dssDto = dssMap.get(item.getDssId());
                BeanUtil.copyProperties(dssDto,item,"mpNum","money","contrId");
                List<DmTaskMpItemViewDto> mpItemViewDtos = mpMap.get(item.getDssId());
                item.setMpItemViewDtos(mpItemViewDtos);
            });
        }
        return hzDto;
    }

    @Override
    public List<DmTaskDssViewDto> selectWaitAccptDssPage(Page page, QueryWrapper queryWrapper) {
        List<DmTaskDssViewDto> dtos = baseMapper.selectWaitAccptDssPage(page, CustomRequestContextHolder.getOrgIdString());
        if (CollectionUtil.isNotEmpty(dtos)){
            //病害集合
            List<String> dssIds = dtos.stream().map(DmTaskDssViewDto::getDssId).collect(Collectors.toList());
            Map<String, DmTaskDssViewDto> accptMap = dtos.stream().collect(Collectors.toMap(DmTaskDssViewDto::getDssId, Function.identity()));
            List<DmTaskDssViewDto> dmTaskDssViewDtos = initTaskDssView(dssIds,null);

            LinkedHashMap<String, List<DmTaskMpItemViewDto>> mpMap = selectMpItemsByDssIds(null, null, dssIds);

            dmTaskDssViewDtos.forEach(item->{
                //回显条目信息
                List<DmTaskMpItemViewDto> mpItemViewDtos = mpMap.get(item.getDssId());
                item.setMpItemViewDtos(mpItemViewDtos);
                //设置措施个数，及措施金额
                BigDecimal money = new BigDecimal(mpItemViewDtos.stream().mapToDouble(DmTaskMpItemViewDto::getMoney).sum()).setScale(3, BigDecimal.ROUND_HALF_UP);
                item.setMoney(money.doubleValue());
                item.setMpNum(mpItemViewDtos.size());

                //回显验收单单号和验收单ID
                DmTaskDssViewDto dmTaskDssViewDto = accptMap.get(item.getDssId());
                if (dmTaskDssViewDto!=null){
                    item.setMtaskAccptId(dmTaskDssViewDto.getMtaskAccptId());
                    item.setMtaskAccptCode(dmTaskDssViewDto.getMtaskAccptCode());
                }
            });
            return dmTaskDssViewDtos;
        }
        return Lists.emptyList();
    }

    @Override
    public List<DmTaskAccptDetailDto> selectPageByDss(Page page, QueryWrapper queryWrapper) {
        List<DmTaskAccptDetailDto> detailDtos = baseMapper.selectPageByDss(page, queryWrapper);
        setView(detailDtos);
        return detailDtos;
    }

    @Override
    public List<DmTaskMpItemViewDto> getMpItemViewDto(String dssId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("dtd.DSS_ID",dssId);
        List<DmTaskMpItemViewDto> dmTaskMpItemViewDtos = baseMapper.selectMpItemsByDssIds(queryWrapper);
        return dmTaskMpItemViewDtos;
    }

    private List<DmTaskDssViewDto> initTaskDssView(List<String> dssIds,String mtaskAccptId) {
        List<DmTaskDssViewDto> dssDtos = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(dssIds)){
            QueryWrapper dssWrapper = new QueryWrapper();
            dssWrapper.in("td.dss_id",dssIds);
            dssDtos = taskDetailMapper.selectDssInfosByAccpt(dssWrapper);
            if (CollectionUtil.isNotEmpty(dssDtos)){
                Map<String, List<DssImage>> imageMap = imageService.mapGroupByDssIds(dssIds);
                H_TaskDssHelper.setViewInfo(dssDtos,imageMap);
            }
        }
        return dssDtos;
    }

    /**
     * 获取病害对应措施
     * @param contrId
     * @param mtaskAcceptId
     * @param dssIds
     * @return
     */
    private LinkedHashMap<String, List<DmTaskMpItemViewDto>> selectMpItemsByDssIds(String contrId, String mtaskAcceptId, List<String> dssIds){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(StrUtil.isNotBlank(contrId),"mmp.CONTR_ID",contrId);
        queryWrapper.eq(StrUtil.isNotBlank(mtaskAcceptId),"dtd.mtask_accpt_id",mtaskAcceptId);
        queryWrapper.in("dtd.DSS_ID",dssIds);
        List<DmTaskMpItemViewDto> mpItemViewDtos = baseMapper.selectMpItemsByDssIds(queryWrapper);
        mpItemViewDtos.forEach(item->{
            //app采用了getMtaskDtlId 做判断，所以后台在此赋值
            if (StrUtil.isBlank(item.getMtaskDtlId()) && StrUtil.isNotBlank(item.getMtaskAccptDtlId())){
                item.setMtaskDtlId(item.getMtaskAccptDtlId());
            }
            item.setMpitemAccountName(item.getAcceptAmount()+"（"+item.getMeasureUnit()+"）");
        });
        LinkedHashMap<String, List<DmTaskMpItemViewDto>> mpMap = mpItemViewDtos.stream().collect(Collectors.groupingBy(e -> e.getDssId(), LinkedHashMap::new, Collectors.toList()));
        return mpMap;
    }

    /**
     * 回显界面值
     * @param dtos
     */
    private void setView(List<DmTaskAccptDetailDto> dtos){
        Collection<IFacBase> iFacBases = CustomApplicationContextHolder.getBeansOfType(IFacBase.class).values();

        dtos.forEach(accptDetail->{
            Double finishStake = accptDetail.getFinishStake();
            String rlStake = StrUtil.isBlank(accptDetail.getRlStake())?"":accptDetail.getRlStake();
            accptDetail.setRlStake(rlStake
                    + (null != finishStake && finishStake > 0 ? "~" + StringUtil.getDisplayStake(finishStake) : ""));

            IFacBase base = iFacBases.stream().filter(e -> e.getFacilityCat().equals(accptDetail.getFacilityCat())).findFirst().orElse(null);
            if (base != null){
                base.setDmTaskAccptDetailView(accptDetail);
            }
            //如果是字母说明未转换
            if (Validator.isWord(accptDetail.getLane())){
                accptDetail.setLaneNm(dicService.getDicName("LANE",accptDetail.getLane()));
            }

            if (StrUtil.isNotBlank(accptDetail.getLineDirect())){
                if ("3".equals(accptDetail.getLineDirect())){
                    accptDetail.setLineDirectNm("全线");
                }else {
                    accptDetail.setLineDirectNm(dicService.getDicName("LANE_DIRECTION",accptDetail.getLineDirect()));
                }
            }

            if (accptDetail.getAcceptAmount() == null) {
                accptDetail.setAcceptAmount(accptDetail.getApplyAmount());
            }
            accptDetail.setAcceptAmount(accptDetail.getAcceptAmount());
            //回显匝道名称
            String rampId = accptDetail.getRampId();
            if(StrUtil.isNotBlank(rampId)) {
                BaseLine line = lineService.selectLine(rampId);
                if(line!=null){
                    accptDetail.setLineName(line.getLineAllname());
                }
            }
            setDssunitAndNum(accptDetail);
        });
    }

    private  void setDssunitAndNum(DmTaskAccptDetailDto entity){
        if(StrUtil.isNotBlank(entity.getHaveDssColom())){
            if(entity.getHaveDssColom().equals("DSS_L")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssL())) ;
                entity.setDssUnit(entity.getDssLUnit());
            }else if(entity.getHaveDssColom().equals("DSS_W")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssW()));
                entity.setDssUnit(entity.getDssWUnit());
            }else if(entity.getHaveDssColom().equals("DSS_D")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssD()));
                entity.setDssUnit(entity.getDssDUnit());
            }else if(entity.getHaveDssColom().equals("DSS_N")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssN())) ;
                entity.setDssUnit(entity.getDssNUnit());
            }else if(entity.getHaveDssColom().equals("DSS_A")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssA()));
                entity.setDssUnit(entity.getDssAUnit());
            }else if(entity.getHaveDssColom().equals("DSS_V")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssV())) ;
                entity.setDssUnit(entity.getDssVUnit());
            }else if(entity.getHaveDssColom().equals("DSS_P")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssP())) ;
                entity.setDssUnit("%");
            }else if(entity.getHaveDssColom().equals("DSS_G")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssG())) ;
                entity.setDssUnit("度");
            }
        }
    }
}
