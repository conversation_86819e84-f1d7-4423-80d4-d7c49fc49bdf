package com.hualu.app.module.mems.dinsp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 巡查轨迹
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InspectTrack对象", description="巡查轨迹")
public class InspectTrack implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "巡查ID")
    @TableField("INSPECT_ID")
    private String inspectId;

    @ApiModelProperty(value = "经度")
    @TableField("LONGITUDE")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    @TableField("LATITUDE")
    private Double latitude;

    @ApiModelProperty(value = "序号")
    @TableField("ORDERS")
    private Double orders;

    @ApiModelProperty(value = "车速")
    @TableField("SPEED")
    private Double speed;

    @ApiModelProperty(value = "巡检车编号")
    @TableField("CARD_NO")
    private String cardNo;

    @ApiModelProperty(value = "采集时间")
    @TableField("TIME")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    @ApiModelProperty(value = "组ID")
    @TableField("GROUP_ID")
    private String groupId;
}
