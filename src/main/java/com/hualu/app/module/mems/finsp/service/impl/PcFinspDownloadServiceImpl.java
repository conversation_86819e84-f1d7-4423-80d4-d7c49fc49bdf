package com.hualu.app.module.mems.finsp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.entity.PcFinspDownload;
import com.hualu.app.module.mems.finsp.mapper.PcFinspDownloadMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspDownloadService;
import com.tg.dev.api.core.global.exception.BaseException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 结构物排查数据下载表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
public class PcFinspDownloadServiceImpl extends ServiceImpl<PcFinspDownloadMapper, PcFinspDownload> implements PcFinspDownloadService {

    static Map<Integer,String> statusMap=new HashMap<Integer,String>();

    static {
        statusMap.put(-1,"数据生成失败");
        statusMap.put(0,"数据正在生成");
        statusMap.put(1,"数据已生成");
    }

    @Override
    public void checkRunning(PcFinspDownload pcFinspDownload) {
        LambdaQueryWrapper<PcFinspDownload> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcFinspDownload::getMntOrgId,pcFinspDownload.getMntOrgId())
                .eq(PcFinspDownload::getPrjId,pcFinspDownload.getPrjId()).eq(PcFinspDownload::getState,0);

        int count = count(wrapper);
        if(count>0){
            throw new BaseException("数据正在后台生成，请稍等");
        }
        save(pcFinspDownload);
    }

    @Override
    public void updateState(String dataId, Integer state) {
        LambdaUpdateWrapper<PcFinspDownload> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(PcFinspDownload::getState,state);
        wrapper.set(PcFinspDownload::getRemark,statusMap.get(state));
        wrapper.eq(PcFinspDownload::getDataId,dataId);
        update(wrapper);
    }
}
