package com.hualu.app.module.mems.task.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 任务单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmTask对象", description="任务单")
public class DmTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务单ID")
    @TableId("MTASK_ID")
    private String mtaskId;

    @NotBlank(message = "任务单编码不能为空")
    @ApiModelProperty(value = "任务单编码")
    @TableField("MTASK_CODE")
    private String mtaskCode;

    @NotBlank
    @ApiModelProperty(value = "维修项目名称")
    @TableField("MTASK_NAME")
    private String mtaskName;

    @ApiModelProperty(value = "管养单位编码")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "发单部门名称")
    @TableField("MNG_DEPT_NM")
    private String mngDeptNm;

    @NotNull
    @ApiModelProperty(value = "指定开工日期")
    @TableField("ASKED_START_DATE")
    private LocalDate askedStartDate;

    @NotNull
    @ApiModelProperty(value = "指定完工日期")
    @TableField("ASKED_COMPL_DATE")
    private LocalDate askedComplDate;

    @NotBlank
    @ApiModelProperty(value = "养护单位名称")
    @TableField("MNTN_ORG_NAME")
    private String mntnOrgName;

    @NotBlank
    @ApiModelProperty(value = "紧急程度")
    @TableField("EMERGY_DEGREE")
    private String emergyDegree;

    @ApiModelProperty(value = "发单人")
    @TableField("SEND_USER")
    private String sendUser;

    @ApiModelProperty(value = "发单时间")
    @TableField("SEND_TIME")
    private LocalDate sendTime;

    @ApiModelProperty(value = "接收人")
    @TableField("RECEIVE_USER")
    private String receiveUser;

    @ApiModelProperty(value = "接收时间")
    @TableField("RECEIVE_TIME")
    private LocalDate receiveTime;

    @ApiModelProperty(value = "申诉理由")
    @TableField("CANCEL_OPINION")
    private String cancelOpinion;

    @ApiModelProperty(value = "技术要求")
    @TableField("TECH_REQ")
    private String techReq;

    @TableField("STATUS")
    private Integer status;

    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDate createTime;

    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "流程ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "任务单相关附件")
    @TableField("MTASK_FILE")
    private String mtaskFile;

    @NotBlank
    @ApiModelProperty(value = "合同id")
    @TableField("CONTR_ID")
    private String contrId;

    @ApiModelProperty(value = "地点")
    @TableField("PLACE")
    private String place;

    @ApiModelProperty(value = "主要维修项目")
    @TableField("MAJOR_PROJECT")
    private String majorProject;

    @ApiModelProperty(value = "项目公司名称")
    @TableField("PRJ_ORG_NAME")
    private String prjOrgName;

    @ApiModelProperty(value = "项目公司编码")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "任务单明细数量")
    @TableField("TOTAL_COUNT")
    private Integer totalCount;

    @ApiModelProperty(value = "任务单验收明细数量")
    @TableField("ACCPT_COUNT")
    private Integer accptCount;


}
