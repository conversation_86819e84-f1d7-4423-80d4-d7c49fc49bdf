package com.hualu.app.module.mems.finsp.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class PcFinspResultDto implements Serializable {

    @ApiModelProperty(value = "检查内容ID")
    @TableField("FINSP_ITEM_ID")
    private String finspItemId;

    @ApiModelProperty(value = "病害描述")
    @TableField("FINSP_DESC")
    private String finspDesc;

    @ApiModelProperty(value = "检查结构（0：否，1：是）")
    @TableField("FINSP_RESULT")
    private String finspResult;
}
