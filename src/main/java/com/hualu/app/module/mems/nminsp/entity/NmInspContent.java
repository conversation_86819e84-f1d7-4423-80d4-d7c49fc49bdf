package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 新版日常巡检及经常检查巡查内容 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmInspContent对象", description="新版日常巡检及经常检查巡查内容")
public class NmInspContent implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "父节点ID")
    @TableField("PID")
    private String pid;

    @ApiModelProperty(value = "巡查内容，BJ:构件，ITEM:巡查项")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "设施类型")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "归属部件")
    @TableField("PART_ID")
    private String partId;

    @ApiModelProperty(value = "页面是否展示： 1：展示，0：不展示")
    @TableField("PAGE_SHOW")
    private String pageShow;

    @ApiModelProperty(value = "巡查类型")
    @TableField("XC_TYPE")
    private String xcType;

    @ApiModelProperty(value = "巡查频率；1：日常白天、2：日常夜间、3：边坡旱季、4：边坡雨前后、5：雨季中")
    @TableField("INSP_FREQUENCY")
    private String inspFrequency;

    @ApiModelProperty(value = "顺序号")
    @TableField("SORT_NO")
    private Double sortNo;

    @ApiModelProperty(value = "关联病害类型")
    @TableField("DSS_TYPE")
    private String dssType;

    @ApiModelProperty(value = "涵洞旧部件id")
    @TableField("OLD_PART_ID")
    private String oldPartId;


}
