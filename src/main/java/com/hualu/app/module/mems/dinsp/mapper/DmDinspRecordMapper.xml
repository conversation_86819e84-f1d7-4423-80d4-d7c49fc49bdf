<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dinsp.mapper.DmDinspRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dinsp.entity.DmDinspRecord">
        <id column="DSS_ID" property="dssId" />
        <result column="DINSP_ID" property="dinspId" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="INSP_TIME_INTVL" property="inspTimeIntvl" />
        <result column="ISSUE_TYPE" property="issueType" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="STAKE" property="stake" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_DEGREE" property="dssDegree" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_PART_ID" property="structPartId" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="LANE" property="lane" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="DSS_CAUSE" property="dssCause" />
        <result column="DSS_L" property="dssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="DSS_W" property="dssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="DSS_D" property="dssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="DSS_N" property="dssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="DSS_A" property="dssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="DSS_V" property="dssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="DSS_P" property="dssP" />
        <result column="DSS_G" property="dssG" />
        <result column="DSS_IMP_FLAG" property="dssImpFlag" />
        <result column="DSS_QUALITY" property="dssQuality" />
        <result column="HIS_DSS_ID" property="hisDssId" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="ISPHONE" property="isphone" />
        <result column="RAMP_ID" property="rampId" />
        <result column="TUNNEL_MOUTH" property="tunnelMouth" />
        <result column="START_HIGH" property="startHigh" />
        <result column="END_HIGH" property="endHigh" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="STAKE_HIGH" property="stakeHigh" />
        <result column="FINISH_STAKE" property="finishStake" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_ID, DINSP_ID, INSP_TIME, INSP_TIME_INTVL, ISSUE_TYPE, LINE_DIRECT, STAKE, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, FACILITY_CAT, STRUCT_ID, STRUCT_PART_ID, STRUCT_COMP_ID, LANE, DSS_POSITION, DSS_DESC, DSS_CAUSE, DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V, DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY, HIS_DSS_ID, X, Y, ISPHONE, RAMP_ID, TUNNEL_MOUTH, START_HIGH, END_HIGH, START_STAKE_NUM, END_STAKE_NUM, STAKE_HIGH, FINISH_STAKE
    </sql>

    <select id="selectRecords"
            resultType="com.hualu.app.module.mems.dinsp.entity.DmDinspRecord">
        select d.*,dt.DSS_TYPE_NAME,decode(d.ISSUE_TYPE,2,'病害','非病害') as ISSUE_TYPE_NAME,dc1.ATTRIBUTE_VALUE as FACILITY_CAT_NAME,
               decode(LINE_DIRECT,4,rl.LINE_SNAME,dc3.ATTRIBUTE_VALUE) as LANE_NAME,
               decode(d.FACILITY_CAT,'QL',br.BRDG_NAME,'HD',hd.CLVRT_NAME,'BP',slo.SLOPE_NAME,'SD',tun.TUNNEL_NAME,'FWQ',
                      a.SERVICE_NAME,'SFZ',t.TOLLGATE_NAME) as struct_name,
               decode(LINE_DIRECT,'1','上行','2','下行','匝道') as LINE_DIRECT_NAME
        from MEMSDB.DM_DINSP_RECORD d
                 inner join memsdb.DSS_TYPE_NEW dt on d.DSS_TYPE=dt.DSS_TYPE
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc1
                           on dc1.ATTRIBUTE_ITEM='FACILITY_CAT' and d.FACILITY_CAT=dc1.ATTRIBUTE_CODE and dc1.ATTRIBUTE_ACTIVE=0
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc3
                           on dc3.ATTRIBUTE_ITEM='LANE' and d.LANE=dc3.ATTRIBUTE_CODE and dc3.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'LM'
                 left join BCTCMSDB.T_BRDG_BRDGRECOG br on d.STRUCT_ID = br.BRDGRECOG_ID and d.FACILITY_CAT = 'QL'
                 left join BCTCMSDB.T_BRDG_PARTSTYPE qlpa on d.STRUCT_PART_ID = qlpa.PARTSTYPE_ID and d.FACILITY_CAT = 'QL'
                 left join BCTCMSDB.T_CLVRT_CLVRTRECOG hd on d.STRUCT_ID = hd.CLVRTRECOG_ID and d.FACILITY_CAT = 'HD'
                 left join BCTCMSDB.T_CLVRT_PARTTYPE hdpa on d.STRUCT_PART_ID = hdpa.PART_CODE and d.FACILITY_CAT = 'HD'
                 left join HSMSDB.HSMS_SLOPE_INFO slo on d.STRUCT_ID = slo.SLOPE_ID and d.FACILITY_CAT = 'BP'
                 left join MTMSDB.MTMS_TUNNEL_BASIC tun on d.STRUCT_ID = tun.TUNNEL_ID and d.FACILITY_CAT = 'SD'
                 left join GDGS.BASE_SERVICE_AREA a on d.STRUCT_ID = a.SERVICE_ID and d.FACILITY_CAT = 'FWQ'
                 left join GDGS.BASE_TOLLGATE t on d.STRUCT_ID = t.TOLLGATE_ID and d.FACILITY_CAT = 'SFZ'
                 left join gdgs.BASE_RAMP_LINE rl on d.RAMP_ID = rl.LINE_ID and LINE_DIRECT = '4'
                 left join MEMSDB.DSS_INFO di on d.DSS_ID = di.DSS_ID
        where d.DSS_id in
        <foreach collection="array" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by INSP_TIME desc
    </select>
    <select id="selectDssRecordPage" resultType="com.hualu.app.module.mems.dss.dto.DssInfoDto">
        select * from (
        SELECT
            df.dINSP_CODE as code_source,
            df.patrolusername as user_name,
            dr.FACILITY_CAT,
            df.LINE_CODE,
            df.MNTN_ORG_NM as oprt_Org_Name,
            df.MNT_ORG_ID as org_id,
            df.SEARCH_DEPT,
            dr.LANE,
            dr.STAKE as RL_STAKE,
            df.INSP_DATE as found_date,
            dr.dss_id,
            dr.DSS_TYPE,
            dr.DSS_DESC,
            dr.dss_l,
            dr.dss_l_unit,
            dr.dss_w,
            dr.dss_w_unit,
            dr.dss_d,
            dr.dss_d_unit,
            dr.dss_n,
            dr.dss_n_unit,
            dr.dss_a,
            dr.dss_a_unit,
            dr.dss_v,
            dr.dss_v_unit,
            dr.dss_p,
            dr.dss_g,
            dt.HAVE_DSS_COLOM,
            dt.HAVE_DSS_UNIT,
            nvl(dss.REPAIR_STATUS,0) as REPAIR_STATUS,
            dss.DEAL_STATUS,
            '1' as DSS_SOURCE,
            dt.DSS_TYPE_NAME,
            dr.close_type,
            df.CREATE_USER_ID
        FROM
            memsdb.DM_DINSP df
                JOIN memsdb.DM_DINSP_RECORD dr ON df.DINSP_ID = dr.DINSP_ID
                JOIN memsdb.DSS_TYPE_NEW dt on dr.dss_type = dt.DSS_TYPE
                left join memsdb.DSS_INFO dss on dr.dss_id = dss.dss_id and dss.dss_source=1) t ${ew.customSqlSegment}
    </select>
</mapper>
