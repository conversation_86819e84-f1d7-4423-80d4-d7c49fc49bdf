package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.tg.dev.api.annotation.MD5Field;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 结构物专项范围表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcProjectScope对象", description="结构物专项范围表")
public class PcProjectScope implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结构ID")
    @TableId("STRUCT_ID")
    private String structId;

    @ApiModelProperty(value = "项目ID")
    @TableField("PRJ_ID")
    private String prjId;

    @ApiModelProperty(value = "是否重要")
    @TableField("IMPORTANT")
    private String important;

    @MD5Field
    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @MD5Field
    @ApiModelProperty(value = "方向（桥幅：左右）")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @MD5Field
    @ApiModelProperty(value = "结构类型（边坡：路堤、路堑；涵洞类型）")
    @TableField("STRUCT_TYPE")
    private String structType;

    @MD5Field
    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @MD5Field
    @ApiModelProperty(value = "结构物级数（边坡层级,桥梁跨径,涵洞跨径）")
    @TableField("STRUCT_LEVEL")
    private String structLevel;

    @MD5Field
    @ApiModelProperty(value = "结构物位置（边坡：左右）")
    @TableField("STRUCT_POSITION")
    private String structPosition;

    @MD5Field
    @ApiModelProperty(value = "设施类型")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "经度")
    @TableField("X")
    private Double x;

    @ApiModelProperty(value = "纬度")
    @TableField("Y")
    private Double y;

    @ApiModelProperty(value = "项目公司ID")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "MD5（基于后期考虑，重复采集经纬的问题）")
    @TableField("MD5")
    private String md5;


    @ApiModelProperty(value = "结构物桩号")
    @TableField("STRUCT_STAKE")
    private String structStake;


    @ApiModelProperty(value = "设计单位")
    @TableField("DESIGN_UNIT")
    private String designUnit;

    @ApiModelProperty(value = "部件类型(墩台类型:混凝土,钢筋混凝土)")
    @TableField("PART_TYPE")
    private String partType;

    @ApiModelProperty(value = "功能类型(过水，过人)")
    @TableField("FUNCTION_TYPE")
    private String functionType;


    @ApiModelProperty(value = "排查单位名称")
    @TableField(exist = false)
    private String inspOrgName;

    @TableLogic
    @TableField(value = "DEL_FLAG",fill = FieldFill.INSERT)
    private Integer delFlag;
}
