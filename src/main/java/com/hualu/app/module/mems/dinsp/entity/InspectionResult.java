package com.hualu.app.module.mems.dinsp.entity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 巡查结果数据实体（根据 SQL 查询结构映射）
 * 对应 SQL 字段：
 * SELECT
 *   N.DSS_TYPE_NAME as dssTypeName,
 *   DECODE(...) as workNums,
 *   ...其他字段
 */
public class InspectionResult {

    // 字段声明（注释说明来源及逻辑）

    /** 设施类型名称，对应 N.DSS_TYPE_NAME */
    private String dssTypeName;

    /**
     * 工作量数值，通过 DECODE 动态计算：
     * - 当 HAVE_DSS_UNIT='m' 时取 D.DSS_L（长度）
     * - 当 HAVE_DSS_UNIT='m2' 时取 D.DSS_A（面积）
     * - 默认取 D.DSS_N（计数）
     */
    private BigDecimal workNums;  // 使用 BigDecimal 兼容整数和小数

    /**
     * 工作量单位，通过 DECODE 映射：
     * - 'm' → '米'
     * - 'm2' → '平方'
     * - 默认 → '处'
     */
    private String workUnit;

    /** 巡检日期，对应 SP.INSP_DATE */
    private LocalDate inspDate;   // 根据实际数据库类型选择 LocalDate/LocalDateTime

    /**
     * 维修状态，通过 DECODE 映射：
     * - 0 → '待维修'
     * - 1 → '修复中'
     * - 2 → '已修复'
     */
    private String repairStatus;

    /** 验收单编号，对应 A.MTASK_ACCPT_CODE */
    private String acceptanceCode; // SQL 别名"验收单"转为英文属性名

    /** 任务单编号，对应 aa.MTASK_CODE */
    private String taskCode;       // SQL 别名"任务单"转为英文属性名

    /** 巡查类型，固定值 '车载巡查' */
    private String xcType = "车载巡查"; // 直接初始化默认值

    /** 巡检单编号，对应 SP.DINSP_CODE */
    private String dinspCode;

    // 构造方法
    public InspectionResult() {
    }

    // 带参构造方法（可选）
    public InspectionResult(String dssTypeName, BigDecimal workNums, String workUnit,
                            LocalDate inspDate, String repairStatus,
                            String acceptanceCode, String taskCode, String dinspCode) {
        this.dssTypeName = dssTypeName;
        this.workNums = workNums;
        this.workUnit = workUnit;
        this.inspDate = inspDate;
        this.repairStatus = repairStatus;
        this.acceptanceCode = acceptanceCode;
        this.taskCode = taskCode;
        this.dinspCode = dinspCode;
        // xcType 已有默认值，无需传入
    }

    // Getters and Setters
    public String getDssTypeName() {
        return dssTypeName;
    }

    public void setDssTypeName(String dssTypeName) {
        this.dssTypeName = dssTypeName;
    }

    // 其他 getter/setter 方法...
    // 可根据 IDE 自动生成，此处省略完整代码以节省篇幅

    // toString() 方法（调试用）
    @Override
    public String toString() {
        return "InspectionResult{" +
                "dssTypeName='" + dssTypeName + '\'' +
                ", workNums=" + workNums +
                ", workUnit='" + workUnit + '\'' +
                ", inspDate=" + inspDate +
                ", repairStatus='" + repairStatus + '\'' +
                ", acceptanceCode='" + acceptanceCode + '\'' +
                ", taskCode='" + taskCode + '\'' +
                ", xcType='" + xcType + '\'' +
                ", dinspCode='" + dinspCode + '\'' +
                '}';
    }
}
