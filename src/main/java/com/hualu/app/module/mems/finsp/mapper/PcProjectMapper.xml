<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcProject">
        <id column="PRJ_ID" property="prjId" />
        <result column="PRJ_CODE" property="prjCode" />
        <result column="PRJ_NAME" property="prjName" />
        <result column="PRJ_YEAR" property="prjYear" />
        <result column="MNT_TYPE" property="mntType" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="INSP_ORG_NAME" property="inspOrgName" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_USER_ID" property="createUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        PRJ_ID, PRJ_CODE, PRJ_NAME, PRJ_YEAR, MNT_TYPE, FACILITY_CAT, INSP_ORG_NAME, MNT_ORG_ID, CREATE_TIME, CREATE_USER_ID
    </sql>

</mapper>
