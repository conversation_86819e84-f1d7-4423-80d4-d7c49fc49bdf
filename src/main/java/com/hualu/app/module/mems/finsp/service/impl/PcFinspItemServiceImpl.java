package com.hualu.app.module.mems.finsp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.entity.PcFinspItem;
import com.hualu.app.module.mems.finsp.mapper.PcFinspItemMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspItemService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 结构物排查检查内容表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Service
public class PcFinspItemServiceImpl extends ServiceImpl<PcFinspItemMapper, PcFinspItem> implements PcFinspItemService {

    @Override
    public List<PcFinspItem> listByFacilityCat(String facilityCat) {
        LambdaQueryWrapper<PcFinspItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspItem::getFacilityCat, facilityCat);
        queryWrapper.eq(PcFinspItem::getIsDelete,0);
        List<PcFinspItem> result = list(queryWrapper);
        return result;
    }
}
