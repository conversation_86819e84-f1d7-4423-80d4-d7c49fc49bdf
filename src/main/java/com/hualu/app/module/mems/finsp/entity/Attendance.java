package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Attendance对象", description="")
public class Attendance implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private String id;

    @NotBlank(message = "结构物不能为空")
    @TableField("STRUCT_ID")
    private String structId;

    /**
     * 结构物名称
     */
    @TableField("STRUCT_NAME")
    private String structName;

    /**
     * 签到人姓名
     */
    @TableField("INSP_PERSON")
    private String inspPerson;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 签到时间
     */
    @TableField("ATTENDANCE_TIME")
    private String attendanceTime;

    /**
     * 上次签到时间
     */
    @TableField("LAST_ATTENDANCE_TIME")
    private String lastAttendanceTime;

    /**
     * 结构物管养单位名称
     */
    @TableField("ORG_NAME")
    private String orgName;

    /**
     * 结构物管养单位名称
     */
    @TableField("ORG_CODE")
    private String orgCode;

    /**
     * 上次签到人
     */
    @TableField("LAST_ATTENDANCE_PERSON")
    private String lastAttendancePerson;

    /**
     * 签到次数
     */
    @TableField("ORDERS")
    private Double orders;

    /**
     * 上次签到人ID
     */
    @TableField("LAST_ATTENDANCE_USER_ID")
    private String lastAttendanceUserId;

    @NotBlank(message = "结构物类型不能为空")
    @TableField("TYPE")
    private String type;

    /**
     * 打卡位置信息
     */
    @TableField("LOCATION")
    private String location;

    @NotBlank(message = "经度不能为空")
    @TableField("LONGITUDE")
    private String longitude;

    @NotBlank(message = "纬度不能为空")
    @TableField("LATITUDE")
    private String latitude;

    @TableField("FIELD_ID")
    private String fieldId;

    @TableField(exist = false)
    private String imageHost;

    //是否需要验证距离 0：不需要 1：需要
    @TableField(exist = false)
    private int needValidLocation;
}
