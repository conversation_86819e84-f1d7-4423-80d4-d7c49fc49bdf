package com.hualu.app.module.mems.autoInspector.utils;

import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwaySegment;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.utils.H_StakeHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;

/**
 * 病害信息转日常巡检记录转换工具类
 * <p>
 * 提供将病害信息转换为日常巡检记录的工具方法
 * </p>
 * 
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
public class DefectToDinspConverter {
    
    /**
     * 将病害信息转换为日常巡检记录，可以使用路段信息
     * 
     * @param defectInfo 病害信息
     * @param segmentCache 路段缓存
     * @return 日常巡检记录
     */
    public static NmDinsp convertToNmDinsp(HighwayDefectInfo defectInfo, Map<String, HighwaySegment> segmentCache) {
        if (defectInfo == null) {
            return null;
        }
        
        NmDinsp nmDinsp = new NmDinsp();
        
        // 基本信息设置
        nmDinsp.setInspFrequency(1);  // 日常巡检
        
        // 设置桩号范围（起点桩号~终点桩号）
        setStakeName(nmDinsp, defectInfo, segmentCache);
        
        nmDinsp.setFacilityCat("LM");  // 默认路面
        
        // 时间信息设置
        LocalDateTime now = LocalDateTime.now();
        Date inspDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        nmDinsp.setInspDate(inspDate);
        nmDinsp.setInspTime(now.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
        
        // 巡检人员信息
        //nmDinsp.setInspPerson("");
        
        // 标记来源为自动检测
        nmDinsp.setXcType(2);
        nmDinsp.setDelFlag(0);
        
        // 设置默认天气
        nmDinsp.setWeather("01");
        
        // 设置病害数量默认1个
        nmDinsp.setDssNum(1);
        
        // 创建和更新时间
        nmDinsp.setCreateTime(now);
        nmDinsp.setUpdateTime(now);
        
        return nmDinsp;
    }
    
    /**
     * 设置桩号范围
     * 
     * @param nmDinsp 日常巡检记录
     * @param defectInfo 病害信息
     * @param segmentCache 路段缓存
     */
    private static void setStakeName(NmDinsp nmDinsp, HighwayDefectInfo defectInfo, Map<String, HighwaySegment> segmentCache) {
        String stakeStart = null;
        String stakeEnd = null;
        
        // 只使用路段表的起终点桩号
        if (segmentCache != null) {
            String segmentId = defectInfo.getSegmentId();
            if (segmentId != null && !segmentId.trim().isEmpty()) {
                HighwaySegment segment = segmentCache.get(segmentId);
                if (segment != null) {
                    stakeStart = segment.getStakestart();
                    stakeEnd = segment.getStakeend();
                    
                    if (stakeStart != null && !stakeStart.trim().isEmpty() &&
                        stakeEnd != null && !stakeEnd.trim().isEmpty()) {
                        log.debug("使用路段[{}]的桩号范围：{}~{}", segmentId, stakeStart, stakeEnd);
                    } else {
                        log.warn("路段[{}]的桩号范围不完整", segmentId);
                    }
                } else {
                    log.warn("未找到路段ID[{}]对应的路段信息", segmentId);
                }
            } else {
                log.warn("病害信息中未包含路段ID");
            }
        } else {
            log.warn("路段缓存为空，无法获取路段桩号信息");
        }
        
        // 设置桩号名称
        if (stakeStart != null && stakeEnd != null) {
            String startDisplay = H_StakeHelper.convertCnStake(stakeStart);
            String endDisplay = H_StakeHelper.convertCnStake(stakeEnd);
            nmDinsp.setStakeName(startDisplay + "~" + endDisplay);
            log.debug("设置桩号范围：{}", nmDinsp.getStakeName());
        } else {
            log.warn("无法设置桩号范围，因为没有足够的路段桩号信息");
        }
    }
    
    /**
     * 将病害信息转换为日常巡检记录项
     * 
     * @param defectInfo 病害信息
     * @param dinspId 日常巡检单ID
     * @return 日常巡检记录项
     */
    public static NmDinspRecord convertToDinspRecord(HighwayDefectInfo defectInfo, String dinspId) {
        if (defectInfo == null) {
            return null;
        }
        
        NmDinspRecord record = new NmDinspRecord();
        
        // 设置基本信息
        record.setDssId(defectInfo.getId());
        record.setDinspId(dinspId);
        
        // 设置行车方向
        String direction = defectInfo.getInspectDirection();
        record.setLineDirect("1".equals(direction) ? "2" : "0".equals(direction) ? "1" : direction);
        
        // 设置桩号（转换为公里数）
        Double stake = stakeToKilometer(defectInfo.getStakeStart());
        record.setStake(stake);
        
        // 设置病害类型信息
        record.setDssType(defectInfo.getYhDssType());
        record.setDssTypeName(defectInfo.getDssTypeName());
        record.setFacilityCat("LM");  // 默认路面
        
        // 设置车道信息
        record.setLane(convertLaneNumberToLetter(defectInfo.getLaneNum(), direction));
        
        // 设置病害位置描述
        record.setDssPosition(defectInfo.getStakeStart() + "~" + defectInfo.getStakeEnd() + "," 
                + convertLaneNumberToLetter(defectInfo.getLaneNum(), direction));
        
        // 设置病害描述
        record.setDssDesc(defectInfo.getDssTypeName() + "，位于" + record.getDssPosition());
        
        // 设置病害尺寸信息
        if (defectInfo.getTargetLen() != null) {
            record.setDssL(defectInfo.getTargetLen().doubleValue());
            record.setDssLUnit("m");
        }
        
        if (defectInfo.getTargetArea() != null) {
            record.setDssA(defectInfo.getTargetArea().doubleValue());
            record.setDssAUnit("m²");
        }
        
        // 设置坐标信息
        if (defectInfo.getLongitude() != null) {
            record.setX(defectInfo.getLongitude().doubleValue());
        }
        if (defectInfo.getLatitude() != null) {
            record.setY(defectInfo.getLatitude().doubleValue());
        }
        
        // 设置路面类型（默认沥青路面）
        record.setPavementType("LQ");
        
        // 设置来源和其他标记
        record.setSource("2");  // 外部系统导入
        record.setDelFlag(0);
        record.setIssueType("2"); // 病害类
        record.setIsphone(0);   // PC端
        
        // 设置时间信息
        LocalDateTime now = LocalDateTime.now();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        
        log.debug("将病害[{}]转换为日常巡检记录项[{}]", defectInfo.getId(), record.getDssId());
        return record;
    }
    
    /**
     * 将桩号字符串转换为公里数
     */
    private static Double stakeToKilometer(String stake) {
        if (stake == null || stake.trim().isEmpty()) {
            return null;
        }

        // 如果已经是数字，检查是否为公里单位，不是则转换
        if (stake.matches("^[0-9]+(\\.[0-9]+)?$")) {
            Double value = Double.valueOf(stake);
            // 如果大于1000，可能是以米为单位，需要除以1000转成公里
            if (value > 1000) {
                return value / 1000;
            }
            return value; // 已经是公里单位
        }

        // 处理K100+500这种标准桩号格式
        if (stake.toUpperCase().startsWith("K")) {
            try {
                // 去掉K前缀
                String temp = stake.trim().toUpperCase().replaceAll("K", "");
                // 按+分割
                String[] parts = temp.split("\\+");

                // 提取公里部分
                Double kilometers = Double.valueOf(parts[0]);

                // 如果有米部分，转换为公里并加上
                if (parts.length > 1) {
                    Double meters = Double.valueOf(parts[1]);
                    kilometers += meters / 1000;
                }

                return kilometers;
            } catch (Exception e) {
                log.error("桩号转换失败: {}", stake, e);
                return null; // 转换出错返回null
            }
        }

        return null; // 不符合格式返回null
    }
    
    /**
     * 将车道号数字转换为对应的字母表示
     */
    private static String convertLaneNumberToLetter(String laneNumber, String direction) {
        if (laneNumber == null || laneNumber.isEmpty()) {
            return "";
        }

        boolean isUpDirection = "1".equals(direction); // 1=上行，2=下行
        StringBuilder result = new StringBuilder();

        // 判断是否包含逗号
        String[] lanes;
        if (laneNumber.contains(",")) {
            lanes = laneNumber.split(",");
        } else {
            // 没有逗号，按单个字符拆分
            lanes = new String[laneNumber.length()];
            for (int i = 0; i < laneNumber.length(); i++) {
                lanes[i] = String.valueOf(laneNumber.charAt(i));
            }
        }

        // 转换每个车道号
        for (int i = 0; i < lanes.length; i++) {
            if (i > 0) {
                result.append(",");
            }

            String lane = lanes[i].trim();
            try {
                int laneNum = Integer.parseInt(lane);
                if (isUpDirection) { // 上行
                    switch (laneNum) {
                        case 1: result.append("C"); break;
                        case 2: result.append("A"); break;
                        case 3: result.append("E"); break;
                        case 4: result.append("G"); break;
                        default: result.append(lane); break;
                    }
                } else { // 下行
                    switch (laneNum) {
                        case 1: result.append("D"); break;
                        case 2: result.append("B"); break;
                        case 3: result.append("F"); break;
                        case 4: result.append("H"); break;
                        default: result.append(lane); break;
                    }
                }
            } catch (NumberFormatException e) {
                // 如果不是数字，保持原样
                result.append(lane);
            }
        }

        return result.toString();
    }
} 