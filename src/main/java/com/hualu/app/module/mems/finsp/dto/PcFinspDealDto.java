package com.hualu.app.module.mems.finsp.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class PcFinspDealDto implements Serializable {

    private static final long serialVersionUID = 1L;

    // 边坡ID
    private String slopeId;

    private Double x;

    private Double y;

    //最高处治类型
    private String dealType;

    private String slopeName;
}
