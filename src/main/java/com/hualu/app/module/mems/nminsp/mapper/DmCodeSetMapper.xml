<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.DmCodeSetMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.DmCodeSet">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="orgCode" column="ORG_CODE" jdbcType="VARCHAR"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
            <result property="code" column="CODE" jdbcType="VARCHAR"/>
            <result property="range" column="RANGE" jdbcType="VARCHAR"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ORG_CODE,CREATE_USER,
        CREATE_TIME,UPDATE_TIME,UPDATE_USER,
        CODE,RANGE,IS_DELETE
    </sql>
</mapper>
