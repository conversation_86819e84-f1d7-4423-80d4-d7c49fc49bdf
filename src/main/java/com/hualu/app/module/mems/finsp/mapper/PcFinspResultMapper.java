package com.hualu.app.module.mems.finsp.mapper;

import com.hualu.app.module.mems.finsp.dto.word.PcFinspRecordWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 结构物排查结论表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface PcFinspResultMapper extends BaseMapper<PcFinspResult> {

    List<PcFinspRecordWordDto> listRecordWord(@Param("finspIds") List<String> finspIds,@Param("prjId") String prjId);
}
