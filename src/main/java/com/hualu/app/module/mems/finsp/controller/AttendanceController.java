package com.hualu.app.module.mems.finsp.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto;
import com.hualu.app.module.mems.finsp.entity.Attendance;
import com.hualu.app.module.mems.finsp.entity.AttendanceAddr;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.SlopeBatchAttentionGs;
import com.hualu.app.module.mems.finsp.mapper.AttendanceMapper;
import com.hualu.app.module.mems.finsp.service.AttendanceAddrService;
import com.hualu.app.module.mems.finsp.service.AttendanceService;
import com.hualu.app.module.mems.finsp.service.DmFinspRecordService;
import com.hualu.app.module.mems.finsp.service.SlopeBatchAttentionGsService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Validated
@Api(tags = "打卡签到接口")
@RestController
@RequestMapping("/attendance")
public class AttendanceController extends M_MyBatisController<Attendance, AttendanceMapper> {

    @Autowired
    AttendanceService service;

    @Autowired
    AttendanceAddrService addrService;

    @Autowired
    DmFinspRecordService recordService;

    @Resource
    private SlopeBatchAttentionGsService slopeBatchAttentionGsService;

    /**
     * 查询养护历史数据
     * @param structId 结构物ID
     * @param facilityCat 结构物类型
     * @return
     */
    @PostMapping("selectHisRecord")
    public RestResult<DmFinspRecord> selectHisRecord(String structId,String facilityCat){
        IPage iPage = recordService.selectHisRecordByStructId(structId, facilityCat);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }


    @ApiOperation("签到记录分页查询")
    @PostMapping("selectPage")
    @Override
    public RestResult<List<Attendance>> selectPage(){
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        queryWrapper.in("org_code", H_DataAuthHelper.selectOrgIds());
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * HSMSDB.SLOPE_BATCH_ATTENTION_GS
     * 经常检查打卡边坡批量签到
     * @param batchId 批量边坡的id
     * @param validLocation 1需要验证，0不需要验证
     * @param lat 纬度
     * @param lng 经度
     * @return
     */
    @ApiOperation("边坡批量签到")
    @RequestMapping(value = "/batchSaveAttendance", method = { RequestMethod.POST, RequestMethod.GET})
    public RestResult<List<SlopeBatchAttentionDto>> batchSaveAttendance(
        String batchId,
        @RequestParam(defaultValue = "1") String validLocation,
        Double lat,
        Double lng
    ) {
        return service.batchSaveAttendance(batchId, validLocation, lat, lng);
    }

    /**
     * HSMSDB.SLOPE_BATCH_ATTENTION_GS
     * 获取边坡批量打卡单
     * @param id 批量边坡的id
     * @return
     */
    @ApiOperation("获取边坡批量打卡单")
    @GetMapping(value = "/getBatchAttentionInfo")
    public RestResult<SlopeBatchAttentionGs> getBatchAttentionInfo(
        String id
    ) {
        return RestResult.success(slopeBatchAttentionGsService.getById(id));
    }

    /**
     * 经常检查打卡签到
     * @param attendance
     * @return
     */
    @ApiOperation("签到")
    @PostMapping(value = "/saveAttendance")
    public RestResult<Attendance> save(@Validated @RequestBody Attendance attendance){
        String message = "";
        try {
            service.attendance(attendance);
        } catch (Exception ep) {
            if (ep instanceof BaseException){
                //当打卡失败时，显示对应的结构物名称
                int code = ((BaseException) ep).getCode();
                if (-11 == code){
                    IBaseStructFace iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                            .filter(e -> e.getFacilityCat().equals(attendance.getType())).findFirst().orElse(null);
                    BaseStructDto structDto = iBaseStructFace.getId(attendance.getStructId());
                    if (structDto == null){
                        throw new BaseException("未找到结构物");
                    }
                    attendance.setStructName(structDto.getStructName());
                }
                message = ep.getMessage();
                RestResult<Attendance> errorResult = RestResult.error(attendance,message);
                return errorResult;
            }
        }
        return RestResult.success(attendance);
    }

    @ApiOperation("签到照片上传")
    @PostMapping(value = "/uploadAttendanceImage")
    public RestResult<String> uploadAttendanceImage(String attendanceId,@RequestParam(value = "files",required = false) MultipartFile[] files){
        Attendance attendance = service.getById(attendanceId);
        if (attendance == null){
            throw new BaseException("未找到扫码打卡数据");
        }
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            attendance.setFieldId(StrUtil.join(",",fileIds));
            service.updateById(attendance);
        }
        return RestResult.success("图片上传成功");
    }

    /**
     * 新增打卡点
     * @param addr
     * @return
     */
    @ApiOperation("新增打卡点")
    @PostMapping(value = "/addAddr")
    public RestResult<AttendanceAddr> addAddr(@Validated @RequestBody AttendanceAddr addr){

        IBaseStructFace iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(addr.getFacilityCat())).findFirst().orElse(null);
        BaseStructDto structDto = iBaseStructFace.getId(addr.getStructId());
        addr.setStructName(structDto.getStructName());
        addr.setOrgCode(CustomRequestContextHolder.getOrgIdString());
        addrService.saveOrUpdate(addr);
        return RestResult.success(addr);
    }

    /**
     * 获取打卡点
     * @param structId 结构物ID
     * @param facilityCat 结构物类型
     * @return
     */
    @PostMapping(value = "/selectAddr")
    public RestResult<List<AttendanceAddr>> selectAddr(@NotBlank String structId,@NotBlank String facilityCat){
        List<AttendanceAddr> attendanceAddrs = addrService.selectAddr(structId, facilityCat);
        return RestResult.success(attendanceAddrs);
    }

    /**
     * 获取打卡记录
     *
     * @param startDate 开始时间 格式yyyy-MM-dd
     * @param endDate   结束时间  格式yyyy-MM-dd
     * @param page 当前页
     * @param pageSize 页数
     * @return
     */
    @ApiOperation("获取打卡记录")
    @PostMapping("/getAttentionList")
    public RestResult<List<Attendance>> getAttentionList(
        @RequestParam(value = "startDate", required = false) String startDate,
        @RequestParam(value = "endDate", required = false) String endDate,
        @RequestParam(value = "facilityCat", required = false) String facilityCat,
        @RequestParam(value = "page", required = false, defaultValue = "1") int page,
        @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize
    ) {
        IPage<Attendance> p = service.getAttentionList(startDate, endDate, facilityCat, page, pageSize);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(p));
    }

}
