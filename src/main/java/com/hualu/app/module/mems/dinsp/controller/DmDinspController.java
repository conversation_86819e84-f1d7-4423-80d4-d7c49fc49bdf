package com.hualu.app.module.mems.dinsp.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.mems.dinsp.entity.*;
import com.hualu.app.module.mems.dinsp.mapper.DmDinspMapper;
import com.hualu.app.module.mems.dinsp.service.DmDinResultService;
import com.hualu.app.module.mems.dinsp.service.DmDinspService;
import com.hualu.app.module.mems.dinsp.service.InspectTrackGroupService;
import com.hualu.app.module.mems.dinsp.service.InspectTrackService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_PageHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "日常巡查接口")
@RestController
@RequestMapping("/dmDinsp")
public class DmDinspController extends M_MyBatisController<DmDinsp, DmDinspMapper> {

    @Autowired
    private DmDinspService dmDinspService;

    @Autowired
    private BaseLineService baseLineService;

    @Autowired
    private DmDinResultService dmDinResultService;

    @Autowired
    private InspectTrackService inspectTrackService;

    @Autowired
    private InspectTrackGroupService inspectTrackGroupService;

    @ApiOperation("分页查询")
    @PostMapping("selectPage")
    @Override
    public RestResult<List<DmDinsp>> selectPage() {
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        queryWrapper.orderByDesc("INSP_DATE");
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        List<DmDinsp> records = iPage.getRecords();

        Map<String, BaseLine> allLineMap = baseLineService.getAllLineMap();
        //设置路线显示名称
        records.forEach(item->{
            BaseLine baseLine = allLineMap.get(item.getLineCode());
            if (baseLine != null){
                item.setLineName(baseLine.getLineAllname()+"("+baseLine.getLineCode()+")");
            }
        });
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    @Override
    protected QueryWrapper initQueryWrapper(Map params) {
        QueryWrapper<DmDinsp> queryWrapper = new QueryWrapper();
        initQueryParam(queryWrapper,params);
        H_BatisQuery.setFieldValue2In(queryWrapper, params, this.getEntityClass());
        return queryWrapper;
    }

    private <T> void initQueryParam(QueryWrapper<T> queryWrapper, Map params){
        queryWrapper.apply("type = '1' ");
        Object startDate = params.get("startDate");
        if (!StrUtil.isBlankIfStr(startDate)){
            queryWrapper.apply("insp_date >= to_date({0},'YYYY-MM-dd')",startDate);
            params.remove("startDate");
        }

        Object endDate = params.get("endDate");
        if (!StrUtil.isBlankIfStr(endDate)){
            queryWrapper.apply("insp_date <= to_date({0},'YYYY-MM-dd')",endDate);
            params.remove("endDate");
        }

        String status = params.getOrDefault("status", "").toString();
        if(!"0".equals(status)){
            queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());
        }
        String taskSql = H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.valueOf(status), "dm_dinsp");

        if (StrUtil.isNotBlank(taskSql)){
            queryWrapper.exists(taskSql);
            params.remove("status");
        }
        queryWrapper.orderByDesc("insp_date");
    }

    @ApiOperation("日常巡查流程办理")
    @PostMapping("approveDmDinsp")
    public RestResult<String> approveDmDinsp(String processInstIds,String appvOpinion,String nextUserId,int status){
        dmDinspService.approveDmDinsp(CustomRequestContextHolder.getUserCode(),processInstIds,nextUserId,appvOpinion,status);
        return RestResult.success("办理成功");
    }

    @ApiOperation("删除日常巡查病害")
    @PostMapping("deleteRecord")
    public RestResult<String> deleteRecord(String dssIds, String dinspId){
        if(StrUtil.isBlank(dinspId)){
            throw new BaseException("主单ID为空");
        }
        if(StrUtil.isBlank(dssIds)){
            throw new BaseException("病害ID为空");
        }
        String[] ids = dssIds.split(",");
        if(null == ids || ids.length == 0){
            throw new BaseException("病害ID为空");
        }
        dmDinspService.deleteRecord(ids,dinspId);
        return RestResult.success("删除成功");
    }

    @ApiOperation("日常巡查记录")
    @PostMapping("queryDmDinResult")
    public RestResult<List<DmDinResult>> queryDmDinResult(String dinspId){
        if(StrUtil.isBlank(dinspId)){
            throw new BaseException("巡查单ID为空");
        }
        QueryWrapper<DmDinResult> queryWrapper = new QueryWrapper();
        queryWrapper.eq("DM_DINSP_ID",dinspId);
        queryWrapper.orderByAsc("XUHAO");
        List<DmDinResult> dinResults = dmDinResultService.list(queryWrapper);
        return RestResult.success(dinResults);
    }

    @ApiOperation("更新日常巡查记录")
    @PostMapping("updateDmDinResult")
    public RestResult<String> updateDmDinResult(@RequestBody DmDinResult result){
        if(result == null){
            throw new BaseException("巡查记录为空");
        }
        dmDinspService.updateResult(result);
        return RestResult.success("更新成功");
    }

    @ApiOperation("删除日常巡查主单")
    @PostMapping("deleteDmDinsp")
    public RestResult<String> deleteDmDinsp(String dinspId){
        if(StrUtil.isBlank(dinspId)){
            throw new BaseException("主单ID为空");
        }
        dmDinspService.deleteDmDinsp(dinspId);
        return RestResult.success("删除成功");
    }

    @ApiOperation("获取巡查轨迹")
    @PostMapping("queryTrack")
    public RestResult<List<InspectTrack>> queryTrack(String dinspId) {
        if (StrUtil.isBlank(dinspId)) {
            throw new BaseException("主单ID为空");
        }

        LambdaQueryWrapper<InspectTrack> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(InspectTrack::getInspectId, dinspId)
            .orderByAsc(InspectTrack::getGroupId, InspectTrack::getOrders);

        return RestResult.success(inspectTrackService.list(wrapper));
    }

    /**
     * 日常巡查巡检单的总体数量
     */
    @ApiOperation("日常巡查巡检单的总体数量")
    @PostMapping("queryGroupCount")
    public RestResult<List<DmCountResult>> queryGroupCount() {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.queryGroupCount(orgId));
    }

    /**
     * 日常巡查巡检单的总体数量
     */
    @ApiOperation("经常检查的总体数量")
    @PostMapping("queryFinspGroupCount")
    public RestResult<List<DmCountResult>> queryFinspGroupCount() {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.queryFinspGroupCount(orgId));
    }

    /**
     * 日常巡查和经常检查巡检单的台账
     */
    @ApiOperation("日常巡查和经常检查巡检单的台账")
    @PostMapping("listInspectionLedgerBillsAll")
    public RestResult<IPage<InspectionLedgerDTO>> listInspectionLedgerBillsAll(@RequestParam(defaultValue = "1") int pageNum,
                                                                            @RequestParam(defaultValue = "10") int pageSize,
                                                                            @RequestParam(required = false) String status,
                                                                             @RequestParam(required = false) String type
                                                                               ) {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return "1".equals(type) ? RestResult.success(dmDinspService.listInspectionLedgerBills(pageNum, pageSize, orgId, status)) : RestResult.success(dmDinspService.listInspectionRCLedgerBills(pageNum, pageSize, orgId, status));
    }


    /**
     * 日常巡查巡检单的台账
     */
    @ApiOperation("日常巡查巡检单的台账")
    @PostMapping("listInspectionLedgerBills")
    public RestResult<IPage<InspectionLedgerDTO>> listInspectionLedgerBills(@RequestParam(defaultValue = "1") int pageNum,
                                                                            @RequestParam(defaultValue = "10") int pageSize,
                                                                            @RequestParam(required = false) String status) {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.listInspectionLedgerBills(pageNum, pageSize, orgId, status));
    }

    /**
     * 日常巡查巡检单的台账
     */
    @ApiOperation("经常检查巡检单的台账")
    @PostMapping("listInspectionRCLedgerBills")
    public RestResult<IPage<InspectionLedgerDTO>> listInspectionRCLedgerBills(@RequestParam(defaultValue = "1") int pageNum,
                                                                              @RequestParam(defaultValue = "10") int pageSize,
                                                                              @RequestParam(required = false) String status) {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.listInspectionRCLedgerBills(pageNum, pageSize, orgId, status));
    }

    /**
     * 日常巡查和经常检查 病害数量统计
     */
        @ApiOperation("日常/经常巡查病害数量统计")
    @PostMapping("countDailyDefectsAll")
    public RestResult<Map<String, Object>> countDailyDefectsAll(@RequestParam(value = "type") String type)
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return Objects.equals(type, "rc") ?  RestResult.success(dmDinspService.countDailyDefects(orgId)) : RestResult.success(dmDinspService.calculateDefectsByCategory(orgId));
    }


    /**
     * 日常巡查巡检单的台账
     */
    @ApiOperation("日常巡查病害数量统计")
    @PostMapping("countDailyDefects")
    public RestResult<Map<String, Object>> countDailyDefects()
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.countDailyDefects(orgId));
    }

    /**
     * 日常巡查巡检单的台账
     */
    @ApiOperation("经常检查病害数量统计")
    @PostMapping("calculateDefectsByCategory")
    public RestResult<Map<String, Object>> calculateDefectsByCategory()
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.calculateDefectsByCategory(orgId));
    }

    /**
     * 日常巡查和经常检查 病害台账
     * @param pageNum
     * @param pageSize
     * @return
     */
    @ApiOperation("日常巡查和经常检查 病害台账")
    @PostMapping("indexDefectRecordsForFastQueryAll")
    public RestResult<IPage<FacilityInspectionDTO>> indexDefectRecordsForFastQueryAll(@RequestParam(defaultValue = "1") int pageNum,
                                                                                   @RequestParam(defaultValue = "10") int pageSize,
                                                                                   @RequestParam(required = false) String type)
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return Objects.equals(type, "jc") ?  RestResult.success(dmDinspService.indexDefectRecordsForFastQuery(pageNum, pageSize, orgId))
                : RestResult.success(dmDinspService.indexDefectRecordsRcForFastQuery(pageNum, pageSize, orgId));
    }

    @ApiOperation("病害台账")
    @PostMapping("indexDefectRecordsForFastQuery")
    public RestResult<IPage<FacilityInspectionDTO>> indexDefectRecordsForFastQuery(@RequestParam(defaultValue = "1") int pageNum,
                                                                                   @RequestParam(defaultValue = "10") int pageSize)
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.indexDefectRecordsForFastQuery(pageNum, pageSize, orgId));
    }

    @ApiOperation("经常检查病害台账")
    @PostMapping("indexDefectRecordsRcForFastQuery")
    public RestResult<IPage<FacilityInspectionDTO>> indexDefectRecordsRcForFastQuery(@RequestParam(defaultValue = "1") int pageNum,
                                                                                     @RequestParam(defaultValue = "10") int pageSize)
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.indexDefectRecordsRcForFastQuery(pageNum, pageSize, orgId));
    }

    @ApiOperation("病害照片")
    @PostMapping("getImageFileId")
    public RestResult<List<String>> getImageFileId(String dssId)
    {
        return RestResult.success(dmDinspService.getImageFileId(dssId));
    }
    @ApiOperation("病害详细信息")
    @PostMapping("InspectionResult")
    public RestResult<List<InspectionResult>> InspectionResult(String dssId)
    {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.InspectionResult(orgId, dssId));
    }

    @ApiOperation("巡检路线")
    @PostMapping("InspectionRecord")
    public RestResult<IPage<InspectionRecord>> InspectionRecord(@RequestParam(defaultValue = "1") int pageNum,
                                                                @RequestParam(defaultValue = "10") int pageSize){
        String orgId = CustomRequestContextHolder.getOrgIdString();
        return RestResult.success(dmDinspService.InspectionRecord(pageNum, pageSize, orgId));
    }
}
