package com.hualu.app.module.mems.dinsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.dinsp.entity.DmDinspRecord;
import com.hualu.app.module.mems.dinsp.mapper.DmDinspRecordMapper;
import com.hualu.app.module.mems.dinsp.service.DmDinspRecordService;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.service.IDssRecordService;
import com.hualu.app.utils.mems.H_DssHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
public class DmDinspRecordServiceImpl extends ServiceImpl<DmDinspRecordMapper, DmDinspRecord> implements DmDinspRecordService, IDssRecordService {

    @Override
    public List<DmDinspRecord> selectRecordByDinspId(String dinspId) {
        QueryWrapper<DmDinspRecord> queryWrapper = new QueryWrapper();
        queryWrapper.eq("DINSP_ID",dinspId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<DmDinspRecord> selectRecords(String[] dssIds) {
        return baseMapper.selectRecords(dssIds);
    }

    @Override
    public String getDssSource() {
        return "1";
    }

    @Override
    public List<DssInfoDto> selectDssRecordPage(Page page, QueryWrapper queryWrapper) {
        List<DssInfoDto> dssInfoDtos = baseMapper.selectDssRecordPage(page,queryWrapper);
        H_DssHelper.setCheckInfo(dssInfoDtos);
        H_DssHelper.setMtaskCode(dssInfoDtos);
        return dssInfoDtos;
    }

    @Override
    public void updateDssRecordByCloseType(List<String> dssIds, String closeType) {
        if (CollectionUtil.isEmpty(dssIds)){
            return;
        }
        LambdaUpdateWrapper<DmDinspRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(DmDinspRecord::getDssId, dssIds);
        updateWrapper.set(DmDinspRecord::getCloseType, closeType);
        update(updateWrapper);
    }
}
