package com.hualu.app.module.mems.finsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.entity.PcFinspGps;
import com.hualu.app.module.mems.finsp.mapper.PcFinspGpsMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspGpsService;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 结构物排查轨迹表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Api(tags = "PcFinspGpsController",description = "结构物排查轨迹表 前端控制器")
@RestController
@RequestMapping("/pcFinspGps")
public class PcFinspGpsController extends M_MyBatisController<PcFinspGps,PcFinspGpsMapper>{

    @Autowired
    private PcFinspGpsService service;

    /**
     * 批量保存GPS数据
     * @param gpsList
     * @return
     */
    @ApiRegister(value = "pcFinspGps:saveBatchGps",businessType = BusinessType.UPDATE)
    @ApiOperation("批量保存GPS数据")
    @PostMapping("saveBatchGps")
    public RestResult<String> saveBatchGps(@RequestBody List<PcFinspGps> gpsList) {
        if (CollectionUtil.isNotEmpty(gpsList)) {
            service.saveOrUpdateBatch(gpsList);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 获取轨迹数据集合
     * @param finspId 经常检查单ID
     * @return
     */
    @ApiOperation("获取轨迹数据集合")
    @GetMapping("getGpsByDinspId")
    public RestResult<List<PcFinspGps>> getGpsByFinspId(String finspId) {
        LambdaQueryWrapper<PcFinspGps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspGps::getFinspId,finspId).orderByAsc(PcFinspGps::getTime);
        List<PcFinspGps> list = service.list(queryWrapper);
        return RestResult.success(list);
    }

    /**
     * 获取实时位置信息
     * @param finspId 经常检查单ID
     * @return
     */
    @GetMapping("getRealTimeLocationByFinspId")
    public RestResult<PcFinspGps> getRealTimeLocationByFinspId(String finspId) {
        PcFinspGps one = baseMapper.getRealTimeLocationByFinspId(finspId);
        return RestResult.success(one);
    }
}