package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.service.*;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import com.tg.dev.api.util.hp.H_CValidator;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service("sdStructImp")
public class SdStructImp implements IStruct {

  @Autowired
  BaseRouteLogicService routeLogicService;

  private static final String OTHER = "OTHER";

  private static final String ISSUE_DESC_FORMAT = "{}存在{}，{}";
  @Autowired
  NmDinspResultService dmResultService;

  @Autowired
  NmDinspRecordService dmRecordService;

  @Autowired
  NmFinspResultService finspResultService;

  @Autowired
  NmFinspRecordService finspRecordService;

  @Autowired
  private NmInspContentService service;

  private static final String FAC_SD = "SD";
  private static final String XC_TYPE_FM = "FM";
  private static final String XC_TYPE_DM = "DM";

  @Override
  public void validateNmDinsp(NmDinsp nmDinsp) {
    H_CValidator.validator2Exception(nmDinsp, new Class[] { StructGroup.class });
    // 根据结构物ID,设置结构物名称
    BaseStructDto structInfo = getStructInfo(nmDinsp.getFacilityCat(), nmDinsp.getStructId());
    nmDinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());
    BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmDinsp.getRouteCode());
    if (routeLogic != null) {
      nmDinsp.setRouteName(routeLogic.getRouteName());
    }
  }

  @Override public void validateNmFinsp(NmFinsp nmFinsp) {
    H_CValidator.validator2Exception(nmFinsp, new Class[] { StructGroup.class });
    // 根据结构物ID,设置结构物名称
    BaseStructDto structInfo = getStructInfo(nmFinsp.getFacilityCat(), nmFinsp.getStructId());
    nmFinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());
    BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmFinsp.getRouteCode());
    if (routeLogic != null) {
      nmFinsp.setRouteName(routeLogic.getRouteName());
    }
  }

  @Override
  public void refreshNmDinspResult(NmDinsp nmDinsp) {
    List<NmDinspResult> nmFinspResult = dmResultService.createNmDinspResult(nmDinsp);
    List<NmDinspRecord> nmFinspRecords = dmRecordService.selectRecordByDinspId(nmDinsp.getDinspId());
    dmRecordService.showView(nmFinspRecords,nmDinsp);
    if (CollectionUtil.isEmpty(nmFinspResult) || CollectionUtil.isEmpty(nmFinspRecords)) {
      //清空结论
      dmResultService.updateBatchById(nmFinspResult);
      return;
    }
    // 根据部件进行分组
    Map<String, List<NmDinspRecord>> partMap = nmFinspRecords.stream().filter(e-> StrUtil.isNotBlank(e.getStructPartId()))
        .collect(Collectors.groupingBy(NmDinspRecord::getStructPartId));
    for (NmDinspResult item : nmFinspResult) {
      if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())){
        continue;
      }
      // partId会存在多个
      List<String> partIds = StrUtil.split(item.getPartId(), ",");
      List<NmDinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
      if (CollectionUtil.isNotEmpty(dssList)) {
        //经常检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
        List<NmDinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(containsDssList)) {
          //String issueDesc = containsDssList.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
          item.setIssueDesc(getIssueDescByDm(containsDssList));
          // 移除已经匹配的病害
          nmFinspRecords.removeAll(dssList);
        }
      }
    }
    // 获取其他项结论
    NmDinspResult otherResult = nmFinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
    if (otherResult != null && CollectionUtil.isNotEmpty(nmFinspRecords)) {
      //String issueDesc = nmFinspRecords.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
      otherResult.setIssueDesc(getIssueDescByDm(nmFinspRecords));
    }
    dmResultService.updateBatchById(nmFinspResult);
  }

  @Override
  public void refreshNmFinspResult(NmFinsp nmFinsp) {
    List<NmFinspResult> nmFinspResult = finspResultService.createNmFinspResult(nmFinsp);
    List<NmFinspRecord> nmFinspRecords = finspRecordService.selectRecordByFinspId(nmFinsp.getFinspId());
    finspRecordService.showView(nmFinspRecords,nmFinsp);
    if (CollectionUtil.isEmpty(nmFinspResult) || CollectionUtil.isEmpty(nmFinspRecords)) {
      //清空结论
      finspResultService.updateBatchById(nmFinspResult);
      return;
    }
    // 根据部件进行分组
    Map<String, List<NmFinspRecord>> partMap = nmFinspRecords.stream().filter(e-> StrUtil.isNotBlank(e.getStructPartId()))
        .collect(Collectors.groupingBy(NmFinspRecord::getStructPartId));
    for (NmFinspResult item : nmFinspResult) {
      if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())){
        continue;
      }
      // partId会存在多个
      List<String> partIds = StrUtil.split(item.getPartId(), ",");
      List<NmFinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
      if (CollectionUtil.isNotEmpty(dssList)) {
        //经常检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
        List<NmFinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(containsDssList)) {
          //String issueDesc = containsDssList.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
          item.setIssueDesc(getIssueDescByFm(containsDssList));
          // 移除已经匹配的病害
          nmFinspRecords.removeAll(dssList);
        }
      }
    }
    // 获取其他项结论
    NmFinspResult otherResult = nmFinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
    if (otherResult != null && CollectionUtil.isNotEmpty(nmFinspRecords)) {
      //String issueDesc = nmFinspRecords.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
      otherResult.setIssueDesc(getIssueDescByFm(nmFinspRecords));
    }
    finspResultService.updateBatchById(nmFinspResult);
  }

  private String getIssueDescByDm(List<NmDinspRecord> dssRecords) {
    if (CollectionUtil.isEmpty(dssRecords)) {
      return null;
    }
    List<String> issueList = Lists.newArrayList();
    dssRecords.forEach(e -> {
      String format = StrUtil.format(ISSUE_DESC_FORMAT, e.getStructPartName(),e.getDssTypeName(), e.getDssNum());
      issueList.add(format);
    });
    return issueList.stream().collect(Collectors.joining("；"));
  }

  private String getIssueDescByFm(List<NmFinspRecord> dssRecords) {
    if (CollectionUtil.isEmpty(dssRecords)) {
      return null;
    }
    List<String> issueList = Lists.newArrayList();
    dssRecords.forEach(e -> {
      String format = StrUtil.format(ISSUE_DESC_FORMAT, e.getStructPartName(),e.getDssTypeName(), e.getDssNum());
      issueList.add(format);
    });
    return issueList.stream().collect(Collectors.joining("；"));
  }

  @Override public void showNmFinspView(List<NmFinspRecord> nmFinspRecords) {
    if (CollectionUtil.isEmpty(nmFinspRecords)) {
      return;
    }

    List<NmInspContent> contents = service.listByContent(FAC_SD, XC_TYPE_FM);
    if (CollectionUtils.isEmpty(contents)) {
      return;
    }
    Map<String, String> partNameMap = contents.stream()
        .collect(Collectors.toMap(NmInspContent::getId, NmInspContent::getName));

    for (NmFinspRecord i : nmFinspRecords) {
      i.setStructPartName(partNameMap.getOrDefault(i.getStructPartId(), ""));
    }
  }

  @Override public void showNmDinspView(List<NmDinspRecord> nmDinspRecords) {
    if (CollectionUtil.isEmpty(nmDinspRecords)) {
      return;
    }
    List<NmInspContent> contents = service.listByContent(FAC_SD, XC_TYPE_DM);
    if (CollectionUtils.isEmpty(contents)) {
      return;
    }
    Map<String, String> partNameMap = contents.stream()
        .collect(Collectors.toMap(NmInspContent::getId, NmInspContent::getName));

    for (NmDinspRecord i : nmDinspRecords) {
      i.setStructPartName(partNameMap.getOrDefault(i.getStructPartId(), ""));
    }
  }

  @Autowired
  NmDinspRecordService dinspRecordService;

  @Override
  public List<String> processAndExportNmDinspWord(List<NmDinsp> nmDinsps, String parentPath) {

    nmDinsps.forEach(nmDinsp -> {
      // 日常巡查只有一种类型，为了解决app公用单传递的2的情况，所以需要重新赋值
      nmDinsp.setInspFrequency(1);
    });
    return H_WordHelper.processAndExportWord(
            false,
        nmDinsps,
        parentPath,
        "DM",
        NmDinsp::getFacilityCat,
        NmDinsp::getInspFrequency,
        NmDinsp::getDinspId,
        NmDinsp::getDinspCode,
        NmDinsp::getInspDate,
        dinspIds -> dmResultService.listResultsByDinspIds(dinspIds),
        NmDinspResult::getDinspId,
        (records, entity) -> {
          // 设置模版显示序号
          AtomicInteger atomicInteger =new AtomicInteger(1);
          records.forEach(record -> {
            record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
          });
        }
    );
  }

  @Override
  public List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps, String parentPath) {
    return H_WordHelper.processAndExportWord(
            false,
        nmFinsps,
        parentPath,
        "FM",
        NmFinsp::getFacilityCat,
        NmFinsp::getInspFrequency,
        NmFinsp::getFinspId,
        NmFinsp::getFinspCode,
        NmFinsp::getInspDate,
        finspIds -> finspResultService.listResultsByFinspIds(finspIds,"SD"),
        NmFinspResult::getFinspId,
        (records, entity) -> {
          // 设置模版显示序号
          AtomicInteger atomicInteger =new AtomicInteger(1);
          records.forEach(record -> {
            record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
          });
        }
    );
  }

}
