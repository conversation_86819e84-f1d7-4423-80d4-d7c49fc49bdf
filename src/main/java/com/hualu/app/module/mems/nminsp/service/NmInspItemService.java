package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmInspItem;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新版日常巡查及经常检查结论配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface NmInspItemService extends IService<NmInspItem> {

    List<NmInspItem> listXcTypeByDinsp(NmDinsp nmDinsp, String xcType);

    Map<String,NmInspItem> listXcTypeToMap(String xcType);

    List<NmInspItem> listXcTypeByFinsp(NmFinsp nmDinsp, String xcType);
}
