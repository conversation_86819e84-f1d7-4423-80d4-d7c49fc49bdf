package com.hualu.app.module.mems.mpc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.mpc.dto.MpcContractDto;
import com.hualu.app.module.mems.mpc.entity.MpcContract;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
public interface MpcContractService extends IService<MpcContract> {

    /**
     * 查询合同数据
     * @param mngOrgId
     * @return
     */
    List<MpcContractDto> selectMpcContracts(String mngOrgId);
}
