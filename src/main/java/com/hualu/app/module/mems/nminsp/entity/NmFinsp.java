package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.hualu.app.module.mems.nminsp.validate.LmAddGroup;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
/**
 * <p>
 * 新版经常巡查 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmFinsp对象", description="新版经常巡查")
public class NmFinsp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结构物中心桩号")
    @TableField("STRUCT_STAKE_NUM")
    private Double structStakeNum;

    @ApiModelProperty(value = "主键")
    @TableId("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "检查单号")
    @TableField("FINSP_CODE")
    private String finspCode;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @NotBlank(message = "设施类型不能为空")
    @ApiModelProperty(value = "设施类型;LM、QL、HD、BP、SD、JA")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @NotBlank(message = "结构物ID不能为空",groups = {StructGroup.class})
    @ApiModelProperty(value = "结构物ID")
    @TableField("STRUCT_ID")
    private String structId;

    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @ApiModelProperty(value = "管养单位ID")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "管养单位名称")
    @TableField("MNT_ORG_NM")
    private String mntOrgNm;

    @NotBlank(message = "巡查单位不能为空")
    @ApiModelProperty(value = "养护单位")
    @TableField("SEARCH_DEPT")
    private String searchDept;

    @ApiModelProperty(value = "巡查人")
    @TableField("INSP_PERSON")
    private String inspPerson;

    @ApiModelProperty(value = "巡查日期")
    @TableField("INSP_DATE")
    private Date inspDate;

    @ApiModelProperty(value = "巡查/检查时间;时分秒：10:01:34")
    @TableField("INSP_TIME")
    private String inspTime;

    @ApiModelProperty(value = "巡查频率;1：边坡旱季，2：边坡雨季前后，3：边坡雨季中，1：梁式桥，2：拱桥（吊杆拱），5：斜拉桥，6：悬索桥")
    @TableField("INSP_FREQUENCY")
    private String inspFrequency;

    @NotBlank(groups = {LmAddGroup.class,StructGroup.class},message = "路线编码不能为空")
    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "路线名称")
    @TableField("LINE_NAME")
    private String lineName;

    @ApiModelProperty(value = "巡查方向;上行、下行、全线")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "路段名称;权限范围内的路段下拉框选择")
    @TableField("ROUTE_NAME")
    private String routeName;

    @ApiModelProperty(value = "桩号范围")
    @TableField("STAKE_NAME")
    private String stakeName;

    @NotBlank(groups = {LmAddGroup.class,StructGroup.class},message = "天气不能为空")
    @ApiModelProperty(value = "天气情况")
    @TableField("WEATHER")
    private String weather;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "数据状态;0:未提交，1：已提交")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "病害个数")
    @TableField("DSS_NUM")
    private Integer dssNum;

    @ApiModelProperty(value = "路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @TableLogic
    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;

    @ApiModelProperty(value = "巡查类型（1：养护单位，0：路段公司）")
    @TableField("XC_TYPE")
    private Integer xcType;

    /**
     * 检查工照的图片ID
     */
    @TableField(exist = false)
    private String fileIds;

    /**
     * 正面照
     */
    @TableField(exist = false)
    private String zmPictureIds;

    /**
     * 侧面照
     */
    @TableField(exist = false)
    private String cmPictureIds;

    /**
     * 天气情况中文格式
     */
    @TableField(exist = false)
    private String weatherName;

    /**
     * 负责人
     */
    @TableField(exist = false)
    private String leader;

    /**
     * 标题名称（负责人或者复核人）
     */
    @TableField(exist = false)
    private String leaderTitle;

    /**
     * 边坡简介
     */
    @TableField(exist = false)
    private String slopeProfile;


    /**
     * 桥位桩号
     */
    @TableField(exist = false)
    private String displayStake;
    /**
     * 桥梁编号
     */
    @TableField(exist = false)
    private String bridgeCode;

    /**
     * 流程共用多少单
     */
    @TableField(exist = false)
    private Long processCount;

    /**
     * 行政区划
     */
    @TableField(exist = false)
    private String areaCode;

    /**
     * 涵洞类型
     */
    @TableField(exist = false)
    private String culvertType;

}
