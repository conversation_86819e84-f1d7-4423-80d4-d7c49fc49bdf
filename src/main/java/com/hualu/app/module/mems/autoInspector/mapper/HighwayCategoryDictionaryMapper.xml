<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayCategoryDictionaryMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayCategoryDictionary">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="categoryCode" column="CATEGORY_CODE" jdbcType="VARCHAR"/>
            <result property="categoryName" column="CATEGORY_NAME" jdbcType="VARCHAR"/>
            <result property="parentCode" column="PARENT_CODE" jdbcType="VARCHAR"/>
            <result property="levelType" column="LEVEL_TYPE" jdbcType="DECIMAL"/>
            <result property="objectType" column="OBJECT_TYPE" jdbcType="VARCHAR"/>
            <result property="defectType" column="DEFECT_TYPE" jdbcType="VARCHAR"/>
            <result property="statusCode" column="STATUS_CODE" jdbcType="VARCHAR"/>
            <result property="statusName" column="STATUS_NAME" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="SORT_ORDER" jdbcType="DECIMAL"/>
            <result property="isEnabled" column="IS_ENABLED" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CATEGORY_CODE,CATEGORY_NAME,
        PARENT_CODE,LEVEL_TYPE,OBJECT_TYPE,
        DEFECT_TYPE,STATUS_CODE,STATUS_NAME,
        SORT_ORDER,IS_ENABLED
    </sql>
</mapper>
