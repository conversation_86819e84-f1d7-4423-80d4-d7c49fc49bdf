package com.hualu.app.module.mems.comm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用于app更新版本
 */
@ApiModel(description = "用于app更新版本")
@TableName(value = "GDGS.APP_UPDATE_VERSION")
public class AppUpdateVersion {
  @ApiModelProperty(value = "")
  private String versionId;

  /**
   * 版本代码
   */
  @ApiModelProperty(value = "版本代码")
  private BigDecimal versionCode;

  /**
   * 版本名称
   */
  @ApiModelProperty(value = "版本名称")
  private String versionName;

  /**
   * app的下载地址
   */
  @ApiModelProperty(value = "app的下载地址")
  private String appUrl;

  /**
   * 版本更新信息
   */
  @ApiModelProperty(value = "版本更新信息")
  private String versionInfo;

  /**
   * app的包名
   */
  @ApiModelProperty(value = "app的包名")
  private String applicationId;

  /**
   * 是否强制更新1更新，0不强制更新
   */
  @ApiModelProperty(value = "是否强制更新1更新，0不强制更新")
  private BigDecimal forceUpdate;

  /**
   * 发布日期
   */
  @ApiModelProperty(value = "发布日期")
  private Date releaseDate;

  public String getVersionId() {
    return versionId;
  }

  public void setVersionId(String versionId) {
    this.versionId = versionId;
  }

  public BigDecimal getVersionCode() {
    return versionCode;
  }

  public void setVersionCode(BigDecimal versionCode) {
    this.versionCode = versionCode;
  }

  public String getVersionName() {
    return versionName;
  }

  public void setVersionName(String versionName) {
    this.versionName = versionName;
  }

  public String getAppUrl() {
    return appUrl;
  }

  public void setAppUrl(String appUrl) {
    this.appUrl = appUrl;
  }

  public String getVersionInfo() {
    return versionInfo;
  }

  public void setVersionInfo(String versionInfo) {
    this.versionInfo = versionInfo;
  }

  public String getApplicationId() {
    return applicationId;
  }

  public void setApplicationId(String applicationId) {
    this.applicationId = applicationId;
  }

  public BigDecimal getForceUpdate() {
    return forceUpdate;
  }

  public void setForceUpdate(BigDecimal forceUpdate) {
    this.forceUpdate = forceUpdate;
  }

  public Date getReleaseDate() {
    return releaseDate;
  }

  public void setReleaseDate(Date releaseDate) {
    this.releaseDate = releaseDate;
  }
}