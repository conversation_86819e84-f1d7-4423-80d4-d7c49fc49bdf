package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 新版经常检查结论表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmFinspResult对象", description="新版经常检查结论表")
public class NmFinspResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("RES_ID")
    private String resId;

    @ApiModelProperty(value = "检查单ID")
    @TableField("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "配置项ID")
    @TableField("ITEM_ID")
    private String itemId;

    @ApiModelProperty(value = "问题描述/病害描述/检查情况/缺损范围")
    @TableField(value = "ISSUE_DESC",strategy = FieldStrategy.IGNORED)
    private String issueDesc;

    @ApiModelProperty(value = "处理结果/处治建议")
    @TableField(value = "DEAL_RESULT",strategy = FieldStrategy.IGNORED)
    private String dealResult;


    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @TableLogic
    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 描述一般性问题或缺陷
     */
    @TableField(value = "GENERAL_ABNORMALITY", strategy = FieldStrategy.IGNORED)
    private String generalAbnormality;

    /**
     * 描述需要紧急处理的问题
     */
    @TableField(value = "SERIOUS_ABNORMALITY", strategy = FieldStrategy.IGNORED)
    private String seriousAbnormality;

    /**
     * 记录持续监控的状态
     */
    @TableField(value = "TRACKING_MONITORING", strategy = FieldStrategy.IGNORED)
    private String trackingMonitoring;

    /**
     * 记录维修或处理措施
     */
    @TableField(value = "REPAIR_TREATMENT", strategy = FieldStrategy.IGNORED)
    private String repairTreatment;

    /**
     * 记录定期检查结果
     */
    @TableField(value = "REGULAR_INSPECTION", strategy = FieldStrategy.IGNORED)
    private String regularInspection;

    /**
     * 检查内容
     */
    @TableField(exist = false)
    private String inspCont;

    /**
     * 检查项
     */
    @TableField(exist = false)
    private String inspCom;

    /**
     * 关联病害类型
     */
    @TableField(exist = false)
    private String dssType;

    /**
     * 关联部位ID
     */
    @TableField(exist = false)
    private String partId;
}
