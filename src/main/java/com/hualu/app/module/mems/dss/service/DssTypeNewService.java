package com.hualu.app.module.mems.dss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-15
 */
public interface DssTypeNewService extends IService<DssTypeNew> {

    DssTypeNew selectByDssType(String dssType);

    List<DssTypeNew> listByDssType(List<String> dssTypes);

    /**
     * 设置病害单位的属性
     * @param obj 实体对象
     * @param dssType 病害类型
     */
    void setDssUnit(Object obj,String dssType);

}
