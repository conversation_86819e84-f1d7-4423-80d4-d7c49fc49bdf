package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 结构物排查结论表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinspResult对象", description="结构物排查结论表")
public class PcFinspResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "结论ID")
    @TableId("FINSP_RES_ID")
    private String finspResId;

    @ApiModelProperty(value = "检查表ID")
    @TableField("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "检查内容ID")
    @TableField("FINSP_ITEM_ID")
    private String finspItemId;

    @ApiModelProperty(value = "病害描述")
    @TableField(value = "FINSP_DESC",strategy = FieldStrategy.IGNORED)
    private String finspDesc;

    @ApiModelProperty(value = "检查结构（0：否，1：是）")
    @TableField("FINSP_RESULT")
    private String finspResult;

    @TableLogic
    @TableField(value = "DEL_FLAG",fill = FieldFill.INSERT)
    private Integer delFlag;

}
