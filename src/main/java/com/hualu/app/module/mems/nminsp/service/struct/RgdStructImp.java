package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspResultService;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import com.tg.dev.api.util.hp.H_CValidator;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service("rgdStructImp")
public class RgdStructImp implements IStruct{

    private static final String OTHER = "OTHER";

    private static final String ISSUE_DESC_FORMAT = "桩号{}处{}存在{}，{}";

    @Autowired
    NmDinspResultService dmResultService;

    @Autowired
    NmDinspRecordService dmRecordService;

    @Autowired
    NmFinspResultService finspResultService;

    @Autowired
    NmFinspRecordService finspRecordService;

    @Autowired
    BaseRouteLogicService routeLogicService;


    @Override
    public List<String> processAndExportNmDinspWord(List<NmDinsp> nmDinsps, String parentPath) {
        // 获取当前请求上下文
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return H_WordHelper.processAndExportWord(
                true,
                nmDinsps,
                parentPath,
                "DM",
                NmDinsp::getFacilityCat,
                NmDinsp::getInspFrequency,
                NmDinsp::getDinspId,
                NmDinsp::getDinspCode,
                NmDinsp::getInspDate,
                dinspIds -> dmResultService.listResultsByDinspIds(dinspIds),
                NmDinspResult::getDinspId,
                (records, entity) -> {
                    // 设置模版显示序号
                    AtomicInteger atomicInteger =new AtomicInteger(1);
                    records.forEach(record -> {
                        record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
                    });
                }
        );
    }

    @Override
    public List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps, String parentPath) {
        return H_WordHelper.processAndExportWord(
                true,
                nmFinsps,
                parentPath,
                "FM",
                NmFinsp::getFacilityCat,
                NmFinsp::getInspFrequency,
                NmFinsp::getFinspId,
                NmFinsp::getFinspCode,
                NmFinsp::getInspDate,
                finspIds -> finspResultService.listResultsByFinspIds(finspIds,"RGD"),
                NmFinspResult::getFinspId,
                (records, entity) -> {
                    // 设置模版显示序号
                    AtomicInteger atomicInteger =new AtomicInteger(1);
                    records.forEach(record -> {
                        record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
                    });
                }
        );
    }

    @Override
    public void refreshNmDinspResult(NmDinsp nmDinsp) {
        List<NmDinspResult> nmDinspResult = dmResultService.createNmDinspResult(nmDinsp);
        // 获取对应的病害ID
        List<NmDinspRecord> nmDinspRecords = dmRecordService.selectRecordByDinspId(nmDinsp.getDinspId());
        dmRecordService.showView(nmDinspRecords,nmDinsp);
        if (CollectionUtil.isEmpty(nmDinspResult) || CollectionUtil.isEmpty(nmDinspRecords)) {
            if (CollectionUtil.isNotEmpty(nmDinspResult)){
                //清空结论
                dmResultService.updateBatchById(nmDinspResult);
            }
            return;
        }
        // 边坡日常巡查，用不到partId判断
        for (NmDinspResult item : nmDinspResult) {
            if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())) {
                continue;
            }
            // 先匹配已经配置的病害项
            List<NmDinspRecord> dssRecords = nmDinspRecords.stream().filter(e ->item.getDssType().contains(e.getDssType()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dssRecords)){
                item.setIssueDesc(getIssueDescByDm(dssRecords));
                //移除已经匹配的病害信息
                nmDinspRecords.removeAll(dssRecords);
            }
        }
        // 获取其他项结论
        NmDinspResult otherResult = nmDinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
        if (otherResult != null && CollectionUtil.isNotEmpty(nmDinspRecords)) {
            otherResult.setIssueDesc(getIssueDescByDm(nmDinspRecords));
        }
        dmResultService.updateBatchById(nmDinspResult);
    }

    private String getIssueDescByDm(List<NmDinspRecord> dssRecords) {
        if (CollectionUtil.isEmpty(dssRecords)) {
            return null;
        }
        List<String> issueList = Lists.newArrayList();
        dssRecords.forEach(e -> {
            String format = StrUtil.format(ISSUE_DESC_FORMAT, e.getStakeCn(),e.getStructPartName(),e.getDssTypeName(), e.getDssNum());
            if (StrUtil.isNotBlank(e.getDssDesc())){
                format = format + "，病害描述："+e.getDssDesc();
            }
            issueList.add(format);
        });
        return issueList.stream().collect(Collectors.joining("；"));
    }

    @Override
    public void refreshNmFinspResult(NmFinsp nmFinsp) {
        List<NmFinspResult> nmFinspResult = finspResultService.createNmFinspResult(nmFinsp);
        List<NmFinspRecord> nmFinspRecords = finspRecordService.selectRecordByFinspId(nmFinsp.getFinspId());
        finspRecordService.showView(nmFinspRecords,nmFinsp);
        if (CollectionUtil.isEmpty(nmFinspResult) && CollectionUtil.isEmpty(nmFinspRecords)) {
            if (CollectionUtil.isNotEmpty(nmFinspResult)){
                //清空结论
                finspResultService.updateBatchById(nmFinspResult);
            }
            return;
        }
        // 根据部件进行分组
        Map<String, List<NmFinspRecord>> partMap = nmFinspRecords.stream().filter(e->StrUtil.isNotBlank(e.getStructPartId()))
                .collect(Collectors.groupingBy(NmFinspRecord::getStructPartId));
        for (NmFinspResult item : nmFinspResult) {
            if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())){
                continue;
            }
            // partId会存在多个
            List<String> partIds = StrUtil.split(item.getPartId(), ",");
            List<NmFinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dssList)) {
                //经常检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
                List<NmFinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(containsDssList)) {
                    item.setIssueDesc(getIssueDescByFm(containsDssList));
                    // 移除已经匹配的病害
                    nmFinspRecords.removeAll(dssList);
                }
            }
        }
        // 获取其他项结论
        NmFinspResult otherResult = nmFinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
        if (otherResult != null && CollectionUtil.isNotEmpty(nmFinspRecords)) {
            otherResult.setIssueDesc(getIssueDescByFm(nmFinspRecords));
        }
        finspResultService.updateBatchById(nmFinspResult);
    }

    private String getIssueDescByFm(List<NmFinspRecord> dssRecords) {
        if (CollectionUtil.isEmpty(dssRecords)) {
            return null;
        }
        List<String> issueList = Lists.newArrayList();
        dssRecords.forEach(e -> {
            String format = StrUtil.format(ISSUE_DESC_FORMAT,e.getStakeCn(), e.getStructPartName(),e.getDssTypeName(), e.getDssNum());
            if (StrUtil.isNotBlank(e.getDssDesc())){
                format = format + "，病害描述："+e.getDssDesc();
            }
            issueList.add(format);
        });
        return issueList.stream().collect(Collectors.joining("；"));
    }

    @Override
    public void validateNmFinsp(NmFinsp nmFinsp) {
        H_CValidator.validator2Exception(nmFinsp,new Class[]{StructGroup.class});
        // 根据结构物ID,设置结构物名称
        BaseStructDto structInfo = getStructInfo(nmFinsp.getFacilityCat(), nmFinsp.getStructId());
        nmFinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());

        BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmFinsp.getRouteCode());
        if (routeLogic != null) {
            nmFinsp.setRouteName(routeLogic.getRouteName());
        }
    }

    @Override
    public void validateNmDinsp(NmDinsp nmDinsp) {
        H_CValidator.validator2Exception(nmDinsp,new Class[]{StructGroup.class});
        // 根据结构物ID,设置结构物名称
        BaseStructDto structInfo = getStructInfo(nmDinsp.getFacilityCat(), nmDinsp.getStructId());
        nmDinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());

        BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmDinsp.getRouteCode());
        if (routeLogic != null) {
            nmDinsp.setRouteName(routeLogic.getRouteName());
        }
    }
}
