package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 结构物排查记录表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinspRecord对象", description="结构物排查记录表")
public class PcFinspRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("RECORD_ID")
    private String recordId;

    @NotBlank(message = "排查查单ID不能为空")
    @ApiModelProperty(value = "排查表ID")
    @TableField("FINSP_ID")
    private String finspId;

    @NotBlank(message = "病害类型ID不能为空")
    @ApiModelProperty(value = "病害类型ID")
    @TableField("DSSTYPE_ID")
    private String dsstypeId;


    @NotBlank(message = "养护处治不能为空")
    @ApiModelProperty(value = "养护处治类型(1:小修处治,2:专项处治,3:应急处治,4:无需处治)")
    @TableField("DEAL_TYPE")
    private String dealType;


    @ApiModelProperty(value = "处治状态(0：未处治，1：已处治)")
    @TableField("DEAL_STATUS")
    private String dealStatus = "0";

    @TableField("X")
    private Double x;

    @TableField("Y")
    private Double y;


    @ApiModelProperty(value = "病害描述")
    @TableField("DSS_DESC")
    private String dssDesc;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableLogic
    @TableField(value = "DEL_FLAG",fill = FieldFill.INSERT)
    private Integer delFlag;

    @TableField(exist = false)
    private List<PcFinspImage> fileIds;

    @TableField(exist = false)
    private String imageHost;

    /**
     * 病害类型名称
     */
    @TableField(exist = false)
    private String dsstypeName;

    /**
     * 分组ID
     */
    @TableField(exist = false)
    private String groupId;

    /**
     * 分组名称
     */
    @TableField(exist = false)
    private String groupName;

    /**
     * 部件名称
     */
    @TableField(exist = false)
    private String partTypeName;

    /**
     * 部件ID
     */
    @TableField(exist = false)
    private String partType;

    /**
     * 处治类型名称
     */
    @TableField(exist = false)
    private String dealTypeName;


    //========================= 处治列表使用属性===============================
    /**
     * 结构物名称
     */
    @TableField(exist = false)
    private String structName;

    /**
     * 检查单号
     */
    @TableField(exist = false)
    private String finspCode;
}
