<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinsp">
        <id column="FINSP_ID" property="finspId" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="INSP_ORG_NAME" property="inspOrgName" />
        <result column="MNT_TYPE" property="mntType" />
        <result column="SITUATION_DESCRIPTION" property="situationDescription" />
        <result column="DSS_NUM" property="dssNum" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="STATUS" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FINSP_ID, PROCESSINSTID, STRUCT_ID, STRUCT_NAME, FACILITY_CAT, INSP_DATE, MNT_ORG_ID, INSP_PERSON, INSP_ORG_NAME, MNT_TYPE, SITUATION_DESCRIPTION, DSS_NUM, DEAL_TYPE, CREATE_USER_ID, CREATE_TIME, UPDATE_TIME, REMARK, STATUS
    </sql>
    <select id="listByCycle" resultType="com.hualu.app.module.mems.finsp.entity.PcFinsp">
        select * from memsdb.PC_FINSP where STRUCT_ID = #{structId} and del_flag=0
          <if test="mntType != null">
              and mnt_type = #{mntType}
          </if>
        order by INSP_DATE desc
    </select>
    <select id="listFinspWord" resultType="com.hualu.app.module.mems.finsp.dto.word.PcFinspWordDto">
        SELECT
            a.FINSP_ID,
            decode(a.mnt_type,1,'定检单位','日常养护单位') as mnt_type,
            a.STRUCT_NAME,
            a.INSP_DATE,
            a.INSP_PERSON,
            a.INSP_ORG_NAME,
            a.FINSP_CODE,
            o.ORG_FULLNAME as org_name,
            b.struct_level as slope_level,
            DECODE(l.line_sname, null, null,l.LINE_sNAME ||'('|| l.LINE_CODE||')') as line_name
        FROM
            MEMSDB.PC_FINSP a
                JOIN MEMSDB.pc_project_scope b ON a.STRUCT_ID = b.STRUCT_ID
                JOIN gdgs.FW_RIGHT_ORG o ON a.mnt_org_id = o.org_code
                LEFT JOIN gdgs.BASE_LINE l on b.line_code = l.line_code and l.is_enable=1 and l.IS_DELETEd = 0 and is_new_GGW = 1
        where 1=1 and a.del_flag=0 and b.del_flag=0 and a.mnt_type in (1,2)
          <if test="finspIds != null and finspIds.size > 0">
              and a.FINSP_ID in
              <foreach collection="finspIds" item="id" index="index" open="(" close=")" separator=",">
                  #{id}
              </foreach>
          </if>

          <if test="prjId != null">
              and b.prj_id = #{prjId}
          </if>
    </select>
    <select id="exportData" resultType="com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto">
        SELECT
            B.LINE_CODE,
            B.STRUCT_NAME,
            B.DESIGN_UNIT,
            B.STRUCT_LEVEL,
            B.STRUCT_TYPE,
            B.STRUCT_STAKE,
            B.PART_TYPE,
            B.FUNCTION_TYPE,
            C.LINE_SNAME as LINE_NAME,
            D.PRJ_NAME,
            A.FINSP_ID,
            A.INSP_DATE,
            A.INSP_PERSON,
            A.INSP_ORG_NAME,
            A.SITUATION_DESCRIPTION,
            A.FINSP_CODE,
            A.ATTR1,
            A.ATTR2,
            A.ATTR3,
            A.ATTR4,
            A.ATTR5,
            A.ATTR6,
            A.ATTR7,
            A.ATTR8,
            A.ATTR9,
            A.ATTR10,
            A.ATTR11,
            A.ATTR12,
            A.ATTR13,
            A.ATTR14,
            A.ATTR15,
            A.ATTR16,
            A.ATTR17,
            A.ATTR18,
            A.ATTR19,
            A.ATTR20,
            A.ATTR21,
            A.DEAL_TYPE
        FROM
            MEMSDB.PC_FINSP A	JOIN	MEMSDB.PC_PROJECT_SCOPE B
            ON 	A.STRUCT_ID = B.STRUCT_ID and A.del_flag=0 and B.del_flag=0 and A.mnt_type in (1,2)
            <if test="prjId != null">
                AND B.PRJ_ID = #{prjId}
            </if>

            <if test="finspIds != null and finspIds.size > 0">
                and A.FINSP_ID IN
                <foreach collection="finspIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            JOIN MEMSDB.PC_PROJECT D on B.PRJ_ID = D.PRJ_ID and D.del_flag = 0
            LEFT JOIN	GDGS.BASE_LINE C	ON 	B.LINE_CODE = C.LINE_CODE AND	C.IS_ENABLE = 1
    </select>
    <select id="listFinspIdByPrjId" resultType="java.lang.String">
        select a.FINSP_ID from memsdb.PC_FINSP a join memsdb.PC_PROJECT_SCOPE b on a.STRUCT_ID = b.STRUCT_ID and a.del_flag=0 and b.del_flag=0 and b.PRJ_ID = #{prjId}
    </select>
    <select id="listDssNumByStructIds" resultType="com.hualu.app.module.mems.finsp.entity.PcFinsp">
        select struct_id,sum(dss_num) dss_num,sum(a.REVIEW_FLAG) REVIEW_FLAG from pc_finsp a where a.struct_id in
        <foreach collection="structIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and a.del_flag = 0
        group by struct_id
    </select>
</mapper>
