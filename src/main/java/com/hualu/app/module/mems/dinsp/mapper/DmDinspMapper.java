package com.hualu.app.module.mems.dinsp.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.dinsp.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface DmDinspMapper extends BaseMapper<DmDinsp> {

    @Update("update MEMSDB.DM_DINSP set status=#{status} where processinstid = #{processInstId}")
    void updateStatus(@Param("processInstId") long processInstId, @Param("status") int status);

    @Update("UPDATE memsdb.dm_dinsp d SET d.DSS_NUM = ( select count(1) from memsdb.dm_dinsp_record b where b.dinsp_id = d.dinsp_id )" +
            "                      where d.dinsp_id = #{dinspId}")
    void updateDssNum(String dinspId);

    DmCountResult queryGroupCount(@Param("orgId") String orgId,
                                  @Param("type") String type);

    DmCountResult queryFinspGroupCount(@Param("orgId") String orgId,
                                       @Param("type") String type);

    IPage<InspectionLedgerDTO> listInspectionLedgerBills(Page<?> page,
                                                         @Param("orgId")  String orgId,
                                                         @Param("status") String status);

    IPage<InspectionLedgerDTO> listInspectionRCLedgerBills(Page<?> page,
                                                           @Param("orgId")  String orgId,
                                                           @Param("status") String status);

    Map<String, Object> countDailyDefects(@Param("orgId") String orgId);

    Map<String, Object> calculateDefectsByCategory(@Param("orgId")  String orgId);

    IPage<FacilityInspectionDTO> indexDefectRecordsForFastQuery(Page<?> page,
                                                                @Param("orgId") String orgId);

    IPage<FacilityInspectionDTO> indexDefectRecordsRcForFastQuery(Page<?> page,
                                                                  @Param("orgId") String orgId);

    List<String> getImageFileId(@Param("dssId") String dssId);

    List<InspectionResult> InspectionResult(@Param("orgId") String orgId,
                                            @Param("dssId") String dssId);

    IPage<InspectionRecord> InspectionRecord(Page<?> page,
                                             @Param("orgId") String orgId);
}
