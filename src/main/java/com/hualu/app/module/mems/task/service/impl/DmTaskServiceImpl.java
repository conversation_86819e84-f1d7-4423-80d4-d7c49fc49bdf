package com.hualu.app.module.mems.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.mpc.dto.MpcContractDto;
import com.hualu.app.module.mems.mpc.entity.MpcContract;
import com.hualu.app.module.mems.mpc.service.MpcContractService;
import com.hualu.app.module.mems.task.dto.DmTaskDto;
import com.hualu.app.module.mems.task.dto.DmTaskOverDto;
import com.hualu.app.module.mems.task.dto.DmTaskPartnameDto;
import com.hualu.app.module.mems.task.dto.DmTaskPrjOrgDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.entity.DmTask;
import com.hualu.app.module.mems.task.mapper.DmTaskMapper;
import com.hualu.app.module.mems.task.service.DmTaskAccptDetailService;
import com.hualu.app.module.mems.task.service.DmTaskDetailService;
import com.hualu.app.module.mems.task.service.DmTaskService;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.hualu.app.utils.H_BasedataHepler;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Service
public class DmTaskServiceImpl extends ServiceImpl<DmTaskMapper, DmTask> implements DmTaskService, IWorkItemEventHandler, OrderInfoHandler {

    private String RWDCODE = "RWD-{}-{}";

    @Autowired
    DmTaskDetailService detailService;

    @Autowired
    DmTaskAccptDetailService accptDetailService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    MpcContractService mpcService;

    @Autowired
    DssInfoService dssInfoService;

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delDmTask(String mtaskId) {
        DmTask task = getById(mtaskId);
        if (task == null) return;
        detailService.delByMtaskId(mtaskId);
        H_WorkFlowHelper.deleteProcessInstance(task.getProcessinstid());
        removeById(mtaskId);
    }

    @Override
    public void finshDmTask(String mtaskId,boolean isCreateAccptDetail,LocalDate yesterday) {
        List<DmTaskDetailDto> detailDtos = detailService.selectTaskDetailsByTaskId(mtaskId);
        if (CollectionUtil.isNotEmpty(detailDtos)){
            Set<String> dssIds = detailDtos.stream().filter(e -> StrUtil.isNotBlank(e.getDssId())).map(DmTaskDetailDto::getDssId).collect(Collectors.toSet());
            dssInfoService.updateRepairStatus(Lists.newArrayList(dssIds),1);
            if (isCreateAccptDetail){
                //自动生成工程记录明细
                accptDetailService.createDmTaskAccptDetails(mtaskId,detailDtos);
            }
        }
        updateDmTaskReceiveUser(mtaskId, CustomRequestContextHolder.getUserCode(),2,yesterday);
    }

    @Override
    public int countDmTaskCode(String orgId, String taskCode, String taskId) {
        LambdaQueryWrapper<DmTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTask::getMntOrgId,orgId);
        queryWrapper.eq(DmTask::getMtaskCode,taskCode);
        if (StrUtil.isNotBlank(taskId)){
            queryWrapper.ne(DmTask::getMtaskId,taskId);
        }
        return count(queryWrapper);
    }

    @Override
    public void saveOrUpdateTask(DmTask task) {
        task.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
        setPrjInfo(task);
        setMtaskCode(task);
        //设置任务单名称
        MpcContract mpc = mpcService.getById(task.getContrId());
        task.setMtaskName(mpc.getContrName());
        saveOrUpdate(task);
    }

    /**
     * 设置任务单编码信息
     * @param dto
     */
    private void setMtaskCode(DmTask dto){
        int i = countDmTaskCode(CustomRequestContextHolder.getOrgIdString(), dto.getMtaskCode(), dto.getMtaskId());
        if (i != 0){
            DmTaskDto nextCode = getNextCode();
            dto.setMtaskCode(nextCode.getMtaskCode());
        }
    }

    /**
     * 设置项目信息编码及名称
     * @param dto
     */
    private void setPrjInfo(DmTask dto){
        //如果项目公司是空，默认选全部 的项目公司
        if (StrUtil.isBlank(dto.getPrjOrgCode())){
            List<DmTaskPrjOrgDto> orgDtos = selectPrjOrgs();
            if (CollectionUtil.isEmpty(orgDtos)){
                return;
            }
            List<String> orgCodes = orgDtos.stream().map(DmTaskPrjOrgDto::getOrgCode).collect(Collectors.toList());
            List<String> orgNames = orgDtos.stream().map(DmTaskPrjOrgDto::getOrgFullname).collect(Collectors.toList());
            dto.setPrjOrgCode(StrUtil.join(",",orgCodes));
            dto.setPrjOrgName(StrUtil.join(",",orgNames));
        }
    }

    @Override
    public List<DmTaskPrjOrgDto> selectPrjOrgs() {
        return baseMapper.selectPrjOrgs(CustomRequestContextHolder.getUserCode());
    }

    @Override
    public DmTaskDto getNextCode() {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        String orgEn = CustomRequestContextHolder.get("ORG_EN") == null ? "" : CustomRequestContextHolder.get("ORG_EN").toString();
        //任务单前缀
        String codePreLike = StrUtil.format(RWDCODE,orgEn,DateUtil.format(new Date(),"yyyyMM"));
        String taskCode = StrUtil.format(RWDCODE,orgEn,DateUtil.format(new Date(),"yyyyMMdd"));
        synchronized (codePreLike){
            Integer num = baseMapper.selectNextCode(codePreLike,orgId);
            String code = num == null ? "1" : ++num + "";
            String suffix = StrUtil.padPre(code, 3, "0");
            taskCode+="-"+suffix;
        }
        DmTaskDto dto = new DmTaskDto();
        dto.setMtaskCode(taskCode);
        List<MpcContractDto> mpcContractDtos = mpcService.selectMpcContracts(orgId);
        if (CollectionUtil.isNotEmpty(mpcContractDtos)){
            MpcContractDto contractDto = mpcContractDtos.get(0);
            dto.setMntnOrgName(contractDto.getImplOrgName());
            dto.setMntOrgId(contractDto.getMntOrgId());
            dto.setMntOrgName(contractDto.getMntOrgName());
            dto.setPrjOrgName(contractDto.getMntOrgName());
            dto.setPrjOrgCode(contractDto.getMntOrgId());
            dto.setContrId(contractDto.getContrId());
            dto.setContrName(contractDto.getContrName());
        }
        dto.setAskedStartDate(LocalDate.now());
        return dto;
    }

    @Override
    public DmTask selectDmTaskByProcessInstId(Long proInstId) {
        LambdaQueryWrapper<DmTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTask::getProcessinstid,proInstId);
        return getOne(queryWrapper);
    }

    @Override
    public void updateDmTaskSendUser(String dmTaskId, String userCode, int status,LocalDate date) {
        LambdaUpdateWrapper<DmTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DmTask::getMtaskId,dmTaskId);
        updateWrapper.set(DmTask::getSendUser,userCode)
                .set(DmTask::getSendTime, date)
                .set(DmTask::getStatus,status);
        update(updateWrapper);
    }

    @Override
    public void updateDmTaskReceiveUser(String dmTaskId, String userCode, int status,LocalDate date) {
        LambdaUpdateWrapper<DmTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DmTask::getMtaskId,dmTaskId);
        updateWrapper.set(DmTask::getReceiveUser,userCode)
                .set(DmTask::getReceiveTime, date)
                .set(DmTask::getStatus,status);
        update(updateWrapper);
    }

    @Override
    public void updateDmtaskStatus(String dmTaskId, int status) {
        LambdaUpdateWrapper<DmTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DmTask::getMtaskId,dmTaskId);
        updateWrapper.set(DmTask::getStatus,status);
        update(updateWrapper);
    }

    @Override
    public IPage selectPage(Page page, QueryWrapper queryWrapper) {
        IPage iPage = baseMapper.selectPage(page, queryWrapper);
        List<DmTaskDto> dmTaskDtos = Lists.newArrayList();
        List<DmTask> records = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(records)){
            //根据流程ID，查询办理人
            Set<Long> proInstIds = records.stream().filter(e -> e.getProcessinstid() != null).map(DmTask::getProcessinstid).collect(Collectors.toSet());
            Map<Long, String> partnameMap = getPartnameMap(proInstIds);

            //根据任务单ID,查询验收情况
            List<String> taskIds = records.stream().map(DmTask::getMtaskId).collect(Collectors.toList());
            Map<String, DmTaskOverDto> overMap = getOverMap(taskIds);

            records.forEach(item->{
                DmTaskDto dmTaskDto = BeanUtil.toBean(item, DmTaskDto.class);
                dmTaskDto.setPartiname(partnameMap.get(item.getProcessinstid()));
                DmTaskOverDto overDto = overMap.get(item.getMtaskId());
                if (overDto != null){
                    dmTaskDto.setAccptNum(overDto.getAccptNum());
                    dmTaskDto.setTotalNum(overDto.getTotalNum());
                }
                dmTaskDto.setEmergyDegreeName(dicService.getDicName("EMERGY_DEGREE",item.getEmergyDegree()));
                dmTaskDto.setStatusName(H_BasedataHepler.taskRepairStatusMap.get(item.getStatus()));
                dmTaskDtos.add(dmTaskDto);
            });
        }
        iPage.setRecords(dmTaskDtos);
        return iPage;
    }

    /**
     * 获取办理人信息
     * @param proInstIds
     * @return
     */
    private Map<Long,String> getPartnameMap(Set<Long> proInstIds){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("processinstid",proInstIds);
        List<DmTaskPartnameDto> nameDtos = baseMapper.selectPartnames(queryWrapper);
        Map<Long, String> partMap = nameDtos.stream().collect(Collectors.toMap(DmTaskPartnameDto::getProcessinstid, DmTaskPartnameDto::getPartiname));
        if (CollectionUtil.isEmpty(partMap)){
            partMap = Maps.newHashMap();
        }
        return partMap;
    }

    /**
     * 获取任务单超时状态
     * @param taskIds
     * @return
     */
    private Map<String, DmTaskOverDto> getOverMap(List<String> taskIds){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("t.mtask_id",taskIds);
        List<DmTaskOverDto> dmTaskOverDtos = baseMapper.selectOvers(queryWrapper);
        if (CollectionUtil.isEmpty(dmTaskOverDtos)){
            return Maps.newHashMap();
        }
        Map<String, DmTaskOverDto> overDtoMap = dmTaskOverDtos.stream().collect(Collectors.toMap(DmTaskOverDto::getMtaskId, Function.identity()));
        return overDtoMap;
    }

    @Override
    public Map<String, String> initAction() {
        return null;
    }

    @Override
    public String getProcessDefName() {
        return "gdcg.emdc.mems.dm.DmTaskWorkflow";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savedoEvent(long processInstId, boolean isEnd, String nextAction, String actId) {
        DmTask dmTask = selectDmTaskByProcessInstId(processInstId);
        if (dmTask == null){
            throw new BaseException(processInstId+"未匹配任务单");
        }
        LocalDate askedStartDate = dmTask.getAskedStartDate();
        LocalDate yesterday  = askedStartDate.plusDays(-1);
        //办结
        if (isEnd){
            finshDmTask(dmTask.getMtaskId(),false,yesterday);
            return;
        }
        if (nextAction.equals("直接派单") || nextAction.equals("派单")) {
            updateDmTaskSendUser(dmTask.getMtaskId(), CustomRequestContextHolder.getUserCode(), 1,yesterday);
        } else if (nextAction.equals("退回")) {
            updateDmtaskStatus(dmTask.getMtaskId(), -1);
        } else if (nextAction.contains("审核")) {
            updateDmtaskStatus(dmTask.getMtaskId(), 6);
        }
    }

    @Override
    public List<OrderInfo> queryOrderInfo(long processInstId) {
        LambdaQueryWrapper<DmTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTask::getProcessinstid,processInstId);
        DmTask dmTask = getOne(queryWrapper);
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderType("RWD");
        orderInfo.setOrderCode(dmTask.getMtaskCode());
        orderInfo.setOrderId(dmTask.getMtaskId());
        orderInfo.setStatus(dmTask.getStatus());
        orderInfo.setOrgCode(dmTask.getMntOrgId());
        return Arrays.asList(orderInfo);
    }
}
