package com.hualu.app.module.mems.nminsp.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value="NmDinspSituationShow对象", description="新版日常巡查情况展示类")
public class NmDinspSituationShow implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "二级单位编码")
    private String secondOrgCode;

    @ApiModelProperty(value = "二级单位名称")
    private String secondOrgName;

    @ApiModelProperty(value = "单位名称")
    private String orgName;

    @ApiModelProperty(value = "单位编码")
    private String orgCode;

    @ApiModelProperty(value = "路线名称")
    private String routeName;

    @ApiModelProperty(value = "路线编码")
    private String routeCode;

    // January fields
    @ApiModelProperty(value = "一月路面完成数量")
    private String janLm;
    @ApiModelProperty(value = "一月边坡完成数量")
    private String janBp;
    @ApiModelProperty(value = "一月隧道完成数量")
    private String janSd;
    @ApiModelProperty(value = "一月一般桥梁完成数量")
    private String janGenQl;
    @ApiModelProperty(value = "一月交安完成数量")
    private String janJa;
    @ApiModelProperty(value = "一月特殊桥梁夜间巡查数量")
    private String janSpeQlNight;
    @ApiModelProperty(value = "一月一级桥梁夜间巡查数量")
    private String janOneQlNight;
    @ApiModelProperty(value = "一月二级桥梁夜间巡查数量")
    private String janTwoQlNight;
    @ApiModelProperty(value = "一月路面完成情况")
    private String janLmResult;
    @ApiModelProperty(value = "一月边坡完成情况")
    private String janBpResult;
    @ApiModelProperty(value = "一月隧道完成情况")
    private String janSdResult;
    @ApiModelProperty(value = "一月一般桥梁完成情况")
    private String janGenQlResult;
    @ApiModelProperty(value = "一月交安完成情况")
    private String janJaResult;
    @ApiModelProperty(value = "一月特殊桥梁夜间巡查情况")
    private String janSpeQlNightResult;
    @ApiModelProperty(value = "一月一级桥梁夜间巡查情况")
    private String janOneQlNightResult;
    @ApiModelProperty(value = "一月二级桥梁夜间巡查情况")
    private String janTwoQlNightResult;

    // February fields
    @ApiModelProperty(value = "二月路面完成数量")
    private String febLm;
    @ApiModelProperty(value = "二月边坡完成数量")
    private String febBp;
    @ApiModelProperty(value = "二月隧道完成数量")
    private String febSd;
    @ApiModelProperty(value = "二月一般桥梁完成数量")
    private String febGenQl;
    @ApiModelProperty(value = "二月交安完成数量")
    private String febJa;
    @ApiModelProperty(value = "二月特殊桥梁夜间巡查数量")
    private String febSpeQlNight;
    @ApiModelProperty(value = "二月一级桥梁夜间巡查数量")
    private String febOneQlNight;
    @ApiModelProperty(value = "二月二级桥梁夜间巡查数量")
    private String febTwoQlNight;
    @ApiModelProperty(value = "二月路面完成情况")
    private String febLmResult;
    @ApiModelProperty(value = "二月边坡完成情况")
    private String febBpResult;
    @ApiModelProperty(value = "二月隧道完成情况")
    private String febSdResult;
    @ApiModelProperty(value = "二月一般桥梁完成情况")
    private String febGenQlResult;
    @ApiModelProperty(value = "二月交安完成情况")
    private String febJaResult;
    @ApiModelProperty(value = "二月特殊桥梁夜间巡查情况")
    private String febSpeQlNightResult;
    @ApiModelProperty(value = "二月一级桥梁夜间巡查情况")
    private String febOneQlNightResult;
    @ApiModelProperty(value = "二月二级桥梁夜间巡查情况")
    private String febTwoQlNightResult;

    // March fields
    @ApiModelProperty(value = "三月路面完成数量")
    private String marLm;
    @ApiModelProperty(value = "三月边坡完成数量")
    private String marBp;
    @ApiModelProperty(value = "三月隧道完成数量")
    private String marSd;
    @ApiModelProperty(value = "三月一般桥梁完成数量")
    private String marGenQl;
    @ApiModelProperty(value = "三月交安完成数量")
    private String marJa;
    @ApiModelProperty(value = "三月特殊桥梁夜间巡查数量")
    private String marSpeQlNight;
    @ApiModelProperty(value = "三月一级桥梁夜间巡查数量")
    private String marOneQlNight;
    @ApiModelProperty(value = "三月二级桥梁夜间巡查数量")
    private String marTwoQlNight;
    @ApiModelProperty(value = "三月路面完成情况")
    private String marLmResult;
    @ApiModelProperty(value = "三月边坡完成情况")
    private String marBpResult;
    @ApiModelProperty(value = "三月隧道完成情况")
    private String marSdResult;
    @ApiModelProperty(value = "三月一般桥梁完成情况")
    private String marGenQlResult;
    @ApiModelProperty(value = "三月交安完成情况")
    private String marJaResult;
    @ApiModelProperty(value = "三月特殊桥梁夜间巡查情况")
    private String marSpeQlNightResult;
    @ApiModelProperty(value = "三月一级桥梁夜间巡查情况")
    private String marOneQlNightResult;
    @ApiModelProperty(value = "三月二级桥梁夜间巡查情况")
    private String marTwoQlNightResult;

    // April fields
    @ApiModelProperty(value = "四月路面完成数量")
    private String aprLm;
    @ApiModelProperty(value = "四月边坡完成数量")
    private String aprBp;
    @ApiModelProperty(value = "四月隧道完成数量")
    private String aprSd;
    @ApiModelProperty(value = "四月一般桥梁完成数量")
    private String aprGenQl;
    @ApiModelProperty(value = "四月交安完成数量")
    private String aprJa;
    @ApiModelProperty(value = "四月特殊桥梁夜间巡查数量")
    private String aprSpeQlNight;
    @ApiModelProperty(value = "四月一级桥梁夜间巡查数量")
    private String aprOneQlNight;
    @ApiModelProperty(value = "四月二级桥梁夜间巡查数量")
    private String aprTwoQlNight;
    @ApiModelProperty(value = "四月路面完成情况")
    private String aprLmResult;
    @ApiModelProperty(value = "四月边坡完成情况")
    private String aprBpResult;
    @ApiModelProperty(value = "四月隧道完成情况")
    private String aprSdResult;
    @ApiModelProperty(value = "四月一般桥梁完成情况")
    private String aprGenQlResult;
    @ApiModelProperty(value = "四月交安完成情况")
    private String aprJaResult;
    @ApiModelProperty(value = "四月特殊桥梁夜间巡查情况")
    private String aprSpeQlNightResult;
    @ApiModelProperty(value = "四月一级桥梁夜间巡查情况")
    private String aprOneQlNightResult;
    @ApiModelProperty(value = "四月二级桥梁夜间巡查情况")
    private String aprTwoQlNightResult;

    // May fields
    @ApiModelProperty(value = "五月路面完成数量")
    private String mayLm;
    @ApiModelProperty(value = "五月边坡完成数量")
    private String mayBp;
    @ApiModelProperty(value = "五月隧道完成数量")
    private String maySd;
    @ApiModelProperty(value = "五月一般桥梁完成数量")
    private String mayGenQl;
    @ApiModelProperty(value = "五月交安完成数量")
    private String mayJa;
    @ApiModelProperty(value = "五月特殊桥梁夜间巡查数量")
    private String maySpeQlNight;
    @ApiModelProperty(value = "五月一级桥梁夜间巡查数量")
    private String mayOneQlNight;
    @ApiModelProperty(value = "五月二级桥梁夜间巡查数量")
    private String mayTwoQlNight;
    @ApiModelProperty(value = "五月路面完成情况")
    private String mayLmResult;
    @ApiModelProperty(value = "五月边坡完成情况")
    private String mayBpResult;
    @ApiModelProperty(value = "五月隧道完成情况")
    private String maySdResult;
    @ApiModelProperty(value = "五月一般桥梁完成情况")
    private String mayGenQlResult;
    @ApiModelProperty(value = "五月交安完成情况")
    private String mayJaResult;
    @ApiModelProperty(value = "五月特殊桥梁夜间巡查情况")
    private String maySpeQlNightResult;
    @ApiModelProperty(value = "五月一级桥梁夜间巡查情况")
    private String mayOneQlNightResult;
    @ApiModelProperty(value = "五月二级桥梁夜间巡查情况")
    private String mayTwoQlNightResult;

    // June fields
    @ApiModelProperty(value = "六月路面完成数量")
    private String junLm;
    @ApiModelProperty(value = "六月边坡完成数量")
    private String junBp;
    @ApiModelProperty(value = "六月隧道完成数量")
    private String junSd;
    @ApiModelProperty(value = "六月一般桥梁完成数量")
    private String junGenQl;
    @ApiModelProperty(value = "六月交安完成数量")
    private String junJa;
    @ApiModelProperty(value = "六月特殊桥梁夜间巡查数量")
    private String junSpeQlNight;
    @ApiModelProperty(value = "六月一级桥梁夜间巡查数量")
    private String junOneQlNight;
    @ApiModelProperty(value = "六月二级桥梁夜间巡查数量")
    private String junTwoQlNight;
    @ApiModelProperty(value = "六月路面完成情况")
    private String junLmResult;
    @ApiModelProperty(value = "六月边坡完成情况")
    private String junBpResult;
    @ApiModelProperty(value = "六月隧道完成情况")
    private String junSdResult;
    @ApiModelProperty(value = "六月一般桥梁完成情况")
    private String junGenQlResult;
    @ApiModelProperty(value = "六月交安完成情况")
    private String junJaResult;
    @ApiModelProperty(value = "六月特殊桥梁夜间巡查情况")
    private String junSpeQlNightResult;
    @ApiModelProperty(value = "六月一级桥梁夜间巡查情况")
    private String junOneQlNightResult;
    @ApiModelProperty(value = "六月二级桥梁夜间巡查情况")
    private String junTwoQlNightResult;

    // July fields
    @ApiModelProperty(value = "七月路面完成数量")
    private String julLm;
    @ApiModelProperty(value = "七月边坡完成数量")
    private String julBp;
    @ApiModelProperty(value = "七月隧道完成数量")
    private String julSd;
    @ApiModelProperty(value = "七月一般桥梁完成数量")
    private String julGenQl;
    @ApiModelProperty(value = "七月交安完成数量")
    private String julJa;
    @ApiModelProperty(value = "七月特殊桥梁夜间巡查数量")
    private String julSpeQlNight;
    @ApiModelProperty(value = "七月一级桥梁夜间巡查数量")
    private String julOneQlNight;
    @ApiModelProperty(value = "七月二级桥梁夜间巡查数量")
    private String julTwoQlNight;
    @ApiModelProperty(value = "七月路面完成情况")
    private String julLmResult;
    @ApiModelProperty(value = "七月边坡完成情况")
    private String julBpResult;
    @ApiModelProperty(value = "七月隧道完成情况")
    private String julSdResult;
    @ApiModelProperty(value = "七月一般桥梁完成情况")
    private String julGenQlResult;
    @ApiModelProperty(value = "七月交安完成情况")
    private String julJaResult;
    @ApiModelProperty(value = "七月特殊桥梁夜间巡查情况")
    private String julSpeQlNightResult;
    @ApiModelProperty(value = "七月一级桥梁夜间巡查情况")
    private String julOneQlNightResult;
    @ApiModelProperty(value = "七月二级桥梁夜间巡查情况")
    private String julTwoQlNightResult;

    // August fields
    @ApiModelProperty(value = "八月路面完成数量")
    private String augLm;
    @ApiModelProperty(value = "八月边坡完成数量")
    private String augBp;
    @ApiModelProperty(value = "八月隧道完成数量")
    private String augSd;
    @ApiModelProperty(value = "八月一般桥梁完成数量")
    private String augGenQl;
    @ApiModelProperty(value = "八月交安完成数量")
    private String augJa;
    @ApiModelProperty(value = "八月特殊桥梁夜间巡查数量")
    private String augSpeQlNight;
    @ApiModelProperty(value = "八月一级桥梁夜间巡查数量")
    private String augOneQlNight;
    @ApiModelProperty(value = "八月二级桥梁夜间巡查数量")
    private String augTwoQlNight;
    @ApiModelProperty(value = "八月路面完成情况")
    private String augLmResult;
    @ApiModelProperty(value = "八月边坡完成情况")
    private String augBpResult;
    @ApiModelProperty(value = "八月隧道完成情况")
    private String augSdResult;
    @ApiModelProperty(value = "八月一般桥梁完成情况")
    private String augGenQlResult;
    @ApiModelProperty(value = "八月交安完成情况")
    private String augJaResult;
    @ApiModelProperty(value = "八月特殊桥梁夜间巡查情况")
    private String augSpeQlNightResult;
    @ApiModelProperty(value = "八月一级桥梁夜间巡查情况")
    private String augOneQlNightResult;
    @ApiModelProperty(value = "八月二级桥梁夜间巡查情况")
    private String augTwoQlNightResult;

    // September fields
    @ApiModelProperty(value = "九月路面完成数量")
    private String sepLm;
    @ApiModelProperty(value = "九月边坡完成数量")
    private String sepBp;
    @ApiModelProperty(value = "九月隧道完成数量")
    private String sepSd;
    @ApiModelProperty(value = "九月一般桥梁完成数量")
    private String sepGenQl;
    @ApiModelProperty(value = "九月交安完成数量")
    private String sepJa;
    @ApiModelProperty(value = "九月特殊桥梁夜间巡查数量")
    private String sepSpeQlNight;
    @ApiModelProperty(value = "九月一级桥梁夜间巡查数量")
    private String sepOneQlNight;
    @ApiModelProperty(value = "九月二级桥梁夜间巡查数量")
    private String sepTwoQlNight;
    @ApiModelProperty(value = "九月路面完成情况")
    private String sepLmResult;
    @ApiModelProperty(value = "九月边坡完成情况")
    private String sepBpResult;
    @ApiModelProperty(value = "九月隧道完成情况")
    private String sepSdResult;
    @ApiModelProperty(value = "九月一般桥梁完成情况")
    private String sepGenQlResult;
    @ApiModelProperty(value = "九月交安完成情况")
    private String sepJaResult;
    @ApiModelProperty(value = "九月特殊桥梁夜间巡查情况")
    private String sepSpeQlNightResult;
    @ApiModelProperty(value = "九月一级桥梁夜间巡查情况")
    private String sepOneQlNightResult;
    @ApiModelProperty(value = "九月二级桥梁夜间巡查情况")
    private String sepTwoQlNightResult;

    // October fields
    @ApiModelProperty(value = "十月路面完成数量")
    private String octLm;
    @ApiModelProperty(value = "十月边坡完成数量")
    private String octBp;
    @ApiModelProperty(value = "十月隧道完成数量")
    private String octSd;
    @ApiModelProperty(value = "十月一般桥梁完成数量")
    private String octGenQl;
    @ApiModelProperty(value = "十月交安完成数量")
    private String octJa;
    @ApiModelProperty(value = "十月特殊桥梁夜间巡查数量")
    private String octSpeQlNight;
    @ApiModelProperty(value = "十月一级桥梁夜间巡查数量")
    private String octOneQlNight;
    @ApiModelProperty(value = "十月二级桥梁夜间巡查数量")
    private String octTwoQlNight;
    @ApiModelProperty(value = "十月路面完成情况")
    private String octLmResult;
    @ApiModelProperty(value = "十月边坡完成情况")
    private String octBpResult;
    @ApiModelProperty(value = "十月隧道完成情况")
    private String octSdResult;
    @ApiModelProperty(value = "十月一般桥梁完成情况")
    private String octGenQlResult;
    @ApiModelProperty(value = "十月交安完成情况")
    private String octJaResult;
    @ApiModelProperty(value = "十月特殊桥梁夜间巡查情况")
    private String octSpeQlNightResult;
    @ApiModelProperty(value = "十月一级桥梁夜间巡查情况")
    private String octOneQlNightResult;
    @ApiModelProperty(value = "十月二级桥梁夜间巡查情况")
    private String octTwoQlNightResult;

    // November fields
    @ApiModelProperty(value = "十一月路面完成数量")
    private String novLm;
    @ApiModelProperty(value = "十一月边坡完成数量")
    private String novBp;
    @ApiModelProperty(value = "十一月隧道完成数量")
    private String novSd;
    @ApiModelProperty(value = "十一月一般桥梁完成数量")
    private String novGenQl;
    @ApiModelProperty(value = "十一月交安完成数量")
    private String novJa;
    @ApiModelProperty(value = "十一月特殊桥梁夜间巡查数量")
    private String novSpeQlNight;
    @ApiModelProperty(value = "十一月一级桥梁夜间巡查数量")
    private String novOneQlNight;
    @ApiModelProperty(value = "十一月二级桥梁夜间巡查数量")
    private String novTwoQlNight;
    @ApiModelProperty(value = "十一月路面完成情况")
    private String novLmResult;
    @ApiModelProperty(value = "十一月边坡完成情况")
    private String novBpResult;
    @ApiModelProperty(value = "十一月隧道完成情况")
    private String novSdResult;
    @ApiModelProperty(value = "十一月一般桥梁完成情况")
    private String novGenQlResult;
    @ApiModelProperty(value = "十一月交安完成情况")
    private String novJaResult;
    @ApiModelProperty(value = "十一月特殊桥梁夜间巡查情况")
    private String novSpeQlNightResult;
    @ApiModelProperty(value = "十一月一级桥梁夜间巡查情况")
    private String novOneQlNightResult;
    @ApiModelProperty(value = "十一月二级桥梁夜间巡查情况")
    private String novTwoQlNightResult;

    // December fields
    @ApiModelProperty(value = "十二月路面完成数量")
    private String decLm;
    @ApiModelProperty(value = "十二月边坡完成数量")
    private String decBp;
    @ApiModelProperty(value = "十二月隧道完成数量")
    private String decSd;
    @ApiModelProperty(value = "十二月一般桥梁完成数量")
    private String decGenQl;
    @ApiModelProperty(value = "十二月交安完成数量")
    private String decJa;
    @ApiModelProperty(value = "十二月特殊桥梁夜间巡查数量")
    private String decSpeQlNight;
    @ApiModelProperty(value = "十二月一级桥梁夜间巡查数量")
    private String decOneQlNight;
    @ApiModelProperty(value = "十二月二级桥梁夜间巡查数量")
    private String decTwoQlNight;
    @ApiModelProperty(value = "十二月路面完成情况")
    private String decLmResult;
    @ApiModelProperty(value = "十二月边坡完成情况")
    private String decBpResult;
    @ApiModelProperty(value = "十二月隧道完成情况")
    private String decSdResult;
    @ApiModelProperty(value = "十二月一般桥梁完成情况")
    private String decGenQlResult;
    @ApiModelProperty(value = "十二月交安完成情况")
    private String decJaResult;
    @ApiModelProperty(value = "十二月特殊桥梁夜间巡查情况")
    private String decSpeQlNightResult;
    @ApiModelProperty(value = "十二月一级桥梁夜间巡查情况")
    private String decOneQlNightResult;
    @ApiModelProperty(value = "十二月二级桥梁夜间巡查情况")
    private String decTwoQlNightResult;
}
