package com.hualu.app.module.mems.finsp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.finsp.entity.DmFinspResultSet;
import com.hualu.app.module.mems.finsp.mapper.DmFinspResultSetMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspResultSetService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class DmFinspResultSetServiceImpl extends ServiceImpl<DmFinspResultSetMapper, DmFinspResultSet> implements DmFinspResultSetService {

    @Override
    public boolean isUpdateResultSet(String mngOrgId, String userId) {
        LambdaQueryWrapper<DmFinspResultSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspResultSet::getOrgCode,mngOrgId);
        queryWrapper.eq(DmFinspResultSet::getUserCode,userId);
        return false;
    }
}
