package com.hualu.app.module.mems.task.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.mems.task.dto.DmTaskOverDto;
import com.hualu.app.module.mems.task.dto.DmTaskPartnameDto;
import com.hualu.app.module.mems.task.dto.DmTaskPrjOrgDto;
import com.hualu.app.module.mems.task.entity.DmTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 任务单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskMapper extends BaseMapper<DmTask> {

    List<DmTaskPartnameDto> selectPartnames(@Param(Constants.WRAPPER)QueryWrapper queryWrapper);

    List<DmTaskOverDto> selectOvers(@Param(Constants.WRAPPER)QueryWrapper queryWrapper);

    Integer selectNextCode(@Param("codePrefix") String codePrefix,@Param("orgId") String orgId);

    /**
     * 查询任务单项目公司
     * @param userCode
     * @return
     */
    List<DmTaskPrjOrgDto> selectPrjOrgs(@Param("userCode") String userCode);
}
