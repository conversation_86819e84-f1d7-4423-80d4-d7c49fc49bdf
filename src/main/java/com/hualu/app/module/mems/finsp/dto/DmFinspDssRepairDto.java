package com.hualu.app.module.mems.finsp.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 经常检查病害维修情况
 */
@Accessors(chain = true)
@Data
public class DmFinspDssRepairDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构编码
     */
    private String orgCode;
    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 结构物名称
     */
    private String structName;

    /**
     * 结构物类型
     */
    private String facilityCatName;

    /**
     * 病害描述
     */
    private String dssDesc;

    /**
     * 发现日期
     */
    private Date inspDate;

    /**
     * 经常检查单号
     */
    private String finspCode;

    /**
     * 任务单单号
     */
    private String mtaskCode;

    /**
     * 验收单单号
     */
    private String mtaskAccptCode;
}
