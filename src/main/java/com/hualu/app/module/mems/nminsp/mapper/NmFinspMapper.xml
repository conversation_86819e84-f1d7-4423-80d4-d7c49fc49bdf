<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmFinspMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmFinsp">
        <id column="FINSP_ID" property="finspId" />
        <result column="FINSP_CODE" property="finspCode" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNT_ORG_NM" property="mntOrgNm" />
        <result column="SEARCH_DEPT" property="searchDept" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="INSP_FREQUENCY" property="inspFrequency" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_NAME" property="lineName" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="STAKE_NAME" property="stakeName" />
        <result column="WEATHER" property="weather" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="STATUS" property="status" />
        <result column="DSS_NUM" property="dssNum" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="XC_TYPE" property="xcType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FINSP_ID, FINSP_CODE, PROCESSINSTID, FACILITY_CAT, STRUCT_ID, STRUCT_NAME, MNT_ORG_ID, MNT_ORG_NM, SEARCH_DEPT, INSP_PERSON, INSP_DATE, INSP_TIME, INSP_FREQUENCY, LINE_CODE, LINE_NAME, LINE_DIRECT, ROUTE_NAME, STAKE_NAME, WEATHER, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, REMARK, STATUS, DSS_NUM, ROUTE_CODE, DEL_FLAG, XC_TYPE
    </sql>
    <select id="lastNmFinsp" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinsp">
        select * from (select * from nm_finsp where facility_cat = #{facilityCat} and mnt_org_id = #{orgIdString} and del_flag=0 order by CREATE_TIME desc ) where rownum &lt;= 1
    </select>
    <select id="querySituationShow" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow">
        with orgtable as (select o.ORG_CODE,o.ORG_NAME,o.PARENT_ID from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1 and o.IS_DELETED = 0
        start with o.ID = #{orgCode} connect by prior o.ID = o.PARENT_ID),
        ORG_DATA as (select oo.org_code as SECOND_ORG_CODE,oo.ORG_NAME as SECOND_ORG_NAME,o.ORG_NAME, o.ORG_CODE, l.ROUTE_NAME,
        ROUTE_CODE from orgtable o inner join GDGS.BASE_ROUTE_LOGIC l on l.OPRT_ORG_CODE = o.ORG_CODE
        inner join gdgs.FW_RIGHT_ORG oo on o.PARENT_ID = oo.ORG_CODE
        where l.IS_ENABLE = 1 and oo.IS_DELETED = 0 and oo.IS_ENABLE = 1
        group by oo.ORG_CODE,oo.org_name, o.ORG_NAME, o.ORG_CODE, l.ROUTE_CODE,l.ROUTE_NAME),
        MONTHS_DATA AS(select month,EXTRACT(DAY FROM LAST_DAY(TO_DATE(month,'yyyy-MM'))) as days from
        (SELECT TO_CHAR(ADD_MONTHS(TO_DATE(#{year} || '-01-01', 'YYYY-MM-DD'), LEVEL - 1), 'yyyy-MM') AS month FROM dual CONNECT BY LEVEL &lt;= 12)),
        INSPECT_BRIDGE AS (select * from (select distinct bk.BYUSSI_ID,bk.PRJ_ID,bk.LAST_GRADE,
        row_number() over (partition by bk.BYUSSI_ID order by tpj.PRJYEAR desc, bk.EVAL_TIME desc) rn from BCTCMSDB.T_BRDG_KEEPSCORE bk
        inner join PTCMSDB.TCC_INSP_PRJ tpj on bk.PRJ_ID = tpj.PRJ_ID
        inner join MEMSDB.SP_PROJECT_SCOPE sp on tpj.SP_PRJ_ID = sp.SP_PRJ_ID and sp.STRUCT_ID = bk.BYUSSI_ID where
        bk.BUSSI_TYPE = 1 and bk.LAST_GRADE in (1, 2, 3, 4, 5)
        and tpj.PRJ_PRO_TYPE = 'QH' and tpj.PRJ_TYPE = 'DJ' and tpj.PRJYEAR between 2025-2 and 2025
        and exists(select 1 from BCTCMSDB.T_BRDG_BRDGRECOG br where br.BRDGRECOG_ID = sp.STRUCT_ID and br.VALID_FLAG = 1)) m where m.rn = 1),
        BASE_TC as (select rm.MAIN_BRDGRECOG_ID,br.ROUTE_CODE, b.*, max(nvl(b.LAST_GRADE, -1)) over (partition by rm.MAIN_BRDGRECOG_ID ) maxGrade,
        row_number() over (partition by rm.MAIN_BRDGRECOG_ID order by RECOGMANAGE_ID) rn2 from
        BCTCMSDB.T_BRDG_BRDGRECOG br inner join BCTCMSDB.T_BRDG_RECOGMANAGE rm on br.BRDGRECOG_ID = rm.BRDGRECOG_ID
        left join INSPECT_BRIDGE b on b.BYUSSI_ID = br.BRDGRECOG_ID where rm.VALID_FLAG = 1 and br.VALID_FLAG = 1),
        SPE_QL_INFO as (select y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.ACROSS_FTR_TYPE = '2' and br.VALID_FLAG = 1
        and exists( select 1 from BCTCMSDB.T_BRDG_BRDGTYPE bt inner join BCTCMSDB.T_BRDG_TOPTYPE t on t.BRDG_TYPE = bt.BRDGTYPE_ID
        where bt.P_BRDGTYPE_ID in ('1','2', '3','4') and t.BRDGRECOG_ID = br.BRDGRECOG_ID and t.VALID_FLAG = 1  and bt.VALID_FLAG = 1)
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1)
        union all
        select  y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.VALID_FLAG = 1
        and exists( select 1 from BCTCMSDB.T_BRDG_BRDGTYPE bt inner join BCTCMSDB.T_BRDG_TOPTYPE t on t.BRDG_TYPE = bt.BRDGTYPE_ID
        where bt.P_BRDGTYPE_ID in ('5','6') and t.BRDGRECOG_ID = br.BRDGRECOG_ID and t.VALID_FLAG = 1  and bt.VALID_FLAG = 1)
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1)),
        SPE_QL_DATA as (select a.ROUTE_CODE,a.comp_date,a.struct_id,b.LAST_GRADE grade from SPE_QL_INFO a left join BASE_TC b on a.struct_id = b.MAIN_BRDGRECOG_ID and b.rn2 = 1),
        ONE_QL_DATA as (select y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.VALID_FLAG = 1
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1
        ) and br.MAINTAIN_GRADE = 1 and not exists(select 1 from SPE_QL_DATA c where br.BRDGRECOG_ID = c.struct_id)),
        TWO_QL_DATA as (select y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.VALID_FLAG = 1
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1
        ) and br.MAINTAIN_GRADE = 2 and not exists(select 1 from SPE_QL_DATA c where br.BRDGRECOG_ID = c.struct_id)),
        HD_DATA as (select ROUTE_CODE,CLVRTRECOG_ID struct_id,MAINTENANCE_LEVEL grade,y.month comp_date from BCTCMSDB.T_CLVRT_CLVRTRECOG a
        inner join MONTHS_DATA y on y.month &gt;= substr(COMPLETED_YEARS, 1, 7) where VALID_FLAG = 1),
        HD_TOTAL as (select ROUTE_CODE,comp_date,sum(case when grade = 1 then 1 else 0 end) as one_total,
        sum(case when grade = 2 then 1 else 0 end) as two_total,sum(case when grade = 3 then 1 else 0 end) as three_total from HD_DATA group by ROUTE_CODE,comp_date),
        SD_TOTAL as (select y.month comp_date,ROUTE_CODE,sum(case when TUNNEL_MAINTAIN_GRADE = 1 then 1 else 0 end) as one_total,
        sum(case when TUNNEL_MAINTAIN_GRADE = 2 then 1 else 0 end) as two_total
        from MTMSDB.MTMS_TUNNEL_BASIC t inner join MONTHS_DATA y on y.month &gt;= to_char(BUILT_DATE,'yyyy-MM') where IS_DELETED = 0 and IS_ENABLE = 1 group by y.month, ROUTE_CODE),
        BP_TOTAL as (select y.month comp_date,ROUTE_CODE,count(*) as total
        from HSMSDB.HSMS_SLOPE_INFO x inner join MONTHS_DATA y on y.month &gt;= to_char(COMPLETION_TIME_DATE,'yyyy-MM') where IS_DELETED = 0 and COMPLETION_TIME_DATE is not null group by y.month, ROUTE_CODE ),
        FINSP_ONE_SD_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'SD'
        and exists(select 1 from MTMSDB.MTMS_TUNNEL_BASIC b where a.STRUCT_ID = b.TUNNEL_ID and a.ROUTE_CODE = b.ROUTE_CODE and TUNNEL_MAINTAIN_GRADE = 1)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_TWO_SD_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'SD'
        and exists(select 1 from MTMSDB.MTMS_TUNNEL_BASIC b where a.STRUCT_ID = b.TUNNEL_ID and a.ROUTE_CODE = b.ROUTE_CODE and TUNNEL_MAINTAIN_GRADE = 2)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_ONE_QL_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL'
        and exists(select 1 from ONE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_TWO_QL_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL'
        and exists(select 1 from TWO_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_SPE_ONE_QL_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL'
        and exists(select 1 from SPE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade = 1)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_SPE_TWO_QL_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL'
        and exists(select 1 from SPE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade = 2)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_SPE_THREE_QL_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL'
        and exists(select 1 from SPE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade = 3)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_SPE_FOUR_QL_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL'
        and exists(select 1 from SPE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade not in (1,2,3))
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_ONE_HD_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'HD'
        and exists(select 1 from HD_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade = 1)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_TWO_HD_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'HD'
        and exists(select 1 from HD_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade = 2)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_THREE_HD_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'HD'
        and exists(select 1 from HD_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE and grade = 3)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_BP_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = '2025' AND FACILITY_CAT = 'BP'
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        FINSP_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,
        sum(case when FACILITY_CAT = 'LM' THEN 1 else 0 end) as lm_total,
        sum(case when FACILITY_CAT = 'JA' THEN 1 else 0 end) as ja_total
        from (SELECT trunc(INSP_DATE) AS year_month,FACILITY_CAT,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_finsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year}
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,FACILITY_CAT)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        BASE_DATA as (select second_org_code,  second_org_name, org_name, org_code, route_name,route_code,
        TO_CHAR(TO_DATE(b.month, 'YYYY-MM'),'MON','NLS_DATE_LANGUAGE=AMERICAN') as month_abbr,
        NVL(dm_lm,0) as lm,case when NVL(dm_lm,0) = 0 then '0' else '1' end as lm_result,
        NVL(dm_ja,0) as ja,case when NVL(dm_ja,0) = 0 then '0' else '1' end as ja_result,
        case when NVL(bp,0) = 0 then '-' else (case when dm_bp &gt; bp then bp else dm_bp end) || ' | ' || bp end as bp,
        case when NVL(bp,0) = 0 then '1' else (case when dm_bp &lt; bp then '0' else '1' end) end as bp_result,
        case when NVL(one_sd,0) = 0 then '-' else (case when dm_one_sd &gt; one_sd then one_sd else dm_one_sd end) || ' | ' || one_sd  end as one_sd,
        case when NVL(one_sd,0) = 0 then '1' else (case when dm_one_sd &lt; one_sd then '0' else '1' end) end as one_sd_result,
        case when NVL(two_sd,0) = 0 then '-' else (case when MAX(dm_two_sd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt; two_sd then two_sd else MAX(dm_two_sd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) end) || ' | ' || two_sd end as two_sd,
        case when NVL(two_sd,0) = 0 then '1' else (case when MAX(dm_two_sd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt;= two_sd then '1' else '0' end)end as two_sd_result,
        case when NVL(one_ql,0) = 0 then '-' else (case when dm_one_ql &gt; one_ql then one_ql else dm_one_ql end) || ' | ' || one_ql  end as one_ql,
        case when NVL(one_ql,0) = 0 then '1' else (case when dm_one_ql &lt; one_ql then '0' else '1' end) end as one_ql_result,
        case when NVL(two_ql,0) = 0 then '-' else (case when MAX(dm_two_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt; two_ql then two_ql else MAX(dm_two_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) end) || ' | ' || two_ql end as two_ql,
        case when NVL(two_ql,0) = 0 then '1' else (case when MAX(dm_two_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt;= two_ql then '1' else '0' end)end as two_ql_result,
        case when NVL(four_spe_ql,0) = 0 then '-' else (case when MAX(dm_four_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/4)) &gt; four_spe_ql then four_spe_ql else MAX(dm_four_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/4)) end) || ' | ' || four_spe_ql end as four_spe_ql,
        case when NVL(four_spe_ql,0) = 0 then '1' else (case when MAX(dm_four_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/4)) &gt;= four_spe_ql then '1' else '0' end)end as four_spe_ql_result,
        case when NVL(two_spe_ql,0) = 0 then '-' else (case when MAX(dm_two_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt; two_spe_ql then two_spe_ql else MAX(dm_two_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) end) || ' | ' || two_spe_ql end as two_spe_ql,
        case when NVL(two_spe_ql,0) = 0 then '1' else (case when MAX(dm_two_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt;= two_spe_ql then '1' else '0' end)end as two_spe_ql_result,
        case when NVL(three_spe_ql,0) = 0 then '-' else (case when MAX(dm_three_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/3)) &gt; three_spe_ql then three_spe_ql else MAX(dm_three_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/3)) end) || ' | ' || three_spe_ql end as three_spe_ql,
        case when NVL(three_spe_ql,0) = 0 then '1' else (case when MAX(dm_three_spe_ql) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/3)) &gt;= three_spe_ql then '1' else '0' end)end as three_spe_ql_result,
        case when NVL(one_spe_ql,0) = 0 then '-' else (case when dm_one_spe_ql &gt; one_spe_ql then one_spe_ql else dm_one_spe_ql end) || ' | ' || one_spe_ql  end as one_spe_ql,
        case when NVL(one_spe_ql,0) = 0 then '1' else (case when dm_one_spe_ql &lt; one_spe_ql then '0' else '1' end) end as one_spe_ql_result,
        case when NVL(two_hd,0) = 0 then '-' else (case when MAX(dm_two_hd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt; two_hd then two_hd else MAX(dm_two_hd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) end) || ' | ' || two_hd end as two_hd,
        case when NVL(two_hd,0) = 0 then '1' else (case when MAX(dm_two_hd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/2)) &gt;= two_hd then '1' else '0' end)end as two_hd_result,
        case when NVL(three_hd,0) = 0 then '-' else (case when MAX(dm_three_hd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/3)) &gt; three_hd then three_hd else MAX(dm_three_hd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/3)) end) || ' | ' || three_hd end as three_hd,
        case when NVL(three_hd,0) = 0 then '1' else (case when MAX(dm_three_hd) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(b.month, 'YYYY-MM')) - 1)/3)) &gt;= three_hd then '1' else '0' end)end as three_hd_result,
        case when NVL(one_hd,0) = 0 then '-' else (case when dm_one_hd &gt; one_hd then one_hd else dm_one_hd end) || ' | ' || one_hd  end as one_hd,
        case when NVL(one_hd,0) = 0 then '1' else (case when dm_one_hd &lt; one_hd then '0' else '1' end) end as one_hd_result
        from (select #{year} as year,a.second_org_code,a.second_org_name,a.org_name,a.org_code,a.route_name,to_char(WM_CONCAT(distinct a.route_code)) route_code,b.month,
        SUM(NVL(c.total,0)) as bp,SUM(NVL(d.one_total,0)) as one_sd,SUM(NVL(d.two_total,0)) as two_sd,SUM(NVL(r.total,0)) as one_ql,SUM(NVL(g.total,0)) as two_ql,
        SUM(NVL(h.spe_ql_one,0)) as one_spe_ql,SUM(NVL(h.spe_ql_two,0)) as two_spe_ql,SUM(NVL(h.spe_ql_three,0)) as three_spe_ql,SUM(NVL(h.spe_ql_four,0)) as four_spe_ql,SUM(NVL(i.one_total,0)) as one_hd,SUM(NVL(i.two_total,0)) as two_hd,SUM(NVL(i.three_total,0)) as three_hd,
        SUM(NVL(j.total,0)) as dm_one_sd,SUM(NVL(k.total,0)) as dm_two_sd,SUM(NVL(l.total,0)) as dm_one_ql,SUM(NVL(m.total,0)) as dm_two_ql,
        SUM(NVL(n.total,0)) as dm_one_spe_ql,SUM(NVL(o.total,0)) as dm_two_spe_ql,SUM(NVL(p.total,0)) as dm_three_spe_ql,SUM(NVL(q.total,0)) as dm_four_spe_ql,
        SUM(NVL(qq.total,0)) as dm_bp,SUM(NVL(s.ja_total,0)) as dm_ja,SUM(NVL(s.lm_total,0)) as dm_lm,
        SUM(NVL(t.total,0)) as dm_one_hd,SUM(NVL(u.total,0)) as dm_two_hd,SUM(NVL(v.total,0)) as dm_three_hd
        from ORG_DATA a inner join MONTHS_DATA b on 1=1 left join BP_TOTAL c on a.ROUTE_CODE = c.ROUTE_CODE and c.comp_date = b.month
        left join SD_TOTAL d on a.ROUTE_CODE = d.ROUTE_CODE and d.comp_date = b.month
        left join (select comp_date, ROUTE_CODE, count(*) as total from ONE_QL_DATA group by comp_date, ROUTE_CODE) r on a.ROUTE_CODE = r.ROUTE_CODE and r.comp_date = b.month
        left join (select comp_date, ROUTE_CODE, count(*) as total from TWO_QL_DATA group by comp_date, ROUTE_CODE) g on a.ROUTE_CODE = g.ROUTE_CODE and g.comp_date = b.month
        left join (select comp_date, ROUTE_CODE,sum(case when grade = 1 then 1 else 0 end) as spe_ql_one,sum(case when grade = 2 then 1 else 0 end) as spe_ql_two,
        sum(case when grade = 3 then 1 else 0 end) as spe_ql_three,sum(case when grade = 4 then 1 else 0 end) as spe_ql_four from SPE_QL_DATA group by comp_date, ROUTE_CODE) h on a.ROUTE_CODE = h.ROUTE_CODE and h.comp_date = b.month
        left join HD_TOTAL i on a.ROUTE_CODE = i.ROUTE_CODE and i.comp_date = b.month
        left join FINSP_ONE_SD_DATA j on a.ROUTE_CODE = j.ROUTE_CODE and j.year_month = b.month
        left join FINSP_TWO_SD_DATA k on a.ROUTE_CODE = k.ROUTE_CODE and k.year_month = b.month
        left join FINSP_ONE_QL_DATA l on a.ROUTE_CODE = l.ROUTE_CODE and l.year_month = b.month
        left join FINSP_TWO_QL_DATA m on a.ROUTE_CODE = m.ROUTE_CODE and m.year_month = b.month
        left join FINSP_SPE_ONE_QL_DATA n on a.ROUTE_CODE = n.ROUTE_CODE and n.year_month = b.month
        left join FINSP_SPE_TWO_QL_DATA o on a.ROUTE_CODE = o.ROUTE_CODE and o.year_month = b.month
        left join FINSP_SPE_THREE_QL_DATA p on a.ROUTE_CODE = p.ROUTE_CODE and p.year_month = b.month
        left join FINSP_SPE_FOUR_QL_DATA q on a.ROUTE_CODE = q.ROUTE_CODE and q.year_month = b.month
        left join FINSP_BP_DATA qq on a.ROUTE_CODE = qq.ROUTE_CODE and qq.year_month = b.month
        left join FINSP_DATA s on a.ROUTE_CODE = s.ROUTE_CODE and s.year_month = b.month
        left join FINSP_ONE_HD_DATA t on a.ROUTE_CODE = t.ROUTE_CODE and t.year_month = b.month
        left join FINSP_TWO_HD_DATA u on a.ROUTE_CODE = u.ROUTE_CODE and u.year_month = b.month
        left join FINSP_THREE_HD_DATA v on a.ROUTE_CODE = v.ROUTE_CODE and v.year_month = b.month
        group by a.second_org_code,a.second_org_name,a.org_name,a.org_code,a.route_name,b.month) t1
        inner join MONTHS_DATA b on t1.month = b.month) select * from (select * from base_data
        pivot ( max(lm) lm,max(bp) bp,max(ja) ja,max(one_hd) one_hd,max(two_hd) two_hd,max(three_hd) three_hd,max(one_sd) one_sd,max(two_sd) two_sd,
                max(one_hd_result) one_hd_result,max(two_hd_result) two_hd_result,max(three_hd_result) three_hd_result,
        max(lm_result) lm_result,max(bp_result) bp_result,max(ja_result) ja_result,max(one_sd_result) one_sd_result,max(two_sd_result) two_sd_result,
        max(one_ql) one_ql,max(two_ql) two_ql, max(one_ql_result) one_ql_result, max(two_ql_result) two_ql_result,
        max(one_spe_ql) one_spe_ql,max(one_spe_ql_result) one_spe_ql_result,max(two_spe_ql) two_spe_ql, max(two_spe_ql_result) two_spe_ql_result,
        max(three_spe_ql) three_spe_ql,max(three_spe_ql_result) three_spe_ql_result,max(four_spe_ql) four_spe_ql, max(four_spe_ql_result) four_spe_ql_result
        for month_abbr in ('JAN' jan,'FEB' feb,'MAR' mar,'APR' apr,'MAY' may,'JUN' jun,'JUL' jul,'AUG' aug,'SEP' sep,'OCT' oct,'NOV' nov,'DEC' dec))
        order by second_org_name,org_name,route_name)
    </select>
    <select id="getDssInfoFImageExport" resultType="com.hualu.app.module.mems.nminsp.entity.RoadInspectionRecord">
        select P.LINE_CODE as lineCode, A.STAKE as stake, P.INSP_DATE as inspDate, N.DSS_TYPE_NAME as pavementType, p.INSP_PERSON as inspPerson,
               c.FILE_ENTITY_PATH as fileEntityPath,p.FINSP_CODE as dinspCode,P.STRUCT_ID as structId
        from MEMSDB.NM_FINSP_RECORD a
                 JOIN MEMSDB.NM_FINSP P ON A.FINSP_ID = P.FINSP_ID
                 JOIN MEMSDB.DSS_TYPE_NEW N ON A.DSS_TYPE = N.DSS_TYPE
                 join MEMSDB.DSS_IMAGE b on a.DSS_ID = b.DSS_ID
                 join gdgs.BASE_FILE_ENTITY c on b.FILE_ID = c.FILE_ENTITY_ID
        WHERE a.FACILITY_CAT  = #{facilityCat}
        and a.FINSP_ID in
        <foreach collection="dinspIds" item="dinspId" index="index" open="(" close=")" separator=",">
            #{dinspId}
        </foreach>
        AND c.FILE_ENTITY_PATH IS NOT NULL
    </select>
    <!--病害台账-->
    <select id="DailyInspectionLedger" resultType="com.hualu.app.module.mems.nminsp.entity.RouteInspection">
        with ORG AS (
            select *
            from GDGS.FW_RIGHT_ORG o
            where o.IS_ENABLE = 1
            start with o.ORG_CODE = #{orgCode}
            connect by prior O.ID = O.PARENT_ID
        )
        select O.ORG_NAME as routeName,
               LR.LINE_CODE as lineCode,
               DECODE(A.LINE_DIRECT, '1', '上行', '2', '下行', '4', '匝道')                             as lineDirect,
               DECODE(a.FACILITY_CAT, 'LM', '路面', 'QL', '桥梁', 'SD', '隧道', 'HD', '涵洞', 'BP', '边坡','JA','交安') as facilityCategory,
               a.STAKE as stake,
               N.DSS_TYPE_NAME as dssTypeName,
               A.DSS_POSITION || A.DSS_DESC as dssDetail,
               P.FINSP_CODE as dinspCode,
               p.INSP_DATE as inspDate,
               a.DSS_ID as dssId,
               TO_CHAR(DBMS_LOB.SUBSTR(
                WM_CONCAT(c.FILE_ENTITY_PATH),
                4000,  -- 最大长度
                1      -- 起始位置
                )) as fileEntityPath,
               TO_CHAR(DBMS_LOB.SUBSTR(
                WM_CONCAT(c.FILE_ENTITY_ID),
                4000,  -- 最大长度
                1      -- 起始位置
                )) AS fileEntityId,
               P.STRUCT_ID as structId
        from MEMSDB.NM_FINSP_RECORD a
                 LEFT JOIN MEMSDB.NM_FINSP P ON A.FINSP_ID = P.FINSP_ID
                 JOIN MEMSDB.DSS_TYPE_NEW N ON A.DSS_TYPE = N.DSS_TYPE
                 join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
                 JOIN ORG O ON LR.OPRT_ORG_CODE=O.ORG_CODE
                 LEFT join MEMSDB.DSS_IMAGE b on a.DSS_ID = b.DSS_ID
                 LEFT join gdgs.BASE_FILE_ENTITY c on b.FILE_ID = c.FILE_ENTITY_ID
        WHERE  b.DEL_FLAG = 0
        <if test="dinspCode != null and dinspCode != ''">
            AND P.FINSP_CODE like #{dinspCode}
        </if>
        <if test="facilityCat != null and facilityCat != ''">
            AND P.FACILITY_CAT=#{facilityCat}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND LR.LINE_CODE=#{lineCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND to_char(P.INSP_DATE,'YYYY-MM') <![CDATA[=]]>#{startDate}
        </if>
        <if test="startStake != null and startDate != ''">
            AND a.STAKE <![CDATA[>=]]>#{startStake}
        </if>
        <if test="endStake != null and endStake != ''">
            AND a.STAKE <![CDATA[<=]]>#{endStake}
        </if>
        <if test="dssTypeName != null and dssTypeName != ''">
            AND (
            <foreach item="type" collection="dssTypeName.split(',')" separator=" OR ">
                N.dss_type_name LIKE '%' || #{type} || '%'
            </foreach>
            )
        </if>
        <if test="facilityCategory != null and facilityCategory != ''">
            AND (
            <foreach item="category" collection="facilityCategory.split(',')" separator=" OR ">
                P.FACILITY_CAT = #{category}
            </foreach>
            )
        </if>
        <if test="lineDirect != null and lineDirect != ''">
            AND (
            <foreach item="direction" collection="lineDirect.split(',')" separator=" OR ">
                A.LINE_DIRECT = #{direction}
            </foreach>
            )
        </if>
        <if test="routeName != null and routeName != ''">
            AND (
            <foreach item="name" collection="routeName.split(',')" separator=" OR ">
                LR.ROUTE_NAME = #{name}
            </foreach>
            )
        </if>
        GROUP BY
        O.ORG_NAME,
        LR.LINE_CODE,
        A.LINE_DIRECT,
        a.FACILITY_CAT,
        a.STAKE,
        N.DSS_TYPE_NAME,
        A.DSS_POSITION,
        A.DSS_DESC,
        P.FINSP_CODE,
        p.INSP_DATE,
        a.DSS_ID,
        p.STRUCT_ID
        ORDER BY routeName,lineCode,lineDirect,facilityCategory,p.INSP_DATE,a.STAKE
    </select>

    <select id="findDssInfoId" resultType="com.hualu.app.module.mems.nminsp.entity.MtaskEntity">
        SELECT DISTINCT D.MTASK_CODE AS mtaskCode, A.MTASK_ACCPT_CODE AS mtaskAccptCode,T.DSS_ID AS dssId,O.REPAIR_DATE AS repairDate
        FROM MEMSDB.DM_TASK D
        left JOIN MEMSDB.DM_TASK_DETAIL T ON D.MTASK_ID=T.MTASK_ID
        left JOIN MEMSDB.DM_TASK_ACCPT_DETAIL TS ON TS.mtask_dtl_id=T.mtask_dtl_id
        left JOIN MEMSDB.DM_TASK_ACCPT A ON TS.mtask_accpt_id = A.MTASK_ACCPT_ID
        left JOIN MEMSDB.DSS_INFO O ON T.DSS_ID=O.DSS_ID
        WHERE O.DSS_ID IN
        <foreach collection="dssIds" item="dssId" index="index" open="(" close=")" separator=",">
            #{dssId}
        </foreach>
        <if test="mtaskCode != null and mtaskCode != ''">
            AND D.MTASK_CODE = #{mtaskCode}
        </if>
        <if test="mtaskAccptCode != null and mtaskAccptCode != ''">
            AND A.MTASK_ACCPT_CODE = #{mtaskAccptCode}
        </if>
    </select>

    <select id="selectPageExtra" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinsp">
        select d.*,
               (select c.LOGIC_CNTR_STAKE_NUM
                from BCTCMSDB.T_CLVRT_CLVRTRECOG c
                where c.CLVRTRECOG_ID =
                      d.STRUCT_ID
                  and c.VALID_FLAG = 1
                  and ROWNUM = 1) c_stake
        from MEMSDB.NM_FINSP d
        ${ew.customSqlSegment}
    </select>

    <!--边坡经常检查台账-->
    <select id="findBpDailyRecord" resultType="com.hualu.app.module.mems.nminsp.entity.SlopeInspectionRecord">
        with ORG AS (select *
             from GDGS.FW_RIGHT_ORG o
             where o.IS_ENABLE = 1
             start with o.ORG_CODE = #{orgCode}
             connect by prior O.ID = O.PARENT_ID)
        select DISTINCT LR.ROUTE_NAME                                                                        as routeName,
               LR.LINE_CODE                                                                         as lineCode,
               OS.SLOPE_NAME as slopeName,
               OS.SLOPE_LENGTH as slopeLength,
               OS.SLOPE_LEVEL as slopeLevel,
               DECODE(P.DSS_NUM, 0, '无', P.DSS_NUM)                                                 AS DssNums,
               decode(HHS.SLOPE_TC_GRADE, '0', '未评定', '1', '稳定', '2', '基本稳定', '3', '欠稳定', '4', '不稳定') as slopeTcGrade,
               P.FINSP_CODE                                                                         as dinspCode,
               '每月一次' as inspFrequency,
               p.INSP_DATE                                                                          as inspDate,
               a.MNTN_ADVICE as mntnAdvice,
               P.SEARCH_DEPT as orgFullname
        from MEMSDB.NM_FINSP P
                 LEFT JOIN MEMSDB.NM_FINSP_RECORD a ON A.FINSP_ID = P.FINSP_ID
                 LEFT JOIN HSMSDB.HSMS_SLOPE_INFO OS ON P.STRUCT_ID = OS.SLOPE_ID
                 LEFT JOIN (
                    SELECT
                    SLOPE_ID,SLOPE_TC_GRADE,
                    ROW_NUMBER() OVER (
                    PARTITION BY SLOPE_ID
                    ORDER BY UPDATE_TIME DESC
                    ) AS rn
                    FROM HSMSDB.HSMS_SFEVAL
                    WHERE IS_DELETED = 0
                ) HHS ON OS.SLOPE_ID = HHS.SLOPE_ID AND HHS.rn = 1
                 join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
                 JOIN ORG O ON LR.OPRT_ORG_CODE = O.ORG_CODE
        WHERE P.FACILITY_CAT = 'BP' AND P.DEL_FLAG=0
        <if test="dinspCode != null and dinspCode != ''">
            AND P.FINSP_CODE=#{dinspCode}
        </if>
        <if test="status != null and status != ''">
            AND P.STATUS=#{status}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND LR.LINE_CODE=#{lineCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND to_char(P.INSP_DATE,'YYYY-MM') <![CDATA[=]]>#{startDate}
        </if>
        <if test="hasDssStatus != null and hasDssStatus != ''">
            <choose>
                <when test="hasDssStatus == '0'.toString()">
                    AND P.DSS_NUM=0
                </when>
                <when test="hasDssStatus == '1'.toString()">
                    AND P.DSS_NUM>=1
                </when>
            </choose>
        </if>
        ORDER BY routeName, lineCode, SUBSTR(P.FINSP_CODE, 12, 8),SUBSTR(P.FINSP_CODE, 21, 4) asc
    </select>
    <select id="findHdDailyRecord" resultType="com.hualu.app.module.mems.nminsp.entity.HdInspectionRecord">
        with ORG AS (select *
             from GDGS.FW_RIGHT_ORG o
             where o.IS_ENABLE = 1
             start with o.ORG_CODE =#{orgCode}
             connect by prior O.ID = O.PARENT_ID)
        select DISTINCT LR.ROUTE_NAME                                                                          as routeName,
                        LR.LINE_CODE                                                                           as lineCode,
                        os.CLVRT_NAME                                                                          as clvrtName,
                        os.CNTR_STAKE as cntrStake,
                        DECODE(P.DSS_NUM, 0, '无', P.DSS_NUM)                                                   AS DssNums,
                        p.INSP_DATE                                                                            as inspDate,
                        '每月一次'                                                                                 as inspFrequency,
                        P.FINSP_CODE                                                                           as dinspCode,
                        P.SEARCH_DEPT as searchDept
        from MEMSDB.NM_FINSP P
                 LEFT JOIN MEMSDB.NM_FINSP_RECORD a ON A.FINSP_ID = P.FINSP_ID
                 LEFT JOIN BCTCMSDB.t_clvrt_clvrtrecog OS ON P.STRUCT_ID = OS.CLVRTRECOG_ID
                 join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
                 JOIN ORG O ON LR.OPRT_ORG_CODE = O.ORG_CODE
        WHERE P.FACILITY_CAT = 'HD' AND P.DEL_FLAG=0
        <if test="dinspCode != null and dinspCode != ''">
            AND P.FINSP_CODE=#{dinspCode}
        </if>
        <if test="status != null and status != ''">
            AND P.STATUS=#{status}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND LR.LINE_CODE=#{lineCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND to_char(P.INSP_DATE,'YYYY-MM') <![CDATA[=]]>#{startDate}
        </if>
        <if test="hasDssStatus != null and hasDssStatus != ''">
            <choose>
                <when test="hasDssStatus == '0'.toString()">
                    AND P.DSS_NUM=0
                </when>
                <when test="hasDssStatus == '1'.toString()">
                    AND P.DSS_NUM>=1
                </when>
            </choose>
        </if>
        ORDER BY routeName, lineCode, SUBSTR(P.FINSP_CODE, 12, 8),SUBSTR(P.FINSP_CODE, 21, 4) asc
    </select>
    <select id="findQLDailyRecord" resultType="com.hualu.app.module.mems.nminsp.entity.BridgeInspection">

        with ORGS AS (select o.ID
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ORG_CODE = #{orgCode}
        connect by prior o.ID = o.PARENT_ID),
        ROUTE_CODES AS (
        select distinct l.ROUTE_CODE
        from GDGS.BASE_ROUTE_LOGIC l
        inner join GDGS.ROUTE_REF ref on l.ROUTE_CODE = ref.ROUTE_CODE
        inner join gdgs.PRO_ROUTE pro on pro.ID = ref.PRO_ROUTE_ID
        where pro.IS_UNIT = 1 and
        exists(
        select 1 from ORGS oo where oo.ID = l.OPRT_ORG_CODE
        )),
        INSPECT_BRIDGE AS (
        select *
        from (select distinct bk.BYUSSI_ID,
        nvl(bk.MAUL_EVAL_TIME, to_char(tpj.PRJ_START_DATE, 'yyyy-mm-dd')) MAUL_EVAL_TIME,
        tpj.PRJ_FULLNAME,
        tpj.PRJYEAR,
        bk.LAST_GRADE,
        row_number() over (partition by bk.BYUSSI_ID order by tpj.PRJYEAR desc, bk.EVAL_TIME desc) rn
        from BCTCMSDB.T_BRDG_KEEPSCORE bk
        inner join PTCMSDB.TCC_INSP_PRJ tpj on bk.PRJ_ID = tpj.PRJ_ID
        inner join MEMSDB.SP_PROJECT_SCOPE sp
        on tpj.SP_PRJ_ID = sp.SP_PRJ_ID and sp.STRUCT_ID = bk.BYUSSI_ID
        where bk.BUSSI_TYPE = 1
        and bk.LAST_GRADE in (1, 2, 3, 4, 5)
        and tpj.PRJ_PRO_TYPE = 'QH'
        and tpj.PRJ_TYPE = 'DJ'
        and exists(
        select 1 from BCTCMSDB.T_BRDG_BRDGRECOG br where br.BRDGRECOG_ID = sp.STRUCT_ID and br.VALID_FLAG = 1
        and exists(
        select 1 from ROUTE_CODES rc where rc.ROUTE_CODE =  br.ROUTE_CODE
        )
        )) m
        where m.rn = 1
        )
        SELECT DISTINCT *
        FROM (

        select LR.ROUTE_NAME                                                                                     as routeName,
        c.ROAD_NUM                                                                                      as lineCode,
        gdgs.GETMULTIDICVALUE(t.BRDG_SPAN_KIND, 'BRDG_SPAN_KIND', 'sys')                             as brdgSpanKind,
        c.BRDG_NAME as brdgName,
        C.DESIGN_STAKE as designStake,
        'K' || replace(to_char(C.LOGIC_CNTR_STAKE_NUM,'fm9999999.099'), '.', '+') as logicCntrStake,
        DECODE(P.DSS_NUM, 0, '无', P.DSS_NUM)                                                              AS DssNums,
        P.INSP_DATE                                                                                       as inspDate,
        decode(c.MAINTAIN_GRADE, '1', 'I类桥梁每月一次', '2', 'II类桥梁每月一次', '3', 'III类桥梁每月一次', '4',
        'III类桥梁每月一次')                                                                              as checkNums,
        P.FINSP_CODE                                                                                      as finspCode,
        decode((select ib.LAST_GRADE from INSPECT_BRIDGE ib where ib.BYUSSI_ID = c.BRDGRECOG_ID and ROWNUM = 1)
        ,1,'一类',2,'二类', 3,'三类', 4,'四类', 5, '五类')    as brdgRating,
        O.ORG_FULLNAME                                                                                    as orgFullname,
        TO_NUMBER(REGEXP_SUBSTR(P.FINSP_CODE, '\d{4}$'))                                                  as ORDERS,
        TO_DATE(REGEXP_SUBSTR(P.FINSP_CODE, '\d{8}'), 'YYYYMMDD')                                         as dateOrders
        from MEMSDB.NM_FINSP P
        LEFT JOIN MEMSDB.NM_FINSP_RECORD a ON A.FINSP_ID = P.FINSP_ID
        LEFT JOIN BCTCMSDB.T_BRDG_BRDGRECOG c on P.STRUCT_ID = C.BRDGRECOG_ID
        LEFT JOIN BCTCMSDB.T_BRDG_TECHINDEX t on c.BRDGRECOG_ID = t.BRDGRECOG_ID
        join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
        inner join ROUTE_CODES rcc on rcc.ROUTE_CODE = p.ROUTE_CODE
        inner join gdgs.FW_RIGHT_ORG o on o.ID = lr.OPRT_ORG_CODE
        WHERE P.FACILITY_CAT = 'QL' AND P.DEL_FLAG=0
        <if test="dinspCode != null and dinspCode != ''">
            AND P.FINSP_CODE=#{dinspCode}
        </if>
        <if test="status != null and status != ''">
            AND P.STATUS=#{status}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND LR.LINE_CODE=#{lineCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND TO_CHAR(P.INSP_DATE,'YYYY-MM')<![CDATA[=]]>#{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND P.INSP_DATE <![CDATA[<=]]>TO_DATE(#{endDate},'YYYY-MM')
        </if>
        <if test="hasDssStatus != null and hasDssStatus != ''">
            <choose>
                <when test="hasDssStatus == '0'.toString()">
                    AND P.DSS_NUM=0
                </when>
                <when test="hasDssStatus == '1'.toString()">
                    AND P.DSS_NUM>=1
                </when>
            </choose>
        </if>
        ) ORDER BY dateOrders,ORDERS
    </select>

    <select id="getList" resultMap="BaseResultMap">
        select * from MEMSDB.NM_FINSP t where t.FACILITY_CAT='QL' and t.MNT_ORG_ID!='7bb57184-3962-4202-a472-3d9f201e088d'  and t.INSP_DATE>=to_date('2025-05-01','yyyy-MM-dd') and DEL_FLAG=0
    </select>

    <select id="getQlDetail" resultType="java.util.Map">
        select br.BRDGRECOG_ID,br.BRDG_NAME from BCTCMSDB.T_BRDG_RECOGMANAGE m
                                                     inner join bctcmsdb.T_BRDG_BRDGRECOG br on m.BRDGRECOG_ID=br.BRDGRECOG_ID
        where m.MAIN_BRDGRECOG_ID=#{structId} and br.VALID_FLAG=1
          and m.VALID_FLAG=1
    </select>

    <select id="selectViewRecord" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinspRecord">
        SELECT A.*, B.FINSP_CODE, B.INSP_DATE, B.INSP_TIME, B.INSP_PERSON, B.STRUCT_ID, B.STRUCT_NAME, B.ROUTE_CODE, B.ROUTE_NAME, B.STAKE_NAME, B.LINE_CODE, B.LINE_NAME, B.LINE_DIRECT, B.MNT_ORG_ID, B.MNT_ORG_NM, B.SEARCH_DEPT, B.INSP_FREQUENCY, B.WEATHER, B.CREATE_USER_ID, B.CREATE_TIME, B.UPDATE_USER_ID, B.UPDATE_TIME, B.REMARK, B.STATUS, B.DSS_NUM, B.DEL_FLAG, B.XC_TYPE, B.PROCESSINSTID
        FROM MEMSDB.NM_FINSP_RECORD A
        INNER JOIN MEMSDB.NM_FINSP B ON A.FINSP_ID = B.FINSP_ID
        WHERE A.FACILITY_CAT = #{facilityCat}
        AND B.FINSP_ID IN
        <foreach collection="finspIds" item="finspId" open="(" separator="," close=")">
            #{finspId,jdbcType=VARCHAR}
        </foreach>
        ORDER BY B.INSP_DATE DESC, B.INSP_TIME DESC
    </select>

    <select id="deleteStructData" resultType="java.lang.String">
        SELECT STRUCT_ID FROM MEMSDB.NM_FINSP P WHERE P.FACILITY_CAT=#{facilityCat}
        AND P.STRUCT_ID IN
        <foreach collection="structsIds" item="structId" open="(" separator="," close=")">
            #{structId,jdbcType=VARCHAR}
        </foreach>
        AND TO_CHAR(P.INSP_DATE,'YYYY-MM')<![CDATA[=]]>#{startDate}
    </select>
</mapper>
