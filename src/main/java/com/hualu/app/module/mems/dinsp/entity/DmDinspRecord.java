package com.hualu.app.module.mems.dinsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmDinspRecord对象", description="")
public class DmDinspRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DSS_ID")
    private String dssId;

    @TableField("DINSP_ID")
    private String dinspId;

    @TableField("INSP_TIME")
    private String inspTime;

    @TableField("INSP_TIME_INTVL")
    private String inspTimeIntvl;

    @TableField("ISSUE_TYPE")
    private String issueType;

    @TableField("LINE_DIRECT")
    private String lineDirect;

    @TableField("STAKE")
    private Double stake;

    @TableField("DSS_TYPE")
    private String dssType;

    @TableField("DSS_DEGREE")
    private String dssDegree;

    @TableField("MNTN_ADVICE")
    private String mntnAdvice;

    @TableField("FACILITY_CAT")
    private String facilityCat;

    @TableField("STRUCT_ID")
    private String structId;

    @TableField("STRUCT_PART_ID")
    private String structPartId;

    @TableField("STRUCT_COMP_ID")
    private String structCompId;

    @TableField("LANE")
    private String lane;

    @TableField("DSS_POSITION")
    private String dssPosition;

    @TableField("DSS_DESC")
    private String dssDesc;

    @TableField("DSS_CAUSE")
    private String dssCause;

    @TableField("DSS_L")
    private Double dssL;

    @TableField("DSS_L_UNIT")
    private String dssLUnit;

    @TableField("DSS_W")
    private Double dssW;

    @TableField("DSS_W_UNIT")
    private String dssWUnit;

    @TableField("DSS_D")
    private Double dssD;

    @TableField("DSS_D_UNIT")
    private String dssDUnit;

    @TableField("DSS_N")
    private Double dssN;

    @TableField("DSS_N_UNIT")
    private String dssNUnit;

    @TableField("DSS_A")
    private Double dssA;

    @TableField("DSS_A_UNIT")
    private String dssAUnit;

    @TableField("DSS_V")
    private Double dssV;

    @TableField("DSS_V_UNIT")
    private String dssVUnit;

    @TableField("DSS_P")
    private Double dssP;

    @TableField("DSS_G")
    private Double dssG;

    @TableField("DSS_IMP_FLAG")
    private Integer dssImpFlag;

    @TableField("DSS_QUALITY")
    private Integer dssQuality;

    @TableField("HIS_DSS_ID")
    private String hisDssId;

    @TableField("X")
    private Double x;

    @TableField("Y")
    private Double y;

    @TableField("ISPHONE")
    private Integer isphone;

    @TableField("RAMP_ID")
    private String rampId;

    @ApiModelProperty(value = "隧道部件")
    @TableField("TUNNEL_MOUTH")
    private String tunnelMouth;

    @ApiModelProperty(value = "起点高度")
    @TableField("START_HIGH")
    private Double startHigh;

    @ApiModelProperty(value = "止点高度")
    @TableField("END_HIGH")
    private Double endHigh;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "桩号高度")
    @TableField("STAKE_HIGH")
    private Double stakeHigh;

    @TableField("FINISH_STAKE")
    private Double finishStake;

    @TableField(exist = false)
    private String dssNum;

    @ApiModelProperty(value = "闭合方式（1：任务单闭合，2：专项闭合）")
    @TableField("CLOSE_TYPE")
    private String closeType;

    /**
     * 病害名称
     */
    @TableField(exist = false)
    private String dssTypeName;

    @TableField(exist = false)
    private String lineName;

    @TableField(exist = false)
    private String lineId;

    @TableField(exist = false)
    private String scope;

    @TableField(exist = false)
    private String issueTypeName;

    @TableField(exist = false)
    private String lineDirectName;

    @TableField(exist = false)
    private String facilityCatName;

    @TableField(exist = false)
    private String repairStatus;

    @TableField(exist = false)
    private String path;

    @TableField(exist = false)
    private String fileIds;

    @TableField(exist = false)
    private String structName;

    @TableField(exist = false)
    private String structPartName;

    @TableField(exist = false)
    private String structCompName;

    @TableField(exist = false)
    private String laneName;

    @TableField(exist = false)
    private String dssPositionName;

    @TableField(exist = false)
    private String dssDegreeName;

    @TableField(exist = false)
    private String dssQualityName;

}
