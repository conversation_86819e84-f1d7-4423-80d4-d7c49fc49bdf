package com.hualu.app.module.mems.dss.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.dss.dto.*;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.mapper.DssInfoMapper;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.hualu.app.utils.mems.H_DssHelper;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class DssInfoServiceImpl extends ServiceImpl<DssInfoMapper, DssInfo> implements DssInfoService {

    private String found_date = "to_date('{}','yyyy-mm-dd')";

    @Autowired
    private BaseDatathirdDicService dicService;

    @Override
    public List<DssInfo> selectJADss(DmFinsp dmFinsp) {
        LambdaQueryWrapper<DssInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DssInfo::getDealStatus,0).eq(DssInfo::getFacilityCat, H_BasedataHepler.JA)
                .eq(DssInfo::getMainRoadId,dmFinsp.getLineCode()).eq(DssInfo::getDssSource,1)
                .ne(DssInfo::getDssType,"BJ-0001").ne(DssInfo::getDssType,"BJ-0006")
                .isNull(DssInfo::getStructId).le(DssInfo::getFoundDate, StrUtil.format(found_date, DateTimeUtil.getNowDate()));

        queryWrapper.select(DssInfo::getRlStakeNew,DssInfo::getDssType);
        queryWrapper.orderByAsc(DssInfo::getDssType,DssInfo::getRlStakeNew);
        return list(queryWrapper);
    }

    @Override
    public void updateRepairStatus(List<String> dssId, Integer repairStatus) {
        LambdaUpdateWrapper<DssInfo> updateWrapper = new LambdaUpdateWrapper();

        List<List<String>> partition = com.google.common.collect.Lists.partition(dssId, 999);
        for(List<String> list : partition){
            updateWrapper.or().in(DssInfo::getDssId,list);
        }
        updateWrapper.set(DssInfo::getRepairStatus,repairStatus);
        updateWrapper.set(DssInfo::getRepairDate, new Date());
        update(updateWrapper);
    }

    @Override
    public void updateDealStatus(List<String> dssIds, Integer dealStatus) {
        updateDealStatus(dssIds,null,dealStatus);
    }

    @Override
    public void updateDealStatus(List<String> dssIds, String mtaskId, Integer dealStatus) {
        LambdaUpdateWrapper<DssInfo> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.in(DssInfo::getDssId,dssIds);
        updateWrapper.set(DssInfo::getDealStatus,dealStatus);
        if (StrUtil.isNotBlank(mtaskId)){
            updateWrapper.set(DssInfo::getMtaskId,mtaskId);
        }
        update(updateWrapper);
    }

    @Override
    public List<DssInfoDto> selectDssPage(Page page, QueryWrapper queryWrapper,String orderSql) {
        List<DssInfoDto> dssInfoDtos = baseMapper.selectDssPage(page, queryWrapper,orderSql);
        H_DssHelper.setCheckInfo(dssInfoDtos);
        H_DssHelper.setMtaskCode(dssInfoDtos);
        return dssInfoDtos;
    }

    @Override
    public Map<String, List<DssWxDto>> selectDssWxList(List<String> dssIds) {

        if (CollectionUtil.isEmpty(dssIds)){
            return Maps.newHashMap();
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("a.dss_id",dssIds);
        List<DssWxDto> wxDtos = baseMapper.selectDssWx(queryWrapper);
        Map<String, List<DssWxDto>> wxResList = wxDtos.stream().collect(Collectors.groupingBy(DssWxDto::getDssId));
        return wxResList;
    }

    @Override
    public DssRepairTimelinessDto getRepairTimeliness(String month) {
        Set<String> orgIds = H_DataAuthHelper.selectOrgIds();
        String dateStr = DateUtil.format(DateUtil.parse(month, "yyyy-MM"), "yyyy-MM");
        List<DssRepairInfoDto> infoDtos = baseMapper.selectDssRepairInfos(dateStr, Lists.newArrayList(orgIds));

        DssRepairTimelinessDto repairTimelinessDto = new DssRepairTimelinessDto();
        //统计正常维修的病害
        Map<Integer, List<DssRepairInfoDto>> repairMap = infoDtos.stream().collect(Collectors.groupingBy(DssRepairInfoDto::getRepairStatus));
        repairMap.forEach((k,v)->{
            //1 表示已维修
            if (k.equals(1)){
                H_DssHelper.doRepaired(v,repairTimelinessDto);
            }else {
                repairTimelinessDto.setNoRepairNum(v.size());
            }
        });
        return repairTimelinessDto;
    }

    @Override
    public List<InfoForSlopeBase> selectSlopeDm(String type, String slopeId) {
        return baseMapper.selectSlopeDm(type, slopeId);
    }
}
