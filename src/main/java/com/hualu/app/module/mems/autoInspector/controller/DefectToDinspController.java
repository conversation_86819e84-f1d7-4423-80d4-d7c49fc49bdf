package com.hualu.app.module.mems.autoInspector.controller;

import com.hualu.app.module.mems.autoInspector.entity.NmDinspVo;
import com.hualu.app.module.mems.autoInspector.service.DefectToDinspService;
import com.hualu.app.comm.RestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病害信息转日常巡检记录控制器
 * <p>
 * 提供病害信息转换为日常巡检记录的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
@RestController
@RequestMapping("/mems/auto/defect-to-dinsp")
@Api(tags = "病害转日常巡检接口")
public class DefectToDinspController {

    @Autowired
    private DefectToDinspService defectToDinspService;

    /**
     * 批量转换病害为日常巡检记录
     *
     * @param defectIds 病害ID列表
     * @return 转换结果
     */
    @PostMapping("/batch-convert")
    @ApiOperation("批量转换病害为日常巡检记录")
    public RestResult<List<NmDinspVo>> batchConvert(
            @ApiParam(value = "病害ID列表", required = true)
            @RequestBody List<String> defectIds) {
        try {
            List<NmDinspVo> result = defectToDinspService.batchConvert(defectIds);
            if (result.isEmpty()) {
                return RestResult.error("未找到指定的病害信息");
            }
            return RestResult.success(result);
        } catch (Exception e) {
            log.error("病害批量转换异常", e);
            return RestResult.error("批量转换异常：" + e.getMessage());
        }
    }
} 