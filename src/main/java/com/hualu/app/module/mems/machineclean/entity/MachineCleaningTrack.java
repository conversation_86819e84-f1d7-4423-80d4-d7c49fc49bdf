package com.hualu.app.module.mems.machineclean.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
    * 机械清扫轨迹
    */
@ApiModel(description="机械清扫轨迹")
@TableName(value = "HSMSDB.MACHINE_CLEANING_TRACK")
public class MachineCleaningTrack {
    @TableField(value = "ID")
    @ApiModelProperty(value="")
    private String id;

    /**
     * 机械清扫id
     */
    @TableField(value = "MACHINE_CLEAN_ID")
    @ApiModelProperty(value="机械清扫id")
    private String machineCleanId;

    /**
     * 轨迹id,一个机械清扫可能有多段轨迹
     */
    @TableField(value = "TRACK_ID")
    @ApiModelProperty(value="轨迹id,一个机械清扫可能有多段轨迹")
    private String trackId;

    /**
     * 纬度
     */
    @TableField(value = "LATITUDE")
    @ApiModelProperty(value="纬度")
    private BigDecimal latitude;

    /**
     * 经度
     */
    @TableField(value = "LONGITUDE")
    @ApiModelProperty(value="经度")
    private BigDecimal longitude;

    /**
     * 记录时间
     */
    @TableField(value = "RECORD_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value="记录时间")
    private LocalDateTime recordTime;

    public static final String COL_ID = "ID";

    public static final String COL_MACHINE_CLEAN_ID = "MACHINE_CLEAN_ID";

    public static final String COL_TRACK_ID = "TRACK_ID";

    public static final String COL_LATITUDE = "LATITUDE";

    public static final String COL_LONGITUDE = "LONGITUDE";

    public static final String COL_RECORD_TIME = "RECORD_TIME";

    /**
     * @return ID
     */
    public String getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取机械清扫id
     *
     * @return MACHINE_CLEAN_ID - 机械清扫id
     */
    public String getMachineCleanId() {
        return machineCleanId;
    }

    /**
     * 设置机械清扫id
     *
     * @param machineCleanId 机械清扫id
     */
    public void setMachineCleanId(String machineCleanId) {
        this.machineCleanId = machineCleanId;
    }

    /**
     * 获取轨迹id,一个机械清扫可能有多段轨迹
     *
     * @return TRACK_ID - 轨迹id,一个机械清扫可能有多段轨迹
     */
    public String getTrackId() {
        return trackId;
    }

    /**
     * 设置轨迹id,一个机械清扫可能有多段轨迹
     *
     * @param trackId 轨迹id,一个机械清扫可能有多段轨迹
     */
    public void setTrackId(String trackId) {
        this.trackId = trackId;
    }

    /**
     * 获取纬度
     *
     * @return LATITUDE - 纬度
     */
    public BigDecimal getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度
     *
     * @param latitude 纬度
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    /**
     * 获取经度
     *
     * @return LONGITUDE - 经度
     */
    public BigDecimal getLongitude() {
        return longitude;
    }

    /**
     * 设置经度
     *
     * @param longitude 经度
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取记录时间
     *
     * @return RECORD_TIME - 记录时间
     */
    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    /**
     * 设置记录时间
     *
     * @param recordTime 记录时间
     */
    public void setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
    }
}