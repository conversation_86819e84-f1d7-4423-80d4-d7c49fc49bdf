package com.hualu.app.module.mems.finsp.mapper;

import com.hualu.app.module.mems.finsp.entity.Attendance;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
public interface AttendanceMapper extends BaseMapper<Attendance> {

    Attendance getLastAttendance(@Param("userId") String userId,@Param("structId") String structId);
}
