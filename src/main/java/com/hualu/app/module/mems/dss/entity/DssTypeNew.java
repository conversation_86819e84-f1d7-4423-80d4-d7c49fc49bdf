package com.hualu.app.module.mems.dss.entity;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DssTypeNew对象", description="")
@TableName(value = "DSS_TYPE_NEW")
public class DssTypeNew implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("DSS_TYPE_ID")
    private String dssTypeId;

    @TableField("DSS_TYPE")
    private String dssType;

    @TableField("DSS_TYPE_NAME")
    private String dssTypeName;

    @TableField("STRUCT_COMP_ID")
    private String structCompId;

    @TableField("STRUCT_POSITION")
    private String structPosition;

    @TableField("QULT_DESC_WAY")
    private String qultDescWay;

    @TableField("DSS_REF_IMG")
    private String dssRefImg;

    @TableField("DSS_DRAW_ICON")
    private String dssDrawIcon;

    @TableField("ORG_ID")
    private String orgId;

    @TableField("MAX_DEGREE")
    private Integer maxDegree;

    @TableField("IS_SYS")
    private Integer isSys;

    @TableField("P_DSS_TYPE_ID")
    private String pDssTypeId;

    @TableField("IS_DELETED")
    private Integer isDeleted;

    @TableField("HAVE_DSS_L")
    private Integer haveDssL;

    @TableField("DSS_L_UNIT")
    private String dssLUnit;

    @TableField("HAVE_DSS_W")
    private Integer haveDssW;

    @TableField("DSS_W_UNIT")
    private String dssWUnit;

    @TableField("HAVE_DSS_D")
    private Integer haveDssD;

    @TableField("DSS_D_UNIT")
    private String dssDUnit;

    @TableField("HAVE_DSS_N")
    private Integer haveDssN;

    @TableField("DSS_N_UNIT")
    private String dssNUnit;

    @TableField("HAVE_DSS_A")
    private Integer haveDssA;

    @TableField("DSS_A_UNIT")
    private String dssAUnit;

    @TableField("HAVE_DSS_V")
    private Integer haveDssV;

    @TableField("DSS_V_UNIT")
    private String dssVUnit;

    @TableField("HAVE_DSS_P")
    private Integer haveDssP;

    @TableField("HAVE_DSS_G")
    private Integer haveDssG;

    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("HAVE_DSS_COLOM")
    private String haveDssColom;

    @TableField("HAVE_DSS_UNIT")
    private String haveDssUnit;

    @ApiModelProperty(value = "该字段用于病害的排序，方便管理员进行管理")
    @TableField("DSS_ORDER_NUMBER")
    private Integer dssOrderNumber;


}
