package com.hualu.app.module.mems.nminsp.util;

import cn.hutool.core.util.NumberUtil;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class TrackCorrector {

  // 轨迹点数据类
  public static class TrackPoint {
    private Double lat;
    private Double lng;
    private String time;
    private Double distance;
    private String trackId;

    public TrackPoint(Double lat, Double lng, String time, String trackId) {
      this.lat = lat;
      this.lng = lng;
      this.time = time;
      this.trackId = trackId;
    }

    public TrackPoint() {
    }

    // Getters and Setters
    public Double getLat() {
      return lat;
    }

    public void setLat(Double lat) {
      this.lat = lat;
    }

    public Double getLng() {
      return lng;
    }

    public void setLng(Double lng) {
      this.lng = lng;
    }

    public String getTime() {
      return time;
    }

    public void setTime(String time) {
      this.time = time;
    }

    public Double getDistance() {
      return distance;
    }

    public void setDistance(Double distance) {
      this.distance = distance;
    }

    public String getTrackId() {
      return trackId;
    }

    public void setTrackId(String trackId) {
      this.trackId = trackId;
    }
  }

  // 经纬度数据类（假设DistanceUtil需要）
  public static class LatLng {
    private final double latitude;
    private final double longitude;

    public LatLng(double latitude, double longitude) {
      this.latitude = latitude;
      this.longitude = longitude;
    }

    public double getLatitude() {
      return latitude;
    }

    public double getLongitude() {
      return longitude;
    }
  }

  // 距离计算工具类（假设已存在）
  public static class DistanceUtil {
    private static final double EARTH_RADIUS = 6371000; // 地球半径，单位：米

    /**
     * 使用 Haversine 公式计算两点间的球面距离
     * @param p1 第一个点的经纬度
     * @param p2 第二个点的经纬度
     * @return 两点间的距离，单位：米
     */
    public static double getDistance(LatLng p1, LatLng p2) {
      double lat1 = p1.getLatitude();
      double lng1 = p1.getLongitude();
      double lat2 = p2.getLatitude();
      double lng2 = p2.getLongitude();

      // 将角度转换为弧度
      double lat1Rad = Math.toRadians(lat1);
      double lng1Rad = Math.toRadians(lng1);
      double lat2Rad = Math.toRadians(lat2);
      double lng2Rad = Math.toRadians(lng2);

      // 计算经纬度差值
      double dLat = lat2Rad - lat1Rad;
      double dLng = lng2Rad - lng1Rad;

      // Haversine 公式
      double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2);
      double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      return EARTH_RADIUS * c; // 返回距离，单位：米
    }
  }

  // 转换百度坐标并计算距离
  public static List<List<TrackPoint>> processBaiduTrack(List<TrackPoint> points, double threshold) {
    // 1. 转换坐标
    List<TrackPoint> wgs84Points = points.stream()
        .filter(p -> p.getLat() != null && p.getLng() != null)
        .peek(p -> {
          double[] wgs84 = CoordinateTransform.bd09ToWgs84(p.getLat(), p.getLng());
          p.setLat(wgs84[0]);
          p.setLng(wgs84[1]);
        })
        .collect(Collectors.toList());
    return correctTrack(wgs84Points, threshold);
  }

  /**
   * 修正轨迹
   */
  public static List<List<TrackPoint>> correctTrack(List<TrackPoint> points, double d) {
    // 根据时间排序
    List<TrackPoint> sortList = points.stream()
        .filter(p -> p.getLat() != null && p.getLng() != null)
        .sorted(Comparator.comparing(TrackPoint::getTime))
        .collect(Collectors.toList());

    // 计算两点的距离
    for (int i = 0; i < sortList.size() - 1; i++) {
      TrackPoint src = sortList.get(i);
      TrackPoint next = sortList.get(i + 1);
      double distance = DistanceUtil.getDistance(
          new LatLng(src.getLat(), src.getLng()),
          new LatLng(next.getLat(), next.getLng())
      );
      src.setDistance(distance);
    }

    // 过滤掉距离为0的点
    List<TrackPoint> sortList1 = sortList.stream()
        .filter(p -> p.getDistance() != null && p.getDistance() > 0)
        .collect(Collectors.toList());

    // 拆分成多线段
    LinkedHashMap<Integer, List<TrackPoint>> lineMap = new LinkedHashMap<>();
    int lineIndex = 0;

    for (TrackPoint point : sortList1) {
      lineMap.computeIfAbsent(lineIndex, k -> new ArrayList<>()).add(point);
      if (point.getDistance() > d) {
        lineIndex++;
      }
    }

    // 过滤掉点数少于等于3的线
    List<List<TrackPoint>> filterLineMap = lineMap.values().stream()
        .filter(list -> list.size() > 3)
        .collect(Collectors.toList());

    // 返回结果
    return new ArrayList<>(filterLineMap);
  }

  // 转换百度坐标并计算距离
  public static double processBaiduDistance(List<TrackPoint> points, double threshold) {
    // 1. 转换坐标
    List<TrackPoint> wgs84Points = points.stream()
        .filter(p -> p.getLat() != null && p.getLng() != null)
        .peek(p -> {
          double[] wgs84 = CoordinateTransform.bd09ToWgs84(p.getLat(), p.getLng());
          p.setLat(wgs84[0]);
          p.setLng(wgs84[1]);
        })
        .collect(Collectors.toList());
    return computeTrackDistance(wgs84Points, threshold);
  }

  /**
   * 计算轨迹总距离
   */
  public static double computeTrackDistance(List<TrackPoint> points, double d) {
    double mileage = 0.0;

    // 按trackId分组并处理
    if (points != null) {
      // 去重并按trackId分组
      Map<String, List<TrackPoint>> trackGroups = points.stream()
          .filter(p -> p.getLat() != null && p.getLng() != null)
          .filter(distinctByKey(p -> p.getLat() + "##" + p.getLng()))
          .collect(Collectors.groupingBy(TrackPoint::getTrackId));

      // 处理每个轨迹组
      for (List<TrackPoint> groupPoints : trackGroups.values()) {
        if (groupPoints.isEmpty()) continue;

        // 按时间排序
        List<TrackPoint> sortedPoints = groupPoints.stream()
            .sorted(Comparator.comparing(TrackPoint::getTime))
            .collect(Collectors.toList());

        // 计算相邻点之间的距离
        for (int i = 0; i < sortedPoints.size() - 1; i++) {
          TrackPoint src = sortedPoints.get(i);
          TrackPoint next = sortedPoints.get(i + 1);

          double distance = DistanceUtil.getDistance(
              new LatLng(src.getLat(), src.getLng()),
              new LatLng(next.getLat(), next.getLng())
          );

          src.setDistance(distance);

          // 过滤异常距离点
          if (distance < d) {
            mileage += distance;
          }
        }
      }
    }

    double kmMileage = mileage / 1000d;
    return NumberUtil.round(kmMileage, 3, RoundingMode.HALF_UP).doubleValue();
  }

  // 辅助方法：按key去重
  private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
    Set<Object> seen = Collections.synchronizedSet(new HashSet<>());
    return t -> seen.add(keyExtractor.apply(t));
  }

}