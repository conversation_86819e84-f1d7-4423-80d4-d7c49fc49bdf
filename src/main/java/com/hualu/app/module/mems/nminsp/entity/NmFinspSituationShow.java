package com.hualu.app.module.mems.nminsp.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value="NmFinspSituationShow对象", description="新版经常检查情况展示")
public class NmFinspSituationShow implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "二级单位编码")
    private String secondOrgCode;

    @ApiModelProperty(value = "二级单位名称")
    private String secondOrgName;

    @ApiModelProperty(value = "单位名称")
    private String orgName;

    @ApiModelProperty(value = "单位编码")
    private String orgCode;

    @ApiModelProperty(value = "路线名称")
    private String routeName;

    @ApiModelProperty(value = "路线编码")
    private String routeCode;

    // ================= 路面（Lm） =================
    @ApiModelProperty(value = "一月路面完成数量") private String janLm;
    @ApiModelProperty(value = "一月路面完成情况") private String janLmResult;
    @ApiModelProperty(value = "二月路面完成数量") private String febLm;
    @ApiModelProperty(value = "二月路面完成情况") private String febLmResult;
    @ApiModelProperty(value = "三月路面完成数量") private String marLm;
    @ApiModelProperty(value = "三月路面完成情况") private String marLmResult;
    @ApiModelProperty(value = "四月路面完成数量") private String aprLm;
    @ApiModelProperty(value = "四月路面完成情况") private String aprLmResult;
    @ApiModelProperty(value = "五月路面完成数量") private String mayLm;
    @ApiModelProperty(value = "五月路面完成情况") private String mayLmResult;
    @ApiModelProperty(value = "六月路面完成数量") private String junLm;
    @ApiModelProperty(value = "六月路面完成情况") private String junLmResult;
    @ApiModelProperty(value = "七月路面完成数量") private String julLm;
    @ApiModelProperty(value = "七月路面完成情况") private String julLmResult;
    @ApiModelProperty(value = "八月路面完成数量") private String augLm;
    @ApiModelProperty(value = "八月路面完成情况") private String augLmResult;
    @ApiModelProperty(value = "九月路面完成数量") private String sepLm;
    @ApiModelProperty(value = "九月路面完成情况") private String sepLmResult;
    @ApiModelProperty(value = "十月路面完成数量") private String octLm;
    @ApiModelProperty(value = "十月路面完成情况") private String octLmResult;
    @ApiModelProperty(value = "十一月路面完成数量") private String novLm;
    @ApiModelProperty(value = "十一月路面完成情况") private String novLmResult;
    @ApiModelProperty(value = "十二月路面完成数量") private String decLm;
    @ApiModelProperty(value = "十二月路面完成情况") private String decLmResult;

    // ================= 边坡（Bp） =================
    @ApiModelProperty(value = "一月边坡完成数量") private String janBp;
    @ApiModelProperty(value = "一月边坡完成情况") private String janBpResult;
    @ApiModelProperty(value = "二月边坡完成数量") private String febBp;
    @ApiModelProperty(value = "二月边坡完成情况") private String febBpResult;
    @ApiModelProperty(value = "三月边坡完成数量") private String marBp;
    @ApiModelProperty(value = "三月边坡完成情况") private String marBpResult;
    @ApiModelProperty(value = "四月边坡完成数量") private String aprBp;
    @ApiModelProperty(value = "四月边坡完成情况") private String aprBpResult;
    @ApiModelProperty(value = "五月边坡完成数量") private String mayBp;
    @ApiModelProperty(value = "五月边坡完成情况") private String mayBpResult;
    @ApiModelProperty(value = "六月边坡完成数量") private String junBp;
    @ApiModelProperty(value = "六月边坡完成情况") private String junBpResult;
    @ApiModelProperty(value = "七月边坡完成数量") private String julBp;
    @ApiModelProperty(value = "七月边坡完成情况") private String julBpResult;
    @ApiModelProperty(value = "八月边坡完成数量") private String augBp;
    @ApiModelProperty(value = "八月边坡完成情况") private String augBpResult;
    @ApiModelProperty(value = "九月边坡完成数量") private String sepBp;
    @ApiModelProperty(value = "九月边坡完成情况") private String sepBpResult;
    @ApiModelProperty(value = "十月边坡完成数量") private String octBp;
    @ApiModelProperty(value = "十月边坡完成情况") private String octBpResult;
    @ApiModelProperty(value = "十一月边坡完成数量") private String novBp;
    @ApiModelProperty(value = "十一月边坡完成情况") private String novBpResult;
    @ApiModelProperty(value = "十二月边坡完成数量") private String decBp;
    @ApiModelProperty(value = "十二月边坡完成情况") private String decBpResult;

    // ================= 一级隧道（OneSd） =================
    @ApiModelProperty(value = "一月一级养护隧道完成数量") private String janOneSd;
    @ApiModelProperty(value = "一月一级养护隧道完成情况") private String janOneSdResult;
    @ApiModelProperty(value = "二月一级养护隧道完成数量") private String febOneSd;
    @ApiModelProperty(value = "二月一级养护隧道完成情况") private String febOneSdResult;
    @ApiModelProperty(value = "三月一级养护隧道完成数量") private String marOneSd;
    @ApiModelProperty(value = "三月一级养护隧道完成情况") private String marOneSdResult;
    @ApiModelProperty(value = "四月一级养护隧道完成数量") private String aprOneSd;
    @ApiModelProperty(value = "四月一级养护隧道完成情况") private String aprOneSdResult;
    @ApiModelProperty(value = "五月一级养护隧道完成数量") private String mayOneSd;
    @ApiModelProperty(value = "五月一级养护隧道完成情况") private String mayOneSdResult;
    @ApiModelProperty(value = "六月一级养护隧道完成数量") private String junOneSd;
    @ApiModelProperty(value = "六月一级养护隧道完成情况") private String junOneSdResult;
    @ApiModelProperty(value = "七月一级养护隧道完成数量") private String julOneSd;
    @ApiModelProperty(value = "七月一级养护隧道完成情况") private String julOneSdResult;
    @ApiModelProperty(value = "八月一级养护隧道完成数量") private String augOneSd;
    @ApiModelProperty(value = "八月一级养护隧道完成情况") private String augOneSdResult;
    @ApiModelProperty(value = "九月一级养护隧道完成数量") private String sepOneSd;
    @ApiModelProperty(value = "九月一级养护隧道完成情况") private String sepOneSdResult;
    @ApiModelProperty(value = "十月一级养护隧道完成数量") private String octOneSd;
    @ApiModelProperty(value = "十月一级养护隧道完成情况") private String octOneSdResult;
    @ApiModelProperty(value = "十一月一级养护隧道完成数量") private String novOneSd;
    @ApiModelProperty(value = "十一月一级养护隧道完成情况") private String novOneSdResult;
    @ApiModelProperty(value = "十二月一级养护隧道完成数量") private String decOneSd;
    @ApiModelProperty(value = "十二月一级养护隧道完成情况") private String decOneSdResult;

    // ================= 二级隧道（TwoSd） =================
    @ApiModelProperty(value = "一月二级养护隧道完成数量") private String janTwoSd;
    @ApiModelProperty(value = "一月二级养护隧道完成情况") private String janTwoSdResult;
    @ApiModelProperty(value = "二月二级养护隧道完成数量") private String febTwoSd;
    @ApiModelProperty(value = "二月二级养护隧道完成情况") private String febTwoSdResult;
    @ApiModelProperty(value = "三月二级养护隧道完成数量") private String marTwoSd;
    @ApiModelProperty(value = "三月二级养护隧道完成情况") private String marTwoSdResult;
    @ApiModelProperty(value = "四月二级养护隧道完成数量") private String aprTwoSd;
    @ApiModelProperty(value = "四月二级养护隧道完成情况") private String aprTwoSdResult;
    @ApiModelProperty(value = "五月二级养护隧道完成数量") private String mayTwoSd;
    @ApiModelProperty(value = "五月二级养护隧道完成情况") private String mayTwoSdResult;
    @ApiModelProperty(value = "六月二级养护隧道完成数量") private String junTwoSd;
    @ApiModelProperty(value = "六月二级养护隧道完成情况") private String junTwoSdResult;
    @ApiModelProperty(value = "七月二级养护隧道完成数量") private String julTwoSd;
    @ApiModelProperty(value = "七月二级养护隧道完成情况") private String julTwoSdResult;
    @ApiModelProperty(value = "八月二级养护隧道完成数量") private String augTwoSd;
    @ApiModelProperty(value = "八月二级养护隧道完成情况") private String augTwoSdResult;
    @ApiModelProperty(value = "九月二级养护隧道完成数量") private String sepTwoSd;
    @ApiModelProperty(value = "九月二级养护隧道完成情况") private String sepTwoSdResult;
    @ApiModelProperty(value = "十月二级养护隧道完成数量") private String octTwoSd;
    @ApiModelProperty(value = "十月二级养护隧道完成情况") private String octTwoSdResult;
    @ApiModelProperty(value = "十一月二级养护隧道完成数量") private String novTwoSd;
    @ApiModelProperty(value = "十一月二级养护隧道完成情况") private String novTwoSdResult;
    @ApiModelProperty(value = "十二月二级养护隧道完成数量") private String decTwoSd;
    @ApiModelProperty(value = "十二月二级养护隧道完成情况") private String decTwoSdResult;

    // ================= 一级桥梁（OneQl） =================
    @ApiModelProperty(value = "一月一级养护桥梁完成数量") private String janOneQl;
    @ApiModelProperty(value = "一月一级养护桥梁完成情况") private String janOneQlResult;
    @ApiModelProperty(value = "二月一级养护桥梁完成数量") private String febOneQl;
    @ApiModelProperty(value = "二月一级养护桥梁完成情况") private String febOneQlResult;
    @ApiModelProperty(value = "三月一级养护桥梁完成数量") private String marOneQl;
    @ApiModelProperty(value = "三月一级养护桥梁完成情况") private String marOneQlResult;
    @ApiModelProperty(value = "四月一级养护桥梁完成数量") private String aprOneQl;
    @ApiModelProperty(value = "四月一级养护桥梁完成情况") private String aprOneQlResult;
    @ApiModelProperty(value = "五月一级养护桥梁完成数量") private String mayOneQl;
    @ApiModelProperty(value = "五月一级养护桥梁完成情况") private String mayOneQlResult;
    @ApiModelProperty(value = "六月一级养护桥梁完成数量") private String junOneQl;
    @ApiModelProperty(value = "六月一级养护桥梁完成情况") private String junOneQlResult;
    @ApiModelProperty(value = "七月一级养护桥梁完成数量") private String julOneQl;
    @ApiModelProperty(value = "七月一级养护桥梁完成情况") private String julOneQlResult;
    @ApiModelProperty(value = "八月一级养护桥梁完成数量") private String augOneQl;
    @ApiModelProperty(value = "八月一级养护桥梁完成情况") private String augOneQlResult;
    @ApiModelProperty(value = "九月一级养护桥梁完成数量") private String sepOneQl;
    @ApiModelProperty(value = "九月一级养护桥梁完成情况") private String sepOneQlResult;
    @ApiModelProperty(value = "十月一级养护桥梁完成数量") private String octOneQl;
    @ApiModelProperty(value = "十月一级养护桥梁完成情况") private String octOneQlResult;
    @ApiModelProperty(value = "十一月一级养护桥梁完成数量") private String novOneQl;
    @ApiModelProperty(value = "十一月一级养护桥梁完成情况") private String novOneQlResult;
    @ApiModelProperty(value = "十二月一级养护桥梁完成数量") private String decOneQl;
    @ApiModelProperty(value = "十二月一级养护桥梁完成情况") private String decOneQlResult;

    // ================= 二级桥梁（TwoQl） =================
    @ApiModelProperty(value = "一月二级养护桥梁完成数量") private String janTwoQl;
    @ApiModelProperty(value = "一月二级养护桥梁完成情况") private String janTwoQlResult;
    @ApiModelProperty(value = "二月二级养护桥梁完成数量") private String febTwoQl;
    @ApiModelProperty(value = "二月二级养护桥梁完成情况") private String febTwoQlResult;
    @ApiModelProperty(value = "三月二级养护桥梁完成数量") private String marTwoQl;
    @ApiModelProperty(value = "三月二级养护桥梁完成情况") private String marTwoQlResult;
    @ApiModelProperty(value = "四月二级养护桥梁完成数量") private String aprTwoQl;
    @ApiModelProperty(value = "四月二级养护桥梁完成情况") private String aprTwoQlResult;
    @ApiModelProperty(value = "五月二级养护桥梁完成数量") private String mayTwoQl;
    @ApiModelProperty(value = "五月二级养护桥梁完成情况") private String mayTwoQlResult;
    @ApiModelProperty(value = "六月二级养护桥梁完成数量") private String junTwoQl;
    @ApiModelProperty(value = "六月二级养护桥梁完成情况") private String junTwoQlResult;
    @ApiModelProperty(value = "七月二级养护桥梁完成数量") private String julTwoQl;
    @ApiModelProperty(value = "七月二级养护桥梁完成情况") private String julTwoQlResult;
    @ApiModelProperty(value = "八月二级养护桥梁完成数量") private String augTwoQl;
    @ApiModelProperty(value = "八月二级养护桥梁完成情况") private String augTwoQlResult;
    @ApiModelProperty(value = "九月二级养护桥梁完成数量") private String sepTwoQl;
    @ApiModelProperty(value = "九月二级养护桥梁完成情况") private String sepTwoQlResult;
    @ApiModelProperty(value = "十月二级养护桥梁完成数量") private String octTwoQl;
    @ApiModelProperty(value = "十月二级养护桥梁完成情况") private String octTwoQlResult;
    @ApiModelProperty(value = "十一月二级养护桥梁完成数量") private String novTwoQl;
    @ApiModelProperty(value = "十一月二级养护桥梁完成情况") private String novTwoQlResult;
    @ApiModelProperty(value = "十二月二级养护桥梁完成数量") private String decTwoQl;
    @ApiModelProperty(value = "十二月二级养护桥梁完成情况") private String decTwoQlResult;

    // ================= 一级涵洞（OneHd） =================
    @ApiModelProperty(value = "一月一级养护涵洞完成数量") private String janOneHd;
    @ApiModelProperty(value = "一月一级养护涵洞完成情况") private String janOneHdResult;
    @ApiModelProperty(value = "二月一级养护涵洞完成数量") private String febOneHd;
    @ApiModelProperty(value = "二月一级养护涵洞完成情况") private String febOneHdResult;
    @ApiModelProperty(value = "三月一级养护涵洞完成数量") private String marOneHd;
    @ApiModelProperty(value = "三月一级养护涵洞完成情况") private String marOneHdResult;
    @ApiModelProperty(value = "四月一级养护涵洞完成数量") private String aprOneHd;
    @ApiModelProperty(value = "四月一级养护涵洞完成情况") private String aprOneHdResult;
    @ApiModelProperty(value = "五月一级养护涵洞完成数量") private String mayOneHd;
    @ApiModelProperty(value = "五月一级养护涵洞完成情况") private String mayOneHdResult;
    @ApiModelProperty(value = "六月一级养护涵洞完成数量") private String junOneHd;
    @ApiModelProperty(value = "六月一级养护涵洞完成情况") private String junOneHdResult;
    @ApiModelProperty(value = "七月一级养护涵洞完成数量") private String julOneHd;
    @ApiModelProperty(value = "七月一级养护涵洞完成情况") private String julOneHdResult;
    @ApiModelProperty(value = "八月一级养护涵洞完成数量") private String augOneHd;
    @ApiModelProperty(value = "八月一级养护涵洞完成情况") private String augOneHdResult;
    @ApiModelProperty(value = "九月一级养护涵洞完成数量") private String sepOneHd;
    @ApiModelProperty(value = "九月一级养护涵洞完成情况") private String sepOneHdResult;
    @ApiModelProperty(value = "十月一级养护涵洞完成数量") private String octOneHd;
    @ApiModelProperty(value = "十月一级养护涵洞完成情况") private String octOneHdResult;
    @ApiModelProperty(value = "十一月一级养护涵洞完成数量") private String novOneHd;
    @ApiModelProperty(value = "十一月一级养护涵洞完成情况") private String novOneHdResult;
    @ApiModelProperty(value = "十二月一级养护涵洞完成数量") private String decOneHd;
    @ApiModelProperty(value = "十二月一级养护涵洞完成情况") private String decOneHdResult;

    // ================= 二级涵洞（TwoHd） =================
    @ApiModelProperty(value = "一月二级养护涵洞完成数量") private String janTwoHd;
    @ApiModelProperty(value = "一月二级养护涵洞完成情况") private String janTwoHdResult;
    @ApiModelProperty(value = "二月二级养护涵洞完成数量") private String febTwoHd;
    @ApiModelProperty(value = "二月二级养护涵洞完成情况") private String febTwoHdResult;
    @ApiModelProperty(value = "三月二级养护涵洞完成数量") private String marTwoHd;
    @ApiModelProperty(value = "三月二级养护涵洞完成情况") private String marTwoHdResult;
    @ApiModelProperty(value = "四月二级养护涵洞完成数量") private String aprTwoHd;
    @ApiModelProperty(value = "四月二级养护涵洞完成情况") private String aprTwoHdResult;
    @ApiModelProperty(value = "五月二级养护涵洞完成数量") private String mayTwoHd;
    @ApiModelProperty(value = "五月二级养护涵洞完成情况") private String mayTwoHdResult;
    @ApiModelProperty(value = "六月二级养护涵洞完成数量") private String junTwoHd;
    @ApiModelProperty(value = "六月二级养护涵洞完成情况") private String junTwoHdResult;
    @ApiModelProperty(value = "七月二级养护涵洞完成数量") private String julTwoHd;
    @ApiModelProperty(value = "七月二级养护涵洞完成情况") private String julTwoHdResult;
    @ApiModelProperty(value = "八月二级养护涵洞完成数量") private String augTwoHd;
    @ApiModelProperty(value = "八月二级养护涵洞完成情况") private String augTwoHdResult;
    @ApiModelProperty(value = "九月二级养护涵洞完成数量") private String sepTwoHd;
    @ApiModelProperty(value = "九月二级养护涵洞完成情况") private String sepTwoHdResult;
    @ApiModelProperty(value = "十月二级养护涵洞完成数量") private String octTwoHd;
    @ApiModelProperty(value = "十月二级养护涵洞完成情况") private String octTwoHdResult;
    @ApiModelProperty(value = "十一月二级养护涵洞完成数量") private String novTwoHd;
    @ApiModelProperty(value = "十一月二级养护涵洞完成情况") private String novTwoHdResult;
    @ApiModelProperty(value = "十二月二级养护涵洞完成数量") private String decTwoHd;
    @ApiModelProperty(value = "十二月二级养护涵洞完成情况") private String decTwoHdResult;

    // ================= 三级涵洞（ThreeHd） =================
    @ApiModelProperty(value = "一月三级养护涵洞完成数量") private String janThreeHd;
    @ApiModelProperty(value = "一月三级养护涵洞完成情况") private String janThreeHdResult;
    @ApiModelProperty(value = "二月三级养护涵洞完成数量") private String febThreeHd;
    @ApiModelProperty(value = "二月三级养护涵洞完成情况") private String febThreeHdResult;
    @ApiModelProperty(value = "三月三级养护涵洞完成数量") private String marThreeHd;
    @ApiModelProperty(value = "三月三级养护涵洞完成情况") private String marThreeHdResult;
    @ApiModelProperty(value = "四月三级养护涵洞完成数量") private String aprThreeHd;
    @ApiModelProperty(value = "四月三级养护涵洞完成情况") private String aprThreeHdResult;
    @ApiModelProperty(value = "五月三级养护涵洞完成数量") private String mayThreeHd;
    @ApiModelProperty(value = "五月三级养护涵洞完成情况") private String mayThreeHdResult;
    @ApiModelProperty(value = "六月三级养护涵洞完成数量") private String junThreeHd;
    @ApiModelProperty(value = "六月三级养护涵洞完成情况") private String junThreeHdResult;
    @ApiModelProperty(value = "七月三级养护涵洞完成数量") private String julThreeHd;
    @ApiModelProperty(value = "七月三级养护涵洞完成情况") private String julThreeHdResult;
    @ApiModelProperty(value = "八月三级养护涵洞完成数量") private String augThreeHd;
    @ApiModelProperty(value = "八月三级养护涵洞完成情况") private String augThreeHdResult;
    @ApiModelProperty(value = "九月三级养护涵洞完成数量") private String sepThreeHd;
    @ApiModelProperty(value = "九月三级养护涵洞完成情况") private String sepThreeHdResult;
    @ApiModelProperty(value = "十月三级养护涵洞完成数量") private String octThreeHd;
    @ApiModelProperty(value = "十月三级养护涵洞完成情况") private String octThreeHdResult;
    @ApiModelProperty(value = "十一月三级养护涵洞完成数量") private String novThreeHd;
    @ApiModelProperty(value = "十一月三级养护涵洞完成情况") private String novThreeHdResult;
    @ApiModelProperty(value = "十二月三级养护涵洞完成数量") private String decThreeHd;
    @ApiModelProperty(value = "十二月三级养护涵洞完成情况") private String decThreeHdResult;

    // ================= 特殊桥梁（SpeQl） =================
// 一类特殊桥梁
    @ApiModelProperty(value = "一月一类特殊桥梁完成数量") private String janOneSpeQl;
    @ApiModelProperty(value = "一月一类特殊桥梁完成情况") private String janOneSpeQlResult;
    @ApiModelProperty(value = "二月一类特殊桥梁完成数量") private String febOneSpeQl;
    @ApiModelProperty(value = "二月一类特殊桥梁完成情况") private String febOneSpeQlResult;
    @ApiModelProperty(value = "三月一类特殊桥梁完成数量") private String marOneSpeQl;
    @ApiModelProperty(value = "三月一类特殊桥梁完成情况") private String marOneSpeQlResult;
    @ApiModelProperty(value = "四月一类特殊桥梁完成数量") private String aprOneSpeQl;
    @ApiModelProperty(value = "四月一类特殊桥梁完成情况") private String aprOneSpeQlResult;
    @ApiModelProperty(value = "五月一类特殊桥梁完成数量") private String mayOneSpeQl;
    @ApiModelProperty(value = "五月一类特殊桥梁完成情况") private String mayOneSpeQlResult;
    @ApiModelProperty(value = "六月一类特殊桥梁完成数量") private String junOneSpeQl;
    @ApiModelProperty(value = "六月一类特殊桥梁完成情况") private String junOneSpeQlResult;
    @ApiModelProperty(value = "七月一类特殊桥梁完成数量") private String julOneSpeQl;
    @ApiModelProperty(value = "七月一类特殊桥梁完成情况") private String julOneSpeQlResult;
    @ApiModelProperty(value = "八月一类特殊桥梁完成数量") private String augOneSpeQl;
    @ApiModelProperty(value = "八月一类特殊桥梁完成情况") private String augOneSpeQlResult;
    @ApiModelProperty(value = "九月一类特殊桥梁完成数量") private String sepOneSpeQl;
    @ApiModelProperty(value = "九月一类特殊桥梁完成情况") private String sepOneSpeQlResult;
    @ApiModelProperty(value = "十月一类特殊桥梁完成数量") private String octOneSpeQl;
    @ApiModelProperty(value = "十月一类特殊桥梁完成情况") private String octOneSpeQlResult;
    @ApiModelProperty(value = "十一月一类特殊桥梁完成数量") private String novOneSpeQl;
    @ApiModelProperty(value = "十一月一类特殊桥梁完成情况") private String novOneSpeQlResult;
    @ApiModelProperty(value = "十二月一类特殊桥梁完成数量") private String decOneSpeQl;
    @ApiModelProperty(value = "十二月一类特殊桥梁完成情况") private String decOneSpeQlResult;

    // 二类特殊桥梁
    @ApiModelProperty(value = "一月二类特殊桥梁完成数量") private String janTwoSpeQl;
    @ApiModelProperty(value = "一月二类特殊桥梁完成情况") private String janTwoSpeQlResult;
    @ApiModelProperty(value = "二月二类特殊桥梁完成数量") private String febTwoSpeQl;
    @ApiModelProperty(value = "二月二类特殊桥梁完成情况") private String febTwoSpeQlResult;
    @ApiModelProperty(value = "三月二类特殊桥梁完成数量") private String marTwoSpeQl;
    @ApiModelProperty(value = "三月二类特殊桥梁完成情况") private String marTwoSpeQlResult;
    @ApiModelProperty(value = "四月二类特殊桥梁完成数量") private String aprTwoSpeQl;
    @ApiModelProperty(value = "四月二类特殊桥梁完成情况") private String aprTwoSpeQlResult;
    @ApiModelProperty(value = "五月二类特殊桥梁完成数量") private String mayTwoSpeQl;
    @ApiModelProperty(value = "五月二类特殊桥梁完成情况") private String mayTwoSpeQlResult;
    @ApiModelProperty(value = "六月二类特殊桥梁完成数量") private String junTwoSpeQl;
    @ApiModelProperty(value = "六月二类特殊桥梁完成情况") private String junTwoSpeQlResult;
    @ApiModelProperty(value = "七月二类特殊桥梁完成数量") private String julTwoSpeQl;
    @ApiModelProperty(value = "七月二类特殊桥梁完成情况") private String julTwoSpeQlResult;
    @ApiModelProperty(value = "八月二类特殊桥梁完成数量") private String augTwoSpeQl;
    @ApiModelProperty(value = "八月二类特殊桥梁完成情况") private String augTwoSpeQlResult;
    @ApiModelProperty(value = "九月二类特殊桥梁完成数量") private String sepTwoSpeQl;
    @ApiModelProperty(value = "九月二类特殊桥梁完成情况") private String sepTwoSpeQlResult;
    @ApiModelProperty(value = "十月二类特殊桥梁完成数量") private String octTwoSpeQl;
    @ApiModelProperty(value = "十月二类特殊桥梁完成情况") private String octTwoSpeQlResult;
    @ApiModelProperty(value = "十一月二类特殊桥梁完成数量") private String novTwoSpeQl;
    @ApiModelProperty(value = "十一月二类特殊桥梁完成情况") private String novTwoSpeQlResult;
    @ApiModelProperty(value = "十二月二类特殊桥梁完成数量") private String decTwoSpeQl;
    @ApiModelProperty(value = "十二月二类特殊桥梁完成情况") private String decTwoSpeQlResult;

    // 三类特殊桥梁
    @ApiModelProperty(value = "一月三类特殊桥梁完成数量") private String janThreeSpeQl;
    @ApiModelProperty(value = "一月三类特殊桥梁完成情况") private String janThreeSpeQlResult;
    @ApiModelProperty(value = "二月三类特殊桥梁完成数量") private String febThreeSpeQl;
    @ApiModelProperty(value = "二月三类特殊桥梁完成情况") private String febThreeSpeQlResult;
    @ApiModelProperty(value = "三月三类特殊桥梁完成数量") private String marThreeSpeQl;
    @ApiModelProperty(value = "三月三类特殊桥梁完成情况") private String marThreeSpeQlResult;
    @ApiModelProperty(value = "四月三类特殊桥梁完成数量") private String aprThreeSpeQl;
    @ApiModelProperty(value = "四月三类特殊桥梁完成情况") private String aprThreeSpeQlResult;
    @ApiModelProperty(value = "五月三类特殊桥梁完成数量") private String mayThreeSpeQl;
    @ApiModelProperty(value = "五月三类特殊桥梁完成情况") private String mayThreeSpeQlResult;
    @ApiModelProperty(value = "六月三类特殊桥梁完成数量") private String junThreeSpeQl;
    @ApiModelProperty(value = "六月三类特殊桥梁完成情况") private String junThreeSpeQlResult;
    @ApiModelProperty(value = "七月三类特殊桥梁完成数量") private String julThreeSpeQl;
    @ApiModelProperty(value = "七月三类特殊桥梁完成情况") private String julThreeSpeQlResult;
    @ApiModelProperty(value = "八月三类特殊桥梁完成数量") private String augThreeSpeQl;
    @ApiModelProperty(value = "八月三类特殊桥梁完成情况") private String augThreeSpeQlResult;
    @ApiModelProperty(value = "九月三类特殊桥梁完成数量") private String sepThreeSpeQl;
    @ApiModelProperty(value = "九月三类特殊桥梁完成情况") private String sepThreeSpeQlResult;
    @ApiModelProperty(value = "十月三类特殊桥梁完成数量") private String octThreeSpeQl;
    @ApiModelProperty(value = "十月三类特殊桥梁完成情况") private String octThreeSpeQlResult;
    @ApiModelProperty(value = "十一月三类特殊桥梁完成数量") private String novThreeSpeQl;
    @ApiModelProperty(value = "十一月三类特殊桥梁完成情况") private String novThreeSpeQlResult;
    @ApiModelProperty(value = "十二月三类特殊桥梁完成数量") private String decThreeSpeQl;
    @ApiModelProperty(value = "十二月三类特殊桥梁完成情况") private String decThreeSpeQlResult;

    // 四类特殊桥梁
    @ApiModelProperty(value = "一月四类特殊桥梁完成数量") private String janFourSpeQl;
    @ApiModelProperty(value = "一月四类特殊桥梁完成情况") private String janFourSpeQlResult;
    @ApiModelProperty(value = "二月四类特殊桥梁完成数量") private String febFourSpeQl;
    @ApiModelProperty(value = "二月四类特殊桥梁完成情况") private String febFourSpeQlResult;
    @ApiModelProperty(value = "三月四类特殊桥梁完成数量") private String marFourSpeQl;
    @ApiModelProperty(value = "三月四类特殊桥梁完成情况") private String marFourSpeQlResult;
    @ApiModelProperty(value = "四月四类特殊桥梁完成数量") private String aprFourSpeQl;
    @ApiModelProperty(value = "四月四类特殊桥梁完成情况") private String aprFourSpeQlResult;
    @ApiModelProperty(value = "五月四类特殊桥梁完成数量") private String mayFourSpeQl;
    @ApiModelProperty(value = "五月四类特殊桥梁完成情况") private String mayFourSpeQlResult;
    @ApiModelProperty(value = "六月四类特殊桥梁完成数量") private String junFourSpeQl;
    @ApiModelProperty(value = "六月四类特殊桥梁完成情况") private String junFourSpeQlResult;
    @ApiModelProperty(value = "七月四类特殊桥梁完成数量") private String julFourSpeQl;
    @ApiModelProperty(value = "七月四类特殊桥梁完成情况") private String julFourSpeQlResult;
    @ApiModelProperty(value = "八月四类特殊桥梁完成数量") private String augFourSpeQl;
    @ApiModelProperty(value = "八月四类特殊桥梁完成情况") private String augFourSpeQlResult;
    @ApiModelProperty(value = "九月四类特殊桥梁完成数量") private String sepFourSpeQl;
    @ApiModelProperty(value = "九月四类特殊桥梁完成情况") private String sepFourSpeQlResult;
    @ApiModelProperty(value = "十月四类特殊桥梁完成数量") private String octFourSpeQl;
    @ApiModelProperty(value = "十月四类特殊桥梁完成情况") private String octFourSpeQlResult;
    @ApiModelProperty(value = "十一月四类特殊桥梁完成数量") private String novFourSpeQl;
    @ApiModelProperty(value = "十一月四类特殊桥梁完成情况") private String novFourSpeQlResult;
    @ApiModelProperty(value = "十二月四类特殊桥梁完成数量") private String decFourSpeQl;
    @ApiModelProperty(value = "十二月四类特殊桥梁完成情况") private String decFourSpeQlResult;

    // ================= 交安（Ja） =================
    @ApiModelProperty(value = "一月交安完成数量") private String janJa;
    @ApiModelProperty(value = "一月交安完成情况") private String janJaResult;
    @ApiModelProperty(value = "二月交安完成数量") private String febJa;
    @ApiModelProperty(value = "二月交安完成情况") private String febJaResult;
    @ApiModelProperty(value = "三月交安完成数量") private String marJa;
    @ApiModelProperty(value = "三月交安完成情况") private String marJaResult;
    @ApiModelProperty(value = "四月交安完成数量") private String aprJa;
    @ApiModelProperty(value = "四月交安完成情况") private String aprJaResult;
    @ApiModelProperty(value = "五月交安完成数量") private String mayJa;
    @ApiModelProperty(value = "五月交安完成情况") private String mayJaResult;
    @ApiModelProperty(value = "六月交安完成数量") private String junJa;
    @ApiModelProperty(value = "六月交安完成情况") private String junJaResult;
    @ApiModelProperty(value = "七月交安完成数量") private String julJa;
    @ApiModelProperty(value = "七月交安完成情况") private String julJaResult;
    @ApiModelProperty(value = "八月交安完成数量") private String augJa;
    @ApiModelProperty(value = "八月交安完成情况") private String augJaResult;
    @ApiModelProperty(value = "九月交安完成数量") private String sepJa;
    @ApiModelProperty(value = "九月交安完成情况") private String sepJaResult;
    @ApiModelProperty(value = "十月交安完成数量") private String octJa;
    @ApiModelProperty(value = "十月交安完成情况") private String octJaResult;
    @ApiModelProperty(value = "十一月交安完成数量") private String novJa;
    @ApiModelProperty(value = "十一月交安完成情况") private String novJaResult;
    @ApiModelProperty(value = "十二月交安完成数量") private String decJa;
    @ApiModelProperty(value = "十二月交安完成情况") private String decJaResult;
}
