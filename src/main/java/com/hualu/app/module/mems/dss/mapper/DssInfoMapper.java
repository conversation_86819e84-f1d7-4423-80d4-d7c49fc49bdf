package com.hualu.app.module.mems.dss.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.dss.dto.*;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DssInfoMapper extends BaseMapper<DssInfo> {

    List<DssInfoDto> selectDssPage(Page page, @Param(Constants.WRAPPER) QueryWrapper wrapper,@Param("orderSql") String orderSql);

    List<DssInfoDto> selectMtaskCode(@Param("dssIds") List<String> dssIds);
    /**
     * 养护单位日常巡查
     * @param wrapper
     * @return
     */
    List<DssCheckInfo> selectByDinsp(@Param(Constants.WRAPPER) QueryWrapper wrapper);



    /**
     * 经常检查单
     * @param wrapper
     * @return
     */
    List<DssCheckInfo> selectByFinsp(@Param(Constants.WRAPPER) QueryWrapper wrapper);


    /**
     * 路政通知书
     * @param wrapper
     * @return
     */
    List<DssCheckInfo> selectByNotice(@Param(Constants.WRAPPER) QueryWrapper wrapper);

    /**
     * 病害维修信息查询
     * @param queryWrapper
     * @return
     */
    List<DssWxDto> selectDssWx(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);


    /**
     * 病害维修情况
     * @param month
     * @param orgIds
     * @return
     */
    List<DssRepairInfoDto> selectDssRepairInfos(@Param("month") String month,@Param("orgIds") List<String> orgIds);


    /**
     * 边坡日常巡查/经常检查
     * @param type
     * @param slopeId
     * @return
     */
    List<InfoForSlopeBase> selectSlopeDm(@Param("type") String type, @Param("slopeId") String slopeId);
}
