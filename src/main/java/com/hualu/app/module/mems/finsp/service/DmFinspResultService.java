package com.hualu.app.module.mems.finsp.service;

import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DmFinspResultService extends IService<DmFinspResult> {


    List<DmFinspResult> selectResultByFinspId(String finspId);

    void delDmFinspResult(String finspId);
    /**
     * 查询上次经常检查结论
     * @param finspId
     * @return
     */
    List<DmFinspResult> selectLastFinspResult(String finspId);

    /**
     * 根据病害修改检查结论
     * @param finspId
     * @param facilityCat
     * @param records
     */
    void updateFinspResultsByRecords(String finspId,String facilityCat,List<DmFinspRecord> records);
}
