package com.hualu.app.module.mems.autoInspector.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.autoInspector.entity.HighwayCategoryDictionary;
import com.hualu.app.module.mems.autoInspector.service.HighwayCategoryDictionaryService;
import com.hualu.app.module.mems.autoInspector.mapper.HighwayCategoryDictionaryMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_CATEGORY_DICTIONARY(巡检分类字典表)】的数据库操作Service实现
* @createDate 2025-04-28 10:11:40
*/
@Service
public class HighwayCategoryDictionaryServiceImpl extends ServiceImpl<HighwayCategoryDictionaryMapper, HighwayCategoryDictionary>
    implements HighwayCategoryDictionaryService{

}




