package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.mapper.NmInspContentMapper;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.utils.H_BasedataHepler;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 新版日常巡检及经常检查巡查内容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Service
public class NmInspContentServiceImpl extends ServiceImpl<NmInspContentMapper, NmInspContent> implements NmInspContentService {

    @Autowired
    DssTypeNewService dssTypeNewService;

    @Override
    public List<NmInspContent> listByContent(String facilityCat, String xcType) {
        return listByContent(facilityCat,xcType,"1");
    }

    @Override
    public List<NmInspContent> listByContent(String facilityCat, String xcType, String pageShow) {

        boolean notRgd = H_BasedataHepler.RGD.equals(facilityCat)?false:true;
        LambdaQueryWrapper<NmInspContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmInspContent::getFacilityCat,facilityCat)
                .eq(notRgd && StrUtil.isNotBlank(pageShow),NmInspContent::getPageShow,pageShow)
                .eq(notRgd && StrUtil.isNotBlank(xcType),NmInspContent::getXcType,xcType);
        queryWrapper.orderByAsc(NmInspContent::getSortNo,NmInspContent::getId);
        return list(queryWrapper);
    }

    @Override
    public List<DssTypeNew> listDssTypeByPartId(String partId,String facilityCat, String xcType) {
        List<NmInspContent> contents = listByContent(facilityCat, xcType);
        if (CollectionUtil.isEmpty(contents)){
            return Collections.emptyList();
        }

        Set<NmInspContent> partContents = contents.stream().filter(e -> e.getPartId().equals(partId) || e.getId().equals(partId)).collect(Collectors.toSet());

        Set<String> dssTypes = Sets.newHashSet();
        for (NmInspContent item : partContents) {
            if (StrUtil.isNotBlank(item.getDssType())){
                List<String> dssList = StrUtil.split(item.getDssType().trim(), ",");
                dssTypes.addAll(dssList);
            }
        }
        // 查询病害类型
        List<DssTypeNew> dssTypeNews = dssTypeNewService.listByDssType(Lists.newArrayList(dssTypes));
        Iterator<DssTypeNew> iterator = dssTypeNews.iterator();
        while (iterator.hasNext()){
            DssTypeNew next = iterator.next();
            if(dssTypeNews.stream().filter((o->o.getDssTypeName().equals(next.getDssTypeName()))).count()>1){
                iterator.remove();
            }
        }
        return dssTypeNews;
    }

    @Override
    public Map<String, String> getContentMap(List<String> ids) {
        List<NmInspContent> list = lambdaQuery().in(NmInspContent::getId, ids).list();
        if (CollectionUtil.isEmpty(list)){
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(NmInspContent::getId,NmInspContent::getName));
    }
}
