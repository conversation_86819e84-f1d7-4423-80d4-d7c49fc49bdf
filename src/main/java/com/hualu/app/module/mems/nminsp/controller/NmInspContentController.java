package com.hualu.app.module.mems.nminsp.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.mapper.NmInspContentMapper;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.nminsp.vo.NmInspContentTreeVo;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.util.hp.H_TreeHelper;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 新版日常巡检及经常检查巡查内容 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Validated
@Api(tags = "巡查内容配置",description = "新版日常巡检及经常检查巡查内容 前端控制器")
@RestController
@RequestMapping("/nmInspContent")
public class NmInspContentController extends M_MyBatisController<NmInspContent,NmInspContentMapper>{

    @Autowired
    private NmInspContentService service;

    /**
     * 查询病害类型
     * @param facilityCat
     * @param partId
     * @return
     */
    @ApiRegister(value = "nmInspContent:list",businessType = BusinessType.OTHER)
    @ApiOperation("查询病害类型")
    @PostMapping("listDssType")
    public RestResult<List<DssTypeNew>> listDssType(@RequestParam @NotBlank String facilityCat,@RequestParam @NotBlank String partId) {
        List<DssTypeNew> dssTypeNews = service.listDssTypeByPartId(partId, facilityCat, null);
        return RestResult.success(dssTypeNews);
    }

    /**
     * 查询部位
     * @param facilityCat
     * @param xcType
     * @return
     */
    @PostMapping("listPart")
    public RestResult<List<NmInspContentTreeVo>> listPart(@RequestParam @NotBlank String facilityCat,String xcType,String inspFrequency) {
        List<NmInspContent> contents = service.listByContent(facilityCat, xcType);
        if(inspFrequency!=null){
            if(inspFrequency.equals("1")||inspFrequency.equals("6")||inspFrequency.equals("5")){
                contents.removeIf(next->!next.getInspFrequency().equals(inspFrequency));
            }
            if(inspFrequency.equals("2")){
                //拱桥包含梁式桥所有部件
                contents.removeIf(next->!(next.getInspFrequency().equals(inspFrequency)||(next.getInspFrequency().equals("1"))));
            }
        }
        List<NmInspContentTreeVo> voList = Lists.newArrayList();
        contents.forEach(content->{
            NmInspContentTreeVo vo = new NmInspContentTreeVo();
            vo.setId(content.getId());
            vo.setName(content.getName());
            vo.setParentId(content.getPid());
            voList.add(vo);
        });
        List<NmInspContentTreeVo> treeVos = H_TreeHelper.buildByRecursive(voList, "-1");
        return RestResult.success(treeVos);
    }
}