package com.hualu.app.module.mems.finsp.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspDealDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspRecordExcelDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.entity.PcFinspRecord;
import com.hualu.app.module.mems.finsp.mapper.PcFinspRecordMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspDsstypeService;
import com.hualu.app.module.mems.finsp.service.PcFinspImageService;
import com.hualu.app.module.mems.finsp.service.PcFinspRecordService;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

/**
 * 结构物排查记录表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Api(tags = "结构物排查记录表_接口",description = "结构物排查记录表 前端控制器")
@RestController
@RequestMapping("/pcFinspRecord")
public class PcFinspRecordController extends M_MyBatisController<PcFinspRecord,PcFinspRecordMapper>{

    @Autowired
    private PcFinspRecordService service;

    @Autowired
    private PcFinspImageService imageService;

    @Autowired
    private PcFinspDsstypeService dsstypeService;

    @Value("${fileReposity}")
    String fileReposity;


    /**
     * 获取视野范围内的边坡处置类型的位置信息
     * @param paramDto
     * @return
     */
    @PostMapping("geometry")
    public RestResult<List<PcFinspDealDto>> getGeometryDeal(@RequestBody @Validated GeometryParamDto paramDto){
        List<PcFinspDealDto> dealDtos = service.getGeometryDeal(paramDto);
        return RestResult.success(dealDtos);
    }


    /**
     * 边坡及排水系统旱季大排查大整治情况汇总表-边坡
     * @param orgId 机构ID
     * @param startMonth 开始日期（2024-07）
     * @param endMonth 结束日期（2024-08）
     * @param response
     */
    @SneakyThrows
    @GetMapping("exportRecords")
    public RestResult<String> exportRecords(String orgId, String startMonth, String endMonth, HttpServletResponse response) {
        List<PcFinspRecordExcelDto> excelDtos = service.exportRecords(orgId,startMonth,endMonth);
        String excelTemplatPath = "excel/pcFinsp/slopeDssInfo.xlsx";
        TemplateExportParams exportParams = new TemplateExportParams(excelTemplatPath,true,null);
        Map<String, Object> xxMap = Maps.newHashMap();
        xxMap.put("recList",excelDtos);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,xxMap);
        //H_ExcelDownloadHelper.downLoad("经常检查统计.xlsx",response,workbook);
        String fileName = File.separator + IdUtil.fastSimpleUUID() + ".xlsx";
        try(OutputStream outputStream = Files.newOutputStream(new File(fileReposity + fileName).toPath())){
            workbook.write(outputStream);
            workbook.close();
        }
        return RestResult.success("fileResposity"+fileName,"操作成功");
    }

    /**
     * 排查病害统计图
     * @param orgId 机构ID
     * @param mntType 单位类型（0：全部、1：定检单位、2：养护单位）
     * @param startMonth 开始日期（2024-07）
     * @param endMonth 结束日期（2024-08）
     * @param facilityCat 设施类型：BP、QL、HD
     * @return
     */
    @GetMapping("/getFinspStat")
    public RestResult<List<PcFinspStatDto>> getFinspStat(String orgId,String mntType,String startMonth, String endMonth,@RequestParam(defaultValue = "BP") String facilityCat) {
        List<PcFinspStatDto> statDtos = service.getFinspStat(startMonth,endMonth,orgId,mntType,facilityCat);
        return RestResult.success(statDtos);
    }

    /**
     * 加载病害类型
     * @return
     */
    @GetMapping("loadDsstypes")
    public RestResult<List<PcFinspDssTypeDto>> getDsstypsByPartId(String partId){
        List<PcFinspDssTypeDto> typeDtos = dsstypeService.listByPartId(partId);
        return RestResult.success(typeDtos);
    }

    /**
     * 删除病害照片
     * @param fileId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @GetMapping("delImage")
    public RestResult<String> deleteImage(String fileId){
        String recordId = imageService.getRecordIdByFileId(fileId);
        imageService.removeById(fileId);
        service.updateStatus(recordId);
        return RestResult.success("操作成功");
    }

    /**
     * 删除检查病害记录
     * @param ids 病害ID，多个病害以逗号分隔（例如：xx1,xx2,xx3）
     * @return
     */
    @PostMapping("deleteFinspRecordByIds")
    public RestResult<String> deleteFinspRecordByIds(@NotBlank @RequestParam("ids") String ids){
        service.deleteFinspRecordByIds(StrUtil.split(ids,","));
        return RestResult.success("操作成功");
    }

    @ApiOperation("查询排查单病害")
    @GetMapping("loadFinspRecordByFinspId/{finspId}")
    public RestResult<List<PcFinspRecord>> loadFinspRecordByFinspId(@PathVariable("finspId") String finspId){
        IPage iPage = service.selectViewRecordByFinspId(finspId);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 根据结构ID和处置状态，查询病害集合
     * @param structId 结构物ID
     * @param dealStatus 处治状态 0：未处治，1：已处治
     * @return
     */
    @GetMapping("loadRecordByStructId")
    public RestResult<List<PcFinspRecord>> loadRecordByStructId(String structId,String dealStatus){
        List<PcFinspRecord> records = service.listRecordByStructId(structId,dealStatus);
        return RestResult.success(records);
    }

    /**
     * 查询处治病害列表
     * @param dealStatus 0：未处治，1：已处治
     * @return
     */
    @PostMapping("loadRecords")
    public RestResult<List<PcFinspRecord>> loadRecords(String dealStatus){
        IPage iPage = service.listRecord(dealStatus);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    @ApiOperation("添加或者修改病害")
    @PostMapping(value="/saveOrUpdateFinspRecord")
    public RestResult<PcFinspRecord> saveOrUpdateFinspRecord(@RequestParam("data") String data,@RequestParam(value = "files",required = false) MultipartFile[] files){
        PcFinspRecord dmFinspRecord = JSONUtil.toBean(data,PcFinspRecord.class);
        H_CValidator.validator2Exception(dmFinspRecord);
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        boolean isAdd = StrUtil.isBlank(dmFinspRecord.getRecordId());
        if (isAdd){
            dmFinspRecord.setRecordId(H_KeyWorker.nextIdToString());
        }
        if (CollectionUtil.isNotEmpty(fileIds)){
            List<PcFinspImage> images = Lists.newArrayList();
            fileIds.forEach(item->{
                // 照片类型(1:处治前,2:处治中,3:处治后)
                PcFinspImage image = new PcFinspImage();
                image.setImageType("1");
                image.setImageId(item);
                image.setRecordId(dmFinspRecord.getRecordId());
                images.add(image);
            });
            dmFinspRecord.setFileIds(images);
        }else {
            //解决图片重复的问题
            dmFinspRecord.setFileIds(null);
        }
        PcFinspRecord dbrecord = service.saveOrUpdateFinspRecord(dmFinspRecord);
        return RestResult.success(dbrecord);
    }


    /**
     * 处治过程中照片上传
     * @param recordId 记录ID
     * @param imageType 照片类型(1:处治前,2:处治中,3:处治后)
     * @param files 图片文件
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @PostMapping("uploadImage")
    public RestResult uploadDssImage(@RequestParam(value = "recordId",required = true) String recordId, @RequestParam(value = "imageType",required = true) String imageType,
                                     @RequestParam(value = "files") MultipartFile[] files){
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            List<PcFinspImage> images = Lists.newArrayList();
            for (String fileId : fileIds) {
                PcFinspImage image = new PcFinspImage();
                image.setImageId(fileId);
                image.setRecordId(recordId);
                image.setImageType(imageType);
                images.add(image);
            }
            imageService.saveBatch(images);
            // 添加照片后，把病害设置为处治后
            service.updateStatus(recordId);
        }
        return RestResult.success(StrUtil.join(",",fileIds), "操作成功");
    }

}