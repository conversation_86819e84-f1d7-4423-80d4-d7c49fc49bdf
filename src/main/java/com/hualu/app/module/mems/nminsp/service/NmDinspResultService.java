package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspResult;

import java.util.List;

/**
 * <p>
 * 新版日常巡查检查结论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface NmDinspResultService extends IService<NmDinspResult> {

    List<NmDinspResult> createNmDinspResult(NmDinsp nmDinsp);

    /**
     * 批量生成检查结论
     * @param nmDinsp
     * @param dinspIds
     * @return
     */
    void createBatchNmDinspResult(NmDinsp nmDinsp,List<String> dinspIds);

    void delBatchByDinspIds(List<String> dinspIds);

    List<NmDinspResult> listResultsByJA(List<String> dinspIds,String inspCont);

    List<NmDinspResult> listResultsByDinspIds(List<String> dinspIds);

    void supplementRemainingResult(NmDinspResult nmDinspResult);
}
