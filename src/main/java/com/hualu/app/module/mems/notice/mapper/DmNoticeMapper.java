package com.hualu.app.module.mems.notice.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.mems.notice.entity.DmNotice;
import org.apache.ibatis.annotations.Param;

public interface DmNoticeMapper extends BaseMapper<DmNotice> {

  IPage<DmNotice> getRoadNoticeList(
      IPage<DmNotice> p,
      @Param("ew") Wrapper<DmNotice> wrapper
  );

  Integer getCurrentDayMaxOrder(
      @Param("prefix") String prefix,
      @Param("orgId") String orgId
  );

}