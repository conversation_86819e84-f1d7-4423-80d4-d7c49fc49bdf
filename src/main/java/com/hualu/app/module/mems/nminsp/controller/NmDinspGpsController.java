package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspGpsMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspGpsService;
import com.hualu.app.module.mems.nminsp.util.H_RabbitHelper;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新版日常巡查轨迹 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Api(tags = "新版日常巡查轨迹",description = "新版日常巡查轨迹 前端控制器")
@RestController
@RequestMapping("/nmDinspGps")
public class NmDinspGpsController extends M_MyBatisController<NmDinspGps,NmDinspGpsMapper>{

    @Autowired
    private NmDinspGpsService service;

    /**
     * 批量保存GPS数据
     * @param gpsList
     * @return
     */
    @ApiRegister(value = "nmDinspGps:saveBatchGps",businessType = BusinessType.UPDATE)
    @ApiOperation("批量保存GPS数据")
    @PostMapping("saveBatchGps")
    @Deprecated
    public RestResult<String> saveBatchGps(@RequestBody List<NmDinspGps> gpsList) {
        return saveBatchGps1(gpsList);
    }

    /**
     * 批量保存GPS数据
     * @param gpsList
     * @return
     */
    @ApiRegister(value = "nmDinspGps:saveBatchGps",businessType = BusinessType.UPDATE)
    @ApiOperation("批量保存GPS数据")
    @PostMapping("saveBatchGps1")
    public RestResult<String> saveBatchGps1(@RequestBody List<NmDinspGps> gpsList) {
        service.saveNmDinspGpsData(gpsList);
        return RestResult.success("操作成功");
    }

    /**
     * 获取轨迹数据集合
     * @param finspId 经常检查单ID
     * @return
     */
    @ApiOperation("获取轨迹数据集合")
    @GetMapping("getGpsByFinspId")
    public RestResult<List<NmDinspGps>> getGpsByFinspId(String finspId) {
        List<NmDinspGps> list = service.getGpsByDinspIdParallel(finspId);
        return RestResult.success(list);
    }

    /**
     * 获取实时位置信息
     * @param dinspId 经常检查单ID
     * @return
     */
    @GetMapping("getRealTimeLocationByFinspId")
    public RestResult<NmDinspGps> getRealTimeLocationByFinspId(String dinspId) {
        NmDinspGps one = baseMapper.getRealTimeLocationByDinspId(dinspId);
        return RestResult.success(one);
    }
}