package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.dto.NmDinspFacilityStat;
import com.hualu.app.module.mems.nminsp.entity.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <p>
 * 新版日常巡查 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface NmDinspService extends IService<NmDinsp> {


    List<NmDinsp> listDinsp(Set<String> dinspIdSet);

    List<NmDinsp> listDinspIdAndInspFrequency(List<String> dinspIds);

    List<NmDinsp> listDinsp(NmDinspCommon dinspCommon);


    List<NmDinsp> listStructId(NmDinspCommon dinspCommon,String facilityCat);

    /**
     * 根据流程ID，获取日常巡查单
     * @param processInstId
     * @return
     */
    List<NmDinsp> getNmDinspByProcessInstId(Long processInstId);


    /**
     * 修改日常巡查单审批状态
     * @param processInstId
     * @param status
     */
    void updateNmDinspStatus(long processInstId, int status);


    /**
     * 更新病害数量
     * @param dinspId
     */
    void updateDssNum(String dinspId);

    /**
     * 获取下张检查单号
     * @param facilityCat
     * @param orgEn
     * @return
     */
    String getNextCode(String facilityCat, String orgEn,LocalDate localDate);


    public String buildDinspCode(String facilityCat, String orgEn,int serial,LocalDate localDate);


    public Integer getNextSerial(String facilityCat, String orgEn,LocalDate localDate);

    /**
     * 初始化巡查单，用于新建单回显默认数据
     * @param facilityCat
     * @param orgIdString
     * @param code
     * @return
     */
    NmDinsp initNmDinsp(String facilityCat, String orgIdString,String code);

    /**
     * 添加或修改日常巡查单
     * @param nmDinsp
     */
    void saveOrUpdateNmDinsp(NmDinsp nmDinsp);

    /**
     * 批量新建巡查单（结构物）
     * @param baseStructDtos 结构物ID集合
     * @param commonDinsp 公用属性
     * @param isMq  true:mq异步执行 false:同步执行
     */
    void createEmptyNmDinspByStruct(List<BaseStructDto> baseStructDtos, NmDinsp commonDinsp,boolean isMq);

    /**
     * 批量新建路面和交安巡查单
     * @param commonDinsp
     */
    void createEmpTyNmDinspByLMAndJA(NmDinsp commonDinsp,boolean isMq);
    /**
     * 批量删除巡查单
     * @param inspIds
     */
    void delBatch(List<String> inspIds);

    /**
     * 导出存储数据
     * @param dinspIds
     * @param facilityCat
     * @return
     */
    DownloadWordDto exportWord(String dinspIds, String facilityCat);

    /**
     * 查询巡查情况
     * @param orgCode
     * @param year
     * @return
     */
    List<NmDinspSituationShow> querySituationShow(String orgCode, int year);

    List<String> getDssInfoImageExport(String dinspIds, String facilityCat, String path);

    IPage<RouteInspection> DailyInspectionLedger(IPage page,
                                                 String dinspCode,
                                                 String status,
                                                 String lineCode,
                                                 String startStake,
                                                 String endStake,
                                                 String startDate,
                                                 String endDate,
                                                 Map reqParam);

    void exportDailyInspection(HttpServletResponse response,
                               IPage page,
                               String dinspCode,
                               String status,
                               String lineCode,
                               String startStake,
                               String endStake,
                               String startDate,
                               String endDate,
                               Map reqParam);

    /**
     * 根据主单ID,查询对应的流程是否多个公用，如果公用时，新建一个流程
     * @param dinspId
     */
    void createProcess(String dinspId);

    /**
     * 批量生成检查单，只支持结构物
     * @param nmDinsp
     * @param isMq  true:mq异步执行 false:同步执行
     */
    void saveBatchNmDinsp(NmDinsp nmDinsp,boolean isMq);

    /**
     * 新建巡查单
     * @param dinspCommon
     * @return
     */
    NmDinsp createDinsp(NmDinspCommon dinspCommon);


    /**
     * 当前巡查单未绑定的数据，与公共单实现绑定
     * @param dinspCommon
     */
    void bindNoDinspCommonId(NmDinspCommon dinspCommon);

    /**
     * 查询流程待办
     * @param commonDinspId
     * @return
     */
    Map<Integer, List<Long>> listProcessTask(String commonDinspId,String facilityCat);

    Map<Integer,List<Long>> listProcessTask(String commonDinspId,String facilityCat,String processInstId);

    /**
     * 根据公用主单移除日常巡查单（流程不删除）
     * @param commonDinspIds
     */
    void removeByCommonDinspIds(List<String> commonDinspIds);

    List<NmDinspFacilityStat> getNmDinspFacilityStat(String commonDinspId, String status);

    IPage<SlopeInspectionRecord> findBpDailyRecord(IPage page,
                                                   String dinspCode,
                                                   String status,
                                                   String lineCode,
                                                   String hasDssStatus,
                                                   String startDate,
                                                   String endDate);

    void findBpDailyRecord(HttpServletResponse response,
                           IPage page,
                           String dinspCode,
                           String status,
                           String lineCode,
                           String hasDssStatus,
                           String startDate,
                           String endDate);

    void findDinspCheckSum(HttpServletResponse response, String startDate, String endDate);
}
