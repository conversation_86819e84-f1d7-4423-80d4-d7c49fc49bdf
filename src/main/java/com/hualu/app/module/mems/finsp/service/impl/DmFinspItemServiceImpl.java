package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.mapper.DmFinspItemMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class DmFinspItemServiceImpl extends ServiceImpl<DmFinspItemMapper, DmFinspItem> implements DmFinspItemService {

    @Override
    public List<DmFinspItem> queryItems(DmFinsp dmFinsp,String mntOrgId) {

        LambdaQueryWrapper<DmFinspItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspItem::getFacilityCat,dmFinsp.getFacilityCat());
        queryWrapper.eq(DmFinspItem::getIsDelete,0);
        if (StrUtil.isNotBlank(dmFinsp.getFinVersion())){
            queryWrapper.eq(DmFinspItem::getFinVersion,dmFinsp.getFinVersion());
        }

        //先判断路段公司设置的检查项
        LambdaQueryWrapper<DmFinspItem> mngWrapper = ObjectUtil.clone(queryWrapper);
        mngWrapper.eq(DmFinspItem::getMntOrgId,mntOrgId);

        List<DmFinspItem> mngItems = list(mngWrapper);
        //查询集团设置的检查项
        if (CollectionUtil.isEmpty(mngItems)){
            LambdaQueryWrapper<DmFinspItem> jtWrapper = ObjectUtil.clone(queryWrapper);
            jtWrapper.eq(DmFinspItem::getMntOrgId,"N000001");
            mngItems = list(jtWrapper);
        }
        return mngItems;
    }
}
