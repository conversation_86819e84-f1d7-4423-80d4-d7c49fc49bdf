package com.hualu.app.module.mems.dss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;

import java.util.List;

/**
 * <p>
 * 病害类型严重程度关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public interface DsstypeDssdegreeService extends IService<DsstypeDssdegree> {

    List<DsstypeDssdegree> selectDegressByDssType(String dssType);

    /**
     * 查询严重程度名称
     * @param dssType
     * @return
     */
    String getDssdegressName(String dssType, String dssDegree);
}
