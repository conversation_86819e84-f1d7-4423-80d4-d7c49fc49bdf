package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.basedata.service.TClvrtClvrtrecogService;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.service.*;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import com.tg.dev.api.util.hp.H_CValidator;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.StringUtils;

@Service("hdStructImp")
public class HdStructImp implements IStruct {

  @Autowired
  BaseRouteLogicService routeLogicService;

  @Autowired
  private NmInspContentService service;

  private static final String OTHER = "OTHER";

  private static final String ISSUE_DESC_FORMAT = "{}存在{}，{}";
  @Autowired
  NmDinspResultService dmResultService;

  @Autowired
  NmDinspRecordService dmRecordService;

  @Autowired
  NmFinspResultService finspResultService;

  @Autowired
  NmFinspRecordService finspRecordService;

  @Autowired
  TClvrtClvrtrecogService tClvrtClvrtrecogService;

  private static final String FAC_HD = "HD";
  private static final String XC_TYPE_FM = "FM";

  @Override
  public void validateNmDinsp(NmDinsp nmDinsp) {
    H_CValidator.validator2Exception(nmDinsp, new Class[] { StructGroup.class });
    // 根据结构物ID,设置结构物名称
    BaseStructDto structInfo = getStructInfo(nmDinsp.getFacilityCat(), nmDinsp.getStructId());
    nmDinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());
    BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmDinsp.getRouteCode());
    if (routeLogic != null) {
      nmDinsp.setRouteName(routeLogic.getRouteName());
    }
  }

  @Override public void validateNmFinsp(NmFinsp nmFinsp) {
    H_CValidator.validator2Exception(nmFinsp, new Class[] { StructGroup.class });
    // 根据结构物ID,设置结构物名称
    BaseStructDto structInfo = getStructInfo(nmFinsp.getFacilityCat(), nmFinsp.getStructId());
    nmFinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());
    BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmFinsp.getRouteCode());
    if (routeLogic != null) {
      nmFinsp.setRouteName(routeLogic.getRouteName());
    }
  }

  @Override
  public void refreshNmDinspResult(NmDinsp nmDinsp) {
    // 路面无检查结论
  }

  @Override
  public void refreshNmFinspResult(NmFinsp nmFinsp) {
    List<NmFinspResult> nmFinspResult = finspResultService.createNmFinspResult(nmFinsp);
    List<NmFinspRecord> nmFinspRecords = finspRecordService.selectRecordByFinspId(nmFinsp.getFinspId());
    finspRecordService.showView(nmFinspRecords,nmFinsp);
    if (CollectionUtil.isEmpty(nmFinspResult) && CollectionUtil.isEmpty(nmFinspRecords)) {
      if (CollectionUtil.isNotEmpty(nmFinspResult)){
        //清空结论
        finspResultService.updateBatchById(nmFinspResult);
      }
      return;
    }
    // 根据部件进行分组
    Map<String, List<NmFinspRecord>> partMap = nmFinspRecords.stream().filter(e-> StrUtil.isNotBlank(e.getStructPartId()))
        .collect(Collectors.groupingBy(NmFinspRecord::getStructPartId));
    for (NmFinspResult item : nmFinspResult) {
      if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())){
        continue;
      }
      // partId会存在多个
      List<String> partIds = StrUtil.split(item.getPartId(), ",");
      List<NmFinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
      if (CollectionUtil.isNotEmpty(dssList)) {
        //经常检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
        List<NmFinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(containsDssList)) {
          //String issueDesc = containsDssList.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
          item.setIssueDesc(getIssueDescByFm(containsDssList));
          // 移除已经匹配的病害
          nmFinspRecords.removeAll(dssList);
        }
      }
    }
    // 获取其他项结论
    NmFinspResult otherResult = nmFinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
    if (otherResult != null && CollectionUtil.isNotEmpty(nmFinspRecords)) {
      //String issueDesc = nmFinspRecords.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
      otherResult.setIssueDesc(getIssueDescByFm(nmFinspRecords));
    }
    finspResultService.updateBatchById(nmFinspResult);
  }

  private String getIssueDescByFm(List<NmFinspRecord> dssRecords) {
    if (CollectionUtil.isEmpty(dssRecords)) {
      return null;
    }
    List<String> issueList = Lists.newArrayList();
    dssRecords.forEach(e -> {
      String format = StrUtil.format(ISSUE_DESC_FORMAT, e.getStructPartName(),e.getDssTypeName(), e.getDssNum());
      issueList.add(format);
    });
    return issueList.stream().collect(Collectors.joining("；"));
  }

  @Override public void showNmFinspView(List<NmFinspRecord> nmFinspRecords) {
    List<NmInspContent> contents = service.listByContent(FAC_HD, XC_TYPE_FM);
    if (CollectionUtils.isEmpty(contents) || CollectionUtils.isEmpty(nmFinspRecords)) {
      return;
    }
    Map<String, String> partNameMap = contents.stream()
        .collect(Collectors.toMap(NmInspContent::getId, NmInspContent::getName));

    for (NmFinspRecord item : nmFinspRecords) {
      item.setStructPartName(partNameMap.getOrDefault(item.getStructPartId(), ""));
    }
  }

  @Override
  public List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps, String parentPath) {
    if (!CollectionUtil.isEmpty(nmFinsps)) {
      List<String> culvertIds =
          nmFinsps.stream().map(NmFinsp::getStructId).collect(Collectors.toList());
      List<TClvrtClvrtrecog> culvertList = tClvrtClvrtrecogService.getCulvertTypeAndPlace(culvertIds);
      if (!CollectionUtils.isEmpty(culvertList)) {
        Map<String, TClvrtClvrtrecog> culvertIdMap = culvertList.stream()
            .collect(Collectors.toMap(TClvrtClvrtrecog::getClvrtrecogId, Function.identity(),(newValue,oldValue)->newValue));

        for (NmFinsp nmFinsp : nmFinsps) {
          String structId = nmFinsp.getStructId();
          if (!StringUtils.hasText(structId)) {
            continue;
          }

          TClvrtClvrtrecog t = culvertIdMap.get(structId);
          if (t == null) {
            continue;
          }

          nmFinsp.setAreaCode(t.getPlace());
          nmFinsp.setCulvertType(t.getCulvertType());
          nmFinsp.setDisplayStake(t.getLogicCntrStake());
        }
      }
    }
    return H_WordHelper.processAndExportWord(
            true,
        nmFinsps,
        parentPath,
        "FM",
        NmFinsp::getFacilityCat,
        NmFinsp::getInspFrequency,
        NmFinsp::getFinspId,
        NmFinsp::getFinspCode,
        NmFinsp::getInspDate,
        finspIds -> finspResultService.listResultsByFinspIds(finspIds,"HD"),
        NmFinspResult::getFinspId,
        (records, entity) -> {
          // 设置模版显示序号
          AtomicInteger atomicInteger =new AtomicInteger(1);
          records.forEach(record -> {
            record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
          });
        }
    );
  }
}
