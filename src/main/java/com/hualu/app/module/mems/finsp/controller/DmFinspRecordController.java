package com.hualu.app.module.mems.finsp.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.service.DmFinspRecordService;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.mybatisplus.service.PageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.util.List;


@Api(tags = "经常检查_病害接口")
@RestController
@RequestMapping("/dmFinspRecord")
public class DmFinspRecordController {

    @Autowired
    DmFinspRecordService recordService;

    @Autowired
    DssImageService imageService;

    @Autowired
    PageService pageService;

    /**
     * 删除病害照片
     * @param fileId
     * @return
     */
    @GetMapping("delImage")
    public RestResult<String> deleteImage(String fileId){
        imageService.delByFileId(fileId);
        return RestResult.success("操作成功");
    }

    /**
     * 查询结构物养护历史
     * @param structId 结构物ID
     * @param facilityCat 结构物类型
     * @return
     */
    public RestResult<List<DmFinspRecord>> selectHisRecordByStructId(String structId,String facilityCat){
        IPage page = recordService.selectHisRecordByStructId(structId,facilityCat);
        return H_PageHelper.getPageResult(H_PageHelper.getPageResult(page));
    }

    /**
     * 删除经常检查病害
     * @param ids 病害ID，多个病害以逗号分隔（例如：xx1,xx2,xx3）
     * @return
     */
    @PostMapping("deleteFinspRecordByIds")
    public RestResult<String> deleteFinspRecordByIds(@NotBlank @RequestParam("ids") String ids){
        recordService.deleteFinspRecordByIds(ids.split(","));
        return RestResult.success("操作成功");
    }

    @ApiOperation("查询经常检查单病害")
    @GetMapping("loadDmFinspRecordByFinspId/{finspId}")
    public RestResult<List<DmFinspRecord>> loadDmFinspRecordByFinspId(@PathVariable("finspId") String finspId){
        IPage iPage = recordService.selectViewRecordByFinspId(finspId);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }


    @ApiOperation("添加或者修改病害")
    @PostMapping(value="/saveOrUpdateDmFinspRecord")
    public RestResult<DmFinspRecord> saveOrUpdateDmFinspRecord(@RequestParam("data") String data,@RequestParam(value = "files",required = false) MultipartFile[] files){
        DmFinspRecord dmFinspRecord = JSONUtil.toBean(data,DmFinspRecord.class);
        H_CValidator.validator2Exception(dmFinspRecord);
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            dmFinspRecord.setFileIds(StrUtil.join(",",fileIds));
        }else {
            //解决图片重复的问题
            dmFinspRecord.setFileIds(null);
        }
        if("4".equals(dmFinspRecord.getLineDirect())){
            dmFinspRecord.setStake(dmFinspRecord.getRstake());
        }
        if (StrUtil.isNotBlank(dmFinspRecord.getDssTypeQ())){
            dmFinspRecord.setDssType(dmFinspRecord.getDssTypeQ());
        }
        if (StrUtil.isNotBlank(dmFinspRecord.getDssDescQ())){
            dmFinspRecord.setDssDesc(dmFinspRecord.getDssDescQ());
        }
        DmFinspRecord dbrecord = recordService.saveOrUpdateFinspRecord(dmFinspRecord);
        return RestResult.success(dbrecord);
    }
}
