package com.hualu.app.module.mems.autoInspector.entity;

import java.math.BigDecimal;

public class DownloadVo {

    private String fileId;

    private String fileName;

    private String dssType;

    private BigDecimal stake;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDssType() {
        return dssType;
    }

    public void setDssType(String dssType) {
        this.dssType = dssType;
    }

    public BigDecimal getStake() {
        return stake;
    }

    public void setStake(BigDecimal stake) {
        this.stake = stake;
    }
}
