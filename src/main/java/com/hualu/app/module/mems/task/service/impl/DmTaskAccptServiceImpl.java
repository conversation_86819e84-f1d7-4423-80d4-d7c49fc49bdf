package com.hualu.app.module.mems.task.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccpt;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import com.hualu.app.module.mems.task.mapper.DmTaskAccptMapper;
import com.hualu.app.module.mems.task.service.DmTaskAccptDetailService;
import com.hualu.app.module.mems.task.service.DmTaskAccptService;
import com.hualu.app.module.mems.task.service.DmTaskService;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 维修验收单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Service
public class DmTaskAccptServiceImpl extends ServiceImpl<DmTaskAccptMapper, DmTaskAccpt> implements DmTaskAccptService, IWorkItemEventHandler, OrderInfoHandler {

    private String YSDCODE = "YSD-{}-{}";

    @Autowired
    DmTaskAccptDetailService detailService;

    @Autowired
    DssInfoService dssInfoService;

    @Autowired
    DmTaskService taskService;

    @Override
    public boolean isClear(String mtaskAccptId) {
        DmTaskAccpt item = getById(mtaskAccptId);
        if (item != null && item.getIsClear() == 1) {
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delAccpt(String mtaskAccptId) {

        LambdaQueryWrapper<DmTaskAccptDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTaskAccptDetail::getMtaskAccptId,mtaskAccptId).groupBy(DmTaskAccptDetail::getMtaskId,DmTaskAccptDetail::getDssId);
        queryWrapper.select(DmTaskAccptDetail::getMtaskId,DmTaskAccptDetail::getDssId);
        List<DmTaskAccptDetail> accptDetails = detailService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(accptDetails)){
            Set<String> mtaskIds = accptDetails.stream().map(DmTaskAccptDetail::getMtaskId).collect(Collectors.toSet());
            mtaskIds.forEach(id->{
                //修改任务单状态
                taskService.updateDmtaskStatus(id,2);
            });
            //todo 修改病害维修状态 任务单重新申请验收时，会自动调整该病害状态
            //Set<String> dssIds = accptDetails.stream().map(DmTaskAccptDetail::getDssId).collect(Collectors.toSet());
            //dssInfoService.updateRepairStatus(Lists.newArrayList(dssIds),0);
        }
        //删除验收单详情
        LambdaQueryWrapper<DmTaskAccptDetail> delWrapper = new LambdaQueryWrapper<>();
        delWrapper.eq(DmTaskAccptDetail::getMtaskAccptId,mtaskAccptId);
        detailService.remove(delWrapper);
        //删除验收单
        removeById(mtaskAccptId);
    }

    @Override
    public void saveOrUpdateAccpt(DmTaskAccpt dmTaskAccpt) {
        dmTaskAccpt.setIsInTime(3);
        dmTaskAccpt.setMtaskAccptId(H_KeyWorker.nextIdToString());
        List<String> accptIds = dmTaskAccpt.getMtaskdtlIds();
        List<DmTaskAccptDetail> accptDetails = detailService.selectByMtaskDtlId(accptIds);
        //验收单的明细都是从维修工程记录来，所以无需重新生成，直接设置验收单ID和任务单即可
        if (CollectionUtil.isNotEmpty(accptDetails)){
            accptDetails.forEach(item->{
                item.setAcceptStatus(1);
                item.setApplyAmount(item.getMpitemNum());
                item.setAcceptAmount(item.getMpitemNum());
                item.setRepairDate(LocalDateTime.now());
                item.setMtaskAccptId(dmTaskAccpt.getMtaskAccptId());
            });
        }else {
            //由于纯在旧版数据，所以需要根据任务单办结重新生成一下维修工程记录
            taskService.finshDmTask(dmTaskAccpt.getMtaskId(),true,LocalDate.now());
            accptDetails = detailService.selectByMtaskDtlId(accptIds);
            accptDetails.forEach(item->{
                item.setAcceptStatus(1);
                item.setApplyAmount(item.getMpitemNum());
                item.setAcceptAmount(item.getMpitemNum());
                if (item.getRepairDate() == null){
                    item.setRepairDate(LocalDateTime.now());
                }
                item.setMtaskAccptId(dmTaskAccpt.getMtaskAccptId());
            });
        }

        if (CollectionUtil.isNotEmpty(accptDetails)){
            detailService.updateBatchById(accptDetails);
        }
        dmTaskAccpt.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
        saveOrUpdate(dmTaskAccpt);
        taskService.updateDmtaskStatus(dmTaskAccpt.getMtaskId(),3);
    }

    @Override
    public List<DmTaskAccptDetailDto> selectAccptDetails(String mtaskId, String mtaskdtlIds) {
        List<Map<String,String>> facilityCats = baseMapper.selectFacilityCat(mtaskId);
        List<DmTaskAccptDetailDto> allDtos = Lists.newArrayList();
        facilityCats.forEach(item->{
            String facilityCat = item.get("FACILITY_CAT");
            List<DmTaskAccptDetailDto> detailDtos = baseMapper.selectAccptDetails(facilityCat,mtaskId);
            allDtos.addAll(detailDtos);
        });
        return allDtos;
    }

    @Override
    public DmTaskAccpt getNextCode() {
        String orgId = CustomRequestContextHolder.getOrgIdString();
        String orgEn = CustomRequestContextHolder.get("ORG_EN") == null ? "" : CustomRequestContextHolder.get("ORG_EN").toString();
        //任务单前缀
        String codePreLike = StrUtil.format(YSDCODE,orgEn, DateUtil.format(new Date(),"yyyyMM"));
        String taskCode = StrUtil.format(YSDCODE,orgEn,DateUtil.format(new Date(),"yyyyMMdd"));
        synchronized (codePreLike){
            Integer num = baseMapper.selectNextCode(codePreLike,orgId);
            String code = num == null ? "1" : ++num + "";
            String suffix = StrUtil.padPre(code, 3, "0");
            taskCode+="-"+suffix;
        }
        DmTaskAccpt dto = new DmTaskAccpt();
        dto.setMtaskAccptCode(taskCode);
        dto.setIsInTimeName("1");
        dto.setApplyUser(CustomRequestContextHolder.getUserName());
        dto.setApplyDate(LocalDate.now());
        //获取上一张单信息
        DmTaskAccpt accpt = baseMapper.selectLast(orgId);
        if (accpt != null){
            dto.setMntnOrgName(accpt.getMntnOrgName());
            dto.setMntnDeptNm(accpt.getMntnDeptNm());
            dto.setApplyUser(CustomRequestContextHolder.getUserName());
        }
        return dto;
    }

    /**
     * 获取任务单数据
     * @param proInstId
     * @return
     */
    private DmTaskAccpt selectDmTaskAccptByProInstId(Long proInstId){
        LambdaQueryWrapper<DmTaskAccpt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTaskAccpt::getProcessinstid,proInstId);
        return getOne(queryWrapper);
    }


    /**
     * 修改验收单状态
     * @param accptId
     * @param status
     */
    private void updateDmTaskAccptStatus(String accptId,Integer status){
        DmTaskAccpt accpt = new DmTaskAccpt();
        accpt.setMtaskAccptId(accptId);
        accpt.setStatus(status);
        updateById(accpt);
    }

    @Override
    public Map<String, String> initAction() {
        return null;
    }

    @Override
    public String getProcessDefName() {
        return "gdcg.emdc.mems.dm.DmTaskAcceptWorkFlow";
    }

    @Override
    public void savedoEvent(long processInstId, boolean isEnd, String nextAction, String actId) {
        DmTaskAccpt dmTaskAccpt = selectDmTaskAccptByProInstId(processInstId);
        if (dmTaskAccpt == null){
            throw new BaseException(processInstId+"未匹配验收单");
        }
        //办结
        if (isEnd){
            List<DmTaskAccptDetail> accptDetails = detailService.selectByDmTaskAccptId(dmTaskAccpt.getMtaskAccptId());
            if (CollectionUtil.isNotEmpty(accptDetails)){
                Set<String> dssIds = accptDetails.stream().filter(e -> StrUtil.isNotBlank(e.getDssId())).map(DmTaskAccptDetail::getDssId).collect(Collectors.toSet());
                //修改病害验收状态
                dssInfoService.updateRepairStatus(Lists.newArrayList(dssIds),2);
                //修改任务单状态
                updateDmTaskStatus(dmTaskAccpt.getMtaskId(), 5);
                //修改验收单状态
                updateDmTaskAccptStatus(dmTaskAccpt.getMtaskAccptId(), 3);
            }
            return;
        }

        if (nextAction.equals("复核")) {
            updateDmTaskStatus(dmTaskAccpt.getMtaskId(), 7);
            updateDmTaskAccptStatus(dmTaskAccpt.getMtaskAccptId(), 1);
        } else if (nextAction.equals("审核")) {
            updateDmTaskAccptStatus(dmTaskAccpt.getMtaskAccptId(), 2);
        } else if (nextAction.equals("退回")) {
            updateDmTaskAccptStatus(dmTaskAccpt.getMtaskAccptId(), -1);
        }
    }


    private void updateDmTaskStatus(String mtaskId,Integer status){
        if (StrUtil.isBlank(mtaskId)){
            return;
        }
        //修改任务单状态
        taskService.updateDmtaskStatus(mtaskId,status);
    }

    @Override
    public List<OrderInfo> queryOrderInfo(long processInstId) {
        LambdaQueryWrapper<DmTaskAccpt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTaskAccpt::getProcessinstid,processInstId);
        DmTaskAccpt accpt = getOne(queryWrapper);
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderCode(accpt.getMtaskAccptCode());
        orderInfo.setOrderType("YSD");
        orderInfo.setOrderId(accpt.getMtaskAccptId());
        orderInfo.setStatus(accpt.getStatus());
        orderInfo.setOrgCode(accpt.getMntOrgId());
        return Arrays.asList(orderInfo);
    }
}
