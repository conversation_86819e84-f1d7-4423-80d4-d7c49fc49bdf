package com.hualu.app.module.mems.task.dto.taskdetail;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class DmTaskMpItemDto implements Serializable {

    //措施ID
    @NotBlank
    private String mpitemId;
    //单位
    @NotBlank
    private String measureUnit;
    //勘估数量
    @NotBlank
    private Double mpitemAccount;
    //是否包干 0:否 1：是
    private Integer isLump;
    //备注
    private String remark;
}
