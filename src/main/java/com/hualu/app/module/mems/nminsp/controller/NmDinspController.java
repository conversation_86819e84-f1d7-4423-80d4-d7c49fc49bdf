package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.NmDinspStsTreeDto;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.dto.NmDinspFacilityStat;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.RouteInspection;
import com.hualu.app.module.mems.nminsp.entity.SlopeInspectionRecord;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.controller.WfworkitemController;
import com.hualu.app.module.workflow.dto.WorkActDto;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.io.FileUtil;
import lombok.SneakyThrows;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * 新版日常巡查 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Validated
@Api(tags = "日常巡查", description = "新版日常巡查 前端控制器")
@RestController
@RequestMapping("/nmDinsp")
public class NmDinspController extends M_MyBatisController<NmDinsp, NmDinspMapper> {

    @Autowired
    private NmDinspService service;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    WfworkitemController wfworkitemController;

    @Value("${fileReposity}")
    String fileReposity;


    /**
     * 分页查询
     *
     * @return
     */
    @ApiRegister(value = "nmDinsp:stsList", businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("stsList")
    public RestResult<List<NmDinspStsTreeDto>> stsList(String facilityCat, String status, String dinspCode, String hasDssStatus, String inspectionDate) {
        Set<String> selectOrgIds = H_DataAuthHelper.selectOrgIds();
        String orgIds = String.join(",", selectOrgIds);


        int structNum = 0;
        if ("QL".equals(facilityCat)) {
            structNum = this.baseMapper.getBridgeNum(orgIds);
        } else if ("SD".equals(facilityCat)) {
            structNum = this.baseMapper.getTunnelNum(orgIds);
        } else if ("BP".equals(facilityCat)) {
            structNum = this.baseMapper.getSlopeNum(orgIds);
        }
        int monthNumber = LocalDate.now().getMonthValue();
        int year = Year.now().getValue();
        int dayNumber = YearMonth.now().lengthOfMonth();
        List<NmDinspStsTreeDto> nmDinspStsTreeDtos = new ArrayList<>();
        String startDate = null;
        String endDate = null;
        if (StringUtil.isNullOrEmpty(inspectionDate)) {
            startDate = year + "-" + (monthNumber > 9 ? monthNumber : "0" + monthNumber) + "-01";
            endDate = year + "-" + (monthNumber > 9 ? monthNumber : "0" + monthNumber) + "-" + dayNumber;
            for (int i = 1; i <= dayNumber; i++) {
                NmDinspStsTreeDto n = new NmDinspStsTreeDto(year, monthNumber + "." + (i > 9 ? i : "0" + i), 0, 0, 0);
                n.setStructNum(structNum);
                nmDinspStsTreeDtos.add(n);
            }
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter formatterResult = DateTimeFormatter.ofPattern("M.dd");
            String[] split = inspectionDate.split(",");
            startDate = split[0];
            endDate = split[1];
            LocalDate date1 = LocalDate.parse(startDate, formatter);
            LocalDate date2 = LocalDate.parse(endDate, formatter);
            long daysBetween = ChronoUnit.DAYS.between(date1, date2);
            dayNumber = (int) Math.abs(daysBetween) + 1;
            List<String> dates = LongStream.range(0, dayNumber)
                    .mapToObj(date1::plusDays)
                    .map(date -> date.format(formatterResult))
                    .collect(Collectors.toList());
            for (String date : dates) {
                NmDinspStsTreeDto n = new NmDinspStsTreeDto(year, date, 0, 0, 0);
                n.setStructNum(structNum);
                nmDinspStsTreeDtos.add(n);
            }
        }

        List<NmDinspStsTreeDto> results = this.baseMapper.selectStsList(orgIds, facilityCat, startDate, endDate, status, dinspCode, hasDssStatus);
        if (results != null) {
            for (NmDinspStsTreeDto n : nmDinspStsTreeDtos) {
                for (NmDinspStsTreeDto r : results) {
                    if (n.getMonth().equals(r.getMonth())) {
                        n.setNum(r.getNum());
                        n.setDis(r.getDis());
                        n.setCheckNum(r.getCheckNum());
                    }
                }
            }
        }
        for (NmDinspStsTreeDto n : nmDinspStsTreeDtos) {
            n.setNum(n.getCheckNum() + "/" + n.getStructNum());
        }
        return RestResult.success(nmDinspStsTreeDtos);
    }

    /**
     * 分页查询
     *
     * @return
     */
    @ApiRegister(value = "nmDinsp:page", businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    @Override
    public RestResult<List<NmDinsp>> selectPage() {
        Map reqParam = this.getReqParam();
        Boolean isEmptyId = Optional.ofNullable(reqParam).filter(m -> m.containsKey("dinspId"))
                .map(m -> m.get("dinspId"))
                .map(value -> {
                    if (value instanceof String) {
                        return ((String) value).trim().isEmpty();
                    }
                    return value == null;
                }).orElse(true);
        if (!isEmptyId) {
            Object finspId = reqParam.get("dinspId");
            reqParam = new HashMap();
            reqParam.put("dinspId", finspId);
        }
        QueryWrapper queryWrapper = this.initQueryWrapper(reqParam);
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);

        List<NmDinsp> records = iPage.getRecords();
        List<BaseDatathirdDic> dics = dicService.listDicByItem("WEATHER");
        records.forEach(record -> {
            String weatherName = dicService.getDicName(dics, record.getWeather());
            record.setWeatherName(weatherName);
        });
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    @Override
    protected QueryWrapper initQueryWrapper(Map params) {
        QueryWrapper<NmDinsp> queryWrapper = new QueryWrapper();
        initQueryParam(queryWrapper, params);
        H_BatisQuery.setFieldValue2In(queryWrapper, params, this.getEntityClass());
        return queryWrapper;
    }

    private void initQueryParam(QueryWrapper<NmDinsp> queryWrapper, Map params) {
        // 查询有无病害
        Object hasDss = params.getOrDefault("hasDssStatus", "3");
        if (hasDss.equals("0")) {
            queryWrapper.eq("dss_Num", 0);
        } else if (hasDss.equals("1")) {
            queryWrapper.ge("dss_Num", 1);
        }

        Object startDate = params.get("startDate");
        if (!StrUtil.isBlankIfStr(startDate)) {
            queryWrapper.apply("insp_date >= to_date({0},'YYYY-MM-dd')", startDate);
            params.remove("startDate");
        }

        Object endDate = params.get("endDate");
        if (!StrUtil.isBlankIfStr(endDate)) {
            queryWrapper.apply("insp_date <= to_date('" + endDate + " 23:59:59','YYYY-MM-dd HH24:mi:ss')");
            params.remove("endDate");
        }

        String status = params.getOrDefault("status", "").toString();
        if (!"0".equals(status)) {
            queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());
        }
        String taskSql = H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.valueOf(status), "nm_dinsp");

        if (StrUtil.isNotBlank(taskSql)) {
            queryWrapper.exists(taskSql);
        }
        params.remove("status");
        if (params.get("sortField") == null && params.get("sortOrder") == null) {
            queryWrapper.lambda().orderByDesc(NmDinsp::getUpdateTime);
        }
    }

    /**
     * 批量生成结构物检查单（多个结构物以逗号分隔赋值到structId中）
     *
     * @param nmDinsp
     * @return
     */

    @PostMapping("saveBatchNmDinsp")
    public RestResult<String> saveBatchNmDinsp(@RequestBody @Validated(StructGroup.class) NmDinsp nmDinsp) {
        service.saveBatchNmDinsp(nmDinsp, false);
        return RestResult.success("操作成功");
    }

    /**
     * 获取日常巡查单，按设施分类统计巡查的个数
     *
     * @param commonDinspId 公用单id
     * @param status        办理状态
     * @return
     */
    @GetMapping("/getNmDinspFacilityStat")
    public RestResult<List<NmDinspFacilityStat>> getNmDinspFacilityStat(String commonDinspId, String status) {
        return RestResult.success(service.getNmDinspFacilityStat(commonDinspId, status));
    }

    /**
     * 初始化巡查单，用于新建单回显默认数据
     *
     * @param facilityCat 设施类型
     * @return
     */
    @GetMapping("init")
    public RestResult<NmDinsp> initNmDinsp(@NotBlank String facilityCat, String rang) {
        NmDinsp nmDinsp = service.initNmDinsp(facilityCat, CustomRequestContextHolder.getOrgIdString(), rang);
        return RestResult.success(nmDinsp);
    }

    @ApiOperation("添加或修改检查单")
    @PostMapping(value = "/saveOrUpdate")
    public RestResult<NmDinsp> saveOrUpdate(@RequestBody @Validated NmDinsp nmDinsp) {
        service.saveOrUpdateNmDinsp(nmDinsp);
        return RestResult.success(nmDinsp);
    }

    /**
     * 批量删除巡查单
     *
     * @param dinspIds
     * @return
     */
    @PostMapping("delBatch")
    public RestResult<String> delBatch(@RequestParam(required = true) String dinspIds) {
        List<String> idList = StrUtil.split(dinspIds, ",");
        service.delBatch(idList);
        return RestResult.success("操作成功");
    }

    /**
     * 返回无病害的日常巡查单
     *
     * @return
     */
    @PostMapping("listNmDinspNoDss")
    public RestResult<List<NmDinsp>> listNmDinspNoDss(String facilityCat, Integer status) {
        Map reqParam = this.getReqParam();
        // 流程待办
        reqParam.put("status", 0);
        QueryWrapper queryWrapper = this.initQueryWrapper(reqParam);
        queryWrapper.eq(ObjectUtil.isNotNull(status), "status", status);
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 查询下一岗
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/loadNextActivitys")
    public RestResult<List<WorkActDto>> loadNextActivitys(@RequestParam(required = true) String facilityCat, @RequestParam(required = false) String processInstId) throws Exception {
        // 1.查询待办的流程实例
        Map<Integer, List<Long>> taskMap = service.listProcessTask(null, facilityCat, processInstId);
        // 流程创建待办
        List<Long> createTaskList = taskMap.get(0);
        // 业主审核待办
        List<Long> submitTaskList = taskMap.get(1);
        if (CollectionUtil.isNotEmpty(createTaskList)) {
            Long proInstId = createTaskList.get(0);
            List<WorkActDto> workActDtos = wfworkitemController.loadNextActivitys(proInstId).getData();
            workActDtos.forEach(workActDto -> {
                workActDto.setProcessInstId(proInstId);
            });
            return RestResult.success(workActDtos);
        }
        if (CollectionUtil.isNotEmpty(submitTaskList)) {
            Long proInstId = submitTaskList.get(0);
            List<WorkActDto> workActDtos = wfworkitemController.loadNextActivitys(proInstId).getData();
            workActDtos.forEach(workActDto -> {
                workActDto.setProcessInstId(proInstId);
            });
            return RestResult.success(workActDtos);
        }
        throw new BaseException("不存在待办项");
    }

    /**
     * 公用单批量办理
     *
     * @param processInstId 当前的流程实例ID
     * @param facilityCat   设施类型
     * @param nextAction    下一节点
     * @param nextUserIds   下一办理人
     * @param apprOpinion   审批意见
     * @param appvOpinion   审批意见
     * @return
     */
    @PostMapping("submitApprove")
    public RestResult<String> submitApprove(
            @RequestParam(required = true) long processInstId
            , @RequestParam(required = true) String facilityCat
            , String nextAction, String nextUserIds
            , @RequestParam(required = false) String apprOpinion
            , @RequestParam(required = false) String appvOpinion) {
        // 查询日常巡查待办任务
        Map<Integer, List<Long>> taskListMap = service.listProcessTask(null, facilityCat);
        // 当前流程是流程创建人待办
        boolean isCreateFlag = false;
        // 流程创建待办
        List<Long> createTaskList = taskListMap.get(0);
        if (CollectionUtil.isNotEmpty(createTaskList)) {
            isCreateFlag = createTaskList.contains(processInstId);
            String processIdStr = StrUtil.join(",", createTaskList);
            wfworkitemController.submitApprove(processIdStr, nextAction, nextUserIds, apprOpinion, appvOpinion);
        }
        // 业主审核待办（直接病害入库）
        List<Long> submitTaskList = taskListMap.get(1);
        if (CollectionUtil.isNotEmpty(submitTaskList)) {
            String processIdStr = StrUtil.join(",", submitTaskList);
            String tempNextAction = isCreateFlag ? "putDss" : nextAction;
            String tempNextUserIds = isCreateFlag ? null : nextUserIds;
            wfworkitemController.submitApprove(processIdStr, tempNextAction, tempNextUserIds, apprOpinion, appvOpinion);
        }
        return RestResult.success("办理成功");
    }

    /**
     * 导出检查单（单个导出一个word,多个导出压缩包）
     * @param dinspIds    检查单ID
     * @param facilityCat 设施类型
     * @return
     */
    @SneakyThrows
    @RequestMapping("exportWord")
    public void exportWord(@RequestParam(value = "dinspIds", required = true) String dinspIds,
                           @RequestParam(value = "facilityCat", required = true) String facilityCat,
                           @RequestParam(value = "exportPhoto", required = false) String exportPhoto,
                           HttpServletResponse response) {
        Objects.requireNonNull(dinspIds, "dinspIds不能为空");
        // 查询所有的主单信息，查询所有的病害信息
        DownloadWordDto wordDto = service.exportWord(dinspIds, facilityCat);
        if (!org.apache.commons.lang3.StringUtils.isBlank(exportPhoto)
                && "1".equals(exportPhoto)) {
            List<String> wordPaths = wordDto.getWordPaths();
            String s = wordPaths.stream().findFirst().get();
            List<String> dssInfoImageExport = service.getDssInfoImageExport(dinspIds, facilityCat, s);
            if (dssInfoImageExport != null && dssInfoImageExport.size() > 0) {
                wordPaths.addAll(dssInfoImageExport);
            }
        }
        H_WordHelper.download(wordDto, response);
    }

    /**
     * @param request
     * @return
     */
    @ApiOperation("病害台账")
    @PostMapping(value = "/DailyInspectionLedger")
    public RestResult<List<RouteInspection>> DailyInspectionLedger(HttpServletRequest request,
                                                                   @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String facilityCat = null;
        if (reqParam.containsKey("facilityCat")) {
            facilityCat = reqParam.get("facilityCat").toString();
        }

        String startStake = null;
        String endStake = null;
        System.out.println(reqParam);
        if (reqParam.containsKey("startStake")) {
            startStake = reqParam.get("startStake").toString();
        }
        if (reqParam.containsKey("endStake")) {
            endStake = reqParam.get("endStake").toString();
        }

        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(Long.parseLong(reqParam.get("pageIndex").toString()) + 1, Long.parseLong(reqParam.get("pageSize").toString()));
        IPage<RouteInspection> routeInspections = this.service.DailyInspectionLedger(page, dinspCode, facilityCat
                , lineCode, startStake,endStake, startDate, endDate, reqParam);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(routeInspections));
    }

    /**
     * @param response
     * @return
     */
    @ApiOperation("导出病害台账")
    @PostMapping(value = "/exportDailyInspection")
    public void exportDailyInspection(HttpServletResponse response,
                                      @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }

        String facilityCat = null;
        if (reqParam.containsKey("facilityCat")) {
            facilityCat = reqParam.get("facilityCat").toString();
        }

        String startStake = null;
        String endStake = null;
        if (reqParam.containsKey("startStake")) {
            startStake = reqParam.get("startStake").toString();
        }
        if (reqParam.containsKey("endStake")) {
            endStake = reqParam.get("endStake").toString();
        }

        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(1, Integer.MAX_VALUE);
        this.service.exportDailyInspection(response, page, dinspCode, facilityCat, lineCode, startStake, endStake, startDate, endDate, reqParam);
    }

    /**
     * pdf预览
     *
     * @param dinspIds
     * @param facilityCat
     * @param response
     */
    @SneakyThrows
    @PostMapping("exportPdf")
    public RestResult<String> exportPdf(@RequestParam(value = "dinspIds", required = true) String dinspIds,
                                        @RequestParam(value = "facilityCat", required = true) String facilityCat,
                                        HttpServletResponse response) {
        Objects.requireNonNull(dinspIds, "dinspIds不能为空");
        // 查询所有的主单信息，查询所有的病害信息
        DownloadWordDto wordDto = service.exportWord(dinspIds, facilityCat);
        String pdfPath = H_WordHelper.wordToPdf(wordDto, fileReposity);
        FileUtil.deleteDir(wordDto.getParentPath());
        return RestResult.success(pdfPath);
    }

    /**
     * @param request
     * @return
     */
    @ApiOperation("边坡台账")
    @PostMapping(value = "/findBpDailyRecord")
    public RestResult<List<RouteInspection>> findBpDailyRecord(HttpServletRequest request,
                                                               @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(Long.parseLong(reqParam.get("pageIndex").toString()) + 1, Long.parseLong(reqParam.get("pageSize").toString()));
        IPage<SlopeInspectionRecord> routeInspections = this.service.findBpDailyRecord(page, dinspCode, status, lineCode, hasDssStatus, startDate, endDate);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(routeInspections));
    }

    /**
     * @param response
     * @return
     */
    @ApiOperation("导出边坡台账")
    @PostMapping(value = "/exportfindBpDailyRecordSlope")
    public void exportfindBpDailyRecordSlope(HttpServletResponse response,
                                             @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(1, Integer.MAX_VALUE);
        this.service.findBpDailyRecord(response, page, dinspCode, status, lineCode, hasDssStatus, startDate, endDate);
    }

    /**
     * @param response
     * @return
     */
    @ApiOperation("日常巡查汇总表")
    @PostMapping(value = "/findDinspCheckSum")
    public void findDinspCheckSum(HttpServletResponse response, String startDate, String endDate)
    {
        this.service.findDinspCheckSum(response, startDate, endDate);
    }
}
