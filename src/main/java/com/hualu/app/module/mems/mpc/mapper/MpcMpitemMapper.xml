<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.mpc.mapper.MpcMpitemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.mpc.entity.MpcMpitem">
        <id column="MPITEM_ID" property="mpitemId" />
        <result column="MPITEM_CODE" property="mpitemCode" />
        <result column="MPITEM_NAME" property="mpitemName" />
        <result column="P_MPITEM_ID" property="pMpitemId" />
        <result column="ORG_ID" property="orgId" />
        <result column="MEASURE_UNIT" property="measureUnit" />
        <result column="MPITEM_BIG_CLASS" property="mpitemBigClass" />
        <result column="CONST_TECH" property="constTech" />
        <result column="CONST_MAT" property="constMat" />
        <result column="MECH_EQUIP" property="mechEquip" />
        <result column="FIELD_MGMT" property="fieldMgmt" />
        <result column="STAFF_CFG" property="staffCfg" />
        <result column="REMARK" property="remark" />
        <result column="MPITEM_SYS_ID" property="mpitemSysId" />
        <result column="MPITEM_SNAME" property="mpitemSname" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MPITEM_ID, MPITEM_CODE, MPITEM_NAME, P_MPITEM_ID, ORG_ID, MEASURE_UNIT, MPITEM_BIG_CLASS, CONST_TECH, CONST_MAT, MECH_EQUIP, FIELD_MGMT, STAFF_CFG, REMARK, MPITEM_SYS_ID, MPITEM_SNAME, CREATE_USER_ID, UPDATE_USER_ID, CREATE_TIME, UPDATE_TIME
    </sql>
    <select id="selectMpitems" resultType="com.hualu.app.module.mems.mpc.entity.MpcMpitem">
        SELECT distinct MPC.mpitem_id,
                        nvl(PRC.MMP_CODE, MPC.mpitem_code) as mpitem_code,
                        MPC.mpitem_name,
                        MPC.p_mpitem_id,
                        MPC.org_id,
                        MPC.measure_unit,
                        MPC.mpitem_big_class,
                        MPC.const_tech,
                        MPC.const_mat,
                        MPC.mech_equip,
                        MPC.field_mgmt,
                        MPC.staff_cfg,
                        MPC.remark,
                        MPC.mpitem_sys_id,
                        MPC.mpitem_sname,
                        MPC.create_user_id,
                        MPC.update_user_id,
                        MPC.create_time,
                        MPC.update_time,
                        PRC.rate,
                        PRC.Mmp_Code
        FROM MPC_MPITEM MPC
                 join MPC_CONTRACT MM on mpc.mpitem_sys_id = MM.MPITEM_SYS_ID
                 join MPC_DSS_MEASURE SURE on MPC.MPITEM_ID = SURE.MPITEM_ID
                 join MPC_MPITEM_PRICE PRC on MPC.MPITEM_ID = PRC.MP_ITEM_ID and MM.CONTR_ID = PRC.CONTR_ID
            ${ew.customSqlSegment}
    </select>
    <select id="selectMpitemsByDssId" resultType="com.hualu.app.module.mems.mpc.entity.MpcMpitem">
        SELECT distinct MPC.MPITEM_ID, MPC.*, PRC.rate, PRC.mmp_code,PRC.UNIT_PRICE
        FROM MPC_MPITEM MPC,
             MPC_DSS_MEASURE SURE,
             MPC_MPITEM_PRICE PRC,
             MPC_CONTRACT MM
            ${ew.customSqlSegment}
    </select>
</mapper>
