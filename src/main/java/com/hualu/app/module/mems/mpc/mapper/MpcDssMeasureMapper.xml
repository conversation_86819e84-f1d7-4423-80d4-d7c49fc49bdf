<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.mpc.mapper.MpcDssMeasureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.mpc.entity.MpcDssMeasure">
        <id column="DSS_TYPE" property="dssType" />
        <result column="MPITEM_ID" property="mpitemId" />
        <result column="ORG_ID" property="orgId" />
        <result column="MPITEM_SYS_ID" property="mpitemSysId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_TYPE, MPITEM_ID, ORG_ID, MPITEM_SYS_ID
    </sql>
    <select id="fetchMpitemSystems" resultType="com.hualu.app.module.mems.mpc.dto.MpitemSystemDto">
        select TK.CONTR_ID,dms.MPITEM_SYS_ID,dms.MPITEM_SYS_NM,MC.MNT_ORG_ID from (
              select MNT_ORG_ID,CONTR_ID from dm_task where to_char(CREATE_TIME,'yyyy') = #{year} group by MNT_ORG_ID, CONTR_ID order by MNT_ORG_ID ) TK
              join MPC_CONTRACT MC on TK.CONTR_ID = MC.CONTR_ID
              join DSS_MPITEM_SYSTEM DMS on mc.MPITEM_SYS_ID = dms.MPITEM_SYS_ID
    </select>
    <select id="listDssType" resultType="java.lang.String">
        select dss_type from MPC_DSS_MEASURE where ORG_ID = #{orgId} and MPITEM_SYS_ID=#{mpSystemId} and (dss_type like '%XC%' or dss_type like'QLJCJC%') group by dss_type order by dss_type
    </select>

</mapper>
