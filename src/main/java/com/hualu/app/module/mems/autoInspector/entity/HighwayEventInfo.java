package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 高速公路交安事件信息表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_EVENT_INFO")
@ApiModel(value="HighwayEventInfo对象", description="高速公路交安事件信息表")
public class HighwayEventInfo implements Serializable {

    
    @NotBlank(message="[交安事件唯一ID]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("交安事件唯一ID")
    @TableField("ID")
    private String id;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("快照ID")
    @TableField("SNAPSHOT_ID")
    private String snapshotId;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("任务ID")
    @TableField("TASK_ID")
    private String taskId;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("路段ID")
    @TableField("SEGMENT_ID")
    private String segmentId;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("路段名称")
    @TableField("SEGMENT_NAME")
    private String segmentName;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("上下行信息")
    @TableField("INSPECT_DIRECTION")
    private String inspectDirection;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("数据来源，离线上传：offline，实时上传：Inspect")
    @TableField("DATA_FROM")
    private String dataFrom;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("对象类型")
    @TableField("OBJECT_TYPE")
    private String objectType;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("对象子类型")
    @TableField("OBJECT_SUBTYPE")
    private String objectSubtype;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("事件类型")
    @TableField("DEFECT_TYPE")
    private String defectType;
    
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("状态")
    @TableField("STATE")
    private String state;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("开始桩号")
    @TableField("STAKE_START")
    private String stakeStart;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("结束桩号")
    @TableField("STAKE_END")
    private String stakeEnd;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("几何类型，如Point")
    @TableField("GEOM_TYPE")
    private String geomType;
    
    @ApiModelProperty("经度坐标")
    @TableField("LONGITUDE")
    private BigDecimal longitude;
    
    @ApiModelProperty("纬度坐标")
    @TableField("LATITUDE")
    private BigDecimal latitude;
    
    @ApiModelProperty("相比上次的变化状态，0,1,2")
    @TableField("TRANSFORMATION")
    private Integer transformation;
    
    @ApiModelProperty("数据状态，0,1,2")
    @TableField("DATA_STATUS")
    private Integer dataStatus;
    
    @ApiModelProperty("公里桩是否校准，0：未校准，1：已校准")
    @TableField("IS_CALIBRATED")
    private Integer isCalibrated;
    
    @ApiModelProperty("创建时间")
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;
    
    @ApiModelProperty("更新时间")
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("交通标志类型")
    @TableField("TRAFFIC_SIGN_TYPE")
    private String trafficSignType;
}
