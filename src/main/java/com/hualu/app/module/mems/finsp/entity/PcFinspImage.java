package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 结构物排查照片表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinspImage对象", description="结构物排查照片表")
public class PcFinspImage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "照片ID")
    @TableId("IMAGE_ID")
    private String imageId;

    @ApiModelProperty(value = "记录表ID")
    @TableField("RECORD_ID")
    private String recordId;

    @ApiModelProperty(value = "照片类型(病害照片：1:处治前,2:处治中,3:处治后)，涵洞照片：1：涵洞进口全貌照片，2：涵洞出口全貌照片，3：涵洞内通视照片，4：病害代表照片")
    @TableField("IMAGE_TYPE")
    private String imageType;


    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @TableLogic
    @TableField(value = "DEL_FLAG",fill = FieldFill.INSERT)
    private Integer delFlag;
}
