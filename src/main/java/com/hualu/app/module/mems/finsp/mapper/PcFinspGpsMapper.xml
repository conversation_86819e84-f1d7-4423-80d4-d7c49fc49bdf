<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspGpsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinspGps">
        <result column="GPS_ID" property="gpsId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="HEIGHT" property="height" />
        <result column="SPEED" property="speed" />
        <result column="LON" property="lon" />
        <result column="LAT" property="lat" />
        <result column="TIME" property="time" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="TRACK_ID" property="trackId" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        GPS_ID, FINSP_ID, CREATE_TIME, UPDATE_TIME, HEIGHT, SPEED, LON, LAT, TIME, INSP_PERSON, TRACK_ID, REMARK
    </sql>
    <select id="getRealTimeLocationByFinspId" resultType="com.hualu.app.module.mems.finsp.entity.PcFinspGps">
        <![CDATA[
        select * from (select * from memsdb.pc_finsp_gps where del_flag=0 and finsp_id = #{finspId} order by time desc) a where rownum <= 1
        ]]>
    </select>

</mapper>
