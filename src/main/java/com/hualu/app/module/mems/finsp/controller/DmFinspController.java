package com.hualu.app.module.mems.finsp.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.hualu.app.comm.RestResult;
import com.hualu.app.config.RedisCacheConfig;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.HsmsSlopeInfoService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.stat.FinspStatDto;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairStatDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspGpsCountDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspRunningDto;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.finsp.mapper.DmFinspMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspRecordService;
import com.hualu.app.module.mems.finsp.service.DmFinspResultService;
import com.hualu.app.module.mems.finsp.service.DmFinspService;
import com.hualu.app.module.mems.finsp.service.SlopeRegularSourceTrackService;
import com.hualu.app.module.mems.mpc.service.MpcFileRefService;
import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.SituationCacheService;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.utils.*;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.excelimport.util.H_ExcelDownloadHelper;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisBatchQuery;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Api(tags = "经常检查_主单接口")
@RestController
@RequestMapping(value = {"dmFinsp",H_BasedataHepler.HUALU_API+"/dmFinsp"})
@Slf4j
public class DmFinspController extends M_MyBatisController<DmFinsp, DmFinspMapper> {

    @Autowired
    DmFinspService service;

    @Autowired
    DmFinspRecordService recordService;

    @Autowired
    DmFinspResultService resultService;

    @Autowired
    BaseLineService lineService;

    @Autowired
    MpcFileRefService refService;

    @Autowired
    DssImageService imageService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    SlopeRegularSourceTrackService trackService;

    @Autowired
    HsmsSlopeInfoService slopeInfoService;

    @Autowired
    FwRightOrgService orgService;

    @Autowired
    FwRightUserService userService;

    @Autowired
    private SituationCacheService situationCacheService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private NmFinspService finspService;

    @Value("${fileReposity}")
    String fileReposity;

    public static String orgAuthSql = "SELECT o.id FROM gdgs.FW_RIGHT_ORG o START WITH o.id = '{}' CONNECT BY PRIOR o.ID = o.PARENT_ID and LEVEL<4 and IS_ENABLE=1 and is_deleted=0";


    /**
     * 正在巡检的边坡经常检查单
     * @return
     */
    @GetMapping("running")
    public RestResult<List<DmFinspRunningDto>> selectRunning(){
        List<DmFinspRunningDto> dtos = service.selectRunning();
        return RestResult.success(dtos);
    }

    /**
     * 经常检查单导出
     * @param ids
     * @param facilityCat
     * @param response
     */
    @RequestMapping("exportExcel")
    public void exportExcel(@RequestParam(value = "ids", required = true) String[] ids,
                            @RequestParam(value = "facilityCat", required = true) String facilityCat
            ,Integer signType, HttpServletResponse response) {
        //Collection<DmFinsp> finsps = service.listByIds(Arrays.asList(ids));
        Collection<DmFinsp> finsps = H_BatisQuery.protectBatchIn(new H_BatisBatchQuery() {
            @Override
            public List selectListForIn(List list) {
                 List<DmFinsp> dbFinsps = (List<DmFinsp>) service.listByIds(list);
                return dbFinsps;
            }
        },Arrays.asList(ids),500);
        initLineName(finsps.stream().collect(Collectors.toList()));
        //excel 模版路径
        String filePath = fileReposity + "\\"+ IdUtil.fastSimpleUUID();
        FileUtil.mkdir(filePath);
        List<String> excelPathList = Lists.newArrayList();
        finsps.forEach(dmFinsp->{
            List<DmFinspResult> dmFinspResults = resultService.selectResultByFinspId(dmFinsp.getFinspId());
            IPage<DmFinspRecord> recordIPage = recordService.selectViewRecordByFinspId(dmFinsp.getFinspId());

            IFacBase base = CustomApplicationContextHolder.getBeansOfType(IFacBase.class).values().stream()
                    .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
            if (base == null){
                throw new BaseException(facilityCat+"找不到对应的下载模版");
            }
            //获取excel下载地址
            String excelPath = base.exportExcel(dmFinsp, recordIPage.getRecords(), dmFinspResults, filePath,signType);
            if (StrUtil.isNotBlank(excelPath)){
                excelPathList.add(excelPath);
            }
        });
        //导出名称
        String excelExportName = "";
        //导出路径地址
        String excelDownloadPath = "";
        if (excelPathList.size() == 1){
            excelDownloadPath = excelPathList.get(0);
            File newFile = new File(excelDownloadPath);
            excelExportName = newFile.getName();
        }else {
            excelExportName = DateUtil.formatDate(new Date())+".zip";
            File zip = ZipUtil.zip(filePath);
            excelDownloadPath = zip.getAbsolutePath();
        }
        H_ExcelDownloadHelper.download(excelExportName,excelDownloadPath,response);
    }

    /**
     * 删除经常检查单
     * @param finspId
     * @return
     */
    @GetMapping("/delete/{finspId}")
    public RestResult<String> deleteFinsp(@PathVariable("finspId") String finspId){
        service.delDmFinsp(finspId);
        return RestResult.success("操作成功");
    }

    /**
     * 删除检查工照
     * @param fileId
     * @return
     */
    @GetMapping("delImage")
    public RestResult<String> deleteImage(String fileId){
        //refService.delByFileId(fileId);
        imageService.delByFileId(fileId);
        return RestResult.success("操作成功");
    }

    /**
     * 审批办理（无病害批量办理）
     * @param processInstIds 流程ID，多个以逗号分隔
     * @param appvOpinion 审批意见
     * @param nextUserId 下一节点审批人
     * @return
     */
    @PostMapping("approveDmFinspNoDss")
    public RestResult<String> approveDmFinspNoDss(String processInstIds,String appvOpinion,String nextUserId){
        service.approveDmFinspNoDss(CustomRequestContextHolder.getUserCode(),processInstIds,nextUserId,appvOpinion);
        return RestResult.success("办理成功");
    }

    /**
     * 审批办理（包含有病害和无病害）
     * @param processInstIds 流程ID
     * @param appvOpinion 审批意见
     * @param nextUserId 下一节点审批人
     * @param status 检查单状态
     * @return
     */
    @PostMapping("approveDmFinsp")
    public RestResult<String> approveDmFinsp(String processInstIds,String appvOpinion,String nextUserId,int status){
        service.approveDmFinsp(CustomRequestContextHolder.getUserCode(),processInstIds,nextUserId,appvOpinion,status);
        return RestResult.success("办理成功");
    }

    @ApiOperation("获取主单详情信息")
    @GetMapping("/getById/{finspId}")
    public RestResult<DmFinsp> getById(@PathVariable("finspId") String finspId){
        DmFinsp dmFinsp = baseMapper.selectById(finspId);
        initLineName(Lists.newArrayList(dmFinsp));
        initImage(Lists.newArrayList(dmFinsp));
        initBpFinsp(Lists.newArrayList(dmFinsp));
        return RestResult.success(dmFinsp);
    }

    /**
     * 获取主单办理状态信息 0:待办 ，其他为已办
     * @param finspId
     * @return
     */
    @ApiOperation("获取主单办理信息")
    @GetMapping("/getDmFinsp/{finspId}")
    public RestResult<DmFinsp> getDmFinspStatus(@PathVariable("finspId") String finspId){
        DmFinsp dmFinsp = baseMapper.selectById(finspId);
        return RestResult.success(dmFinsp);
    }

    @ApiOperation("分页查询")
    @PostMapping("selectPage")
    @Override
    public RestResult<List<DmFinsp>> selectPage() {
        Map reqParam = this.getReqParam();
        Object fxpg = reqParam.get("fxpg");
        QueryWrapper queryWrapper = this.initQueryWrapper(reqParam);
        if (fxpg != null){
            queryWrapper.inSql("struct_Id","select SLOPE_ID from MEMSDB.SLOPE_RISK_REF");
        }
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        List<DmFinsp> records = iPage.getRecords();
        initLineName(records);
        initImage(records);
        initBpFinsp(records);
        setSourceTrace(reqParam,records);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 初始化化边坡经常检查单，是否包含原始轨迹
     * @param reqParam
     * @param records
     */
    private void setSourceTrace(Map reqParam,List<DmFinsp> records) {
        Object facilityCat = reqParam.get("facilityCat");
        //目前只有边坡才有原始轨迹
        if (!"BP".equals(facilityCat)){
            return;
        }
        List<String> slopeIds = records.stream().map(DmFinsp::getStructId).collect(Collectors.toList());
        Set<String> dbSlopeIds = trackService.hasSourceTack(slopeIds);

        records.forEach(item->{
            if (dbSlopeIds.contains(item.getStructId())){
                item.setHasSourceTrack(1);
            }
        });
    }

    /**
     * 初始化路线名称
     * @param records
     */
    private void initLineName(List<DmFinsp> records) {
        Map<String, BaseLine> allLineMap = lineService.getAllLineMap();
        //设置路线显示名称
        records.forEach(item->{
            BaseLine baseLine = allLineMap.get(item.getLineCode());
            if (baseLine != null){
                item.setLineName(baseLine.getLineAllname()+"("+baseLine.getLineCode()+")");
            }
        });
    }

    /**
     * 初始化照片
     * @param finsps
     */
    private void initImage(List<DmFinsp> finsps){
        if (CollectionUtil.isEmpty(finsps)){
            return;
        }
        Set<String> dssIds = finsps.stream().map(DmFinsp::getFinspId).collect(Collectors.toSet());
        Map<String, List<String>> imageMap = imageService.mapByDssIds(dssIds);
        for (DmFinsp item : finsps) {
            List<String> fileIds = imageMap.get(item.getFinspId());
            if (CollectionUtil.isNotEmpty(fileIds)){
                item.setFileIds(StrUtil.join(",",fileIds));
                item.setImageHost(C_Constant.IMAGE_HOST);
            }
        }
    }

    /**
     * 初始化边坡经常检查内容
     * @param finsps
     */
    private void initBpFinsp(List<DmFinsp> finsps){
        if (CollectionUtil.isEmpty(finsps)){
            return;
        }
        finsps.forEach(item->{
            if (H_BasedataHepler.BP.equals(item.getFacilityCat()) && H_BasedataHepler.BPJC_VERSION.equals(item.getFinVersion())){
                item.setSlopeTypeName(dicService.getDicName(H_BasedataHepler.DIC_BP_TYPE,item.getSlopeType()));
                item.setSlopePositionName(dicService.getDicName(H_BasedataHepler.DIC_BP_POSITION,item.getSlopePosition()));
                item.setSlopeFxdjName(dicService.getDicName(H_BasedataHepler.DIC_BP_FXDJ,item.getSlopeFxdj()));
                item.setSlopeZhlxName(dicService.getDicName(H_BasedataHepler.DIC_BP_ZHTYPE,item.getSlopeZhlx()));
            }
            item.setWeatherName(dicService.getDicName(H_BasedataHepler.DIC_WEATHER,item.getWeather()));
        });
    }

    /**
     * 初始化路线名称
     * @param dmFinsp
     * @param lineId
     */
    private void initLine(DmFinsp dmFinsp,String lineId){
        //初始化路线名称
        if (StrUtil.isNotBlank(dmFinsp.getLineCode())){
            Map<String, BaseLine> allLineMap = lineService.getAllLineMap();
            BaseLine baseLine = allLineMap.get(dmFinsp.getLineCode());
            if (baseLine != null) {
                dmFinsp.setLineName(baseLine.getLineAllname()+"("+baseLine.getLineCode()+")");
            }
        }

        if (StrUtil.isNotBlank(lineId)){
            BaseLine baseLine = lineService.selectLine(lineId);
            if (baseLine != null) {
                dmFinsp.setLineCode(baseLine.getLineId());
                dmFinsp.setLineName(baseLine.getLineAllname()+"("+baseLine.getLineCode()+")");
            }
        }
    }

    @Override
    protected QueryWrapper initQueryWrapper(Map params) {
        QueryWrapper<DmFinsp> queryWrapper = new QueryWrapper();
        initQueryParam(queryWrapper,params);
        H_BatisQuery.setFieldValue2In(queryWrapper, params, this.getEntityClass());
        return queryWrapper;
    }

    private <T> void initQueryParam(QueryWrapper<T> queryWrapper, Map params){
        queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());
        queryWrapper.eq("project_type",1);
        Object startDate = params.get("startDate");
        if (!StrUtil.isBlankIfStr(startDate)){
            queryWrapper.apply("insp_date >= to_date({0},'YYYY-MM-dd')",startDate);
            params.remove("startDate");
        }

        Object endDate = params.get("endDate");
        if (!StrUtil.isBlankIfStr(endDate)){
            queryWrapper.apply("insp_date <= to_date({0},'YYYY-MM-dd hh24:mi:ss')",endDate+" 23:59:59");
            params.remove("endDate");
        }

        String status = params.getOrDefault("status", "").toString();
        String taskSql = H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.valueOf(status), "dm_finsp");

        if (StrUtil.isNotBlank(taskSql)){
            queryWrapper.exists(taskSql);
            params.remove("status");
        }

        //根据组织机构编码，查询权限范围内数据
        Object orgCode = params.get("orgCode");
        if (!StrUtil.isBlankIfStr(orgCode)){
            String orgSql = StrUtil.format(orgAuthSql, orgCode);
            queryWrapper.inSql("mnt_org_id",orgSql);
        }
    }


    /**
     * 查看经常检查单
     * @param structId 结构物ID
     * @param facilityCat 设施类型
     * @return
     */
    private RestResult<DmFinsp> getDmFinpByStructId(String structId,String facilityCat){
        String finspId = service.getFinspId(structId, facilityCat, new Date());
        if (StrUtil.isNotBlank(finspId)){
            DmFinsp dbFinsp = service.getById(finspId);
            initLineName(Lists.newArrayList(dbFinsp));
            initImage(Lists.newArrayList(dbFinsp));
            initBpFinsp(Lists.newArrayList(dbFinsp));
            return RestResult.success(dbFinsp);
        }
        return RestResult.success(null);
    }

    /**
     * 根据当前日期，返回当前季度对应的经常检查单
     * @param structId
     * @param facilityCat
     * @return
     */
    @PostMapping("listByQuarter")
    public RestResult<List<DmFinsp>> listByQuarter(String structId,String facilityCat){
        List<DmFinsp> dmFinsps = service.listByQuarter(structId, facilityCat, new Date());
        if (CollectionUtil.isNotEmpty(dmFinsps)){
            initLineName(dmFinsps);
            initImage(dmFinsps);
            initBpFinsp(dmFinsps);
            return RestResult.success(dmFinsps);
        }
        return RestResult.error("本季度暂无经常检查单");
    }


    /**
     * 边坡经常检查单创建（新博APP）
     * @param structId 结构物ID
     * @param isShow 是否查看
     * @return
     */
    @PostMapping("createFinspByStructId2App")
    public RestResult<DmFinsp> createFinspByStructId2App(String structId,@RequestParam(defaultValue = "false") boolean isShow){
        String facilityCat = "BP";
        String finspId = service.getFinspId(structId, facilityCat, new Date());
        DmFinsp dmFinsp = new DmFinsp();
        if (isShow){
            return getDmFinpByStructId(structId,facilityCat);
        }
        if (StrUtil.isBlank(finspId)){
            //根据结构物ID，查询结构信息
            HsmsSlopeInfo slopeInfo = slopeInfoService.getById(structId);
            if (slopeInfo == null){
                throw new BaseException("结构物不存在");
            }
            //finsp_id,facility_cat,mnt_org_id,insp_date,insp_person,line_code,status,search_dept 必填项
            Object en = CustomRequestContextHolder.get("ORG_EN");
            String orgEn = en == null ? "" : en.toString();
            String finspCode = service.getNextCode(facilityCat, orgEn);
            dmFinsp.setFacilityCat(facilityCat);
            dmFinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
            dmFinsp.setMntnOrgNm(CustomRequestContextHolder.getOrgName());
            dmFinsp.setStructId(structId);

            //巡查单位必填  暂时无法默认巡查单位  根据当前登录人所在部门进行展示？
            dmFinsp.setSearchDept(userService.getDeptName(CustomRequestContextHolder.getUserId()));
            dmFinsp.setWeather("01");//表示晴天
            dmFinsp.setInspPerson(CustomRequestContextHolder.getUserName());
            dmFinsp.setWeatherName(dicService.getDicName(H_BasedataHepler.DIC_WEATHER,dmFinsp.getWeather()));
            dmFinsp.setFinspCode(finspCode);
            dmFinsp.setInspDate(new Date());
            initLine(dmFinsp, slopeInfo.getLineId());
            service.saveOrUpdateFinsp(dmFinsp);
        }else {
            dmFinsp.setFinspId(finspId);
        }
        DmFinsp dbFinsp = service.getById(dmFinsp.getFinspId());
        initLineName(Lists.newArrayList(dbFinsp));
        initImage(Lists.newArrayList(dbFinsp));
        initBpFinsp(Lists.newArrayList(dbFinsp));
        return RestResult.success(dbFinsp);
    }

    /**
     * 添加或修改经常检查单
     * @param data dmFinsp对象字符串
     * @param files 上传文件
     * @return
     * @throws Exception
     */
    @ApiOperation("添加或修改经常检查单")
    @PostMapping(value = "/saveOrUpdateDmFinsp")
    public RestResult<DmFinsp> saveOrUpdateFinsp(@RequestParam("data") String data,@RequestParam(value = "files",required = false) MultipartFile[] files) throws Exception {
        DmFinsp dmFinsp = JSONUtil.toBean(data,DmFinsp.class);

        // 针对新博app，同一个边坡同一天，只创建一个单，如果已经存在，直接返回数据
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            dmFinsp.setFileIds(StrUtil.join(",",fileIds));
        }
        boolean containCode = service.isContainCode(dmFinsp.getFacilityCat(), dmFinsp.getFinspCode(), dmFinsp.getFinspId());
        String newCode = "";
        //单号不存在
        if (!containCode){
            Object en = CustomRequestContextHolder.get("ORG_EN");
            String orgEn = en == null ? "" : en.toString();
            dmFinsp.setFinspCode(service.getNextCode(dmFinsp.getFacilityCat(),orgEn));
            newCode = "保存成功，单号重复已重新生成【" + dmFinsp.getFinspCode() + "】";
        }
        service.saveOrUpdateFinsp(dmFinsp);
        DmFinsp dbFinsp = service.getById(dmFinsp.getFinspId());
        initLineName(Lists.newArrayList(dbFinsp));
        initImage(Lists.newArrayList(dmFinsp));
        initBpFinsp(Lists.newArrayList(dmFinsp));
        return RestResult.success(dbFinsp);
    }

    /**
     * 获取下一张检查单编号、管理单位、养护单位等信息
     * @param facilityCat 设施分类
     * @param structId 结构物ID（新建检查单时用）
     * @return
     */
    @ApiOperation("获取下一张检查单编号、管理单位、养护单位等信息")
    @GetMapping("getNextCode")
    public RestResult<DmFinsp> getNextCode(@RequestParam("facilityCat") String facilityCat,@RequestParam(value = "structId",required = false) String structId){

        IBaseStructFace structFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        if (structFace == null){
            return RestResult.success("设施分类未匹配");
        }
        BaseStructDto structDto = null;
        if (StrUtil.isNotBlank(structId)){
            structDto = structFace.getId(structId);
            // 结构物扫码时，需核对当前结构物的管养单位是否与当前用户匹配
            if (StrUtil.isNotBlank(structDto.getOprtOrgCode()) && !structDto.getOprtOrgCode().equals(CustomRequestContextHolder.getOrgIdString())){
                throw new BaseException("该结构物不归本单位管养");
            }
        }
        Object en = CustomRequestContextHolder.get("ORG_EN");
        String orgEn = en == null ? "" : en.toString();
        String nextCode = service.getNextCode(facilityCat, orgEn);

        DmFinsp dmFinsp = new DmFinsp();
        dmFinsp.setFacilityCat(facilityCat);
        dmFinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
        dmFinsp.setStructId(structId);
        dmFinsp.setFinVersion(H_BasedataHepler.BPJC_VERSION);
        dmFinsp.setInspPerson(CustomRequestContextHolder.getUserName());
        DmFinsp lastFinsp = service.getLastFinsp(dmFinsp,null);
        if (lastFinsp != null){
            //采用上次巡查人的名称，如果为空，采用当前账号
            dmFinsp.setInspPerson(StrUtil.isNotBlank(lastFinsp.getInspPerson())?lastFinsp.getInspPerson():CustomRequestContextHolder.getUserName());
            dmFinsp.setMntnOrgNm(lastFinsp.getMntnOrgNm());
            dmFinsp.setLineCode(lastFinsp.getLineCode());
            dmFinsp.setLineDirect(lastFinsp.getLineDirect());
            dmFinsp.setStructId(lastFinsp.getStructId());
            dmFinsp.setStructName(lastFinsp.getStructName());
            dmFinsp.setSearchDept(lastFinsp.getSearchDept());
            dmFinsp.setWeather(lastFinsp.getWeather());
            dmFinsp.setLineDirect(lastFinsp.getLineDirect());
            dmFinsp.setRoadSection(lastFinsp.getRoadSection());
            dmFinsp.setSlopeType(lastFinsp.getSlopeType());
            dmFinsp.setSlopePosition(lastFinsp.getSlopePosition());
            initBpFinsp(Lists.newArrayList(dmFinsp));
        }else {
            //IBaseStructFace
            /*structFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                    .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
            if (structFace != null && StrUtil.isNotBlank(structId)){
                BaseStructDto structDto = structFace.getId(structId);
                dmFinsp.setStructName(structDto.getStructName());
                dmFinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
                dmFinsp.setMntnOrgNm(CustomRequestContextHolder.getOrgName());
                initLine(dmFinsp,structDto.getLineId());
            }*/
            dmFinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
            dmFinsp.setMntnOrgNm(CustomRequestContextHolder.getOrgName());
            if (structDto != null){
                dmFinsp.setStructName(structDto.getStructName());
                initLine(dmFinsp,structDto.getLineId());
            }
        }
        if (StrUtil.isBlank(dmFinsp.getWeather())){
            dmFinsp.setWeather("01");//表示晴天
        }
        dmFinsp.setWeatherName(dicService.getDicName(H_BasedataHepler.DIC_WEATHER,dmFinsp.getWeather()));
        dmFinsp.setFinspCode(nextCode);
        dmFinsp.setInspDate(new Date());
        initLine(dmFinsp,null);
        dmFinsp.setFinspCode(nextCode);
        return RestResult.success(dmFinsp);
    }


    /**
     * 经常检查单统计图（按照当前单位直接统计数据）
     * @param month 月份：2024-08
     * @param importantSlope 重点边坡 true:是，false:否
     * @return
     */
    @ApiOperation("经常检查单统计图（按照当前单位直接统计数据）")
    @GetMapping("getFinspStat")
    public RestResult<List<FinspStatDto>> getFinspStat(@RequestParam("month") String month,
            @RequestParam(value = "importantSlope",required = false,defaultValue = "false") Boolean importantSlope) {
        String orgCode = CustomRequestContextHolder.getOrgIdString();
        
        // 解析年月
        String[] yearMonth = month.split("-");
        int year = Integer.parseInt(yearMonth[0]);
        int monthNum = Integer.parseInt(yearMonth[1]);
        
        // 获取月份前缀
        String[] months = {"jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"};
        String monthPrefix = months[monthNum - 1];
        
        // 1. 先从缓存获取数据
        String cacheKey = String.format(RedisCacheConfig.FINSP_SITUATION_CACHE_KEY, "N000001", year);
        List<NmFinspSituationShow> allData = redisUtils.getList(cacheKey, NmFinspSituationShow.class);
        List<NmFinspSituationShow> situationList;
        
        if (allData != null && !allData.isEmpty()) {
            Set<String> orgSet = new HashSet<>(orgService.selectChildOprtOrgCodes(orgCode));
            situationList = allData.parallelStream()
                    .filter(item -> orgSet.contains(item.getOrgCode()))
                    .collect(Collectors.toList());
            
            if (situationList.isEmpty()) {
                situationList = finspService.querySituationShow(orgCode, year);
            }
        } else {
            situationList = finspService.querySituationShow(orgCode, year);
        }
        
        // 2. 转换为FinspStatDto并按设施类型分组处理
        if (situationList != null && !situationList.isEmpty()) {
            Map<String, FinspStatDto> facilityStatMap = new HashMap<>();
            
            for (NmFinspSituationShow item : situationList) {
                // 获取所有属性
                Field[] fields = item.getClass().getDeclaredFields();
                
                // 用于记录每个设施类型在当前月份是否已经处理过
                Map<String, Boolean> facilityProcessed = new HashMap<>();
                
                for (Field field : fields) {
                    field.setAccessible(true);
                    String fieldName = field.getName().toLowerCase();
                    
                    // 只处理当前月份的数据，且排除带result的属性
                    if (!fieldName.startsWith(monthPrefix.toLowerCase()) || fieldName.contains("result")) {
                        continue;
                    }
                    
                    // 判断属性属于哪个设施类型
                    String facilityCat = null;
                    if (fieldName.contains("ql")) {
                        facilityCat = "QL";
                    } else if (fieldName.contains("lm")) {
                        facilityCat = "LM";
                    } else if (fieldName.contains("bp")) {
                        facilityCat = "BP";
                    } else if (fieldName.contains("sd")) {
                        facilityCat = "SD";
                    } else if (fieldName.contains("ja")) {
                        facilityCat = "JA";
                    } else if (fieldName.contains("hd")) {
                        facilityCat = "HD";
                    }
                    
                    if (facilityCat == null) {
                        continue;
                    }
                    
                    try {
                        Object value = field.get(item);
                        if (value == null) {
                            continue;
                        }
                        
                        String strValue = value.toString().trim();
                        if (strValue.isEmpty() || strValue.equals("-")) {
                            continue;
                        }
                        
                        // 获取或创建对应设施类型的DTO
                        String finalFacilityCat = facilityCat;
                        FinspStatDto dto = facilityStatMap.computeIfAbsent(facilityCat, k -> {
                            FinspStatDto newDto = new FinspStatDto();
                            newDto.setOrgCode(item.getOrgCode());
                            newDto.setOrgName(item.getOrgName());
                            newDto.setFacilityCat(finalFacilityCat);
                            newDto.setStructNum(0);
                            newDto.setCheckNum(0);
                            return newDto;
                        });
                        
                        // 如果该设施类型在当前月份已经处理过，跳过
                        if (facilityProcessed.getOrDefault(facilityCat, false)) {
                            continue;
                        }
                        
                        // 解析数值
                        if (strValue.contains("|")) {
                            String[] parts = strValue.split("\\|");
                            String completedPart = parts[0].trim();
                            String totalPart = parts[1].trim();
                            
                            // 如果任一部分为"-"，跳过此数据
                            if (completedPart.equals("-") || totalPart.equals("-")) {
                                continue;
                            }
                            
                            try {
                                int completedNum = Integer.parseInt(completedPart);
                                int totalNum = Integer.parseInt(totalPart);
                                
                                // 更新统计数据
                                dto.setCheckNum(completedNum);  // 完成数量
                                
                                // 对于LM和JA，结构物数量始终为1
                                if (facilityCat.equals("LM") || facilityCat.equals("JA")) {
                                    dto.setStructNum(1);
                                } else {
                                    dto.setStructNum(totalNum);    // 结构物数量
                                }
                                
                                // 设置noticeMsg
                                String facilityName = getFacilityName(facilityCat);
                                String unit = (facilityCat.equals("LM") || facilityCat.equals("JA")) ? "" : "座";
                                
                                if (facilityCat.equals("LM") || facilityCat.equals("JA")) {
                                    // 对于路面和交安设施，显示还差多少未完成
                                    int remaining = dto.getStructNum() - dto.getCheckNum();
                                    if (remaining > 0) {
                                        dto.setNoticeMsg(String.format("需完成的%s为%d", 
                                            facilityName, dto.getStructNum()));
                                    } else {
                                        dto.setNoticeMsg(String.format("%s已完成检查", facilityName));
                                    }
                                } else {
                                    // 对于其他设施类型
                                    if (dto.getCheckNum() < dto.getStructNum()) {
                                        dto.setNoticeMsg(String.format("需完成的%s总数为%d%s，已完成%d%s", 
                                            facilityName, dto.getStructNum(), unit, dto.getCheckNum(), unit));
                                    } else {
                                        dto.setNoticeMsg(String.format("%s已完成检查", facilityName));
                                    }
                                }
                                
                                // 标记该设施类型在当前月份已处理
                                facilityProcessed.put(facilityCat, true);
                                
                                log.info("处理数据 - 设施类型: {}, 字段: {}, 值: {}, 完成数量: {}, 结构物数量: {}", 
                                    facilityCat, fieldName, strValue, completedNum, 
                                    facilityCat.equals("LM") || facilityCat.equals("JA") ? 1 : totalNum);
                            } catch (NumberFormatException e) {
                                log.error("解析数值失败: {}", strValue);
                                continue;
                            }
                        } else {
                            // 只有单个数值的情况，按 数值|1 处理
                            if (strValue.equals("-")) {
                                continue;
                            }
                            
                            try {
                                int num = Integer.parseInt(strValue);
                                dto.setCheckNum(num);     // 完成数量
                                
                                // 对于LM和JA，结构物数量始终为1
                                if (facilityCat.equals("LM") || facilityCat.equals("JA")) {
                                    dto.setStructNum(1);
                                } else {
                                    dto.setStructNum(1);     // 结构物数量为1
                                }
                                
                                // 设置noticeMsg
                                String facilityName = getFacilityName(facilityCat);
                                String unit = (facilityCat.equals("LM") || facilityCat.equals("JA")) ? "" : "座";
                                
                                if (facilityCat.equals("LM") || facilityCat.equals("JA")) {
                                    // 对于路面和交安设施，显示还差多少未完成
                                    int remaining = dto.getStructNum() - dto.getCheckNum();
                                    if (remaining > 0) {
                                        dto.setNoticeMsg(String.format("需完成的%s为%d", 
                                            facilityName, dto.getStructNum()));
                                    } else {
                                        dto.setNoticeMsg(String.format("%s已完成检查", facilityName));
                                    }
                                } else {
                                    // 对于其他设施类型
                                    if (dto.getCheckNum() < dto.getStructNum()) {
                                        dto.setNoticeMsg(String.format("需完成的%s总数为%d%s，已完成%d%s", 
                                            facilityName, dto.getStructNum(), unit, dto.getCheckNum(), unit));
                                    } else {
                                        dto.setNoticeMsg(String.format("%s已完成检查", facilityName));
                                    }
                                }
                                
                                // 标记该设施类型在当前月份已处理
                                facilityProcessed.put(facilityCat, true);
                                
                                log.info("处理数据 - 设施类型: {}, 字段: {}, 值: {}, 完成数量: {}, 结构物数量: {}", 
                                    facilityCat, fieldName, strValue, num,
                                    facilityCat.equals("LM") || facilityCat.equals("JA") ? 1 : 1);
                            } catch (NumberFormatException e) {
                                log.error("解析数值失败: {}", strValue);
                                continue;
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理属性[{}]失败: {}, 值: {}", field.getName(), e.getMessage());
                    }
                }
            }
            
            // 转换为列表并返回
            List<FinspStatDto> result = new ArrayList<>(facilityStatMap.values());
            
            // 打印最终统计结果
            for (FinspStatDto dto : result) {
                log.info("最终统计结果 - 设施类型: {}, 完成数量: {}, 结构物数量: {}", 
                    dto.getFacilityCat(), dto.getCheckNum(), dto.getStructNum());
            }
            
            return RestResult.success(result);
        }
        
        return RestResult.success(new ArrayList<>());
    }

    /**
     * 获取设施类型的中文名称
     */
    private String getFacilityName(String facilityCat) {
        switch (facilityCat) {
            case "QL":
                return "桥梁";
            case "LM":
                return "路面";
            case "BP":
                return "边坡";
            case "SD":
                return "隧道";
            case "JA":
                return "交安设施";
            case "HD":
                return "涵洞";
            default:
                return facilityCat;
        }
    }

    /**
     * 统计周期内，原始轨迹、巡查轨迹的边坡个数（新博APP）
     * @param startMonth 开始日期（2024-07）
     * @param endMonth 结束日期（2024-08）
     * @return
     */
    @GetMapping("getFinspStat2App")
    public RestResult<List<DmFinspGpsCountDto>> getFinspStat2App(String orgId,String startMonth,String endMonth){
        List<String> orgCodes = orgService.selectChildOprtOrgCodes(orgId);
        Map<String, Set<String>> orgRouteMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(orgCodes)){
            // 获取单位、路段编码集合
            Map<String, Set<String>> routeMap = H_DataAuthHelper.selectOrgGroupByRouteCode(CustomRequestContextHolder.getUserCode());
            for (String item : orgCodes) {
                Set<String> routeCodes = routeMap.get(item);
                if (CollectionUtil.isNotEmpty(routeCodes)){
                    orgRouteMap.put(item,routeCodes);
                }
            }
        }else {
            orgRouteMap = H_DataAuthHelper.selectOrgGroupByRouteCode(CustomRequestContextHolder.getUserCode());
        }
        List<DmFinspGpsCountDto> dmFinspGpsCountDtos = service.countGpsCompleteStat(orgRouteMap,startMonth,endMonth);
        return RestResult.success(dmFinspGpsCountDtos);
    }

    /**
     * 导出统计周期内，原始轨迹、巡查轨迹的边坡个数（新博APP）
     * @param startMonth 开始日期（2024-07）
     * @param endMonth 结束日期（2024-08）
     * @param response
     */
    @SneakyThrows
    @GetMapping("exportFinspStat2App")
    public RestResult<String> exportFinspStat2App(String orgId,String startMonth,String endMonth,HttpServletResponse response){
        RestResult<List<DmFinspGpsCountDto>> restResult = getFinspStat2App(orgId,startMonth, endMonth);
        List<DmFinspGpsCountDto> countDtos = restResult.getData();
        int index = 0;
        for (DmFinspGpsCountDto countDto : countDtos) {
            countDto.setIndex(++index);
        }
        String excelTemplatPath = "excel/dssRepair.xlsx";
        TemplateExportParams exportParams = new TemplateExportParams(excelTemplatPath,true,null);
        Map<String, Object> xxMap = Maps.newHashMap();
        xxMap.put("recList",countDtos);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,xxMap);
        //H_ExcelDownloadHelper.downLoad("经常检查统计.xlsx",response,workbook);
        String fileName = File.separator + IdUtil.fastSimpleUUID() + ".xlsx";
        try(OutputStream outputStream = Files.newOutputStream(new File(fileReposity + fileName).toPath())){
            workbook.write(outputStream);
            workbook.close();
        }
        return RestResult.success("fileResposity"+fileName,"操作成功");
    }

    /**
     * 经常检查单统计图（展示下级单位数据）
     * @param month 月份：2024-08
     * @param importantSlope 重点边坡 true:是，false:否
     * @return
     */
    @ApiOperation("经常检查单统计图（展示下级单位数据）")
    @GetMapping("getFinspStatByOrg")
    public RestResult<List<Map<String,Object>>> getFinspStatByOrg(@RequestParam("month") String month,@RequestParam(value = "importantSlope",required = false,defaultValue = "false") Boolean importantSlope){
        Map<String, Set<String>> orgRouteMap = H_DataAuthHelper.selectOrgGroupByRouteCode(CustomRequestContextHolder.getUserCode());
        Map<String, IFinspStatBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFinspStatBase.class);
        List<CompletableFuture<FinspStatDto>> futureList = new ArrayList<>();
        beansOfType.values().forEach(bean->{
            orgRouteMap.forEach((k,v)->{
                CompletableFuture<FinspStatDto> future = CompletableFuture.supplyAsync(() -> {
                    CustomRequestContextHolder.set("dmfinsp:importantSlope",importantSlope);
                    FinspStatDto finspStat = bean.getFinspStat(Lists.newArrayList(v), month);
                    finspStat.setOrgCode(k);
                    return finspStat;
                });
                futureList.add(future);
            });
        });
        List<FinspStatDto> dtoList = H_FutureHelper.sequence(futureList).join();
        List<String> orgCode = dtoList.stream().map(FinspStatDto::getOrgCode).collect(Collectors.toList());
        Map<String, String> orgMap = orgService.selectByOrgCodes(orgCode);

        //设置机构名称
        dtoList.forEach(item->{
            item.setOrgName(orgMap.get(item.getOrgCode()));
        });
        List<Map<String,Object>> resList = Lists.newArrayList();
        Map<String, List<FinspStatDto>> orgResMap = dtoList.stream().collect(Collectors.groupingBy(FinspStatDto::getOrgCode));
        orgResMap.forEach((k,v)->{
            Map<String,Object> resMap = Maps.newTreeMap();
            resMap.put("orgCode",k);
            resMap.put("orgName",v.get(0).getOrgName());

            v.forEach(item->{
                String fac = item.getFacilityCat().toLowerCase();
                Object structNum = ReflectUtil.getFieldValue(item, "structNum");
                Object checkNum = ReflectUtil.getFieldValue(item, "checkNum");
                resMap.put(fac+"StructNum",structNum);
                resMap.put(fac+"CheckNum",checkNum);
            });
            resList.add(resMap);
        });
        return RestResult.success(resList);
    }


    /**
     *经常检查病害维修表（新博）
     * @param month 年月（2024-08）
     * @param orgId 机构编码
     * @return
     */
    @PostMapping("selectDssRepairPage")
    public RestResult<List<DmFinspDssRepairDto>> selectDssRepairPage(String month,String orgId){
        Page page = this.getPage();
        List<DmFinspDssRepairDto> dssRepairPage = service.selectDssRepairPage(page,month,orgId);
        page.setRecords(dssRepairPage);
        return H_RestResultHelper.returnPage(page);
    }


    /**
     * 统计结构物类型、病害数量、已维修病害数
     * @param month 年月（2024-08）
     * @param orgId 机构编码
     * @return
     */
    @PostMapping("getDssRepairStat")
    public RestResult<List<DmFinspDssRepairStatDto>> getDssRepairStat(String month,String orgId){
        List<DmFinspDssRepairStatDto> dtos = service.selectDssRepairStat(month,orgId);
        return RestResult.success(dtos);
    }
}
