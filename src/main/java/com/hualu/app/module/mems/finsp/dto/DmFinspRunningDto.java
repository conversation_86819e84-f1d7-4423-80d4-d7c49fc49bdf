package com.hualu.app.module.mems.finsp.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 正在巡查边坡
 */
@Accessors(chain = true)
@Data
public class DmFinspRunningDto implements Serializable {

    /**
     * 经常检查单ID
     */
    private String finspId;

    /**
     * 项目公司
     */
    private String mntOrgId;

    /**
     * 检查人
     */
    private String inspPerson;

    /**
     * 巡查路线
     */
    private String lineCode;

    /**
     * 项目公司名称
     */
    private String mntnOrgNm;

    /**
     * 结构物ID
     */
    private String structId;

    /**
     * 结构物名称
     */
    private String structName;
}
