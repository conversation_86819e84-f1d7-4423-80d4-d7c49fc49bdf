package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.entity.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新版经常巡查 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface NmFinspService extends IService<NmFinsp> {

    NmFinsp initNmFinsp(String facilityCat, String orgIdString,String range);

    /**
     * 获取下张检查单号
     * @param facilityCat
     * @param orgEn
     * @return
     */
    String getNextCode(String facilityCat, String orgEn, LocalDate localDate);

    void saveOrUpdateNmFinsp(NmFinsp nmDinsp);

    void delBatch(List<String> idList);

    /**
     * 修改经常检查单审批状态
     * @param processInstId
     * @param status
     */
    void updateNmFinspStatus(long processInstId, int status);

    /**
     * 根据流程ID，获取巡查单
     * @param processInstId
     * @return
     */
    List<NmFinsp> getNmFinspByProcessInstId(Long processInstId);

    /**
     * 更新病害数量
     * @param dinspId
     */
    void updateDssNum(String dinspId);


    DownloadWordDto exportWord(String finspIds, String facilityCat);

    List<String> getDssInfoImageExport(String finspIds, String facilityCat, String path);

    IPage<RouteInspection> DailyInspectionLedger(IPage page,
                                                 String dinspCode,
                                                 String facilityCats,
                                                 String lineCode,
                                                 String startStake,
                                                 String endStake,
                                                 String startDate,
                                                 String endDate,
                                                 Map reqParam);

    void exportDailyInspection(HttpServletResponse request,
                               IPage page,
                               String dinspCode,
                               String facilityCats,
                               String lineCode,
                               String startStake,
                               String endStake,
                               String startDate,
                               String endDate,
                               Map reqParam);

    /**
     * 查询经常检查情况
     * @param orgCode
     * @param year
     * @return
     */
    List<NmFinspSituationShow> querySituationShow(String orgCode, int year);

    void saveBatchNmFinsp(NmFinsp nmfinsp);

    Map<Integer, List<Long>> listProcessTask(String facilityCat);

    Map<Integer, List<Long>> listProcessTask(String facilityCat,String processInstId);

    void saveInspectionPhotos(String finspId, List<String> idList);

    void deleteInspectionPhotoById(String id);

    String getInspectionPhotos(String finspId);

    IPage<SlopeInspectionRecord> findBpDailyRecord(IPage page,
                                                   String dinspCode,
                                                   String status,
                                                   String lineCode,
                                                   String hasDssStatus,
                                                   String startDate,
                                                   String endDate);

    void findBpDailyRecord(HttpServletResponse response,
                           IPage page,
                           String dinspCode,
                           String status,
                           String lineCode,
                           String hasDssStatus,
                           String startDate,
                           String endDate);

    IPage<BridgeInspection> findQLDailyRecord(IPage page,
                                              String dinspCode,
                                              String status,
                                              String lineCode,
                                              String hasDssStatus,
                                              String startDate,
                                              String endDate);

    IPage<HdInspectionRecord> findHdDailyRecord(IPage page,
                                              String dinspCode,
                                              String status,
                                              String lineCode,
                                              String hasDssStatus,
                                              String startDate);

    void findHdDailyRecord(HttpServletResponse response,
                           IPage page,
                           String dinspCode,
                           String status,
                           String lineCode,
                           String hasDssStatus,
                           String startDate);

    void findQLDailyRecord(HttpServletResponse response,
                           IPage page,
                           String dinspCode,
                           String status,
                           String lineCode,
                           String hasDssStatus,
                           String startDate,
                           String endDate);

  List<NmFinsp> getList();

    List<Map> loadLineQlDetail(String structId);

    List<NmFinspRecord> selectViewRecord(List<String> finspIds, String facilityCat);

    List<NmFinsp> listFinspIdAndInspFrequency(List<String> noResultFinspIds);

    List<String> deleteStructData(List<String> strings,
                                  String type);
}
