package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hualu.app.module.mongo.constant.C_Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 结构物排查表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinsp对象", description="结构物排查表")
public class PcFinsp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "流程ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "结构物ID")
    @TableField("STRUCT_ID")
    private String structId;

    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @ApiModelProperty(value = "设施分类")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "排查日期")
    @TableField("INSP_DATE")
    private Date inspDate;

    @ApiModelProperty(value = "项目公司")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "巡查人")
    @TableField("INSP_PERSON")
    private String inspPerson;

    @ApiModelProperty(value = "排查单位名称")
    @TableField("INSP_ORG_NAME")
    private String inspOrgName;

    @ApiModelProperty(value = "单位类型（1：定检单位、2：养护单位、3：边坡清疏）")
    @TableField("MNT_TYPE")
    private String mntType;

    @ApiModelProperty(value = "主要隐患情况描述/边坡清理内容")
    @TableField(value = "SITUATION_DESCRIPTION",strategy = FieldStrategy.IGNORED)
    private String situationDescription;

    @ApiModelProperty(value = "病害数量")
    @TableField("DSS_NUM")
    private Integer dssNum;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "状态（0:未处置,1：处置中,2:完成处置）")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "排查单编码")
    @TableField("FINSP_CODE")
    private String finspCode;

    @ApiModelProperty(value = "水中桩基-最近水下基础检测年份；涵洞-不均匀沉降，跳车-检查结果")
    @TableField(value = "ATTR1",strategy = FieldStrategy.IGNORED)
    private String attr1;

    @ApiModelProperty(value = "水中桩基-检测最大冲刷深度（m）；涵洞-不均匀沉降，跳车-处治结论")
    @TableField(value = "ATTR2",strategy = FieldStrategy.IGNORED)
    private String attr2;

    @ApiModelProperty(value = "水中桩基-冲刷深度是否已收敛稳定或仍在发展；涵洞-边坡开裂、冲刷、滑坡等-排查结果")
    @TableField(value = "ATTR3",strategy = FieldStrategy.IGNORED)
    private String attr3;

    @ApiModelProperty(value = "水中桩基-现冲刷深度是否超原设计冲刷深度；涵洞-边坡开裂、冲刷、滑坡等-处置建议")
    @TableField(value = "ATTR4",strategy = FieldStrategy.IGNORED)
    private String attr4;

    @ApiModelProperty(value = "水中桩基-现有效桩长/埋置深度（m）；涵洞-淤塞，排水不畅-排查结果")
    @TableField(value = "ATTR5",strategy = FieldStrategy.IGNORED)
    private String attr5;

    @ApiModelProperty(value = "水中桩基-桩基钢筋是否外露、缩颈；涵洞-淤塞，排水不畅-处置建议")
    @TableField(value = "ATTR6",strategy = FieldStrategy.IGNORED)
    private String attr6;

    @ApiModelProperty(value = "水中桩基-现河床表层地质；涵洞-严重淤塞，基本失去过水功能-排查结果")
    @TableField(value = "ATTR7",strategy = FieldStrategy.IGNORED)
    private String attr7;

    @ApiModelProperty(value = "水中桩基-最大冲刷深度复算结果（m）；涵洞-严重淤塞，基本失去过水功能-处置建议")
    @TableField(value = "ATTR8",strategy = FieldStrategy.IGNORED)
    private String attr8;

    @ApiModelProperty(value = "桥面排水-泄水孔是否有淤堵；涵洞-明显沉降、开裂、变形-排查结果")
    @TableField(value = "ATTR9",strategy = FieldStrategy.IGNORED)
    private String attr9;

    @ApiModelProperty(value = "桥面排水-共多少处泄水孔存在淤堵；涵洞-明显沉降、开裂、变形-处置建议")
    @TableField(value = "ATTR10",strategy = FieldStrategy.IGNORED)
    private String attr10;

    @ApiModelProperty(value = "桥面排水-桥下落水管是否有破损、脱落；涵洞-浆砌片（块）石涵身砌体开裂、松动、外凸-排查结果")
    @TableField(value = "ATTR11",strategy = FieldStrategy.IGNORED)
    private String attr11;

    @ApiModelProperty(value = "桥面排水-共多少落水管存在破损、脱落；涵洞-浆砌片（块）石涵身砌体开裂、松动、外凸-处置建议")
    @TableField(value = "ATTR12",strategy = FieldStrategy.IGNORED)
    private String attr12;

    @ApiModelProperty(value = "桥面排水-桥下排水沟是否有破损，排水是否顺接；涵洞-基础淘空-排查结果")
    @TableField(value = "ATTR13",strategy = FieldStrategy.IGNORED)
    private String attr13;

    @ApiModelProperty(value = "桥面排水-共多少水沟存在破损、排水未顺接；涵洞-基础淘空-处置建议")
    @TableField(value = "ATTR14",strategy = FieldStrategy.IGNORED)
    private String attr14;

    @ApiModelProperty(value = "水中桩基-现冲刷深度是否超原设计冲刷深度-问题描述；涵洞-漏土或漏沙严重-排查结果")
    @TableField(value = "ATTR15",strategy = FieldStrategy.IGNORED)
    private String attr15;

    @ApiModelProperty(value = "水中桩基-桩基钢筋是否外露、缩颈-问题描述；涵洞-漏土或漏沙严重-处置建议")
    @TableField(value = "ATTR16",strategy = FieldStrategy.IGNORED)
    private String attr16;

    @ApiModelProperty(value = "涵洞-排查结论:基本无风险（四级），低风险（三级），中风险（二级），高风险（一级）")
    @TableField(value = "ATTR17",strategy = FieldStrategy.IGNORED)
    private String attr17;

    @ApiModelProperty(value = "涵洞-需要应急措施：是，否")
    @TableField(value = "ATTR18",strategy = FieldStrategy.IGNORED)
    private String attr18;

    @ApiModelProperty(value = "涵洞-需要专业检查：是，否")
    @TableField(value = "ATTR19",strategy = FieldStrategy.IGNORED)
    private String attr19;

    @ApiModelProperty(value = "涵洞-几节墙身出现裂缝")
    @TableField(value = "ATTR20",strategy = FieldStrategy.IGNORED)
    private String attr20;

    @ApiModelProperty(value = "涵洞-几节盖板出现裂缝/边坡清理人数")
    @TableField(value = "ATTR21",strategy = FieldStrategy.IGNORED)
    private String attr21;

    @ApiModelProperty(value = "养护处治类型(1:小修处治,2:专项处治,3:应急处治,4:无需处治)")
    @TableField(value = "DEAL_TYPE",strategy = FieldStrategy.IGNORED)
    private String dealType;

    @TableLogic
    @TableField(value = "DEL_FLAG",fill = FieldFill.INSERT)
    private Integer delFlag;


    /**
     * 复核人
     */
    @TableField(value = "REVIEW_USER")
    private String reviewUser;

    /**
     * 复核状态 （1：已复核，0：未复核）
     */
    @TableField(value = "REVIEW_FLAG")
    private Integer reviewFlag;

    /**
     * 复核日期
     */
    @TableField(value = "REVIEW_DATE")
    private Date reviewDate;

    /**
     * 1：已检查，0：未检查 ,2：全部
     */
    @TableField(exist = false)
    private Integer checked = 0;


    //结构物经度(打卡需要)
    @TableField(exist = false)
    private Double x;

    //结构物纬度(打卡需要)
    @TableField(exist = false)
    private Double y;

    /**
     * 边坡俯视图
     */
    @TableField(exist = false)
    private String fileId;

    /**
     * 涵洞照片：0:检查照片，1：涵洞进口全貌照片，2：涵洞出口全貌照片，3：涵洞内通视照片，4：病害代表照片,5：打草清疏前照片，6:打草清疏后照片
     */
    @TableField(exist = false)
    private List<PcFinspImage> finspImages;

    @TableField(exist = false)
    private String imageHost = C_Constant.IMAGE_HOST;

    // 边坡层级（边坡清疏）
    @TableField(exist = false)
    private String slopeLevelName;

}
