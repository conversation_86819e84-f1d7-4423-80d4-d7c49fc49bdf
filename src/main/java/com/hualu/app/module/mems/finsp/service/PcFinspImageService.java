package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结构物排查照片表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface PcFinspImageService extends IService<PcFinspImage> {

    /**
     * 根据检查记录，批量删除病害照片
     * @param recordIds
     */
    void removeByRecordIds(List<String> recordIds);

    /**
     * 根据检查记录，查询对应的病害照片
     * @param recordIds
     * @return
     */
    Map<String,List<PcFinspImage>> selectByRecordIds(List<String> recordIds);

    /**
     * 是否处治完成
     * @param recordId
     * @return
     */
    boolean isDealFinsh(String recordId);

    /**
     * 根据照片ID获取对应的病害ID
     * @param fileId
     * @return
     */
    String getRecordIdByFileId(String fileId);
}
