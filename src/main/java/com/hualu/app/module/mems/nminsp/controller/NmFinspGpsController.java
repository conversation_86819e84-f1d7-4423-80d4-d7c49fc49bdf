package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import javax.validation.Valid;
import java.util.List;
import org.assertj.core.util.Lists;
import com.hualu.app.module.mems.nminsp.entity.NmFinspGps;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspGpsMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspGpsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;

/**
 * 新版经常检查轨迹 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Api(tags = "新版经常检查轨迹",description = "新版经常检查轨迹 前端控制器")
@RestController
@RequestMapping("/nmFinspGps")
public class NmFinspGpsController extends M_MyBatisController<NmFinspGps,NmFinspGpsMapper>{

    @Autowired
    private NmFinspGpsService service;

    /**
     * 批量保存GPS数据
     * @param gpsList
     * @return
     */
    @ApiRegister(value = "nmFinspGps:saveBatchGps",businessType = BusinessType.UPDATE)
    @ApiOperation("批量保存GPS数据")
    @PostMapping("saveBatchGps")
    public RestResult<String> saveBatchGps(@RequestBody List<NmFinspGps> gpsList) {
        if (CollectionUtil.isNotEmpty(gpsList)) {
            service.saveOrUpdateBatch(gpsList, 300);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 获取轨迹数据集合
     * @param finspId 经常检查单ID
     * @return
     */
    @ApiOperation("获取轨迹数据集合")
    @GetMapping("getGpsByFinspId")
    public RestResult<List<NmFinspGps>> getGpsByFinspId(String finspId) {
        LambdaQueryWrapper<NmFinspGps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmFinspGps::getFinspId,finspId);
        List<NmFinspGps> list = service.list(queryWrapper);
        return RestResult.success(list);
    }

    /**
     * 获取实时位置信息
     * @param dinspId 经常检查单ID
     * @return
     */
    @GetMapping("getRealTimeLocationByFinspId")
    public RestResult<NmFinspGps> getRealTimeLocationByFinspId(String dinspId) {
        NmFinspGps one = baseMapper.getRealTimeLocationByFinspId(dinspId);
        return RestResult.success(one);
    }
}