package com.hualu.app.module.mems.autoInspector.utils;

import javax.imageio.*;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;
import java.awt.*;
import java.awt.image.*;
import java.io.File;
import java.io.IOException;
import java.util.Iterator;

/**
 * 图片标注工具类
 * 解决标注后图片曝光/颜色变化问题
 */
public class ImageAnnotationUtil {

    /**
     * 在图片上绘制矩形框和标注文字（使用默认样式）
     *
     * @param sourceImagePath 源图片路径
     * @param outputImagePath 输出图片路径
     * @param x 矩形框左上角x坐标
     * @param y 矩形框左上角y坐标
     * @param width 矩形框宽度
     * @param height 矩形框高度
     * @param text 标注文字
     * @throws IOException 如果图片读取或保存失败
     */
    public static void annotateImage(String sourceImagePath, String outputImagePath,
                                     int x, int y, int width, int height,
                                     String text) throws IOException {
        annotateImage(sourceImagePath, outputImagePath, x, y, width, height,
                text, Color.RED, Color.WHITE, 16, 3, true);
    }

    /**
     * 完整参数的图片标注方法
     *
     * @param sourceImagePath 源图片路径
     * @param outputImagePath 输出图片路径
     * @param x 矩形框左上角x坐标
     * @param y 矩形框左上角y坐标
     * @param width 矩形框宽度
     * @param height 矩形框高度
     * @param text 标注文字
     * @param boxColor 矩形框颜色
     * @param textColor 文字颜色
     * @param fontSize 文字大小
     * @param boxThickness 矩形框线宽
     * @param textBackground 是否显示文字背景
     * @throws IOException 如果图片读取或保存失败
     */
    public static void annotateImage(String sourceImagePath, String outputImagePath,
                                     int x, int y, int width, int height, String text,
                                     Color boxColor, Color textColor, int fontSize,
                                     int boxThickness, boolean textBackground) throws IOException {
        // 读取源图片并保持原始格式
        BufferedImage originalImage = ImageIO.read(new File(sourceImagePath));

        // 创建与原始图片相同类型的新图像
        BufferedImage image = new BufferedImage(
                originalImage.getWidth(),
                originalImage.getHeight(),
                getCompatibleImageType(originalImage));

        // 将原始图像绘制到新图像上
        Graphics2D g2d = image.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);

        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

        // 绘制矩形框
        g2d.setColor(boxColor);
        g2d.setStroke(new BasicStroke(boxThickness));
        g2d.drawRect(x, y, width, height);

        // 设置字体
        Font font = new Font("Microsoft YaHei", Font.BOLD, fontSize);
        g2d.setFont(font);

        // 计算文字位置（在矩形框上方居中显示）
        FontMetrics fontMetrics = g2d.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(text);
        int textHeight = fontMetrics.getHeight();
        int textX = x + (width - textWidth) / 2;
        int textY = y - 5; // 在矩形框上方留5像素间距

        // 绘制文字背景（可选）
        if (textBackground) {
            g2d.setColor(new Color(0, 0, 0, 150)); // 半透明黑色背景
            g2d.fillRect(textX - 2, textY - textHeight + 3, textWidth + 4, textHeight);
        }

        // 绘制文字
        g2d.setColor(textColor);
        g2d.drawString(text, textX, textY);

        // 释放资源
        g2d.dispose();

        // 保存图片（根据格式选择不同的保存方式）
        saveImageWithQuality(image, outputImagePath);
    }

    /**
     * 获取与原始图像兼容的图像类型
     */
    private static int getCompatibleImageType(BufferedImage original) {
        if (original.getTransparency() == Transparency.OPAQUE) {
            return BufferedImage.TYPE_INT_RGB;
        }
        return BufferedImage.TYPE_INT_ARGB;
    }

    /**
     * 高质量保存图片（自动处理不同格式）
     */
    private static void saveImageWithQuality(BufferedImage image, String outputPath) throws IOException {
        String formatName = outputPath.substring(outputPath.lastIndexOf(".") + 1).toLowerCase();
        File outputFile = new File(outputPath);

        // 特殊处理JPEG格式
        if ("jpg".equals(formatName) || "jpeg".equals(formatName)) {
            saveAsJpeg(image, outputFile, 0.95f); // 高质量JPEG保存
        }
        // 特殊处理PNG格式
        else if ("png".equals(formatName)) {
            ImageIO.write(image, "PNG", outputFile);
        }
        // 其他格式使用默认保存方式
        else {
            ImageIO.write(image, formatName, outputFile);
        }
    }

    /**
     * 高质量保存JPEG图片
     */
    private static void saveAsJpeg(BufferedImage image, File file, float quality) throws IOException {
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
        if (!writers.hasNext()) {
            throw new IllegalStateException("No JPEG writers found");
        }

        ImageWriter writer = writers.next();
        try (ImageOutputStream ios = ImageIO.createImageOutputStream(file)) {
            writer.setOutput(ios);

            JPEGImageWriteParam jpegParams = new JPEGImageWriteParam(null);
            jpegParams.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            jpegParams.setCompressionQuality(quality);

            writer.write(null, new IIOImage(image, null, null), jpegParams);
        } finally {
            writer.dispose();
        }
    }

    /**
     * 示例用法
     */
    public static void main(String[] args) {
        try {

            // 保存为PNG格式（无损）
            annotateImage("/Users/<USER>/project/user/test-bug/src/main/resources/img.png", "output_png.png",
                    80, 80, 220, 180, "PNG格式标注");

            System.out.println("图片标注完成！");
        } catch (IOException e) {
            System.err.println("图片处理出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}