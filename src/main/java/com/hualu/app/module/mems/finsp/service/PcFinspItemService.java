package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.PcFinspItem;

import java.util.List;

/**
 * <p>
 * 结构物排查检查内容表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface PcFinspItemService extends IService<PcFinspItem> {

    /**
     * 根据设施类型，查询检查项
     * @param facilityCat
     * @return
     */
    List<PcFinspItem> listByFacilityCat(String facilityCat);
}
