<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.machineclean.mapper.MachineCleaningTrackMapper">
  <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.machineclean.entity.MachineCleaningTrack">
    <!--@mbg.generated-->
    <!--@Table HSMSDB.MACHINE_CLEANING_TRACK-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MACHINE_CLEAN_ID" jdbcType="VARCHAR" property="machineCleanId" />
    <result column="TRACK_ID" jdbcType="VARCHAR" property="trackId" />
    <result column="LATITUDE" jdbcType="DECIMAL" property="latitude" />
    <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude" />
    <result column="RECORD_TIME" jdbcType="TIMESTAMP" property="recordTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MACHINE_CLEAN_ID, TRACK_ID, LATITUDE, LONGITUDE, RECORD_TIME
  </sql>
</mapper>