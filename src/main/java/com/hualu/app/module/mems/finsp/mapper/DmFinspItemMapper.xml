<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.DmFinspItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.DmFinspItem">
        <id column="FINSP_ITEM_ID" property="finspItemId" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="FINSP_ITEM_CODE" property="finspItemCode" />
        <result column="INSP_COM" property="inspCom" />
        <result column="INSP_CONT" property="inspCont" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="FIN_VERSION" property="finVersion" />
        <result column="IS_DELETE" property="isDelete" />
        <result column="FIN_DESC" property="finDesc" />
        <result column="FIN_REMARK" property="finRemark" />
        <result column="PART_CODE" property="partCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FINSP_ITEM_ID, FACILITY_CAT, FINSP_ITEM_CODE, INSP_COM, INSP_CONT, MNT_ORG_ID, FIN_VERSION, IS_DELETE, FIN_DESC, FIN_REMARK, PART_CODE
    </sql>

</mapper>
