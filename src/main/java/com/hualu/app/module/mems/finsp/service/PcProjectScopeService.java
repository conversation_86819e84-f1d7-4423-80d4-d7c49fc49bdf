package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.basedata.dto.near.BaseNearStructStatDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结构物专项范围表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
public interface PcProjectScopeService extends IService<PcProjectScope> {

    /**
     * 查询附件结构物
     * @param dto
     * @return
     */
    RestResult<Map<String,Object>> getNear(BaseNearStructDto dto);

    PcProjectScope getByStructId(String structId);

    List<PcProjectScope> listByMd5(List<String> md5,String prjId);

    void saveBatchByExcel(PcProject pcProject, File uploadFile);

    /**
     * 获取已检与未检数量
     * @param prjIds
     * @param facilityCat
     * @return
     */
    BaseNearStructStatDto getCheckStructIds(List<String> prjIds, String facilityCat,String mntType);

    /**
     * 统计已检核未检的数量
     * @param orgCodes
     * @param prjIds
     * @param mntType
     * @return
     */
    List<PcFinspStatDto> checkStat(List<String> orgCodes, List<String> prjIds, String mntType);

    /**
     * 查询结构类型
     * @param facilityCat
     * @param mntType
     * @return
     */
    List<String> listStructType(String facilityCat, String mntType);

    void removeByPrjId(String prjId);
}
