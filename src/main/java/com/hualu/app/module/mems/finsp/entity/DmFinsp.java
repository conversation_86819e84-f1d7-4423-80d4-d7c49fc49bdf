package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName DM_FINSP
 */
@TableName(value ="DM_FINSP")
@Data
public class DmFinsp implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "FINSP_ID")
    private String finspId;

    /**
     * 
     */
    @NotBlank(message = "经常检查单编号不能为空")
    @TableField(value = "FINSP_CODE")
    private String finspCode;

    /**
     * 
     */
    @NotBlank(message = "设施分类不能为空")
    @TableField(value = "FACILITY_CAT")
    private String facilityCat;

    /**
     * 
     */
    @NotBlank(message = "项目公司ID不能为空")
    @TableField(value = "MNT_ORG_ID")
    private String mntOrgId;

    /**
     * 
     */
    @NotBlank(message = "项目公司不能为空")
    @TableField(value = "MNTN_ORG_NM")
    private String mntnOrgNm;

    /**
     * 
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "巡查日期不能为空")
    @TableField(value = "INSP_DATE")
    private Date inspDate;

    /**
     * 
     */
    @NotBlank(message = "检查人不能为空")
    @TableField(value = "INSP_PERSON")
    private String inspPerson;

    /**
     * 
     */
    @NotBlank(message = "路线不能为空")
    @TableField(value = "LINE_CODE")
    private String lineCode;

    /**
     * 
     */
    @TableField(value = "LINE_DIRECT")
    private String lineDirect;

    /**
     * 
     */
    @TableField(value = "START_STAKE")
    private BigDecimal startStake;

    /**
     * 
     */
    @TableField(value = "END_STAKE")
    private BigDecimal endStake;

    /**
     * 
     */
    @TableField(value = "STRUCT_ID")
    private String structId;

    /**
     * 
     */
    @NotBlank(message = "结构物不能为空")
    @TableField(value = "STRUCT_NAME")
    private String structName;

    /**
     * 
     */
    @TableField(value = "TC_COMMENT")
    private String tcComment;

    /**
     * 
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 
     */
    @TableField(value = "STATUS")
    private Integer status;

    /**
     * 
     */
    @TableField(value = "CREATE_USER_ID",fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 
     */
    @TableField(value = "CREATE_TIME",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "UPDATE_USER_ID",fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 
     */
    @TableField(value = "UPDATE_TIME",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 
     */
    @TableField(value = "PROCESSINSTID")
    private Long processinstid;

    /**
     * 
     */
    @TableField(value = "FILE_ID")
    private String fileId;

    /**
     * 
     */
    @TableField(value = "ROAD_SECTION")
    private String roadSection;

    /**
     * 
     */
    @TableField(value = "SEARCH_DEPT")
    private String searchDept;

    /**
     * 
     */
    @TableField(value = "INSP_END_DATE")
    private Date inspEndDate;

    /**
     * 
     */
    @TableField(value = "FIN_VERSION")
    private String finVersion;

    /**
     * 处理结果
     */
    @TableField(value = "OPINIONS")
    private String opinions;

    /**
     * 
     */
    @TableField(value = "RESULTS")
    private String results;

    /**
     * 
     */
    @TableField(value = "WEATHER")
    private String weather;

    /**
     * 病害数量
     */
    @TableField(value = "DSS_NUM")
    private Integer dssNum;

    /**
     * 边坡类型
     */
    @TableField(value = "SLOPE_TYPE")
    private String slopeType;

    /**
     * 边坡位置
     */
    @TableField(value = "SLOPE_POSITION")
    private String slopePosition;

    /**
     * 边坡风险等级
     */
    @TableField(value = "SLOPE_FXDJ")
    private String slopeFxdj;

    /**
     * 边坡灾害类型
     */
    @TableField(value = "slope_zhlx")
    private String slopeZhlx;

    /**
     * 边坡破坏后缘位置
     */
    @TableField(value = "SLOPE_PH_HYWZ")
    private String slopePhHywz;

    /**
     * 边坡破坏出口位置
     */
    @TableField(value = "SLOPE_PH_CKWZ")
    private String slopePhCkwz;

    /**
     *灾害到达范围
     */
    @TableField(value = "SLOPE_ZK_FW")
    private String slopeZkFw;

    /**
     *承灾对象
     */
    @TableField(value = "SLOPE_CZDX")
    private String slopeCzdx;

    /**
     * 项目类型（1 经常检查， 2 安全隐患排查）
     */
    @TableField(value = "PROJECT_TYPE")
    private Integer projectType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String fileIds;//图片ID，以逗号分隔

    @TableField(exist = false)
    private String imageHost;//图片前缀

    @TableField(exist = false)
    private String lineName;//路线名称

    @TableField(exist = false)
    private String slopeTypeName;//边坡类型名称
    @TableField(exist = false)
    private String slopePositionName; //边坡位置名称
    @TableField(exist = false)
    private String slopeFxdjName;//边坡风险等级名称
    @TableField(exist = false)
    private String slopeZhlxName; //边坡灾害类型名称
    @TableField(exist = false)
    private String weatherName;//天气名称

    /**
     * 是否包含原始轨迹 1:包含，0：未包含
     */
    @TableField(exist = false)
    private Integer hasSourceTrack = 0;
}