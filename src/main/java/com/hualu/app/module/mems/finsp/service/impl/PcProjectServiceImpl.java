package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.mapper.PcProjectMapper;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.hualu.app.module.mems.finsp.service.PcProjectService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.api.util.hp.H_UniqueConstraintHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 结构物专项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
public class PcProjectServiceImpl extends ServiceImpl<PcProjectMapper, PcProject> implements PcProjectService {

    @Autowired
    FwRightOrgService orgService;

    @Autowired
    PcProjectScopeService scopeService;

    @Override
    public List<PcProject> listProjectByLast(String orgId, int year, String mntType, String facilityCat) {
        List<String> orgCodes = orgService.selectChildOprtOrgCodes(orgId);
        LambdaQueryWrapper<PcProject> queryWrapper = new LambdaQueryWrapper<>();
        //.eq(PcProject::getPrjYear,year) 不能根据年份查询最后的项目
        queryWrapper.in(PcProject::getMntOrgId,orgCodes)
                .eq(StrUtil.isNotBlank(facilityCat),PcProject::getFacilityCat,facilityCat).eq(PcProject::getLatest,1);
        if (StrUtil.isNotBlank(mntType) && (mntType.equals("1") || mntType.equals("2"))) {
            queryWrapper.eq(StrUtil.isNotBlank(mntType),PcProject::getMntType,mntType);
        }
        return list(queryWrapper);
    }


    @Override
    public List<String> listPrjId(String orgId, int year, String mntType, String facilityCat) {
        List<PcProject> pcProjects = listProjectByLast(orgId, year, mntType,facilityCat);
        return pcProjects.stream().map(PcProject::getPrjId).collect(Collectors.toList());
    }


    private PcProject getByMd5(String md5) {
        LambdaQueryWrapper<PcProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcProject::getMd5,md5).eq(PcProject::getLatest,1);
        return getOne(queryWrapper);
    }

    /**
     * 设置其他项目的latest=0
     * @param pcProject
     */
    private void setPrjHistory(PcProject pcProject) {
        LambdaUpdateWrapper<PcProject> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PcProject::getMntOrgId,pcProject.getMntOrgId());
        updateWrapper.eq(PcProject::getMntType,pcProject.getMntType())
                .eq(PcProject::getFacilityCat,pcProject.getFacilityCat());
        updateWrapper.set(PcProject::getLatest,0);
        update(updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PcProject createProject(PcProject pcProject,File uploadFile) {
        pcProject.setLatest(1).setMntOrgId(CustomRequestContextHolder.getOrgIdString())
                .setPrjYear(DateUtil.thisYear());
        String unique = H_UniqueConstraintHelper.createUnique(pcProject);
        pcProject.setMd5(unique);
        // 查询数据库是否存在相同的数据
        PcProject dbProject = getByMd5(unique);
        if (dbProject == null) {
            pcProject.setPrjId(H_KeyWorker.nextIdToString());
            setPrjHistory(pcProject);
            save(pcProject);
        }else {
            // 使用数据库存在的项目ID
            pcProject.setPrjId(dbProject.getPrjId());
            updateById(pcProject);
        }
        scopeService.saveBatchByExcel(pcProject, uploadFile);
        return pcProject;
    }
}
