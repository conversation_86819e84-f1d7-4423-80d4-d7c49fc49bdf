package com.hualu.app.module.mems.finsp.upload;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.word.WordUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.service.PcFinspRecordService;
import com.hualu.app.module.mems.finsp.service.PcFinspResultService;
import com.hualu.app.module.mems.finsp.service.PcFinspService;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;

@Service
public class BridgeImportImp implements IImportStruct{

    private static List<String> excelHeaderTemplates = Lists.newArrayList("路线编码","桥梁名称","桥梁桩号","桥梁跨径","设计单位");

    @Lazy
    @Autowired
    PcProjectScopeService scopeService;

    @Lazy
    @Autowired
    PcFinspService pcFinspService;

    @Autowired
    PcFinspResultService resultService;

    @Autowired
    PcFinspRecordService recordService;

    @Value("${fileReposity}")
    String fileReposity;

    @Override
    public String getFacilityCat() {
        return "QL";
    }

    @Override
    public List<PcProjectScope> doImport(PcProject pcProject, File uploadFile) {
        ExcelReader reader = ExcelUtil.getReader(uploadFile);
        //获取excel表头
        List<Object> excelHeaders = reader.read(0).get(0);
        excelHeaderTemplates.removeAll(excelHeaders);
        if (excelHeaderTemplates.size()>0){
            throw new BaseException("excel模版不匹配,未包含列："+ StrUtil.join("2",excelHeaderTemplates));
        }
        reader.addHeaderAlias("路线编码","lineCode");
        reader.addHeaderAlias("桥梁名称","structName");
        reader.addHeaderAlias("桥梁桩号","structStake");
        reader.addHeaderAlias("桥梁跨径","structLevel");
        reader.addHeaderAlias("设计单位","designUnit");
        List<PcProjectScope> pcProjectScopes = reader.readAll(PcProjectScope.class);
        return pcProjectScopes;
    }

    @Override
    public List<BaseNearStructDto> toBaseNearStructDto(List<PcProjectScope> pcProjectScopes) {
        List<BaseNearStructDto> structDtos = Lists.newArrayList();
        for (PcProjectScope item : pcProjectScopes) {
            BaseNearStructDto dto = new BaseNearStructDto();
            dto.setStructName(item.getStructName());
            dto.setStructStake(item.getStructStake());
            dto.setStructLevel(item.getStructLevel());
            dto.setX(item.getX());
            dto.setY(item.getY());
            dto.setFacilityCat(item.getFacilityCat());
            dto.setLineDirect(item.getLineDirect());
            dto.setStructId(item.getStructId());
            dto.setOprtOrgCode(item.getMntOrgId());
            structDtos.add(dto);
        }
        return structDtos;
    }

    @SneakyThrows
    @Override
    public String exportWord(PcFinsp pcFinsp,String qlType) {
        List<PcProjectExportDto> projectDtos = pcFinspService.exportWordData(Lists.newArrayList(pcFinsp.getFinspId()), null);
        if (CollectionUtil.isEmpty(projectDtos)){
            throw new BaseException("当前检查单不存在");
        }
        String name = "szz".equalsIgnoreCase(qlType)?"_水中桩":"_桥面排水";
        String templateType = "szz".equalsIgnoreCase(qlType)?"bridge-szz":"bridge-ps";
        Map xxMap = BeanUtil.toBean(projectDtos.get(0), Map.class);
        initRadio(xxMap);
        String fileName = File.separator + projectDtos.get(0).getFinspCode()+name + ".docx";
        //ClassPathResource classPathResource = new ClassPathResource("excel/pcFinsp/"+templateType+".docx");
        InputStream resourceAsStream = WordUtil.class.getClassLoader().getResourceAsStream("excel/pcFinsp/" + templateType + ".docx");
        File file = new File(fileReposity + fileName);
        ConfigureBuilder builder = Configure.builder();
        XWPFTemplate template = XWPFTemplate.compile(resourceAsStream, builder.build()).render(xxMap);
        try(OutputStream outputStream = Files.newOutputStream(file.toPath())){
            template.writeAndClose(outputStream);
        }
        resourceAsStream.close();
        return fileName;
    }

    @Override
    public void exportWordByZip(String filePath, List<String> finspIds, String prjId) {
        List<PcProjectExportDto> projectDtos = pcFinspService.exportWordData(finspIds, prjId);
        int idx = 0;
        for (PcProjectExportDto projectDto : projectDtos) {
            projectDto.setIndex(++idx);
            exportWordWithQLAndHD(filePath,projectDto,"bridge-szz","_水中桩基");
            exportWordWithQLAndHD(filePath,projectDto,"bridge-ps","_桥面排水系统");
        }
        exportExcelWithQLAndHD(filePath,projectDtos,"bridge-szz","水中桩基");
        exportExcelWithQLAndHD(filePath,projectDtos,"bridge-ps","桥面排水系统");
    }
}
