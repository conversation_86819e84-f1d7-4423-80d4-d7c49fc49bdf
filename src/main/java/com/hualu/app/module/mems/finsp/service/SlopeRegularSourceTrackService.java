package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.SlopeRegularSourceTrack;

import java.util.List;
import java.util.Set;

public interface SlopeRegularSourceTrackService extends IService<SlopeRegularSourceTrack>{

    /**
     * 有原始轨迹的边坡
     * @param slopeIds
     * @return
     */
    Set<String> hasSourceTack(List<String> slopeIds);
}
