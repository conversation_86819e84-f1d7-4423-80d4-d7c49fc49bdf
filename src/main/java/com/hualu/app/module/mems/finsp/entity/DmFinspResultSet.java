package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmFinspResultSet对象", description="")
public class DmFinspResultSet implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ORG_CODE")
    private String orgCode;

    @TableField("USER_CODE")
    private String userCode;


}
