package com.hualu.app.module.mems.task.dto.accpt;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 维修工程记录编辑对象
 */
@Data
public class DmAccptUpdateDto implements Serializable {

    //病害ID
    @NotBlank
    private String dssId;

    @NotBlank
    @ApiModelProperty(value = "验收单明细ID")
    @TableId("MTASK_ACCPT_DTL_ID")
    private String mtaskAccptDtlId;

    @NotNull
    @ApiModelProperty(value = "工程数量")
    @TableField("MPITEM_NUM")
    private Double mpitemNum;

    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("REPAIR_DATE")
    private LocalDate repairDate;

    @NotBlank
    //是否包干 0:否 1：是
    private Integer isLump;
}
