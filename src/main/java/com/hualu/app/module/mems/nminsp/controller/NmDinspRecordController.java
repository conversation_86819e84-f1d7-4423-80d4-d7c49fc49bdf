package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspRecordMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.utils.H_PageHelper;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新版日常巡查记录表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Validated
@Api(tags = "日常巡查记录表",description = "新版日常巡查记录表 前端控制器")
@RestController
@RequestMapping("/nmDinspRecord")
public class NmDinspRecordController extends M_MyBatisController<NmDinspRecord,NmDinspRecordMapper>{

    @Autowired
    private NmDinspRecordService service;

    @Lazy
    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    DssImageService imageService;

    @Autowired
    NmInspContentService contentService;

    /**
    * 分页查询
    * @return
    */
    @ApiRegister(value = "nmDinspRecord:page",businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    public RestResult<List<NmDinspRecord>> selectListByDinspId(String dinspId) {
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        List<NmDinspRecord> records = iPage.getRecords();
        service.showView(records,nmDinspService.getById(dinspId));
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 路面检查结论
     * @param dinspId
     * @return
     */
    @GetMapping("listLmResultByDinspId/{dinspId}")
    public RestResult<List<NmDinspRecord>> listLmResultByDinspId(@PathVariable("dinspId") String dinspId) {
        List<NmDinspRecord> nmDinspRecords = service.selectRecordByDinspId(dinspId);
        if (CollectionUtil.isEmpty(nmDinspRecords)){
            return RestResult.success(Lists.newArrayList());
        }
        service.showView(nmDinspRecords,nmDinspService.getById(dinspId));
        List<NmInspContent> contents = contentService.listByContent("LM", null,null);

        for (NmDinspRecord item : nmDinspRecords) {
            //当病害记录的病害类型==部位的dssType，巡查内容设置为病害类型
            NmInspContent nmInspContent = contents.stream().filter(e -> StrUtil.isNotBlank(e.getDssType()) && item.getStructPartId().equals(item.getStructPartId())
                    && e.getDssType().contains(item.getDssType())).findFirst().orElse(null);
            if (nmInspContent != null) {
                item.setDssTypeName(nmInspContent.getName());
            }
        }
        return RestResult.success(nmDinspRecords);
    }

    /**
    * 根据主键查询
    * @return
    */
    @ApiRegister(value = "nmDinspRecord:getById",businessType = BusinessType.OTHER)
    @ApiOperation("根据主键查询")
    @GetMapping("getById/{id}")
    public Object getById(@PathVariable String id) {
        return service.getById(id);
    }

    /**
    * 添加或者修改
    * @param entity
    * @return
    */
    @ApiRegister(value = "nmDinspRecord:saveOrUpdate",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改")
    @PostMapping("saveOrUpdate")
    public RestResult<String> saveOrUpdate(@RequestBody @Validated NmDinspRecord entity) {
        H_StructHelper.validateNmDinspRecord(entity);
        service.saveOrUpdateRecordForPC(entity);
        return RestResult.success("操作成功");
    }

    /**
    * 删除
    */
    @ApiRegister(value = "nmDinspRecord:delIds",businessType = BusinessType.DELETE)
    @ApiOperation("删除（dssIds为字符串，以逗号分割）")
    @PostMapping("delIds")
    public RestResult<String> delIds(String dssIds){
        List<String> idList = StrUtil.split(dssIds, ",");
        service.deleteRecord(idList);
        return RestResult.success("操作成功");
    }

    /**
     * 删除病害照片
     * @param fileId
     * @return
     */
    @GetMapping("delImage")
    public RestResult<String> deleteImage(String fileId){
        imageService.delByFileId(fileId);
        return RestResult.success("操作成功");
    }
}