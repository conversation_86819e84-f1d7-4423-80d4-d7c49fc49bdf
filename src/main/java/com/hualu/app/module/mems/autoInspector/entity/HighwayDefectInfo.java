package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.time.LocalDateTime;
import java.util.Date;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 高速公路病害信息表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_DEFECT_INFO")
@ApiModel(value="HighwayDefectInfo对象", description="高速公路病害信息表")
public class HighwayDefectInfo implements Serializable {

    
    @NotBlank(message="[病害唯一ID]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("病害唯一ID")
    @TableField("ID")
    private String id;
    
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("病害类型0,1,2,1000等")
    @TableField("DEFECT_TYPE")
    private String defectType;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("路产类型")
    @TableField("EQUIPMENT_TYPE")
    private String equipmentType;
    
    @ApiModelProperty("采集时间")
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;
    
    @ApiModelProperty("更新时间")
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;
    
    @Size(max= 10,message="编码长度不能超过10")
    @ApiModelProperty("病害状态")
    @TableField("STATE")
    private String state;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("开始桩号")
    @TableField("STAKE_START")
    private String stakeStart;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("结束桩号")
    @TableField("STAKE_END")
    private String stakeEnd;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("几何类型，如Point")
    @TableField("GEOM_TYPE")
    private String geomType;
    
    @ApiModelProperty("经度坐标")
    @TableField("LONGITUDE")
    private BigDecimal longitude;
    
    @ApiModelProperty("纬度坐标")
    @TableField("LATITUDE")
    private BigDecimal latitude;
    
    @ApiModelProperty("病害面积（平方米）")
    @TableField("TARGET_AREA")
    private BigDecimal targetArea;
    
    @ApiModelProperty("病害长度（米）")
    @TableField("TARGET_LEN")
    private BigDecimal targetLen;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("病害快照ID")
    @TableField("SNAPSHOT_ID")
    private String snapshotId;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("任务ID")
    @TableField("TASK_ID")
    private String taskId;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("路段ID")
    @TableField("SEGMENT_ID")
    private String segmentId;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("路段名称")
    @TableField("SEGMENT_NAME")
    private String segmentName;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("上下行信息")
    @TableField("INSPECT_DIRECTION")
    private String inspectDirection;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("数据来源，离线上传：offline，实时上传：Inspect")
    @TableField("DATA_FROM")
    private String dataFrom;
    
    @ApiModelProperty("是否预警，0：否，1：是")
    @TableField("WARNING_FLAG")
    private Integer warningFlag;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("关联路产实体ID")
    @TableField("EQUIPMENT_ID")
    private String equipmentId;
    
    @ApiModelProperty("相比上次的变化状态，0,1,2")
    @TableField("TRANSFORMATION")
    private Integer transformation;
    
    @ApiModelProperty("数据状态，0,1,2")
    @TableField("DATA_STATUS")
    private Integer dataStatus;
    
    @ApiModelProperty("病害与车顶天线距离")
    @TableField("OBJ_DISTANCE")
    private BigDecimal objDistance;
    
    @ApiModelProperty("公里桩是否校准，0：未校准，1：已校准")
    @TableField("IS_CALIBRATED")
    private Integer isCalibrated;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("车道号，多个车道以逗号分隔")
    @TableField("LANE_NUM")
    private String laneNum;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("对象类型")
    @TableField("OBJECT_TYPE")
    private String objectType;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("对象子类型")
    @TableField("OBJECT_SUBTYPE")
    private String objectSubtype;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("病害类型名称")
    @TableField("DSS_TYPE_NAME")
    private String dssTypeName;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("设施类型名称")
    @TableField("CAT_NAME")
    private String catName;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("结构物名称/路面类型名称")
    @TableField("TYPE_NAME")
    private String typeName;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("养护平台病害类型编码")
    @TableField("YH_DSS_TYPE")
    private String yhDssType;
    
    @ApiModelProperty("巡查类型（1，日常，2，经常）")
    @TableField("INSP_TYPE")
    private Integer inspType;
    
    @ApiModelProperty("是否已建单（0，否，1，是）")
    @TableField("IS_CREATED")
    private Integer isCreated;

    @ApiModelProperty("原始ID")
    @TableField("OG_ID")
    private String ogId;
}
