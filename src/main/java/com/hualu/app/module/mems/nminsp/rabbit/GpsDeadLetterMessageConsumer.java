package com.hualu.app.module.mems.nminsp.rabbit;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.config.InspQueueConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.log4j.Log4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Log4j
@Service
public class GpsDeadLetterMessageConsumer {

    final String RETRY_NAME = "retryCount";
    @Autowired
    RabbitTemplate rabbitTemplate;

    //@RabbitListener(queues = InspQueueConfig.GPS_DEAD_LETTER_QUEUE_NAME,containerFactory = "gpsQueueContainerFactory")
    public void receiveDeadLetterMessage(Message message, Channel channel) {

        /*try {
            //log.error("Gps轨迹异常: " + content);
            // 这里可以添加对死信消息的特殊处理逻辑，如记录日志、人工干预等
            int retryCount = getRetryCount(message);
            if (retryCount >= InspQueueConfig.RETRY_COUNT) {
                String content = StrUtil.str(message.getBody(), CharsetUtil.CHARSET_UTF_8);
                // 达到最大重试次数，丢弃消息
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                System.out.println("Message discarded after reaching max retry count: " + content);
                log.error("Message discarded after reaching max retry count: " + content);
                return;
            }
            // 未达到最大重试次数，重新放回正常队列进行重试
            message.getMessageProperties().setHeader(RETRY_NAME, retryCount + 1);
            rabbitTemplate.send(message.getMessageProperties().getReceivedExchange(),
                    message.getMessageProperties().getReceivedRoutingKey(), message);
        }catch (Exception e) {
            e.printStackTrace();
        }*/
        String content = StrUtil.str(message.getBody(), CharsetUtil.CHARSET_UTF_8);
        log.error("消息被丢弃:"+content);
    }

    private int getRetryCount(Message message) {
        Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get(RETRY_NAME);
        return retryCount == null ? 0 : retryCount;
    }
}    