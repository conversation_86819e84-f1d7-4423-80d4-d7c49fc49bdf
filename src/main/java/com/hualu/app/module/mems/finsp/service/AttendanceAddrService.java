package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.AttendanceAddr;

import java.util.List;

/**
 * <p>
 * 打卡点表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
public interface AttendanceAddrService extends IService<AttendanceAddr> {

    /**
     * 查询打卡点
     * @param structId
     * @param facilityCat
     * @return
     */
    List<AttendanceAddr> selectAddr(String structId, String facilityCat);
}
