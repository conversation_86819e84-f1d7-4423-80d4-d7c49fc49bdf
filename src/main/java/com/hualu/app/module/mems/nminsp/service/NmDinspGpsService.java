package com.hualu.app.module.mems.nminsp.service;

import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 新版日常巡查轨迹 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
public interface NmDinspGpsService extends IService<NmDinspGps> {

  List<NmDinspGps> getGpsByDinspIdParallel(String dinspId);

  boolean saveNmDinspGpsData(List<NmDinspGps> gpsList);
}
