package com.hualu.app.module.mems.autoInspector.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayCategoryDictionary;
import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectMedia;
import com.hualu.app.module.mems.autoInspector.mapper.HighwayDefectInfoMapper;
import com.hualu.app.module.mems.autoInspector.service.HighwayDefectInfoService;
import com.hualu.app.module.mems.autoInspector.service.HighwayCategoryDictionaryService;
import com.hualu.app.module.mems.autoInspector.service.HighwayDefectMediaService;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_DEFECT_INFO(高速公路病害信息表)】的数据库操作Service实现
* @createDate 2025-04-27 09:02:29
*/
@Service
public class HighwayDefectInfoServiceImpl extends ServiceImpl<HighwayDefectInfoMapper, HighwayDefectInfo>
    implements HighwayDefectInfoService {

    @Resource
    private HighwayCategoryDictionaryService categoryDictionaryService;

    @Resource
    private DssTypeNewService dssTypeNewService;
    @Autowired
    private HighwayDefectMediaService highwayDefectMediaService;

    @Override
    public DssTypeNew getDefectTypeMapping(String defectType, String objectType, String objectSubtype) {
        // 1. 获取病害类型名称
        HighwayCategoryDictionary defectCategory = categoryDictionaryService.lambdaQuery()
            .eq(HighwayCategoryDictionary::getCategoryCode, defectType)
            .one();

        if (defectCategory == null) {
            return null;
        }

        // 2. 处理特殊名称转换
        String categoryName = defectCategory.getCategoryName();
        if ("带状修补".equals(categoryName)) {
            categoryName = "条状修补";
        }

        // 3. 查询病害类型映射
        // 使用子查询来解决排序和限制行数的问题
        String sql = "SELECT * FROM (SELECT * FROM DSS_TYPE_NEW WHERE DSS_TYPE_NAME = {0} AND IS_DELETED = {1} " +
                     "ORDER BY CASE WHEN DSS_TYPE LIKE 'LM%' THEN 0 ELSE 1 END, DSS_TYPE DESC) WHERE ROWNUM = 1";

        return dssTypeNewService.getBaseMapper().selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<DssTypeNew>()
                .apply(sql, categoryName, 0)
        );
    }

    @Override
    public void removeOldData(String dateStr, String routeName) {
        QueryWrapper<HighwayDefectInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("to_char(CREATED_TIME,'yyyy-MM-dd') = {0}", dateStr);
        LambdaQueryWrapper<HighwayDefectInfo> lambda = queryWrapper.lambda();
        lambda.eq(HighwayDefectInfo::getSegmentName, routeName);
        List<HighwayDefectInfo> list = list(lambda);
        if(list != null && !list.isEmpty()){
            List<String> ids = list.stream().map(HighwayDefectInfo::getId).collect(Collectors.toList());
            int size = ids.size();
            if(size >= 999){
                for(int i = 0; i < size; i += 900){
                    int end = Math.min(i + 900, size);
                    List<String> subIds = ids.subList(i, end);
                    highwayDefectMediaService.remove(new LambdaQueryWrapper<HighwayDefectMedia>().in(HighwayDefectMedia::getDefectId, subIds));
                    removeByIds(subIds);
                }
            }
        }
    }
}




