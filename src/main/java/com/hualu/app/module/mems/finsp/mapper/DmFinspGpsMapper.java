package com.hualu.app.module.mems.finsp.mapper;

import com.hualu.app.module.mems.finsp.entity.DmFinspGps;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 经常检查单GPS位置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface DmFinspGpsMapper extends BaseMapper<DmFinspGps> {

    DmFinspGps getRealTimeLocationByFinspId(@Param("finspId") String finspId);
}
