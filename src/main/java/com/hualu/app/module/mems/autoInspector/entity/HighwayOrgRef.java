package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 巡检系统路段关联表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_ORG_REF")
@ApiModel(value="HighwayOrgRef对象", description="巡检系统路段关联表")
public class HighwayOrgRef implements Serializable {

    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("巡检系统项目名称")
    @TableField("ORG_NAME")
    private String orgName;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("巡检系统路段名称")
    @TableField("ROUTE_NAME")
    private String routeName;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("养护平台组织机构名称")
    @TableField("YH_ORG_NAME")
    private String yhOrgName;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("养护平台路段名称")
    @TableField("YH_ROUTE_NAME")
    private String yhRouteName;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("养护平台组织机构编码")
    @TableField("YH_ORG_CODE")
    private String yhOrgCode;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("巡检系统路段ID")
    @TableField("ROUTE_ID")
    private String routeId;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("养护平台路段编码")
    @TableField("YH_ROUTE_CODE")
    private String yhRouteCode;

    @ApiModelProperty("养护平台单位英文")
    @TableField("ORG_EN")
    private String orgEn;
}
