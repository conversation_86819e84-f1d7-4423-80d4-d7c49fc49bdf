package com.hualu.app.module.mems.task.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccpt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维修验收单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskAccptMapper extends BaseMapper<DmTaskAccpt> {

    List<Map<String, String>> selectFacilityCat(@Param("mtaskId") String mtaskId);

    List<DmTaskAccptDetailDto> selectAccptDetails(@Param("facilityCat") String facilityCat,@Param("mtaskId") String mtaskId);

    Integer selectNextCode(@Param("codePrefix") String codePrefix, @Param("orgId") String orgId);

    DmTaskAccpt selectLast(@Param("orgId") String orgId);

    @Update("update DM_TASK_ACCPT_DETAIL set MTASK_ACCPT_ID='',APPLY_AMOUNT=MPITEM_NUM,ACCEPT_AMOUNT=MPITEM_NUM,ACCEPT_STATUS=0 ${ew.customSqlSegment} ")
    void updateAccptNull(@Param(Constants.WRAPPER) LambdaQueryWrapper<DmTaskAccptDetail> queryWrapper);
}
