package com.hualu.app.module.mems.finsp.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.basedata.service.HsmsSlopeInfoService;
import com.hualu.app.module.basedata.service.HsmsSlopePicService;
import com.hualu.app.module.facility.impl.FacBpImpl;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspGps;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.mapper.PcFinspMapper;
import com.hualu.app.module.mems.finsp.service.*;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 结构物排查表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Api(tags = "结构物排查表",description = "结构物排查表 前端控制器")
@RestController
@RequestMapping("/pcFinsp")
public class PcFinspController extends M_MyBatisController<PcFinsp,PcFinspMapper>{

    @Autowired
    private PcFinspService service;

    @Autowired
    FacBpImpl bpImp;

    @Autowired
    HsmsSlopeInfoService slopeInfoService;

    @Autowired
    HsmsSlopePicService picService;

    @Autowired
    FwRightUserService userService;

    @Autowired
    PcFinspResultService resultService;

    @Autowired
    PcProjectScopeService scopeService;

    @Autowired
    PcFinspRecordService pcFinspRecordService;

    @Autowired
    private PcFinspImageService imageService;

    @Autowired
    PcFinspGpsService gpsService;

    @Value("${fileReposity}")
    String fileReposity;


    /**
     * 导出检查单
     * @param finspId 检查单ID
     * @param qlType ps:桥面排水  szz:水中桩
     * @return
     */
    @GetMapping("exportWord")
    @SneakyThrows
    public RestResult<String> exportWord(String finspId,String qlType){
        String fileName = service.exportWord(finspId,qlType);
        return RestResult.success("fileResposity"+fileName,"操作成功");
    }

    /**
     * 批量导出word以及excel
     * @param finspIds 排查单ID，多个逗号分隔
     * @param prjId 项目ID
     * @param facilityCat 设施类型 QL、HD、BP
     * @return
     */
    @PostMapping("exportWordByZip")
    public RestResult<String> exportWordByZip(String finspIds, String prjId, String facilityCat){
        String path = service.exportWordByZip(finspIds,prjId,facilityCat);
        return RestResult.success("fileResposity/"+path,"操作成功");
    }

    /**
     * 删除排查单
     * @param finspId
     * @return
     */
    @GetMapping("/delete/{finspId}")
    public RestResult<String> deleteFinsp(@PathVariable("finspId") String finspId){
        service.delPcFinsp(finspId);
        return RestResult.success("操作成功");
    }


    /**
     * 获取主单详情
     * @param finspId
     * @return
     */
    @GetMapping("/getById/{finspId}")
    public RestResult<PcFinsp> getById(@PathVariable("finspId") String finspId){
        PcFinsp pcFinsp = service.getById(finspId);
        initXy(pcFinsp);
        return RestResult.success(pcFinsp);
    }

    /**
     * 附近排查结构物（大排查大整治）
     * @param facilityCat 设施类型 BP
     * @param isNear  是否附近查询 1：是，0：否
     * @param checked  3:已复核，1：已检查，0：未检查 ,2：全部
     * @param x 经度
     * @param y 纬度
     * @param structName 结构物名称，用于模糊搜索
     * @param mntType 单位类型（1：定检单位，2：养护单位,3：边坡清疏）
     * @param structType 结构类型（边坡：路堤、路堑；涵洞类型） ，多选以逗号分隔
     * @return
     */
    @PostMapping("getNearByStruct")
    public RestResult<Map<String,Object>> getNearByStruct(@RequestParam(name = "facilityCat",defaultValue = "BP") String facilityCat
            , @RequestParam(required = true,defaultValue = "0") Integer isNear
            , @RequestParam(required = true,defaultValue = "2") Integer checked
            , Double x, Double y, String structName
            ,@RequestParam(name = "mntType",defaultValue = "1") String mntType
            ,@RequestParam(name = "structType",defaultValue = "") String structType){
        Map reqParam = getReqParam();
        BaseNearStructDto dto = BeanUtil.toBean(reqParam, BaseNearStructDto.class);
        if (dto.getIsNear().equals(1) && (dto.getX() == null || dto.getY() == null)){
            throw new BaseException("经纬度信息不能为空");
        }
        RestResult<Map<String,Object>> restResult = scopeService.getNear(dto);
        return restResult;
    }

    /**
     * 返回结构类型
     * @param facilityCat 设施类型 BP
     * @param mntType 单位类型（1：定检单位，2：养护单位,3：边坡清疏）
     * @return
     */
    @GetMapping("listStructType")
    public RestResult<List<String>> listStructType(@RequestParam(name = "facilityCat",defaultValue = "BP") String facilityCat
            ,@RequestParam(name = "mntType",defaultValue = "1") String mntType){
        List<String> structTypeList = scopeService.listStructType(facilityCat,mntType);
        return RestResult.success(structTypeList);
    }

    /**
     * 根据当前日期，返回当前季度对应的经常检查单
     * @param structId 结构ID
     * @param facilityCat 设施类型
     * @param mntType 单位类型（1：定检单位，2：养护单位,3：边坡清疏）
     * @return
     */
    @PostMapping("listByQuarter")
    public RestResult<List<PcFinsp>> listByQuarter(String structId, String facilityCat,@RequestParam(name = "mntType",defaultValue = "1") String mntType){
        List<PcFinsp> dmFinsps = service.listByQuarter(structId, facilityCat, new Date(),mntType);
        if (CollectionUtil.isNotEmpty(dmFinsps)){
            return RestResult.success(dmFinsps);
        }
        return RestResult.error("本季度暂无排查单");
    }

    /**
     * 新建排查单
     * @param structId structId 结构物ID
     * @param mntType 单位类型（1：定检单位、2：养护单位）
     * @return
     */
    @PostMapping("createFinspByStructId")
    public RestResult<PcFinsp> createFinspByStructId(String structId,String mntType,@RequestParam(defaultValue = "BP") String facilityCat){
        String finspId = service.getFinspId(structId, facilityCat, new Date(),mntType);
        PcFinsp pcFinsp = new PcFinsp();
        if (StrUtil.isBlank(finspId)){
            //根据结构物ID，查询结构信息
            PcProjectScope slopeInfo = scopeService.getByStructId(structId);
            if (slopeInfo == null){
                throw new BaseException("结构物不存在");
            }
            Object en = CustomRequestContextHolder.get("ORG_EN");
            String orgEn = en == null ? "" : en.toString();
            String finspCode = service.getNextCode(facilityCat, orgEn);
            pcFinsp.setFinspCode(finspCode);
            pcFinsp.setFacilityCat(facilityCat);
            pcFinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
            pcFinsp.setStructName(slopeInfo.getStructName());
            pcFinsp.setStructId(structId);
            pcFinsp.setInspDate(new Date());
            pcFinsp.setMntType(mntType);
            pcFinsp.setStatus("0");
            pcFinsp.setInspPerson(CustomRequestContextHolder.getUserName());
            pcFinsp.setInspOrgName(slopeInfo.getInspOrgName());
            service.saveOrUpdateFinsp(pcFinsp);
        }else {
            pcFinsp.setFinspId(finspId);
        }
        PcFinsp dbFinsp = service.getById(pcFinsp.getFinspId());
        initXy(dbFinsp);

        return RestResult.success(dbFinsp);
    }

    /**
     * 添加或修改排查主单
     * @param pcFinsp
     * @return
     */
    @PostMapping("saveOrUpdate")
    public RestResult<PcFinsp> saveOrUpdate(@RequestBody PcFinsp pcFinsp){
        service.saveOrUpdateFinsp(pcFinsp);
        PcFinsp dbFinsp = service.getById(pcFinsp.getFinspId());
        initXy(dbFinsp);
        return RestResult.success(dbFinsp);
    }

    /**
     * 修改排查主单，并实现照片上传
     * @param data
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @PostMapping("updateWithFile")
    public RestResult<PcFinsp> updateWithFile(
        @RequestParam(value = "data") String data,
        @RequestParam(value = "files",required = false) MultipartFile[] files,
        @RequestParam(value = "structFiles",required = false) MultipartFile[] structFiles,
        @RequestParam(value = "imageTypes",required = false) String[] imageTypes
        ){
        PcFinsp pcFinsp = JSONUtil.toBean(data,PcFinsp.class);
        String finspId =  StrUtil.isBlank(pcFinsp.getFinspId())? H_KeyWorker.nextIdToString():pcFinsp.getFinspId();
        List<PcFinspImage> images = Lists.newArrayList();
        // 病害照片
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            for (String fileId : fileIds) {
                PcFinspImage image = new PcFinspImage();
                image.setImageId(fileId);
                image.setRecordId(finspId).setImageType("4");
                images.add(image);
            }
        }
        // 结构物照片
        for (int i = 0; imageTypes != null
            && structFiles != null
            && imageTypes.length == structFiles.length
            && i < imageTypes.length; i++) {
            PcFinspImage image = new PcFinspImage();
            String imageType = imageTypes[i];
            MultipartFile structFile = structFiles[i];
            List<String> structImageIds =
                H_UploadHelper.uploadFile(new MultipartFile[] { structFile });
            image.setRecordId(finspId).setImageId(structImageIds.get(0)).setImageType(imageType);
            images.add(image);
        }
        if (CollectionUtil.isNotEmpty(images)){
            imageService.saveBatch(images);
        }
        update(pcFinsp);
        PcFinsp dbFinsp = service.getById(pcFinsp.getFinspId());
        initXy(dbFinsp);
        return RestResult.success(dbFinsp);
    }

    /**
     * 初始化经纬度
     * @param pcFinsp
     * @param pcFinsp
     */
    private void initXy(PcFinsp pcFinsp){
        if (ObjectUtil.isEmpty(pcFinsp) || ObjectUtil.isEmpty(pcFinsp.getFinspId())){
            return;
        }

        //边坡位置
        PcProjectScope projectScope = scopeService.getById(pcFinsp.getStructId());
        pcFinsp.setSlopeLevelName(projectScope.getStructLevel());

        Map<String, List<PcFinspImage>> imageMap = imageService.selectByRecordIds(Lists.newArrayList(pcFinsp.getFinspId()));
        if (CollectionUtil.isNotEmpty(imageMap)){
            List<PcFinspImage> images = imageMap.get(pcFinsp.getFinspId());
            pcFinsp.setFinspImages(images);
        }
        // 优先顺序：病害位置 --》边坡位置 --》轨迹位置

        List<Double> dssXys = pcFinspRecordService.getXyByFinspId(pcFinsp.getFinspId());
        if (CollectionUtil.isNotEmpty(dssXys)){
            pcFinsp.setX(dssXys.get(0));
            pcFinsp.setY(dssXys.get(1));
            return;
        }


        if (projectScope.getX() != null && projectScope.getY() != null){
            pcFinsp.setX(projectScope.getX());
            pcFinsp.setY(projectScope.getY());
            return;
        }

        //轨迹位置
        PcFinspGps gpsXy = gpsService.getXyByFinspId(pcFinsp.getFinspId());
        if (gpsXy != null){
            pcFinsp.setX(gpsXy.getLon());
            pcFinsp.setY(gpsXy.getLat());
        }
    }

    /**
     * 涵洞照片：1：涵洞进口全貌照片，2：涵洞出口全貌照片，3：涵洞内通视照片，4：病害代表照片
     * @param finspId 记录ID
     * @param imageType 照片类型(1：涵洞进口全貌照片，2：涵洞出口全貌照片，3：涵洞内通视照片，4：病害代表照片)
     * @param files 图片文件
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @PostMapping("uploadImage")
    public RestResult uploadDssImage(@RequestParam(value = "finspId",required = true) String finspId,
                                     @RequestParam(value = "imageType",required = true) String imageType,
                                     @RequestParam(value = "files") MultipartFile[] files){
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            List<PcFinspImage> images = Lists.newArrayList();
            for (String fileId : fileIds) {
                PcFinspImage image = new PcFinspImage();
                image.setImageId(fileId);
                image.setImageType(imageType);
                image.setRecordId(finspId);
                images.add(image);
            }
            imageService.saveBatch(images);
        }
        return RestResult.success(StrUtil.join(",",fileIds), "操作成功");
    }

    /**
     * 删除病害照片
     * @param fileId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @GetMapping("delImage")
    public RestResult<String> deleteImage(String fileId){
        imageService.removeById(fileId);
        return RestResult.success("操作成功");
    }
}