package com.hualu.app.module.mems.comm.controller;

import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.comm.entity.AppUpdateVersion;
import com.hualu.app.module.mems.comm.service.impl.AppUpdateVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * app版本更新
 */
@Api(tags = "版本更新接口")
@RestController
@RequestMapping("/appUpdate")
public class AppUpdateVersionController {

  @Resource
  private AppUpdateVersionService appUpdateVersionService;

  /**
   * 检测app的版本
   * @param versionCode 版本号
   * @param appId 应用id
   * @return
   */
  @ApiOperation("app版本更新检测")
  @GetMapping("/checkAppVersion")
  public RestResult<AppUpdateVersion> checkAppVersion(int versionCode, String appId) {
    AppUpdateVersion appVersion = appUpdateVersionService.checkAppVersion(versionCode, appId);
    return appVersion != null ? RestResult.success(appVersion) : RestResult.error();
  }
}
