package com.hualu.app.module.mems.task.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskDetailMapper extends BaseMapper<DmTaskDetail> {

    List<DmTaskDetailDto> selectDetailsByTaskId(@Param("dmTaskId") String dmTaskId);

    /**
     * 查询病害数量及金额
     * @param mtaskId
     * @return
     */
    List<DmTaskDssViewDto> selectDssNumAndMoney(@Param("mtaskId") String mtaskId);

    /**
     * 查询病害信息
     * @param queryWrapper
     * @return
     */
    List<DmTaskDssViewDto> selectDssInfos(@Param(Constants.WRAPPER)QueryWrapper queryWrapper);

    List<DmTaskDssViewDto> selectDssInfosByAccpt(@Param(Constants.WRAPPER)QueryWrapper queryWrapper);

    /**
     * 查询措施信息
     * @param contrId
     * @param mtaskId
     * @param dssId
     * @return
     */
    List<DmTaskMpItemViewDto> selectMpItems(@Param("contrId") String contrId, @Param("mtaskId") String mtaskId,
                                            @Param("dssId") String dssId);

    /**
     * 根据病害信息直接关联查询措施
     * @param queryWrapper
     * @return
     */
    List<DmTaskMpItemViewDto> selectMpItemsByDssIds(@Param(Constants.WRAPPER)QueryWrapper queryWrapper);

    /**
     * 查询未验收措施
     * @param queryWrapper
     * @return
     */
    List<DmTaskDetailDto> selectNoAccpt(@Param(Constants.WRAPPER) QueryWrapper<DmTaskDetailDto> queryWrapper);

    /**
     * 维修工程记录删除时，修改任务单明细状态
     * @param accptDtlIds
     */
    void updateStatus(@Param("accptDtlIds") List<String> accptDtlIds);

}
