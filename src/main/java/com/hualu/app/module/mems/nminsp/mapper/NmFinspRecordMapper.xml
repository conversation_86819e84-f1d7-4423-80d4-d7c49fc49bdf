<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmFinspRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmFinspRecord">
        <id column="DSS_ID" property="dssId" />
        <result column="DINSP_ID" property="dinspId" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="STAKE" property="stake" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_DEGREE" property="dssDegree" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_PART_ID" property="structPartId" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="LANE" property="lane" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="DSS_CAUSE" property="dssCause" />
        <result column="DSS_L" property="dssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="DSS_W" property="dssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="DSS_D" property="dssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="DSS_N" property="dssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="DSS_A" property="dssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="DSS_V" property="dssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="DSS_P" property="dssP" />
        <result column="DSS_G" property="dssG" />
        <result column="DSS_IMP_FLAG" property="dssImpFlag" />
        <result column="DSS_QUALITY" property="dssQuality" />
        <result column="HIS_DSS_ID" property="hisDssId" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="ISPHONE" property="isphone" />
        <result column="RAMP_ID" property="rampId" />
        <result column="TUNNEL_MOUTH" property="tunnelMouth" />
        <result column="START_HIGH" property="startHigh" />
        <result column="END_HIGH" property="endHigh" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="STAKE_HIGH" property="stakeHigh" />
        <result column="FINISH_STAKE" property="finishStake" />
        <result column="SOURCE" property="source" />
        <result column="PAVEMENT_TYPE" property="pavementType" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="FOUND_DATE" property="foundDate" />
        <result column="CLOSE_TYPE" property="closeType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_ID, DINSP_ID, INSP_TIME, LINE_DIRECT, STAKE, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, FACILITY_CAT, STRUCT_ID, STRUCT_PART_ID, STRUCT_COMP_ID, LANE, DSS_POSITION, DSS_DESC, DSS_CAUSE, DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V, DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY, HIS_DSS_ID, X, Y, ISPHONE, RAMP_ID, TUNNEL_MOUTH, START_HIGH, END_HIGH, START_STAKE_NUM, END_STAKE_NUM, STAKE_HIGH, FINISH_STAKE, SOURCE, PAVEMENT_TYPE, DEL_FLAG, CREATE_TIME, UPDATE_TIME, FOUND_DATE, CLOSE_TYPE
    </sql>
    <select id="selectViewRecord" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinspRecord">
        with taskcode as (select
        distinct
        dtd.DSS_ID,
        dt.MTASK_CODE,
        dt.MTASK_ID,
        dta.PROCESSINSTID ,
        dta.MTASK_ACCPT_CODE from DM_TASK_DETAIL dtd
        inner JOIN DM_TASK dt ON dt.MTASK_ID = dtd.MTASK_ID
        left JOIN DM_TASK_ACCPT dta ON dta.MTASK_ID = dt.MTASK_ID where dtd.DSS_ID in
        <foreach collection="dssIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )

        SELECT
        distinct
        d.repair_status,
        line.line_sname || '(' || line.line_code || ')' AS line_id,
        ( SELECT count( 1 ) FROM dss_image d WHERE d.dss_id = f.dss_id ) AS IMG,
        f.*,
        t.DSS_TYPE_NAME,
        tc.MTASK_CODE,
        tc.MTASK_ID,
        tc.PROCESSINSTID AS MTASK_ACCPT_ID,
        tc.MTASK_ACCPT_CODE
        FROM
        NM_FINSP_RECORD f
        LEFT JOIN DSS_TYPE_NEW t ON f.DSS_TYPE = t.DSS_TYPE
        LEFT JOIN dss_info d ON ( f.dss_id = d.dss_id )
        LEFT JOIN gdgs.base_line line ON line.line_id = d.main_road_id
        LEFT JOIN taskcode tc on tc.DSS_ID = f.DSS_ID
        WHERE f.del_flag=0 and f.dss_id in
        <foreach collection="dssIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="findBySql" resultType="java.util.Map">
        #{sql}
    </select>

    <insert id="saveOrUpdateBySql">
        #{insertDss}
    </insert>

    <select id="findNmFinspRecordBySql" resultMap="BaseResultMap">
        #{sql}
    </select>

    <select id="findNmFinspResultBySql" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinspResult">
        select * from NM_FINSP_RESULT d where d.finsp_id = #{sql}
    </select>

    <select id="getLastfinspIdByStruct" resultType="java.util.Map">
        select a.finsp_id from (select * from  nm_finsp p where
        <if test="structId != null and structId != ''">
            p.struct_id =#{structId}  and p.Facility_Cat=#{facilityCat} and p.status=3 and p.DEL_FLAG=0 order by p.insp_date desc) a where rownum=1
        </if>
        <if test="structId == null ">
            p.Facility_Cat=#{facilityCat} and p.status=3 and p.DEL_FLAG=0 order by p.insp_date desc) a where rownum=1
        </if>
    </select>

    <insert id="saveNmFinspRecordOfQL">
        insert into nm_finsp_record
        (DSS_ID,FINSP_ID,LINE_DIRECT,STAKE,STRUCT_PART_ID,STRUCT_COMP_ID,DSS_TYPE,DSS_DEGREE,MNTN_ADVICE,LANE,DSS_POSITION,DSS_DESC,
         DSS_L,DSS_L_UNIT,DSS_W,DSS_W_UNIT,DSS_D,DSS_D_UNIT,DSS_N,DSS_N_UNIT,DSS_A,DSS_A_UNIT,DSS_V,DSS_V_UNIT,DSS_P,DSS_G,DSS_IMP_FLAG,DSS_QUALITY,
         HIS_DSS_ID,DSS_CAUSE,X,Y,ISPHONE,RAMP_ID,STRUCT_ID)
        select GET_UUID(),#{finspId},k.* from (
                                           Select t.LINE_DIRECT,t.STAKE,t.STRUCT_PART_ID,t.STRUCT_COMP_ID,t.DSS_TYPE,t.DSS_DEGREE,t.MNTN_ADVICE,t.LANE,
                                                  t.DSS_POSITION,t.DSS_DESC,t.DSS_L,t.DSS_L_UNIT,
                                                  t.DSS_W,t.DSS_W_UNIT,t.DSS_D,t.DSS_D_UNIT,t.DSS_N,t.DSS_N_UNIT,t.DSS_A,t.DSS_A_UNIT,t.DSS_V,
                                                  t.DSS_V_UNIT,t.DSS_P,t.DSS_G,t.DSS_IMP_FLAG,t.DSS_QUALITY,t.his_dss_id,
                                                  t.DSS_CAUSE,t.X,t.Y,t.ISPHONE,t.RAMP_ID,t.STRUCT_ID
                                           From nM_FINSP_RECORD t
                                                    inner join dss_info info  on (info.dss_id=t.dss_id)
                                           where t.finsp_id=#{oldFinspId} and info.deal_status=0
                                           group by t.LINE_DIRECT,t.STAKE,t.STRUCT_PART_ID,t.STRUCT_COMP_ID,t.DSS_TYPE,t.DSS_DEGREE,t.MNTN_ADVICE,t.LANE,
                                                    t.DSS_POSITION,t.DSS_DESC,t.DSS_L,t.DSS_L_UNIT,
                                                    t.DSS_W,t.DSS_W_UNIT,t.DSS_D,t.DSS_D_UNIT,t.DSS_N,t.DSS_N_UNIT,t.DSS_A,t.DSS_A_UNIT,t.DSS_V,
                                                    t.DSS_V_UNIT,t.DSS_P,t.DSS_G,t.DSS_IMP_FLAG,t.DSS_QUALITY,t.his_dss_id,
                                                    t.DSS_CAUSE,t.X,t.Y,t.ISPHONE,t.RAMP_ID,t.STRUCT_ID) k
    </insert>

    <insert id="saveNmFinspRecordOfOthers">
        insert into nm_finsp_record
        (DSS_ID,FINSP_ID,LINE_DIRECT,STAKE,STRUCT_PART_ID,STRUCT_COMP_ID,DSS_TYPE,DSS_DEGREE,MNTN_ADVICE,LANE,DSS_POSITION,DSS_DESC,
         DSS_L,DSS_L_UNIT,DSS_W,DSS_W_UNIT,DSS_D,DSS_D_UNIT,DSS_N,DSS_N_UNIT,DSS_A,DSS_A_UNIT,DSS_V,DSS_V_UNIT,DSS_P,DSS_G,DSS_IMP_FLAG,DSS_QUALITY,
         HIS_DSS_ID,DSS_CAUSE,X,Y,ISPHONE,RAMP_ID,STRUCT_ID,TUNNEL_MOUTH,STAKE_HIGH,
         START_STAKE_NUM,END_STAKE_NUM,START_HIGH,END_HIGH) Select  get_uuid(),#{finspId},t.LINE_DIRECT,
                                                                    t.STAKE,t.STRUCT_PART_ID,t.STRUCT_COMP_ID,t.DSS_TYPE,t.DSS_DEGREE,t.MNTN_ADVICE,t.LANE,
                                                                    t.DSS_POSITION,t.DSS_DESC,t.DSS_L,t.DSS_L_UNIT,
                                                                    t.DSS_W,t.DSS_W_UNIT,t.DSS_D,t.DSS_D_UNIT,t.DSS_N,t.DSS_N_UNIT,t.DSS_A,t.DSS_A_UNIT,t.DSS_V,
                                                                    t.DSS_V_UNIT,t.DSS_P,t.DSS_G,t.DSS_IMP_FLAG,t.DSS_QUALITY,t.his_dss_id,
                                                                    t.DSS_CAUSE,t.X,t.Y,t.ISPHONE,t.RAMP_ID,t.STRUCT_ID,
                                                                    t.TUNNEL_MOUTH,t.STAKE_HIGH,t.START_STAKE_NUM,t.END_STAKE_NUM,t.START_HIGH,t.END_HIGH
        From NM_FINSP_RECORD t left join dss_info info  on (info.dss_id=t.dss_id)
        where t.finsp_id=#{oldFinspId} and info.deal_status=0
    </insert>

    <insert id="copyFinspFetail2NewOne">
        declare newfin_id varchar2(40);oldfinspId varchar2(40);
        begin oldfinspId:=#{oldfinspId};newfin_id:=#{newFinspId};copy_nfin_record(oldfinspId,newfin_id);end;
    </insert>

    <select id="findNmFinspResultSet" resultType="java.util.Map">
        select * from NM_FINSP_RESULT_SET where org_code = #{orgCode}
         and user_code = #{userCode}
    </select>

    <select id="findNmFinspRecord" resultMap="BaseResultMap">
        select a.ITEM_ID as FINSP_ITEM_ID,c.STRUCT_PART_ID,d.DSS_TYPE_NAME,
               c.DSS_TYPE,LINE_DIRECT, STAKE, STRUCT_PART_ID, c.STRUCT_COMP_ID, LANE, DSS_DESC,
               DSS_L, c.DSS_L_UNIT, DSS_W, c.DSS_W_UNIT, DSS_D, c.DSS_D_UNIT, DSS_N, c.DSS_N_UNIT, DSS_A, c.DSS_A_UNIT, DSS_V,
               c.DSS_V_UNIT, DSS_P, DSS_G, DSS_QUALITY,b.FIN_DESC as dss_desc_q
        <if test="facilityCat == 'SD'">
            from NM_FINSP_RESULT a
            inner join NM_INSP_ITEM b on a.ITEM_ID = b.ITEM_ID
            inner join BASE_STRUCT_COMP k on k.STRUCT_COMP_NAME = b.INSP_COM
            inner join NM_FINSP_RECORD c on  a.FINSP_ID = c.FINSP_ID
            inner join DSS_TYPE_NEW d on c.DSS_TYPE = d.DSS_TYPE
            where a.FINSP_ID = #{finspId}
        </if>
        <if test="facilityCat != 'SD'">
            from NM_FINSP_RESULT a
            inner join NM_INSP_ITEM b on a.ITEM_ID = b.ITEM_ID
            inner join NM_FINSP_RECORD c on a.FINSP_ID = c.FINSP_ID
            inner join DSS_TYPE_NEW d on c.DSS_TYPE = d.DSS_TYPE
            where a.FINSP_ID =#{finspId}
        </if>
    </select>

    <delete id="deleteInspectionPhotoById">
        delete memsdb.DSS_IMAGE d where d.FILE_ID=#{id}
    </delete>

    <insert id="saveInspectionPhoto">
        insert into memsdb.DSS_IMAGE values (sys_guid(),#{finspId},#{id},6,null,0)
    </insert>

    <select id="getInspectionPhotos" resultType="java.lang.String">
        select to_char(WM_CONCAT(d.FILE_ID)) from   memsdb.DSS_IMAGE d where d.DSS_ID=#{finspId}
    </select>

    <select id="selectOldPartList" resultType="java.util.Map">
        select t.PARTS_CODE as id,t.PARTSTYPE_NAME as name
        from BCTCMSDB.T_BRDG_PARTSTYPE t
        where t.PARTS_CODE in
        <foreach item="code" collection="partsCode" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectOldQlPoistionList" resultType="java.util.Map">
        select t.PARTPOST_D as id,t.POST_NAME as name from BCTCMSDB.T_BRDG_PARTPOST t where t.PARTPOST_D in
        <foreach item="code" collection="collect" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectRecordDtlByFinspId" resultMap="BaseResultMap">
        select t.*,dt.DSS_TYPE_NAME,p.POST_NAME as dss_position_name from MEMSDB.NM_FINSP_RECORD t
                                                                              left join  bctcmsdb.T_BRDG_PARTPOST p on t.DSS_POSITION=p.PARTPOST_D
                                                                              inner join memsdb.DSS_TYPE_NEW dt on t.DSS_TYPE=dt.DSS_TYPE
        where t.FINSP_ID=#{finspId} and t.DEL_FLAG=0
    </select>

    <select id="findUndoDssByFinspId" resultMap="BaseResultMap">
        select * from MEMSDB.NM_FINSP_RECORD f where FINSP_ID in(#{oldfinspId})
                                                 and exists(select f.DSS_ID from MEMSDB.DSS_INFO d where d.DSS_ID=f.DSS_ID
                                                                                                     and d.DEAL_STATUS = 0)
    </select>

    <select id="getLastfinspIdListByStructList" resultType="com.hualu.app.module.mems.nminsp.vo.NmFinspRel">
        select max(p.FINSP_ID) as LAST_FINSP_ID,p.STRUCT_ID from  nm_finsp p where
        p.struct_id in (#{collectStructIds})  and p.Facility_Cat=#{facilityCat}
        and p.status=3
        and p.DEL_FLAG=0
        group by p.STRUCT_ID
    </select>
</mapper>
