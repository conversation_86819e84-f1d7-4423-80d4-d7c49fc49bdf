package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 结构物排查数据下载表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinspDownload对象", description="结构物排查数据下载表")
public class PcFinspDownload implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId("DATA_ID")
    private String dataId;

    @ApiModelProperty(value = "数据名称")
    @TableField("DATA_NAME")
    private String dataName;

    @ApiModelProperty(value = "路径地址")
    @TableField("DATA_URL")
    private String dataUrl;

    @ApiModelProperty(value = "归属单位")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "项目ID")
    @TableField("PRJ_ID")
    private String prjId;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "数据状态（-1：数据生成失败，0：数据正在生成，1：数据已生成）")
    @TableField(value = "STATE", fill = FieldFill.INSERT)
    private Integer state;

    @ApiModelProperty(value = "备注信息")
    @TableField("REMARK")
    private String remark;

}
