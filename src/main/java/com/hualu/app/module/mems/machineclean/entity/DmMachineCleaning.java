package com.hualu.app.module.mems.machineclean.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

@ApiModel(description="HSMSDB.DM_MACHINE_CLEANING")
@TableName(value = "HSMSDB.DM_MACHINE_CLEANING")
public class DmMachineCleaning {
    /**
     * 主键ID
     */
    @TableId(value = "MC_ID")
    @ApiModelProperty(value="主键ID")
    private String mcId;

    /**
     * 开始时间
     */
    @TableField(value = "START_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value="开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(value = "END_TIME")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @ApiModelProperty(value="结束时间")
    private LocalDateTime endTime;

    /**
     * 路线ID
     */
    @TableField(value = "LINE_ID")
    @ApiModelProperty(value="路线ID")
    private String lineId;

    /**
     * 方向
     */
    @TableField(value = "DIRECT")
    @ApiModelProperty(value="方向")
    private String direct;

    /**
     * 车道
     */
    @TableField(value = "LANE")
    @ApiModelProperty(value="车道")
    private String lane;

    /**
     * 起点桩号
     */
    @TableField(value = "START_STAKE")
    @ApiModelProperty(value="起点桩号")
    private BigDecimal startStake;

    /**
     * 终点桩号
     */
    @TableField(value = "END_STAKE")
    @ApiModelProperty(value="终点桩号")
    private BigDecimal endStake;

    /**
     * 巡查里程
     */
    @TableField(value = "QS_LENGTH")
    @ApiModelProperty(value="巡查里程")
    private BigDecimal qsLength;

    /**
     * 起点照片
     */
    @TableField(value = "START_IMAGE", strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value="起点照片")
    private String startImage;

    /**
     * 结束照片
     */
    @TableField(value = "END_IMAGE", strategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value="结束照片")
    private String endImage;

    /**
     * 组织机构ID
     */
    @TableField(value = "ORG_ID")
    @ApiModelProperty(value="组织机构ID")
    private String orgId;

    /**
     * 创建用户ID
     */
    @TableField(value = "CREATE_USER_ID")
    @ApiModelProperty(value="创建用户ID")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty(value="创建时间")
    private String createTime;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    @ApiModelProperty(value="备注")
    private String remark;

    @TableField(exist = false)
    private String startStakeStr;

    @TableField(exist = false)
    private String endStakeStr;

    @TableField(exist = false)
    private String lineName;

    @TableField(exist = false)
    private String lineDirectName;

    @TableField(exist = false)
    private String laneName;

    @TableField(exist = false)
    private String orgName;

    @TableField(exist = false)
    private String lineCode;

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getStartStakeStr() {
        return startStakeStr;
    }

    public void setStartStakeStr(String startStakeStr) {
        this.startStakeStr = startStakeStr;
    }

    public String getEndStakeStr() {
        return endStakeStr;
    }

    public void setEndStakeStr(String endStakeStr) {
        this.endStakeStr = endStakeStr;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineDirectName() {
        return lineDirectName;
    }

    public void setLineDirectName(String lineDirectName) {
        this.lineDirectName = lineDirectName;
    }

    public String getLaneName() {
        return laneName;
    }

    public void setLaneName(String laneName) {
        this.laneName = laneName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public static final String COL_MC_ID = "MC_ID";

    public static final String COL_START_TIME = "START_TIME";

    public static final String COL_END_TIME = "END_TIME";

    public static final String COL_LINE_ID = "LINE_ID";

    public static final String COL_DIRECT = "DIRECT";

    public static final String COL_LANE = "LANE";

    public static final String COL_START_STAKE = "START_STAKE";

    public static final String COL_END_STAKE = "END_STAKE";

    public static final String COL_QS_LENGTH = "QS_LENGTH";

    public static final String COL_START_IMAGE = "START_IMAGE";

    public static final String COL_END_IMAGE = "END_IMAGE";

    public static final String COL_ORG_ID = "ORG_ID";

    public static final String COL_CREATE_USER_ID = "CREATE_USER_ID";

    public static final String COL_CREATE_TIME = "CREATE_TIME";

    public static final String COL_REMARK = "REMARK";

    /**
     * 获取主键ID
     *
     * @return MC_ID - 主键ID
     */
    public String getMcId() {
        return mcId;
    }

    /**
     * 设置主键ID
     *
     * @param mcId 主键ID
     */
    public void setMcId(String mcId) {
        this.mcId = mcId;
    }

    /**
     * 获取开始时间
     *
     * @return START_TIME - 开始时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }

    /**
     * 设置开始时间
     *
     * @param startTime 开始时间
     */
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    /**
     * 获取结束时间
     *
     * @return END_TIME - 结束时间
     */
    public LocalDateTime getEndTime() {
        return endTime;
    }

    /**
     * 设置结束时间
     *
     * @param endTime 结束时间
     */
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    /**
     * 获取路线ID
     *
     * @return LINE_ID - 路线ID
     */
    public String getLineId() {
        return lineId;
    }

    /**
     * 设置路线ID
     *
     * @param lineId 路线ID
     */
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    /**
     * 获取方向
     *
     * @return DIRECT - 方向
     */
    public String getDirect() {
        return direct;
    }

    /**
     * 设置方向
     *
     * @param direct 方向
     */
    public void setDirect(String direct) {
        this.direct = direct;
    }

    /**
     * 获取车道
     *
     * @return LANE - 车道
     */
    public String getLane() {
        return lane;
    }

    /**
     * 设置车道
     *
     * @param lane 车道
     */
    public void setLane(String lane) {
        this.lane = lane;
    }

    /**
     * 获取起点桩号
     *
     * @return START_STAKE - 起点桩号
     */
    public BigDecimal getStartStake() {
        return startStake;
    }

    /**
     * 设置起点桩号
     *
     * @param startStake 起点桩号
     */
    public void setStartStake(BigDecimal startStake) {
        this.startStake = startStake;
    }

    /**
     * 获取终点桩号
     *
     * @return END_STAKE - 终点桩号
     */
    public BigDecimal getEndStake() {
        return endStake;
    }

    /**
     * 设置终点桩号
     *
     * @param endStake 终点桩号
     */
    public void setEndStake(BigDecimal endStake) {
        this.endStake = endStake;
    }

    /**
     * 获取巡查里程
     *
     * @return QS_LENGTH - 巡查里程
     */
    public BigDecimal getQsLength() {
        return qsLength;
    }

    /**
     * 设置巡查里程
     *
     * @param qsLength 巡查里程
     */
    public void setQsLength(BigDecimal qsLength) {
        this.qsLength = qsLength;
    }

    /**
     * 获取起点照片
     *
     * @return START_IMAGE - 起点照片
     */
    public String getStartImage() {
        return startImage;
    }

    /**
     * 设置起点照片
     *
     * @param startImage 起点照片
     */
    public void setStartImage(String startImage) {
        this.startImage = startImage;
    }

    /**
     * 获取结束照片
     *
     * @return END_IMAGE - 结束照片
     */
    public String getEndImage() {
        return endImage;
    }

    /**
     * 设置结束照片
     *
     * @param endImage 结束照片
     */
    public void setEndImage(String endImage) {
        this.endImage = endImage;
    }

    /**
     * 获取组织机构ID
     *
     * @return ORG_ID - 组织机构ID
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * 设置组织机构ID
     *
     * @param orgId 组织机构ID
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * 获取创建用户ID
     *
     * @return CREATE_USER_ID - 创建用户ID
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建用户ID
     *
     * @param createUserId 创建用户ID
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取创建时间
     *
     * @return CREATE_TIME - 创建时间
     */
    public String getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取备注
     *
     * @return REMARK - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}