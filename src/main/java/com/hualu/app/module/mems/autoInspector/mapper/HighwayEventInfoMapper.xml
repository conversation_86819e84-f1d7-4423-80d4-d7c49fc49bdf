<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayEventInfoMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayEventInfo">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="snapshotId" column="SNAPSHOT_ID" jdbcType="VARCHAR"/>
            <result property="taskId" column="TASK_ID" jdbcType="VARCHAR"/>
            <result property="segmentId" column="SEGMENT_ID" jdbcType="VARCHAR"/>
            <result property="segmentName" column="SEGMENT_NAME" jdbcType="VARCHAR"/>
            <result property="inspectDirection" column="INSPECT_DIRECTION" jdbcType="VARCHAR"/>
            <result property="dataFrom" column="DATA_FROM" jdbcType="VARCHAR"/>
            <result property="objectType" column="OBJECT_TYPE" jdbcType="VARCHAR"/>
            <result property="objectSubtype" column="OBJECT_SUBTYPE" jdbcType="VARCHAR"/>
            <result property="defectType" column="DEFECT_TYPE" jdbcType="VARCHAR"/>
            <result property="state" column="STATE" jdbcType="VARCHAR"/>
            <result property="stakeStart" column="STAKE_START" jdbcType="VARCHAR"/>
            <result property="stakeEnd" column="STAKE_END" jdbcType="VARCHAR"/>
            <result property="geomType" column="GEOM_TYPE" jdbcType="VARCHAR"/>
            <result property="longitude" column="LONGITUDE" jdbcType="DECIMAL"/>
            <result property="latitude" column="LATITUDE" jdbcType="DECIMAL"/>
            <result property="transformation" column="TRANSFORMATION" jdbcType="DECIMAL"/>
            <result property="dataStatus" column="DATA_STATUS" jdbcType="DECIMAL"/>
            <result property="isCalibrated" column="IS_CALIBRATED" jdbcType="DECIMAL"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="trafficSignType" column="TRAFFIC_SIGN_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SNAPSHOT_ID,TASK_ID,
        SEGMENT_ID,SEGMENT_NAME,INSPECT_DIRECTION,
        DATA_FROM,OBJECT_TYPE,OBJECT_SUBTYPE,
        DEFECT_TYPE,STATE,STAKE_START,
        STAKE_END,GEOM_TYPE,LONGITUDE,
        LATITUDE,TRANSFORMATION,DATA_STATUS,
        IS_CALIBRATED,CREATED_TIME,UPDATED_TIME,
        TRAFFIC_SIGN_TYPE
    </sql>
</mapper>
