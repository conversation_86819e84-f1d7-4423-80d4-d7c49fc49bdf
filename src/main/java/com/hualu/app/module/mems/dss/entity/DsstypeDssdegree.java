package com.hualu.app.module.mems.dss.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 病害类型严重程度关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DsstypeDssdegree对象", description="病害类型严重程度关系表")
public class DsstypeDssdegree implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "病害类型")
    @TableField("DSS_TYPE")
    private String dssType;

    @ApiModelProperty(value = "病害严重程度")
    @TableField("DSS_DEGREE")
    private String dssDegree;

    @ApiModelProperty(value = "病害严重程度（名称）")
    @TableField("DSS_DEGREE_NAME")
    private String dssDegreeName;


}
