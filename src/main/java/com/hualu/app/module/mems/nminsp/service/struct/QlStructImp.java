package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.basedata.service.TBrdgBrdgrecogService;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.mapper.NmInspItemMapper;
import com.hualu.app.module.mems.nminsp.service.*;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import com.tg.dev.api.util.hp.H_CValidator;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service("qlStructImp")
public class QlStructImp implements IStruct{

    @Autowired
    BaseRouteLogicService routeLogicService;
    private static final String OTHER = "OTHER";
    private static final String ISSUE_DESC_FORMAT = "{}，{}";
    @Autowired
    NmDinspResultService dmResultService;

    @Autowired
    NmDinspRecordService dmRecordService;
    @Autowired
    NmFinspResultService finspResultService;

    @Autowired
    NmFinspRecordService finspRecordService;

    @Autowired
    TBrdgBrdgrecogService bridgeInfoService;
    @Autowired
    NmInspItemService inspItemService;
    @Autowired
    NmInspItemMapper mapper;
    @Override
    public void validateNmDinsp(NmDinsp nmDinsp) {
        H_CValidator.validator2Exception(nmDinsp,new Class[]{StructGroup.class});
        // 根据结构物ID,设置结构物名称
        BaseStructDto structInfo = getStructInfo(nmDinsp.getFacilityCat(), nmDinsp.getStructId());
        nmDinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());
    }

    @Override
    public void validateNmFinsp(NmFinsp nmFinsp) {
        H_CValidator.validator2Exception(nmFinsp,new Class[]{StructGroup.class});
        // 根据结构物ID,设置结构物名称
        BaseStructDto structInfo = getStructInfo(nmFinsp.getFacilityCat(), nmFinsp.getStructId());
        nmFinsp.setStructName(structInfo.getStructName()).setRouteCode(structInfo.getRouteCode());

        BaseRouteLogic routeLogic = routeLogicService.getRouteByRouteCode(nmFinsp.getRouteCode());
        if (routeLogic != null) {
            nmFinsp.setRouteName(routeLogic.getRouteName());
        }
    }

    @Override
    public void refreshNmDinspResult(NmDinsp nmDinsp) {
        List<NmDinspResult> nmDinspResult = dmResultService.createNmDinspResult(nmDinsp);
        // 获取对应的病害ID
        List<NmDinspRecord> nmDinspRecords = dmRecordService.selectRecordByDinspId(nmDinsp.getDinspId());

        if (CollectionUtil.isEmpty(nmDinspResult) || CollectionUtil.isEmpty(nmDinspRecords)) {
            return;
        }
        dmRecordService.showView(nmDinspRecords,nmDinsp);
        // 边坡日常巡查，用不到partId判断
        for (NmDinspResult item : nmDinspResult) {
            if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())) {
                continue;
            }
            // 先匹配已经配置的病害项
            List<NmDinspRecord> dssRecords = nmDinspRecords.stream().filter(e ->item.getDssType().contains(e.getDssType()))
                .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dssRecords)){

                List<String> issueList = Lists.newArrayList();
                dssRecords.forEach(e -> {
                    String format = StrUtil.format(ISSUE_DESC_FORMAT, e.getDssTypeName(), e.getDssL()+(e.getDssLUnit()==null?"处":e.getDssLUnit()));
                    issueList.add(format);
                });
                String issueDesc  = issueList.stream().collect(Collectors.joining("；"));
//                String issueDesc = dssRecords.stream().map(NmDinspRecord::getDssDesc).collect(Collectors.joining("；"));
                item.setIssueDesc(issueDesc);
                //移除已经匹配的病害信息
                nmDinspRecords.removeAll(dssRecords);
            }
        }
        // 获取其他项结论
        NmDinspResult otherResult = nmDinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
        if (otherResult != null && CollectionUtil.isNotEmpty(nmDinspRecords)) {
            String issueDesc = nmDinspRecords.stream().map(NmDinspRecord::getDssDesc).collect(Collectors.joining("；"));
            otherResult.setIssueDesc(issueDesc);
        }
        dmResultService.updateBatchById(nmDinspResult);
    }

    @Override
    public void refreshNmFinspResult(NmFinsp nmFinsp) {
        String mntOrgId = nmFinsp.getMntOrgId();
        if(mntOrgId.equals("N000034")&&(nmFinsp.getFacilityCat().equals("QL")||nmFinsp.getFacilityCat().equals("HD"))){
            //虎门大桥巡查结论自填
            return;
        }
        List<NmFinspResult> nmFinspResult = finspResultService.createNmFinspResult(nmFinsp);
        List<NmInspItem> items = inspItemService.listXcTypeByFinsp(nmFinsp, "FM");
        Map<String, String> itemMap = items.stream().collect(Collectors.toMap(NmInspItem::getItemId, NmInspItem::getInspCont));
        List<NmFinspRecord> nmFinspRecords = finspRecordService.selectRecordDtlByFinspId(nmFinsp.getFinspId());
        finspRecordService.showView(nmFinspRecords,nmFinsp);
        if (CollectionUtil.isEmpty(nmFinspResult) || CollectionUtil.isEmpty(nmFinspRecords)) {
            if (CollectionUtil.isNotEmpty(nmFinspResult)){
                //清空结论
                finspResultService.updateBatchById(nmFinspResult);
            }
            return;
        }
        List<Map<String, String>> partRel = mapper.findBrdgPartRel();
        Map<String, String> partDicMap = partRel.stream()
            .collect(Collectors.toMap(
                map -> map.get("OLD"),  // Key mapper
                map -> map.get("NEW"), (oldValue, newValue) -> oldValue
            ));


        for(NmFinspRecord record:nmFinspRecords){
            record.setBrdgPartIdNew(partDicMap.get(record.getStructPartId()));
        }
        // 根据部件进行分组
        Map<String, List<NmFinspRecord>> partMap = nmFinspRecords.stream().filter(e->StrUtil.isNotBlank(e.getBrdgPartIdNew()))
            .collect(Collectors.groupingBy(NmFinspRecord::getBrdgPartIdNew));
        for (NmFinspResult item : nmFinspResult) {
            if("N000033".equals(mntOrgId)&&(item.getItemId().equals("89")||item.getItemId().equals("92")||item.getItemId().equals("93")||item.getItemId().equals("100")||item.getItemId().equals("102")||item.getItemId().equals("104"))){
                item.setIssueDesc("/");
                continue;
            }
            if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())){
                continue;
            }

            // partId会存在多个
            List<String> partIds = StrUtil.split(item.getPartId(), ",");
            List<NmFinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dssList)) {
                //经常检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
//                List<NmFinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(dssList)) {
                    //String issueDesc = containsDssList.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
                    item.setIssueDesc(getIssueDescByFm(dssList));
                    HashSet dssMap = new HashSet<String>();
                    for(NmFinspRecord record:dssList){
                        dssMap.add(record.getDssTypeName());
                    }
                    String s = itemMap.get(item.getItemId());
                    item.setRemark(covertConclusion(dssMap,s));
                    // 移除已经匹配的病害
                    nmFinspRecords.removeAll(dssList);
                }
            }
        }
        // 获取其他项结论
        NmFinspResult otherResult = nmFinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
        if (otherResult != null && CollectionUtil.isNotEmpty(nmFinspRecords)) {
            //String issueDesc = nmFinspRecords.stream().map(NmFinspRecord::getDssDesc).collect(Collectors.joining("；"));
            otherResult.setIssueDesc(getIssueDescByFm(nmFinspRecords));
        }
        finspResultService.updateBatchById(nmFinspResult);
    }

    private String covertConclusion (HashSet<String> dssSet, String itemName) {
        String[] parents = itemName.split(";");
        for(int j=0;j<parents.length;j++){
            String[] sons = parents[j].split("、");
            for(int k=0 ;k<sons.length;k++){
                for(String dss:dssSet){
                    if(dss.contains(sons[k])||sons[k].contains(dss)){
                        sons[k] = sons[k].replace("□","√");
                        break;
                    }
                }
            }
            parents[j] = String.join("、",sons);
        }
        return String.join(";",parents);
    }

    public static Double getFirstNonEmpty(Double dssL, Double dssW, Double dssA,Double dssN,Double dssV, Double dssP, Double dssG) {
        if (dssL != null ) return dssL;
        if (dssW != null) {
            return dssW;
        }
        if (dssA != null) {
            return dssA;
        }
        if (dssN != null ) {
            return dssN;
        }
        if (dssV != null ) {
            return dssV;
        }
        if (dssP != null) {
            return dssP;
        }
        if (dssG != null) {
            return dssG;
        }
        return 0d;
    }
    public static String getFirstNonEmpty(String dssL, String dssW, String dssA,String dssN,String dssV, String dssP, String dssG) {
        if (dssL != null ) return dssL;
        if (dssW != null) {
            return dssW;
        }
        if (dssA != null) {
            return dssA;
        }
        if (dssN != null ) {
            return dssN;
        }
        if (dssV != null ) {
            return dssV;
        }
        if (dssP != null) {
            return dssP;
        }
        if (dssG != null) {
            return dssG;
        }
        return "处";
    }
    private String getIssueDescByFm(List<NmFinspRecord> dssRecords) {
        if (CollectionUtil.isEmpty(dssRecords)) {
            return null;
        }
        List<String> issueList = Lists.newArrayList();
        dssRecords.forEach(e -> {
            String format = null;
            String mntnAdvice = e.getMntnAdvice();
            if(mntnAdvice!=null&&!mntnAdvice.isEmpty()){
                format = StrUtil.format(ISSUE_DESC_FORMAT,e.getDssTypeName(),mntnAdvice);
            }else {
                format = concatConclusion(e);
//                format = StrUtil.format(ISSUE_DESC_FORMAT, e.getDssTypeName(), getFirstNonEmpty(e.getDssL(), e.getDssW(), e.getDssA(), e.getDssN(), e.getDssV(), e.getDssP(), e.getDssG()) + getFirstNonEmpty(e.getDssLUnit(), e.getDssWUnit(), e.getDssAUnit(), e.getDssNUnit(), e.getDssVUnit(), "%", "%"));
            }
            if((format==null||format.contains("null")||format.contains("0.0"))&&e.getDssDesc()!=null&&!e.getDssDesc().isEmpty()){
                format = e.getDssDesc();
            }
            issueList.add(format);
        });
        String collect = issueList.stream().collect(Collectors.joining("；"));
        return collect.length()>=3999?collect.substring(0,3999):collect;
    }
    private String concatConclusion(NmFinspRecord record){
        StringBuffer buffer = new StringBuffer();
        if(record!=null){
            if(record.getStructCompId()!=null){
                buffer.append(record.getStructCompId());
            }
            if(record.getDssPosition()!=null){
                buffer.append(record.getDssPositionName());
            }
            if(record.getDssType()!=null){
                buffer.append(record.getDssTypeName());
            }
            if(record.getDssLUnit()==null){
                record.setDssLUnit("m");
            }
            if(record.getDssWUnit()==null){
                record.setDssWUnit("mm");
            }
            if(record.getDssAUnit()==null){
                record.setDssAUnit("㎡");
            }
            if(record.getDssVUnit()==null){
                record.setDssVUnit("m³");
            }
            if(record.getDssL()!=null&&record.getDssW()==null&&record.getDssA()==null){
                buffer.append("长：").append(record.getDssL()).append(record.getDssLUnit());
            }
            if(record.getDssL()==null&&record.getDssW()!=null&&record.getDssA()==null){
                buffer.append("宽：").append(record.getDssW()).append(record.getDssWUnit());
            }
            if(record.getDssL()!=null&&record.getDssW()!=null&&record.getDssA()==null){
                buffer.append("长：").append(record.getDssL()).append(record.getDssLUnit()).append(",宽：").append(record.getDssW()).append(record.getDssWUnit());
            }
            if(record.getDssA()!=null){
                buffer.append("面积：").append(record.getDssA()).append(record.getDssAUnit());
            }
            if(record.getDssV()!=null){
                buffer.append("体积：").append(record.getDssV()).append(record.getDssVUnit());
            }
            if(record.getDssP()!=null){
                buffer.append("百分比：").append(record.getDssP()).append("%");
            }
            if(record.getDssG()!=null){
                buffer.append("数量：").append(record.getDssP()).append("处");
            }
            buffer.append(";");
        }
        return buffer.toString();
    }
    @Override
    public List<String> processAndExportNmDinspWord(List<NmDinsp> nmDinsps, String parentPath) {
        // 初始化桥梁简介
//        Map<String, String> bridgeProfileMap = bridgeInfoService.resolveBridgeProfileMap(nmDinsps.stream().filter(e -> StrUtil.isNotBlank(e.getStructId())).map(NmDinsp::getStructId).collect(Collectors.toList()));
//        nmDinsps.forEach(item->{
//            item.setBridgeProfile(bridgeProfileMap.get(item.getStructId()));
//        });

        for(NmDinsp nmFinsp:nmDinsps){
            TBrdgBrdgrecog byId = bridgeInfoService.getById(nmFinsp.getStructId());
            nmFinsp.setDisplayStake(byId.getLogicCntrStake());
            nmFinsp.setBridgeCode(byId.getBrdgCode());
        }
        return H_WordHelper.processAndExportWord(
            false,
            nmDinsps,
            parentPath,
            "DM",
            NmDinsp::getFacilityCat,
            NmDinsp::getInspFrequency,
            NmDinsp::getDinspId,
            NmDinsp::getDinspCode,
            NmDinsp::getInspDate,
            dinspIds -> dmResultService.listResultsByDinspIds(dinspIds),
            NmDinspResult::getDinspId,
            (records, entity) -> {
                // 设置模版显示序号
                AtomicInteger atomicInteger =new AtomicInteger(1);
                records.forEach(record -> {
                    record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
                });
            }
        );
    }
    @Override
    public List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps, String parentPath) {
        //桥梁回显桥梁编码及中心桩号

        for(NmFinsp nmFinsp:nmFinsps){
            TBrdgBrdgrecog byId = bridgeInfoService.getById(nmFinsp.getStructId());
            nmFinsp.setDisplayStake(byId.getLogicCntrStake());
            nmFinsp.setBridgeCode(byId.getTrafficCode());
        }

        return H_WordHelper.processAndExportWord(
            false,
            nmFinsps,
            parentPath,
            "FM",
            NmFinsp::getFacilityCat,
            NmFinsp::getInspFrequency,
            NmFinsp::getFinspId,
            NmFinsp::getFinspCode,
            NmFinsp::getInspDate,
            finspIds -> finspResultService.listResultsByFinspIds(finspIds, "QL"),
            NmFinspResult::getFinspId,
            (records, entity) -> {
                // 设置模版显示序号
                AtomicInteger atomicInteger =new AtomicInteger(1);
                records.forEach(record -> {
                    record.setItemId(String.valueOf(atomicInteger.getAndIncrement()));
                });
            }
        );
    }
}
