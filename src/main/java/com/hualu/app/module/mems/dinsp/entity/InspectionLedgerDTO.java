package com.hualu.app.module.mems.dinsp.entity;

import java.math.BigDecimal;
import java.util.Date;

public class InspectionLedgerDTO {

    private Integer order;
    private String statusName;      // decode(STATUS, ...) 翻译结果
    private BigDecimal dssNums;     // P.DSS_NUM
    private String dinspCode;       // P.DINSP_CODE
    private Date inspDate;          // P.INSP_DATE
    private String lineCode;        // l.LINE_CODE
    private String routeName;       // l.ROUTE_NAME
    private String orgFullName;     // o.ORG_FULLNAME
    private String inspPerson;      // P.INSP_PERSON
    private String searchDept;      // p.SEARCH_DEPT
    private String xcTypeName;      // decode(P.XC_TYPE, ...)
    private String weather;         // P.WEATHER

    // getter and setter methods

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public BigDecimal getDssNums() {
        return dssNums;
    }

    public void setDssNums(BigDecimal dssNums) {
        this.dssNums = dssNums;
    }

    public String getDinspCode() {
        return dinspCode;
    }

    public void setDinspCode(String dinspCode) {
        this.dinspCode = dinspCode;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getOrgFullName() {
        return orgFullName;
    }

    public void setOrgFullName(String orgFullName) {
        this.orgFullName = orgFullName;
    }

    public String getInspPerson() {
        return inspPerson;
    }

    public void setInspPerson(String inspPerson) {
        this.inspPerson = inspPerson;
    }

    public String getSearchDept() {
        return searchDept;
    }

    public void setSearchDept(String searchDept) {
        this.searchDept = searchDept;
    }

    public String getXcTypeName() {
        return xcTypeName;
    }

    public void setXcTypeName(String xcTypeName) {
        this.xcTypeName = xcTypeName;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }
}
