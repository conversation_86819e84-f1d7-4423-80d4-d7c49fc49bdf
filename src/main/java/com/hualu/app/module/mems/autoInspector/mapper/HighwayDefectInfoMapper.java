package com.hualu.app.module.mems.autoInspector.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.autoInspector.dto.DefectTypeMappingDTO;
import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_DEFECT_INFO(高速公路病害信息表)】的数据库操作Mapper
* @createDate 2025-04-27 09:02:29
* @Entity com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo
*/
@Mapper
public interface HighwayDefectInfoMapper extends BaseMapper<HighwayDefectInfo> {

}




