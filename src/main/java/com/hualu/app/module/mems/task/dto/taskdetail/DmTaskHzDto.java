package com.hualu.app.module.mems.task.dto.taskdetail;

import lombok.Data;
import org.assertj.core.util.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * 任务单汇总信息
 */
@Data
public class DmTaskHzDto implements Serializable {

    //任务单ID
    private String mtaskId;
    //任务单金额
    private Double totalMoney = 0d;
    //病害数量
    private Integer dssNum = 0;
    //病害详情列表
    List<DmTaskDssViewDto> dssDtos = Lists.newArrayList();
}
