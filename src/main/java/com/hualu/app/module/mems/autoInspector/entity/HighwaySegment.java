package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 公路路段信息表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_SEGMENT")
@ApiModel(value="HighwaySegment对象", description="公路路段信息表")
public class HighwaySegment implements Serializable {

    
    @NotBlank(message="[路段唯一标识]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("路段唯一标识")
    @TableField("ID")
    private String id;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("路段名称")
    @TableField("SEGNAME")
    private String segname;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("起始桩号")
    @TableField("STAKESTART")
    private String stakestart;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("终止桩号")
    @TableField("STAKEEND")
    private String stakeend;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("道路类型")
    @TableField("ROADTYPE")
    private String roadtype;
    
    @ApiModelProperty("里程")
    @TableField("MILEAGE")
    private BigDecimal mileage;
    
    @ApiModelProperty("车道数量")
    @TableField("LANECOUNT")
    private Integer lanecount;
    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("备注")
    @TableField("MARK")
    private String mark;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("城市信息")
    @TableField("CITY")
    private String city;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("区县信息")
    @TableField("DISTRICT")
    private String district;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("养护级别")
    @TableField("CURINGLEVEL")
    private String curinglevel;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("方向")
    @TableField("DIRECTION")
    private String direction;
    
    @ApiModelProperty("最大限速")
    @TableField("MAXSPEEDLIMIT")
    private Integer maxspeedlimit;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("路段编码")
    @TableField("SEGCODE")
    private String segcode;
    
    @ApiModelProperty("创建时间")
    @TableField("CREATEDTIME")
    private LocalDateTime createdtime;
    
    @ApiModelProperty("更新时间")
    @TableField("UPDATEDTIME")
    private LocalDateTime updatedtime;
}
