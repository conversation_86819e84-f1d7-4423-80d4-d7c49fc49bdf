package com.hualu.app.module.mems.finsp.service;

import com.hualu.app.module.mems.finsp.entity.PcClean;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 打草清疏 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
public interface PcCleanService extends IService<PcClean> {

    void saveOrUpdateClean(@Param("bean") PcClean bean, @Param("beforeFiles") MultipartFile[] beforeFiles, @Param("afterFiles") MultipartFile[] afterFiles);

    void removeClean(List<String> ids);
}
