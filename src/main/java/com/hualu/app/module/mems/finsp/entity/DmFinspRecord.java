package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmFinspRecord对象", description="")
public class DmFinspRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DSS_ID")
    private String dssId;

    @NotBlank(message = "经常检查单ID不能为空")
    @TableField("FINSP_ID")
    private String finspId;

    @TableField("LINE_DIRECT")
    private String lineDirect;

    @TableField("STAKE")
    private Double stake;

    @TableField("STRUCT_PART_ID")
    private String structPartId;

    @TableField("STRUCT_COMP_ID")
    private String structCompId;

    @TableField("DSS_TYPE")
    private String dssType;

    @TableField(value = "DSS_DEGREE",strategy = FieldStrategy.IGNORED)
    private String dssDegree;

    @TableField(value = "MNTN_ADVICE",strategy = FieldStrategy.IGNORED)
    private String mntnAdvice;

    @TableField("LANE")
    private String lane;

    @TableField(value = "DSS_POSITION",strategy = FieldStrategy.IGNORED)
    private String dssPosition;

    @TableField(value = "DSS_DESC",strategy = FieldStrategy.IGNORED)
    private String dssDesc;

    @TableField(value = "DSS_L",strategy = FieldStrategy.IGNORED)
    private Double dssL;

    @TableField(value = "DSS_L_UNIT",strategy = FieldStrategy.IGNORED)
    private String dssLUnit;

    @TableField(value = "DSS_W",strategy = FieldStrategy.IGNORED)
    private Double dssW;

    @TableField(value = "DSS_W_UNIT",strategy = FieldStrategy.IGNORED)
    private String dssWUnit;

    @TableField(value = "DSS_D",strategy = FieldStrategy.IGNORED)
    private Double dssD;

    @TableField(value = "DSS_D_UNIT",strategy = FieldStrategy.IGNORED)
    private String dssDUnit;

    @TableField(value = "DSS_N",strategy = FieldStrategy.IGNORED)
    private Double dssN;

    @TableField(value = "DSS_N_UNIT",strategy = FieldStrategy.IGNORED)
    private String dssNUnit;

    @TableField(value = "DSS_A",strategy = FieldStrategy.IGNORED)
    private Double dssA;

    @TableField(value = "DSS_A_UNIT",strategy = FieldStrategy.IGNORED)
    private String dssAUnit;

    @TableField(value = "DSS_V",strategy = FieldStrategy.IGNORED)
    private Double dssV;

    @TableField(value = "DSS_V_UNIT",strategy = FieldStrategy.IGNORED)
    private String dssVUnit;

    @TableField(value = "DSS_P",strategy = FieldStrategy.IGNORED)
    private Double dssP;

    @TableField(value = "DSS_G",strategy = FieldStrategy.IGNORED)
    private Double dssG;

    @TableField("DSS_IMP_FLAG")
    private Integer dssImpFlag;

    @TableField(value = "DSS_QUALITY",strategy = FieldStrategy.IGNORED)
    private Integer dssQuality;

    @TableField("HIS_DSS_ID")
    private String hisDssId;

    @TableField(value = "DSS_CAUSE",strategy = FieldStrategy.IGNORED)
    private String dssCause;

    @TableField("X")
    private Double x;

    @TableField("Y")
    private Double y;

    @TableField("FINSP_ITEM_ID")
    private String finspItemId;

    @TableField("ISPHONE")
    private Integer isphone; // 来源 ： 0 pc端 ，1 移动端

    @TableField("RAMP_ID")
    private String rampId;

    @ApiModelProperty(value = "桥梁ID（分左右幅）")
    @TableField("STRUCT_ID")
    private String structId;

    @TableField("L_DSS_ID")
    private String lDssId;

    @ApiModelProperty(value = "隧道部件")
    @TableField("TUNNEL_MOUTH")
    private String tunnelMouth;

    @ApiModelProperty(value = "起点高度")
    @TableField("START_HIGH")
    private Double startHigh;

    @ApiModelProperty(value = "止点高度")
    @TableField("END_HIGH")
    private Double endHigh;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "桩号高度")
    @TableField("STAKE_HIGH")
    private Double stakeHigh;

    @ApiModelProperty(value = "衬砌部位类型")
    @TableField("LINING_STRUCTURE")
    private String liningStructure;

    @ApiModelProperty(value = "标线部位类型")
    @TableField("MARKING_LINE_POSITION")
    private String markingLinePosition;

    //病害发现日期
    @TableField("FOUND_DATE")
    private Date foundDate;

    @ApiModelProperty(value = "闭合方式（1：任务单闭合，2：专项闭合）")
    @TableField("CLOSE_TYPE")
    private String closeType;

    //k+格式的桩号，冗余字段，只用于经常检查单中病害明细信息加载使用
    @TableField(exist = false)
    private String kStake;

    //部位名称
    @TableField(exist = false)
    private String dssPositionName;

    //严重程度名称
    @TableField(exist = false)
    private String dssDegreeName;

    @TableField(exist = false)
    private String dssNum; //病害数量（描述）

    @TableField(exist = false)
    private String dssTypeName;//病害名称

    @TableField(exist = false)
    private String dssDescQ;//

    @TableField(exist = false)
    private String dssTypeQ;

    @TableField(exist = false)
    private String iStructCompId;//构件编号，冗余字段，只用于经常检查单中病害明细信息加载使用
    @TableField(exist = false)
    private String structPartName; //部件名称

    @TableField(exist = false)
    private String tunnelMouthName;//隧道部件名称

    @TableField(exist = false)
    private String structCompName;//构件名称

    @TableField(exist = false)
    private String repairStatus;	//病害修复状态
    @TableField(exist = false)
    private Integer isPhone;

    @TableField(exist = false)
    //结构物类型
    private String facilityCat;

    //病害性质名称
    @TableField(exist = false)
    private String qualityName;

    @TableField(exist = false)
    private String flagName;

    //车道名称（边坡为级数）
    @TableField(exist = false)
    private String laneName;

    @TableField(exist = false)
    private Double rstake;	//匝道桩号
    @TableField(exist = false)
    private String rampName;	//匝道名称
    @TableField(exist = false)
    private String lineId;

    //结构物名称
    @TableField(exist = false)
    private String structName;

    @TableField(exist = false)
    private String mtaskId;
    @TableField(exist = false)
    private String mtaskCode;
    @TableField(exist = false)
    private String mtaskAccptId;
    @TableField(exist = false)
    private String mtaskAccptCode;
    @TableField(exist = false)
    private String fileIds;

    @TableField(exist = false)
    private int img;

    @TableField(exist = false)
    private String imageHost;

    //用于区间经常检查单版本
    @TableField(exist = false)
    private String finVersion;
}
