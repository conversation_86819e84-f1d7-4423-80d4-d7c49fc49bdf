package com.hualu.app.module.mems.mpc.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MpcFileRef对象", description="")
public class MpcFileRef implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("FILE_ID")
    private String fileId;

    @TableField("FILE_NAME")
    private String fileName;

    @TableField("BILL_TYPE")
    private String billType;

    @TableField("BILL_ID")
    private String billId;


}
