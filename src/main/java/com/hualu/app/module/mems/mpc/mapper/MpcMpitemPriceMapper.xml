<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.mpc.mapper.MpcMpitemPriceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.mpc.entity.MpcMpitemPrice">
        <id column="CONTR_MPITEM_ID" property="contrMpitemId" />
        <result column="CONTR_ID" property="contrId" />
        <result column="MPITEM_CODE" property="mpitemCode" />
        <result column="MPITEM_NAME" property="mpitemName" />
        <result column="UNIT_PRICE" property="unitPrice" />
        <result column="LUMP_PRICE" property="lumpPrice" />
        <result column="UNIT_COUNT" property="unitCount" />
        <result column="MEASURE_UNIT" property="measureUnit" />
        <result column="IS_LUMP" property="isLump" />
        <result column="RATE" property="rate" />
        <result column="MMP_CODE" property="mmpCode" />
        <result column="MP_ITEM_ID" property="mpItemId" />
        <result column="REMARK" property="remark" />
        <result column="PREVENT_PRICE" property="preventPrice" />
        <result column="EFFECT_PRICE" property="effectPrice" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CONTR_MPITEM_ID, CONTR_ID, MPITEM_CODE, MPITEM_NAME, UNIT_PRICE, LUMP_PRICE, UNIT_COUNT, MEASURE_UNIT, IS_LUMP, RATE, MMP_CODE, MP_ITEM_ID, REMARK, PREVENT_PRICE, EFFECT_PRICE
    </sql>

</mapper>
