package com.hualu.app.module.mems.finsp.upload;

import cn.afterturn.easypoi.entity.ImageEntity;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.word.WordUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.service.*;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.mongo.service.BaseFileEntityService;
import com.hualu.app.utils.mems.H_PcFinspCulvertHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CulverImportImp implements IImportStruct{

    private static List<String> excelHeaderTemplates = Lists.newArrayList("路线编码","涵洞名称","涵洞桩号","涵洞跨径","涵洞类型","墩台类型","涵洞功能");

    @Lazy
    @Autowired
    PcProjectScopeService scopeService;

    @Lazy
    @Autowired
    PcFinspService pcFinspService;

    @Autowired
    PcFinspResultService resultService;

    @Autowired
    PcFinspRecordService recordService;

    @Autowired
    PcFinspImageService imageService;

    @Autowired
    BaseFileEntityService fileService;

    @Autowired
    MinioProp minioProp;

    @Value("${fileReposity}")
    String fileReposity;

    @Override
    public String getFacilityCat() {
        return "HD";
    }

    @Override
    public List<PcProjectScope> doImport(PcProject pcProject, File uploadFile) {
        ExcelReader reader = ExcelUtil.getReader(uploadFile);
        //获取excel表头
        List<Object> excelHeaders = reader.read(0).get(0);
        excelHeaderTemplates.removeAll(excelHeaders);
        if (excelHeaderTemplates.size()>0){
            throw new BaseException("excel模版不匹配,未包含列："+ StrUtil.join("2",excelHeaderTemplates));
        }
        reader.addHeaderAlias("路线编码","lineCode");
        reader.addHeaderAlias("涵洞名称","structName");
        reader.addHeaderAlias("涵洞桩号","structStake");
        reader.addHeaderAlias("涵洞跨径","structLevel");
        reader.addHeaderAlias("涵洞类型","structType");
        reader.addHeaderAlias("墩台类型","partType");
        reader.addHeaderAlias("涵洞功能","functionType");
        List<PcProjectScope> pcProjectScopes = reader.readAll(PcProjectScope.class);
        return pcProjectScopes;
    }

    @Override
    public List<BaseNearStructDto> toBaseNearStructDto(List<PcProjectScope> pcProjectScopes) {
        List<BaseNearStructDto> structDtos = Lists.newArrayList();
        for (PcProjectScope item : pcProjectScopes) {
            BaseNearStructDto dto = new BaseNearStructDto();
            dto.setStructName(item.getStructName());
            dto.setStructStake(item.getStructStake());
            dto.setStructLevel(item.getStructLevel());
            dto.setFacilityCat(item.getFacilityCat());
            dto.setLineDirect(item.getLineDirect());
            dto.setX(item.getX());
            dto.setY(item.getY());
            dto.setStructId(item.getStructId());
            dto.setOprtOrgCode(item.getMntOrgId());
            structDtos.add(dto);
        }
        return structDtos;
    }

    @SneakyThrows
    @Override
    public String exportWord(PcFinsp pcFinsp, String qlType) {
        List<PcProjectExportDto> projectDtos = pcFinspService.exportWordData(Lists.newArrayList(pcFinsp.getFinspId()), null);
        if (CollectionUtil.isEmpty(projectDtos)){
            throw new BaseException("当前检查单不存在");
        }
        String name = "_过水涵洞";
        String templateType = "culvert";
        PcProjectExportDto exportDto = projectDtos.get(0);
        H_PcFinspCulvertHelper.initWord(exportDto);
        Map xxMap = BeanUtil.toBean(exportDto, Map.class);
        initRadio(xxMap);
        String fileName = File.separator + exportDto.getFinspCode()+name + ".docx";
        //ClassPathResource classPathResource = new ClassPathResource("excel/pcFinsp/"+templateType+".docx");
        InputStream resourceAsStream = WordUtil.class.getClassLoader().getResourceAsStream("excel/pcFinsp/" + templateType + ".docx");

        File file = new File(fileReposity + fileName);
        ConfigureBuilder builder = Configure.builder();
        XWPFTemplate template = XWPFTemplate.compile(resourceAsStream, builder.build()).render(xxMap);
        try(OutputStream outputStream = Files.newOutputStream(file.toPath())){
            template.writeAndClose(outputStream);
        }
        resourceAsStream.close();
        return fileName;
    }

    @Override
    public void exportWordByZip(String filePath, List<String> finspIds, String prjId) {
        List<PcProjectExportDto> projectDtos = pcFinspService.exportWordData(finspIds, prjId);
        // 排查单对应的病害照片
        List<String> imageRecordIds = projectDtos.stream().map(PcProjectExportDto::getFinspId).collect(Collectors.toList());
        Map<String, List<PcFinspImage>> finspImages = imageService.selectByRecordIds(imageRecordIds);
        int idx = 0;
        for (PcProjectExportDto projectDto : projectDtos) {
            projectDto.setIndex(++idx);
            H_PcFinspCulvertHelper.initWord(projectDto);
            exportWordWithQLAndHD(filePath,projectDto,"culvert","_过水涵洞");
            // 根据finspId查询对应的照片数据，填充到excel表格里面
            projectDto.setJkimage(getImageEntity(finspImages.get(projectDto.getFinspId()),"1"));
            projectDto.setCkimage(getImageEntity(finspImages.get(projectDto.getFinspId()),"2"));
            projectDto.setDnimage(getImageEntity(finspImages.get(projectDto.getFinspId()),"3"));
            projectDto.setDssimage(getImageEntity(finspImages.get(projectDto.getFinspId()),"4"));
        }
        exportExcelWithQLAndHD(filePath,projectDtos,"culvert","过水涵洞");
    }

    private ImageEntity getImageEntity(List<PcFinspImage> finspImages,String imageType) {
        if (CollectionUtil.isNotEmpty(finspImages)){
            PcFinspImage image = finspImages.stream().filter(e -> e.getImageType().equals(imageType)).collect(Collectors.toList()).stream().findFirst().orElse(null);
            if (image != null) {
                try {
                    String fileEntityPath = fileService.getFileEntityPath(image.getImageId());
                    String url = minioProp.getEndpoint() + "/" + fileEntityPath;
                    ImageEntity imageEntity = new ImageEntity();
                    //imageEntity.setUrl("http://172.29.0.20:7001/minio/home/<USER>/12/15/a2ca835942de49e5b0cca000d2796b8d.jpg");
                    imageEntity.setUrl(url);
                    return imageEntity;
                }catch (Exception e){
                    return null;
                }
            }
        }
        return null;
    }
}
