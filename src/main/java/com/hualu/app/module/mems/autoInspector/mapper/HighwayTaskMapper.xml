<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayTaskMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayTask">
            <id property="taskId" column="TASK_ID" jdbcType="VARCHAR"/>
            <result property="taskType" column="TASK_TYPE" jdbcType="VARCHAR"/>
            <result property="segmentId" column="SEGMENT_ID" jdbcType="VARCHAR"/>
            <result property="segmentCode" column="SEGMENT_CODE" jdbcType="VARCHAR"/>
            <result property="segmentName" column="SEGMENT_NAME" jdbcType="VARCHAR"/>
            <result property="inspectDirection" column="INSPECT_DIRECTION" jdbcType="VARCHAR"/>
            <result property="segmentLength" column="SEGMENT_LENGTH" jdbcType="DECIMAL"/>
            <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="taskStatus" column="TASK_STATUS" jdbcType="VARCHAR"/>
            <result property="qualityLevel" column="QUALITY_LEVEL" jdbcType="DECIMAL"/>
            <result property="roadType" column="ROAD_TYPE" jdbcType="VARCHAR"/>
            <result property="stakeStart" column="STAKE_START" jdbcType="VARCHAR"/>
            <result property="stakeEnd" column="STAKE_END" jdbcType="VARCHAR"/>
            <result property="pqiValue" column="PQI_VALUE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        TASK_ID,TASK_TYPE,SEGMENT_ID,
        SEGMENT_CODE,SEGMENT_NAME,INSPECT_DIRECTION,
        SEGMENT_LENGTH,START_TIME,END_TIME,
        CREATED_TIME,UPDATED_TIME,TASK_STATUS,
        QUALITY_LEVEL,ROAD_TYPE,STAKE_START,
        STAKE_END,PQI_VALUE
    </sql>
</mapper>
