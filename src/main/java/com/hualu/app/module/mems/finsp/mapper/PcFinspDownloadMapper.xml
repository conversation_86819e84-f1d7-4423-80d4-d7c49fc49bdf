<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspDownloadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinspDownload">
        <id column="DATA_ID" property="dataId" />
        <result column="DATA_NAME" property="dataName" />
        <result column="DATA_URL" property="dataUrl" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="PRJ_ID" property="prjId" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="STATE" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DATA_ID, DATA_NAME, DATA_URL, MNT_ORG_ID, PRJ_ID, CREATE_USER_ID, CREATE_TIME, STATE
    </sql>

</mapper>
