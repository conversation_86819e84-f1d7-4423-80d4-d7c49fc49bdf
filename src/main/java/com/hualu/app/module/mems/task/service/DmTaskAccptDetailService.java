package com.hualu.app.module.mems.task.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.dto.accpt.DmAccptHzDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;

import java.util.List;

/**
 * <p>
 * 验收单明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskAccptDetailService extends IService<DmTaskAccptDetail> {

    /**
     * 生成维修工程明细
     * @param mtaskId 任务单ID
     * @param detailDtos 任务单明细数据
     */
    void createDmTaskAccptDetails(String mtaskId,List<DmTaskDetailDto> detailDtos);

    void createDmTaskAccptDetails(String accptId,String mtaskId,List<DmTaskDetailDto> detailDtos);

    List<DmTaskAccptDetailDto> selectPageTaskRecord(Page page, QueryWrapper queryWrapper);

    /**
     * 根据任务单详情ID，查询维修工程记录
     * @param mtaskDtlIds
     * @return
     */
    List<DmTaskAccptDetail> selectByMtaskDtlId(List<String> mtaskDtlIds);

    /**
     * 根据验收单ID，查询工程记录
     * @param dmTaskAccptId
     * @return
     */
    List<DmTaskAccptDetail> selectByDmTaskAccptId(String dmTaskAccptId);

    /**
     * 获取验收单汇总明细信息
     * @param mtaskAccptId
     * @return
     */
    DmAccptHzDto getAcceptHzDto(String mtaskAccptId);

    /**
     * 待维修病害查询
     * @param page
     * @param queryWrapper
     * @return
     */
    List<DmTaskDssViewDto> selectWaitAccptDssPage(Page page, QueryWrapper queryWrapper);

    /**
     * 维修工程记录（病害为基准）
     * @param page
     * @param queryWrapper
     * @return
     */
    List<DmTaskAccptDetailDto> selectPageByDss(Page page, QueryWrapper queryWrapper);

    /**
     * 查询病害措施
     * @param dssId
     * @return
     */
    List<DmTaskMpItemViewDto> getMpItemViewDto(String dssId);
}
