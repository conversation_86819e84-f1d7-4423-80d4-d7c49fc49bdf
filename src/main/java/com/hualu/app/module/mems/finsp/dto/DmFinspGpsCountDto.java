package com.hualu.app.module.mems.finsp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class DmFinspGpsCountDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Integer index;

    private String orgCode;

    private String orgName;

    /**
     * 结构物数量
     */
    private Integer structNum;

    /**
     * 原始轨迹数量
     */
    private Integer sourceGpsNum;

    /**
     * 巡查轨迹数量
     */
    private Integer xcGpsNum;
}
