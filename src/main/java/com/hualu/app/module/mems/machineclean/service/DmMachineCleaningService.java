package com.hualu.app.module.mems.machineclean.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.machineclean.entity.DmMachineCleaning;
import com.hualu.app.module.mems.machineclean.entity.MachineCleaningTrack;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

public interface DmMachineCleaningService extends IService<DmMachineCleaning> {

  RestResult<List<DmMachineCleaning>> getMachineCleanList(int page, int pageSize);

  RestResult<String> saveMachineClean(
      DmMachineCleaning machineClean,
      MultipartFile startImage,
      MultipartFile endImage
  );

  RestResult<String> uploadTrack(List<MachineCleaningTrack> tracks);

  RestResult<String> deleteMachineClean(String machineCleanId);

  RestResult<List<MachineCleaningTrack>> getTrack(String machineCleanId);

  RestResult<String> deleteMachineCleanImage(
      String machineCleanId,
      String fileId,
      boolean whetherBeforeImage
  );

  RestResult<DmMachineCleaning> getLastMachineCleanRecord();

}
