<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.comm.mapper.BaseStructCompMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.comm.entity.BaseStructComp">
        <id column="STRUCT_COMP_ID" property="structCompId" />
        <result column="STRUCT_COMP_CODE" property="structCompCode" />
        <result column="STRUCT_COMP_NAME" property="structCompName" />
        <result column="P_STRUCT_COMP_ID" property="pStructCompId" />
        <result column="ASSET_COMP_TYPE" property="assetCompType" />
        <result column="PART_CODE" property="partCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        STRUCT_COMP_ID, STRUCT_COMP_CODE, STRUCT_COMP_NAME, P_STRUCT_COMP_ID, ASSET_COMP_TYPE, PART_CODE
    </sql>
    <select id="selectStructCompByTj" resultType="com.hualu.app.module.mems.comm.entity.BaseStructComp">
        select sc.struct_comp_id, sc.struct_comp_name, sc.p_struct_comp_id, sc.part_code
        from memsdb.BASE_STRUCT_COMP sc
        where sc.struct_comp_id = (select sd.p_struct_comp_id from BASE_STRUCT_COMP sd where sd.struct_comp_id = #{structCompId})
    </select>

</mapper>
