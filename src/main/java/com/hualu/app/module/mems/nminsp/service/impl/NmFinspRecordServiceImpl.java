package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.disease.entity.DiseaseAnalysisRuleDetails;
import com.hualu.app.module.disease.service.DiseaseAnalysisRuleDetailsService;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspRecordMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.mems.nminsp.vo.NmFinspRel;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_StakeHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 新版经常检查记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Service
public class NmFinspRecordServiceImpl extends ServiceImpl<NmFinspRecordMapper, NmFinspRecord> implements NmFinspRecordService {

    @Autowired
    DssImageService imageService;

    @Autowired
    NmFinspService nmFinspService;

    @Autowired
    DssTypeNewService dssTypeNewService;

    @Autowired
    NmInspContentService contentService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    private DiseaseAnalysisRuleDetailsService diseaseAnalysisRuleDetailsService;

    @Override
    public void deleteRecordByFinspIds(List<String> inspIds) {
        LambdaQueryWrapper<NmFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NmFinspRecord::getFinspId,inspIds).select(NmFinspRecord::getDssId);
        List<NmFinspRecord> recordList = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(recordList)){
            List<String> dssIds = recordList.stream().map(NmFinspRecord::getDssId).collect(Collectors.toList());
            // 删除照片
            imageService.delBatchByDssIds(dssIds);
            // 删除病害
            removeByIds(dssIds);
        }
    }

    @Override
    public List<NmFinspRecord> selectRecordByFinspId(String finspId) {
        LambdaQueryWrapper<NmFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmFinspRecord::getFinspId,finspId);
        return list(queryWrapper);
    }

    @Override
    public List<NmFinspRecord> selectRecordByFinspId(List<String> finspIds) {
        List<List<String>> partition = ListUtil.partition(finspIds, 500);
        List<NmFinspRecord> recordList = Lists.newArrayList();
        partition.forEach(rows->{
            recordList.addAll(lambdaQuery().in(NmFinspRecord::getFinspId,rows).list());
        });
        return recordList;
    }

    @Override
    public void saveOrUpdateRecord(NmFinspRecord entity,boolean isApp) {
        NmFinsp nmDinsp = nmFinspService.getById(entity.getFinspId());
        dssTypeNewService.setDssUnit(entity,entity.getDssType());
        if (StrUtil.isBlank(entity.getDssId())){
            entity.setDssId(H_KeyWorker.nextIdToString());
            // 数来源于经常检查：2
            entity.setSource("2");
            save(entity);
            // 病害补充结构ID
            if (StrUtil.isBlank(nmDinsp.getStructId())){
                entity.setStructId(nmDinsp.getStructId()).setStructName(entity.getStructName());
            }
        }else {
            updateById(entity);
        }
        if (isApp){
            //照片只做追加，不做删除处理
            imageService.saveDssImageForApp(entity.getDssId(),entity.getFileIds(),1);
        }else {
            imageService.saveDssImageForPC(entity.getDssId(),entity.getFileIds(),1);
        }
        nmFinspService.updateDssNum(nmDinsp.getFinspId());
        H_StructHelper.refreshNmFinspResult(nmDinsp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRecord(List<String> dssIds) {
        if (CollectionUtil.isEmpty(dssIds)){
            return;
        }
        NmFinspRecord record = getById(dssIds.get(0));
        NmFinsp nmDinsp = nmFinspService.getById(record.getFinspId());
        // 删除照片
        imageService.delBatchByDssIds(dssIds);
        removeByIds(dssIds);
        // 更新检查单病害数量
        nmFinspService.updateDssNum(nmDinsp.getFinspId());
        // 更新结论
        H_StructHelper.refreshNmFinspResult(nmDinsp);
    }

    @Override
    public void showView(List<NmFinspRecord> records, NmFinsp entity) {
        List<DiseaseAnalysisRuleDetails> ruleDetails;
        boolean isAuto;
        if(null != entity && "LM".equals(entity.getFacilityCat()) && entity.getXcType() == 2){
            isAuto = true;
            ruleDetails = diseaseAnalysisRuleDetailsService.queryRuleDetailsByOrg(entity.getMntOrgId());
        }else{
            ruleDetails = null;
            isAuto = false;
        }
        showCommonView(records);
        //各个设施特殊字段回显
        if (entity != null && CollectionUtil.isNotEmpty(records)) {
            records.forEach(v -> {
                v.setStructId(entity.getStructId())
                .setStructName(entity.getStructName()).setLineCode(entity.getLineCode());
                BigDecimal dssL = v.getDssL() == null?null:BigDecimal.valueOf(v.getDssL());
                BigDecimal dssA = v.getDssA() == null?null:BigDecimal.valueOf(v.getDssA());
                if(isAuto && ruleDetails != null && !ruleDetails.isEmpty()){
                    for(DiseaseAnalysisRuleDetails d : ruleDetails){
                        String dssType = d.getDssType();
                        String colom = d. getUnit();
                        BigDecimal minValue = d.getMinValue();
                        BigDecimal maxValue = d.getMaxValue();
                        if(dssType.equals(v.getDssType())){
                            if("m".equals(colom)){
                                if(dssL.compareTo(minValue) == -1){
                                    v.setGrade("3");
                                    break;
                                }else if(dssL.compareTo(maxValue) == 1){
                                    v.setGrade("1");
                                    break;
                                }else{
                                    v.setGrade("2");
                                    break;
                                }
                            }else if("m2".equals(colom)){
                                if(dssA.compareTo(minValue) == -1){
                                    v.setGrade("3");
                                    break;
                                }else if(dssA.compareTo(maxValue) == 1){
                                    v.setGrade("1");
                                    break;
                                }else{
                                    v.setGrade("2");
                                    break;
                                }
                            }
                        }
                    }
                }
            });
            H_StructHelper.showNmFinspView(entity.getFacilityCat(),records);
        }
    }

  @Override
  public String getLastfinspIdByStruct(String structId, String facilityCat) {
      List<Map> list1 = this.baseMapper.getLastfinspIdByStruct(structId,facilityCat);
      if(list1!=null && !list1.isEmpty() && list1.get(0)!=null)return list1.get(0).get("FINSP_ID")+"";
      return null;
  }

    @Override
    public void copyFinspFetail2NewOne(String oldfinspId, String newFinspId, String cat) {
        if(!StringUtil.isNullOrEmpty(oldfinspId)){
            //检查经常检查单病害数量
            if(!"JA".equals(cat)){
//                this.baseMapper.copyFinspFetail2NewOne(oldfinspId,newFinspId);
                if(oldfinspId!=null&&!oldfinspId.isEmpty()){
                    QueryWrapper<NmFinspRecord> recordQueryWrapper = new QueryWrapper<>();
                    recordQueryWrapper.eq("FINSP_ID",newFinspId);
                    this.baseMapper.delete(recordQueryWrapper);
                    List<NmFinspRecord> undoList = this.baseMapper.findUndoDssByFinspId(oldfinspId);
                    for (NmFinspRecord undo:undoList){
                        undo.setHisDssId(undo.getDssId());
                        undo.setDssId(H_KeyWorker.nextIdToString());
                        undo.setFinspId(newFinspId);
                        baseMapper.insert(undo);
                    }
                }

            }else{

            }
        }
    }

    @Override
    public Map<String, List<NmFinspRecord>> copyFinspFetail2NewOne(List<NmFinspRel> relList, Map<String,String> structId2finspIdMap) {
        String oldfinspIds = relList.stream()
            .filter(Objects::nonNull)
            .map(NmFinspRel::getLastFinspId)
            .filter(id -> id != null && !id.isEmpty())
            .collect(Collectors.joining(","));
        Map<String,List<NmFinspRecord>> recordMap = new HashMap<>();
        Set<String> keySet = structId2finspIdMap.keySet();
        List<NmFinspRecord> undoList = new ArrayList<>();
        if (StrUtil.isNotBlank(oldfinspIds)) {
            String[] finspIdArr = oldfinspIds.split(",");
            int batchSize = 300;
            for (int i = 0; i < finspIdArr.length; i += batchSize) {
                int end = Math.min(i + batchSize, finspIdArr.length);
                String batchIds = String.join(",", Arrays.copyOfRange(finspIdArr, i, end));
                List<NmFinspRecord> batchResult = this.baseMapper.findUndoDssByFinspId(batchIds);
                if (batchResult != null) {
                    undoList.addAll(batchResult);
                }
            }
        }

        for (NmFinspRecord undo:undoList){
            undo.setHisDssId(undo.getDssId());
            undo.setDssId(H_KeyWorker.nextIdToString());
            undo.setFinspId(structId2finspIdMap.get(undo.getStructId()));
            baseMapper.insert(undo);
            for(String key:keySet){
                if(key.equals(undo.getStructId())) {
                    List<NmFinspRecord> nmFinspRecords = recordMap.get(undo.getStructId());
                    if (nmFinspRecords == null) {
                        nmFinspRecords = new ArrayList<>();
                    }
                    nmFinspRecords.add(undo);
                    recordMap.put(undo.getStructId(), nmFinspRecords);
                }
            }
        }
        return recordMap;
    }


    @Override
    public void refreshResult4QL() {

        List<NmFinsp> list = nmFinspService.getList();
        int z = list.size();
        for(int j=0;j<list.size();j++){
            System.out.println("=========刷新结论，还剩"+z--);
            H_StructHelper.refreshNmFinspResult(list.get(j));
        }
    }
    private List<NmFinspRecord> batchSelectViewRecord(List<String> finspIds) {
        List<NmFinspRecord> result = new ArrayList<>();
        // 每批处理1000条数据
        int batchSize = 20;
        for (int i = 0; i < finspIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, finspIds.size());
            List<String> batchIds = finspIds.subList(i, endIndex);
            List<NmFinspRecord> batchResult = baseMapper.selectViewRecord(batchIds);
            result.addAll(batchResult);
        }
        return result;
    }


    public List<NmFinspRecord> selectViewRecord(List<String> finspIds) {
        if (CollectionUtils.isEmpty(finspIds)) {
            return new ArrayList<>();
        }
        return batchSelectViewRecord(finspIds);
    }

    @Override
    public List<NmFinspRecord> selectRecordDtlByFinspId(String finspId) {
        return this.baseMapper.selectRecordDtlByFinspId(finspId);
    }

    @Override
    public List<NmFinspRel> getLastfinspIdListByStructList(String collectStructIds, String facilityCat) {
        if (StrUtil.isBlank(collectStructIds)) {
            return Collections.emptyList();
        }
        String[] structIdArr = collectStructIds.split(",");
        List<NmFinspRel> result = new ArrayList<>();
        int batchSize = 100;
        for (int i = 0; i < structIdArr.length; i += batchSize) {
            int end = Math.min(i + batchSize, structIdArr.length);
            String batchIds = "'"+String.join("','", Arrays.copyOfRange(structIdArr, i, end))+"'";
            List<NmFinspRel> batchResult = this.baseMapper.getLastfinspIdListByStructList(batchIds, facilityCat);
            if (batchResult != null) {
                result.addAll(batchResult);
            }
        }
        return result;
    }

    /**
     * 回显公用字段：路线方向，病害数量，病害名称，病害照片
     * @param records
     */
    private void showCommonView(List<NmFinspRecord> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        String facilityCat = records.get(0).getFacilityCat();
        //todo 根据病害ID，查询对应的任务单、验收单号
        List<String> dssIds = records.stream().map(NmFinspRecord::getDssId).collect(Collectors.toList());
        List<NmFinspRecord> viewRecords = this.selectViewRecord(dssIds);
        //查询照片
        Map<String, List<String>> imageMap = imageService.mapByDssIds(dssIds.stream().collect(Collectors.toSet()));

        // 部件名称
        Map<String, String> contentMap = new HashMap<>();
        //部位名称
        Map<String, String> positionMap = new HashMap<>();
        if("QL".equals(facilityCat)){
            List<Map<String, String>> maps = baseMapper.selectOldPartList(records.stream().map(NmFinspRecord::getStructPartId).collect(Collectors.toSet()));
            for(Map<String,String> map:maps){
                contentMap.put(map.get("ID"),map.get("NAME"));
            }
            List<Map<String, String>> positionList = baseMapper.selectOldQlPoistionList(records.stream().map(NmFinspRecord::getDssPosition).collect(Collectors.toSet()));
            for(Map<String,String> map:positionList){
                positionMap.put(map.get("ID"),map.get("NAME"));
            }
        }else {
            contentMap = contentService.getContentMap(Lists.newArrayList(records.stream().map(NmFinspRecord::getStructPartId).collect(Collectors.toSet())));
        }

        Map<String, NmFinspRecord> dssMap = viewRecords.stream().collect(Collectors.toMap(NmFinspRecord::getDssId, Function.identity()));
        for (NmFinspRecord record : records) {

            if(positionMap!=null){
                record.setDssPositionName(positionMap.get(record.getDssPosition()));
            }

            NmFinspRecord item = dssMap.get(record.getDssId());
            if (item != null) {
                record.setMtaskAccptId(item.getMtaskAccptId()).setMtaskAccptCode(item.getMtaskAccptCode())
                        .setMtaskId(item.getMtaskId()).setMtaskCode(item.getMtaskCode())
                        .setRepairStatus(item.getRepairStatus()).setDssTypeName(item.getDssTypeName());

                record.setStructPartName(contentMap.get(record.getStructPartId()));
                record.setLineDirectName(dicService.getDicName("LANE_DIRECTION", item.getLineDirect()));
                record.setLaneName(dicService.getDicName("LANE",item.getLane()));
                List<String> images = imageMap.get(item.getDssId());
                //赋值照片
                if (CollectionUtil.isNotEmpty(images)) {
                    record.setFileIds(StrUtil.join(",", images));
                    item.setImageHost(C_Constant.IMAGE_HOST);
                }
            }
            //回显位置信息
            if (ObjectUtil.isNotEmpty(record.getStake())){
                String stake = record.getStake() == null ? null : record.getStake().toString();
                record.setStakeCn(H_StakeHelper.convertCnStake(stake));
            }

            initDssDisplayNum(record);
            initStatus(record);
        }
    }


    /**
     * 修复状态
     * @param record
     */
    private void initStatus(NmFinspRecord record){
        if(StrUtil.isBlank(record.getRepairStatus())||record.getRepairStatus().equals("0")){
            record.setRepairStatus("待修复");
        }else if(record.getRepairStatus().equals("1")){
            record.setRepairStatus("修复中");
        }else{
            record.setRepairStatus("已修复");
        }
    }

    /**
     * 显示病害
     * @param entity
     */
    private void initDssDisplayNum(NmFinspRecord entity){
        StringBuffer str = new StringBuffer();
        Double dssL = entity.getDssL();
        if(dssL!= null && StrUtil.isNotBlank(entity.getDssLUnit()) && BigDecimal.valueOf(dssL).compareTo(BigDecimal.ZERO) > 0){
            str.append("长："+ dssL + entity.getDssLUnit()+" ");
            entity.setDlNum(dssL).setDlUnit(entity.getDssLUnit());
        }
        Double dssW = entity.getDssW();
        if(dssW!= null && StrUtil.isNotBlank(entity.getDssWUnit()) && BigDecimal.valueOf(dssW).compareTo(BigDecimal.ZERO) > 0){
            str.append("宽："+ dssW + entity.getDssWUnit()+" ");
            entity.setDlNum(dssW).setDlUnit(entity.getDssWUnit());
        }
        Double dssD = entity.getDssD();
        if(dssD!= null && StrUtil.isNotBlank(entity.getDssDUnit()) && BigDecimal.valueOf(dssD).compareTo(BigDecimal.ZERO) > 0){
            str.append("深："+ dssD + entity.getDssDUnit()+" ");
            entity.setDlNum(dssD).setDlUnit(entity.getDssDUnit());
        }
        Double dssA = entity.getDssA();
        if(dssA!= null && StrUtil.isNotBlank(entity.getDssAUnit()) && BigDecimal.valueOf(dssA).compareTo(BigDecimal.ZERO) > 0){
            str.append("面积："+ dssA + entity.getDssAUnit()+" ");
            entity.setDlNum(dssA).setDlUnit(entity.getDssAUnit());
        }
        Double dssV = entity.getDssV();
        if(dssV!= null && StrUtil.isNotBlank(entity.getDssVUnit()) && BigDecimal.valueOf(dssV).compareTo(BigDecimal.ZERO) > 0){
            str.append("体积："+ dssV + entity.getDssVUnit()+" ");
            entity.setDlNum(dssV).setDlUnit(entity.getDssVUnit());
        }
        Double dssN = entity.getDssN();
        if(dssN!= null && StrUtil.isNotBlank(entity.getDssNUnit()) && BigDecimal.valueOf(dssN).compareTo(BigDecimal.ZERO) > 0){
            str.append("数量："+ dssN + entity.getDssNUnit()+" ");
            entity.setDlNum(dssN).setDlUnit(entity.getDssNUnit());
        }
        Double dssP = entity.getDssP();
        if(dssP!= null && BigDecimal.valueOf(dssP).compareTo(BigDecimal.ZERO) > 0){
            str.append("百分比："+ dssP +"% ");
            entity.setDlNum(dssP).setDlUnit("%");
        }
        Double dssG = entity.getDssG();
        if(dssG!= null && BigDecimal.valueOf(dssG).compareTo(BigDecimal.ZERO) > 0){
            str.append("角度："+ dssG +"度 ");
            entity.setDlNum(dssG).setDlUnit("度");
        }
        entity.setDssNum(str.toString());
    }


}
