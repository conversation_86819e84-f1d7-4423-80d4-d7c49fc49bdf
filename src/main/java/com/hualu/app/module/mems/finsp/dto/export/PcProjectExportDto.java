package com.hualu.app.module.mems.finsp.dto.export;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Accessors(chain = true)
@Data
public class PcProjectExportDto implements Serializable {

    private Integer index;

    private String prjName;

    @ApiModelProperty(value = "主键")
    @TableId("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "流程ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "结构物ID")
    @TableField("STRUCT_ID")
    private String structId;

    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @ApiModelProperty(value = "设施分类")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "排查日期")
    @TableField("INSP_DATE")
    private LocalDate inspDate;

    @ApiModelProperty(value = "项目公司")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "巡查人")
    @TableField("INSP_PERSON")
    private String inspPerson;

    @ApiModelProperty(value = "排查单位名称")
    @TableField("INSP_ORG_NAME")
    private String inspOrgName;

    @ApiModelProperty(value = "单位类型（1：定检单位、2：养护单位）")
    @TableField("MNT_TYPE")
    private String mntType;

    @ApiModelProperty(value = "主要隐患情况描述")
    @TableField(value = "SITUATION_DESCRIPTION",strategy = FieldStrategy.IGNORED)
    private String situationDescription;


    @ApiModelProperty(value = "排查单编码")
    @TableField("FINSP_CODE")
    private String finspCode;

    @ApiModelProperty(value = "水中桩基-最近水下基础检测年份；涵洞-不均匀沉降，跳车-检查结果")
    @TableField(value = "ATTR1",strategy = FieldStrategy.IGNORED)
    private String attr1;

    @ApiModelProperty(value = "水中桩基-检测最大冲刷深度（m）；涵洞-不均匀沉降，跳车-处治结论")
    @TableField(value = "ATTR2",strategy = FieldStrategy.IGNORED)
    private String attr2;

    @ApiModelProperty(value = "水中桩基-冲刷深度是否已收敛稳定或仍在发展；涵洞-边坡开裂、冲刷、滑坡等-排查结果")
    @TableField(value = "ATTR3",strategy = FieldStrategy.IGNORED)
    private String attr3;

    @ApiModelProperty(value = "水中桩基-现冲刷深度是否超原设计冲刷深度；涵洞-边坡开裂、冲刷、滑坡等-处置建议")
    @TableField(value = "ATTR4",strategy = FieldStrategy.IGNORED)
    private String attr4;

    //渲染单选按钮
    private Object attr4Yes;
    private Object attr4No;

    @ApiModelProperty(value = "水中桩基-现有效桩长/埋置深度（m）；涵洞-淤塞，排水不畅-排查结果")
    @TableField(value = "ATTR5",strategy = FieldStrategy.IGNORED)
    private String attr5;

    @ApiModelProperty(value = "水中桩基-桩基钢筋是否外露、缩颈；涵洞-淤塞，排水不畅-处置建议")
    @TableField(value = "ATTR6",strategy = FieldStrategy.IGNORED)
    private String attr6;

    private Object attr6Yes;
    private Object attr6No;

    @ApiModelProperty(value = "水中桩基-现河床表层地质；涵洞-严重淤塞，基本失去过水功能-排查结果")
    @TableField(value = "ATTR7",strategy = FieldStrategy.IGNORED)
    private String attr7;

    @ApiModelProperty(value = "水中桩基-最大冲刷深度复算结果（m）；涵洞-严重淤塞，基本失去过水功能-处置建议")
    @TableField(value = "ATTR8",strategy = FieldStrategy.IGNORED)
    private String attr8;

    @ApiModelProperty(value = "桥面排水-泄水孔是否有淤堵；涵洞-明显沉降、开裂、变形-排查结果")
    @TableField(value = "ATTR9",strategy = FieldStrategy.IGNORED)
    private String attr9;

    private Object attr9Yes;
    private Object attr9No;

    @ApiModelProperty(value = "桥面排水-共多少处泄水孔存在淤堵；涵洞-明显沉降、开裂、变形-处置建议")
    @TableField(value = "ATTR10",strategy = FieldStrategy.IGNORED)
    private String attr10;

    @ApiModelProperty(value = "桥面排水-桥下落水管是否有破损、脱落；涵洞-浆砌片（块）石涵身砌体开裂、松动、外凸-排查结果")
    @TableField(value = "ATTR11",strategy = FieldStrategy.IGNORED)
    private String attr11;

    private Object attr11Yes;
    private Object attr11No;

    @ApiModelProperty(value = "桥面排水-共多少落水管存在破损、脱落；涵洞-浆砌片（块）石涵身砌体开裂、松动、外凸-处置建议")
    @TableField(value = "ATTR12",strategy = FieldStrategy.IGNORED)
    private String attr12;

    @ApiModelProperty(value = "桥面排水-桥下排水沟是否有破损，排水是否顺接；涵洞-基础淘空-排查结果")
    @TableField(value = "ATTR13",strategy = FieldStrategy.IGNORED)
    private String attr13;

    private Object attr13Yes;
    private Object attr13No;

    @ApiModelProperty(value = "桥面排水-共多少水沟存在破损、排水未顺接；涵洞-基础淘空-处置建议")
    @TableField(value = "ATTR14",strategy = FieldStrategy.IGNORED)
    private String attr14;

    @ApiModelProperty(value = "水中桩基-现冲刷深度是否超原设计冲刷深度-问题描述；涵洞-漏土或漏沙严重-排查结果")
    @TableField(value = "ATTR15",strategy = FieldStrategy.IGNORED)
    private String attr15;

    @ApiModelProperty(value = "水中桩基-桩基钢筋是否外露、缩颈-问题描述；涵洞-漏土或漏沙严重-处置建议")
    @TableField(value = "ATTR16",strategy = FieldStrategy.IGNORED)
    private String attr16;

    @ApiModelProperty(value = "涵洞-排查结论:基本无风险（四级），低风险（三级），中风险（二级），高风险（一级）")
    @TableField(value = "ATTR17",strategy = FieldStrategy.IGNORED)
    private String attr17;

    @ApiModelProperty(value = "涵洞-需要应急措施：是，否")
    @TableField(value = "ATTR18",strategy = FieldStrategy.IGNORED)
    private String attr18;

    @ApiModelProperty(value = "涵洞-需要专业检查：是，否")
    @TableField(value = "ATTR19",strategy = FieldStrategy.IGNORED)
    private String attr19;

    @ApiModelProperty(value = "涵洞-几节墙身出现裂缝")
    @TableField(value = "ATTR20",strategy = FieldStrategy.IGNORED)
    private String attr20;

    @ApiModelProperty(value = "涵洞-几节盖板出现裂缝")
    @TableField(value = "ATTR21",strategy = FieldStrategy.IGNORED)
    private String attr21;

    @ApiModelProperty(value = "养护处治类型(1:小修处治,2:专项处治,3:应急处治,4:无需处治)")
    @TableField(value = "DEAL_TYPE",strategy = FieldStrategy.IGNORED)
    private String dealType;

    @ApiModelProperty(value = "结构物桩号")
    @TableField("STRUCT_STAKE")
    private String structStake;

    @ApiModelProperty(value = "设计单位")
    @TableField("DESIGN_UNIT")
    private String designUnit;

    @ApiModelProperty(value = "部件类型(墩台类型:混凝土,钢筋混凝土)")
    @TableField("PART_TYPE")
    private String partType;

    @ApiModelProperty(value = "功能类型(过水，过人)")
    @TableField("FUNCTION_TYPE")
    private String functionType;

    @ApiModelProperty(value = "结构类型（边坡：路堤、路堑；涵洞类型）")
    @TableField("STRUCT_TYPE")
    private String structType;

    private String structLevel;

    /**
     * 涵洞照片：1：涵洞进口全貌照片，2：涵洞出口全貌照片，3：涵洞内通视照片，4：病害代表照片
     */
    @TableField(exist = false)
    private List<PcFinspImage> finspImages;

    //进口照片
    private Object jkimage;

    //出口照片
    private Object ckimage;

    //洞内照片
    private Object dnimage;

    //病害照片
    private Object dssimage;

    private String lineName;

    private String lineCode;

    private CulvertWordDto wordDto = new CulvertWordDto();
}
