package com.hualu.app.module.mems.finsp.service;

import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DmFinspItemService extends IService<DmFinspItem> {

    /**
     * 查询经常检查项
     * @param dmFinsp
     * @return
     */
    List<DmFinspItem> queryItems(DmFinsp dmFinsp,String mntOrgId);
}
