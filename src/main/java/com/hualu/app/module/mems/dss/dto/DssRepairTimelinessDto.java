package com.hualu.app.module.mems.dss.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 病害维修及时性
 */
@Accessors(chain = true)
@Data
public class DssRepairTimelinessDto implements Serializable {

    /**
     * 正常维修数量
     */
    private int normalNum;

    /**
     * 超时已维修
     */
    private int timeoutRepairNum;

    /**
     * 超时未维修
     */
    private int noRepairNum;
}
