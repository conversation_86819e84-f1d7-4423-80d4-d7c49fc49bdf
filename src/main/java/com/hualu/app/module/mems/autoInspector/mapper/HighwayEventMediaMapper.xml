<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayEventMediaMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayEventMedia">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="eventId" column="EVENT_ID" jdbcType="VARCHAR"/>
            <result property="name" column="NAME" jdbcType="VARCHAR"/>
            <result property="imgUrl" column="IMG_URL" jdbcType="VARCHAR"/>
            <result property="roiUrl" column="ROI_URL" jdbcType="VARCHAR"/>
            <result property="rect" column="RECT" jdbcType="VARCHAR"/>
            <result property="subId" column="SUB_ID" jdbcType="DECIMAL"/>
            <result property="ignoreCalc" column="IGNORE_CALC" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,EVENT_ID,NAME,
        IMG_URL,ROI_URL,RECT,
        SUB_ID,IGNORE_CALC
    </sql>
</mapper>
