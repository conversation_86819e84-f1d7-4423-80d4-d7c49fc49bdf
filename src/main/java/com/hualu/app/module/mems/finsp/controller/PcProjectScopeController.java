package com.hualu.app.module.mems.finsp.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.mapper.PcProjectScopeMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspService;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "结构物专项范围表",description = "结构物专项范围表 前端控制器")
@RestController
@RequestMapping("/pcProjectScope")
public class PcProjectScopeController extends M_MyBatisController<PcProjectScope, PcProjectScopeMapper> {

    @Autowired
    PcProjectScopeService pcProjectScopeService;

    @Lazy
    @Autowired
    PcFinspService pcFinspService;


    /**
     * 删除结构物
     * @param structIds
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @PostMapping("deleteByStructIds")
    public RestResult<String> deleteByStructIds(@RequestParam("structIds") String structIds){
        List<String> ids = StrUtil.split(structIds, ",");
        pcProjectScopeService.removeByIds(ids);
        // 只逻辑删除结构物，不删除检查单
        //pcFinspService.removeByStructIds(ids);
        return RestResult.success("操作成功");
    }

    /**
     * 获取结构物信息
     *
     * @param structId
     * @param facilityCat
     * @return
     */
    @GetMapping("getByStructId")
    public RestResult<BaseStructDto> getByStructId(String structId, @RequestParam(value = "facilityCat", defaultValue = "BP") String facilityCat) {
        PcProjectScope scopeInfo = pcProjectScopeService.getByStructId(structId);
        if (scopeInfo == null) {
            return RestResult.success(null);
        }
        BaseStructDto dto = new BaseStructDto();

        dto.setStructId(scopeInfo.getStructId());
        dto.setStructName(scopeInfo.getStructName());
        dto.setX(scopeInfo.getX());
        dto.setY(scopeInfo.getY());
        return RestResult.success(dto);
    }

    /**
     * 更新结构经纬度
     * @param structId
     * @param x
     * @param y
     * @return
     */
    @PostMapping("updateXy")
    public RestResult<String> updateXy(String structId, Double x, Double y) {
        PcProjectScope projectScope = pcProjectScopeService.getById(structId);
        projectScope.setX(x);
        projectScope.setY(y);
        pcProjectScopeService.updateById(projectScope);
        return RestResult.success("操作成功");
    }
}
