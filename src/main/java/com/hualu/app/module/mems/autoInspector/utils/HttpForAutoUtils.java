package com.hualu.app.module.mems.autoInspector.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hualu.app.module.mongo.dto.MgFileDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Slf4j
public class HttpForAutoUtils {

    /**
     * 加签算法
     *
     * @param path
     * @param params
     * @param appkey
     * @param timestamp
     * @param <T>
     * @return
     */
    public static <T> String doHmacSHA2(String path, Map<String, T> params, String
            appsecret, String appkey, String timestamp) {
        List<Map.Entry<String, T>> parameters = new ArrayList<Map.Entry<String,
                T>>(params.entrySet()) ;
        SecretKeySpec signingKey = new SecretKeySpec(appsecret.getBytes(), "HmacSHA256") ;
        Charset CHARSET_UTF8 = Charset.forName("UTF-8") ;
        Mac mac;
        try {
            mac = Mac.getInstance("HmacSHA256") ;
            mac.init(signingKey) ;
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException(e.getMessage(), e) ;
        } catch (InvalidKeyException e) {
            throw new IllegalStateException(e.getMessage(), e) ;
        }
        if (path != null && path.length() > 0) {
            mac.update(path.getBytes(CHARSET_UTF8)) ;
        }
        if (appkey.length() > 0) {
            mac.update(appkey.getBytes(CHARSET_UTF8)) ;
        }
        if (parameters != null) {
            Collections.sort(parameters, new MapEntryComparator<String, T>()) ;
            for (Map.Entry<String, T> parameter : parameters) {
                byte[] name = parameter.getKey ().getBytes(CHARSET_UTF8) ;
                Object value;
                //如果出现 object 对象中嵌套 object 请将对应的 value值设置为字符串
                if (parameter.getValue() instanceof String) {
                    value = parameter.getValue() ;
                } else {
                    value = JSON.toJSONString (parameter.getValue()) ;
                }
                if (value instanceof Collection) {
                    for (Object o : (Collection) value) {
                        mac.update(name) ;
                        if (o != null) {
                            mac.update(o.toString ().getBytes(CHARSET_UTF8)) ;
                        }
                    }
                } else {
                    mac.update(name) ;
                    if (value != null) {
                        mac.update(value.toString ().getBytes(CHARSET_UTF8)) ;
                    }
                }
            }
        }
        if (timestamp != null && timestamp.length() > 0) {
            mac.update(timestamp.toString ().getBytes(CHARSET_UTF8)) ;
        }
        return encodeHexStr(mac.doFinal()) ;
    }

    /**
     * 将字节数组转换成 16进制字符串
     *
     * @param bytes
     * @return
     */
    public static String encodeHexStr(final byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        char[] digital = "0123456789ABCDEF".toCharArray () ;
        char[] result = new char[bytes.length * 2] ;
        for (int i = 0; i < bytes.length; i++) {
            result[i * 2] = digital[(bytes[i] & 0xf0) >> 4] ;
            result[i * 2 + 1] = digital[bytes[i] & 0x0f];
        }
        return new String (result) ;
    }

    /**
     * Map 参数排序类
     * 将参数按照参数名称进行排序
     * @param <K>
     * @param <V>
     */
    static class MapEntryComparator<K, V> implements Comparator<Map.Entry<K, V>> {
        @Override
        public int compare(Map.Entry<K, V> o1, Map.Entry<K, V> o2) {
            if (o1 == o2) {
                return 0;
            }
            final String k1 = o1.getKey().toString () ;
            final String k2 = o2.getKey().toString () ;
            int l1 = k1.length();
            int l2a = k2.length();
            for (int i = 0; i < l1; i++) {
                char c1 = k1.charAt(i) ;
                char c2;
                if (i < l2a) {
                    c2 = k2.charAt(i) ;
                } else {
                    return 1;
                }
                if (c1 > c2) {
                    return 1;
                } else if (c1 < c2) {
                    return-1 ;
                }
            }
            if (l1 < l2a) {
                return-1 ;
            } else if (l1 == l2a) {
                return 0;
            } else {
                return-1;
            }
        }
    }

    private static CloseableHttpClient getHttpClient() {
        RequestConfig defaultRequestConfig = RequestConfig.custom()
                .setSocketTimeout(100000)
                .setConnectTimeout(100000)
                .setConnectionRequestTimeout(100000)
                .setStaleConnectionCheckEnabled(true)
                .build() ;
        return HttpClients.custom().setDefaultRequestConfig (defaultRequestConfig).build() ;
    }

    /**
     * 发送GET 请求
     * @param url 请求 url
     * @param timestamp 请求头中的时间戳
     * @param sik sik 值
     * @param sign 加签后的签名
     * @param paramMap 参数
     * @return
     * @throws Exception
    8
     */
    public static JSON doGet(String url, String timestamp, String sik, String sign,
                             Map<String, Object> paramMap) throws Exception {
        StringBuffer params = new StringBuffer() ;
        if (paramMap.size() > 0) {
            Set<String> keys = paramMap.keySet() ;
            for (String key : keys) {
                params.append(key + "=" + paramMap.get(key)) ;
                params.append("&") ;
            }
            url = url + "?" + params.substring(0,params.lastIndexOf("&")) ;
        } // 注意如果url字段中出现 '+' 字符，请在请求前将+转义为 '%2B'
        CloseableHttpClient httpClient = getHttpClient() ;
        HttpGet httpGet = new HttpGet(url) ;
        // 设置header
        httpGet.setHeader("X-Cgcloud-Open-Timestamp", timestamp) ;
        httpGet.setHeader("X-Cgcloud-Open-Sik", sik) ;
        httpGet.setHeader("X-Cgcloud-Open-Sign", sign) ;
        CloseableHttpResponse response = null ;
        try {
            response = httpClient.execute(httpGet) ;
            HttpEntity entity = response.getEntity () ;
            if (entity != null) {
                String result = EntityUtils.toString (entity) ;
                EntityUtils.consume(entity) ;
                if (result != null && !result.isEmpty()) {
                    JSONObject map = JSONObject.parseObject(result) ;
                    return map;
                }
            }
        } catch (Exception e) {
            log.error("获取失败{}", e.getMessage()); ;
        } finally {
            httpGet.releaseConnection() ;
            if (httpClient != null) {
                httpClient.close() ;
            }
        }
        return null;
    }
    /**
     * 发送Post请求
     * @param url 请求 url
     * @param timestamp 设置请求头中的时间戳
     * @param sik 设置请求头中 sik值
     * @param sign 设置请求头中加签后的签名
     * @param paramMap 参数
     * @return
     * @throws Exception
     */
    public static JSON doPost(String url, String timestamp,String sik, String sign,
                              Map<String, Object> paramMap) throws Exception {
        CloseableHttpClient httpClient = getHttpClient() ;
        HttpPost httpPost = new HttpPost(url) ;
        httpPost.setHeader("X-Cgcloud-Open-Timestamp", timestamp) ;
        httpPost.setHeader("X-Cgcloud-Open-Sik", sik) ;
        httpPost.setHeader("X-Cgcloud-Open-Sign", sign) ;
        List<NameValuePair> nvps = new ArrayList<NameValuePair>() ;
        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            nvps.add(new BasicNameValuePair(entry.getKey (), (String) entry.getValue())) ;
        }
        httpPost.setEntity (new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8)) ;
        try {
            CloseableHttpResponse response = httpClient.execute(httpPost) ;
            HttpEntity entity = response.getEntity() ;
            if (entity != null) {
                String result = EntityUtils.toString (entity) ;
                EntityUtils.consume(entity) ;
                if (result != null && !result.isEmpty()) {
                    JSONObject map = JSONObject.parseObject(result) ;
                    return map;
                }
            }
        } catch (Exception e) {
            log.error("失败{}", e) ;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection() ;//释放资源
            }
            if (httpClient != null) {
                httpClient.close() ;//释放资源
            }
        }
        return null;
    }

    public static String getTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    public static String convertToUtcDateTime(String dateStr) throws DateTimeParseException {
        // 解析输入字符串为 LocalDate
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(dateStr, inputFormatter);

        // 转换为当天的UTC零点时间
        ZonedDateTime utcMidnight = date.atStartOfDay(ZoneOffset.UTC);

        // 定义目标格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX");

        // 格式化输出
        return utcMidnight.format(outputFormatter);
    }

    public static String getCurrentUtcTime() {
        return ZonedDateTime.now(ZoneOffset.UTC)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX"));
    }

    /**
     * 上传图片文件到远程服务器
     * @param files 文件数组
     * @return 上传成功后的文件ID列表
     */
    public static List<String> uploadImage(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return new ArrayList<>();
        }

        String url = "http://***********:7001/hwaymems/baseFileEntity/uploadImage";

        try {
            // 创建OkHttpClient
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(100, TimeUnit.SECONDS)
                    .readTimeout(100, TimeUnit.SECONDS)
                    .writeTimeout(100, TimeUnit.SECONDS)
                    .build();

            // 创建MultipartBody
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("userCode", "sjzx-zengxj");

            // 添加文件
            for (MultipartFile file : files) {
                builder.addFormDataPart(
                        "files",
                        file.getOriginalFilename(),
                        okhttp3.RequestBody.create(
                                okhttp3.MediaType.parse(file.getContentType()),
                                file.getBytes()
                        )
                );
            }

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(builder.build())
                    .build();

            // 执行请求
            Response response = client.newCall(request).execute();

            if (response.isSuccessful() && response.body() != null) {
                String result = response.body().string();

                // 解析返回结果
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject != null && jsonObject.getInteger("code") == 1) {
                    return jsonObject.getJSONArray("data").toJavaList(String.class);
                } else {
                    log.error("上传图片失败: {}", result);
                }
            } else {
                log.error("上传图片请求失败: {}", response.code());
            }
        } catch (Exception e) {
            log.error("上传图片异常: {}", e.getMessage(), e);
        }

        return new ArrayList<>();
    }

    /**
     * 上传文件到远程服务器
     * @param files 文件数组
     * @return 上传成功后的文件实体列表
     */
    public static List<MgFileDto> uploadFile(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return new ArrayList<>();
        }

        String url = "http://***********:7001/hwaymems/baseFileEntity/uploadFile";

        try {
            // 创建OkHttpClient
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(100, TimeUnit.SECONDS)
                    .readTimeout(100, TimeUnit.SECONDS)
                    .writeTimeout(100, TimeUnit.SECONDS)
                    .build();

            // 创建MultipartBody
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("userCode", "sjzx-zengxj");

            // 添加文件
            for (MultipartFile file : files) {
                builder.addFormDataPart(
                        "files",
                        file.getOriginalFilename(),
                        okhttp3.RequestBody.create(
                                okhttp3.MediaType.parse(file.getContentType()),
                                file.getBytes()
                        )
                );
            }

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(builder.build())
                    .build();

            // 执行请求
            Response response = client.newCall(request).execute();

            if (response.isSuccessful() && response.body() != null) {
                String result = response.body().string();

                // 解析返回结果
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject != null && jsonObject.getInteger("code") == 1) {
                    return jsonObject.getJSONArray("data").toJavaList(MgFileDto.class);
                } else {
                    log.error("上传图片失败: {}", result);
                }
            } else {
                log.error("上传图片请求失败: {}", response.code());
            }
        } catch (Exception e) {
            log.error("上传图片异常: {}", e.getMessage(), e);
        }

        return new ArrayList<>();
    }

    /**
     * 发送GET请求并获取JSON响应字符串
     * @param url 请求URL
     * @param params 请求参数
     * @param method 请求方法 GET/POST
     * @return 响应字符串
     * @throws Exception 请求异常
     */
    public static String getJson(String url, Map<String, Object> params, String method) throws Exception {
        // 应从DataSyncServiceImpl获取SIK和SIS值，这里临时创建
        final String sik = "dummySik"; // 实际使用时应从配置获取
        final String sis = "dummySis"; // 实际使用时应从配置获取
        
        if (params != null && !params.isEmpty() && "GET".equalsIgnoreCase(method)) {
            StringBuilder urlParams = new StringBuilder();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (urlParams.length() > 0) {
                    urlParams.append("&");
                }
                urlParams.append(entry.getKey()).append("=").append(entry.getValue());
            }
            url = url + "?" + urlParams.toString();
        }

        // 获取当前时间戳
        String timestamp = getTimestamp();
        
        // 计算签名
        String sign = null;
        if (params != null) {
            // 将参数转换为TreeMap确保排序
            TreeMap<String, Object> sortedParams = new TreeMap<>(params);
            // 从URL中提取路径
            String path = url.substring(url.indexOf("/openapi"));
            if (path.contains("?")) {
                path = path.substring(0, path.indexOf("?"));
            }
            
            // 计算签名
            sign = doHmacSHA2(path, sortedParams, sis, sik, timestamp);
        }
        
        // 发送请求
        if ("GET".equalsIgnoreCase(method)) {
            JSON json = doGet(url, timestamp, sik, sign, params != null ? params : new HashMap<>());
            return json != null ? json.toJSONString() : null;
        } else if ("POST".equalsIgnoreCase(method)) {
            JSON json = doPost(url, timestamp, sik, sign, params != null ? params : new HashMap<>());
            return json != null ? json.toJSONString() : null;
        } else {
            throw new IllegalArgumentException("不支持的请求方法: " + method);
        }
    }
}
