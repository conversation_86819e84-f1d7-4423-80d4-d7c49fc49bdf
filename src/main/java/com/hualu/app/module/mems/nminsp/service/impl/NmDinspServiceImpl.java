package com.hualu.app.module.mems.nminsp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.basedata.service.impl.BaseRouteLogicServiceImpl;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.dto.NmDinspFacilityStat;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspCommonService;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.struct.IStruct;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.mongo.service.BaseFileEntityService;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.dto.WorkPartinameDto;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.hualu.app.module.workflow.service.WfworkitemService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_InspCodeHelper;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import io.minio.MinioClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTbl;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblGrid;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblLayoutType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 新版日常巡查 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class NmDinspServiceImpl extends ServiceImpl<NmDinspMapper, NmDinsp> implements NmDinspService, IWorkItemEventHandler, OrderInfoHandler {

    @Autowired
    private NmDinspRecordService nmDinspRecordService;

    @Autowired
    private DssInfoService dssInfoService;

    @Autowired
    private DssImageService dssImageService;

    @Autowired
    NmDinspResultService resultService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    FwRightUserService userService;

    @Autowired
    BaseRouteLogicService logicService;

    @Autowired
    BaseLineService lineService;

    @Autowired
    WfworkitemService wfworkitemService;

    @Lazy
    @Autowired
    NmDinspCommonService nmDinspCommonService;


    @Autowired
    BaseFileEntityService fileEntityService;

    @Resource
    MinioClient minioClient;

    @Value("${fileReposity}")
    String fileReposity;

    @Value("${resource.path}")
    private String filepath;

    // 格式：XCD-设施类型-机构编码-日期（示例：XCD-BP-***********）
    private static final String DINSP_CODE_TEMPLATE = "XCD-{}-{}-{}";

    private static final String INSP_TIME = "09:00:00-18:00:00";

    private static final DateTimeFormatter TIME_FORMATTER =
            DateTimeFormatter.ofPattern("HH:mm:ss");

    // 2. 线程安全的日期格式化（Java8 DateTime API）
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd")
            .withZone(ZoneId.of("Asia/Shanghai"));

    public static String processDefName = "gdcg.emdc.mems.dm.NDInspWorkFlow";

    Map<String, Integer> incur = new ConcurrentHashMap<>();

    Map<String, Integer> imageCache = new HashMap<>();

    @Autowired
    private BaseRouteLogicServiceImpl baseRouteLogicServiceImpl;

    @Override
    public List<NmDinsp> listDinsp(Set<String> dinspIdSet) {
        List<NmDinsp> nmDinspList = lambdaQuery().select(NmDinsp::getDinspId, NmDinsp::getStructId, NmDinsp::getStructName).list();
        return nmDinspList;
    }

    @Override
    public List<NmDinsp> listDinspIdAndInspFrequency(List<String> dinspIds) {
        if (CollectionUtil.isEmpty(dinspIds)) {
            return Collections.emptyList();
        }
        List<NmDinsp> nmDinspList = lambdaQuery().in(NmDinsp::getDinspId, dinspIds)
                .select(NmDinsp::getDinspId, NmDinsp::getInspFrequency,NmDinsp::getFacilityCat)
                .list();
        return nmDinspList;
    }

    @Override
    public List<NmDinsp> listDinsp(NmDinspCommon dinspCommon) {

        return lambdaQuery().eq(NmDinsp::getMntOrgId, dinspCommon.getMntOrgId())
                .eq(NmDinsp::getInspDate, dinspCommon.getInspDate()).list();
    }

    @Override
    public List<NmDinsp> listStructId(NmDinspCommon dinspCommon, String facilityCat) {
        return lambdaQuery().select(NmDinsp::getStructId)
                .eq(NmDinsp::getMntOrgId, dinspCommon.getMntOrgId())
                .eq(NmDinsp::getInspDate, dinspCommon.getInspDate())
                .eq(NmDinsp::getFacilityCat, facilityCat).list();
    }

    @Override
    public List<NmDinsp> getNmDinspByProcessInstId(Long processInstId) {
        LambdaQueryWrapper<NmDinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmDinsp::getProcessinstid, processInstId);
        return list(queryWrapper);
    }

    /**
     * 保存信息到病害库中
     *
     * @param nmDinsp
     * @param records
     */
    private void saveDssInfo(NmDinsp nmDinsp, List<NmDinspRecord> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        Map<String, BaseLine> allLineMap = lineService.getAllLineMap();
        String mainRoadId = allLineMap != null && allLineMap.containsKey(nmDinsp.getLineCode())
                ? allLineMap.get(nmDinsp.getLineCode()).getLineId()
                : nmDinsp.getLineCode();
        List<DssInfo> dssInfoList = Lists.newArrayList();
        records.forEach(item -> {
            DssInfo ds = new DssInfo();
            BeanUtil.copyProperties(item,ds);
            if (StrUtil.isBlank(ds.getStructId())){
                ds.setStructId(nmDinsp.getStructId());
            }
            ds.setDssCode(item.getDssId());
            ds.setRlStakeNew(item.getStake());
            ds.setFindDssUserName(nmDinsp.getInspPerson());
            ds.setDssImpFlag(0);
            ds.setRelTaskCode(nmDinsp.getDinspId());
            ds.setDssSource(1);
            ds.setRepairStatus(0);
            ds.setDealStatus(0);
            ds.setFoundDate(nmDinsp.getInspDate());
            ds.setMainRoadId(mainRoadId);
            ds.setLinedirect(item.getLineDirect());
            ds.setRoutecode(nmDinsp.getRouteCode());

            // todo 是否需要设置orgId
            dssInfoList.add(ds);
        });
        dssInfoService.saveOrUpdateBatch(dssInfoList);
    }


    @Override
    public void updateNmDinspStatus(long processInstId, int status) {
        baseMapper.updateStatus(processInstId, status);
    }


    @Override
    public void updateDssNum(String dinspId) {
        baseMapper.updateDssNum(dinspId);
        createProcess(dinspId);
    }

    @Override
    public String getNextCode(String facilityCat, String orgEn, LocalDate localDate) {
        validateParams(facilityCat, orgEn, localDate);
        return String.format("%s-%04d", generateBaseCode(facilityCat, orgEn, localDate), getNextSerial(facilityCat, orgEn, localDate) + 1);
    }

    /**
     * 组装单号
     *
     * @param facilityCat
     * @param orgEn
     * @param serial
     * @return
     */
    @Override
    public String buildDinspCode(String facilityCat, String orgEn, int serial, LocalDate localDate) {
        validateParams(facilityCat, orgEn, localDate);
        return String.format("%s-%04d", generateBaseCode(facilityCat, orgEn, localDate), serial);
    }

    @Override
    public Integer getNextSerial(String facilityCat, String orgEn, LocalDate localDate) {
        validateParams(facilityCat, orgEn, localDate);
        return queryMaxSerialFromDB(facilityCat, orgEn, localDate);
    }

    // 提取公共方法：参数校验
    private void validateParams(String facilityCat, String orgEn, LocalDate localDate) {
        Objects.requireNonNull(facilityCat, "设施类型不能为空");
        Objects.requireNonNull(orgEn, "机构编码不能为空");
        Objects.requireNonNull(localDate, "巡查日期不能为空");
    }

    // 提取公共方法：生成基础编码
    private String generateBaseCode(String facilityCat, String orgEn, LocalDate localDate) {
        return StrUtil.format(DINSP_CODE_TEMPLATE,
                facilityCat,
                orgEn.toUpperCase(),
                localDate.format(DATE_FORMATTER)
        );
    }

    // 优化数据库查询（带日期过滤）
    private Integer queryMaxSerialFromDB(String facilityCat, String orgEn, LocalDate localDate) {
        QueryWrapper<NmDinsp> wrapper = new QueryWrapper<>();
        wrapper.select("COALESCE(MAX(TO_NUMBER(SUBSTR(DINSP_CODE, -4))), 0) AS max_serial")
                .likeRight("DINSP_CODE", generateBaseCode(facilityCat, orgEn, localDate))
                .eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString());

        return Optional.ofNullable(getObj(wrapper, obj -> Integer.parseInt(obj.toString())))
                .orElse(0);
    }


    @Override
    public NmDinsp initNmDinsp(String facilityCat, String orgIdString,String code) {
        NmDinsp nmDinsp = baseMapper.lastNmDinsp(facilityCat, orgIdString);
        if (nmDinsp == null) {
            nmDinsp = new NmDinsp();
            nmDinsp.setWeather("01");//表示晴天
        } else {
            nmDinsp.setDinspId(null);
            nmDinsp.setDssNum(0);
            nmDinsp.setProcessinstid(null);
        }
        if (StrUtil.isBlank(nmDinsp.getSearchDept())) {
            nmDinsp.setSearchDept(userService.getDeptName(CustomRequestContextHolder.getUserId()));
        }
        if (StrUtil.isBlank(nmDinsp.getMntOrgNm())) {
            nmDinsp.setMntOrgNm(CustomRequestContextHolder.getOrgName());
        }
        String orgEn = CustomRequestContextHolder.get("ORG_EN") == null ? "" : CustomRequestContextHolder.get("ORG_EN").toString();
        if(StringUtils.isNotBlank(code)){
            orgEn = code;
        }
        nmDinsp.setDinspCode(getNextCode(facilityCat, orgEn, LocalDate.now()));
        nmDinsp.setInspPerson(CustomRequestContextHolder.getUserName());
        nmDinsp.setFacilityCat(facilityCat);
        nmDinsp.setMntOrgId(orgIdString);
        //路面需要用到巡查时间
        nmDinsp.setInspTime(INSP_TIME);
        nmDinsp.setInspDate(new Date());
        // 默认日间巡查
        nmDinsp.setInspFrequency(1);
        nmDinsp.setCreateTime(null).setUpdateTime(null).setCreateUserId(null).setUpdateUserId(null).setCommonDinspId(null);
        return nmDinsp;
    }


    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void saveOrUpdateNmDinsp(NmDinsp nmDinsp) {
        IStruct bean = H_StructHelper.getStructBean(nmDinsp.getFacilityCat());
        bean.validateNmDinsp(nmDinsp);
        boolean isAdd = StrUtil.isBlank(nmDinsp.getDinspId()) ? true : false;

        // 一个路段管理多个路线，当界面新建单为时S51，手动改为G15时，路面名称没有变化，所以每次修改时，需要再次更新路线名称
        String lineName = lineService.getLineName(nmDinsp.getLineCode());
        if (StrUtil.isNotBlank(lineName)) {
            nmDinsp.setLineName(lineName);
        }
        if (isAdd) {
            Integer mntPerson = userService.isMntPerson(CustomRequestContextHolder.getUserCode());
            nmDinsp.setXcType(mntPerson);
            nmDinsp.setDinspId(H_KeyWorker.nextIdToString());
            if (StrUtil.isBlank(nmDinsp.getDinspCode())) {
                String orgEn = CustomRequestContextHolder.get("ORG_EN") == null ? "" : CustomRequestContextHolder.get("ORG_EN").toString();
                nmDinsp.setDinspCode(getNextCode(nmDinsp.getFacilityCat(), orgEn, LocalDate.now()));
            }
            //验证单号  todo 验证单号编码与巡查日期是否匹配
            //验证单号，如果单号中的日期与巡查日期不一致，重新赋值
            if (!H_InspCodeHelper.checkCode(nmDinsp.getDinspCode(),nmDinsp.getInspDate())){
                String orgEn = CustomRequestContextHolder.get("ORG_EN")==null?"":CustomRequestContextHolder.get("ORG_EN").toString();
                String nextCode = getNextCode(nmDinsp.getFacilityCat(), orgEn, DateTimeUtil.toLocalDate(nmDinsp.getInspDate()));
                nmDinsp.setDinspCode(nextCode);
            }
            if (nmDinsp.getProcessinstid() == null || nmDinsp.getProcessinstid() == 0) {
                long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "巡查单", "");
                log.info("日常巡查实例ID：" + processInstId);
                nmDinsp.setProcessinstid(processInstId);
                nmDinsp.setStatus(0);
            }
            // 生成检查结论
            bean.refreshNmDinspResult(nmDinsp);
        }
        if (StrUtil.isBlank(nmDinsp.getCommonDinspId())) {
            String commonDinspId = nmDinspCommonService.bindDinspCommon(nmDinsp);
            nmDinsp.setCommonDinspId(commonDinspId);
        }
        saveOrUpdate(nmDinsp);
        //设置检查工照
        dssImageService.saveDssImageForPC(nmDinsp.getDinspId(), nmDinsp.getFileIds(), 7);
        updateDssNum(nmDinsp.getDinspId());
    }


    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void delBatch(List<String> inspIds) {
        //删除检查结论
        resultService.delBatchByDinspIds(inspIds);
        //删除病害信息
        nmDinspRecordService.deleteRecordByDinspIds(inspIds);

        // 首页待办中不显示新版日常巡查待办任务，所以流程不进行物理删除（加快响应速度） 20250516
        //删除流程信息
        /*LambdaQueryWrapper<NmDinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NmDinsp::getDinspId,inspIds).select(NmDinsp::getProcessinstid);
        List<NmDinsp> nmDinspList = list(queryWrapper);

        // 批量处理只有一个流程实例，删除时，需特殊处理
        Map<Long, Long> processMap = nmDinspList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getProcessinstid())).collect(Collectors.groupingBy(NmDinsp::getProcessinstid, Collectors.counting()));
        Map<Long, Long> dbInstMap = countProcessMap(processMap.keySet());

        for (Long instId : processMap.keySet()) {
            Long vCount = processMap.get(instId);
            // 如果前端删除的单量 >= 数据库中的单量，则删除流程
            Long dbCount = dbInstMap.getOrDefault(instId, 0L);
            if (vCount >= dbCount){
                H_WorkFlowHelper.deleteProcessInstance(instId);
            }
        }*/
        removeByIds(inspIds);
    }

    private Map<Long, Long> countProcessMap(Set<Long> processIds) {
        QueryWrapper<NmDinsp> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("count(*) as process_Count,processinstid").in("processinstid", processIds).groupBy("processinstid");
        List<NmDinsp> nmDinspList = list(queryWrapper);
        Map<Long, Long> processMap = nmDinspList.stream().filter(Objects::nonNull).collect(Collectors.toMap(NmDinsp::getProcessinstid, NmDinsp::getProcessCount, (exists, replace) -> exists));
        return processMap;
    }

    @Override
    public DownloadWordDto exportWord(String dinspIds, String facilityCat) {
        IStruct bean = H_StructHelper.getStructBean(facilityCat);
        //word导出公用地址
        String parentPath = H_WordHelper.getParentPath(facilityCat, "NmDinsp");
        List<NmDinsp> nmDinspList = Lists.newArrayList();
        ListUtil.partition(StrUtil.split(dinspIds, ","), 500).forEach(items -> {
            nmDinspList.addAll(lambdaQuery().in(NmDinsp::getDinspId, items).list());
        });

        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String signType = request.getParameter("signType");
        // 回显天气、路面类型
        //回显负责人
        Set<Long> processInstIdSet = nmDinspList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getProcessinstid())).map(NmDinsp::getProcessinstid).collect(Collectors.toSet());
        Map<Long, WorkPartinameDto> processMap = wfworkitemService.resolveApproverMap(processInstIdSet, "manualActivity3");

        nmDinspList.forEach(item -> {
            String weatherName = dicService.getDicName("WEATHER", item.getWeather());
            item.setWeatherName(weatherName);
            WorkPartinameDto workDto = processMap.getOrDefault(item.getProcessinstid(), new WorkPartinameDto());
            String learder = workDto.getPartiname();
            if ("1".equals(signType)) {
                learder = "   fz1      fz2      fz3";
                item.setInspPerson("    jl");
            }
            item.setWeatherName(weatherName).setLeader(learder).setLeaderTitle(workDto.getLeaderTitle());
            if ((StrUtil.isBlank(item.getLineName()) || item.getLineName().trim().equals("null")) && StrUtil.isNotBlank(item.getLineCode())) {
                item.setLineName(lineService.getLineName(item.getLineCode()));
            }
        });
        List<String> wordPath = bean.processAndExportNmDinspWord(nmDinspList, parentPath);
        return new DownloadWordDto().setParentPath(parentPath).setWordPaths(wordPath);
    }


    @Override
    public List<String> getDssInfoImageExport(String dinspIds, String facilityCat, String path) {
        List<String> strings = Arrays.asList(dinspIds.split(","));
        List<RoadInspectionRecord> records = Lists.newArrayList();
        ListUtil.partition(strings,500).forEach(items->{
            records.addAll(this.baseMapper.getDssInfoImageExport(items, facilityCat));
        });

        List<String> pahtArr = new ArrayList<>();

        Map<String, List<RoadInspectionRecord>> collect
                = records.stream().collect(Collectors.groupingBy(RoadInspectionRecord::getDinspCode));
        collect.entrySet().stream().forEach(v ->
        {
            String key = v.getKey();
            List<RoadInspectionRecord> value = v.getValue();
            MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
            // 创建文档
            XWPFDocument doc = new XWPFDocument();
            String dinspCode = key;
            // 添加标题
            if (!value.isEmpty()) {
                XWPFParagraph title = doc.createParagraph();
                title.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun run = title.createRun();
                run.setBold(true);
                run.setFontSize(16);
                run.setText(dinspCode);
            }

            // 分组每两条数据
            for (int i = 0; i < value.size(); i += 2) {
                RoadInspectionRecord left = value.get(i);
                RoadInspectionRecord right = (i + 1 < value.size()) ? value.get(i + 1) : null;
                //查询结构物的中心桩号
                if (!"LM".equals(facilityCat) && !"JA".equals(facilityCat))
                {
                    //查询结构物的中心桩号
                    IStruct bean = H_StructHelper.getStructBean(facilityCat);
                    BaseStructDto structInfo = bean.getStructInfo(facilityCat, left.getStructId());
                    left.setStructStake(structInfo.getStructName());

                    if (right != null && !StringUtils.isBlank(right.getStructId()))
                    {
                        IStruct beanRight = H_StructHelper.getStructBean(facilityCat);
                        BaseStructDto structInfoRight = beanRight.getStructInfo(facilityCat, right.getStructId());
                        right.setStructStake(structInfoRight.getStructName());
                    }
                }

                // 第一行：图片
                XWPFTable table = doc.createTable(2, 2); // 两行两列：第一行图片，第二行文字
                table.setWidth("100%");
                table.getCTTbl().getTblPr().addNewTblLayout()
                        .setType(STTblLayoutType.FIXED);
                CTTbl ctTbl = table.getCTTbl();
                CTTblGrid tblGrid = ctTbl.addNewTblGrid();
                tblGrid.addNewGridCol().setW(BigInteger.valueOf(4500)); // 左列宽度
                tblGrid.addNewGridCol().setW(BigInteger.valueOf(4500)); // 右列宽度
                // 图片行
                insertImageToCell(table.getRow(0).getCell(0), left, minioProp);
                if (right != null) insertImageToCell(table.getRow(0).getCell(1), right, minioProp);

                // 文字信息行
                setTextToCell(table.getRow(1).getCell(0),
                        "线路编码：" + safe(left.getLineCode()),
                        "桩号：" + safe(StringUtils.isBlank(left.getStructStake()) ? left.getStake() : left.getStructStake()),
                        "日期：" + formatDate(left.getInspDate()),
                        "病害：" + safe(left.getPavementType()),
                        "检查人：" + safe(left.getInspPerson()));
                if (right != null) setTextToCell(table.getRow(1).getCell(1),
                        "线路编码：" + safe(right.getLineCode()),
                        "桩号：" + safe(StringUtils.isBlank(right.getStructStake()) ? right.getStake() : right.getStructStake()),
                        "日期：" + formatDate(right.getInspDate()),
                        "病害：" + safe(right.getPavementType()),
                        "检查人：" + safe(right.getInspPerson()));

                // 空行分隔
                doc.createParagraph();
            }

            // 写入文件
            String substring = path.substring(0, path.lastIndexOf("\\"))+"\\"+dinspCode + "2.docx";;
            FileOutputStream out = null;
            try {
                out = new FileOutputStream(substring);
                doc.write(out);
                out.close();
                doc.close();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                pahtArr.add(substring);
            }
        });


        return pahtArr;
    }

    @Override
    public IPage<RouteInspection> DailyInspectionLedger(IPage page,
                                                        String dinspCode,
                                                        String facilityCats,
                                                        String lineCode,
                                                        String startStake,
                                                        String endStake,
                                                        String startDate,
                                                        String endDate,
                                                        Map reqParam){
        //获取当前用户的机构ID
        MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
        //获取桶名称
        String bucketName = minioProp.getBucketName();

        String orgIdString = CustomRequestContextHolder.getOrgIdString();

        Double start = null;
        if (!StringUtils.isBlank(startStake))
        {
            start = Double.valueOf(startStake);
        }

        Double end = null;
        if (!StringUtils.isBlank(endStake))
        {
            end = Double.valueOf(endStake);
        }

        String code = null;
        if (!StringUtils.isBlank(dinspCode))
        {
            code = "%"+dinspCode+"%";
        }

        //过滤筛选数据
        String dssTypeNameStr = getStringParam(reqParam, "dssTypeName");
        String facilityCategoryStr = getStringParam(reqParam, "facilityCategory");
        String lineDirectStr = getStringParam(reqParam, "lineDirect");
        String routeNameStr = getStringParam(reqParam, "routeName");
        String mtaskCodeStr = getStringParam(reqParam, "mtaskCode");
        String mtaskAccptCodeStr = getStringParam(reqParam, "mtaskAccptCode");

        IPage<RouteInspection> routeInspections = this.baseMapper.DailyInspectionLedger(page, orgIdString, code, facilityCats,
                lineCode, start, end, startDate, endDate, dssTypeNameStr, facilityCategoryStr, lineDirectStr, routeNameStr);
        List<RouteInspection> records = routeInspections.getRecords();
        List<String> collect = records.stream().map(RouteInspection::getDssId).collect(Collectors.toList());
        if (collect != null && collect.size() > 0)
        {
            if (records != null && records.size() > 0)
            {
                List<MtaskEntity> dssInfoId = CollUtil.split(collect, 1000).stream()
                        .flatMap(batch -> this.baseMapper.findDssInfoId(batch, mtaskCodeStr, mtaskAccptCodeStr).stream()).collect(Collectors.toList());
                Map<String, MtaskEntity> collect1 = dssInfoId.stream().collect(Collectors.toMap(MtaskEntity::getDssId, Function.identity()));
                records.stream().forEach(v ->
                {
                    try {
                        String dssId = v.getDssId();
                        String facilityCat = null;
                        String facilityCategory = v.getFacilityCategory();
                        if (!"路面".equals(facilityCategory) && !"交安".equals(facilityCategory) && !StringUtils.isBlank(facilityCategory))
                        {
                            switch (facilityCategory){
                                case "边坡":
                                    facilityCat = "BP";
                                    break;
                                case "桥梁":
                                    facilityCat = "QL";
                                    break;
                                case "涵洞":
                                    facilityCat = "HD";
                                    break;
                                case "隧道":
                                    facilityCat = "SD";
                                    break;
                            }
                            IStruct bean = H_StructHelper.getStructBean(facilityCat);
                            BaseStructDto structInfo = bean.getStructInfo(facilityCat, v.getStructId());
                            v.setStake(structInfo.getStructName());
                        }
                        if(collect1.containsKey(dssId))
                        {
                            MtaskEntity mtaskEntity = collect1.get(dssId);
                            v.setMtaskCode(mtaskEntity.getMtaskCode());
                            v.setMtaskAccptCode(mtaskEntity.getMtaskAccptCode());
                            v.setRepairDate(mtaskEntity.getRepairDate());
                        }
                        String fileEntityPath = v.getFileEntityId();
                        if (!StringUtils.isBlank(fileEntityPath))
                        {
                            String[] split = fileEntityPath.split(",");
                            v.setPothoNums(split.length);
                        }
                        v.setFileUrl(fileEntityPath);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }
        if (StringUtils.isNotBlank(mtaskCodeStr)) {
            routeInspections.getRecords().removeIf(r ->
                    !StringUtils.equals(r.getMtaskCode(), mtaskCodeStr));
        }

        if (StringUtils.isNotBlank(mtaskAccptCodeStr)) {
            routeInspections.getRecords().removeIf(r ->
                    !StringUtils.equals(r.getMtaskAccptCode(), mtaskAccptCodeStr));
        }
        if (StringUtils.isNotBlank(mtaskCodeStr) || StringUtils.isNotBlank(mtaskAccptCodeStr))
        {
            // 8. 更新分页信息
            routeInspections.setTotal(routeInspections.getRecords().size());
            routeInspections.setPages((int) Math.ceil((double) routeInspections.getTotal() / page.getSize()));
        }
        return routeInspections;
    }

    private String getStringParam(Map<String, Object> reqParam, String key) {
        Object value = reqParam.get(key);
        if (value == null) {
            return null;
        }
        String strValue = value.toString();
        return StrUtil.isBlank(strValue) ? null : strValue;
    }

    @Override
    public void exportDailyInspection(HttpServletResponse response,
                                      IPage page,
                                      String dinspCode,
                                      String facilityCats,
                                      String lineCode,
                                      String startStake,
                                      String endStake,
                                      String startDate,
                                      String endDate,
                                      Map reqParam) {
        //获取当前用户的机构ID
        MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
        //获取桶名称
        String bucketName = minioProp.getBucketName();

        XSSFWorkbook workbook = null;

        Double start = null;
        if (!StringUtils.isBlank(startStake))
        {
            start = Double.valueOf(startStake);
        }

        Double end = null;
        if (!StringUtils.isBlank(endStake))
        {
            end = Double.valueOf(endStake);
        }

        String code = null;
        if (!StringUtils.isBlank(dinspCode))
        {
            code = "%"+dinspCode+"%";
        }

        //过滤筛选数据
        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        String dssTypeNameStr = getStringParam(reqParam, "dssTypeName");
        String facilityCategoryStr = getStringParam(reqParam, "facilityCategory");
        String lineDirectStr = getStringParam(reqParam, "lineDirect");
        String routeNameStr = getStringParam(reqParam, "routeName");
        String mtaskCodeStr = getStringParam(reqParam, "mtaskCode");
        String mtaskAccptCodeStr = getStringParam(reqParam, "mtaskAccptCode");

        IPage<RouteInspection> routeInspections = this.baseMapper.DailyInspectionLedger(page, orgIdString, code, facilityCats,
                lineCode, start, end, startDate, endDate, dssTypeNameStr, facilityCategoryStr, lineDirectStr, routeNameStr);
        String routeName = "";
        List<RouteInspection> records = filterDuplicateRecords(routeInspections.getRecords());

        Optional<RouteInspection> first = records.stream().findFirst();
        if (first.isPresent())
        {
            RouteInspection routeInspection = first.get();
            routeName = routeInspection.getRouteName();
        }

        List<String> collect = records.stream().map(RouteInspection::getDssId).distinct().collect(Collectors.toList());
        if (collect != null && collect.size() > 0)
        {
            List<MtaskEntity> dssInfoId = CollUtil.split(collect, 1000).stream()
                    .flatMap(batch -> this.baseMapper.findDssInfoId(batch, mtaskCodeStr, mtaskAccptCodeStr).stream())
                    .collect(Collectors.toList());
            Map<String, List<MtaskEntity>> collect1 = dssInfoId.stream().collect(Collectors.groupingBy(MtaskEntity::getDssId));
            if (records != null && records.size() > 0)
            {
                Map<String, List<RouteInspection>> collect2 = records.stream().filter(v -> !StringUtils.isBlank(v.getFacilityCategory()))
                        .collect(Collectors.groupingBy(RouteInspection::getFacilityCategory));
                Map<String, Map<String, BaseStructDto>> structListMap = new HashMap<>();
                collect2.entrySet().stream().forEach(v ->
                {
                    String facilityCat = null;
                    String facilityCategory = v.getKey();
                    List<RouteInspection> value = v.getValue();
                    List<String> collect3 = value.stream().map(RouteInspection::getStructId).distinct().collect(Collectors.toList());
                    if (!"路面".equals(facilityCategory) && !"交安".equals(facilityCategory)) {
                        switch (facilityCategory) {
                            case "边坡":
                                facilityCat = "BP";
                                break;
                            case "桥梁":
                                facilityCat = "QL";
                                break;
                            case "涵洞":
                                facilityCat = "HD";
                                break;
                            case "隧道":
                                facilityCat = "SD";
                                break;
                        }
                        IStruct bean = H_StructHelper.getStructBean(facilityCat);
                        String finalFacilityCat = facilityCat;
                        List<BaseStructDto> structInfos = CollUtil.split(collect3, 1000).stream()
                                .flatMap(batch -> bean.getStructInfos(finalFacilityCat, batch).stream())
                                .collect(Collectors.toList());
                        Map<String, BaseStructDto> collect4 = structInfos.stream().collect(Collectors.toMap(BaseStructDto::getStructId, Function.identity()));
                        structListMap.put(facilityCategory, collect4);
                    }
                });
                records.stream().forEach(v ->
                {
                    //InputStream stream = null;
                    try {
                        String dssId = v.getDssId();
                        String facilityCategory = v.getFacilityCategory();
                        Map<String, BaseStructDto> stringListMap = structListMap.get(facilityCategory);
                        String structId = v.getStructId();
                        if (!StringUtils.isBlank(structId)
                                && stringListMap != null
                                && stringListMap.size() > 0
                                && stringListMap.containsKey(structId))
                        {
                            BaseStructDto baseStructDtos = stringListMap.get(structId);
                            v.setStake(baseStructDtos.getStructName());
                        }
                        if(collect1.containsKey(dssId))
                        {
                            List<MtaskEntity> mtaskEntities = collect1.get(dssId);
                            Optional<MtaskEntity> first1 = mtaskEntities.stream().filter(codes -> !StringUtils.isBlank(v.getMtaskAccptCode())).findFirst();
                            MtaskEntity mtaskEntity = null;
                            if (first1.isPresent())
                            {
                                mtaskEntity = first1.get();
                            }else if (mtaskEntities != null && mtaskEntities.size() > 0)
                            {
                                mtaskEntity = mtaskEntities.stream().findFirst().get();
                            }
                            v.setMtaskCode(mtaskEntity.getMtaskCode());
                            v.setMtaskAccptCode(mtaskEntity.getMtaskAccptCode());
                            v.setRepairDate(mtaskEntity.getRepairDate());
                        }
               /*         String fileEntityPath = v.getFileEntityPath();
                        String objectName = fileEntityPath.replaceAll(bucketName, "");
                        stream = new FileInputStream("C:\\Users\\<USER>\\Downloads\\87ac5261-d18b-4007-9573-1ed1e1993b2c.png");*/
                        //v.setProductImage(toByteArray(stream));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                if (StringUtils.isNotBlank(mtaskCodeStr)) {
                    records.removeIf(r ->
                            !StringUtils.equals(r.getMtaskCode(), mtaskCodeStr));
                }

                if (StringUtils.isNotBlank(mtaskAccptCodeStr)) {
                    records.removeIf(r ->
                            !StringUtils.equals(r.getMtaskAccptCode(), mtaskAccptCodeStr));
                }
            }
        }
        try {
            String fileName = routeName + "高速巡查检查病害处治台账.xlsx";
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            // 7. 创建临时文件
            try {
                response.setHeader("Content-Disposition",
                        "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            // 8. 初始化工作簿（使用磁盘缓存）
            workbook = new XSSFWorkbook(); // 保留100行在内存中

            // 9. 创建工作表
            XSSFSheet sheet = workbook.createSheet("巡查记录");
            CreationHelper helper = workbook.getCreationHelper();
            // 设置列宽（如第6列用于图片）
            sheet.setColumnWidth(5, 20 * 256); // 第6列宽度

            // 1. 添加标题行（第一行），合并单元格并居中
            XSSFRow titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30); // 设置行高

            // 合并单元格 A1:F1（0到5列）
            CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 14);
            sheet.addMergedRegion(mergedRegion);

            XSSFCell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(routeName + "高速巡查检查病害处治台账");
            CellStyle titleStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setFontName("仿宋");                      // 设置字体为仿宋
            font.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
            font.setBold(true);                           // 加粗
            titleStyle.setFont(font);

            // 设置水平和垂直居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            titleCell.setCellStyle(titleStyle);

            // 设置列宽（比如第6列用于图片）
            sheet.setColumnWidth(12, 20 * 256); // 第6列宽度（图片列）


            sheet.setColumnWidth(5, 20 * 256); // 第6列宽度（图片列）


            // 11.创建表头
            // 11. 创建表头
            XSSFRow header = sheet.createRow(1);
            String[] headers = {
                    "序号", "路段", "路线", "路线方向", "设施分类", "车道（结构物）桩号",
                    "病害类型", "位置及描述", "巡查单号", "发现日期", "任务单号",
                    "验收单单号", "病害照片", "维修日期", "备注"
            };
            sheet.autoSizeColumn(0);
            sheet.autoSizeColumn(2);
            sheet.autoSizeColumn(3);
            sheet.autoSizeColumn(4);
            for (int i = 0; i < headers.length; i++) {
                header.createCell(i).setCellValue(headers[i]);
                if (i != 12 && i != 0 && i != 2 && i != 3 && i != 4 && i != 6 && i != 5) { // 跳过图片列（索引12）
                    sheet.setColumnWidth(i, 20 * 256); // 第6列宽度（图片列）
                }
                if (i != 5) { // 跳过图片列（索引12）
                    sheet.setColumnWidth(i, 25 * 256); // 第6列宽度（图片列）
                }
            }

            // 12. 设置列宽（特别是图片列）
            sheet.setColumnWidth(12, 20 * 256); // 病害照片列

            // 13. 处理数据行
            int rowIndex = 2;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            for (RouteInspection record : records) {
                XSSFRow row = sheet.createRow(rowIndex);

                // 填充基本数据
                row.createCell(0).setCellValue(rowIndex - 1); // 序号
                safeSetCellValue(row, 1, record.getRouteName());
                safeSetCellValue(row, 2, record.getLineCode());
                safeSetCellValue(row, 3, record.getLineDirect());
                safeSetCellValue(row, 4, record.getFacilityCategory());
                safeSetCellValue(row, 5, record.getStake());
                safeSetCellValue(row, 6, record.getDssTypeName());
                safeSetCellValue(row, 7, record.getDssDetail());
                safeSetCellValue(row, 8, record.getDinspCode());
                safeSetCellValue(row, 9, safeFormatDate(sdf, record.getInspDate()));
                safeSetCellValue(row, 10, record.getMtaskCode());
                safeSetCellValue(row, 11, record.getMtaskAccptCode());
                safeSetCellValue(row, 13, safeFormatDate(sdf, record.getRepairDate()));
                row.createCell(14).setCellValue(""); // 备注

                // 14. 处理病害照片
                if (record.getFileEntityPath() != null) {
                    // 从Minio获取图片
                    String objectName = record.getFileEntityPath().replace(bucketName + "/", "");
                    try(InputStream imageStream = minioClient.getObject(minioProp.getBucketName(), objectName)) {

                        // 添加图片到工作簿
                        int pictureIdx;
                        if (imageCache.containsKey(objectName)) {
                            pictureIdx = imageCache.get(objectName);
                        } else {
                            byte[] imageData = IOUtils.toByteArray(imageStream);
                            byte[] standardizedImageData = standardizeImageFormat(imageData);
                            int pictureTypePng = Workbook.PICTURE_TYPE_PNG;
                            if (objectName.toUpperCase().contains("JPG"))
                            {
                                pictureTypePng = Workbook.PICTURE_TYPE_JPEG;
                            }
                            pictureIdx = workbook.addPicture(standardizedImageData, pictureTypePng);
                            imageCache.put(objectName, pictureIdx);
                        }

                        // 创建锚点并插入图片
                        Drawing<?> drawing = sheet.createDrawingPatriarch();
                        ClientAnchor anchor = helper.createClientAnchor();
                        anchor.setCol1(12); // 病害照片列
                        anchor.setRow1(rowIndex);
                        anchor.setCol2(12); // 跨一列
                        anchor.setRow2(rowIndex); // 跨5行高度

                        drawing.createPicture(anchor, pictureIdx);
                        anchor.setDx2(Units.toEMU((int)(110))); // 宽度控制
                        anchor.setDy2(Units.toEMU((int)(110))); // 高度控制（EMU单位）
                        sheet.setColumnWidth(12, 15 * 256); // 约110像素宽

                        // 调整行高以适应图片
                        row.setHeightInPoints(80);

                    } catch (Exception e) {
                        row.createCell(12).setCellValue("无图片");
                    }

                    // 创建带边框的通用样式（放在创建标题样式之后）
                    CellStyle borderStyle = workbook.createCellStyle();
                    borderStyle.setBorderTop(BorderStyle.THIN);
                    borderStyle.setBorderBottom(BorderStyle.THIN);
                    borderStyle.setBorderLeft(BorderStyle.THIN);
                    borderStyle.setBorderRight(BorderStyle.THIN);
                    borderStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 创建表头边框样式（加粗字体）
                    CellStyle headerBorderStyle = workbook.createCellStyle();
                    Font headerFont = workbook.createFont();
                    headerFont.setFontName("仿宋");
                    headerFont.setBold(true);
                    headerBorderStyle.setFont(headerFont);
                    headerBorderStyle.setBorderTop(BorderStyle.THIN);
                    headerBorderStyle.setBorderBottom(BorderStyle.MEDIUM); // 底部加粗
                    headerBorderStyle.setBorderLeft(BorderStyle.THIN);
                    headerBorderStyle.setBorderRight(BorderStyle.THIN);
                    headerBorderStyle.setAlignment(HorizontalAlignment.CENTER);

                    // 应用标题行边框
                    titleStyle.setBorderTop(BorderStyle.MEDIUM);
                    titleStyle.setBorderBottom(BorderStyle.MEDIUM);
                    titleStyle.setBorderLeft(BorderStyle.MEDIUM);
                    titleStyle.setBorderRight(BorderStyle.MEDIUM);

                    // 创建表头样式（添加居中对齐）
                    CellStyle headerBorderStyle2 = workbook.createCellStyle();
                    Font headerFont2 = workbook.createFont();
                    headerFont2.setFontName("仿宋");
                    headerFont2.setBold(true);
                    headerBorderStyle2.setFont(headerFont2);
                    headerBorderStyle2.setBorderTop(BorderStyle.THIN);
                    headerBorderStyle2.setBorderBottom(BorderStyle.MEDIUM);
                    headerBorderStyle2.setBorderLeft(BorderStyle.THIN);
                    headerBorderStyle2.setBorderRight(BorderStyle.THIN);

                    // 设置表头居中对齐（新增）
                    headerBorderStyle2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                    headerBorderStyle2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 创建数据行样式（添加居中对齐）
                    CellStyle borderStyle1 = workbook.createCellStyle();
                    borderStyle1.setBorderTop(BorderStyle.THIN);
                    borderStyle1.setBorderBottom(BorderStyle.THIN);
                    borderStyle1.setBorderLeft(BorderStyle.THIN);
                    borderStyle1.setBorderRight(BorderStyle.THIN);

                    // 设置数据行居中对齐（新增）
                    borderStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                    borderStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 应用表头边框
                    for (int i = 0; i < headers.length; i++) {
                        Cell cell = header.getCell(i);
                        if (cell == null) cell = header.createCell(i);
                        cell.setCellStyle(headerBorderStyle);
                    }

                    // 应用数据行边框
                    for (int i = 2; i <= rowIndex; i++) { // 从第3行开始（索引2）
                        Row rows = sheet.getRow(i);
                        if (rows == null) continue;

                        for (int j = 0; j < headers.length; j++) {
                            Cell cell = rows.getCell(j);
                            if (cell == null) {
                                cell = rows.createCell(j);
                                cell.setCellValue(""); // 防止空单元格
                            }
                            cell.setCellStyle(borderStyle);
                        }
                    }
                }
                rowIndex++;
            }

            try {
                workbook.write(response.getOutputStream());
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    public List<RouteInspection> filterDuplicateRecords(List<RouteInspection> records) {
        // 先按 dssId 分组
        Map<String, List<RouteInspection>> grouped = records.stream()
                .collect(Collectors.groupingBy(RouteInspection::getDssId));

        // 过滤后新列表
        List<RouteInspection> filteredList = new ArrayList<>();

        for (Map.Entry<String, List<RouteInspection>> entry : grouped.entrySet()) {
            List<RouteInspection> groupList = entry.getValue();

            // 先找有没有fileEntityPath不为空的记录
            Optional<RouteInspection> withImage = groupList.stream()
                    .filter(r -> StringUtils.isNotBlank(r.getFileEntityPath()))
                    .findFirst();

            if (withImage.isPresent()) {
                filteredList.add(withImage.get());
            } else {
                // 没有图片则取第一条
                filteredList.add(groupList.get(0));
            }
        }

        return filteredList;
    }


    private byte[] standardizeImageFormat(byte[] imageData) throws IOException {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(imageData)) {
            BufferedImage bufferedImage = ImageIO.read(bais);
            if (bufferedImage == null) {
                throw new IOException("图片格式不支持，无法读取图片数据");
            }

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "png", baos);
            return baos.toByteArray();
        }catch (Exception e)
        {
            e.printStackTrace();
        }
        return imageData;
    }


    // 安全设置单元格值
    private void safeSetCellValue(XSSFRow row, int colIndex, Object value) {
        if (value == null) {
            row.createCell(colIndex).setCellValue("");
        } else {
            row.createCell(colIndex).setCellValue(String.valueOf(value));
        }
    }

    private void safeSetCellValue(SXSSFRow row, int colIndex, Object value) {
        if (value == null) {
            row.createCell(colIndex).setCellValue("");
        } else {
            row.createCell(colIndex).setCellValue(String.valueOf(value));
        }
    }

    // 安全格式化时间的方法
    private String safeFormatDate(SimpleDateFormat sdf, Date date) {
        return date == null ? "" : sdf.format(date);
    }

    // 辅助方法：图片压缩
    private byte[] compressImage(byte[] original, float quality) {
        try {
            ByteArrayInputStream bais = new ByteArrayInputStream(original);
            BufferedImage image = ImageIO.read(bais);

            // 创建输出流
            ByteArrayOutputStream baos = new ByteArrayOutputStream();

            // 获取PNG编码器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("png");
            if (!writers.hasNext()) return original;

            ImageWriter writer = writers.next();
            try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
                writer.setOutput(ios);

                // 设置压缩参数
                ImageWriteParam param = writer.getDefaultWriteParam();
                if (param.canWriteCompressed()) {
                    param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                    param.setCompressionQuality(quality);
                }

                // 写入压缩后的图片
                writer.write(null, new IIOImage(image, null, null), param);
            }
            writer.dispose();

            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return original;
        }
    }

    // 插入图片到单元格
    private void insertImageToCell(XWPFTableCell cell, RoadInspectionRecord record, MinioProp minioProp) {
        cell.getCTTc().addNewTcPr().addNewTcW().setW(BigInteger.valueOf(4500));
        InputStream inputStream = null;
        try {
            String fileEntityPath = record.getFileEntityPath();
            File imgFile = new File(filepath + record.getFileEntityPath());
            String bucketName = minioProp.getBucketName();
            String objectName = fileEntityPath.replaceAll(bucketName, "");
            inputStream = minioClient.getObject(minioProp.getBucketName(), objectName);
            byte[] bytes = toByteArray(inputStream);
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
            int width = image.getWidth();
            inputStream = new ByteArrayInputStream(bytes);
            XWPFParagraph para = cell.addParagraph();
            XWPFRun run = para.createRun();
            int pictureTypePng = Workbook.PICTURE_TYPE_PNG;
            if (fileEntityPath.toUpperCase().contains("JPG"))
            {
                pictureTypePng = Workbook.PICTURE_TYPE_JPEG;
            }
            run.addPicture(inputStream, pictureTypePng, imgFile.getName(),
                    Units.toEMU((int)(210)), Units.toEMU(230));  // 设置图片大小
            cell.removeParagraph(0); // 删除默认段落
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            IoUtil.close(inputStream);
        }
    }

    public static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[4096];
        int nRead;
        while ((nRead = input.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        return buffer.toByteArray();
    }
    // 设置文字内容到单元格
    private void setTextToCell(XWPFTableCell cell, String... lines) {
        cell.removeParagraph(0); // 移除默认段落

        XWPFParagraph para = cell.addParagraph();
        para.setAlignment(ParagraphAlignment.LEFT); // 设置左对齐
        para.setSpacingBetween(1.0);

        XWPFRun run = para.createRun();
        run.setFontSize(10);

        for (int i = 0; i < lines.length; i++) {
            run.setText(lines[i]);
            if (i != lines.length - 1) {
                run.addBreak(); // 显式换行
            }
        }
    }

    private String safe(Object val) {
        return val == null ? "" : val.toString();
    }
    private String formatDate(Date date) {
        if (date == null) return "";
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }



    @SneakyThrows
    @Override
    public void createProcess(String dinspId) {
        NmDinsp nmDinsp = getById(dinspId);
        Integer count = lambdaQuery().eq(NmDinsp::getProcessinstid, nmDinsp.getProcessinstid()).count();
        // 说明该流程多个公用，并且存在病害，重新新建流程赋值
        if (count > 1 && nmDinsp.getDssNum() > 0){
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "巡查单", "");
            nmDinsp.setProcessinstid(processInstId);
            updateById(nmDinsp);
        }
    }


    @Override
    public void saveBatchNmDinsp(NmDinsp nmDinsp,boolean isMq) {
        // 一个路段管理多个路线，当界面新建单为时S51，手动改为G15时，路面名称没有变化，所以每次修改时，需要再次更新路线名称
        String lineName = lineService.getLineName(nmDinsp.getLineCode());
        if (StrUtil.isNotBlank(lineName)){
            nmDinsp.setLineName(lineName);
        }
        // 1. 增强空值防护与字符串分割
        List<String> structIds = Optional.ofNullable(nmDinsp.getStructId())
                .filter(StrUtil::isNotBlank)
                .map(str -> StrUtil.split(str, ","))
                .orElseGet(Collections::emptyList);

        // 2. 缓存Bean集合避免重复查询 + 防御性编程
        IBaseStructFace structFace = Optional.ofNullable(nmDinsp.getFacilityCat())
                .map(cat -> CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                        .filter(bean -> Objects.equals(bean.getFacilityCat(), cat))
                        .findFirst()
                        .orElseThrow(() -> new BaseException("设施类型[" + cat + "]无对应实现类"))
                )
                .orElseThrow(() -> new BaseException("设施类型字段为空"));

        // 3. 集合空值兜底与数据校验
        List<BaseStructDto> baseStructDtos = structFace.listByStructIds(Lists.newArrayList(structIds));
        if (CollectionUtil.isEmpty(baseStructDtos)) {
            log.warn("结构体数据为空，structIds: {}", structIds);
            throw new BaseException("找不到对应的结构物");
        }
        createEmptyNmDinspByStruct(baseStructDtos,nmDinsp,isMq);
    }

    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void createEmptyNmDinspByStruct(List<BaseStructDto> baseStructDtos, NmDinsp nmDinsp,boolean isMq) {

        if (CollectionUtil.isEmpty(baseStructDtos)){
            return;
        }
        // 4. 并行流加速映射处理（适用于大数据量）
        Set<String> routeCodeSet = baseStructDtos.parallelStream()
                .map(BaseStructDto::getRouteCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toCollection(LinkedHashSet::new));  // 保持顺序

        // 5. 批量查询优化（添加缓存机制）
        Map<String, BaseRouteLogic> routeMap = logicService.resolveRouteNamesByCodes(routeCodeSet);

        //5.1 批量查询路线编码，路线名称
        Set<String> lineCodeSet = routeMap.values().stream().map(BaseRouteLogic::getLineCode).collect(Collectors.toSet());
        Map<String, String> lineMap = lineService.resolveLineNamesByCodes(lineCodeSet);

        Integer xcType = userService.isMntPerson(CustomRequestContextHolder.getUserCode());

        // 6. 预初始化集合容量 + 方法抽取提升可读性
        List<NmDinsp> nmDinspList = new ArrayList<>(baseStructDtos.size());
        baseStructDtos.forEach(item -> nmDinspList.add(buildNmDinsp(nmDinsp, item, routeMap,lineMap,xcType)));

        String code = nmDinsp.getDinspCode();
        String orgEn = "";
        if (StrUtil.isNotBlank(code)){
            String[] parts = code.split("-", -1);
            if (parts.length >= 3) {
                orgEn = parts[2];
            }
        } else {
            orgEn = Optional.ofNullable(CustomRequestContextHolder.get("ORG_EN"))
                    .map(Object::toString)
                    .orElse("");
        }
        // 根据主单设置其他的单日期
        LocalDate localDate = nmDinsp.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 7.查询现数据库单号数字，在后面累计
        AtomicInteger nextSerial = new AtomicInteger(getNextSerial(nmDinsp.getFacilityCat(), orgEn,localDate));
        String facilityCat = nmDinsp.getFacilityCat();

        // 7.1 绑定巡查公单
        String commonDinspId = nmDinspCommonService.bindDinspCommon(nmDinsp);

        // 8. 批量新建只创建一个流程实例，后续如果该单录入病害时，会重新生成流程ID赋值给该单
        long processInstId = isMq?-1:H_WorkFlowHelper.createWorkItem(processDefName, "巡查单", "");
        //long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "巡查单", "");
        // 9. 赋值单号、流程实例ID
        for (NmDinsp dinsp : nmDinspList) {
            dinsp.setDinspId(H_KeyWorker.nextIdToString());
            String dinspCode = buildDinspCode(facilityCat, orgEn, nextSerial.incrementAndGet(),localDate);
            dinsp.setDinspCode(dinspCode).setCommonDinspId(commonDinspId);
            if (!isMq){
                // 经常检查需要一个任务一个流程
                dinsp.setProcessinstid(processInstId);
            }
        }
        if (!isMq){
            // 11.批量保存巡查单
            saveBatch(nmDinspList);
            return;
        }

        // 组装mq信息
        /*NmDinspEmptDto dinspEmptDto = new NmDinspEmptDto();
        dinspEmptDto.setCommonDinspId(commonDinspId)
                .setNmDinspList(nmDinspList)
                .setNmDinsp(nmDinsp)
                .setUserCode(CustomRequestContextHolder.getUserCode())
                .setUserName(CustomRequestContextHolder.getUserName())
                .setUserId(CustomRequestContextHolder.getUserId());
        H_RabbitHelper.sendDinspMessage(dinspEmptDto);*/
    }

    @SneakyThrows
    @Override
    public void createEmpTyNmDinspByLMAndJA(NmDinsp commonDinsp,boolean isMq) {
        // 路线编码、公司ID、巡查日期、天气情况、项目公司、巡查单位、巡查人、交安（巡查频率）
        String orgEn = Optional.ofNullable(CustomRequestContextHolder.get("ORG_EN"))
                .map(Object::toString)
                .orElse("");
        // LM在公用单时，已经创建，在此处不需要重新新建
        List<String> facilityCatList = Lists.newArrayList("JA");

        // 根据设施查询路段信息
        IStruct bean = H_StructHelper.getStructBean("LM");
        bean.validateNmDinsp(commonDinsp);

        LocalDate inspDate = commonDinsp.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        List<NmDinsp> nmDinspList = Lists.newArrayList();
        for (String cat : facilityCatList) {
            Integer count = lambdaQuery().eq(NmDinsp::getFacilityCat, cat)
                    .eq(NmDinsp::getMntOrgId, commonDinsp.getMntOrgId())
                    .eq(NmDinsp::getInspDate, commonDinsp.getInspDate())
                    .eq(ObjectUtil.isNotEmpty(commonDinsp.getInspFrequency()),
                            NmDinsp::getInspFrequency, commonDinsp.getInspFrequency()).count();
            // count = 0 时表示未新建单
            if (count == 0){

                NmDinsp newDinsp = BeanUtil.copyProperties(commonDinsp, NmDinsp.class,
                        "dinspId", "createTime", "updateTime", "createUserId", "updateUserId","dssNum").setFacilityCat(cat);
                // 8.查询现数据库单号数字，在后面累计
                Integer nextSerial = getNextSerial(cat, orgEn,inspDate);
                String dinspCode = buildDinspCode(cat, orgEn, ++nextSerial,inspDate);
                // 8.1 绑定巡查公单
                String commonDinspId = nmDinspCommonService.bindDinspCommon(newDinsp);
                newDinsp.setDinspCode(dinspCode).setCommonDinspId(commonDinspId).setDinspId(H_KeyWorker.nextIdToString());

                // 7. 批量新建只创建一个流程实例，后续如果该单录入病害时，会重新生成流程ID赋值给该单
                if (!isMq){
                    long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "巡查单", "");
                    newDinsp.setProcessinstid(processInstId);
                }
                nmDinspList.add(newDinsp);
            }
        }

        // 10.批量生成检查结论
        if (CollectionUtil.isNotEmpty(nmDinspList)){
            saveBatch(nmDinspList);
            // 不采用mq
            /*if (isMq){
                nmDinspList.forEach(nmDinsp -> {
                    nmDinsp.setDinspId(H_KeyWorker.nextIdToString());
                    NmDinspEmptDto dto = new NmDinspEmptDto()
                            .setNmDinspList(Lists.newArrayList(nmDinsp))
                            .setNmDinsp(nmDinsp)
                            .setUserCode(CustomRequestContextHolder.getUserCode())
                            .setUserName(CustomRequestContextHolder.getUserName())
                            .setUserId(CustomRequestContextHolder.getUserId()).setCommonDinspId(nmDinsp.getDinspId());
                    H_RabbitHelper.sendDinspMessage(dto);
                });
            }else {
                saveBatch(nmDinspList);
            }*/
        }
    }

    @Override
    public NmDinsp createDinsp(NmDinspCommon dinspCommon) {
        // 根据主单及结构物ID，查询是否已存在单
        LambdaQueryWrapper<NmDinsp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NmDinsp::getCommonDinspId,dinspCommon.getDinspId())
                .eq(NmDinsp::getFacilityCat,dinspCommon.getFacilityCat())
                // 不同的结构物，建立不同的单
                .eq(StrUtil.isNotBlank(dinspCommon.getStructId()),NmDinsp::getStructId,dinspCommon.getStructId())
                // 查询未办理的单
                .in(NmDinsp::getStatus,0,-1);
        NmDinsp nmDinsp = getOne(wrapper, false);
        if (ObjectUtil.isNotEmpty(nmDinsp)){
            return nmDinsp;
        }
        NmDinsp newDinsp = new NmDinsp();
        // 需要新建巡查单
        BeanUtil.copyProperties(dinspCommon,newDinsp,"dinspId","createTime","updateTime");
        newDinsp.setInspDate(new Date()).setCommonDinspId(dinspCommon.getDinspId());
        saveOrUpdateNmDinsp(newDinsp);
        return newDinsp;
    }

    @Override
    public void bindNoDinspCommonId(NmDinspCommon dinspCommon) {
        if (dinspCommon.getDinspId() == null){
            return;
        }
        // 公用单新建时可能未赋值用户ID
        String createUserId = StrUtil.isNotBlank(dinspCommon.getCreateUserId())?dinspCommon.getCreateUserId():CustomRequestContextHolder.getUserId();

        List<NmDinsp> nmDinspList = lambdaQuery()
                .select(NmDinsp::getDinspId)
                .eq(NmDinsp::getMntOrgId, dinspCommon.getMntOrgId())
                .eq(NmDinsp::getInspDate, dinspCommon.getInspDate())
                .eq(NmDinsp::getLineCode, dinspCommon.getLineCode())
                .eq(NmDinsp::getCreateUserId, createUserId)
                .isNull(NmDinsp::getCommonDinspId).list();
        if (CollectionUtil.isNotEmpty(nmDinspList)){
            List<String> dinspIds = nmDinspList.stream().map(NmDinsp::getDinspId).collect(Collectors.toList());
            ListUtil.partition(dinspIds,500).forEach(rows->{
                LambdaUpdateWrapper<NmDinsp> wrapper = new LambdaUpdateWrapper<>();
                wrapper.in(NmDinsp::getDinspId,rows);
                wrapper.set(NmDinsp::getCommonDinspId,dinspCommon.getDinspId());
                update(wrapper);
            });
        }
    }

    @Override
    public Map<Integer, List<Long>> listProcessTask(String commonDinspId,String facilityCat) {
        return listProcessTask(commonDinspId,facilityCat,null);
    }

    @Override
    public Map<Integer, List<Long>> listProcessTask(String commonDinspId, String facilityCat, String processInstId) {

        Set<Long> processidSet = Sets.newHashSet();
        if (StrUtil.isBlank(processInstId)){
            //待办清单
            String taskSql = H_WorkFlowHelper.getUserTaskSql(Integer.valueOf(0), "nm_dinsp");
            LambdaQueryWrapper<NmDinsp> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StrUtil.isNotBlank(commonDinspId),NmDinsp::getCommonDinspId,commonDinspId)
                    .eq(StrUtil.isNotBlank(facilityCat),NmDinsp::getFacilityCat,facilityCat)
                    .exists(taskSql)
                    .select(NmDinsp::getProcessinstid)
                    .groupBy(NmDinsp::getProcessinstid);
            // 获取流程ID
            processidSet = list(wrapper).stream().filter(e -> ObjectUtil.isNotEmpty(e.getProcessinstid())).map(NmDinsp::getProcessinstid).collect(Collectors.toSet());
        }else {
            Set<String> instIds = StrUtil.split(processInstId, ",").stream().collect(Collectors.toSet());
            processidSet = Convert.toSet(Long.class, instIds);
        }
        /*if (processidSet.size() > 100){
            throw new BaseException("流程办理超过100个，请用批量办理（勾选版）或者");
        }*/
        Map<Integer, List<Long>> processMap = wfworkitemService.selectTaskMapByNmInsp(processidSet,true);
        return processMap;
    }

    @Override
    public void removeByCommonDinspIds(List<String> commonDinspIds) {
        if (CollectionUtil.isNotEmpty(commonDinspIds)){
            LambdaQueryWrapper<NmDinsp> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(NmDinsp::getCommonDinspId,commonDinspIds);
            remove(wrapper);
        }
    }

    // 方法抽取：对象构建逻辑封装
    private NmDinsp buildNmDinsp(NmDinsp source, BaseStructDto item, Map<String, BaseRouteLogic> routeMap,
                                 Map<String, String> lineMap,Integer xcType) {
        // 1. 使用final确保对象不可变
        final NmDinsp bean = BeanUtil.toBean(source, NmDinsp.class);

        // 2. 使用Optional处理空值
        Date defaultDate = Optional.ofNullable(bean.getInspDate())
                .orElseGet(() -> {
                    Date now = new Date();
                    bean.setInspDate(now);  // 设置值
                    return now;             // 返回Date类型
                });

        // 3. 提取路由逻辑到独立代码块
        final BaseRouteLogic routeLogic = Optional.ofNullable(routeMap.get(item.getRouteCode()))
                .orElseGet(BaseRouteLogic::new);
        final String lineCode = routeLogic.getLineCode();

        // 4. 消除魔法值
        final String UNKNOWN_LINE = "未知路线";
        final String lineName = lineMap.getOrDefault(lineCode, UNKNOWN_LINE);

        // 5. 链式调用拆分提高可读性
        return bean
                .setRouteName(routeLogic.getRouteName())
                .setLineCode(lineCode)
                .setLineName(lineName)
                .setStructId(item.getStructId())
                .setDinspId(H_KeyWorker.nextIdToString())
                .setStatus(0)
                .setInspTime(INSP_TIME)
                .setXcType(xcType)
                .setStructStakeNum(item.getRlCntrStake())
                .setStructName(item.getStructName())
                .setMntOrgId(CustomRequestContextHolder.getOrgIdString())
                .setRouteCode(item.getRouteCode());
    }

    @Override
    public Map<String, String> initAction() {
        return null;
    }

    @Override
    public String getProcessDefName() {
        return processDefName;
    }

    @Override
    public void savedoEvent(long processInstId, boolean isEnd, String nextAction, String actId) {
        if (isEnd){
            List<NmDinsp> dmDinsps = getNmDinspByProcessInstId(processInstId);
            List<String> dinspIds = dmDinsps.stream().map(NmDinsp::getDinspId).collect(Collectors.toList());
            List<NmDinspRecord> dmDinspRecords = nmDinspRecordService.selectRecordByDinspId(dinspIds);
            Map<String, List<NmDinspRecord>> finspMap = dmDinspRecords.stream().collect(Collectors.groupingBy(NmDinspRecord::getDinspId));
            for (NmDinsp dmDinsp : dmDinsps) {
                List<NmDinspRecord> nmDinspRecords = finspMap.get(dmDinsp.getDinspId());
                if (CollectionUtil.isNotEmpty(nmDinspRecords)) {
                    saveDssInfo(dmDinsp,dmDinspRecords);
                }
            }
            updateNmDinspStatus(processInstId,3);
            return;
        }
        switch (actId) {
            case "manualActivity":
                updateNmDinspStatus(processInstId, 1);
                break;
            case "审核":
                updateNmDinspStatus(processInstId, 2);
                break;
            case "退回":
                updateNmDinspStatus(processInstId, -1);
                break;
        }
    }
    @Override
    public List<NmDinspSituationShow> querySituationShow(String orgCode, int year) {
        return this.baseMapper.querySituation(orgCode, year);
    }

    @Override
    public List<OrderInfo> queryOrderInfo(long processInstId) {
        List<NmDinsp> list = getNmDinspByProcessInstId(processInstId);
        List<OrderInfo> infos = new ArrayList<>();
        list.stream().forEach(item->{
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderCode(item.getDinspCode());
            orderInfo.setOrderType("XCD_NEW");
            orderInfo.setOrderId(item.getDinspId());
            orderInfo.setCat(item.getFacilityCat());
            orderInfo.setStatus(item.getStatus());
            orderInfo.setOrgCode(item.getMntOrgId());
            orderInfo.setXcType(item.getXcType());
            infos.add(orderInfo);
        });
        return infos;
    }

    @Override
    public List<NmDinspFacilityStat> getNmDinspFacilityStat(String commonDinspId, String status) {
        Set<String> orgIds = H_DataAuthHelper.selectOrgIds();
        String taskSql = H_WorkFlowHelper.getUserTaskSql(Convert.toInt(status, 0), "c");
        LambdaQueryWrapper<NmDinsp> ew = new LambdaQueryWrapper<>();
        ew.eq(NmDinsp::getCommonDinspId, commonDinspId);
        ew.in(NmDinsp::getMntOrgId, orgIds);
        if (StrUtil.isNotBlank(taskSql)){
            ew.exists(taskSql);
        }
        return getBaseMapper().getNmDinspFacilityStat(ew);
    }

    @Override
    public IPage<SlopeInspectionRecord> findBpDailyRecord(IPage page,
                                                          String dinspCode,
                                                          String status,
                                                          String lineCode,
                                                          String hasDssStatus,
                                                          String startDate,
                                                          String endDate){

        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        IPage<SlopeInspectionRecord> routeInspections = this.baseMapper.findBpDinspRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate, endDate);

        return routeInspections;
    }
    @Override
    public void findBpDailyRecord(HttpServletResponse response,
                                  IPage page,
                                  String dinspCode,
                                  String status,
                                  String lineCode,
                                  String hasDssStatus,
                                  String startDate,
                                  String endDate)
    {
        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        String orgName = CustomRequestContextHolder.getOrgName();
        IPage<SlopeInspectionRecord> routeInspections = this.baseMapper.findBpDinspRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate, endDate);

        String routeName = "";
        String oprtName = "";
        List<SlopeInspectionRecord> records = routeInspections.getRecords();
        Optional<SlopeInspectionRecord> first = records.stream().findFirst();
        if (first.isPresent())
        {
            SlopeInspectionRecord routeInspection = first.get();
            routeName = routeInspection.getRouteName();
            oprtName = routeInspection.getOrgFullname();
        }

        //请求头设置
        // 设置导出文件名（含时间戳避免重复）
        String fileName = routeName + "高速巡查检查病害处治台账" + ".xlsx";

        // 设置响应头（适配中文文件名）
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        // 中文文件名需要编码处理
        try {
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("巡查记录");
        CreationHelper helper = workbook.getCreationHelper();
        // 设置列宽（如第6列用于图片）
        sheet.setColumnWidth(5, 20 * 256); // 第6列宽度

        // 1. 添加标题行（第一行），合并单元格并居中
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30); // 设置行高

        // 2.添加第二行显示养护单位
        XSSFRow oprtRow = sheet.createRow(1);
        oprtRow.setHeightInPoints(25); // 设置行高

        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 14);
        sheet.addMergedRegion(mergedRegion);
        //合并单元格
        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedPrjRegion = new CellRangeAddress(1, 1, 0, 5);
        sheet.addMergedRegion(mergedPrjRegion);

        CellRangeAddress mergedOprtRegion = new CellRangeAddress(1, 1, 6, 9);
        sheet.addMergedRegion(mergedOprtRegion);

        CellRangeAddress mergedDateRegion = new CellRangeAddress(1, 1, 10, 12);
        sheet.addMergedRegion(mergedDateRegion);

        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(routeName + "高速公路边坡经常检查台账");
        CellStyle titleStyle = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontName("仿宋");                      // 设置字体为仿宋
        font.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        font.setBold(true);                           // 加粗
        titleStyle.setFont(font);

        // 设置水平和垂直居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        titleCell.setCellStyle(titleStyle);

        //项目公司
        XSSFCell prjCell = oprtRow.createCell(0);
        prjCell.setCellValue("项目公司：" + orgName);
        CellStyle prjStyle = workbook.createCellStyle();
        XSSFFont fontprj = workbook.createFont();
        fontprj.setFontName("仿宋");                      // 设置字体为仿宋
        fontprj.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        fontprj.setBold(true);                           // 加粗
        prjStyle.setFont(fontprj);

        // 设置水平和垂直居中
        prjStyle.setAlignment(HorizontalAlignment.CENTER);
        prjStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        prjCell.setCellStyle(prjStyle);

        //管养公司
        XSSFCell oprtCell = oprtRow.createCell(1);
        oprtCell.setCellValue("养护单位：" + oprtName);
        CellStyle oprtStyle = workbook.createCellStyle();
        XSSFFont fontoprt = workbook.createFont();
        fontoprt.setFontName("仿宋");                      // 设置字体为仿宋
        fontoprt.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        fontoprt.setBold(true);                           // 加粗
        oprtStyle.setFont(fontoprt);

        // 设置水平和垂直居中
        oprtStyle.setAlignment(HorizontalAlignment.CENTER);
        oprtStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        oprtCell.setCellStyle(oprtStyle);

        //检查时间
        XSSFCell DateCell = oprtRow.createCell(2);
        DateCell.setCellValue("检查时间：" + oprtName);
        CellStyle DateStyle = workbook.createCellStyle();
        XSSFFont fontDate = workbook.createFont();
        fontDate.setFontName("仿宋");                      // 设置字体为仿宋
        fontDate.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        fontDate.setBold(true);                           // 加粗
        DateStyle.setFont(fontDate);

        // 设置水平和垂直居中
        DateStyle.setAlignment(HorizontalAlignment.CENTER);
        DateStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        DateCell.setCellStyle(DateStyle);

        // 设置列宽（比如第6列用于图片）
        sheet.setColumnWidth(5, 20 * 256); // 第6列宽度（图片列）

        // 创建表头
        XSSFRow header = sheet.createRow(2);
        String[] headers = {
                "序号", "路段", "所属线路", "边坡名称", "边坡长度（m）", "边坡级数", "病害情况", "技术状况",
                "检查单编码", "检查日期", "巡查频率", "养护建议", "备注"
        };
        for (int i = 0; i < headers.length; i++) {
            header.createCell(i).setCellValue(headers[i]);
        }

        // 写入数据
        int rowIndex = 3;
        int serial = 1;
        for (SlopeInspectionRecord record : records) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            XSSFRow row = sheet.createRow(rowIndex);
            int col = 0;

            safeSetCellValue(row, col++, serial); //序号
            safeSetCellValue(row, col++, record.getRouteName());
            safeSetCellValue(row, col++, record.getLineCode());
            safeSetCellValue(row, col++, record.getSlopeName());
            safeSetCellValue(row, col++, record.getSlopeLength());
            safeSetCellValue(row, col++, record.getSlopeLevel());
            safeSetCellValue(row, col++, record.getDssNums());
            safeSetCellValue(row, col++, record.getSlopeTcGrade());
            safeSetCellValue(row, col++, record.getDinspCode());
            safeSetCellValue(row, col++, safeFormatDate(sdf, record.getInspDate()));
            safeSetCellValue(row, col++, record.getInspFrequency());
            safeSetCellValue(row, col++, record.getMntnAdvice());
            safeSetCellValue(row, col++, ""); //备注

            serial++;
            rowIndex++;
        }
        try {
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void findDinspCheckSum(HttpServletResponse response, String startDate, String endDate) {
        String orgCode = CustomRequestContextHolder.getOrgIdString();
        Integer cur = null;
        if (incur.containsKey(orgCode))
        {
            cur = incur.get(orgCode);
        }else
        {
            cur = 1;
        }
        List<SumInspectionRecord> dinspCheckSum = this.baseMapper.findDinspCheckSum(orgCode
                , startDate + " 00:00:00", endDate + " 23:59:59");

        String orgNameFirst = null;
        if (dinspCheckSum != null && dinspCheckSum.size() > 0)
        {
            SumInspectionRecord sumInspectionRecord = dinspCheckSum.stream().findFirst().get();
            String dinspCode = sumInspectionRecord.getDinspCode();
            String[] split = dinspCode.split("-");
            orgNameFirst = split[2];
        }

        // 按 lineCode分组
        Map<String, List<SumInspectionRecord>> collect1 = dinspCheckSum.stream()
                .collect(Collectors.groupingBy(SumInspectionRecord::getLineCode));

        // 临时文件列表
        List<File> tempExcelFiles = new ArrayList<>();

        AtomicInteger i = new AtomicInteger(cur);
        String finalOrgNameFirst = orgNameFirst;
        collect1.forEach((lineCode, value2) -> {
            Map<String, List<SumInspectionRecord>> collect5 = value2.stream()
                    .sorted(Comparator.comparing(SumInspectionRecord::getInspDate))
                    .collect(Collectors.groupingBy(SumInspectionRecord::getInspDate));
            collect5.entrySet().stream().forEach(vvs ->
            {
                String key = vvs.getKey();
                List<SumInspectionRecord> value = vvs.getValue();
                try {
                    String persion = value.stream().map(SumInspectionRecord::getCheckPerson).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));
                    BigDecimal reduce = value.stream().map(SumInspectionRecord::getInspectLength).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    String collect = value.stream().map(SumInspectionRecord::getSearchDept).distinct().collect(Collectors.joining(","));

                    //天气、路段、时间等
                    String collect2 = value.stream().map(SumInspectionRecord::getWeather).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));
                    String stakeName = value.stream().map(SumInspectionRecord::getStakeName).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));
                    String inspTime = value.stream().map(SumInspectionRecord::getInspTime).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));

                    value = value.stream().filter(v ->
                            Double.valueOf(v.getDssNum()) > 0).collect(Collectors.toList());
                    Map<String, List<SumInspectionRecord>> collect3 = value.stream().collect(Collectors.groupingBy(SumInspectionRecord::getFacilityCategory));
                    collect3.entrySet().stream().forEach(vv ->
                    {
                        String facilityCategory = vv.getKey();
                        List<SumInspectionRecord> value1 = vv.getValue();
                        List<String> StructId = value1.stream().map(SumInspectionRecord::getStructId).collect(Collectors.toList());
                        String facilityCat = "";
                        if (!"路面".equals(facilityCategory) && !"交安".equals(facilityCategory)) {
                            switch (facilityCategory) {
                                case "边坡":
                                    facilityCat = "BP";
                                    break;
                                case "桥梁":
                                    facilityCat = "QL";
                                    break;
                                case "涵洞":
                                    facilityCat = "HD";
                                    break;
                                case "隧道":
                                    facilityCat = "SD";
                                    break;
                            }
                            IStruct bean = H_StructHelper.getStructBean(facilityCat);
                            String finalFacilityCat = facilityCat;
                            List<BaseStructDto> structInfos = CollUtil.split(StructId, 1000).stream()
                                    .flatMap(batch -> bean.getStructInfos(finalFacilityCat, batch).stream())
                                    .collect(Collectors.toList());
                            Map<String, BaseStructDto> collect4 = structInfos.stream().collect(Collectors.toMap(BaseStructDto::getStructId, Function.identity()));
                            value1.stream().forEach(map ->
                            {
                                String structId = map.getStructId();
                                if (collect4 != null && collect4.containsKey(structId))
                                {
                                    BaseStructDto baseStructDto = collect4.get(structId);
                                    map.setStake(baseStructDto.getStructName());
                                }
                            });
                        }
                    });

                    //项目公司
                    String orgName = CustomRequestContextHolder.getOrgName();

                    // 病害
                    String resultLM = getFacilityDssName(value, "路面");
                    String resultLJ = getFacilityDssName(value, "路基");
                    String resultQL = getFacilityDssName(value, "桥梁");
                    String resultHD = getFacilityDssName(value, "涵洞");
                    String resultSD = getFacilityDssName(value, "隧道");
                    String resultSS = getFacilityDssName(value, "沿线设施");
                    String resultLH = getFacilityDssName(value, "沿线绿化");

                    // 处理意见
                    String resultLMdvice = getFacilityMntnAdvice(value, "路面");
                    String resultLJdvice = getFacilityMntnAdvice(value, "路基");
                    String resultQLdvice = getFacilityMntnAdvice(value, "桥梁");
                    String resultHDdvice = getFacilityMntnAdvice(value, "涵洞");
                    String resultSDdvice = getFacilityMntnAdvice(value, "隧道");
                    String resultSSdvice = getFacilityMntnAdvice(value, "沿线设施");
                    String resultLHdvice = getFacilityMntnAdvice(value, "沿线绿化");

                    List<String> knownFacilities = Arrays.asList("路面", "路基", "桥梁", "涵洞", "隧道", "沿线设施", "沿线绿化");

                    String resultQT = value.stream()
                            .filter(vv -> vv.getFacilityCategory() == null || !knownFacilities.contains(vv.getFacilityCategory()))
                            .map(vv -> Optional.ofNullable(vv.getLineDirect()).orElse("") +
                                    Optional.ofNullable(vv.getLane()).orElse("") +
                                    Optional.ofNullable(vv.getStake()).orElse("") +
                                    Optional.ofNullable(vv.getDssTypeName()).orElse(""))
                            .distinct().collect(Collectors.joining(","));
                    if (StringUtils.isBlank(resultQT))
                    {
                        resultQT = "----";
                    }

                    String resultQTdvice = value.stream()
                            .filter(vv -> vv.getFacilityCategory() == null || !knownFacilities.contains(vv.getFacilityCategory()))
                            .map(SumInspectionRecord::getMntnAdvice)
                            .distinct().collect(Collectors.joining(","));
                    if (StringUtils.isBlank(resultQTdvice))
                    {
                        resultQTdvice = "/";
                    }

                    // 获取最早和最晚时间
                    LocalTime earliest = LocalTime.of(8, 0);  // 默认早上8点
                    LocalTime latest = LocalTime.of(22, 0);   // 默认晚上10点

                    if (inspTime != null && !inspTime.trim().isEmpty()) {
                        try {
                            LocalTime parsedEarliest = getEarliestTime(inspTime);
                            LocalTime parsedLatest = getLatestTime(inspTime);
                            if (parsedEarliest != null) {
                                earliest = parsedEarliest;
                            }
                            if (parsedLatest != null) {
                                latest = parsedLatest;
                            }
                        } catch (Exception e) {
                            // 如果解析失败，继续使用默认时间
                            e.printStackTrace();
                        }
                    }
                    // 使用格式化器转换为指定格式的字符串
                    String earliestStr = earliest.format(TIME_FORMATTER);
                    String latestStr = latest.format(TIME_FORMATTER);

                    // 模板路径
                    TemplateExportParams params = new TemplateExportParams("excel/日常巡查单汇总表.xlsx", true);

                    int andIncrement = i.getAndIncrement();
                    String code = "XCD-" + lineCode + "-" + finalOrgNameFirst + "-" + key.replace("-", "") + "-00" + andIncrement;

                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("code", code);
                    dataMap.put("startDate", key);
                    dataMap.put("orgName", orgName);
                    dataMap.put("opertName", collect);
                    dataMap.put("inspTime", earliestStr + "-" + latestStr);
                    dataMap.put("stakeName", stakeName);
                    dataMap.put("collect2", collect2);
                    dataMap.put("reduce", reduce);

                    dataMap.put("resultLM", resultLM);
                    dataMap.put("resultLMdvice", resultLMdvice);
                    dataMap.put("resultLJ", resultLJ);
                    dataMap.put("resultLJdvice", resultLJdvice);
                    dataMap.put("resultQL", resultQL);
                    dataMap.put("resultQLdvice", resultQLdvice);
                    dataMap.put("resultHD", resultHD);
                    dataMap.put("resultHDdvice", resultHDdvice);
                    dataMap.put("resultSD", resultSD);
                    dataMap.put("resultSDdvice", resultSDdvice);
                    dataMap.put("resultSS", resultSS);
                    dataMap.put("resultSSdvice", resultSSdvice);
                    dataMap.put("resultLH", resultLH);
                    dataMap.put("resultLHdvice", resultLHdvice);
                    dataMap.put("resultQT", resultQT);
                    dataMap.put("resultQTdvice", resultQTdvice);
                    dataMap.put("persion", persion);
                    dataMap.put("recList", value);
                    String fileName = code + ".xlsx"; // 完整文件名

                    Workbook workbook = ExcelExportUtil.exportExcel(params, dataMap);

                    // 创建临时目录（如果不存在）
                    Path tempDir = Paths.get(System.getProperty("java.io.tmpdir"), "my_temp_files");
                    if (!Files.exists(tempDir)) {
                        Files.createDirectories(tempDir);
                    }

                    // 创建自定义文件名的临时文件
                    Path tempFilePath = tempDir.resolve(fileName);
                    File tempFile = tempFilePath.toFile();
                    FileOutputStream fos = new FileOutputStream(tempFile);
                    workbook.write(fos);
                    fos.close();
                    workbook.close();

                    tempExcelFiles.add(tempFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }finally {
                    String format = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
                    incur.put(orgCode + "_" + format, i.get());
                }
            });
        });

        // 压缩打包为zip
        try {
            String zipName = "巡查记录_" + startDate + "_汇总.zip";
            response.setContentType("application/zip");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
            response.setHeader("content-disposition",
                    "attachment; filename=\"" + zipName + "\"; filename*=UTF-8''" + URLEncoder.encode(zipName, "UTF-8"));
            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());
            byte[] buffer = new byte[1024];
            // 自定义排序规则：先按日期排序，再按序号排序
            tempExcelFiles.sort((file1, file2) -> {
                String name1 = file1.getName();
                String name2 = file2.getName();

                // 提取日期部分（格式：20250702）
                String dateStr1 = name1.split("-")[3];
                String dateStr2 = name2.split("-")[3];

                // 提取序号部分（格式：001.xlsx）
                String seqStr1 = name1.split("-")[4];
                String seqStr2 = name2.split("-")[4];

                // 去掉序号的扩展名（".xlsx"）
                seqStr1 = seqStr1.substring(0, seqStr1.indexOf('.'));
                seqStr2 = seqStr2.substring(0, seqStr2.indexOf('.'));

                // 先比较日期
                int dateCompare = dateStr1.compareTo(dateStr2);
                if (dateCompare != 0) {
                    return dateCompare;
                }

                // 日期相同再比较序号
                return Integer.compare(
                        Integer.parseInt(seqStr1),
                        Integer.parseInt(seqStr2)
                );
            });
            for (File file : tempExcelFiles) {
                FileInputStream fis = new FileInputStream(file);
                zipOut.putNextEntry(new ZipEntry(file.getName()));
                int len;
                while ((len = fis.read(buffer)) > 0) {
                    zipOut.write(buffer, 0, len);
                }
                zipOut.closeEntry();
                fis.close();
            }

            zipOut.finish();
            zipOut.close();

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            // 删除临时文件
            for (File file : tempExcelFiles) {
                file.delete();
            }
        }
    }

    public static LocalTime getEarliestTime(String timeRanges) {
        return Arrays.stream(timeRanges.split(","))
                .flatMap(range -> Arrays.stream(range.split("-")))
                .map(time -> LocalTime.parse(time, TIME_FORMATTER))
                .min(Comparator.naturalOrder())
                .orElse(LocalTime.MIN);
    }

    public static LocalTime getLatestTime(String timeRanges) {
        return Arrays.stream(timeRanges.split(","))
                .flatMap(range -> Arrays.stream(range.split("-")))
                .map(time -> LocalTime.parse(time, TIME_FORMATTER))
                .max(Comparator.naturalOrder())
                .orElse(LocalTime.MAX);
    }

    private String getFacilityDssName(List<SumInspectionRecord> dinspCheckSum, String facility)
    {
        String collect = Optional.ofNullable(dinspCheckSum).orElse(Collections.emptyList()).stream()
                .filter(v -> ObjectUtil.isNotNull(v.getFacilityCategory()) && facility.equals(v.getFacilityCategory()))
                .map(v -> {
                    String lineDirect = Optional.ofNullable(v.getLineDirect()).orElse("");
                    String lane = Optional.ofNullable(v.getLane()).orElse("");
                    String stake = Optional.ofNullable(v.getStake()).orElse("");
                    String dssTypeName = Optional.ofNullable(v.getDssTypeName()).orElse("");
                    return lineDirect + lane + stake + dssTypeName;
                })
                .distinct()
                .collect(Collectors.joining(","));
        if (!StringUtils.isBlank(collect))
        {
            return collect;
        }else
        {
            return "未发现病害";
        }
    }

    private String getFacilityMntnAdvice(List<SumInspectionRecord> dinspCheckSum, String facility)
    {
        String collect = Optional.ofNullable(dinspCheckSum).orElse(Collections.emptyList()).stream()
                .filter(v -> ObjectUtil.isNotNull(v.getFacilityCategory()) && facility.equals(v.getFacilityCategory()))
                .map(v -> v.getMntnAdvice())
                .distinct()
                .collect(Collectors.joining(","));
        if (!StringUtils.isBlank(collect) && !"null".equals(collect))
        {
            return collect;
        }else
        {
            return "/";
        }
    }
}
