package com.hualu.app.module.mems.task.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.task.dto.DmTaskDto;
import com.hualu.app.module.mems.task.dto.DmTaskPrjOrgDto;
import com.hualu.app.module.mems.task.entity.DmTask;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 任务单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskService extends IService<DmTask> {


    /**
     * 删除任务单
     * @param mtaskId
     */
    void delDmTask(String mtaskId);

    /**
     * 任务单办结
     */
    void finshDmTask(String mtaskId,boolean isCreateAccptDetail,LocalDate date);

    /**
     * 统计任务单编码个数
     * @param orgId
     * @param taskCode
     * @param taskId
     * @return
     */
    int countDmTaskCode(String orgId,String taskCode,String taskId);

    /**
     * 添加或修改任务单
     * @param task
     */
    void saveOrUpdateTask(DmTask task);
    /**
     * 查询项目公司
     * @return
     */
    List<DmTaskPrjOrgDto> selectPrjOrgs();

    /**
     * 获取下一张单编码
     * @return
     */
    DmTaskDto getNextCode();

    /**
     * 根据流程实例ID，查询任务单
     * @param proInstId
     * @return
     */
    DmTask selectDmTaskByProcessInstId(Long proInstId);

    /**
     * 设置任务单发送人信息
     * @param dmTaskId
     * @param userCode
     * @param status
     */
    void updateDmTaskSendUser(String dmTaskId, String userCode, int status, LocalDate date);

    /**
     * 设置任务单接收人信息
     * @param dmTaskId
     * @param userCode
     * @param status
     */
    void updateDmTaskReceiveUser(String dmTaskId, String userCode, int status, LocalDate date);

    /**
     * 修改任务单状态
     * @param dmTaskId
     * @param status
     */
    void updateDmtaskStatus(String dmTaskId,int status);

    /**
     * 任务单分页查询
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage selectPage(Page page, QueryWrapper queryWrapper);
}
