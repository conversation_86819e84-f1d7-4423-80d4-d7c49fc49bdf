package com.hualu.app.module.mems.comm.service;

import com.hualu.app.module.mems.comm.entity.BaseStructComp;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface BaseStructCompService extends IService<BaseStructComp> {

    /**
     * 查询交安类型
     * @return
     */
    List<BaseStructComp> selectStructCompByJA();

    /**
     * 查询桥梁
     * @param partsCode
     * @return
     */
    List<BaseStructComp> selectStrcutCompByQL(String partsCode);

    /**
     * 查询同级构件信息
     * @param structCompId
     * @return
     */
    List<BaseStructComp> selectStructCompByTj(String structCompId);

}
