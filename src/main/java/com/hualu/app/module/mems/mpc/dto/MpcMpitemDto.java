package com.hualu.app.module.mems.mpc.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 措施信息
 */
@Data
public class MpcMpitemDto implements Serializable {

    //措施ID
    private String mpitemId;
    //措施编码
    private String mpitemCode;
    //措施名称
    private String mpitemName;
    //勘估数量
    private Double mpitemAccount;
    //单位
    private String measureUnit;
    //措施单价
    private Double unitPrice;
    //勘估数量（单位）
    private String mpitemAccountName;
    //措施总额（任务单金额）
    private Double money;
}
