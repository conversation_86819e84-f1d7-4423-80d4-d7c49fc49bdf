package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;

import java.util.List;

/**
 * <p>
 * 新版日常巡查记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface NmDinspRecordService extends IService<NmDinspRecord> {

    List<NmDinspRecord> selectRecordByDinspId(String dinspId);

    List<NmDinspRecord> selectRecordByDinspId(List<String> dinspIds);

    List<NmDinspRecord> selectRecords(String[] dssIds);

    /**
     * 根据通用主单，查询病害列表
     * @param dinspCommon
     * @return
     */
    List<NmDinspRecord> selectRecords(NmDinspCommon dinspCommon);

    /**
     * 回显前端界面数据
     * @param records
     * @param nmDinsp
     */
    void showView(List<NmDinspRecord> records, NmDinsp nmDinsp);

    /**
     * 删除记录
     * @param dssIds
     */
    void deleteRecord(List<String> dssIds);

    void deleteRecordByDinspIds(List<String> dinspIds);
    /**
     * 添加或修改记录
     * @param nmDinspRecord
     */
    void saveOrUpdateRecordForPC(NmDinspRecord nmDinspRecord);

    void saveOrUpdateRecordForApp(NmDinspRecord nmDinspRecord);
}
