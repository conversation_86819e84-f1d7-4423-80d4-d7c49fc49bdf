package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 公路路面质量指数计算任务表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_TASK")
@ApiModel(value="HighwayTask对象", description="公路路面质量指数计算任务表")
public class HighwayTask implements Serializable {

    
    @NotBlank(message="[任务唯一标识]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("任务唯一标识")
    @TableField("TASK_ID")
    private String taskId;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("任务类型")
    @TableField("TASK_TYPE")
    private String taskType;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("路段ID")
    @TableField("SEGMENT_ID")
    private String segmentId;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("路段编码")
    @TableField("SEGMENT_CODE")
    private String segmentCode;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("路段名称")
    @TableField("SEGMENT_NAME")
    private String segmentName;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("检测方向")
    @TableField("INSPECT_DIRECTION")
    private String inspectDirection;
    
    @ApiModelProperty("路段长度(米)")
    @TableField("SEGMENT_LENGTH")
    private BigDecimal segmentLength;
    
    @ApiModelProperty("统计开始时间")
    @TableField("START_TIME")
    private LocalDateTime startTime;
    
    @ApiModelProperty("统计结束时间")
    @TableField("END_TIME")
    private LocalDateTime endTime;
    
    @ApiModelProperty("创建时间")
    @TableField("CREATED_TIME")
    private LocalDateTime createdTime;
    
    @ApiModelProperty("更新时间")
    @TableField("UPDATED_TIME")
    private LocalDateTime updatedTime;
    
    @Size(max= 2,message="编码长度不能超过2")
    @ApiModelProperty("状态：-1-失败，0-未计算，1-计算中，2-完成")
    @TableField("TASK_STATUS")
    private String taskStatus;
    
    @ApiModelProperty("等级")
    @TableField("QUALITY_LEVEL")
    private Integer qualityLevel;
    
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("道路类型")
    @TableField("ROAD_TYPE")
    private String roadType;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("起始桩号")
    @TableField("STAKE_START")
    private String stakeStart;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("终止桩号")
    @TableField("STAKE_END")
    private String stakeEnd;
    
    @ApiModelProperty("路面质量指数")
    @TableField("PQI_VALUE")
    private BigDecimal pqiValue;
}
