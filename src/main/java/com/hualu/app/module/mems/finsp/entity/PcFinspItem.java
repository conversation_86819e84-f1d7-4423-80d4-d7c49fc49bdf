package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 结构物排查检查内容表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinspItem对象", description="结构物排查检查内容表")
public class PcFinspItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("FINSP_ITEM_ID")
    private String finspItemId;

    @TableField("FACILITY_CAT")
    private String facilityCat;

    @TableField("FINSP_ITEM_CODE")
    private String finspItemCode;

    @TableField("INSP_COM")
    private String inspCom;

    @TableField("INSP_CONT")
    private String inspCont;

    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @TableField("FIN_VERSION")
    private String finVersion;

    @TableField("IS_DELETE")
    private String isDelete;

    @TableField("FIN_DESC")
    private String finDesc;

    @TableField("FIN_REMARK")
    private String finRemark;

    @TableField("PART_CODE")
    private String partCode;


}
