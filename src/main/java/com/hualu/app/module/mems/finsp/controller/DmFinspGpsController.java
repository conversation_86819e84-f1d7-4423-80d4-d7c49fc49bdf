package com.hualu.app.module.mems.finsp.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspGps;
import com.hualu.app.module.mems.finsp.entity.SlopeRegularSourceTrack;
import com.hualu.app.module.mems.finsp.mapper.DmFinspGpsMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspGpsService;
import com.hualu.app.module.mems.finsp.service.DmFinspService;
import com.hualu.app.module.mems.finsp.service.SlopeRegularSourceTrackService;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 经常检查单GPS位置 前端控制器
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Api(tags = "经常检查_轨迹接口")
@RestController
@RequestMapping("/dmFinspGps")
public class DmFinspGpsController extends M_MyBatisController<DmFinspGps,DmFinspGpsMapper>{

    @Autowired
    private DmFinspGpsService service;

    @Autowired
    private SlopeRegularSourceTrackService slopeRegularSourceTrackService;

    @Autowired
    private DmFinspService dmFinspService;

    /**
    * 批量保存GPS数据
    * @param gpsList
    * @return
    */
    @ApiRegister(value = "dmFinspGps:saveBatchGps",businessType = BusinessType.UPDATE)
    @ApiOperation("批量保存GPS数据")
    @PostMapping("saveBatchGps")
    public RestResult<String> saveBatchGps(@RequestBody List<DmFinspGps> gpsList) {
        if (CollectionUtil.isNotEmpty(gpsList)) {
            service.saveOrUpdateBatch(gpsList);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 获取轨迹数据集合
     * @param finspId 经常检查单ID
     * @return
     */
    @ApiOperation("获取轨迹数据集合")
    @GetMapping("getGpsByFinspId")
    public RestResult<List<DmFinspGps>> getGpsByFinspId(String finspId) {
        LambdaQueryWrapper<DmFinspGps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspGps::getFinspId,finspId).orderByAsc(DmFinspGps::getTime);
        List<DmFinspGps> list = service.list(queryWrapper);
        return RestResult.success(list);
    }

    /**
     * 获取实时位置信息
     * @param finspId 经常检查单ID
     * @return
     */
    @GetMapping("getRealTimeLocationByFinspId")
    public RestResult<DmFinspGps> getRealTimeLocationByFinspId(String finspId) {
        DmFinspGps one = baseMapper.getRealTimeLocationByFinspId(finspId);
        return RestResult.success(one);
    }

    /**
     * 批量保存边坡原始轨迹
     * @param gpsList
     * @return
     */
    @ApiOperation("批量保存边坡原始轨迹")
    @PostMapping("saveSlopeTrack")
    public RestResult<String> saveSlopeTrack(@RequestBody List<SlopeRegularSourceTrack> gpsList) {
        if (CollectionUtil.isNotEmpty(gpsList)) {
            slopeRegularSourceTrackService.saveOrUpdateBatch(gpsList);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 获取边坡原始轨迹集合
     * @param finspId 检查单id
     * @return
     */
    @ApiOperation("获取边坡原始轨迹集合")
    @GetMapping("getSlopeTrack")
    public RestResult<List<SlopeRegularSourceTrack>> getSlopeTrack(String finspId) {
        LambdaQueryWrapper<DmFinsp> ew = new LambdaQueryWrapper<>();
        ew.eq(DmFinsp::getFinspId, finspId);
        DmFinsp finsp = dmFinspService.getOne(ew, false);
        if (finsp != null) {
            LambdaQueryWrapper<SlopeRegularSourceTrack> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SlopeRegularSourceTrack::getSlopeId, finsp.getStructId())
                .orderByAsc(SlopeRegularSourceTrack::getTime);
            List<SlopeRegularSourceTrack> list = slopeRegularSourceTrackService.list(queryWrapper);
            return RestResult.success(list);
        }
        return RestResult.success(new ArrayList<>());
    }

    /**
     * 清除边坡原始巡查轨迹
     * @param slopeId 检查单id
     * @return
     */
    @ApiOperation("清除边坡原始巡查轨迹")
    @GetMapping("clearSlopeTrack")
    public RestResult<String> clearSlopeTrack(String slopeId) {
        LambdaQueryWrapper<SlopeRegularSourceTrack> ew = new LambdaQueryWrapper<>();
        ew.eq(SlopeRegularSourceTrack::getSlopeId, slopeId);
        boolean result = slopeRegularSourceTrackService.remove(ew);
        return result ? RestResult.success("删除成功") : RestResult.error("删除失败");
    }

    /**
     * 获取边坡原始轨迹集合
     * @param slopeId 边坡id
     * @return
     */
    @ApiOperation("获取边坡原始轨迹集合")
    @GetMapping("getSlopeTrackBySlopeId")
    public RestResult<List<SlopeRegularSourceTrack>> getSlopeTrackBySlopeId(String slopeId) {
        LambdaQueryWrapper<SlopeRegularSourceTrack> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SlopeRegularSourceTrack::getSlopeId, slopeId)
            .orderByAsc(SlopeRegularSourceTrack::getTime);
        List<SlopeRegularSourceTrack> list = slopeRegularSourceTrackService.list(queryWrapper);
        return RestResult.success(list);
    }


}