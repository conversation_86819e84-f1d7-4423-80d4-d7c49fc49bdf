package com.hualu.app.module.mems.dinsp.entity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 巡查记录实体类
 * 对应 SQL 字段：
 * SELECT
 *   O.ORG_NAME,
 *   L.ROUTE_NAME,
 *   DECODE(L.LINE_DIRECT, '1', '上行', '2', '下行') as LINE_DIRECT,
 *   D.STAKE,
 *   L.ROUTE_LENGTH as length,
 *   '经常检查' AS checkType,
 *   ABS(D.END_STAKE_NUM - D.START_STAKE_NUM) as checkLength,
 *   SP.SEARCH_DEPT,
 *   SP.INSP_PERSON,
 *   SP.INSP_DATE
 */
public class InspectionRecord {

    // 字段声明及注释

    /**
     * 机构名称，对应 O.ORG_NAME
     */
    private String orgName;

    /**
     * 路线名称，对应 L.ROUTE_NAME
     */
    private String routeName;

    /**
     * 线路方向，通过 DECODE 转换：
     * - '1' → '上行'
     * - '2' → '下行'
     * 对应字段 L.LINE_DIRECT
     */
    private String lineDirect;

    /**
     * 桩号，对应 D.STAKE（例如 K123+456）
     */
    private String stake;

    /**
     * 路线长度，对应 L.ROUTE_LENGTH（别名为 length）
     */
    private BigDecimal length; // 使用 BigDecimal 保证精度

    /**
     * 检查类型，固定值 '经常检查'
     */
    private final String checkType = "经常检查"; // 常量直接初始化

    /**
     * 检查长度，通过 ABS(D.END_STAKE_NUM - D.START_STAKE_NUM) 计算
     * 表示检测区间绝对距离
     */
    private BigDecimal checkLength;

    /**
     * 巡查部门，对应 SP.SEARCH_DEPT
     */
    private String searchDept;

    /**
     * 巡查人员，对应 SP.INSP_PERSON
     */
    private String inspPerson;

    /**
     * 巡查日期，对应 SP.INSP_DATE
     */
    private LocalDate inspDate; // 根据数据库实际类型选择 LocalDate/LocalDateTime

    // 构造方法
    public InspectionRecord() {
    }

    // 带参构造方法（可选）
    public InspectionRecord(String orgName, String routeName, String lineDirect,
                            String stake, BigDecimal length, BigDecimal checkLength,
                            String searchDept, String inspPerson, LocalDate inspDate) {
        this.orgName = orgName;
        this.routeName = routeName;
        this.lineDirect = lineDirect;
        this.stake = stake;
        this.length = length;
        this.checkLength = checkLength;
        this.searchDept = searchDept;
        this.inspPerson = inspPerson;
        this.inspDate = inspDate;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public String getStake() {
        return stake;
    }

    public void setStake(String stake) {
        this.stake = stake;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public String getCheckType() {
        return checkType;
    }

    public BigDecimal getCheckLength() {
        return checkLength;
    }

    public void setCheckLength(BigDecimal checkLength) {
        this.checkLength = checkLength;
    }

    public String getSearchDept() {
        return searchDept;
    }

    public void setSearchDept(String searchDept) {
        this.searchDept = searchDept;
    }

    public String getInspPerson() {
        return inspPerson;
    }

    public void setInspPerson(String inspPerson) {
        this.inspPerson = inspPerson;
    }

    public LocalDate getInspDate() {
        return inspDate;
    }

    public void setInspDate(LocalDate inspDate) {
        this.inspDate = inspDate;
    }
}
