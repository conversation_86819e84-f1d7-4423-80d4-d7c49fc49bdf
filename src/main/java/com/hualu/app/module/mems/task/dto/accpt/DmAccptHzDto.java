package com.hualu.app.module.mems.task.dto.accpt;

import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * 验收单单汇总信息
 */
@Data
public class DmAccptHzDto implements Serializable {

    //验收单ID
    private String mtaskAccptId;

    //验收单金额
    private Double totalMoney = 0d;

    //病害数量
    private Integer dssNum = 0;

    //病害详情列表
    List<DmTaskDssViewDto> dssViewDtos = Lists.newArrayList();
}
