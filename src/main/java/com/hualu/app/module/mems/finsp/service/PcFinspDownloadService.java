package com.hualu.app.module.mems.finsp.service;

import com.hualu.app.module.mems.finsp.entity.PcFinspDownload;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 结构物排查数据下载表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
public interface PcFinspDownloadService extends IService<PcFinspDownload> {

    /**
     * 检查数据是否正在生成
     * @param pcFinspDownload
     */
    void checkRunning(PcFinspDownload pcFinspDownload);

    void updateState(String dataId, Integer state);
}
