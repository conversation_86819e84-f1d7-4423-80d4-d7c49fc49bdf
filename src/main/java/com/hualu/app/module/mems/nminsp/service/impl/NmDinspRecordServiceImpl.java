package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspRecordMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 新版日常巡查记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class NmDinspRecordServiceImpl extends ServiceImpl<NmDinspRecordMapper, NmDinspRecord> implements NmDinspRecordService {

    @Autowired
    DssImageService imageService;

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    DssTypeNewService dssTypeNewService;

    @Autowired
    NmInspContentService contentService;

    @Autowired
    BaseDatathirdDicService dicService;

    private static Map<Integer,String> statusMap = Maps.newHashMap();

    static {
        statusMap.put(0,"未提交");
        statusMap.put(1,"已提交");
        statusMap.put(2,"待审核");
        statusMap.put(3,"已审核");
        statusMap.put(0,"被退回");
    }

    @Override
    public List<NmDinspRecord> selectRecordByDinspId(String dinspId) {
        LambdaQueryWrapper<NmDinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmDinspRecord::getDinspId,dinspId);
        return list(queryWrapper);
    }

    @Override
    public List<NmDinspRecord> selectRecordByDinspId(List<String> dinspIds) {
        List<List<String>> partition = ListUtil.partition(dinspIds, 500);
        List<NmDinspRecord> recordList = Lists.newArrayList();
        partition.forEach(rows->{
            recordList.addAll(lambdaQuery().in(NmDinspRecord::getDinspId,rows).list());
        });
        return recordList;
    }

    @Override
    public List<NmDinspRecord> selectRecords(String[] dssIds) {
        LambdaQueryWrapper<NmDinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NmDinspRecord::getDssId,dssIds);
        return list(queryWrapper);
    }

    @Override
    public List<NmDinspRecord> selectRecords(NmDinspCommon dinspCommon) {
        List<NmDinspRecord> records = baseMapper.selectRecords(dinspCommon);
        Map<String, List<NmDinspRecord>> dinspMap = records.stream().collect(Collectors.groupingBy(NmDinspRecord::getDinspId));
        dinspMap.forEach((dinspId,recordList)->{
            NmDinspRecord dinspRecord = recordList.get(0);
            NmDinsp nmDinsp = new NmDinsp()
                .setStructId(dinspRecord.getStructId())
                .setFacilityCat(dinspRecord.getFacilityCat())
                .setLineCode(dinspRecord.getLineCode())
                .setStructName(dinspRecord.getStructName());
            showView(recordList,nmDinsp);
        });
        return records;
    }

    @Override
    public void showView(List<NmDinspRecord> records, NmDinsp nmDinsp) {
        showCommonView(records);
        //各个设施特殊字段回显
        if (nmDinsp != null && CollectionUtil.isNotEmpty(records)) {
            records.forEach(v -> {
                v.setLineCode(nmDinsp.getLineCode());
                // 在路面巡查时，会录入服务区和收费站信息
                if (StrUtil.isBlank(v.getStructId())){
                    v.setStructId(nmDinsp.getStructId()).setStructName(nmDinsp.getStructName());
                }
            });
            H_StructHelper.showNmDinspView(nmDinsp.getFacilityCat(),records);
        }
    }

    /**
     * 回显公用字段：路线方向，病害数量，病害名称，病害照片
     * @param records
     */
    private void showCommonView(List<NmDinspRecord> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        //todo 根据病害ID，查询对应的任务单、验收单号
        List<String> dssIds = records.stream().map(NmDinspRecord::getDssId).collect(Collectors.toList());
        //查询照片
        Map<String, List<String>> imageMap = imageService.mapByDssIds(dssIds.stream().collect(Collectors.toSet()));
        List<NmDinspRecord> viewRecords = baseMapper.selectViewRecord(dssIds);
        // 查询部位名称
        Map<String, String> contentMap = contentService.getContentMap(Lists.newArrayList(records.stream().map(NmDinspRecord::getStructPartId).collect(Collectors.toSet())));
        Map<String, NmDinspRecord> dssMap = viewRecords.stream().collect(Collectors.toMap(NmDinspRecord::getDssId, Function.identity()));
        for (NmDinspRecord record : records) {
            NmDinspRecord item = dssMap.get(record.getDssId());
            if (item != null) {
                record.setMtaskAccptId(item.getMtaskAccptId()).setMtaskAccptCode(item.getMtaskAccptCode())
                        .setMtaskId(item.getMtaskId()).setMtaskCode(item.getMtaskCode())
                        .setRepairStatus(item.getRepairStatus()).setDssTypeName(item.getDssTypeName());
            }
            record.setStructPartName(contentMap.get(record.getStructPartId()));
            record.setLineDirectName(dicService.getDicName("LANE_DIRECTION", item.getLineDirect()));
            record.setLaneName(dicService.getDicName("LANE",item.getLane()));
            List<String> images = imageMap.get(item.getDssId());
            //赋值照片
            if (CollectionUtil.isNotEmpty(images)) {
                record.setFileIds(StrUtil.join(",", images));
                item.setImageHost(C_Constant.IMAGE_HOST);
            }
            //回显位置信息
            if (ObjectUtil.isNotEmpty(record.getStake())){
                String stake = record.getStake() == null ? null : record.getStake().toString();
                record.setStakeCn(H_StakeHelper.convertCnStake(stake));
            }

            // 回显服务区和收费站信息
            if (ObjectUtil.isNull(record.getStakeCn()) && ObjectUtil.isNotEmpty(record.getStructName())){
                record.setStakeCn(record.getStructName());
            }

            // 回显单状态
            record.setStatusName(statusMap.get(record.getStatus()));
            initDssDisplayNum(record);
            initStatus(record);
        }
    }


    /**
     * 修复状态
     * @param record
     */
    private void initStatus(NmDinspRecord record){
        if(StrUtil.isBlank(record.getRepairStatus())||record.getRepairStatus().equals("0")){
            record.setRepairStatus("待修复");
        }else if(record.getRepairStatus().equals("1")){
            record.setRepairStatus("修复中");
        }else{
            record.setRepairStatus("已修复");
        }
    }

    /**
     * 显示病害
     * @param entity
     */
    private void initDssDisplayNum(NmDinspRecord entity){
        StringBuffer str = new StringBuffer();
        Double dssL = entity.getDssL();
        if(dssL!= null && StrUtil.isNotBlank(entity.getDssLUnit()) && BigDecimal.valueOf(dssL).compareTo(BigDecimal.ZERO) > 0){
            str.append("长："+ dssL + entity.getDssLUnit()+" ");
            entity.setDlNum(dssL).setDlUnit(entity.getDssLUnit());
        }
        Double dssW = entity.getDssW();
        if(dssW!= null && StrUtil.isNotBlank(entity.getDssWUnit()) && BigDecimal.valueOf(dssW).compareTo(BigDecimal.ZERO) > 0){
            str.append("宽："+ dssW + entity.getDssWUnit()+" ");
            entity.setDlNum(dssW).setDlUnit(entity.getDssWUnit());
        }
        Double dssD = entity.getDssD();
        if(dssD!= null && StrUtil.isNotBlank(entity.getDssDUnit()) && BigDecimal.valueOf(dssD).compareTo(BigDecimal.ZERO) > 0){
            str.append("深："+ dssD + entity.getDssDUnit()+" ");
            entity.setDlNum(dssD).setDlUnit(entity.getDssDUnit());
        }
        Double dssA = entity.getDssA();
        if(dssA!= null && StrUtil.isNotBlank(entity.getDssAUnit()) && BigDecimal.valueOf(dssA).compareTo(BigDecimal.ZERO) > 0){
            str.append("面积："+ dssA + entity.getDssAUnit()+" ");
            entity.setDlNum(dssA).setDlUnit(entity.getDssAUnit());
        }
        Double dssV = entity.getDssV();
        if(dssV!= null && StrUtil.isNotBlank(entity.getDssVUnit()) && BigDecimal.valueOf(dssV).compareTo(BigDecimal.ZERO) > 0){
            str.append("体积："+ dssV + entity.getDssVUnit()+" ");
            entity.setDlNum(dssV).setDlUnit(entity.getDssVUnit());
        }
        Double dssN = entity.getDssN();
        if(dssN!= null && StrUtil.isNotBlank(entity.getDssNUnit()) && BigDecimal.valueOf(dssN).compareTo(BigDecimal.ZERO) > 0){
            str.append("数量："+ dssN + entity.getDssNUnit()+" ");
            entity.setDlNum(dssN).setDlUnit(entity.getDssNUnit());
        }
        Double dssP = entity.getDssP();
        if(dssP!= null && BigDecimal.valueOf(dssP).compareTo(BigDecimal.ZERO) > 0){
            str.append("百分比："+ dssP +"% ");
            entity.setDlNum(dssP).setDlUnit("%");
        }
        Double dssG = entity.getDssG();
        if(dssG!= null && BigDecimal.valueOf(dssG).compareTo(BigDecimal.ZERO) > 0){
            str.append("角度："+ dssG +"度 ");
            entity.setDlNum(dssG).setDlUnit("度");
        }
        entity.setDssNum(str.toString());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteRecord(List<String> dssIds) {
        if (CollectionUtil.isEmpty(dssIds)){
            return;
        }
        NmDinspRecord record = getById(dssIds.get(0));
        NmDinsp nmDinsp = nmDinspService.getById(record.getDinspId());
        // 删除照片
        imageService.delBatchByDssIds(dssIds);
        removeByIds(dssIds);
        // 更新检查单病害数量
        nmDinspService.updateDssNum(nmDinsp.getDinspId());
        // 更新结论
        H_StructHelper.refreshNmDinspResult(nmDinsp);
    }

    @Override
    public void deleteRecordByDinspIds(List<String> dinspIds) {
        LambdaQueryWrapper<NmDinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NmDinspRecord::getDinspId,dinspIds).select(NmDinspRecord::getDssId);
        List<NmDinspRecord> recordList = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(recordList)){
            List<String> dssIds = recordList.stream().map(NmDinspRecord::getDssId).collect(Collectors.toList());
            // 删除照片
            imageService.delBatchByDssIds(dssIds);
            // 删除病害
            removeByIds(dssIds);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateRecordForPC(NmDinspRecord nmDinspRecord) {
        saveOrUpdateRecord(nmDinspRecord,false);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateRecordForApp(NmDinspRecord nmDinspRecord) {
        saveOrUpdateRecord(nmDinspRecord,true);
    }


    private void saveOrUpdateRecord(NmDinspRecord nmDinspRecord ,boolean isApp) {
        // 根据病害定量，获取定量单位参数
        dssTypeNewService.setDssUnit(nmDinspRecord,nmDinspRecord.getDssType());
        NmDinsp nmDinsp = nmDinspService.getById(nmDinspRecord.getDinspId());
        if (StrUtil.isBlank(nmDinspRecord.getDssId())){
            nmDinspRecord.setDssId(H_KeyWorker.nextIdToString());
            // 数来源于日常巡查：1
            nmDinspRecord.setSource("1");
            nmDinspRecord.setCreateTime(null);
            // 病害补充结构ID
            if (StrUtil.isBlank(nmDinspRecord.getStructId())){
                nmDinspRecord.setStructId(nmDinsp.getStructId()).setStructName(nmDinspRecord.getStructName());
            }
            save(nmDinspRecord);
        }else {
            nmDinspRecord.setUpdateTime(null);
            updateById(nmDinspRecord);
        }
        if (isApp){
            imageService.saveDssImageForApp(nmDinspRecord.getDssId(),nmDinspRecord.getFileIds(),1);
        }else {
            imageService.saveDssImageForPC(nmDinspRecord.getDssId(),nmDinspRecord.getFileIds(),1);
        }
        nmDinspService.updateDssNum(nmDinsp.getDinspId());
        H_StructHelper.refreshNmDinspResult(nmDinsp);
    }
}
