package com.hualu.app.module.mems.autoInspector.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("病害类型映射DTO")
public class DefectTypeMappingDTO {
    
    @ApiModelProperty("病害类型编码")
    private String dssType;
    
    @ApiModelProperty("病害类型名称")
    private String categoryName;
    
    @ApiModelProperty("对象类型名称")
    private String objectType;
    
    @ApiModelProperty("对象子类型名称")
    private String objectSubtypeName;
} 