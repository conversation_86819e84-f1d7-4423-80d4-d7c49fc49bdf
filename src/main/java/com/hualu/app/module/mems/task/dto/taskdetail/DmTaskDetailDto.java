package com.hualu.app.module.mems.task.dto.taskdetail;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DmTaskDetailDto implements Serializable {

    private String mainRoadId;

    @TableId("MTASK_DTL_ID")
    private String mtaskDtlId;

    @TableField("MTASK_ID")
    private String mtaskId;

    @TableField("DSS_ID")
    private String dssId;

    @TableField("MPITEM_ID")
    private String mpitemId;

    @TableField("MEASURE_UNIT")
    private String measureUnit;

    @TableField("MPITEM_ACCOUNT")
    private Double mpitemAccount;

    @TableField("IS_LUMP")
    private Integer isLump;

    @TableField("REMARK")
    private String remark;

    @TableField("ACCEPT_STATUS")
    private Integer acceptStatus;

    @TableField("IS_ADD")
    private Integer isAdd;

    private String contrId;
    private String rlStake;
    private String structId;
    private String lineDirect;
    private String lane;
    private double stake;
    private String dssTypeName;
    private String mpitemName;
    private String lineId;
    private String lineCode;
    private String line_direct;
    private String rl_stake;
    private String lineName;		//路线名称+（编码）
    private String rowNum;			//序号
    private Date foundDate;		//病害发现日期
    private Date sendTime;		//维修通知时间
    private String lump;
    private String processinstid;
    private Double finishStake;
    private Double dssL;				//病害长
    private String dssLUnit;			//病害长单位
    private Double dssW;				//病害宽
    private String dssWUnit;			//病害宽单位
    private Double dssD;				//病害深
    private String dssDUnit;
    private String dssPosition;//病害深单位
    private Double dssN;				//病害数量
    private String dssNUnit;			//病害数量单位
    private Double dssA;				//病害面积
    private String dssAUnit;			//病害面积单位
    private Double dssV;				//病害体积
    private String dssVUnit;			//病害体积单位
    private Double dssP;				//病害百分比
    private Double dssG;				//病害角度
    //病害数量（描述）
    private String dssNum;
    private Integer dssSource;

    private String repairDate;			//维修时间，冗余字段，维修任务单直接跳转至验收申请时使用
    private Double applyAmount;			//申请验收数量，冗余字段，维修任务单直接跳转至验收申请时使用
    private Double acceptAmount;//验收数量
    private String repairStatus;		//修复状态
    private String mmpCode;
    private String fileIds;
    private String afterFileIds;
    private String rampId;				//匝道ID
    private String ids;				//匝道ID
    private String decods;				//匝道ID

    private double unitPrice;   //单价
    private double money;	//小计
    private String mtaskAccptCode;		//验收单主单ID mtask_accpt_id

    private String accptPsId;           //验收单流程ID 用于查询验收单accpt_ps_id;
    private int xuHao;

    private String structPartId;

    private Double rlFinishStake;
}
