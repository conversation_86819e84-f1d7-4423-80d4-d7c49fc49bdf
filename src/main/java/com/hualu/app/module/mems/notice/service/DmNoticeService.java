package com.hualu.app.module.mems.notice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.notice.entity.DmNotice;
import org.springframework.web.multipart.MultipartFile;

public interface DmNoticeService extends IService<DmNotice> {

  IPage<DmNotice> getRoadNoticeList(
      IPage<DmNotice> p,
      String startDate,
      String endDate,
      String keyword,
      String lineCode,
      int status
  );

  DmNotice saveOrUpdateDmNotice(DmNotice dmNotice, MultipartFile[] files);

  DmNotice getDefaultNotice();

  boolean deleteNoticeById(String noticeId);

}
