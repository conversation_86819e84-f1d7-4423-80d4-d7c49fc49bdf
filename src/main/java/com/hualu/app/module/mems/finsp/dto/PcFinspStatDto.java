package com.hualu.app.module.mems.finsp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class PcFinspStatDto implements Serializable {

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 结构物类型 QL、SD、HD、BP
     */
    @JsonIgnore
    private String facilityCat;


    /**
     * 月份 2024-08
     */
    private String month;

    /**
     * 单位类型（1：定检单位、2：养护单位）
     */
    private String mntType;

    /**
     * 养护处治类型(1:小修处治,2:专项处治,3:应急处治)
     */
    private String dealType;

    /**
     * 小修处治病害数
     */
    private int xxDealNum;

    /**
     * 专项处治病害数
     */
    private int zxDealNum;

    /**
     * 应急处治病害数
     */
    private int yjDealNum;

    /**
     * 无需处治病害数
     */
    private int noDealNum;

    /**
     * 病害个数
     */
    private Integer dssNum;

    /**
     * 处治病害个数
     */
    private Integer dealDssNum;


    /**
     * 已检查结构物数量
     */
    private Integer checkedNum;

    /**
     * 未检查结构物数量
     */
    private Integer notCheckedNum;

    /**
     * 类型：PC：排查，WX：维修
     */
    @JsonIgnore
    private String type;

    @JsonIgnore
    private String md5;
}
