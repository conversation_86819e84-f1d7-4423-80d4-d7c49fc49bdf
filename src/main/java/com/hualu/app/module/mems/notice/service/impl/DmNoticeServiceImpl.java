package com.hualu.app.module.mems.notice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.notice.entity.DmNotice;
import com.hualu.app.module.mems.notice.entity.DmNoticeDetail;
import com.hualu.app.module.mems.notice.mapper.DmNoticeMapper;
import com.hualu.app.module.mems.notice.service.DmNoticeDetailService;
import com.hualu.app.module.mems.notice.service.DmNoticeService;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class DmNoticeServiceImpl extends ServiceImpl<DmNoticeMapper, DmNotice>
    implements DmNoticeService, IWorkItemEventHandler, OrderInfoHandler {

  @Resource
  private DmNoticeServiceImpl dmNoticeService;

  @Resource
  DssImageService imageService;

  @Resource
  private DssInfoService dssInfoService;

  @Resource
  private DmNoticeDetailService detailService;

  @Override
  public IPage<DmNotice> getRoadNoticeList(
      IPage<DmNotice> p,
      String startDate,
      String endDate,
      String keyword,
      String lineId,
      int status
  ) {
    QueryWrapper<DmNotice> wrapper = new QueryWrapper<>();
    wrapper.apply(StringUtils.hasText(startDate), "NOTICE_DATE >= to_date({0},'YYYY-MM-dd')",
        startDate);
    wrapper.apply(StringUtils.hasText(endDate), "NOTICE_DATE <= to_date({0},'YYYY-MM-dd')",
        endDate);
    wrapper.eq(StringUtils.hasText(lineId), DmNotice.COL_LINE_CODE, lineId);
    wrapper.apply(StringUtils.hasText(keyword), "NOTICE_CODE like '%' || {0} || '%'", keyword);
    wrapper.in("MNT_ORG_ID", H_DataAuthHelper.selectOrgIds());
    String taskSql = H_WorkFlowHelper.getUserTaskSql(status, "m");
    if (StringUtils.hasText(taskSql)) {
      wrapper.exists(taskSql);
    }
    wrapper.orderByDesc("NOTICE_DATE");
    IPage<DmNotice> ps = getBaseMapper().getRoadNoticeList(p, wrapper);
    ps.getRecords()
        .forEach(v -> v.setStatusName(
            H_BasedataHepler.roadNoticeStatusMap.getOrDefault(v.getStatus(), "")));
    initImage(ps.getRecords());
    return ps;
  }

  /**
   * 初始化照片
   *
   * @param notices 通知单集合
   */
  private void initImage(List<DmNotice> notices) {
    if (CollectionUtil.isEmpty(notices)) {
      return;
    }
    Set<String> dssIds = notices.stream().map(DmNotice::getNoticeId).collect(Collectors.toSet());
    Map<String, List<String>> imageMap = imageService.mapByDssIds(dssIds);
    for (DmNotice item : notices) {
      List<String> fileIds = imageMap.get(item.getNoticeId());
      if (CollectionUtil.isNotEmpty(fileIds)) {
        item.setFileIds(StrUtil.join(",", fileIds));
        item.setImageHost(C_Constant.IMAGE_HOST);
      }
    }
  }

  @Override
  public DmNotice saveOrUpdateDmNotice(DmNotice dmNotice, MultipartFile[] files) {
    dmNotice.setMntOrgId(CustomRequestContextHolder.getOrgIdString());

    //上传检查工照
    List<String> fileIds = H_UploadHelper.uploadFile(files);
    if (CollectionUtil.isNotEmpty(fileIds)) {
      dmNotice.setFileIds(StrUtil.join(",", fileIds));
    }

    //设置修改和创建人
    String userCode = CustomRequestContextHolder.getUserCode();
    if (StringUtil.isNullOrEmpty(dmNotice.getNoticeId())) {
      dmNotice.setCreateUserId(userCode);
    } else {
      dmNotice.setUpdateUserId(userCode);
    }

    boolean existNoticeCode = getOne(
        new QueryWrapper<DmNotice>()
            .eq(DmNotice.COL_NOTICE_CODE, dmNotice.getNoticeCode()),
        false
    ) != null;

    if (!StringUtils.hasText(dmNotice.getNoticeId()) && existNoticeCode) {
      throw new BaseException("已存在通知单编码");
    }

    return dmNoticeService.saveOrUpdateDmNotice(dmNotice);
  }

  @SneakyThrows
  @BpsTransactionalAnno
  @Transactional(rollbackFor = Exception.class)
  public DmNotice saveOrUpdateDmNotice(DmNotice dmNotice) {
    //验证单号
    String noticeCode = dmNotice.getNoticeCode();
    StringUtil.checkCode(noticeCode, 3);
    if (dmNotice.getProcessinstid() == null || dmNotice.getProcessinstid() == 0) {
      String processDefName = "gdcg.emdc.mems.dm.DmNoticeWorkFlow";
      long processInstId = H_WorkFlowHelper.createWorkItem(processDefName,
          "路政通知书[" + dmNotice.getNoticeCode() + "]", dmNotice.getRemark());
      dmNotice.setProcessinstid(processInstId);
      dmNotice.setStatus(0);
    }
    String noticeId = dmNotice.getNoticeId();
    if (!StringUtils.hasText(noticeId)) {
      noticeId = H_KeyWorker.nextIdToString();
      dmNotice.setNoticeId(noticeId);
    }
    imageService.saveDssImageForApp(dmNotice.getNoticeId(), dmNotice.getFileIds(), 8);
    saveOrUpdate(dmNotice);
    return getById(noticeId);
  }

  @Override public DmNotice getDefaultNotice() {
    DmNotice dmNotice = new DmNotice();
    String orgId = CustomRequestContextHolder.getOrgIdString();
    String orgEn = CustomRequestContextHolder.get("ORG_EN") == null ? ""
        : CustomRequestContextHolder.get("ORG_EN").toString();
    String currentDate = DateUtil.format(new Date(), "yyyyMMdd");

    String tempNoticeCode = "TZX-%s-%s-%s";
    String codePrefix = String.format("TZX-%s-%s-", orgEn, currentDate);
    Integer order = getBaseMapper().getCurrentDayMaxOrder(codePrefix, orgId);
    int index = order != null ? order : 0;
    String noticeCode = String.format(
        tempNoticeCode,
        orgEn,
        currentDate,
        StrUtil.padPre(String.valueOf(index + 1), 3, "0")
    );
    dmNotice.setNoticeCode(noticeCode);
    dmNotice.setNoticeDate(new Date());
    dmNotice.setMntOrgId(orgId);
    return dmNotice;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean deleteNoticeById(String noticeId) {
    DmNotice dmNotice = this.getById(noticeId);
    if (dmNotice == null) {
      throw new BaseException("找不到通知书");
    }
    //删除流程
    try {
      H_WorkFlowHelper.deleteProcessInstance(dmNotice.getProcessinstid());
    } catch (Exception e) {
      throw new BaseException("删除流程失败");
    }
    //删除主单
    removeById(dmNotice.getNoticeId());
    //删除明细
    LambdaQueryWrapper<DmNoticeDetail> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(DmNoticeDetail::getNoticeId, dmNotice.getNoticeId());
    detailService.remove(wrapper);
    //删除附件
    imageService.delByDssId(dmNotice.getNoticeId());
    return true;
  }

  @Override public Map<String, String> initAction() {
    return null;
  }

  @Override public String getProcessDefName() {
    return "gdcg.emdc.mems.dm.DmNoticeWorkFlow";
  }

  @Override
  public void savedoEvent(long processInstId, boolean isEnd, String nextAction, String actId) {
    if (isEnd) {
      DmNotice dmNotice = getNoticeByWorkFlowId(processInstId);
      if (dmNotice != null) {
        List<DmNoticeDetail> detailList = getNoticeDetailByNoticeId(dmNotice.getNoticeId());
        saveDssInfo(dmNotice, detailList);
        updateDmNoticeStatus(processInstId, 3, "");
      }
      return;
    }
    if (nextAction.equals("退回")) {
      updateDmNoticeStatus(processInstId, -1, "");
    } else if (actId.equals("manualActivity")) {
      updateDmNoticeStatus(processInstId, 1, "");
    } else if (actId.equals("manualActivity1")) {
      updateDmNoticeStatus(processInstId, 2, "FH");
    } else if (actId.equals("manualActivity2")) {
      updateDmNoticeStatus(processInstId, 3, "SH");
    }
  }

  private DmNotice getNoticeByWorkFlowId(Long workFlowId) {
    if (workFlowId == null) {
      throw new BaseException("无流程id");
    }
    LambdaQueryWrapper<DmNotice> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(DmNotice::getProcessinstid, workFlowId);
    return getOne(wrapper, false);
  }

  private List<DmNoticeDetail> getNoticeDetailByNoticeId(String noticeId) {
    if (!StringUtils.hasText(noticeId)) {
      throw new BaseException("无路政通知id");
    }
    LambdaQueryWrapper<DmNoticeDetail> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(DmNoticeDetail::getNoticeId, noticeId);
    return detailService.list(wrapper);
  }

  private void updateDmNoticeStatus(
      Long processInstId,
      Integer status,
      String type
  ) {
    String userCode = CustomRequestContextHolder.getUserCode();
    LambdaUpdateWrapper<DmNotice> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(processInstId != null, DmNotice::getProcessinstid, processInstId);
    wrapper.set(status != null, DmNotice::getStatus, status);
    if ("SH".equalsIgnoreCase(type)) {
      wrapper.set(StringUtils.hasText(userCode), DmNotice::getConfirmUser, userCode);
      wrapper.set(DmNotice::getConfirmTime, new Date());
    } else if ("FH".equalsIgnoreCase(type)) {
      wrapper.set(StringUtils.hasText(userCode), DmNotice::getSendUser, userCode);
      wrapper.set(DmNotice::getSendTime, new Date());
    }
    update(null, wrapper);
  }

  private void saveDssInfo(DmNotice dmNotice, List<DmNoticeDetail> records) {
    if (CollectionUtil.isEmpty(records)) {
      return;
    }
    Map<String, BaseStructDto> map = new HashMap<>();
    records.forEach(item -> {
      DssInfo ds = new DssInfo();
      BeanUtils.copyProperties(item, ds);
      //String id = H_KeyWorker.nextIdToString();
      ds.setDssId(item.getDssId());
      ds.setDssCode(item.getDssId());
      String structId = item.getStructId();
      String facilityCat = item.getFacilityCat();
      if (StringUtils.hasText(structId)) {
        BaseStructDto dto = null;
        if (map.containsKey(structId)) {
          dto = map.get(structId);
        } else {
          dto = getStructDto(facilityCat, structId);
          map.put(structId, dto);
        }
        ds.setRpIntrvlId(dto.getRpIntrvlId());
        ds.setRampId(dto.getRampId());
        ds.setStake(dto.getCntrStake());
        ds.setRlStakeNew(dto.getRlCntrStake());
        ds.setRoutecode(dto.getRouteCode());
        ds.setRouteversion(dto.getRouteVersion());
      } else {

        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth(dmNotice.getLineCode());
        //设置routeCode 随机获取一个route（未区分方向）
        if (CollectionUtil.isNotEmpty(routeCodes)) {
          ds.setRoutecode(routeCodes.stream().findFirst().get());
        }
      }
      ds.setDssImpFlag(0);
      ds.setRlStakeNew(item.getStake() != null ? item.getStake().doubleValue() : null);
      ds.setFindDssUserName(dmNotice.getCreateUserId());
      ds.setFacilityCat(facilityCat);
      ds.setStructId(structId);
      ds.setRelTaskCode(dmNotice.getNoticeId());
      ds.setDssSource(4);
      ds.setRepairStatus(0);
      ds.setDealStatus(0);
      ds.setFoundDate(dmNotice.getNoticeDate());
      ds.setMainRoadId(dmNotice.getLineCode());
      ds.setLinedirect(item.getLineDirect());
      dssInfoService.saveOrUpdate(ds);
    });
  }

  private BaseStructDto getStructDto(String facilityCat, String structId) {
    BaseStructDto structDto = null;
    IBaseStructFace iFacBase =
        CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
            .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
    if (iFacBase != null) {
      structDto = iFacBase.getId(structId);
    }
    return structDto;
  }

  @Override
  public List<OrderInfo> queryOrderInfo(long processInstId) {
    LambdaQueryWrapper<DmNotice> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(DmNotice::getProcessinstid, processInstId);
    DmNotice dmNotice = baseMapper.selectOne(queryWrapper);
    OrderInfo orderInfo = new OrderInfo();
    orderInfo.setOrderId(dmNotice.getNoticeId());
    orderInfo.setOrderType("TZS");
    orderInfo.setOrderCode(dmNotice.getNoticeCode());
    orderInfo.setStatus(dmNotice.getStatus());
    orderInfo.setOrgCode(dmNotice.getMntOrgId());
    return Arrays.asList(orderInfo);
  }
}
