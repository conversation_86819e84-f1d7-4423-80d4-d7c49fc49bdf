package com.hualu.app.module.mems.nminsp.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.entity.DmCodeSet;
import com.hualu.app.module.mems.nminsp.mapper.DmCodeSetMapper;
import com.hualu.app.module.mems.nminsp.service.DmCodeSetService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 自定义单号 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Api(tags = "自定义单号",description = "自定义单号 前端控制器")
@RestController
@RequestMapping("/dmCodeSet")
public class DmCodeSetController extends M_MyBatisController<DmCodeSet, DmCodeSetMapper> {

    @Autowired
    private DmCodeSetService dmCodeSetService;

    /**
     * 获取自定义单号列表
     * @return
     */
    @GetMapping("/list")
    public RestResult<List<DmCodeSet>> list(String range){
        LambdaQueryWrapper<DmCodeSet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmCodeSet::getIsDelete,0);
        queryWrapper.eq(DmCodeSet::getRange,range);
        queryWrapper.eq(DmCodeSet::getOrgCode, CustomRequestContextHolder.getOrgIdString());
        queryWrapper.orderByDesc(DmCodeSet::getCreateTime);
        List<DmCodeSet> list = dmCodeSetService.list(queryWrapper);
        return RestResult.success(list);
    }
}
