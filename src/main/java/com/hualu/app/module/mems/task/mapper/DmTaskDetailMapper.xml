<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.task.mapper.DmTaskDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.task.entity.DmTaskDetail">
        <id column="MTASK_DTL_ID" property="mtaskDtlId" />
        <result column="MTASK_ID" property="mtaskId" />
        <result column="DSS_ID" property="dssId" />
        <result column="MPITEM_ID" property="mpitemId" />
        <result column="MEASURE_UNIT" property="measureUnit" />
        <result column="MPITEM_ACCOUNT" property="mpitemAccount" />
        <result column="IS_LUMP" property="isLump" />
        <result column="REMARK" property="remark" />
        <result column="ACCEPT_STATUS" property="acceptStatus" />
        <result column="IS_ADD" property="isAdd" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MTASK_DTL_ID, MTASK_ID, DSS_ID, MPITEM_ID, MEASURE_UNIT, MPITEM_ACCOUNT, IS_LUMP, REMARK, ACCEPT_STATUS, IS_ADD
    </sql>
    <update id="updateStatus">
        update dm_task_detail t
        set t.accept_status=0
        WHERE t.mtask_dtl_id in (select distinct d.mtask_dtl_id
        from dm_task_accpt_detail d
        where d.mtask_accpt_dtl_id in
        <foreach collection="accptDtlIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>)
    </update>
    <select id="selectDetailsByTaskId" resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto">
        select d.found_date,
               d.lane,
               a.UNIT_PRICE,
               to_number(to_char(a.UNIT_PRICE * a.MPITEM_ACCOUNT, '*********.00')) AS money,
               gl.line_id,
               gl.line_code,
               gl.LINE_ALLNAME || '(' || gl.LINE_CODE || ')'            as line_name,
               d.linedirect                                             as line_direct,
               d.rl_stake_new                                           as rl_stake,
               a.*,
               d.*,
               dt.dss_type_name
        from (
                 select distinct t.*, mp.MPITEM_NAME, MMP.MMP_CODE, MMP.UNIT_PRICE,MMP.CONTR_ID
                 from DM_TASK_DETAIL t
                          left join MPC_MPITEM mp on t.MPITEM_ID = mp.MPITEM_ID
                          left join dm_task dt on dt.mtask_id = t.mtask_id
                          left join MPC_MPITEM_PRICE mmp on mmp.contr_id = dt.contr_id
                     and mmp.mp_item_id = mp.MPITEM_ID
                          left join memsdb.dm_task_accpt_detail ac
                                    on t.mtask_dtl_id = ac.mtask_dtl_id
                          left join memsdb.dm_task_accpt pt on ac.mtask_accpt_id = pt.mtask_accpt_id
                 where t.MTASK_ID = #{dmTaskId}
             ) a,
             DSS_INFO d,
             DSS_TYPE_NEW dt,
             GDGS.V_BASE_LINE gl
        where a.DSS_ID = d.DSS_ID
          and d.DSS_TYPE = dt.DSS_TYPE
          and decode(d.ramp_id, d.ramp_id, d.MAIN_ROAD_ID) = gl.line_id
        order by gl.line_id, d.linedirect, d.struct_id, d.dss_type, d.stake, a.mmp_code
    </select>


    <select id="selectDssNumAndMoney" resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto">
        select dss_id,CONTR_ID, count(1) as mp_num, sum(MONEY) as money
        from (select distinct dtd.dss_id,dt.CONTR_ID, to_number(to_char(MMP.UNIT_PRICE * dtd.MPITEM_ACCOUNT, '*********.00')) AS money
              from DM_TASK_DETAIL dtd
                       join DM_TASK dt on dt.MTASK_ID = dtd.MTASK_ID
                       join MPC_MPITEM_PRICE mmp on dtd.MPITEM_ID = mmp.MP_ITEM_ID and dt.CONTR_ID = mmp.CONTR_ID
              where dtd.MTASK_ID = #{mtaskId})
        group by DSS_ID,CONTR_ID
    </select>
    <select id="selectDssInfos" resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto">
        select DISTINCT *
        from (SELECT td.dss_id,
                     di.STRUCT_ID,
                     di.MAIN_ROAD_ID,
                     nvl(di.RL_STAKE_NEW, di.STAKE)     as RL_STAKE_NEW,
                     di.RL_FINISH_STAKE,
                     nvl(di.FACILITY_CAT, 'LM')         as FACILITY_CAT,
                     di.DSS_TYPE,
                     di.LANE,
                     nvl(di.LINEDIRECT, null) as LINEDIRECT,
                     nvl(dt.dss_type_name, '通用保洁')  as DSS_TYPE_NAME
              FROM MEMSDB.DM_TASK_DETAIL td
                       LEFT JOIN MEMSDB.DSS_INFO di ON td.DSS_ID = di.DSS_ID
                       LEFT JOIN memsdb.DSS_TYPE_NEW dt ON di.DSS_TYPE = dt.DSS_TYPE
                  ${ew.customSqlSegment}
              order by di.MAIN_ROAD_ID, di.STAKE)
    </select>
    <select id="selectMpItems" resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto">
        select distinct dtd.MTASK_DTL_ID,mmp.MPITEM_CODE,dtd.DSS_ID,
               mmp.MPITEM_NAME,
               mmp.UNIT_PRICE,
               dtd.MPITEM_ACCOUNT,
               mmp.MEASURE_UNIT,
               to_number(to_char(MMP.UNIT_PRICE * dtd.MPITEM_ACCOUNT, '*********.00')) AS money
        from DM_TASK_DETAIL dtd
                 join MPC_MPITEM_PRICE mmp
                      on dtd.MPITEM_ID = mmp.MP_ITEM_ID
        where dtd.MTASK_ID = #{mtaskId}
          and dtd.DSS_ID = #{dssId}
          and mmp.CONTR_ID = #{contrId}
        order by TO_NUMBER(translate (mmp.MPITEM_CODE, '#' || translate (mmp.MPITEM_CODE, '**********', '#'), '/'))
    </select>
    <select id="selectMpItemsByDssIds"
            resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto">
        select distinct dtd.MTASK_DTL_ID,mmp.MPITEM_CODE,dtd.DSS_ID,
               mmp.MPITEM_NAME,
               mmp.UNIT_PRICE,
               dtd.MPITEM_ACCOUNT,
               mmp.MEASURE_UNIT,
               to_number(to_char(MMP.UNIT_PRICE * dtd.MPITEM_ACCOUNT, '*********.00')) AS money
        from DM_TASK_DETAIL dtd
                 join MPC_MPITEM_PRICE mmp
                      on dtd.MPITEM_ID = mmp.MP_ITEM_ID
            ${ew.customSqlSegment}
        order by dtd.dss_id,TO_NUMBER(translate (mmp.MPITEM_CODE, '#' || translate (mmp.MPITEM_CODE, '**********', '#'), '/'))
    </select>
    <select id="selectNoAccpt" resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto">
        select dtd.*,d.*,dt.contr_id from DM_TASK_DETAIL dtd join dm_task dt on dtd.mtask_id = dt.mtask_id
            left join DSS_INFO d on dtd.DSS_ID = d.DSS_ID ${ew.customSqlSegment}
    </select>
    <select id="selectDssInfosByAccpt"
            resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto">
        select DISTINCT * from (SELECT
                                    td.dss_id,
                                    di.STRUCT_ID,
                                    td.MAIN_ROAD_ID,
                                    nvl(di.RL_STAKE_NEW,td.STAKE) as RL_STAKE_NEW,
                                    di.RL_FINISH_STAKE,
                                    nvl(di.FACILITY_CAT,'LM') as FACILITY_CAT,
                                    di.DSS_TYPE,
                                    td.LANE,
                                    nvl(di.LINEDIRECT,td.LINE_DIRECT) as LINEDIRECT,
                                    nvl(dt.dss_type_name,'通用保洁') as DSS_TYPE_NAME
                                FROM
                                    MEMSDB.DM_TASK_ACCPT_DETAIL td
                                        LEFT JOIN MEMSDB.DSS_INFO di ON td.DSS_ID = di.DSS_ID
                                        LEFT JOIN memsdb.DSS_TYPE_NEW dt ON di.DSS_TYPE = dt.DSS_TYPE
                                    ${ew.customSqlSegment}
                                order by td.MAIN_ROAD_ID,td.STAKE)
    </select>
</mapper>
