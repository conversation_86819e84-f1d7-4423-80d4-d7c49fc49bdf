package com.hualu.app.module.mems.task.dto.taskdetail;

import lombok.Data;

import java.io.Serializable;

/**
 * 任务单措施信息
 */
@Data
public class DmTaskMpItemViewDto implements Serializable {

    //任务单明细ID
    private String mtaskDtlId;
    //验收单单明细ID
    private String mtaskAccptDtlId;
    //病害ID
    private String dssId;
    //措施编码
    private String mpitemCode;
    //措施名称
    private String mpitemName;
    //勘估数量(任务单数量)
    private Double mpitemAccount;
    //单位
    private String measureUnit;
    //措施单价
    private Double unitPrice;
    //勘估数量（单位）
    private String mpitemAccountName;
    //措施总额（任务单金额）
    private Double money;

    //申请验收数量
    private Double applyAmount;
    //验收数量
    private Double acceptAmount;
    //措施总额（验收单金额）
    private Double acceptMoney;

    //工程数量
    private Double mpitemNum;

    // 是否来源任务单 1：任务单 其他：否
    private Integer isTaskRecord;

}
