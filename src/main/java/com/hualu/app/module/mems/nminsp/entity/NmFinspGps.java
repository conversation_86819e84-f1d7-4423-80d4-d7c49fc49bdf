package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 新版经常检查轨迹 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-03-28
 */
@JsonIgnoreProperties(value = {"createTime","updateTime","remark","delFlag","height","gpsId"})
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmFinspGps对象", description="新版经常检查轨迹")
public class NmFinspGps implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId("GPS_ID")
    private String gpsId;

    @ApiModelProperty(value = "巡查单ID")
    @TableField("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "高度")
    @TableField("HEIGHT")
    private Double height;

    @ApiModelProperty(value = "速度")
    @TableField("SPEED")
    private Double speed;

    @ApiModelProperty(value = "经度")
    @TableField("LON")
    private Double lon;

    @ApiModelProperty(value = "纬度")
    @TableField("LAT")
    private Double lat;

    @ApiModelProperty(value = "时间")
    @TableField("TIME")
    private LocalDateTime time;

    @ApiModelProperty(value = "巡查人")
    @TableField("INSP_PERSON")
    private String inspPerson;

    @ApiModelProperty(value = "轨迹id（检查单有时有多段轨迹）")
    @TableField("TRACK_ID")
    private String trackId;

    @ApiModelProperty(value = "备注（存储版本信息）")
    @TableField("REMARK")
    private String remark;

    @TableLogic
    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;


}
