package com.hualu.app.module.mems.dinsp.service.impl;

import com.hualu.app.module.mems.dinsp.entity.DmDinResult;
import com.hualu.app.module.mems.dinsp.entity.DmDinspRecord;
import com.hualu.app.module.mems.dinsp.mapper.DmDinResultMapper;
import com.hualu.app.module.mems.dinsp.service.DmDinResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.mems.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
public class DmDinResultServiceImpl extends ServiceImpl<DmDinResultMapper, DmDinResult> implements DmDinResultService {

    @Autowired
    private BaseDatathirdDicService dicService;

    @Override
    public void updateResult(DmDinResult result, List<DmDinspRecord> records) {
        StringBuilder inspections = new StringBuilder();
        for(Iterator<DmDinspRecord> iterator = records.iterator(); iterator.hasNext();){
            DmDinspRecord record = iterator.next();
            String facilityCat = record.getFacilityCat();
            if("LM".equals(facilityCat)){
                inspections.append(record.getLaneName()).append(" ")
                        .append(StringUtil.getDisplayStake(record.getStake())).append(":")
                        .append(record.getDssTypeName());
            }else if("FWQ".equals(facilityCat) || "SFZ".equals(facilityCat)){
                inspections.append(record.getStructName()).append("【").append(record.getStructPartName())
                        .append("】").append(record.getDssTypeName());
            }else if("BP".equals(facilityCat) || "SD".equals(facilityCat) || "HD".equals(facilityCat)
                    || "QL".equals(facilityCat)){
                inspections.append(record.getStructName()).append(":").append(record.getDssTypeName());
            }else if("FJGC".equals(facilityCat)){
                inspections.append(record.getStructName()).append(":").append(record.getDssTypeName());
            }else{
                String linename = "4".equals(record.getLineDirect()) ? "匝道" : record.getLineName();
                inspections.append(linename).append(" ").append(StringUtil.getDisplayStake(record.getStake()))
                        .append(":").append(record.getDssTypeName());
            }
            if(iterator.hasNext()){
                inspections.append(",");
            }
            result.setInspections(inspections.toString());
            this.baseMapper.updateById(result);
        }
    }
}
