<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinspResult">
        <id column="FINSP_RES_ID" property="finspResId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="FINSP_ITEM_ID" property="finspItemId" />
        <result column="FINSP_DESC" property="finspDesc" />
        <result column="FINSP_RESULT" property="finspResult" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FINSP_RES_ID, FINSP_ID, FINSP_ITEM_ID, FINSP_DESC, FINSP_RESULT
    </sql>
    <select id="listRecordWord" resultType="com.hualu.app.module.mems.finsp.dto.word.PcFinspRecordWordDto">
        select a.FINSP_ID,b.insp_com, b.insp_cont, a.FINSP_RESULT, a.FINSP_DESC
        from PC_FINSP_RESULT a  join PC_FINSP_ITEM b on a.FINSP_ITEM_ID = b.FINSP_ITEM_ID
        join PC_FINSP c on a.finsp_id = c.finsp_id
        join PC_PROJECT_SCOPE d on c.struct_id = d.struct_id
        where 1=1 and a.del_flag=0 and c.del_flag=0 and d.del_flag=0 and c.mnt_type in (1,2)
        <if test="finspIds != null and finspIds.size > 0">
            and a.FINSP_ID in
            <foreach collection="finspIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="prjId != null">
            and d.prj_id = #{prjId}
        </if>
    </select>

</mapper>
