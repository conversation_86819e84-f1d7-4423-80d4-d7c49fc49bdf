package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 新版日常巡查记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface NmDinspRecordMapper extends BaseMapper<NmDinspRecord> {

    List<NmDinspRecord> selectViewRecord(@Param("dssIds") List<String> dssIds);

    List<NmDinspRecord> selectRecords(@Param("dinspCommon") NmDinspCommon dinspCommon);
}
