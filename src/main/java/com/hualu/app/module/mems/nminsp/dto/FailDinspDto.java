package com.hualu.app.module.mems.nminsp.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class FailDinspDto implements Serializable {

    private String commonDinspId;

    private String facilityCat;

    private String createUserId;

    private String createInspPerson;

    private String createOrg;

    private String createUserCode;

    private String createUserName;

    private String comUserId;

    private String comUserCode;

    private String comUserName;

    private String comInspPerson;

    private String comOrg;
}
