package com.hualu.app.module.mems.mpc.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.mpc.dto.MpcItemDto;
import com.hualu.app.module.mems.mpc.entity.MpcMpitem;
import com.hualu.app.module.mems.mpc.mapper.MpcMpitemMapper;
import com.hualu.app.module.mems.mpc.service.MpcMpitemService;
import com.hualu.app.utils.H_RestResultHelper;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 *  养护工程条目
 * <AUTHOR>
 * @since 2023-07-03
 */
@RestController
@RequestMapping("/mpcMpitem")
public class MpcMpitemController extends M_MyBatisController<MpcMpitem, MpcMpitemMapper> {

    @Autowired
    MpcMpitemService itemService;

    /**
     * 分页查询
     * @return
     */
    @PostMapping("selectPage")
    public RestResult<List<MpcItemDto>> selectPage(){
        Map<String,String> reqParam = getReqParam();
        Page page = getPage();
        List<MpcMpitem> mpcMpitems = itemService.selectMpitems(page, reqParam.get("contrId"), reqParam.get("dssType"), reqParam.get("mpitemName"));
        page.setRecords(mpcMpitems);
        return H_RestResultHelper.returnPage(page);
    }

    /**
     * 查询养护措施（维修工程记录）
     * @return
     */
    @PostMapping("selectMpitemsByDssId")
    public RestResult<List<MpcMpitem>> selectMpitemsByDssId(String dssId,String dssType,String contrId,String mpitemName){
        List<MpcMpitem> mpcMpitems = itemService.selectMpitemsByDssId(dssId,dssType, contrId, mpitemName);
        return RestResult.success(mpcMpitems);
    }
}
