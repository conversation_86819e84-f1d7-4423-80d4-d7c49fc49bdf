package com.hualu.app.module.mems.notice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.notice.entity.DmNoticeDetail;
import org.springframework.web.multipart.MultipartFile;

public interface DmNoticeDetailService extends IService<DmNoticeDetail> {

  IPage<DmNoticeDetail> getNoticeList(IPage<DmNoticeDetail> p, String noticeId);

  boolean deleteNoticeDetailById(String noticeDetailId);

  DmNoticeDetail getRoadNoticeDetail(String noticeDetailId);

  boolean saveOrUpdateNoticeDetail(DmNoticeDetail dmNoticeDetail, MultipartFile[] files);
}
