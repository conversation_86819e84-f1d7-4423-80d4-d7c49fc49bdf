<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.notice.mapper.DmNoticeMapper">

  <select id="getRoadNoticeList" resultType="com.hualu.app.module.mems.notice.entity.DmNotice">
    select *
    from (select t.*,
            d.mtask_code                                  as taskCode,
            decode(dnd.total_Num, null, 0, dnd.total_Num) as totalNum,
            decode(a1.accpt_Num, null, 0, a1.accpt_Num)   as acceptNum,
            decode(a2.undo_Num, null, 0, a2.undo_Num)     as undoNum,
            (select u.USER_NAME from gdgs.FW_RIGHT_USER u where u.USER_CODE = t.SEND_USER and ROWNUM = 1) sendUserName,
            (select u.USER_NAME from gdgs.FW_RIGHT_USER u where u.USER_CODE = t.CONFIRM_USER and ROWNUM = 1) confirmUserName,
            (select l.LINE_ALLNAME || '(' || l.LINE_CODE || ')' from GDGS.V_BASE_LINE l where l.IS_ENABLE = 1 and l.IS_DELETED = 0 and l.LINE_ID = t.LINE_CODE and ROWNUM = 1) lineName
          from MEMSDB.DM_NOTICE t
                 left join (select dt.notice_id, count(1) as total_Num
                            from MEMSDB.dm_notice_detail dt
                            group by dt.notice_id) dnd on t.notice_id = dnd.notice_id
                 left join (select dt.notice_id, count(1) as accpt_Num
                            from MEMSDB.dm_notice_detail dt
                                   left join memsdb.dss_info io on io.dss_id = dt.dss_id
                            where io.repair_status = 2
                            group by dt.notice_id) a1 on t.notice_id = a1.notice_id
                 left join (select dt.notice_id, count(1) as undo_Num
                            from MEMSDB.dm_notice_detail dt
                                   left join memsdb.dss_info io on io.dss_id = dt.dss_id
                            where io.repair_status != 2
    and (io.DEAL_MEASURE = 5 or io.DEAL_MEASURE = 0 or io.DEAL_MEASURE = 6)
    group by dt.notice_id) a2 on t.notice_id = a2.notice_id
    left join MEMSDB.dm_task d on d.mtask_id = t.mtask_id) m
    <where>
      ${ew.sqlSegment}
    </where>
  </select>

  <select id="getCurrentDayMaxOrder" resultType="java.lang.Integer">
    select max(to_number(nvl(regexp_substr(NOTICE_CODE, '\d+', 1, 2), 0)))
    from MEMSDB.DM_NOTICE d
    where d.MNT_ORG_ID = #{orgId}
      and NOTICE_CODE like concat(#{prefix}, '%')
  </select>
</mapper>