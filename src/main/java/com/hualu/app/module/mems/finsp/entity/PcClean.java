package com.hualu.app.module.mems.finsp.entity;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.hualu.app.module.mongo.constant.C_Constant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 打草清疏 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcClean对象", description="打草清疏")
public class PcClean implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableField("CLEAN_ID")
    private String cleanId;

    @NotBlank(message = "结构物名称不能为空")
    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @NotBlank(message = "结构物桩号不能为空")
    @ApiModelProperty(value = "结构物桩号")
    @TableField("STRUCT_STAKE")
    private String structStake;

    @NotBlank(message = "结构物层级不能为空")
    @ApiModelProperty(value = "结构物层级")
    @TableField("STRUCT_LEVEL")
    private String structLevel;

    @ApiModelProperty(value = "管理单位")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "清疏日期")
    @TableField("INSP_DATE")
    private Date inspDate;

    @NotNull(message = "清理人数不能为空")
    @ApiModelProperty(value = "清理人数")
    @TableField("INSP_PERSON_NUM")
    private Integer inspPersonNum;

    @NotBlank(message = "清理内容不能为空")
    @ApiModelProperty(value = "清理内容")
    @TableField("CLEAN_CONTENT")
    private String cleanContent;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableLogic
    @ApiModelProperty(value = "是否删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;


    /**
     * 照片类型(病害照片：1:处治前,2:处治中,3:处治后)
     */
    @TableField(exist = false)
    private List<PcFinspImage> finspImages;

    @TableField(exist = false)
    private String imageHost = C_Constant.IMAGE_HOST;
}
