package com.hualu.app.module.mems.payment.mapper;

import com.hualu.app.module.mems.autoInspector.entity.DownloadVo;
import com.hualu.app.module.mems.payment.entity.MeterageReportLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【METERAGE_REPORT_LOG(上传报表日志)】的数据库操作Mapper
* @createDate 2025-06-19 08:09:51
* @Entity com.hualu.app.module.mems.payment.entity.entity.MeterageReportLog
*/
public interface MeterageReportLogMapper extends BaseMapper<MeterageReportLog> {

    List<DownloadVo> getDssImageList(String code);
}




