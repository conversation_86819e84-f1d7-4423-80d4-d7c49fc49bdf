package com.hualu.app.module.mems.comm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseStructComp对象", description="")
public class BaseStructComp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("STRUCT_COMP_ID")
    private String structCompId;

    @TableField("STRUCT_COMP_CODE")
    private String structCompCode;

    @TableField("STRUCT_COMP_NAME")
    private String structCompName;

    @TableField("P_STRUCT_COMP_ID")
    private String pStructCompId;

    @TableField("ASSET_COMP_TYPE")
    private String assetCompType;

    @TableField("PART_CODE")
    private String partCode;

    @TableField(exist = false)
    private String dssType;

    public BaseStructComp() {
    }

    public BaseStructComp(String structCompId, String structCompCode, String structCompName, String pStructCompId) {
        this.structCompId = structCompId;
        this.structCompCode = structCompCode;
        this.structCompName = structCompName;
        this.pStructCompId = pStructCompId;
    }
}
