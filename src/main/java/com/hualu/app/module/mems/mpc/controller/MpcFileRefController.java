package com.hualu.app.module.mems.mpc.controller;


import com.hualu.app.module.mems.mpc.service.MpcFileRefService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

@Api("检查工照")
@RestController
@RequestMapping("/mpcFileRef")
public class MpcFileRefController {

    @Autowired
    MpcFileRefService refService;

    @ApiOperation("根据经常检查单ID，查询检查附件")
    @GetMapping("loadFilesByFinspId/{finspId}")
    public Object loadFilesByFinspId(@PathVariable("finspId") String finspId){
        return refService.listFileByBillId(finspId);
    }
}
