package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspDsstype;
import com.hualu.app.module.mems.finsp.mapper.PcFinspDsstypeMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspDsstypeService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 结构物排查病害表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Service
public class PcFinspDsstypeServiceImpl extends ServiceImpl<PcFinspDsstypeMapper, PcFinspDsstype> implements PcFinspDsstypeService {

    @Override
    public List<PcFinspDssTypeDto> listByPartId(String partId) {
        List<PcFinspDssTypeDto> dsstypeList = baseMapper.recursionChilds(partId);
        if (CollectionUtil.isEmpty(dsstypeList)){
            return Collections.emptyList();
        }
        Map<String, List<PcFinspDssTypeDto>> pidMap = dsstypeList.stream().collect(Collectors.groupingBy(PcFinspDssTypeDto::getPid));

        // 个数为1时，表示坡体和检修道
        if (pidMap.size() == 1){
            List<PcFinspDssTypeDto> childList = pidMap.get(partId);
            return childList;
        }else {
            // 排除部件本身
            dsstypeList = dsstypeList.stream().filter(e->!(e.getPid().equals(partId)) ).collect(Collectors.toList());
        }
        return dsstypeList;
    }

    @Override
    public List<PcFinspDsstype> listPart(String facilityCat) {
        LambdaQueryWrapper<PcFinspDsstype> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspDsstype::getFacilityCat,facilityCat);
        queryWrapper.eq(PcFinspDsstype::getType,"BJ");
        return list(queryWrapper);
    }

    @Override
    public List<PcFinspDssTypeDto> listByDsstypeIds(List<String> dsstypeIds) {
        List<PcFinspDssTypeDto> typeDtos = baseMapper.listByDsstypeIds(dsstypeIds);
        return typeDtos;
    }
}
