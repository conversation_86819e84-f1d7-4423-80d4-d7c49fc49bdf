package com.hualu.app.module.mems.mpc.mapper;

import com.hualu.app.module.mems.mpc.dto.MpitemSystemDto;
import com.hualu.app.module.mems.mpc.entity.MpcDssMeasure;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
public interface MpcDssMeasureMapper extends BaseMapper<MpcDssMeasure> {

    List<MpitemSystemDto> fetchMpitemSystems(@Param("year") Integer year);

    List<String> listDssType(@Param("orgId") String orgId,@Param("mpSystemId") String mpSystemId);
}
