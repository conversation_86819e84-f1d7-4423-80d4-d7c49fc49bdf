<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmFinspResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmFinspResult">
        <id column="RES_ID" property="resId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="ITEM_ID" property="itemId" />
        <result column="ISSUE_DESC" property="issueDesc" />
        <result column="DEAL_RESULT" property="dealResult" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RES_ID, FINSP_ID, ITEM_ID, ISSUE_DESC, DEAL_RESULT, REMARK
    </sql>
    <select id="selectByFinspId" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinspResult">
        SELECT
            B.ITEM_ID,
            B.FINSP_ID,
            B.RES_ID,
            B.ISSUE_DESC,
            B.DEAL_RESULT,
            A.DSS_TYPE,
            A.XC_TYPE,
            A.PART_ID,
            A.INSP_CONT,
            A.INSP_COM,
            b.GENERAL_ABNORMALITY,
            b.SERIOUS_ABNORMALITY,
            b.TRACKING_MONITORING,
            b.REPAIR_TREATMENT,
            b.REGULAR_INSPECTION
        FROM
            NM_INSP_ITEM A join NM_FINSP_RESULT B
                                ON A.ITEM_ID = B.ITEM_ID and b.FINSP_ID = #{finspId} and A.IS_DELETE = 0 and B.DEL_FLAG=0
        order by TO_NUMBER(REGEXP_REPLACE(A.ITEM_CODE, '[^0-9]', ''))
    </select>
    <select id="listByFinspIds" resultType="com.hualu.app.module.mems.nminsp.entity.NmFinspResult">
        SELECT
            B.ITEM_ID,
            B.FINSP_ID,
            B.RES_ID,
            B.ISSUE_DESC,
            B.DEAL_RESULT,
            A.DSS_TYPE,
            A.XC_TYPE,
            A.PART_ID,
            A.INSP_CONT,
            A.INSP_COM,
            B.REMARK,
            A.INSP_COM,
            b.GENERAL_ABNORMALITY,
            b.SERIOUS_ABNORMALITY,
            b.TRACKING_MONITORING,
            b.REPAIR_TREATMENT,
            b.REGULAR_INSPECTION
        FROM
            NM_INSP_ITEM A join NM_FINSP_RESULT B
            ON A.ITEM_ID = B.ITEM_ID and b.FINSP_ID in
            <foreach collection="finspIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            and A.IS_DELETE = 0 and B.DEL_FLAG=0
        order by b.FINSP_ID,TO_NUMBER(REGEXP_REPLACE(A.ITEM_CODE, '[^0-9]', ''))
    </select>

    <select id="findNmFinspResult" resultMap="BaseResultMap">
        select * from MEMSDB.NM_FINSP_RESULT f where FINSP_ID in (#{finspId})
        and f.DEL_FLAG=0
    </select>
</mapper>
