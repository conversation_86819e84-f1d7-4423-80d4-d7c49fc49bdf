package com.hualu.app.module.mems.autoInspector.service;

import com.hualu.app.module.mems.autoInspector.entity.HighwayVehicleDevice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.alibaba.fastjson.JSON;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_VEHICLE_DEVICE(车辆设备状态信息表)】的数据库操作Service
* @createDate 2025-04-27 09:02:29
*/
public interface HighwayVehicleDeviceService extends IService<HighwayVehicleDevice> {
    
    /**
     * 同步并保存设备数据
     * @param json API返回的JSON数据
     * @return 成功同步的数据总数
     */
    int syncAndSaveDeviceData(JSON json);
}
