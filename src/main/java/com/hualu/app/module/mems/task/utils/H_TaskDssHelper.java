package com.hualu.app.module.mems.task.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.mapper.DmTaskDetailMapper;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.assertj.core.util.Lists;

public class H_TaskDssHelper {

    public List<DmTaskDssViewDto> initDss(List<String> dssIds,String contrId){
        List<DmTaskDssViewDto> dssDtos = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(dssIds)){
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.in("di.dss_id",dssIds);

            DmTaskDetailMapper taskDetailMapper = CustomApplicationContextHolder.getBean(DmTaskDetailMapper.class);
            dssDtos = taskDetailMapper.selectDssInfos(queryWrapper);
        }
        /*if (CollectionUtil.isNotEmpty(dssDtos)){
            setViewInfo(dssDtos);
            LinkedHashMap<String, List<DmTaskMpItemViewDto>> mpMap = selectMpItemsByDssIds(contrId, dmTaskId, dssIds);

            //设置签到回显值
            Map<String, DmTaskDssViewDto> dssMap = dssDtos.stream().collect(Collectors.toMap(DmTaskDssViewDto::getDssId, Function.identity()));
            dtos.forEach(item->{
                DmTaskDssViewDto dssDto = dssMap.get(item.getDssId());
                BeanUtil.copyProperties(dssDto,item,"mpNum","money","contrId");
                List<DmTaskMpItemViewDto> mpItemViewDtos = mpMap.get(item.getDssId());
                item.setMpItemViewDtos(mpItemViewDtos);
            });
        }*/
        return null;
    }

    /**
     * 设置前端页面回显值
     * @param dtos
     */
    public static void setViewInfo(List<DmTaskDssViewDto> dtos, Map<String, List<DssImage>> imageMap){
        dtos.forEach(item->{
            if (StrUtil.isNotBlank(item.getStructId())){
                BaseStructDto structDto = H_BasedataHepler.getStructDto(item.getStructId(), item.getFacilityCat());
                item.setStructName(structDto != null ? structDto.getStructName() : "");
            }
            item.setLineDirectName(H_BasedataHepler.directMap.get(item.getLinedirect()));
            item.setFacilityCatName(H_BasedataHepler.getDicName("FACILITY_CAT",item.getFacilityCat()));
            item.setLaneName(H_BasedataHepler.getDicName("LANE",item.getLane()));
            item.setLineName(H_BasedataHepler.getLineName(item.getMainRoadId()));
            if (item.getRlStakeNew() != null) {
                item.setStakeName(H_StakeHelper.convertCnStake(item.getRlStakeNew().toString()));
            }

            //设置病害位置信息
            if (StrUtil.isNotBlank(item.getStructName())){
                item.setPositionName(item.getStructName());
            }else if (StrUtil.isNotBlank(item.getLaneName())){
                item.setPositionName(item.getLaneName());
            }else {
                item.setPositionName(item.getLineDirectName());
            }

            //设置病害照片
            List<DssImage> images = imageMap.get(item.getDssId());
            if (CollectionUtil.isNotEmpty(images)){
                images.forEach(row->{
                    //病害照片为空时，表示修复前照片
                    if (ObjectUtil.isEmpty(row.getProcessing())){
                        row.setProcessing(0);
                    }
                });
                Map<Integer, List<DssImage>> processMap = images.stream().collect(Collectors.groupingBy(DssImage::getProcessing));
                //维修后照片
                List<DssImage> afterImages = processMap.get(1);
                if (afterImages != null){
                    String afterFileIds = afterImages.stream().map(DssImage::getFileId).collect(Collectors.joining(","));
                    item.setAfterFileIds(afterFileIds);
                }
                //维修前照片
                List<DssImage> preImages = processMap.get(0);
                if (preImages != null){
                    String fileIds = preImages.stream().map(DssImage::getFileId).collect(Collectors.joining(","));
                    item.setFileIds(fileIds);
                }
                item.setImageHost(C_Constant.IMAGE_HOST);
            }
        });
    }
}
