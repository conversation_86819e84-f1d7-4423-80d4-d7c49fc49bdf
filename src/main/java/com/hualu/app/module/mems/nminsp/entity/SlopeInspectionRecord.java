package com.hualu.app.module.mems.nminsp.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

public class SlopeInspectionRecord {
    // 路线信息
    private String routeName;          // 路线名称
    private String lineCode;           // 线路代码

    // 边坡信息
    private String slopeName;          // 边坡名称
    private Double slopeLength;        // 边坡长度
    private String slopeLevel;         // 边坡等级
    private String slopeTcGrade;       // 边坡稳定性分级（转换后）

    // 检查信息
    private String dssNums;            // 病害点数量（0转换为'无'）
    private String dinspCode;          // 检查记录编码
    private String inspFrequency;     // 检查频率
    private Date inspDate;    // 检查日期

    // 详情与建议
    private String dssDetail;          // 病害详情（位置+描述）
    private String mntnAdvice;         // 维护建议,
    private String orgFullname;
    private String remark;

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getSlopeName() {
        return slopeName;
    }

    public void setSlopeName(String slopeName) {
        this.slopeName = slopeName;
    }

    public Double getSlopeLength() {
        return slopeLength;
    }

    public void setSlopeLength(Double slopeLength) {
        this.slopeLength = slopeLength;
    }

    public String getSlopeLevel() {
        return slopeLevel;
    }

    public void setSlopeLevel(String slopeLevel) {
        this.slopeLevel = slopeLevel;
    }

    public String getSlopeTcGrade() {
        return slopeTcGrade;
    }

    public void setSlopeTcGrade(String slopeTcGrade) {
        this.slopeTcGrade = slopeTcGrade;
    }

    public String getDssNums() {
        return dssNums;
    }

    public void setDssNums(String dssNums) {
        this.dssNums = dssNums;
    }

    public String getDinspCode() {
        return dinspCode;
    }

    public void setDinspCode(String dinspCode) {
        this.dinspCode = dinspCode;
    }

    public String getInspFrequency() {
        return inspFrequency;
    }

    public void setInspFrequency(String inspFrequency) {
        this.inspFrequency = inspFrequency;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getDssDetail() {
        return dssDetail;
    }

    public void setDssDetail(String dssDetail) {
        this.dssDetail = dssDetail;
    }

    public String getMntnAdvice() {
        return mntnAdvice;
    }

    public void setMntnAdvice(String mntnAdvice) {
        this.mntnAdvice = mntnAdvice;
    }

    @Override
    public String toString() {
        return "SlopeInspectionRecord{" +
                "routeName='" + routeName + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", slopeName='" + slopeName + '\'' +
                ", slopeLength=" + slopeLength +
                ", slopeLevel='" + slopeLevel + '\'' +
                ", slopeTcGrade='" + slopeTcGrade + '\'' +
                ", dssNums='" + dssNums + '\'' +
                ", dinspCode='" + dinspCode + '\'' +
                ", inspFrequency='" + inspFrequency + '\'' +
                ", inspDate=" + inspDate +
                ", dssDetail='" + dssDetail + '\'' +
                ", mntnAdvice='" + mntnAdvice + '\'' +
                '}';
    }

    public String getOrgFullname() {
        return orgFullname;
    }

    public void setOrgFullname(String orgFullname) {
        this.orgFullname = orgFullname;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
