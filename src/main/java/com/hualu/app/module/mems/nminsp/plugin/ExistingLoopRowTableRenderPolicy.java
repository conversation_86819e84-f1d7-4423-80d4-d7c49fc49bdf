package com.hualu.app.module.mems.nminsp.plugin;
/*
 * Copyright 2014-2024 Sayi
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import cn.hutool.core.util.StrUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.exception.RenderException;
import com.deepoove.poi.policy.RenderPolicy;
import com.deepoove.poi.render.compute.EnvModel;
import com.deepoove.poi.render.compute.RenderDataCompute;
import com.deepoove.poi.render.processor.DocumentProcessor;
import com.deepoove.poi.render.processor.EnvIterator;
import com.deepoove.poi.resolver.TemplateResolver;
import com.deepoove.poi.template.ElementTemplate;
import com.deepoove.poi.template.MetaTemplate;
import com.deepoove.poi.template.run.RunTemplate;
import com.deepoove.poi.util.ReflectionUtils;
import com.deepoove.poi.util.TableTools;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.apache.xmlbeans.XmlObject;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;

import java.util.Iterator;
import java.util.List;

/**
 * Hack for loop table row
 *
 * <AUTHOR>
 */
public class ExistingLoopRowTableRenderPolicy implements RenderPolicy {

    private String prefix;
    private String suffix;
    private boolean onSameLine;

    private boolean isLoopRow;

    // 固定行，例如路面及交安需要保留空白行
    private Integer fixedRow = 0;

    public ExistingLoopRowTableRenderPolicy() {
        this(false,false);
    }


    public ExistingLoopRowTableRenderPolicy(boolean onSameLine, boolean isLoopRow) {
        this("[", "]", onSameLine);
        this.isLoopRow = isLoopRow;
    }

    public ExistingLoopRowTableRenderPolicy(boolean onSameLine, boolean isLoopRow ,Integer fixedRow) {
        this("[", "]", onSameLine);
        this.isLoopRow = isLoopRow;
        this.fixedRow = fixedRow;
    }

    public ExistingLoopRowTableRenderPolicy(String prefix, String suffix) {
        this(prefix, suffix, false);
    }

    public ExistingLoopRowTableRenderPolicy(String prefix, String suffix, boolean onSameLine) {
        this.prefix = prefix;
        this.suffix = suffix;
        this.onSameLine = onSameLine;
    }

    @Override
    public void render(ElementTemplate eleTemplate, Object data, XWPFTemplate template) {
        RunTemplate runTemplate = (RunTemplate) eleTemplate;
        XWPFRun run = runTemplate.getRun();
        try {
            if (!TableTools.isInsideTable(run)) {
                throw new IllegalStateException(
                        "The template tag " + runTemplate.getSource() + " must be inside a table");
            }
            XWPFTableCell tagCell = (XWPFTableCell) ((XWPFParagraph) run.getParent()).getBody();
            XWPFTable table = tagCell.getTableRow().getTable();
            run.setText("", 0);
            int templateRowIndex = getTemplateRowIndex(tagCell);
            int index = 0;
            if (null != data && data instanceof Iterable) {
                Iterator<?> iterator = ((Iterable<?>) data).iterator();
                XWPFTableRow templateRow = table.getRow(templateRowIndex);

                TemplateResolver resolver = new TemplateResolver(template.getConfig().copy(prefix, suffix));
                boolean firstFlag = true;

                boolean hasNext = iterator.hasNext();
                while (hasNext) {
                    Object root = iterator.next();
                    hasNext = iterator.hasNext();
                    //insertPosition是要插入模板行的下标，模板行下标+1;
                    int insertPosition = templateRowIndex++;
                    //表格里把模板行所在的位置插入了一个空白行，模板行在表格中的下标+1,和templateRowIndex增加后的值保持一致
                    table.insertNewTableRow(insertPosition);
                    //setTableRow方法是插件的内部方法，根据(List)ReflectionUtils.getValue("tableRows", table);获取到所有行，再把刚插入的一行替换成模板行
                    this.setTableRow(table, templateRow, insertPosition);
                    //创建一个XML的游标，指向模板行的xml标签；通过该游标，我们可以访问并修改CTRow对象中的所有XML元素，如<w:tr>、<w:tc>、<w:p>等
                    XmlCursor newCursor = templateRow.getCtRow().newCursor();
                    //newCursor.toPrevSibling()的作用是将XML游标向前移动一个兄弟节点，即模板行的上一行，也是我们新插入的一行
                    newCursor.toPrevSibling();
                    //获取到新插入一行的xml数据
                    XmlObject object = newCursor.getObject();
                    //通过新插入行的数据反向生成XWPFTableRow类
                    XWPFTableRow nextRow = new XWPFTableRow((CTRow)object, table);
                    if (!firstFlag) {
                        // update VMerge cells for non-first row
                        List<XWPFTableCell> tableCells = nextRow.getTableCells();
                        for (XWPFTableCell cell : tableCells) {
                            CTTcPr tcPr = TableTools.getTcPr(cell);
                            CTVMerge vMerge = tcPr.getVMerge();
                            if (null == vMerge) continue;
                            if (STMerge.RESTART == vMerge.getVal()) {
                                vMerge.setVal(STMerge.CONTINUE);
                            }
                        }
                    } else {
                        firstFlag = false;
                    }
                    //对模板表格进行再处理后（垂直合并处理后）重新替换表格新插入的一行
                    setTableRow(table, nextRow, insertPosition);

                    RenderDataCompute dataCompute = template.getConfig()
                            .getRenderDataComputeFactory()
                            .newCompute(EnvModel.of(root, EnvIterator.makeEnv(index++, hasNext)));
                    List<XWPFTableCell> cells = nextRow.getTableCells();
                    cells.forEach(cell -> {
                        List<MetaTemplate> templates = resolver.resolveBodyElements(cell.getBodyElements());
                        new DocumentProcessor(template, resolver, dataCompute).process(templates);
                    });
                    if (!isLoopRow){
                        adjustRowHeight(nextRow);
                    }
                }
            }
            int endRow = 1;
            if (!isLoopRow) {
                endRow = fixedRow != 0 ? ((index - fixedRow < 0) ? index  : fixedRow) : index;
            }
            endRow = (endRow < 1) ? 1 : endRow;
            for (int i = 0; i < endRow; i++) {
                table.removeRow(templateRowIndex);
            }
            afterloop(table, data);
        } catch (Exception e) {
            throw new RenderException("HackLoopTable for " + eleTemplate + " error: " + e.getMessage(), e);
        }
    }

    private int getTemplateRowIndex(XWPFTableCell tagCell) {
        XWPFTableRow tagRow = tagCell.getTableRow();
        return onSameLine ? getRowIndex(tagRow) : (getRowIndex(tagRow) + 1);
    }

    protected void afterloop(XWPFTable table, Object data) {
    }


    @SuppressWarnings("unchecked")
    private void setTableRow(XWPFTable table, XWPFTableRow templateRow, int pos) {
        List<XWPFTableRow> rows = (List<XWPFTableRow>) ReflectionUtils.getValue("tableRows", table);
        rows.set(pos, templateRow);
        table.getCTTbl().setTrArray(pos, templateRow.getCtRow());
    }

    private int getRowIndex(XWPFTableRow row) {
        List<XWPFTableRow> rows = row.getTable().getRows();
        return rows.indexOf(row);
    }

    /**
     * 超过行默认高度时，自动扩充高度
     * @param row
     */
    private void adjustRowHeight(XWPFTableRow row) {
        int maxHeight = 0;
        List<XWPFTableCell> cells = row.getTableCells();
        for (XWPFTableCell cell : cells) {
            int cellHeight = calculateCellHeight(cell);
            if (cellHeight > maxHeight) {
                maxHeight = cellHeight;
            }
        }
        // 获取默认行高
        int defaultHeight = row.getHeight();
        if (maxHeight > defaultHeight) {
            row.setHeight(maxHeight);
        }
    }

    private int calculateCellHeight(XWPFTableCell cell) {
        int height = 0;
        List<XWPFParagraph> paragraphs = cell.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            // 考虑段落间距
            height += paragraph.getSpacingAfter();
            height += paragraph.getSpacingBefore();
            List<XWPFRun> runs = paragraph.getRuns();
            for (XWPFRun run : runs) {
                int fontSize = run.getFontSize();
                if (fontSize == -1) {
                    // 默认字体大小
                    fontSize = 10;
                }
                height += fontSize;
            }
        }
        return height;
    }
}					