<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmDinspGpsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmDinspGps">
        <id column="GPS_ID" property="gpsId" />
        <result column="DINSP_ID" property="dinspId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="HEIGHT" property="height" />
        <result column="SPEED" property="speed" />
        <result column="LON" property="lon" />
        <result column="LAT" property="lat" />
        <result column="TIME" property="time" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="TRACK_ID" property="trackId" />
        <result column="REMARK" property="remark" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        GPS_ID, DINSP_ID, CREATE_TIME, UPDATE_TIME, HEIGHT, SPEED, LON, LAT, TIME, INSP_PERSON, TRACK_ID, REMARK, DEL_FLAG
    </sql>
    <select id="getRealTimeLocationByDinspId" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinspGps">
        <![CDATA[
        select * from (select * from memsdb.nm_dinsp_gps where del_flag=0 and dinsp_id = #{dinspId} order by time desc) a where rownum <= 1
        ]]>
    </select>

</mapper>
