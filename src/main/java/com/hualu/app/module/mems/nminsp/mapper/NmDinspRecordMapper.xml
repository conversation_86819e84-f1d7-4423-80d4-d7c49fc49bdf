<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmDinspRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmDinspRecord">
        <id column="DSS_ID" property="dssId" />
        <result column="DINSP_ID" property="dinspId" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="ISSUE_TYPE" property="issueType" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="STAKE" property="stake" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_DEGREE" property="dssDegree" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_PART_ID" property="structPartId" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="LANE" property="lane" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="DSS_CAUSE" property="dssCause" />
        <result column="DSS_L" property="dssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="DSS_W" property="dssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="DSS_D" property="dssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="DSS_N" property="dssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="DSS_A" property="dssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="DSS_V" property="dssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="DSS_P" property="dssP" />
        <result column="DSS_G" property="dssG" />
        <result column="DSS_IMP_FLAG" property="dssImpFlag" />
        <result column="DSS_QUALITY" property="dssQuality" />
        <result column="HIS_DSS_ID" property="hisDssId" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="ISPHONE" property="isphone" />
        <result column="RAMP_ID" property="rampId" />
        <result column="TUNNEL_MOUTH" property="tunnelMouth" />
        <result column="START_HIGH" property="startHigh" />
        <result column="END_HIGH" property="endHigh" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="STAKE_HIGH" property="stakeHigh" />
        <result column="FINISH_STAKE" property="finishStake" />
        <result column="SOURCE" property="source" />
        <result column="CLOSE_TYPE" property="closeType" />
        <result column="PAVEMENT_TYPE" property="pavementType" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_ID, DINSP_ID, INSP_TIME, INSP_TIME_INTVL, ISSUE_TYPE, LINE_DIRECT, STAKE, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, FACILITY_CAT, STRUCT_ID, STRUCT_PART_ID, STRUCT_COMP_ID, LANE, DSS_POSITION, DSS_DESC, DSS_CAUSE, DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V, DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY, HIS_DSS_ID, X, Y, ISPHONE, RAMP_ID, TUNNEL_MOUTH, START_HIGH, END_HIGH, START_STAKE_NUM, END_STAKE_NUM, STAKE_HIGH, FINISH_STAKE, SOURCE, CLOSE_TYPE, PAVEMENT_TYPE, DEL_FLAG, CREATE_TIME, UPDATE_TIME
    </sql>
    <select id="selectViewRecord" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinspRecord">

        with taskcode as (select
        distinct
        dtd.DSS_ID,
        dt.MTASK_CODE,
        dt.MTASK_ID,
        dta.PROCESSINSTID ,
        dta.MTASK_ACCPT_CODE from DM_TASK_DETAIL dtd
        inner JOIN DM_TASK dt ON dt.MTASK_ID = dtd.MTASK_ID
        left JOIN DM_TASK_ACCPT dta ON dta.MTASK_ID = dt.MTASK_ID where dtd.DSS_ID in
        <foreach collection="dssIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )

        SELECT
        distinct
        d.repair_status,
        line.line_sname || '(' || line.line_code || ')' AS line_id,
        ( SELECT count( 1 ) FROM dss_image d WHERE d.dss_id = f.dss_id ) AS IMG,
        f.*,
        t.DSS_TYPE_NAME,
        tc.MTASK_CODE,
        tc.MTASK_ID,
        tc.PROCESSINSTID AS MTASK_ACCPT_ID,
        tc.MTASK_ACCPT_CODE
        FROM
        NM_DINSP_RECORD f
        LEFT JOIN DSS_TYPE_NEW t ON f.DSS_TYPE = t.DSS_TYPE
        LEFT JOIN dss_info d ON ( f.dss_id = d.dss_id )
        LEFT JOIN gdgs.base_line line ON line.line_id = d.main_road_id
        LEFT JOIN taskcode tc on tc.DSS_ID = f.DSS_ID
        WHERE f.del_flag=0 and f.dss_id in
        <foreach collection="dssIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>
    <select id="selectRecords" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinspRecord">
        select a.dss_id,
        a.dinsp_id,
        a.insp_time,
        a.issue_type,
        a.line_direct,
        a.stake,
        a.dss_type,
        a.dss_degree,
        a.mntn_advice,
        a.facility_cat,
        a.struct_part_id,
        a.struct_comp_id,
        a.lane,
        a.dss_position,
        a.dss_desc,
        a.dss_cause,
        a.dss_l,
        a.dss_l_unit,
        a.dss_w,
        a.dss_w_unit,
        a.dss_d,
        a.dss_d_unit,
        a.dss_n,
        a.dss_n_unit,
        a.dss_a,
        a.dss_a_unit,
        a.dss_v,
        a.dss_v_unit,
        a.dss_p,
        a.dss_g,
        a.dss_imp_flag,
        a.dss_quality,
        a.isphone,
        a.ramp_id,
        a.tunnel_mouth,
        a.start_high,
        a.end_high,
        a.start_stake_num,
        a.end_stake_num,
        a.stake_high,
        a.finish_stake,
        a.source,
        a.close_type,
        a.pavement_type,
        a.update_time,
        b.common_dinsp_id,
        b.line_code,
        b.STRUCT_ID,
        b.STRUCT_NAME,
        b.FACILITY_CAT,
        b.status,
        b.MNT_ORG_ID from NM_DINSP_RECORD a join NM_DINSP b on a.DINSP_ID = b.DINSP_ID and a.DEL_FLAG = 0 and b.DEL_FLAG = 0
        where b.COMMON_DINSP_ID = #{dinspCommon.dinspId}
          <if test="dinspCommon.facilityCat != null and  dinspCommon.facilityCat != ''">
              and b.FACILITY_CAT = #{dinspCommon.facilityCat}
          </if>
    </select>

</mapper>
