package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.service.*;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.LmAddGroup;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("jaStructImp")
public class JaStructImp implements IStruct{

    private static final String JA_ISSUE_DESC_FORMAT = "{}存在{}，{}";

    private static final String OTHER = "OTHER";

    @Autowired
    BaseRouteLogicService routeLogicService;

    @Autowired
    NmDinspResultService dmResultService;

    @Autowired
    NmDinspRecordService dmRecordService;

    @Autowired
    NmFinspResultService finspResultService;

    @Autowired
    NmFinspRecordService finspRecordService;

    @Autowired
    NmInspItemService itemService;


    @Override
    public List<String> processAndExportNmDinspWord(List<NmDinsp> nmDinsps, String parentPath) {
        return H_WordHelper.processAndExportWord(
                true,
                nmDinsps,
                parentPath,
                "DM",
                NmDinsp::getFacilityCat,
                NmDinsp::getInspFrequency,
                NmDinsp::getDinspId,
                NmDinsp::getDinspCode,
                NmDinsp::getInspDate,
                dinspIds -> dmResultService.listResultsByDinspIds(dinspIds),
                NmDinspResult::getDinspId,
                (records, entity) -> showWordDmView(records, entity,"DM")
        );
    }

    private void showWordDmView(List<NmDinspResult> records, NmDinsp entity,String xcType) {
        /*List<NmInspItem> items = itemService.listXcTypeByDinsp(entity, xcType);
        if (CollectionUtil.isEmpty(items)){
            return;
        }
        String inspCont = items.get(0).getInspCont();
        records.forEach(item->{
            item.setInspCont(inspCont);
        });*/
    }


    @Override
    public List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps, String parentPath) {
        return H_WordHelper.processAndExportWord(
                true,
                nmFinsps,
                parentPath,
                "FM",
                NmFinsp::getFacilityCat,
                NmFinsp::getInspFrequency,
                NmFinsp::getFinspId,
                NmFinsp::getFinspCode,
                NmFinsp::getInspDate,
                finspIds -> finspResultService.listResultsByFinspIds(finspIds,"JA"),
                NmFinspResult::getFinspId,
                (records, entity) -> showWordFmView(records, entity,"FM")
        );
    }

    private void showWordFmView(List<NmFinspResult> records, NmFinsp entity,String xcType) {
        /*List<NmInspItem> items = itemService.listXcTypeByFinsp(entity, xcType);
        if (CollectionUtil.isEmpty(items)){
            return;
        }
        String inspCont = items.get(0).getInspCont();
        records.forEach(item->{
            item.setInspCont(inspCont);
        });*/
    }

    @Override
    public void validateNmDinsp(NmDinsp nmDinsp) {
        H_CValidator.validator2Exception(nmDinsp, new Class[]{LmAddGroup.class});
        // 根据路线编码，生成对应的路段编码及路段名称
        BaseRouteLogic routeLogic = routeLogicService.getRouteByLineCodeOrRouteCode(nmDinsp.getLineCode(),nmDinsp.getRouteCode());
        if (routeLogic == null) {
            throw new BaseException("路线编码无法匹配对应的路段信息");
        }
        nmDinsp.setRouteCode(routeLogic.getRouteCode()).setRouteName(routeLogic.getRoadName());
    }

    @Override
    public void validateNmFinsp(NmFinsp nmFinsp) {
        H_CValidator.validator2Exception(nmFinsp, new Class[]{LmAddGroup.class});
        // 根据路线编码，生成对应的路段编码及路段名称
        BaseRouteLogic routeLogic = routeLogicService.getRouteByLineCodeOrRouteCode(nmFinsp.getLineCode(),nmFinsp.getRouteCode());
        if (routeLogic == null) {
            throw new BaseException("路线编码无法匹配对应的路段信息");
        }
        nmFinsp.setRouteCode(routeLogic.getRouteCode()).setRouteName(routeLogic.getRoadName());
    }

    @Override
    public void refreshNmFinspResult(NmFinsp nmFinsp) {
        List<NmFinspResult> nmDinspResult = finspResultService.createNmFinspResult(nmFinsp);
        // 获取对应的病害ID
        List<NmFinspRecord> nmDinspRecords = finspRecordService.selectRecordByFinspId(nmFinsp.getFinspId());
        finspRecordService.showView(nmDinspRecords,nmFinsp);
        if (CollectionUtil.isEmpty(nmDinspResult) || CollectionUtil.isEmpty(nmDinspRecords)) {
            if (CollectionUtil.isNotEmpty(nmDinspResult)){
                //清空结论
                finspResultService.updateBatchById(nmDinspResult);
            }
            return;
        }
        // 根据部件进行分组
        Map<String, List<NmFinspRecord>> partMap = nmDinspRecords.stream().filter(e->StrUtil.isNotBlank(e.getStructPartId()))
                .collect(Collectors.groupingBy(NmFinspRecord::getStructPartId));
        for (NmFinspResult item : nmDinspResult) {
            if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())) {
                continue;
            }
            // partId会存在多个
            List<String> partIds = StrUtil.split(item.getPartId(), ",");
            List<NmFinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dssList)) {
                //检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
                List<NmFinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(containsDssList)) {
                    item.setIssueDesc(getIssueDescByFm(containsDssList));
                    // 移除已经匹配的病害
                    nmDinspRecords.removeAll(dssList);
                }
            }
        }
        // 获取其他项结论
        NmFinspResult otherResult = nmDinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
        if (otherResult != null && CollectionUtil.isNotEmpty(nmDinspRecords)) {
            otherResult.setIssueDesc(getIssueDescByFm(nmDinspRecords));
        }
        finspResultService.updateBatchById(nmDinspResult);
    }

    private String getIssueDescByFm(List<NmFinspRecord> nmDinspRecords) {
        if (CollectionUtil.isEmpty(nmDinspRecords)){
            return null;
        }
        List<String> issueList = Lists.newArrayList();
        nmDinspRecords.forEach(record->{
            String stake = record.getStake() == null ? null : record.getStake().toString();
            String stakeCn = H_StakeHelper.convertCnStake(stake);
            String partName = StrUtil.isBlank(record.getStructPartName())?"":record.getStructPartName();
            String issueDesc = StrUtil.format(JA_ISSUE_DESC_FORMAT, partName, record.getDssTypeName(), record.getDssNum());
            if (StrUtil.isNotBlank(record.getDssDesc())){
                issueDesc = issueDesc + "，病害描述："+record.getDssDesc();
            }
            issueDesc = record.getLineDirectName()+"方向，"+stakeCn+issueDesc;
            issueList.add(issueDesc);
        });
        return issueList.stream().collect(Collectors.joining("；"));
    }

    @Override
    public void refreshNmDinspResult(NmDinsp nmDinsp) {
        List<NmDinspResult> nmDinspResult = dmResultService.createNmDinspResult(nmDinsp);
        // 获取对应的病害ID
        List<NmDinspRecord> nmDinspRecords = dmRecordService.selectRecordByDinspId(nmDinsp.getDinspId());
        dmRecordService.showView(nmDinspRecords,nmDinsp);
        if (CollectionUtil.isEmpty(nmDinspResult) || CollectionUtil.isEmpty(nmDinspRecords)) {
            if (CollectionUtil.isNotEmpty(nmDinspResult)){
                //清空结论
                dmResultService.updateBatchById(nmDinspResult);
            }
            return;
        }
        // 根据部件进行分组
        Map<String, List<NmDinspRecord>> partMap = nmDinspRecords.stream().filter(e->StrUtil.isNotBlank(e.getStructPartId()))
                .collect(Collectors.groupingBy(NmDinspRecord::getStructPartId));
        for (NmDinspResult item : nmDinspResult) {
            if (StrUtil.isBlank(item.getDssType()) || OTHER.equals(item.getPartId())) {
                continue;
            }
            // partId会存在多个
            List<String> partIds = StrUtil.split(item.getPartId(), ",");
            List<NmDinspRecord> dssList = partIds.stream().map(partId-> partMap.getOrDefault(partId, Lists.newArrayList())).flatMap(List::stream).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(dssList)) {
                //检查单会存在partId一致，但是未包含所有的病害类型，所以需要判断病害类型是否匹配
                List<NmDinspRecord> containsDssList = dssList.stream().filter(e -> item.getDssType().contains(e.getDssType())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(containsDssList)) {
                    item.setIssueDesc(getIssueDescByDm(containsDssList));
                    // 移除已经匹配的病害
                    nmDinspRecords.removeAll(dssList);
                }
            }
        }
        // 获取其他项结论
        NmDinspResult otherResult = nmDinspResult.stream().filter(e -> OTHER.equals(e.getPartId())).findFirst().orElse(null);
        if (otherResult != null && CollectionUtil.isNotEmpty(nmDinspRecords)) {
            otherResult.setIssueDesc(getIssueDescByDm(nmDinspRecords));
        }
        dmResultService.updateBatchById(nmDinspResult);
    }

    private String getIssueDescByDm(List<NmDinspRecord> containsDssList) {
        if (CollectionUtil.isEmpty(containsDssList)){
            return null;
        }
        List<String> issueList = Lists.newArrayList();
        containsDssList.forEach(record->{
            String stake = record.getStake() == null ? null : record.getStake().toString();
            String stakeCn = H_StakeHelper.convertCnStake(stake);
            String partName = StrUtil.isBlank(record.getStructPartName())?"":record.getStructPartName();
            String issueDesc = StrUtil.format(JA_ISSUE_DESC_FORMAT, partName, record.getDssTypeName(), record.getDssNum());
            if (StrUtil.isNotBlank(record.getDssDesc())){
                issueDesc = issueDesc + "，病害描述："+record.getDssDesc();
            }
            issueDesc = record.getLineDirectName()+"方向，"+stakeCn+issueDesc;
            issueList.add(issueDesc);
        });
        return issueList.stream().collect(Collectors.joining("；"));
    }
}
