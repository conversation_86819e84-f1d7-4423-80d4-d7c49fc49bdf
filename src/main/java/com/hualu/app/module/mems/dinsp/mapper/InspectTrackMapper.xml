<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dinsp.mapper.InspectTrackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dinsp.entity.InspectTrack">
        <id column="ID" property="id" />
        <result column="INSPECT_ID" property="inspectId" />
        <result column="LONGITUDE" property="longitude" />
        <result column="LATITUDE" property="latitude" />
        <result column="ORDERS" property="orders" />
        <result column="SPEED" property="speed" />
        <result column="CARD_NO" property="cardNo" />
        <result column="TIME" property="time" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, INSPECT_ID, LONGITUDE, LATITUDE, ORDERS, SPEED, CARD_NO, TIME
    </sql>

</mapper>
