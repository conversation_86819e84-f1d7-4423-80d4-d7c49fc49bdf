package com.hualu.app.module.mems.nminsp.dto;

import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Accessors(chain = true)
@Data
public class NmDinspEmptDto implements Serializable {

    //来源主单
    private NmDinsp nmDinsp;

    // 后续生成目的主单
    private List<NmDinsp> nmDinspList;

    // 公用主单
    private String commonDinspId;

    //用户账号
    private String userCode;

    //用户名称
    private String userName;

    //用户ID
    private String userId;
}
