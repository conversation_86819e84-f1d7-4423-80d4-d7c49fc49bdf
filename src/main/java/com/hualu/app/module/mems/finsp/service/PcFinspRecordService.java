package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspDealDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspRecordExcelDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspRecord;

import java.util.List;

/**
 * <p>
 * 结构物排查记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface PcFinspRecordService extends IService<PcFinspRecord> {

    void deleteFinspRecordByIds(List<String> recordIds);

    IPage selectViewRecordByFinspId(String finspId);

    List<PcFinspRecord> listRecordByFinspId(String finspId);

    PcFinspRecord saveOrUpdateFinspRecord(PcFinspRecord dmFinspRecord);

    List<PcFinspStatDto> getFinspStat(String startMonth, String endMonth, String orgId,String mntType,String facilityCat);

    void removeByFinspIds(List<String> finspIds);

    IPage listRecord(String status);

    List<PcFinspRecordExcelDto> exportRecords(String orgId, String startMonth, String endMonth);

    List<PcFinspRecord> listRecordByStructId(String structId, String dealStatus);

    void updateStatus(String recordId);

    List<PcFinspRecordExcelDto> listRecordExcelDtoByPrjId(List<String> prjIds);

    List<PcFinspDealDto> getGeometryDeal(GeometryParamDto paramDto);

    /**
     * 根据主单，查询病害经纬度信息  0:x，1：y
     * @param finspId
     * @return
     */
    List<Double> getXyByFinspId(String finspId);
}
