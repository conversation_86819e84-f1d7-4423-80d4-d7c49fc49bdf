<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.payment.mapper.MeterageReportLogMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.payment.entity.MeterageReportLog">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="fileId" column="FILE_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="VARCHAR"/>
            <result property="contrId" column="CONTR_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,USER_CODE,FILE_NAME,
        FILE_ID,CREATE_TIME,CONTR_ID
    </sql>
    <select id="getDssImageList" resultType="com.hualu.app.module.mems.autoInspector.entity.DownloadVo">
        select a.FILE_ID,aaa.FILE_ENTITY_NAME as file_name,aaaa.DSS_TYPE_NAME as dss_type,aa.RL_STAKE_NEW as stake from DSS_IMAGE a
        inner join dss_info aa on a.DSS_ID = aa.DSS_ID
        inner join DSS_TYPE_NEW aaaa on aa.DSS_TYPE = aaaa.DSS_TYPE
        inner join gdgs.BASE_FILE_ENTITY aaa on a.FILE_ID = aaa.FILE_ENTITY_ID
        where exists (select 1 from DM_TASK_ACCPT_DETAIL b where exists(select 1 from DM_TASK_ACCPT c where b.MTASK_ACCPT_ID =
        c.MTASK_ACCPT_ID and c.MTASK_ACCPT_CODE = #{code}) and b.DSS_ID  = a.DSS_ID  )
        order by RL_STAKE_NEW
    </select>
</mapper>
