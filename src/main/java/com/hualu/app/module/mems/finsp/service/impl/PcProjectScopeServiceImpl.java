package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.basedata.dto.near.BaseNearStructStatDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.mapper.PcProjectScopeMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspService;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.hualu.app.module.mems.finsp.service.PcProjectService;
import com.hualu.app.module.mems.finsp.upload.IImportStruct;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.utils.H_RestResultHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_UniqueConstraintHelper;
import com.tg.dev.mybatisplus.service.PageService;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 结构物专项范围表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
public class PcProjectScopeServiceImpl extends ServiceImpl<PcProjectScopeMapper, PcProjectScope> implements PcProjectScopeService {

    private static List<String> excelHeaderTemplates = Lists.newArrayList("路线编码","路线方向","边坡桩号/名称","边坡位置","边坡级数","路段类型","是否重点边坡");

    @Autowired
    PageService pageService;

    @Autowired
    HttpServletRequest request;

    @Autowired
    PcProjectService projectService;

    @Lazy
    @Autowired
    PcFinspService pcFinspService;

    @Autowired
    FwRightOrgService orgService;

    @Override
    public RestResult<Map<String, Object>> getNear(BaseNearStructDto dto) {
        Page page = pageService.parsePageParam(request);
        List<String> prjIds = projectService.listPrjId(CustomRequestContextHolder.getOrgIdString(), DateUtil.thisYear(), dto.getMntType(), dto.getFacilityCat());

        //当前公司不存在专项项目，直接返回
        if (CollectionUtil.isEmpty(prjIds)) {
            return RestResult.success(Maps.newHashMap());
        }
        LambdaQueryWrapper<PcProjectScope> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PcProjectScope::getPrjId,prjIds);

        //附近1公里
        double nearLength = 0.00005;
        if (dto.getIsNear().equals(1)){
            queryWrapper.between(PcProjectScope::getX,dto.getX()-nearLength,dto.getX() + nearLength)
                    .between(PcProjectScope::getY,dto.getY()-nearLength,dto.getY() + nearLength);
        }

        //模糊查询结构物名称
        if (StrUtil.isNotBlank(dto.getStructName())){
            String likeName = StrUtil.replace(dto.getStructName()," ","%");
            queryWrapper.like(PcProjectScope::getStructName,likeName);
        }

        // 已检查单
        if (dto.getChecked().equals(1)){
            queryWrapper.exists(getExistsSql(dto.getFacilityCat(),false,dto.getMntType()));
        }
        //未检查
        if (dto.getChecked().equals(0)){
            queryWrapper.notExists(getExistsSql(dto.getFacilityCat(),false,dto.getMntType()));
        }

        //已复核
        if (dto.getChecked().equals(3)){
            queryWrapper.exists(getExistsSql(dto.getFacilityCat(),true,dto.getMntType()));
        }

        //结构物类型查询
        if (StrUtil.isNotBlank(dto.getStructType())){
            queryWrapper.in(PcProjectScope::getStructType,StrUtil.split(dto.getStructType(),","));
        }

        //根据结构物名称及桩号排序
        queryWrapper.orderByAsc(PcProjectScope::getStructName,PcProjectScope::getStructStake);

        BaseNearStructStatDto structStatDto = getCheckStructIds(prjIds, dto.getFacilityCat(),dto.getMntType());
        Map<String,Object> checkedMap = new HashMap<>();
        checkedMap.put("checkNum",structStatDto.getCheckedNum());
        checkedMap.put("noCheckedNum",structStatDto.getNotCheckedNum());

        IPage ipage = page(page, queryWrapper);
        List<BaseNearStructDto> structDtos = null;
        Map<String, IImportStruct> beans = CustomApplicationContextHolder.getBeansOfType(IImportStruct.class);
        for (IImportStruct value : beans.values()) {
            if (value.getFacilityCat().equals(dto.getFacilityCat())){
                structDtos = value.toBaseNearStructDto(ipage.getRecords());
                break;
            }
        }
        ipage.setRecords(structDtos);
        if (CollectionUtil.isEmpty(structDtos)){
            RestResult<List<BaseNearStructDto>> result = H_RestResultHelper.returnPage(ipage);
            checkedMap.put("data", result.getData());
            return RestResult.success(checkedMap,result.getTotal(),result.getPage(),result.getPageSize());

        }

        structDtos.forEach(item->{
            if (structStatDto.getStructIds().contains(item.getStructId())){
                item.setChecked(1);
            }
        });
        initDssNum(structDtos);
        RestResult<List<BaseNearStructDto>> result = H_RestResultHelper.returnPage(ipage);
        checkedMap.put("data", result.getData());
        return RestResult.success(checkedMap,result.getTotal(),result.getPage(),result.getPageSize());
    }

    //回显病害数
    private void initDssNum(List<BaseNearStructDto> structDtos) {
        List<String> structIds = structDtos.stream().map(BaseNearStructDto::getStructId).collect(Collectors.toList());
        Map<String, PcFinsp> dssNumMap = pcFinspService.getDssNum(structIds);

        structDtos.forEach(item->{
            PcFinsp pcFinsp = dssNumMap.get(item.getStructId());
            if (pcFinsp != null){
                item.setDssNum(pcFinsp.getDssNum()==null?0: pcFinsp.getDssNum());
                item.setReviewFlag(pcFinsp.getReviewFlag());
            }
        });
    }

    @Override
    public PcProjectScope getByStructId(String structId) {
        PcProjectScope projectScope = baseMapper.selectByStructId(structId);
        return projectScope;
    }

    @Override
    public List<PcProjectScope> listByMd5(List<String> md5, String prjId) {
        List<PcProjectScope> projectScopes = Lists.newArrayList();
        List<List<String>> lists = ListUtil.partition(md5,200);

        lists.forEach(items->{
            List<PcProjectScope> dbList = baseMapper.listByMd5(items,prjId);
            projectScopes.addAll(dbList);
        });
        return projectScopes;
    }

    @Override
    public void saveBatchByExcel(PcProject pcProject, File uploadFile) {
        Map<String, IImportStruct> beans = CustomApplicationContextHolder.getBeansOfType(IImportStruct.class);

        List<PcProjectScope> pcProjectScopes = null;
        for (IImportStruct item : beans.values()) {
            if (pcProject.getFacilityCat().equals(item.getFacilityCat())){
                pcProjectScopes = item.doImport(pcProject,uploadFile);
                break;
            }
        }
        if (pcProjectScopes == null){
            throw new BaseException("未匹配相应的模版");
        }
        List<String> md5s = Lists.newArrayList();
        pcProjectScopes.forEach(item->{
            item.setPrjId(pcProject.getPrjId()).setFacilityCat(pcProject.getFacilityCat()).setMntOrgId(pcProject.getMntOrgId());
            String unique = H_UniqueConstraintHelper.createUnique(item);
            item.setMd5(unique);
            md5s.add(unique);
        });
        //获取数据已存在的结构物数据
        List<PcProjectScope> dbPcProjectScopes = listByMd5(md5s, pcProject.getPrjId());
        Map<String, List<PcProjectScope>> md5Map = dbPcProjectScopes.stream().collect(Collectors.groupingBy(PcProjectScope::getMd5));

        pcProjectScopes.forEach(item->{
            List<PcProjectScope> md5Values = md5Map.get(item.getMd5());
            if (md5Values != null) {
                // 如果数据库存在该数据，设置未修改
                item.setStructId(md5Values.get(0).getStructId());
                // 恢复状态
                item.setDelFlag(0);
            }
        });
        if (CollectionUtil.isNotEmpty(pcProjectScopes)) {
            updateIgnoreDelField(pcProjectScopes.stream().filter(e->StrUtil.isNotBlank(e.getStructId())).map(PcProjectScope::getStructId).collect(Collectors.toList()));
            saveOrUpdateBatch(pcProjectScopes);
        }
    }

    /**
     * 恢复逻辑删除的数据
     * @param structIds
     */
    private void updateIgnoreDelField(List<String> structIds){
        if (CollectionUtil.isEmpty(structIds)){
            return;
        }
        List<List<String>> lists = ListUtil.partition(structIds,200);
        lists.forEach(item->{
            baseMapper.updateIgnoreDelField(item);
        });
    }

    /**
     * 获取已检查结构物
     * @param prjIds
     * @param facilityCat
     * @return
     */
    @Override
    public BaseNearStructStatDto getCheckStructIds(List<String> prjIds,String facilityCat,String mntType){
        LambdaQueryWrapper<PcProjectScope> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PcProjectScope::getPrjId,prjIds);
        queryWrapper.select(PcProjectScope::getStructId);
        //查询项目内的所有结构物ID
        List<String> allStructIds = list(queryWrapper).stream().map(PcProjectScope::getStructId).collect(Collectors.toList());
        queryWrapper.exists(getExistsSql(facilityCat,false,mntType));

        //查询已检查的结构物
        List<String> checkStructIds = list(queryWrapper).stream().map(PcProjectScope::getStructId).collect(Collectors.toList());
        allStructIds.removeAll(checkStructIds);

        BaseNearStructStatDto statDto = new BaseNearStructStatDto();
        statDto.setStructIds(checkStructIds).setCheckedNum(checkStructIds.size()).setNotCheckedNum(allStructIds.size());
        return statDto;
    }

    private String getExistsSql(String facilityCat,boolean reviewFlag,String mntType){
        int review = reviewFlag ? 1 : 0;
        String existsSql = "select 1 from memsdb.pc_finsp d where d.FACILITY_CAT = '{}' and d.STRUCT_ID = Pc_Project_Scope.struct_id and d.del_flag=0 and d.review_flag={}";
        if (StrUtil.isBlank(facilityCat)){
            existsSql = "select 1 from memsdb.pc_finsp d where d.STRUCT_ID = Pc_Project_Scope.struct_id and d.del_flag=0 and d.review_flag={}";
            String format = StrUtil.format(zzCleanSql(existsSql,mntType),review);
            return format;
        }
        String format = StrUtil.format(zzCleanSql(existsSql,mntType), facilityCat,review);
        return format;
    }

    private String zzCleanSql(String sql,String mntType){
        boolean isClean = StrUtil.isNotBlank(mntType) && "3".equals(mntType)?true:false;

        if (isClean){
            return sql + " and d.mnt_type=3";
        }
        return sql;
    }

    @Override
    public List<PcFinspStatDto> checkStat(List<String> orgCodes, List<String> prjIds, String mntType) {

        List<PcFinspStatDto> statDtos = baseMapper.checkStat(orgCodes,prjIds,mntType);
        Map<String, List<PcFinspStatDto>> typeMap = statDtos.stream().collect(Collectors.groupingBy(PcFinspStatDto::getType));
        //最终返回结果
        List<PcFinspStatDto> results = Lists.newArrayList();
        Map<String, PcFinspStatDto> checkMap = typeMap.getOrDefault("PC", Lists.newArrayList()).stream().collect(Collectors.toMap(PcFinspStatDto::getOrgName, Function.identity()));
        Map<String, PcFinspStatDto> nocheckMap = typeMap.getOrDefault("WX", Lists.newArrayList()).stream().collect(Collectors.toMap(PcFinspStatDto::getOrgName, Function.identity()));
        checkMap.forEach((k,v)->{
            PcFinspStatDto nocheckDto = nocheckMap.get(k);
            PcFinspStatDto item = new PcFinspStatDto();
            item.setOrgName(k).setCheckedNum(v.getCheckedNum()).setNotCheckedNum(nocheckDto.getNotCheckedNum());
            String md5 = H_UniqueConstraintHelper.createUniqueArgs(item.getOrgName(), item.getMonth(), item.getMntType(), item.getDealType());
            item.setMd5(md5);
            results.add(item);
        });
        return results;
    }

    @Override
    public List<String> listStructType(String facilityCat, String mntType) {
        List<String> prjIds = projectService.listPrjId(CustomRequestContextHolder.getOrgIdString(), DateUtil.thisYear(), mntType, facilityCat);
        if (CollectionUtil.isEmpty(prjIds)){
            return Lists.newArrayList();
        }
        List<String> structTypes =  baseMapper.listStructType(prjIds).stream().distinct().collect(
            Collectors.toList());
        return structTypes;
    }

    @Override
    public void removeByPrjId(String prjId) {
        LambdaQueryWrapper<PcProjectScope> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcProjectScope::getPrjId,prjId);
        remove(queryWrapper);
    }
}
