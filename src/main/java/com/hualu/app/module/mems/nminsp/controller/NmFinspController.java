package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ctc.wstx.util.StringUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.StructGroup;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.controller.WfworkitemController;
import com.hualu.app.module.workflow.dto.WorkActDto;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_PageHelper;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.io.FileUtil;
import lombok.SneakyThrows;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 新版经常巡查 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Api(tags = "经常巡查", description = "新版经常巡查 前端控制器")
@RestController
@RequestMapping("/nmFinsp")
public class NmFinspController extends M_MyBatisController<NmFinsp, NmFinspMapper> {

    @Autowired
    private NmFinspService service;

    @Autowired
    DssImageService imageService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    WfworkitemController wfworkitemController;

    @Value("${fileReposity}")
    String fileReposity;

    /**
     * 分页查询
     *
     * @return
     */
    @ApiRegister(value = "nmDinsp:page", businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    @Override
    public RestResult<List<NmFinsp>> selectPage() {
        Map reqParam = this.getReqParam();
        Boolean isEmptyId = Optional.ofNullable(reqParam).filter(m -> m.containsKey("finspId"))
                .map(m -> m.get("finspId"))
                .map(value -> {
                    if (value instanceof String) {
                        return ((String) value).trim().isEmpty();
                    }
                    return value == null;
                }).orElse(true);
        if (!isEmptyId) {
            Object finspId = reqParam.get("finspId");
            reqParam = new HashMap();
            reqParam.put("finspId", finspId);
        }

        QueryWrapper queryWrapper = this.initQueryWrapper(reqParam);

        String facilityCat = reqParam.getOrDefault("facilityCat", "").toString();
        Object lat = reqParam.get("lat");
        Object lng = reqParam.get("lng");
        if (ObjectUtil.isNotEmpty(lat) && ObjectUtil.isNotEmpty(lng)) {
            IFacBase base = CustomApplicationContextHolder.getBeansOfType(IFacBase.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
            if (base != null) {
                double latitude = NumberUtil.parseDouble(lat.toString(), 0d);
                double longitude = NumberUtil.parseDouble(lng.toString(), 0d);
                List<String> routeCodes = CollectionUtil.newArrayList(H_DataAuthHelper.selectRouteCodeAuth());
                List<BaseStructDto> structList =
                    base.getNearBaseStruct(routeCodes, facilityCat, latitude, longitude);
                queryWrapper.in("struct_id",
                    structList.stream().map(BaseStructDto::getStructId).collect(
                        Collectors.toList()));
            }
        }

        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        //IPage iPage = this.baseMapper.selectPageExtra(this.getPage(), queryWrapper);
        showView(iPage.getRecords());
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 回显前端界面值
     *
     * @param records
     */
    private void showView(List<NmFinsp> records) {
        if (CollectionUtil.isEmpty(records)) {
            return;
        }
        Map<String, List<DssImage>> imageMap = imageService.mapGroupByDssIds(records.stream().map(NmFinsp::getFinspId).collect(Collectors.toList()));
        List<BaseDatathirdDic> dics = dicService.listDicByItem("WEATHER");
        records.forEach(record -> {
            String weatherName = dicService.getDicName(dics, record.getWeather());
            record.setWeatherName(weatherName);
            List<DssImage> images = imageMap.get(record.getFinspId());
            if (images != null && !images.isEmpty()) {
                String gzPicture = images.stream().filter(e -> ObjectUtil.equals(7, e.getImageType())).map(DssImage::getFileId).collect(Collectors.joining(","));
                String zmPicture = images.stream().filter(e -> ObjectUtil.equals(8, e.getImageType())).map(DssImage::getFileId).collect(Collectors.joining(","));
                String cmPicture = images.stream().filter(e -> ObjectUtil.equals(9, e.getImageType())).map(DssImage::getFileId).collect(Collectors.joining(","));
                record.setFileIds(gzPicture).setZmPictureIds(zmPicture).setCmPictureIds(cmPicture);
            }
        });
    }

    @Override
    protected QueryWrapper initQueryWrapper(Map params) {
        QueryWrapper<NmFinsp> queryWrapper = new QueryWrapper();
        initQueryParam(queryWrapper, params);
        H_BatisQuery.setFieldValue2In(queryWrapper, params, this.getEntityClass());
        return queryWrapper;
    }

    private void initQueryParam(QueryWrapper<NmFinsp> queryWrapper, Map params) {
        // 查询有无病害
        Object hasDss = params.getOrDefault("hasDssStatus", "3");
        if (hasDss.equals("0")) {
            queryWrapper.eq("dss_Num", 0);
        } else if (hasDss.equals("1")) {
            queryWrapper.ge("dss_Num", 1);
        }

        Object startDate = params.get("startDate");
        if (!StrUtil.isBlankIfStr(startDate)) {
            queryWrapper.apply("insp_date >= to_date({0},'YYYY-MM-dd')", startDate);
            params.remove("startDate");
        }

        Object endDate = params.get("endDate");
        if (!StrUtil.isBlankIfStr(endDate)) {
            queryWrapper.apply("insp_date <= to_date('" + endDate + " 23:59:59','YYYY-MM-dd HH24:mi:ss')");
            params.remove("endDate");
        }

        String status = params.getOrDefault("status", "").toString();
        if (!"0".equals(status)) {
            queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());
        }
        String taskSql = H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.parseInt(status), "nm_finsp");

        if (StrUtil.isNotBlank(taskSql)) {
            queryWrapper.exists(taskSql);
        }
        params.remove("status");
        //queryWrapper.orderByDesc("insp_date");
        if (params.get("sortField") == null && params.get("sortOrder") == null) {
            queryWrapper.lambda().orderByDesc(NmFinsp::getInspDate);
        }
    }

    /**
     * 初始化检查巡查单，用于新建单回显默认数据
     *
     * @param facilityCat 设施类型
     * @return
     */
    @GetMapping("init")
    public RestResult<NmFinsp> initNmFinsp(@NotBlank String facilityCat, String rang) {
        NmFinsp nmDinsp = service.initNmFinsp(facilityCat, CustomRequestContextHolder.getOrgIdString(), rang);
        return RestResult.success(nmDinsp);
    }

    @ApiOperation("添加或修改检查单")
    @PostMapping(value = "/saveOrUpdate")
    public RestResult<NmFinsp> saveOrUpdate(@RequestBody @Validated NmFinsp nmDinsp) {
        service.saveOrUpdateNmFinsp(nmDinsp);
        return RestResult.success(nmDinsp);
    }

    /**
     * @param request
     * @return
     */
    @ApiOperation("病害台账")
    @PostMapping(value = "/DailyInspectionLedger")
    public RestResult<List<RouteInspection>> DailyInspectionLedger(HttpServletRequest request,
                                                                   @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String facilityCat = null;
        if (reqParam.containsKey("facilityCat")) {
            facilityCat = reqParam.get("facilityCat").toString();
        }

        String startStake = null;
        String endStake = null;
        if (reqParam.containsKey("startStake")) {
            startStake = reqParam.get("startStake").toString();
        }
        if (reqParam.containsKey("endStake")) {
            endStake = reqParam.get("endStake").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(Long.parseLong(reqParam.get("pageIndex").toString()) + 1, Long.parseLong(reqParam.get("pageSize").toString()));
        IPage<RouteInspection> routeInspections = this.service.DailyInspectionLedger(page, dinspCode, facilityCat
                , lineCode, startStake, endStake, startDate, endDate, reqParam);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(routeInspections));
    }

    /**
     * @param response
     * @return
     */
    @ApiOperation("导出病害台账")
    @PostMapping(value = "/exportDailyInspection")
    public void exportDailyInspection(HttpServletResponse response,
                                      @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String facilityCat = null;
        if (reqParam.containsKey("facilityCat")) {
            facilityCat = reqParam.get("facilityCat").toString();
        }

        String startStake = null;
        String endStake = null;
        if (reqParam.containsKey("startStake")) {
            startStake = reqParam.get("startStake").toString();
        }
        if (reqParam.containsKey("endStake")) {
            endStake = reqParam.get("endStake").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(1, Integer.MAX_VALUE);
        this.service.exportDailyInspection(response, page, dinspCode, facilityCat, lineCode, startStake, endStake, startDate, endDate, reqParam);
    }

    /**
     * @param
     * @return
     */
    @ApiOperation("获取病害图片")
    @GetMapping(value = "/images")
    public void getImage(HttpServletResponse response) {
        try {
            // 1. 读取图片流
            InputStream is = new FileInputStream("E:\\highway-mems\\src\\main\\resources\\images\\clipbord_1749005308555.png");

            // 2. 设置响应头（关键！）
            response.setContentType("image/png"); // 根据实际图片类型调整
            response.setHeader("Cache-Control", "max-age=3600"); // 可选缓存

            // 3. 流复制（Apache Commons IO简化操作）
            IOUtils.copy(is, response.getOutputStream());

            // 4. 关闭资源
            is.close();
            response.flushBuffer();
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    /**
     * 批量删除巡查单
     *
     * @param finspIds
     * @return
     */
    @PostMapping("delBatch")
    public RestResult<String> delBatch(@RequestParam(required = true) String finspIds) {
        List<String> idList = StrUtil.split(finspIds, ",");
        service.delBatch(idList);
        return RestResult.success("操作成功");
    }


    /**
     * 批量生成结构物检查单（多个结构物以逗号分隔赋值到structId中）
     *
     * @param nmfinsp
     * @return
     */
    @PostMapping("saveBatchNmFinsp")
    public RestResult<String> saveBatchNmFinsp(@RequestBody @Validated(StructGroup.class) NmFinsp nmfinsp) {
        service.saveBatchNmFinsp(nmfinsp);
        return RestResult.success("操作成功");
    }

    /**
     * 返回无病害的经常检查单
     *
     * @return
     */
    @PostMapping("listNmFinspNoDss")
    public RestResult<List<NmFinsp>> listNmFinspNoDss(String facilityCat, Integer status) {
        Map reqParam = this.getReqParam();
        // 流程待办
        reqParam.put("status", 0);
        QueryWrapper queryWrapper = this.initQueryWrapper(reqParam);
        queryWrapper.eq(ObjectUtil.isNotNull(status), "status", status);
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 查询下一岗
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/loadNextActivitys")
    public RestResult<List<WorkActDto>> loadNextActivitys(@RequestParam(required = true) String facilityCat) throws Exception {
        // 1.查询待办的流程实例
        Map<Integer, List<Long>> taskMap = service.listProcessTask(facilityCat);
        // 流程创建待办
        List<Long> createTaskList = taskMap.get(0);
        // 业主审核待办
        List<Long> submitTaskList = taskMap.get(1);
        if (CollectionUtil.isNotEmpty(createTaskList)) {
            Long proInstId = createTaskList.get(0);
            List<WorkActDto> workActDtos = wfworkitemController.loadNextActivitys(proInstId).getData();
            workActDtos.forEach(workActDto -> {
                workActDto.setProcessInstId(proInstId);
            });
            return RestResult.success(workActDtos);
        }
        if (CollectionUtil.isNotEmpty(submitTaskList)) {
            Long proInstId = submitTaskList.get(0);
            List<WorkActDto> workActDtos = wfworkitemController.loadNextActivitys(proInstId).getData();
            workActDtos.forEach(workActDto -> {
                workActDto.setProcessInstId(proInstId);
            });
            return RestResult.success(workActDtos);
        }
        throw new BaseException("不存在待办项");
    }

    /**
     * 公用单批量办理
     *
     * @param processInstId 当前的流程实例ID
     * @param nextAction    下一节点
     * @param nextUserIds   下一办理人
     * @param apprOpinion   审批意见
     * @param appvOpinion   审批意见
     * @return
     */
    @PostMapping("submitApprove")
    public RestResult<String> submitApprove(
            @RequestParam(required = true) long processInstId,
            @RequestParam(required = true) String facilityCat
            , String nextAction, String nextUserIds
            , @RequestParam(required = false) String apprOpinion
            , @RequestParam(required = false) String appvOpinion) {
        // 查询日常巡查待办任务
        Map<Integer, List<Long>> taskListMap = service.listProcessTask(facilityCat);
        // 当前流程是流程创建人待办
        boolean isCreateFlag = false;
        // 流程创建待办
        List<Long> createTaskList = taskListMap.get(0);
        if (CollectionUtil.isNotEmpty(createTaskList)) {
            isCreateFlag = createTaskList.contains(processInstId);
            String processIdStr = StrUtil.join(",", createTaskList);
            wfworkitemController.submitApprove(processIdStr, nextAction, nextUserIds, apprOpinion, appvOpinion);
        }
        // 业主审核待办（直接病害入库）
        List<Long> submitTaskList = taskListMap.get(1);
        if (CollectionUtil.isNotEmpty(submitTaskList)) {
            String processIdStr = StrUtil.join(",", submitTaskList);
            String tempNextAction = isCreateFlag ? "putDss" : nextAction;
            String tempNextUserIds = isCreateFlag ? null : nextUserIds;
            wfworkitemController.submitApprove(processIdStr, tempNextAction, tempNextUserIds, apprOpinion, appvOpinion);
        }
        return RestResult.success("办理成功");
    }

    /**
     * 导出检查单（单个导出一个word,多个导出压缩包）
     *
     * @param finspIds    检查单ID
     * @param facilityCat 设施类型
     * @return
     */
    @SneakyThrows
    @RequestMapping("exportWord")
    public void exportWord(String finspIds, String facilityCat, HttpServletResponse response,
                           String exportPhoto
    ) {
        Objects.requireNonNull(finspIds, "finspIds不能为空");
        // 查询所有的主单信息，查询所有的病害信息
        DownloadWordDto wordDto = service.exportWord(finspIds, facilityCat);
        if (!org.apache.commons.lang3.StringUtils.isBlank(exportPhoto)
                && "1".equals(exportPhoto)) {
            List<String> wordPaths = wordDto.getWordPaths();
            String path = wordPaths.stream().findFirst().get();
            List<String> dssInfoImageExport = service.getDssInfoImageExport(finspIds, facilityCat, path);
            if (dssInfoImageExport != null && dssInfoImageExport.size() > 0) {
                wordPaths.addAll(dssInfoImageExport);
            }
        }
        H_WordHelper.download(wordDto, response);
    }

    /**
     * pdf预览
     *
     * @param finspIds
     * @param facilityCat
     * @param response
     */
    @SneakyThrows
    @PostMapping("exportPdf")
    public RestResult<String> exportPdf(@RequestParam(value = "finspIds", required = true) String finspIds,
                                        @RequestParam(value = "facilityCat", required = true) String facilityCat,
                                        HttpServletResponse response) {
        Objects.requireNonNull(finspIds, "finspIds不能为空");
        // 查询所有的主单信息，查询所有的病害信息
        DownloadWordDto wordDto = service.exportWord(finspIds, facilityCat);
        String pdfPath = H_WordHelper.wordToPdf(wordDto, fileReposity);
        FileUtil.deleteDir(wordDto.getParentPath());
        return RestResult.success(pdfPath);
    }

    /**
     * 保存检查工照
     *
     * @return
     */
    @PostMapping("saveInspectionPhotos")
    public RestResult<String> saveInspectionPhotos(@RequestParam(required = true) String finspId, @RequestParam(required = true) String photoIds) {
        List<String> idList = StrUtil.split(photoIds, ",");
        service.saveInspectionPhotos(finspId, idList);
        return RestResult.success("保存检查工照成功");
    }

    /**
     * 删除检查工照
     *
     * @return
     */
    @PostMapping("delInspectionPhotos")
    public RestResult<String> delInspectionPhotos(@RequestParam(required = true) String id) {
        service.deleteInspectionPhotoById(id);
        return RestResult.success("删除检查工照成功");
    }

    /**
     * 获取检查工照
     *
     * @return
     */
    @PostMapping("getInspectionPhotos")
    public RestResult<String> getInspectionPhotos(@RequestParam(value = "finspId", required = true) String finspId) {
        return RestResult.success(service.getInspectionPhotos(finspId), "获取检查工照成功");
    }


    /**
     * @param request
     * @return
     */
    @ApiOperation("边坡台账")
    @PostMapping(value = "/findBpDailyRecord")
    public RestResult<List<RouteInspection>> findBpDailyRecord(HttpServletRequest request,
                                                               @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(Long.parseLong(reqParam.get("pageIndex").toString()) + 1, Long.parseLong(reqParam.get("pageSize").toString()));
        IPage<SlopeInspectionRecord> routeInspections = this.service.findBpDailyRecord(page, dinspCode, status, lineCode, hasDssStatus, startDate, endDate);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(routeInspections));
    }

    /**
     * @param response
     * @return
     */
    @ApiOperation("导出边坡台账")
    @PostMapping(value = "/exportfindBpDailyRecordSlope")
    public void exportfindBpDailyRecordSlope(HttpServletResponse response,
                                             @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(1, Integer.MAX_VALUE);
        this.service.findBpDailyRecord(response, page, dinspCode, status, lineCode, hasDssStatus, startDate, endDate);
    }

    /**
     * @param request
     * @return
     */
    @ApiOperation("边坡台账")
    @PostMapping(value = "/findQLDailyRecord")
    public RestResult<List<RouteInspection>> findQLDailyRecord(HttpServletRequest request,
                                                               @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(Long.parseLong(reqParam.get("pageIndex").toString()) + 1, Long.parseLong(reqParam.get("pageSize").toString()));
        IPage<BridgeInspection> routeInspections = this.service.findQLDailyRecord(page, dinspCode, status, lineCode, hasDssStatus, startDate, endDate);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(routeInspections));
    }

    /**
     * @param request
     * @return
     */
    @ApiOperation("涵洞台账")
    @PostMapping(value = "/findHdDailyRecord")
    public RestResult<List<HdInspectionRecord>> findHdDailyRecord(HttpServletRequest request,
                                                               @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        IPage page = new Page(Long.parseLong(reqParam.get("pageIndex").toString()) + 1, Long.parseLong(reqParam.get("pageSize").toString()));
        IPage<HdInspectionRecord> routeInspections = this.service.findHdDailyRecord(page, dinspCode, status, lineCode, hasDssStatus, startDate);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(routeInspections));
    }
    /**
     * @param response
     * @return
     */
    @ApiOperation("导出边坡台账")
    @PostMapping(value = "/exportfindHdDailyRecordSlope")
    public void exportfindHdDailyRecordSlope(HttpServletResponse response,
                                             @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        IPage page = new Page(1, Integer.MAX_VALUE);
        this.service.findHdDailyRecord(response, page, dinspCode, status, lineCode, hasDssStatus, startDate);
    }
    /**
     * @param response
     * @return
     */
    @ApiOperation("导出边坡台账")
    @PostMapping(value = "/exportfindQLDailyRecord")
    public void exportfindQLDailyRecord(HttpServletResponse response,
                                        @RequestBody String json) {
        Map reqParam = new HashMap();
        if (!StringUtils.isBlank(json)) {
            Map<String, Object> innerMap = JSON.parseObject(json).getInnerMap();
            if (innerMap != null && innerMap.size() > 0) {
                reqParam.putAll(innerMap);
            }
        }
        if (reqParam != null && null != reqParam.get("finspId") && !reqParam.get("finspId").equals("")) {
            reqParam.put("status", 3);
        }
        String lineCode = null;
        if (reqParam.containsKey("lineCode")) {
            lineCode = reqParam.get("lineCode").toString();
        }
        String hasDssStatus = null;
        if (reqParam.containsKey("hasDssStatus")) {
            hasDssStatus = reqParam.get("hasDssStatus").toString();
        }
        String dinspCode = null;
        if (reqParam.containsKey("dinspCode")) {
            dinspCode = reqParam.get("dinspCode").toString();
        }
        String status = null;
        if (reqParam.containsKey("status")) {
            status = reqParam.get("status").toString();
        }
        String startDate = null;
        String endDate = null;
        if (reqParam.containsKey("startDate")) {
            startDate = reqParam.get("startDate").toString();
        }
        if (reqParam.containsKey("endDate")) {
            endDate = reqParam.get("endDate").toString();
        }
        IPage page = new Page(1, Integer.MAX_VALUE);
        this.service.findQLDailyRecord(response, page, dinspCode, status, lineCode, hasDssStatus, startDate, endDate);
    }
    /**
     * @param request
     * @return
     */
    @ApiOperation("查询当月是否已经生成过检查单")
    @PostMapping(value = "/deleteStructData")
    public RestResult<List<String>> deleteStructData(HttpServletRequest request, @RequestBody String structIds)
    {
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(structIds);
        } catch (Exception e) {
            throw new IllegalArgumentException("参数 structIds 不是合法的 JSON 字符串", e);
        }

        // 获取 structIds 数组
        List<String> structIdList = new ArrayList<>();
        JSONArray structIdArray = jsonObject.getJSONArray("structIds");
        if (structIdArray != null) {
            structIdList = structIdArray.toJavaList(String.class);
        }

        // 获取 type 字段
        String type = jsonObject.getString("type");

        return RestResult.success(this.service.deleteStructData(structIdList, type));
    }
}
