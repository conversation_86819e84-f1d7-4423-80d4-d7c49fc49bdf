package com.hualu.app.module.mems.machineclean.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.machineclean.entity.DmMachineCleaning;
import com.hualu.app.module.mems.machineclean.entity.MachineCleaningTrack;
import com.hualu.app.module.mems.machineclean.mapper.DmMachineCleaningMapper;
import com.hualu.app.module.mems.machineclean.service.DmMachineCleaningService;
import com.hualu.app.module.mems.machineclean.service.MachineCleaningTrackService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class DmMachineCleaningServiceImpl
    extends ServiceImpl<DmMachineCleaningMapper, DmMachineCleaning>
    implements DmMachineCleaningService {

  @Resource
  private MachineCleaningTrackService trackService;

  @Override public RestResult<List<DmMachineCleaning>> getMachineCleanList(
      int page,
      int pageSize
  ) {
    IPage<DmMachineCleaning> p = new Page<>(page, pageSize);
    LambdaQueryWrapper<DmMachineCleaning> wrapper = new LambdaQueryWrapper<>();
    wrapper.in(DmMachineCleaning::getOrgId, H_DataAuthHelper.selectOrgIds());
    wrapper.orderByDesc(DmMachineCleaning::getStartTime);
    wrapper.last("NULLS LAST");
    IPage<DmMachineCleaning> ps = getBaseMapper().getMachineCleanList(p, wrapper);
    return RestResult.success(
        ps.getRecords(),
        ps.getTotal(),
        ps.getCurrent(),
        ps.getSize()
    );
  }

  @Override public RestResult<String> saveMachineClean(
      DmMachineCleaning machineClean,
      MultipartFile startImage,
      MultipartFile endImage
  ) {
    if (startImage != null) {
      List<String> beforeFileIds = H_UploadHelper.uploadFile(new MultipartFile[] {startImage});
      if (!CollectionUtils.isEmpty(beforeFileIds)) {
        machineClean.setStartImage(beforeFileIds.get(0));
      }
    }

    if (endImage != null) {
      List<String> afterFileIds = H_UploadHelper.uploadFile(new MultipartFile[] {endImage});
      if (!CollectionUtils.isEmpty(afterFileIds)) {
        machineClean.setEndImage(afterFileIds.get(0));
      }
    }

    machineClean.setOrgId(CustomRequestContextHolder.getOrgIdString());
    machineClean.setCreateUserId(CustomRequestContextHolder.getUserId());
    machineClean.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    return saveOrUpdate(machineClean) ? RestResult.success("保存成功")
        : RestResult.error("保存失败");
  }

  @Transactional(rollbackFor = Exception.class)
  @Override public RestResult<String> uploadTrack(List<MachineCleaningTrack> tracks) {
    if (CollectionUtils.isEmpty(tracks)) {
      return RestResult.success("上传成功,没有轨迹点");
    }

    String machineCleanId = tracks.get(0).getMachineCleanId();
    if (!StringUtils.hasText(machineCleanId)) {
      return RestResult.error("没有机械清扫id");
    }

    //一个trackId表示一条轨迹，可以同时上传多条轨迹
    //同一个trackId表示同一条轨迹上传多次，删除掉原来的
    Map<String, List<MachineCleaningTrack>> trackMap =
        tracks.stream()
            .filter(c -> StringUtils.hasText(c.getTrackId()))
            .collect(Collectors.groupingBy(MachineCleaningTrack::getTrackId));

    //表示轨迹没有groupId,如果走下面逻辑会将其他轨迹删除
    if (CollectionUtils.isEmpty(trackMap.keySet())) {
      return RestResult.success("上传成功,没有轨迹id");
    }

    //删除原来的轨迹
    LambdaQueryWrapper<MachineCleaningTrack> wrapper = new LambdaQueryWrapper<>();
    wrapper
        .eq(MachineCleaningTrack::getMachineCleanId, machineCleanId)
        .in(
            !CollectionUtils.isEmpty(trackMap.keySet()),
            MachineCleaningTrack::getTrackId,
            trackMap.keySet()
        );
    trackService.remove(wrapper);

    boolean successful = trackService.saveBatch(tracks);
    return successful ? RestResult.success("上传成功") : RestResult.error("上传失败");
  }

  @Transactional(rollbackFor = Exception.class)
  @Override public RestResult<String> deleteMachineClean(String machineCleanId) {
    //删除机械清扫记录
    boolean successful = removeById(machineCleanId);

    //删除机械清扫轨迹
    if (StringUtils.hasText(machineCleanId)) {
      LambdaQueryWrapper<MachineCleaningTrack> wrapper = new LambdaQueryWrapper<>();
      wrapper.eq(MachineCleaningTrack::getMachineCleanId, machineCleanId);
      successful = successful && trackService.remove(wrapper);
    }

    return successful ? RestResult.success("删除成功") : RestResult.error("删除失败");
  }

  @Override public RestResult<List<MachineCleaningTrack>> getTrack(String machineCleanId) {
    LambdaQueryWrapper<MachineCleaningTrack> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(
        StringUtils.hasText(machineCleanId),
        MachineCleaningTrack::getMachineCleanId, machineCleanId);
    wrapper.orderByAsc(MachineCleaningTrack::getRecordTime);
    return RestResult.success(trackService.list(wrapper));
  }

  @Override
  public RestResult<String> deleteMachineCleanImage(
      String machineCleanId,
      String fileId,
      boolean whetherBeforeImage
  ) {
    LambdaUpdateWrapper<DmMachineCleaning> wrapper = new LambdaUpdateWrapper<>();
    wrapper.eq(DmMachineCleaning::getMcId, machineCleanId);
    if (whetherBeforeImage) {
      wrapper.set(DmMachineCleaning::getStartImage, null);
    } else {
      wrapper.set(DmMachineCleaning::getEndImage, null);
    }
    boolean successful = update(wrapper);
    return RestResult.success(successful ? "删除成功" : "删除失败");
  }

  @Override public RestResult<DmMachineCleaning> getLastMachineCleanRecord() {
    LambdaQueryWrapper<DmMachineCleaning> wrapper = new LambdaQueryWrapper<>();
    wrapper.in(DmMachineCleaning::getOrgId, H_DataAuthHelper.selectOrgIds());
    wrapper.orderByDesc(DmMachineCleaning::getStartTime);
    wrapper.last("NULLS LAST");
    IPage<DmMachineCleaning> p = new Page<>(1, 1);
    IPage<DmMachineCleaning> ps = getBaseMapper().getMachineCleanList(p, wrapper);
    List<DmMachineCleaning> records = ps.getRecords();
    DmMachineCleaning dmMachineCleaning = null;
    if (CollectionUtils.isEmpty(records)) {
      dmMachineCleaning = records.get(0);
      dmMachineCleaning.setMcId(null);
      dmMachineCleaning.setStartImage(null);
      dmMachineCleaning.setEndImage(null);
      dmMachineCleaning.setEndTime(null);
    } else {
      dmMachineCleaning = new DmMachineCleaning();
    }
    return RestResult.success(dmMachineCleaning, "");
  }

}
