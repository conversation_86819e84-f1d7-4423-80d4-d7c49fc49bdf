package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspMapper;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspRecordMapper;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspResultMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.struct.IStruct;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.vo.NmFinspRel;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.dto.WorkPartinameDto;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.hualu.app.module.workflow.service.WfworkitemService;
import com.hualu.app.utils.H_InspCodeHelper;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.hualu.app.utils.nminsp.H_NmInspHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import io.minio.MinioClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.usermodel.*;
import org.apache.poi.xwpf.usermodel.*;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTbl;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblGrid;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STTblLayoutType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 新版经常巡查 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@Service
public class NmFinspServiceImpl extends ServiceImpl<NmFinspMapper, NmFinsp> implements NmFinspService, IWorkItemEventHandler, OrderInfoHandler {

    public static String finspCode = "JCJC-{}-{}-{}";

    // 格式：XCD-设施类型-机构编码-日期（示例：XCD-BP-***********）
    private static final String FINSP_CODE_TEMPLATE = "JCJC-{}-{}-{}";

    private static final String INSP_TIME = "09:00:00-18:00:00";

    // 2. 线程安全的日期格式化（Java8 DateTime API）
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd")
            .withZone(ZoneId.of("Asia/Shanghai"));

    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM")
            .withZone(ZoneId.of("Asia/Shanghai"));

    private static String processDefName="gdcg.emdc.mems.dm.NFinspWorkFlow";

    @Value("${resource.path}")
    private String filepath;

    @Autowired
    FwRightUserService userService;

    @Autowired
    DssImageService dssImageService;

    @Autowired
    NmFinspResultService resultService;

    @Autowired
    DssInfoService dssInfoService;

    @Autowired
    NmFinspRecordService recordService;

    @Autowired
    NmFinspRecordMapper nmFinspRecordMapper;
    @Autowired
    NmFinspResultMapper nmFinspResultMapper;

    @Autowired
    private BaseLineService lineService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    BaseRouteLogicService logicService;

    @Autowired
    WfworkitemService wfworkitemService;

    @Resource
    MinioClient minioClient;

    @Resource
    NmFinspMapper nmFinspMapper;

    Map<String, Integer> imageCache = new HashMap<>();

    @Override
    public NmFinsp initNmFinsp(String facilityCat, String orgIdString,String range) {
        NmFinsp nmDinsp = baseMapper.lastNmFinsp(facilityCat,orgIdString);
        if (nmDinsp == null) {
            nmDinsp = new NmFinsp();
            nmDinsp.setWeather("01");//表示晴天
        }else {
            nmDinsp.setFinspId(null);
            nmDinsp.setDssNum(0);
            nmDinsp.setProcessinstid(null);
        }
        if (StrUtil.isBlank(nmDinsp.getSearchDept())){
            nmDinsp.setSearchDept(userService.getDeptName(CustomRequestContextHolder.getUserId()));
        }
        if (StrUtil.isBlank(nmDinsp.getMntOrgNm())){
            nmDinsp.setMntOrgNm(CustomRequestContextHolder.getOrgName());
        }
        String orgEn = CustomRequestContextHolder.get("ORG_EN")==null?"":CustomRequestContextHolder.get("ORG_EN").toString();
        if(StringUtils.isNotBlank(range)){
            orgEn = range;
        }
        nmDinsp.setFinspCode(getNextCode(facilityCat,orgEn,LocalDate.now()));
        nmDinsp.setInspPerson(CustomRequestContextHolder.getUserName());
        nmDinsp.setFacilityCat(facilityCat);
        nmDinsp.setMntOrgId(orgIdString);
        //路面需要用到巡查时间
        nmDinsp.setInspTime("09:00:00-18:00:00");
        nmDinsp.setInspDate(new Date());
        nmDinsp.setCreateTime(null).setUpdateTime(null).setCreateUserId(null).setUpdateUserId(null);
        // 自动设置边坡的检查类型
        nmDinsp.setInspFrequency(H_NmInspHelper.buildInspFrequencyByFinsp(facilityCat,new Date(),nmDinsp.getInspFrequency()));
        return nmDinsp;
    }

    @Override
    public String getNextCode(String facilityCat, String orgEn,LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return String.format("%s-%04d", generateBaseCode(facilityCat, orgEn,localDate), getNextSerial(facilityCat, orgEn,localDate)+1);
    }

    /**
     * 组装单号
     * @param facilityCat
     * @param orgEn
     * @param serial
     * @return
     */
    private String buildFinspCode(String facilityCat, String orgEn,int serial,LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return String.format("%s-%04d", generateBaseCode(facilityCat, orgEn,localDate), serial);
    }

    public Integer getNextSerial(String facilityCat, String orgEn,LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return queryMaxSerialFromDB(facilityCat, orgEn,localDate);
    }

    // 提取公共方法：参数校验
    private void validateParams(String facilityCat, String orgEn,LocalDate localDate) {
        Objects.requireNonNull(facilityCat, "设施类型不能为空[2,9](@ref)");
        Objects.requireNonNull(orgEn, "机构编码不能为空[2,9](@ref)");
    }

    // 提取公共方法：生成基础编码
    private String generateBaseCode(String facilityCat, String orgEn,LocalDate localDate) {
        return StrUtil.format(FINSP_CODE_TEMPLATE,
                facilityCat,
                orgEn.toUpperCase(),
                localDate.format(DATE_FORMATTER)
        );
    }

    /**
     * 按照月份进程查询
     * @param facilityCat
     * @param orgEn
     * @param localDate
     * @return
     */
    private String generateBaseCodeByMonth(String facilityCat, String orgEn,LocalDate localDate) {
        return StrUtil.format(FINSP_CODE_TEMPLATE,
                facilityCat,
                orgEn.toUpperCase(),
                localDate.format(MONTH_FORMATTER)
        );
    }

    // 优化数据库查询（带日期过滤）
    private Integer queryMaxSerialFromDB(String facilityCat, String orgEn,LocalDate localDate) {
        QueryWrapper<NmFinsp> wrapper = new QueryWrapper<>();
        wrapper.select("COALESCE(MAX(TO_NUMBER(SUBSTR(FINSP_CODE, -4))), 0) AS max_serial")
                .likeRight("FINSP_CODE", generateBaseCodeByMonth(facilityCat, orgEn,localDate))
                .eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString())
                .apply("REGEXP_LIKE(SUBSTR(FINSP_CODE, -4), '^[0-9]+$')");

        return Optional.ofNullable(getObj(wrapper, obj -> Integer.parseInt(obj.toString())))
                .orElse(0);
    }

    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void saveOrUpdateNmFinsp(NmFinsp nmDinsp) {

        IStruct bean = H_StructHelper.getStructBean(nmDinsp.getFacilityCat());
        bean.validateNmFinsp(nmDinsp);
        boolean isAdd = StrUtil.isBlank(nmDinsp.getFinspId())?true:false;
        if (isAdd) {
            Integer mntPerson = userService.isMntPerson(CustomRequestContextHolder.getUserCode());
            nmDinsp.setXcType(mntPerson);
            nmDinsp.setFinspId(H_KeyWorker.nextIdToString());
            // 根据路线编码生成路线名称
            nmDinsp.setLineName(lineService.getLineName(nmDinsp.getLineCode()));
            //验证单号，如果单号中的日期与巡查日期不一致，重新赋值
            if (!H_InspCodeHelper.checkCode(nmDinsp.getFinspCode(),nmDinsp.getInspDate())){
                String orgEn = CustomRequestContextHolder.get("ORG_EN")==null?"":CustomRequestContextHolder.get("ORG_EN").toString();
                String nextCode = getNextCode(nmDinsp.getFacilityCat(), orgEn, DateTimeUtil.toLocalDate(nmDinsp.getInspDate()));
                nmDinsp.setFinspCode(nextCode);
            }
            if (nmDinsp.getProcessinstid() == null || nmDinsp.getProcessinstid() == 0) {
                long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "经常检查单["+ nmDinsp.getFinspCode()+"]", "");
                log.info("经常检查实例ID："+processInstId);
                nmDinsp.setProcessinstid(processInstId);
                nmDinsp.setStatus(0);
            }
            // 生成检查结论
            bean.refreshNmFinspResult(nmDinsp);
        }else {
            // 修改边坡经常检查单时，若前台传入检查日期与数据库存储的月份不符，后台需自动更新检查结论表。
            H_NmInspHelper.updateNmFinspResult(nmDinsp);
        }
        saveOrUpdate(nmDinsp);
        //设置检查工照
        dssImageService.saveDssImageForPC(nmDinsp.getFinspId(),nmDinsp.getFileIds(),7);
        //正面照
        dssImageService.saveDssImageForPC(nmDinsp.getFinspId(),nmDinsp.getZmPictureIds(),8);
        //侧面照
        dssImageService.saveDssImageForPC(nmDinsp.getFinspId(),nmDinsp.getCmPictureIds(),9);

        updateDssNum(nmDinsp.getFinspId());
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delBatch(List<String> inspIds) {
        //删除检查结论
        resultService.delBatchByDinspIds(inspIds);
        //删除病害信息
        recordService.deleteRecordByFinspIds(inspIds);

        // 首页待办中不显示新版日常巡查待办任务，所以流程不进行物理删除（加快响应速度） 20250516
        //删除流程信息
        /*LambdaQueryWrapper<NmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(NmFinsp::getFinspId,inspIds).select(NmFinsp::getProcessinstid);
        List<NmFinsp> nmDinspList = list(queryWrapper);

        // 批量处理只有一个流程实例，删除时，需特殊处理
        Map<Long, Long> processMap = nmDinspList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getProcessinstid())).collect(Collectors.groupingBy(NmFinsp::getProcessinstid, Collectors.counting()));
        Map<Long, Long> dbInstMap = countProcessMap(processMap.keySet());
        for (Long instId : processMap.keySet()) {
            Long vCount = processMap.get(instId);
            // 如果前端删除的单量 >= 数据库中的单量，则删除流程
            Long dbCount = dbInstMap.getOrDefault(instId, 0L);
            if (vCount >= dbCount){
                H_WorkFlowHelper.deleteProcessInstance(instId);
            }
        }*/
        removeByIds(inspIds);
    }

    private Map<Long,Long> countProcessMap(Set<Long> processIds) {
        QueryWrapper<NmFinsp> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("count(*) as process_Count,processinstid").in("processinstid",processIds).groupBy("processinstid");
        List<NmFinsp> nmDinspList = list(queryWrapper);
        Map<Long, Long> processMap = nmDinspList.stream().filter(Objects::nonNull).collect(Collectors.toMap(NmFinsp::getProcessinstid, NmFinsp::getProcessCount, (exists, replace) -> exists));
        return processMap;
    }

    @Override
    public void updateNmFinspStatus(long processInstId, int status) {
        baseMapper.updateStatus(processInstId, status);
    }

    @Override
    public List<NmFinsp> getNmFinspByProcessInstId(Long processInstId) {
        LambdaQueryWrapper<NmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmFinsp::getProcessinstid,processInstId);
        return list(queryWrapper);
    }

    @Override
    public void updateDssNum(String dinspId) {
        baseMapper.updateDssNum(dinspId);
        createProcess(dinspId);
    }

    @SneakyThrows
    private void createProcess(String dinspId) {
        NmFinsp nmFinsp = getById(dinspId);
        Integer count = lambdaQuery().eq(NmFinsp::getProcessinstid, nmFinsp.getProcessinstid()).count();
        if (count > 1 && nmFinsp.getDssNum() > 0) {
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "经常检查", "");
            nmFinsp.setProcessinstid(processInstId);
            updateById(nmFinsp);
        }
    }

    @Override
    public DownloadWordDto exportWord(String finspIds, String facilityCat) {
        IStruct bean = H_StructHelper.getStructBean(facilityCat);
        //word导出公用地址
        String parentPath = H_WordHelper.getParentPath(facilityCat,"NmFinsp");
        List<NmFinsp> nmDinspList = Lists.newArrayList();
        ListUtil.partition(StrUtil.split(finspIds,","),500).forEach(rows->{
            nmDinspList.addAll(lambdaQuery().in(NmFinsp::getFinspId, rows).list());
        });

        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String signType = request.getParameter("signType");
        // 日常巡查单公共数据回显
        //回显负责人
        Set<Long> processInstIdSet = nmDinspList.stream().filter(e -> ObjectUtil.isNotEmpty(e.getProcessinstid())).map(NmFinsp::getProcessinstid).collect(Collectors.toSet());
        Map<Long, WorkPartinameDto> processMap = wfworkitemService.resolveApproverMap(processInstIdSet, "manualActivity2");
        // 回显天气、路面类型
        nmDinspList.forEach(item->{
            String weatherName = dicService.getDicName("WEATHER", item.getWeather());
            WorkPartinameDto workDto = processMap.getOrDefault(item.getProcessinstid(), new WorkPartinameDto());
            String learder = workDto.getPartiname();
            if("1".equals(signType)){
                learder = "   fz1      fz2      fz3";
                item.setInspPerson("    jl");
            }
            item.setWeatherName(weatherName).setLeader(learder).setLeaderTitle(workDto.getLeaderTitle());
            if ((StrUtil.isBlank(item.getLineName()) || item.getLineName().trim().equals("null")) && StrUtil.isNotBlank(item.getLineCode())) {
                item.setLineName(lineService.getLineName(item.getLineCode()));
            }
        });
        List<String> wordPath = bean.processAndExportNmFinspWord(nmDinspList, parentPath);
        return new DownloadWordDto().setParentPath(parentPath).setWordPaths(wordPath);
    }


    @Override
    public List<String> getDssInfoImageExport(String finspIds, String facilityCat, String path) {
        List<String> strings = Arrays.asList(finspIds.split(","));
        List<RoadInspectionRecord> records = Lists.newArrayList();
        ListUtil.partition(strings,500).forEach(items->{
            records.addAll(this.baseMapper.getDssInfoFImageExport(items, facilityCat));
        });
        List<String> pahtArr = new ArrayList<>();

        Map<String, List<RoadInspectionRecord>> collect
                = records.stream().collect(Collectors.groupingBy(RoadInspectionRecord::getDinspCode));
        collect.entrySet().stream().forEach(v ->
        {
            String key = v.getKey();
            List<RoadInspectionRecord> value = v.getValue();
            MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
            // 创建文档
            XWPFDocument doc = new XWPFDocument();
            String dinspCode = key;
            // 添加标题
            if (!value.isEmpty()) {
                XWPFParagraph title = doc.createParagraph();
                title.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun run = title.createRun();
                run.setBold(true);
                run.setFontSize(16);
                run.setText(dinspCode);
            }

            // 分组每两条数据
            for (int i = 0; i < value.size(); i += 2) {
                RoadInspectionRecord left = value.get(i);
                RoadInspectionRecord right = (i + 1 < value.size()) ? value.get(i + 1) : null;

                if (!"LM".equals(facilityCat) && !"JA".equals(facilityCat)) {
                    //查询结构物的中心桩号
                    IStruct bean = H_StructHelper.getStructBean(facilityCat);
                    BaseStructDto structInfo = bean.getStructInfo(facilityCat, left.getStructId());
                    left.setStructStake(structInfo.getStructName());

                    if (right != null && !StringUtils.isBlank(right.getStructId())) {
                        IStruct beanRight = H_StructHelper.getStructBean(facilityCat);
                        BaseStructDto structInfoRight = beanRight.getStructInfo(facilityCat, right.getStructId());
                        right.setStructStake(structInfoRight.getStructName());
                    }
                }

                // 第一行：图片
                XWPFTable table = doc.createTable(2, 2); // 两行两列：第一行图片，第二行文字
                table.setWidth("100%");
                table.getCTTbl().getTblPr().addNewTblLayout()
                        .setType(STTblLayoutType.FIXED);
                CTTbl ctTbl = table.getCTTbl();
                CTTblGrid tblGrid = ctTbl.addNewTblGrid();
                tblGrid.addNewGridCol().setW(BigInteger.valueOf(4500)); // 左列宽度
                tblGrid.addNewGridCol().setW(BigInteger.valueOf(4500)); // 右列宽度
                // 图片行
                insertImageToCell(table.getRow(0).getCell(0), left, minioProp);
                if (right != null) insertImageToCell(table.getRow(0).getCell(1), right, minioProp);

                // 文字信息行
                setTextToCell(table.getRow(1).getCell(0),
                        "线路编码：" + safe(left.getLineCode()),
                        "桩号：" + safe(StringUtils.isBlank(left.getStructStake()) ? left.getStake() : left.getStructStake()),
                        "日期：" + formatDate(left.getInspDate()),
                        "病害：" + safe(left.getPavementType()),
                        "检查人：" + safe(left.getInspPerson()));
                if (right != null) setTextToCell(table.getRow(1).getCell(1),
                        "线路编码：" + safe(right.getLineCode()),
                        "桩号：" + safe(StringUtils.isBlank(right.getStructStake()) ? right.getStake() : right.getStructStake()),
                        "日期：" + formatDate(right.getInspDate()),
                        "病害：" + safe(right.getPavementType()),
                        "检查人：" + safe(right.getInspPerson()));

                // 空行分隔
                doc.createParagraph();
            }

            // 写入文件
            String substring = path.substring(0, path.lastIndexOf("\\") + 1) + dinspCode + ".docx";
            FileOutputStream out = null;
            try {
                out = new FileOutputStream(substring);
                doc.write(out);
                out.close();
                doc.close();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                pahtArr.add(substring);
            }
        });
        return pahtArr;
    }


    // 插入图片到单元格
    private void insertImageToCell(XWPFTableCell cell, RoadInspectionRecord record, MinioProp minioProp) {
        cell.getCTTc().addNewTcPr().addNewTcW().setW(BigInteger.valueOf(4500));
        InputStream inputStream = null;
        try {
            String fileEntityPath = record.getFileEntityPath();
            File imgFile = new File(filepath + record.getFileEntityPath());
            String bucketName = minioProp.getBucketName();
            String objectName = fileEntityPath.replaceAll(bucketName, "");
            inputStream = minioClient.getObject(minioProp.getBucketName(), objectName);
            byte[] bytes = toByteArray(inputStream);
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
            inputStream = new ByteArrayInputStream(bytes);
            XWPFParagraph para = cell.addParagraph();
            XWPFRun run = para.createRun();
            int pictureTypePng = Workbook.PICTURE_TYPE_PNG;
            if (fileEntityPath.toUpperCase().contains("JPG"))
            {
                pictureTypePng = Workbook.PICTURE_TYPE_JPEG;
            }
            run.addPicture(inputStream, pictureTypePng, imgFile.getName(),
                    Units.toEMU((int)(210)), Units.toEMU(230)); // 设置图片大小
            cell.removeParagraph(0); //删除默认段落
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            IoUtil.close(inputStream);
        }
    }
    public static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[4096];
        int nRead;
        while ((nRead = input.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        return buffer.toByteArray();
    }

    // 设置文字内容到单元格
    private void setTextToCell(XWPFTableCell cell, String... lines) {
        cell.removeParagraph(0); // 移除默认段落

        XWPFParagraph para = cell.addParagraph();
        para.setAlignment(ParagraphAlignment.LEFT); // 设置左对齐
        para.setSpacingBetween(1.0);

        XWPFRun run = para.createRun();
        run.setFontSize(10);

        for (int i = 0; i < lines.length; i++) {
            run.setText(lines[i]);
            if (i != lines.length - 1) {
                run.addBreak(); // 显式换行
            }
        }
    }

    private String safe(Object val) {
        return val == null ? "" : val.toString();
    }
    private String formatDate(Date date) {
        if (date == null) return "";
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }


    @Override
    public IPage<RouteInspection> DailyInspectionLedger(IPage page,
                                                        String dinspCode,
                                                        String facilityCats,
                                                        String lineCode,
                                                        String startStake,
                                                        String endStake,
                                                        String startDate,
                                                        String endDate,
                                                        Map reqParam){
        //获取当前用户的机构ID
        MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
        //获取桶名称
        String bucketName = minioProp.getBucketName();

        Double start = null;
        if (!StringUtils.isBlank(startStake))
        {
            start = Double.valueOf(startStake);
        }

        Double end = null;
        if (!StringUtils.isBlank(endStake))
        {
            end = Double.valueOf(endStake);
        }

        String code = null;
        if (!StringUtils.isBlank(dinspCode))
        {
            code = "%"+dinspCode+"%";
        }

        //过滤筛选数据
        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        String dssTypeNameStr = getStringParam(reqParam, "dssTypeName");
        String facilityCategoryStr = getStringParam(reqParam, "facilityCategory");
        String lineDirectStr = getStringParam(reqParam, "lineDirect");
        String routeNameStr = getStringParam(reqParam, "routeName");
        String mtaskCodeStr = getStringParam(reqParam, "mtaskCode");
        String mtaskAccptCodeStr = getStringParam(reqParam, "mtaskAccptCode");

        IPage<RouteInspection> routeInspections =  this.baseMapper.DailyInspectionLedger(page, orgIdString, code, facilityCats,
                lineCode, start, end, startDate, endDate, dssTypeNameStr, facilityCategoryStr, lineDirectStr, routeNameStr);
        List<RouteInspection> records = routeInspections.getRecords();
        List<String> collect = records.stream().map(RouteInspection::getDssId).collect(Collectors.toList());
        if (collect != null && collect.size() > 0)
        {
            List<MtaskEntity> dssInfoId = this.baseMapper.findDssInfoId(collect, mtaskCodeStr, mtaskAccptCodeStr);
            Map<String, MtaskEntity> collect1 = dssInfoId.stream().collect(Collectors.toMap(MtaskEntity::getDssId, Function.identity()));
            if (records != null && records.size() > 0)
            {
                records.stream().forEach(v ->
                {
                    String facilityCat = null;
                    String facilityCategory = v.getFacilityCategory();
                    if (!"路面".equals(facilityCategory) && !"交安".equals(facilityCategory))
                    {
                        switch (facilityCategory){
                            case "边坡":
                                facilityCat = "BP";
                                break;
                            case "桥梁":
                                facilityCat = "QL";
                                break;
                            case "涵洞":
                                facilityCat = "HD";
                                break;
                            case "隧道":
                                facilityCat = "SD";
                                break;
                        }
                        IStruct bean = H_StructHelper.getStructBean(facilityCat);
                        BaseStructDto structInfo = bean.getStructInfo(facilityCat, v.getStructId());
                        v.setStake(structInfo.getStructName());
                    }
                    try {
                        String dssId = v.getDssId();
                        if(collect1.containsKey(dssId))
                        {
                            MtaskEntity mtaskEntity = collect1.get(dssId);
                            v.setMtaskCode(mtaskEntity.getMtaskCode());
                            v.setMtaskAccptCode(mtaskEntity.getMtaskAccptCode());
                            v.setRepairDate(mtaskEntity.getRepairDate());
                        }
                        String fileEntityPath = v.getFileEntityId();
                        if (!StringUtils.isBlank(fileEntityPath))
                        {
                            String[] split = fileEntityPath.split(",");
                            v.setPothoNums(split.length);
                        }
                        v.setFileUrl(fileEntityPath);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            }
        }
        if (StringUtils.isNotBlank(mtaskCodeStr)) {
            routeInspections.getRecords().removeIf(r ->
                    !StringUtils.equals(r.getMtaskCode(), mtaskCodeStr));
        }

        if (StringUtils.isNotBlank(mtaskAccptCodeStr)) {
            routeInspections.getRecords().removeIf(r ->
                    !StringUtils.equals(r.getMtaskAccptCode(), mtaskAccptCodeStr));
        }
        if (StringUtils.isNotBlank(mtaskCodeStr) || StringUtils.isNotBlank(mtaskAccptCodeStr))
        {
            // 8. 更新分页信息
            routeInspections.setTotal(routeInspections.getRecords().size());
            routeInspections.setPages((int) Math.ceil((double) routeInspections.getTotal() / page.getSize()));
        }
        return routeInspections;
    }

    @Override
    public void exportDailyInspection(HttpServletResponse response,
                                      IPage page,
                                      String dinspCode,
                                      String facilityCats,
                                      String lineCode,
                                      String startStake,
                                      String endStake,
                                      String startDate,
                                      String endDate,
                                      Map reqParam){
        //获取当前用户的机构ID
        MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
        //获取桶名称
        String bucketName = minioProp.getBucketName();

        Double start = null;
        if (!StringUtils.isBlank(startStake))
        {
            start = Double.valueOf(startStake);
        }

        Double end = null;
        if (!StringUtils.isBlank(endStake))
        {
            end = Double.valueOf(endStake);
        }

        String code = null;
        if (!StringUtils.isBlank(dinspCode))
        {
            code = "%"+dinspCode+"%";
        }

        //过滤筛选数据
        String dssTypeNameStr = getStringParam(reqParam, "dssTypeName");
        String facilityCategoryStr = getStringParam(reqParam, "facilityCategory");
        String lineDirectStr = getStringParam(reqParam, "lineDirect");
        String routeNameStr = getStringParam(reqParam, "routeName");
        String mtaskCodeStr = getStringParam(reqParam, "mtaskCode");
        String mtaskAccptCodeStr = getStringParam(reqParam, "mtaskAccptCode");

        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        IPage<RouteInspection> routeInspections =  this.baseMapper.DailyInspectionLedger(page, orgIdString, code, facilityCats,
                lineCode, start, end, startDate, endDate, dssTypeNameStr, facilityCategoryStr, lineDirectStr, routeNameStr);
        String routeName = "";
        List<RouteInspection> records =filterDuplicateRecords(routeInspections.getRecords());
        Optional<RouteInspection> first = records.stream().findFirst();
        if (first.isPresent())
        {
            RouteInspection routeInspection = first.get();
            routeName = routeInspection.getRouteName();
        }

        List<String> collect = records.stream().map(RouteInspection::getDssId).distinct().collect(Collectors.toList());
        if (collect != null && collect.size() > 0)
        {
            List<MtaskEntity> dssInfoId = CollUtil.split(collect, 1000).stream()
                    .flatMap(batch -> this.baseMapper.findDssInfoId(batch, mtaskCodeStr, mtaskAccptCodeStr).stream())
                    .collect(Collectors.toList());
            Map<String, MtaskEntity> collect1 = dssInfoId.stream().collect(Collectors.toMap(MtaskEntity::getDssId, Function.identity()));
            if (records != null && records.size() > 0)
            {
                Map<String, List<RouteInspection>> collect2 = records.stream().filter(v -> !StringUtils.isBlank(v.getFacilityCategory()))
                        .collect(Collectors.groupingBy(RouteInspection::getFacilityCategory));
                Map<String, Map<String, BaseStructDto>> structListMap = new HashMap<>();
                collect2.entrySet().stream().forEach(v ->
                {
                    String facilityCat = null;
                    String facilityCategory = v.getKey();
                    List<RouteInspection> value = v.getValue();
                    List<String> collect3 = value.stream().map(RouteInspection::getStructId).distinct().collect(Collectors.toList());
                    if (!"路面".equals(facilityCategory) && !"交安".equals(facilityCategory)) {
                        switch (facilityCategory) {
                            case "边坡":
                                facilityCat = "BP";
                                break;
                            case "桥梁":
                                facilityCat = "QL";
                                break;
                            case "涵洞":
                                facilityCat = "HD";
                                break;
                            case "隧道":
                                facilityCat = "SD";
                                break;
                        }
                        IStruct bean = H_StructHelper.getStructBean(facilityCat);
                        String finalFacilityCat = facilityCat;
                        List<BaseStructDto> structInfos = CollUtil.split(collect3, 1000).stream()
                                .flatMap(batch -> bean.getStructInfos(finalFacilityCat, batch).stream())
                                .collect(Collectors.toList());
                        Map<String, BaseStructDto> collect4 = structInfos.stream().collect(Collectors.toMap(BaseStructDto::getStructId, Function.identity()));
                        structListMap.put(facilityCategory, collect4);
                    }
                });
                records.stream().forEach(v ->
                {
                    InputStream stream = null;
                    try {
                        String dssId = v.getDssId();
                        String facilityCategory = v.getFacilityCategory();
                        if (structListMap.containsKey(facilityCategory))
                        {
                            Map<String, BaseStructDto> stringListMap = structListMap.get(facilityCategory);
                            String structId = v.getStructId();
                            if (!StringUtils.isBlank(structId))
                            {
                                BaseStructDto baseStructDtos = stringListMap.get(structId);
                                v.setStake(baseStructDtos.getStructName());
                            }
                        }
                        if(collect1.containsKey(dssId))
                        {
                            MtaskEntity mtaskEntity = collect1.get(dssId);
                            v.setMtaskCode(mtaskEntity.getMtaskCode());
                            v.setMtaskAccptCode(mtaskEntity.getMtaskAccptCode());
                            v.setRepairDate(mtaskEntity.getRepairDate());
                        }
                       /* String fileEntityPath = v.getFileEntityPath();
                        String objectName = fileEntityPath.replaceAll(bucketName, "");
                        stream = minioClient.getObject(minioProp.getBucketName(), objectName);
                        v.setProductImage(toByteArray(stream));*/
                    } catch (Exception e) {
                        e.printStackTrace();
                    }finally {
                        IoUtil.close(stream);
                    }
                });
                if (StringUtils.isNotBlank(mtaskCodeStr)) {
                    records.removeIf(r ->
                            !StringUtils.equals(r.getMtaskCode(), mtaskCodeStr));
                }

                if (StringUtils.isNotBlank(mtaskAccptCodeStr)) {
                    records.removeIf(r ->
                            !StringUtils.equals(r.getMtaskAccptCode(), mtaskAccptCodeStr));
                }
            }
        }
        XSSFWorkbook workbook = null;
        try {
            String fileName = routeName + "高速巡查检查病害处治台账.xlsx";
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            // 7. 创建临时文件
            try {
                response.setHeader("Content-Disposition",
                        "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            // 8. 初始化工作簿（使用磁盘缓存）
            workbook = new XSSFWorkbook(); // 保留100行在内存中

            // 9. 创建工作表
            XSSFSheet sheet = workbook.createSheet("巡查记录");
            CreationHelper helper = workbook.getCreationHelper();
            // 设置列宽（如第6列用于图片）
            sheet.setColumnWidth(5, 20 * 256); // 第6列宽度

            // 1. 添加标题行（第一行），合并单元格并居中
            XSSFRow titleRow = sheet.createRow(0);
            titleRow.setHeightInPoints(30); // 设置行高

            // 合并单元格 A1:F1（0到5列）
            CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 14);
            sheet.addMergedRegion(mergedRegion);

            XSSFCell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(routeName + "高速巡查检查病害处治台账");
            CellStyle titleStyle = workbook.createCellStyle();
            Font font = workbook.createFont();
            font.setFontName("仿宋");                      // 设置字体为仿宋
            font.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
            font.setBold(true);                           // 加粗
            titleStyle.setFont(font);

            // 设置水平和垂直居中
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            titleCell.setCellStyle(titleStyle);

            // 设置列宽（比如第6列用于图片）
            sheet.setColumnWidth(12, 20 * 256); // 第6列宽度（图片列）

            // 11.创建表头
            XSSFRow header = sheet.createRow(1);
            String[] headers = {
                    "序号", "路段", "路线", "路线方向", "设施分类", "车道（结构物）桩号",
                    "病害类型", "位置及描述", "巡查单号", "发现日期", "任务单号",
                    "验收单单号", "病害照片", "维修日期", "备注"
            };
            for (int i = 0; i < headers.length; i++) {
                header.createCell(i).setCellValue(headers[i]);
                if (i != 12 && i != 0 && i != 2 && i != 3 && i != 4) { // 跳过图片列（索引12）
                    sheet.setColumnWidth(i, 20 * 256); // 第6列宽度（图片列）
                }
                if (i != 5) { // 跳过图片列（索引12）
                    sheet.setColumnWidth(i, 25 * 256); // 第6列宽度（图片列）
                }
            }

            // 12. 设置列宽（特别是图片列）
            sheet.setColumnWidth(12, 20 * 256); // 病害照片列

            // 13. 处理数据行
            int rowIndex = 2;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (RouteInspection record : records) {
                XSSFRow row = sheet.createRow(rowIndex);

                // 填充基本数据
                row.createCell(0).setCellValue(rowIndex - 1); // 序号
                safeSetCellValue(row, 1, record.getRouteName(), workbook);
                safeSetCellValue(row, 2, record.getLineCode(), workbook);
                safeSetCellValue(row, 3, record.getLineDirect(), workbook);
                safeSetCellValue(row, 4, record.getFacilityCategory(), workbook);
                safeSetCellValue(row, 5, record.getStake(), workbook);
                safeSetCellValue(row, 6, record.getDssTypeName(), workbook);
                safeSetCellValue(row, 7, record.getDssDetail(), workbook);
                safeSetCellValue(row, 8, record.getDinspCode(), workbook);
                safeSetCellValue(row, 9, safeFormatDate(sdf, record.getInspDate()), workbook);
                safeSetCellValue(row, 10, record.getMtaskCode(), workbook);
                safeSetCellValue(row, 11, record.getMtaskAccptCode(), workbook);
                safeSetCellValue(row, 13, safeFormatDate(sdf, record.getRepairDate()), workbook);
                XSSFCell cell1 = row.createCell(14);
                cell1.setCellValue(""); // 备注
                safeSetCellBorder(workbook, cell1);

                // 14. 处理病害照片
                if (record.getFileEntityPath() != null) {
                    // 从Minio获取图片
                    String objectName = record.getFileEntityPath().replace(bucketName + "/", "");
                    try(InputStream imageStream = minioClient.getObject(minioProp.getBucketName(), objectName)) {

                        // 添加图片到工作簿
                        int pictureIdx;
                        if (imageCache.containsKey(objectName)) {
                            pictureIdx = imageCache.get(objectName);
                        } else {
                            byte[] imageData = IOUtils.toByteArray(imageStream);
                            byte[] standardizedImageData = standardizeImageFormat(imageData);
                            int pictureTypePng = Workbook.PICTURE_TYPE_PNG;
                            if (objectName.toUpperCase().contains("JPG"))
                            {
                                pictureTypePng = Workbook.PICTURE_TYPE_JPEG;
                            }
                            pictureIdx = workbook.addPicture(standardizedImageData, pictureTypePng);
                            imageCache.put(objectName, pictureIdx);
                        }

                        // 创建锚点并插入图片
                        Drawing<?> drawing = sheet.createDrawingPatriarch();
                        ClientAnchor anchor = helper.createClientAnchor();
                        anchor.setCol1(12); // 病害照片列
                        anchor.setRow1(rowIndex);
                        anchor.setCol2(12); // 跨一列
                        anchor.setRow2(rowIndex); // 跨5行高度

                        drawing.createPicture(anchor, pictureIdx);
                        anchor.setDx2(Units.toEMU((int)(110))); // 宽度控制
                        anchor.setDy2(Units.toEMU((int)(110))); // 高度控制（EMU单位）
                        sheet.setColumnWidth(12, 15 * 256); // 约110像素宽

                        // 调整行高以适应图片
                        row.setHeightInPoints(80);

                    } catch (Exception e) {
                        row.createCell(12).setCellValue("无图片");
                    }

                    // 创建带边框的通用样式（放在创建标题样式之后）
                    CellStyle borderStyle = workbook.createCellStyle();
                    borderStyle.setBorderTop(BorderStyle.THIN);
                    borderStyle.setBorderBottom(BorderStyle.THIN);
                    borderStyle.setBorderLeft(BorderStyle.THIN);
                    borderStyle.setBorderRight(BorderStyle.THIN);
                    borderStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 创建表头边框样式（加粗字体）
                    CellStyle headerBorderStyle = workbook.createCellStyle();
                    Font headerFont = workbook.createFont();
                    headerFont.setFontName("仿宋");
                    headerFont.setBold(true);
                    headerBorderStyle.setFont(headerFont);
                    headerBorderStyle.setBorderTop(BorderStyle.THIN);
                    headerBorderStyle.setBorderBottom(BorderStyle.MEDIUM); // 底部加粗
                    headerBorderStyle.setBorderLeft(BorderStyle.THIN);
                    headerBorderStyle.setBorderRight(BorderStyle.THIN);
                    headerBorderStyle.setAlignment(HorizontalAlignment.CENTER);

                    // 应用标题行边框
                    titleStyle.setBorderTop(BorderStyle.MEDIUM);
                    titleStyle.setBorderBottom(BorderStyle.MEDIUM);
                    titleStyle.setBorderLeft(BorderStyle.MEDIUM);
                    titleStyle.setBorderRight(BorderStyle.MEDIUM);

                    // 创建表头样式（添加居中对齐）
                    CellStyle headerBorderStyle2 = workbook.createCellStyle();
                    Font headerFont2 = workbook.createFont();
                    headerFont2.setFontName("仿宋");
                    headerFont2.setBold(true);
                    headerBorderStyle2.setFont(headerFont2);
                    headerBorderStyle2.setBorderTop(BorderStyle.THIN);
                    headerBorderStyle2.setBorderBottom(BorderStyle.MEDIUM);
                    headerBorderStyle2.setBorderLeft(BorderStyle.THIN);
                    headerBorderStyle2.setBorderRight(BorderStyle.THIN);

                    // 设置表头居中对齐（新增）
                    headerBorderStyle2.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                    headerBorderStyle2.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 创建数据行样式（添加居中对齐）
                    CellStyle borderStyle1 = workbook.createCellStyle();
                    borderStyle1.setBorderTop(BorderStyle.THIN);
                    borderStyle1.setBorderBottom(BorderStyle.THIN);
                    borderStyle1.setBorderLeft(BorderStyle.THIN);
                    borderStyle1.setBorderRight(BorderStyle.THIN);

                    // 设置数据行居中对齐（新增）
                    borderStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                    borderStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    // 应用表头边框
                    for (int i = 0; i < headers.length; i++) {
                        Cell cell = header.getCell(i);
                        if (cell == null) cell = header.createCell(i);
                        cell.setCellStyle(headerBorderStyle);
                    }

                    // 应用数据行边框
                    for (int i = 2; i <= rowIndex; i++) { // 从第3行开始（索引2）
                        Row rows = sheet.getRow(i);
                        if (rows == null) continue;

                        for (int j = 0; j < headers.length; j++) {
                            Cell cell = rows.getCell(j);
                            if (cell == null) {
                                cell = rows.createCell(j);
                                cell.setCellValue(""); // 防止空单元格
                            }
                            cell.setCellStyle(borderStyle);
                        }
                    }
                    sheet.setColumnWidth(0, 5 * 256);
                    sheet.setColumnWidth(2, 5 * 256);
                    sheet.setColumnWidth(3, 5 * 256);
                    sheet.setColumnWidth(4, 5 * 256);
                    sheet.setColumnWidth(4, 5 * 256);
                }
                rowIndex++;
            }

            try {
                workbook.write(response.getOutputStream());
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }


    private String getStringParam(Map<String, Object> reqParam, String key) {
        Object value = reqParam.get(key);
        if (value == null) {
            return null;
        }
        String strValue = value.toString();
        return StrUtil.isBlank(strValue) ? null : strValue;
    }

    private byte[] standardizeImageFormat(byte[] imageData) throws IOException {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(imageData)) {
            BufferedImage bufferedImage = ImageIO.read(bais);
            if (bufferedImage == null) {
                throw new IOException("图片格式不支持，无法读取图片数据");
            }

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "png", baos);
            return baos.toByteArray();
        }catch (Exception e)
        {
            e.printStackTrace();
        }
        return imageData;
    }

    public List<RouteInspection> filterDuplicateRecords(List<RouteInspection> records) {
        // 先按 dssId 分组
        Map<String, List<RouteInspection>> grouped = records.stream()
                .collect(Collectors.groupingBy(RouteInspection::getDssId));

        // 过滤后新列表
        List<RouteInspection> filteredList = new ArrayList<>();

        for (Map.Entry<String, List<RouteInspection>> entry : grouped.entrySet()) {
            List<RouteInspection> groupList = entry.getValue();

            // 先找有没有fileEntityPath不为空的记录
            Optional<RouteInspection> withImage = groupList.stream()
                    .filter(r -> StringUtils.isNotBlank(r.getFileEntityPath()))
                    .findFirst();

            if (withImage.isPresent()) {
                filteredList.add(withImage.get());
            } else {
                // 没有图片则取第一条
                filteredList.add(groupList.get(0));
            }
        }

        return filteredList;
    }

    // 安全设置单元格值
    private void safeSetCellValue(XSSFRow row, int colIndex, Object value, Workbook workbook) {
        if (value == null) {
            XSSFCell cell = row.createCell(colIndex);
            cell.setCellValue("");
            safeSetCellBorder(workbook, cell);
        } else {
            XSSFCell cell = row.createCell(colIndex);
            cell.setCellValue(String.valueOf(value));
            safeSetCellBorder(workbook, cell);
        }
    }
    private void safeSetCellBorder(Workbook workbook, XSSFCell cell)
    {
        //设置边框
        CellStyle borderStyle = workbook.createCellStyle();
        borderStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        borderStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

        borderStyle.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyle.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyle.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyle.setBorderRight(BorderStyle.THIN);  // 右边框
        cell.setCellStyle(borderStyle);
    }

    // 安全格式化时间的方法
    private String safeFormatDate(SimpleDateFormat sdf, Date date) {
        return date == null ? "" : sdf.format(date);
    }

    @Override
    public List<NmFinspSituationShow> querySituationShow(String orgCode, int year) {
        return this.baseMapper.querySituationShow(orgCode, year);
    }

    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void saveBatchNmFinsp(NmFinsp nmfinsp) {
        // 一个路段管理多个路线，当界面新建单为时S51，手动改为G15时，路面名称没有变化，所以每次修改时，需要再次更新路线名称
        String lineName = lineService.getLineName(nmfinsp.getLineCode());
        if (StrUtil.isNotBlank(lineName)){
            nmfinsp.setLineName(lineName);
        }
        nmfinsp.setInspFrequency(H_NmInspHelper.buildInspFrequencyByFinsp(nmfinsp.getFacilityCat(),nmfinsp.getInspDate(),nmfinsp.getInspFrequency()));
        // 1. 增强空值防护与字符串分割
        List<String> structIds = Optional.ofNullable(nmfinsp.getStructId())
                .filter(StrUtil::isNotBlank)
                .map(str -> StrUtil.split(str, ","))
                .orElseGet(Collections::emptyList);

        // 2. 缓存Bean集合避免重复查询 + 防御性编程
        IBaseStructFace structFace = Optional.ofNullable(nmfinsp.getFacilityCat())
                .map(cat -> CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                        .filter(bean -> Objects.equals(bean.getFacilityCat(), cat))
                        .findFirst()
                        .orElseThrow(() -> new BaseException("设施类型[" + cat + "]无对应实现类"))
                )
                .orElseThrow(() -> new BaseException("设施类型字段为空"));

        // 3. 集合空值兜底与数据校验
        List<BaseStructDto> baseStructDtos = structFace.listByStructIds(structIds);
        if (CollectionUtil.isEmpty(baseStructDtos)) {
            log.warn("结构体数据为空，structIds: {}", structIds);
            throw new BaseException("找不到对应的结构物");
        }

        // 4. 并行流加速映射处理（适用于大数据量）
        Set<String> routeCodeSet = baseStructDtos.parallelStream()
                .map(BaseStructDto::getRouteCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toCollection(LinkedHashSet::new));  // 保持顺序

        // 5. 批量查询优化（添加缓存机制）
        Map<String, BaseRouteLogic> routeMap = logicService.resolveRouteNamesByCodes(routeCodeSet);

        //5.1 批量查询路线编码，路线名称
        Set<String> lineCodeSet = routeMap.values().stream().map(BaseRouteLogic::getLineCode).collect(Collectors.toSet());
        Map<String, String> lineMap = lineService.resolveLineNamesByCodes(lineCodeSet);

        Integer xcType = userService.isMntPerson(CustomRequestContextHolder.getUserCode());

        // 6. 预初始化集合容量 + 方法抽取提升可读性
        List<NmFinsp> nmFinspList = new ArrayList<>(baseStructDtos.size());
        baseStructDtos.forEach(item -> nmFinspList.add(buildNmFinsp(nmfinsp, item, routeMap,lineMap,xcType)));

        String code = nmfinsp.getFinspCode();
        String[] parts = code.split("-", -1);
        String orgEn = "";
        if (parts.length >= 3) {
            orgEn = parts[2];
        } else {
            orgEn = Optional.ofNullable(CustomRequestContextHolder.get("ORG_EN"))
                    .map(Object::toString)
                    .orElse("");
        }

        // 根据主单设置其他的单日期
        LocalDate localDate = nmfinsp.getInspDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 8.查询现数据库单号数字，在后面累计
        AtomicInteger nextSerial = new AtomicInteger(getNextSerial(nmfinsp.getFacilityCat(), orgEn,localDate));
        String facilityCat = nmfinsp.getFacilityCat();
        // 9. 赋值单号、流程实例ID
        Map<String,String> structId2finspIdMap = new HashMap<>();
        for (NmFinsp dinsp : nmFinspList) {
            // 每个经常检查都新建一个流程实例（业主有要求，不同的边坡、桥梁发送给不同的业务人员审批）
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "巡查单", "");
            String dinspCode = buildFinspCode(facilityCat, orgEn, nextSerial.incrementAndGet(),localDate);
            dinsp.setFinspCode(dinspCode).setProcessinstid(processInstId);
            if(dinsp.getStructId()!=null){
                structId2finspIdMap.put(dinsp.getStructId(),dinsp.getFinspId());
            }
        }

        if ("QL".equals(facilityCat) || "HD".equals(facilityCat) || "JA".equals(facilityCat) || "SD".equals(facilityCat) || "BP".equals(facilityCat)) {
            String collectStructIds = structIds.stream().collect(Collectors.joining(","));
            List<NmFinspRel> relList = recordService.getLastfinspIdListByStructList(collectStructIds,facilityCat);
            Map<String,String> finspIdMap = new HashMap<>();
            for(NmFinspRel rel:relList){
                for (NmFinsp dinsp : nmFinspList) {
                    if (rel.getLastFinspId() != null&&dinsp.getStructId().equals(rel.getStructId())) {
                        finspIdMap.put(dinsp.getFinspId(), rel.getLastFinspId());
                    }
                }
            }
            Map<String, List<NmFinspRecord>> recordMap = recordService.copyFinspFetail2NewOne(relList, structId2finspIdMap);
            for(NmFinsp dmFinsp:nmFinspList) {
                String lastfinspIdByStruct = finspIdMap.get(dmFinsp.getFinspId());
                if(lastfinspIdByStruct==null){
                    continue;
                }
//                    String lastfinspIdByStruct = recordService.getLastfinspIdByStruct(dmFinsp.getStructId(), facilityCat);
//                String lastfinspIdByStruct =  idMap.get(dmFinsp.getStructId());
//                    recordService.copyFinspFetail2NewOne(lastfinspIdByStruct, dmFinsp.getFinspId(), facilityCat);
                    String finspId = dmFinsp.getFinspId();
                List<NmFinspRecord> nmFinspRecords = recordMap.get(dmFinsp.getStructId());
                if(nmFinspRecords!=null&&nmFinspRecords.size()>0){
                        List<NmFinspResult> nmFinspResults = queryFinspResultsByRecords(lastfinspIdByStruct, facilityCat);
                        if(nmFinspResults!=null&&nmFinspResults.size()>0){
                            for(NmFinspResult result:nmFinspResults){
                                result.setFinspId(finspId);
                                result.setResId(H_KeyWorker.nextIdToString());
                                if("N000033".equals(dmFinsp.getMntOrgId())&&"QL".equals(dmFinsp.getFacilityCat())&&(result.getItemId().equals("89")||result.getItemId().equals("92")||result.getItemId().equals("93")||result.getItemId().equals("100")||result.getItemId().equals("102")||result.getItemId().equals("104"))){
                                    result.setIssueDesc("/");
                                }
                                nmFinspResultMapper.insert(result);
                            }
                        }else{
                            H_StructHelper.refreshNmFinspResult(dmFinsp);
                        }
                    }
//                else{
//                        H_StructHelper.refreshNmFinspResult(dmFinsp);
//                    }
                }


        }



        // 11.批量保存巡查单
        saveBatch(nmFinspList);
        for(NmFinsp nmFinsp:nmFinspList){
            String finspId = nmFinsp.getFinspId();
            this.baseMapper.updateDssNum(finspId);
        }
    }

    private List<NmFinspResult> queryFinspResultsByRecords(String finspId, String facilityCat) {
        if (StrUtil.isBlank(finspId)) {
            return Collections.emptyList();
        }
        String[] finspIdArr = finspId.split(",");
        List<NmFinspResult> result = new ArrayList<>();
        int batchSize = 300;
        for (int i = 0; i < finspIdArr.length; i += batchSize) {
            int end = Math.min(i + batchSize, finspIdArr.length);
            String batchIds = String.join(",", Arrays.copyOfRange(finspIdArr, i, end));
            List<NmFinspResult> batchResult = this.nmFinspResultMapper.findNmFinspResult(batchIds);
            if (batchResult != null) {
                result.addAll(batchResult);
            }
        }
        return result;
    }

    private boolean isDmFinspResultSet(String orgCode, String userCode) {
        return this.resultService.isNmFinspResultSet(orgCode, userCode);
    }


    @Override
    public Map<Integer, List<Long>> listProcessTask(String facilityCat) {
        return listProcessTask(facilityCat,null);
    }

    @Override
    public Map<Integer, List<Long>> listProcessTask(String facilityCat, String processInstId) {
        Set<Long> processidSet = Sets.newHashSet();
        if (StrUtil.isBlank(processInstId)){
            //待办清单
            String taskSql = H_WorkFlowHelper.getUserTaskSql(Integer.valueOf(0), "nm_finsp");
            LambdaQueryWrapper<NmFinsp> wrapper = new LambdaQueryWrapper<>();
            wrapper.exists(taskSql).eq(NmFinsp::getFacilityCat,facilityCat)
                    .select(NmFinsp::getProcessinstid)
                    .groupBy(NmFinsp::getProcessinstid);
            // 获取流程ID
            processidSet = list(wrapper).stream().filter(e -> ObjectUtil.isNotEmpty(e.getProcessinstid())).map(NmFinsp::getProcessinstid).collect(Collectors.toSet());
        }else {
            Set<String> instIds = StrUtil.split(processInstId, ",").stream().collect(Collectors.toSet());
            processidSet = Convert.toSet(Long.class, instIds);
        }

        Map<Integer, List<Long>> processMap = wfworkitemService.selectTaskMapByNmInsp(processidSet,false);
        return processMap;
    }

  @Override
  public void saveInspectionPhotos(String finspId, List<String> idList) {
        for(String id:idList) {
            this.deleteInspectionPhotoById(id);
            this.nmFinspRecordMapper.saveInspectionPhoto(finspId,id);
        }
  }

    @Override
    public void deleteInspectionPhotoById(String id) {
        this.nmFinspRecordMapper.deleteInspectionPhotoById(id);
    }

    @Override
    public String getInspectionPhotos(String finspId) {
        return      this.nmFinspRecordMapper.getInspectionPhotos(finspId);
    }

    @Override
    public List<NmFinsp> getList() {
        return nmFinspMapper.getList();
    }

    @Override
    public List<Map> loadLineQlDetail(String structId) {
        return nmFinspMapper.getQlDetail(structId);
    }

    // 方法抽取：对象构建逻辑封装
    private NmFinsp buildNmFinsp(NmFinsp source, BaseStructDto item, Map<String, BaseRouteLogic> routeMap,
                                 Map<String, String> lineMap,Integer xcType) {
        // 1. 使用final确保对象不可变
        final NmFinsp bean = BeanUtil.toBean(source, NmFinsp.class);

        // 2. 使用Optional处理空值
        Date defaultDate = Optional.ofNullable(bean.getInspDate())
                .orElseGet(() -> {
                    Date now = new Date();
                    bean.setInspDate(now);  // 设置值
                    return now;             // 返回Date类型
                });

        // 3. 提取路由逻辑到独立代码块
        final BaseRouteLogic routeLogic = Optional.ofNullable(routeMap.get(item.getRouteCode()))
                .orElseGet(BaseRouteLogic::new);
        final String lineCode = routeLogic.getLineCode();

        // 4. 消除魔法值
        final String UNKNOWN_LINE = "未知路线";
        final String lineName = lineMap.getOrDefault(lineCode, UNKNOWN_LINE);

        // 5. 链式调用拆分提高可读性
        return bean
                .setRouteName(routeLogic.getRouteName())
                .setLineCode(lineCode)
                .setLineName(lineName)
                .setStructId(item.getStructId())
                .setFinspId(H_KeyWorker.nextIdToString())
                .setStatus(0)
                .setInspTime(INSP_TIME)
                .setXcType(xcType)
                .setStructStakeNum(item.getRlCntrStake())
                .setStructName(item.getStructName())
                .setMntOrgId(CustomRequestContextHolder.getOrgIdString())
                .setRouteCode(item.getRouteCode());
    }

    @Override
    public Map<String, String> initAction() {
        return Collections.emptyMap();
    }

    @Override
    public String getProcessDefName() {
        return processDefName;
    }

    @Override
    public void savedoEvent(long processInstId, boolean isEnd, String nextAction, String actId) {
        if (isEnd){
            List<NmFinsp> dmFinsps = getNmFinspByProcessInstId(processInstId);
            List<String> finspIds = dmFinsps.stream().map(NmFinsp::getFinspId).collect(Collectors.toList());
            List<NmFinspRecord> dmDinspRecords = recordService.selectRecordByFinspId(finspIds);
            Map<String, List<NmFinspRecord>> finspMap = dmDinspRecords.stream().collect(Collectors.groupingBy(NmFinspRecord::getFinspId));
            for (NmFinsp dmFinsp : dmFinsps) {
                List<NmFinspRecord> nmFinspRecords = finspMap.get(dmFinsp.getFinspId());
                if (CollectionUtil.isNotEmpty(nmFinspRecords)) {
                    saveDssInfo(dmFinsp,nmFinspRecords);
                }
            }

            updateNmFinspStatus(processInstId,3);
            return;
        }
        if ("manualActivity".equals(actId)){
            updateNmFinspStatus(processInstId,1);
        }else if (nextAction.contains("审核")){
            updateNmFinspStatus(processInstId,2);
        }else if (nextAction.equals("退回")){
            updateNmFinspStatus(processInstId,-1);
        }
    }

    private void saveDssInfo(NmFinsp dmFinsp, List<NmFinspRecord> dmDinspRecords) {
        if (CollectionUtil.isEmpty(dmDinspRecords)){
            return;
        }
        Map<String, BaseLine> allLineMap = lineService.getAllLineMap();
        String mainRoadId = allLineMap != null && allLineMap.containsKey(dmFinsp.getLineCode())
                ? allLineMap.get(dmFinsp.getLineCode()).getLineId()
                : dmFinsp.getLineCode();
        List<DssInfo> dssInfoList = Lists.newArrayList();
        dmDinspRecords.forEach(item->{
            DssInfo ds = new DssInfo();
            // todo 设置病害路段信息
            /*if (dto != null){
                ds.setRpIntrvlId(dto.getRpIntrvlId());
                ds.setRampId(dto.getRampId());
                ds.setStake(dto.getCntrStake());
                ds.setRlStakeNew(dto.getRlCntrStake());
                ds.setRoutecode(dto.getRouteCode());
                ds.setRouteversion(dto.getRouteVersion());
            }*/
            BeanUtil.copyProperties(item,ds);
            if (StrUtil.isBlank(ds.getStructId())){
                ds.setStructId(dmFinsp.getStructId());
            }
            ds.setDssCode(item.getDssId());
            ds.setRlStakeNew(item.getStake());
            ds.setFindDssUserName(dmFinsp.getInspPerson());
            ds.setDssImpFlag(0);
            ds.setRelTaskCode(dmFinsp.getFinspId());
            ds.setDssSource(2);
            ds.setRepairStatus(0);
            ds.setDealStatus(0);
            ds.setFoundDate(dmFinsp.getInspDate());
            ds.setMainRoadId(mainRoadId);
            ds.setLinedirect(item.getLineDirect());
            ds.setRoutecode(dmFinsp.getRouteCode());
            dssInfoList.add(ds);
        });
        dssInfoService.saveOrUpdateBatch(dssInfoList);
    }

    @Override
    public List<OrderInfo> queryOrderInfo(long processInstId) {
        List<NmFinsp> list = getNmFinspByProcessInstId(processInstId);
        List<OrderInfo> infos = new ArrayList<>();
        list.stream().forEach(item->{
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderCode(item.getFinspCode());
            orderInfo.setOrderType("JCJC_NEW");
            orderInfo.setOrderId(item.getFinspId());
            orderInfo.setCat(item.getFacilityCat());
            orderInfo.setStatus(item.getStatus());
            orderInfo.setOrgCode(item.getMntOrgId());
            orderInfo.setXcType(item.getXcType());
            infos.add(orderInfo);
        });
        return infos;
    }

    @Override
    public IPage<SlopeInspectionRecord> findBpDailyRecord(IPage page,
                                                          String dinspCode,
                                                          String status,
                                                          String lineCode,
                                                          String hasDssStatus,
                                                          String startDate,
                                                          String endDate){

        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        IPage<SlopeInspectionRecord> routeInspections = this.baseMapper.findBpDailyRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate, endDate);

        return routeInspections;
    }
    @Override
    public void findBpDailyRecord(HttpServletResponse response,
                                  IPage page,
                                  String dinspCode,
                                  String status,
                                  String lineCode,
                                  String hasDssStatus,
                                  String startDate,
                                  String endDate)
    {
        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        String orgName = CustomRequestContextHolder.getOrgName();
        IPage<SlopeInspectionRecord> routeInspections = this.baseMapper.findBpDailyRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate, endDate);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        String routeName = "";
        String oprtName = "";
        String checkDate = "";
        List<SlopeInspectionRecord> records = routeInspections.getRecords();
        Optional<SlopeInspectionRecord> first = records.stream().findFirst();
        if (first.isPresent())
        {
            SlopeInspectionRecord routeInspection = first.get();
            routeName = routeInspection.getRouteName();
            oprtName = routeInspection.getOrgFullname();
            checkDate = safeFormatDate(sdf, routeInspection.getInspDate());
        }

        //请求头设置
        // 设置导出文件名（含时间戳避免重复）
        String fileName = routeName + "高速边坡经常检查台账" + ".xlsx";

        // 设置响应头（适配中文文件名）
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        // 中文文件名需要编码处理
        try {
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("巡查记录");
        CreationHelper helper = workbook.getCreationHelper();

        // 1. 添加标题行（第一行），合并单元格并居中
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30); // 设置行高

        // 2.添加第二行显示养护单位
        XSSFRow oprtRow = sheet.createRow(1);
        oprtRow.setHeightInPoints(25); // 设置行高

        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 14);
        sheet.addMergedRegion(mergedRegion);
        //合并单元格
        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedPrjRegion = new CellRangeAddress(1, 1, 0, 5);
        sheet.addMergedRegion(mergedPrjRegion);

        CellRangeAddress mergedOprtRegion = new CellRangeAddress(1, 1, 6, 9);
        sheet.addMergedRegion(mergedOprtRegion);

        CellRangeAddress mergedDateRegion = new CellRangeAddress(1, 1, 10, 12);
        sheet.addMergedRegion(mergedDateRegion);

        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(routeName + "高速公路边坡经常检查台账");
        CellStyle titleStyle = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontName("仿宋");                      // 设置字体为仿宋
        font.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        font.setBold(true);                           // 加粗
        titleStyle.setFont(font);

        // 设置水平和垂直居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        titleCell.setCellStyle(titleStyle);

        //项目公司
        XSSFCell prjCell = oprtRow.createCell(0);
        prjCell.setCellValue("项目公司：" + orgName);
        CellStyle prjStyle = workbook.createCellStyle();
        XSSFFont fontprj = workbook.createFont();
        fontprj.setFontName("仿宋");                      // 设置字体为仿宋
        fontprj.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontprj.setBold(true);                           // 加粗
        prjStyle.setFont(fontprj);

        // 设置水平和垂直居中
        prjStyle.setAlignment(HorizontalAlignment.CENTER);
        prjStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyles = workbook.createCellStyle();
        borderStyles.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyles.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyles.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyles.setBorderRight(BorderStyle.THIN);  // 右边框
        prjCell.setCellStyle(borderStyles);

        prjCell.setCellStyle(prjStyle);

        //管养公司
        XSSFCell oprtCell = oprtRow.createCell(6);
        oprtCell.setCellValue("养护单位：" + oprtName);
        CellStyle oprtStyle = workbook.createCellStyle();
        XSSFFont fontoprt = workbook.createFont();
        fontoprt.setFontName("仿宋");                      // 设置字体为仿宋
        fontoprt.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontoprt.setBold(true);                           // 加粗
        oprtStyle.setFont(fontoprt);

        // 设置水平和垂直居中
        CellStyle borderStyleOprt = workbook.createCellStyle();
        borderStyleOprt.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyleOprt.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyleOprt.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyleOprt.setBorderRight(BorderStyle.THIN);  // 右边框
        oprtCell.setCellStyle(borderStyleOprt);
        oprtStyle.setAlignment(HorizontalAlignment.CENTER);
        oprtStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        oprtCell.setCellStyle(oprtStyle);

        //检查时间
        XSSFCell DateCell = oprtRow.createCell(10);
        DateCell.setCellValue("检查时间：" + checkDate);
        CellStyle DateStyle = workbook.createCellStyle();
        XSSFFont fontDate = workbook.createFont();
        fontDate.setFontName("仿宋");                      // 设置字体为仿宋
        fontDate.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontDate.setBold(true);                           // 加粗
        DateStyle.setFont(fontDate);

        // 设置水平和垂直居中
        DateStyle.setAlignment(HorizontalAlignment.CENTER);
        DateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyleborder = workbook.createCellStyle();
        borderStyleborder.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyleborder.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyleborder.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyleborder.setBorderRight(BorderStyle.THIN);  // 右边框
        DateCell.setCellStyle(borderStyleborder);

        DateCell.setCellStyle(DateStyle);

        // 设置列宽（比如第6列用于图片）
        sheet.setColumnWidth(5, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(0, 5 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(1, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(2, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(3, 20 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(4, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(6, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(8, 25 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(9, 15 * 256); // 第6列宽度（图片列）

        // 创建表头
        XSSFRow header = sheet.createRow(2);
        String[] headers = {
                "序号", "路段", "所属线路", "边坡名称", "边坡长度（m）", "边坡级数", "病害情况", "技术状况",
                "检查单编码", "检查日期", "巡查频率", "养护建议", "备注"
        };
        for (int i = 0; i < headers.length; i++) {
            XSSFCell cell = header.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle titleStyleTemp = workbook.createCellStyle();
            // 设置水平和垂直居中
            titleStyleTemp.setAlignment(HorizontalAlignment.CENTER);
            titleStyleTemp.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(titleStyleTemp);

            //设置边框
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setBorderTop(BorderStyle.THIN);  // 上边框
            borderStyle.setBorderBottom(BorderStyle.THIN);  // 下边框
            borderStyle.setBorderLeft(BorderStyle.THIN);  // 左边框
            borderStyle.setBorderRight(BorderStyle.THIN);  // 右边框
            cell.setCellStyle(borderStyle);
        }

        // 写入数据
        int rowIndex = 3;
        int serial = 1;
        for (SlopeInspectionRecord record : records) {
            XSSFRow row = sheet.createRow(rowIndex);
            int col = 0;

            safeSetCellValue(row, col++, serial, workbook); // 序号
            safeSetCellValue(row, col++, record.getRouteName(), workbook);
            safeSetCellValue(row, col++, record.getLineCode(), workbook);
            safeSetCellValue(row, col++, record.getSlopeName(), workbook);
            safeSetCellValue(row, col++, record.getSlopeLength(), workbook);
            safeSetCellValue(row, col++, record.getSlopeLevel(), workbook);
            safeSetCellValue(row, col++, record.getDssNums(), workbook);
            safeSetCellValue(row, col++, record.getSlopeTcGrade(), workbook);
            safeSetCellValue(row, col++, record.getDinspCode(), workbook);
            safeSetCellValue(row, col++, safeFormatDate(sdf, record.getInspDate()), workbook);
            safeSetCellValue(row, col++, record.getInspFrequency(), workbook);
            safeSetCellValue(row, col++, record.getMntnAdvice(), workbook);
            safeSetCellValue(row, col++, "", workbook); // 备注

            serial++;
            rowIndex++;
        }
        try {
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public IPage<BridgeInspection> findQLDailyRecord(IPage page,
                                                          String dinspCode,
                                                          String status,
                                                          String lineCode,
                                                          String hasDssStatus,
                                                          String startDate,
                                                          String endDate){

        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        IPage<BridgeInspection> routeInspections = this.baseMapper.findQLDailyRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate, endDate);

        return routeInspections;
    }
    @Override
    public IPage<HdInspectionRecord> findHdDailyRecord(IPage page,
                                                     String dinspCode,
                                                     String status,
                                                     String lineCode,
                                                     String hasDssStatus,
                                                     String startDate){

        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        IPage<HdInspectionRecord> routeInspections = this.baseMapper.findHdDailyRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate);

        return routeInspections;
    }

    @Override
    public void findHdDailyRecord(HttpServletResponse response,
                                  IPage page,
                                  String dinspCode,
                                  String status,
                                  String lineCode,
                                  String hasDssStatus,
                                  String startDate)
    {
        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        String orgName = CustomRequestContextHolder.getOrgName();
        IPage<HdInspectionRecord> routeInspections = this.baseMapper.findHdDailyRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate);

        String routeName = "";
        String oprtName = "";
        String checkDate = "";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        List<HdInspectionRecord> records = routeInspections.getRecords();
        Optional<HdInspectionRecord> first = records.stream().findFirst();
        if (first.isPresent())
        {
            HdInspectionRecord routeInspection = first.get();
            routeName = routeInspection.getRouteName();
            oprtName = routeInspection.getSearchDept();
            checkDate = safeFormatDate(sdf, routeInspection.getInspDate());
        }

        //请求头设置
        // 设置导出文件名（含时间戳避免重复）
        String fileName = routeName + "高速巡查涵洞检查处治台账" + ".xlsx";

        // 设置响应头（适配中文文件名）
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        // 中文文件名需要编码处理
        try {
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("巡查记录");
        CreationHelper helper = workbook.getCreationHelper();
        // 设置列宽（如第6列用于图片）
        sheet.setColumnWidth(5, 20 * 256); // 第6列宽度

        // 1. 添加标题行（第一行），合并单元格并居中
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30); // 设置行高

        // 2.添加第二行显示养护单位
        XSSFRow oprtRow = sheet.createRow(1);
        oprtRow.setHeightInPoints(25); // 设置行高

        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 14);
        sheet.addMergedRegion(mergedRegion);
        //合并单元格
        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedPrjRegion = new CellRangeAddress(1, 1, 0, 3);
        sheet.addMergedRegion(mergedPrjRegion);

        CellRangeAddress mergedOprtRegion = new CellRangeAddress(1, 1, 4, 7);
        sheet.addMergedRegion(mergedOprtRegion);

        CellRangeAddress mergedDateRegion = new CellRangeAddress(1, 1, 8, 10);
        sheet.addMergedRegion(mergedDateRegion);

        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(routeName + "高速公路涵洞经常检查台账");
        CellStyle titleStyle = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontName("仿宋");                      // 设置字体为仿宋
        font.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        font.setBold(true);                           // 加粗
        titleStyle.setFont(font);

        // 设置水平和垂直居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyles = workbook.createCellStyle();
        borderStyles.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyles.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyles.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyles.setBorderRight(BorderStyle.THIN);  // 右边框
        titleCell.setCellStyle(borderStyles);

        titleCell.setCellStyle(titleStyle);

        //项目公司
        XSSFCell prjCell = oprtRow.createCell(0);
        prjCell.setCellValue("项目公司：" + orgName);
        CellStyle prjStyle = workbook.createCellStyle();
        XSSFFont fontprj = workbook.createFont();
        fontprj.setFontName("仿宋");                      // 设置字体为仿宋
        fontprj.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontprj.setBold(true);                           // 加粗
        prjStyle.setFont(fontprj);

        // 设置水平和垂直居中
        prjStyle.setAlignment(HorizontalAlignment.LEFT);
        prjStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyleborder = workbook.createCellStyle();
        borderStyleborder.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyleborder.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyleborder.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyleborder.setBorderRight(BorderStyle.THIN);  // 右边框
        prjCell.setCellStyle(borderStyleborder);
        prjCell.setCellStyle(prjStyle);

        //管养公司
        XSSFCell oprtCell = oprtRow.createCell(4);
        oprtCell.setCellValue("养护单位：" + oprtName);
        CellStyle oprtStyle = workbook.createCellStyle();
        XSSFFont fontoprt = workbook.createFont();
        fontoprt.setFontName("仿宋");                      // 设置字体为仿宋
        fontoprt.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontoprt.setBold(true);                           // 加粗
        oprtStyle.setFont(fontoprt);

        // 设置水平和垂直居中
        oprtStyle.setAlignment(HorizontalAlignment.LEFT);
        oprtStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyleoprt = workbook.createCellStyle();
        borderStyleoprt.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyleoprt.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyleoprt.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyleoprt.setBorderRight(BorderStyle.THIN);  // 右边框
        oprtCell.setCellStyle(borderStyleoprt);

        oprtCell.setCellStyle(oprtStyle);

        //检查时间
        XSSFCell DateCell = oprtRow.createCell(8);
        DateCell.setCellValue("检查时间：" + checkDate);
        CellStyle DateStyle = workbook.createCellStyle();
        XSSFFont fontDate = workbook.createFont();
        fontDate.setFontName("仿宋");                      // 设置字体为仿宋
        fontDate.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontDate.setBold(true);                           // 加粗
        DateStyle.setFont(fontDate);

        // 设置水平和垂直居中
        DateStyle.setAlignment(HorizontalAlignment.LEFT);
        DateStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        DateCell.setCellStyle(DateStyle);

        // 设置列宽（比如第6列用于图片）
        sheet.setColumnWidth(5, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(0, 5 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(1, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(2, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(3, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(4, 20 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(6, 13 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(7, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(8, 25 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(9, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(10, 25 * 256); // 第6列宽度（图片列）

        // 创建表头
        XSSFRow header = sheet.createRow(2);
        String[] headers = {
                "序号", "路段", "所属线路", "涵洞名称", "中心桩号", "病害情况",
                "检查日期", "巡查频率", "检查单编码", "维修保养建议", "备注"
        };
        for (int i = 0; i < headers.length; i++) {
            XSSFCell cell = header.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle titleStyleTemp = workbook.createCellStyle();
            // 设置水平和垂直居中
            titleStyleTemp.setAlignment(HorizontalAlignment.CENTER);
            titleStyleTemp.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(titleStyleTemp);

            //设置边框
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setBorderTop(BorderStyle.THIN);  // 上边框
            borderStyle.setBorderBottom(BorderStyle.THIN);  // 下边框
            borderStyle.setBorderLeft(BorderStyle.THIN);  // 左边框
            borderStyle.setBorderRight(BorderStyle.THIN);  // 右边框
            cell.setCellStyle(borderStyle);
        }

        // 写入数据
        int rowIndex = 3;
        int serial = 1;
        for (HdInspectionRecord record : records) {
            XSSFRow row = sheet.createRow(rowIndex);
            int col = 0;

            safeSetCellValue(row, col++, serial, workbook); // 序号
            safeSetCellValue(row, col++, record.getRouteName(), workbook);
            safeSetCellValue(row, col++, record.getLineCode(), workbook);
            safeSetCellValue(row, col++, record.getClvrtName(), workbook);
            safeSetCellValue(row, col++, record.getCntrStake(), workbook);
            safeSetCellValue(row, col++, record.getDssNums(), workbook);
            safeSetCellValue(row, col++, safeFormatDate(sdf, record.getInspDate()), workbook);
            safeSetCellValue(row, col++, record.getInspFrequency(), workbook);
            safeSetCellValue(row, col++, record.getDinspCode(), workbook);
            safeSetCellValue(row, col++, "/", workbook);
            safeSetCellValue(row, col++, "", workbook); // 备注

            serial++;
            rowIndex++;
        }
        try {
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void findQLDailyRecord(HttpServletResponse response,
                                  IPage page,
                                  String dinspCode,
                                  String status,
                                  String lineCode,
                                  String hasDssStatus,
                                  String startDate,
                                  String endDate)
    {
        String orgIdString = CustomRequestContextHolder.getOrgIdString();
        String orgName = CustomRequestContextHolder.getOrgName();
        IPage<BridgeInspection> routeInspections = this.baseMapper.findQLDailyRecord(page, orgIdString
                , dinspCode, status, lineCode, hasDssStatus, startDate, endDate);

        String routeName = "";
        String oprtName = "";
        String checkDate = "";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        List<BridgeInspection> records = routeInspections.getRecords();
        Optional<BridgeInspection> first = records.stream().findFirst();
        if (first.isPresent())
        {
            BridgeInspection routeInspection = first.get();
            routeName = routeInspection.getRouteName();
            oprtName = routeInspection.getOrgFullname();
            checkDate = safeFormatDate(sdf, routeInspection.getInspDate());
        }

        //请求头设置
        // 设置导出文件名（含时间戳避免重复）
        String fileName = routeName + "高速巡查检查病害处治台账" + ".xlsx";

        // 设置响应头（适配中文文件名）
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        // 中文文件名需要编码处理
        try {
            response.setHeader("Content-Disposition",
                    "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + URLEncoder.encode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet("巡查记录");
        CreationHelper helper = workbook.getCreationHelper();
        // 设置列宽（如第6列用于图片）
        sheet.setColumnWidth(5, 20 * 256); // 第6列宽度

        // 1. 添加标题行（第一行），合并单元格并居中
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30); // 设置行高

        // 2.添加第二行显示养护单位
        XSSFRow oprtRow = sheet.createRow(1);
        oprtRow.setHeightInPoints(25); // 设置行高

        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedRegion = new CellRangeAddress(0, 0, 0, 14);
        sheet.addMergedRegion(mergedRegion);
        //合并单元格
        // 合并单元格 A1:F1（0到5列）
        CellRangeAddress mergedPrjRegion = new CellRangeAddress(1, 1, 0, 4);
        sheet.addMergedRegion(mergedPrjRegion);

        CellRangeAddress mergedOprtRegion = new CellRangeAddress(1, 1, 5, 10);
        sheet.addMergedRegion(mergedOprtRegion);

        CellRangeAddress mergedDateRegion = new CellRangeAddress(1, 1, 11, 13);
        sheet.addMergedRegion(mergedDateRegion);

        XSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(routeName + "高速公路桥梁经常检查台账");
        CellStyle titleStyle = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontName("仿宋");                      // 设置字体为仿宋
        font.setFontHeightInPoints((short) 18);       // 设置字体大小为18号
        font.setBold(true);                           // 加粗
        titleStyle.setFont(font);

        // 设置水平和垂直居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyles = workbook.createCellStyle();
        borderStyles.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyles.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyles.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyles.setBorderRight(BorderStyle.THIN);  // 右边框
        titleCell.setCellStyle(borderStyles);

        titleCell.setCellStyle(titleStyle);

        //项目公司
        XSSFCell prjCell = oprtRow.createCell(0);
        prjCell.setCellValue("项目公司：" + orgName);
        CellStyle prjStyle = workbook.createCellStyle();
        XSSFFont fontprj = workbook.createFont();
        fontprj.setFontName("仿宋");                      // 设置字体为仿宋
        fontprj.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontprj.setBold(true);                           // 加粗
        prjStyle.setFont(fontprj);

        // 设置水平和垂直居中
        prjStyle.setAlignment(HorizontalAlignment.LEFT);
        prjStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyleborder = workbook.createCellStyle();
        borderStyleborder.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyleborder.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyleborder.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyleborder.setBorderRight(BorderStyle.THIN);  // 右边框
        prjCell.setCellStyle(borderStyleborder);
        prjCell.setCellStyle(prjStyle);

        //管养公司
        XSSFCell oprtCell = oprtRow.createCell(5);
        oprtCell.setCellValue("养护单位：" + oprtName);
        CellStyle oprtStyle = workbook.createCellStyle();
        XSSFFont fontoprt = workbook.createFont();
        fontoprt.setFontName("仿宋");                      // 设置字体为仿宋
        fontoprt.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontoprt.setBold(true);                           // 加粗
        oprtStyle.setFont(fontoprt);

        // 设置水平和垂直居中
        oprtStyle.setAlignment(HorizontalAlignment.LEFT);
        oprtStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle borderStyleoprt = workbook.createCellStyle();
        borderStyleoprt.setBorderTop(BorderStyle.THIN);  // 上边框
        borderStyleoprt.setBorderBottom(BorderStyle.THIN);  // 下边框
        borderStyleoprt.setBorderLeft(BorderStyle.THIN);  // 左边框
        borderStyleoprt.setBorderRight(BorderStyle.THIN);  // 右边框
        oprtCell.setCellStyle(borderStyleoprt);

        oprtCell.setCellStyle(oprtStyle);

        //检查时间
        XSSFCell DateCell = oprtRow.createCell(11);
        DateCell.setCellValue("检查时间：" + checkDate);
        CellStyle DateStyle = workbook.createCellStyle();
        XSSFFont fontDate = workbook.createFont();
        fontDate.setFontName("仿宋");                      // 设置字体为仿宋
        fontDate.setFontHeightInPoints((short) 12);       // 设置字体大小为18号
        fontDate.setBold(true);                           // 加粗
        DateStyle.setFont(fontDate);

        // 设置水平和垂直居中
        DateStyle.setAlignment(HorizontalAlignment.LEFT);
        DateStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        DateCell.setCellStyle(DateStyle);

        // 设置列宽（比如第6列用于图片）
        sheet.setColumnWidth(5, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(0, 5 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(1, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(2, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(3, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(4, 20 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(6, 13 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(7, 10 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(8, 25 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(9, 15 * 256); // 第6列宽度（图片列）
        sheet.setColumnWidth(10, 25 * 256); // 第6列宽度（图片列）

        // 创建表头
        XSSFRow header = sheet.createRow(2);
        String[] headers = {
                "序号", "路段", "所属线路", "桥梁分类", "桥梁名称", "设计中心桩号", "营运中心桩号", "病害情况",
                "检查日期", "巡查频率", "检查单编码", "结构物技术状况", "维修保养建议", "备注"
        };
        for (int i = 0; i < headers.length; i++) {
            XSSFCell cell = header.createCell(i);
            cell.setCellValue(headers[i]);
            CellStyle titleStyleTemp = workbook.createCellStyle();
            // 设置水平和垂直居中
            titleStyleTemp.setAlignment(HorizontalAlignment.CENTER);
            titleStyleTemp.setVerticalAlignment(VerticalAlignment.CENTER);
            cell.setCellStyle(titleStyleTemp);

            //设置边框
            CellStyle borderStyle = workbook.createCellStyle();
            borderStyle.setBorderTop(BorderStyle.THIN);  // 上边框
            borderStyle.setBorderBottom(BorderStyle.THIN);  // 下边框
            borderStyle.setBorderLeft(BorderStyle.THIN);  // 左边框
            borderStyle.setBorderRight(BorderStyle.THIN);  // 右边框
            cell.setCellStyle(borderStyle);
        }

        // 写入数据
        int rowIndex = 3;
        int serial = 1;
        for (BridgeInspection record : records) {
            XSSFRow row = sheet.createRow(rowIndex);
            int col = 0;

            safeSetCellValue(row, col++, serial, workbook); // 序号
            safeSetCellValue(row, col++, record.getRouteName(), workbook);
            safeSetCellValue(row, col++, record.getLineCode(), workbook);
            safeSetCellValue(row, col++, record.getBrdgSpanKind(), workbook);
            safeSetCellValue(row, col++, record.getBrdgName(), workbook);
            safeSetCellValue(row, col++, record.getDesignStake(), workbook);
            safeSetCellValue(row, col++, record.getLogicCntrStake(), workbook);
            safeSetCellValue(row, col++, record.getDssNums(), workbook);
            safeSetCellValue(row, col++, safeFormatDate(sdf, record.getInspDate()), workbook);
            safeSetCellValue(row, col++, record.getCheckNums(), workbook);
            safeSetCellValue(row, col++, record.getFinspCode(), workbook);
            safeSetCellValue(row, col++, record.getBrdgRating(), workbook);
            safeSetCellValue(row, col++, "/", workbook);
            safeSetCellValue(row, col++, "", workbook); // 备注

            serial++;
            rowIndex++;
        }
        try {
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private List<NmFinspRecord> batchSelectViewRecord(List<String> finspIds, String facilityCat) {
        List<NmFinspRecord> result = new ArrayList<>();
        // 每批处理1000条数据
        int batchSize = 20;
        for (int i = 0; i < finspIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, finspIds.size());
            List<String> batchIds = finspIds.subList(i, endIndex);
            List<NmFinspRecord> batchResult = baseMapper.selectViewRecord(batchIds, facilityCat);
            result.addAll(batchResult);
        }
        return result;
    }

    @Override
    public List<NmFinspRecord> selectViewRecord(List<String> finspIds, String facilityCat) {
        if (CollectionUtils.isEmpty(finspIds)) {
            return new ArrayList<>();
        }
        return batchSelectViewRecord(finspIds, facilityCat);
    }

    @Override
    public List<NmFinsp> listFinspIdAndInspFrequency(List<String> noResultFinspIds) {
        if (CollectionUtil.isEmpty(noResultFinspIds)){
            return Collections.emptyList();
        }
        List<NmFinsp> nmFinsps = lambdaQuery().in(NmFinsp::getFinspId, noResultFinspIds)
                .select(NmFinsp::getFinspId, NmFinsp::getInspFrequency, NmFinsp::getFacilityCat)
                .list();
        return nmFinsps;
    }

    @Override
    public List<String> deleteStructData(List<String> strings,
                                         String type)
    {
        LocalDate now = LocalDate.now(); // 当前日期
        String currentMonth = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<String> strings1 = CollUtil.split(strings, 1000).stream()
                .flatMap(batch -> this.baseMapper.deleteStructData(batch, type, currentMonth).stream())
                .collect(Collectors.toList());
        return strings1;
    }
}
