package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hualu.app.module.mems.nminsp.validate.LmAddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 新版经常检查记录表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmFinspRecord对象", description="新版经常检查记录表")
public class NmFinspRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "病害ID(主键)")
    @TableId("DSS_ID")
    private String dssId;

    @NotBlank(message = "巡查单ID不能为空")
    @ApiModelProperty(value = "巡查单ID")
    @TableField("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "巡查时间")
    @TableField("INSP_TIME")
    private String inspTime;

    @NotBlank(message = "路线方向不能为空",groups = {LmAddGroup.class})
    @ApiModelProperty(value = "路线方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @NotNull(message = "桩号不能为空",groups = {LmAddGroup.class})
    @ApiModelProperty(value = "桩号")
    @TableField("STAKE")
    private Double stake;

    @NotBlank(message = "问题类型不能为空；1：非病害类，2：病害类")
    @ApiModelProperty(value = "问题类型；1：非病害类，2：病害类")
    @TableField("ISSUE_TYPE")
    private String issueType;


    @NotBlank(message = "病害类型不能为空")
    @ApiModelProperty(value = "病害类型")
    @TableField("DSS_TYPE")
    private String dssType;

    @ApiModelProperty(value = "严重程度")
    @TableField("DSS_DEGREE")
    private String dssDegree;

    @ApiModelProperty(value = "养护建议")
    @TableField("MNTN_ADVICE")
    private String mntnAdvice;

    @NotBlank(message = "设施类型不能为空")
    @ApiModelProperty(value = "设施类型")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "结构物ID")
    @TableField("STRUCT_ID")
    private String structId;

    @NotNull(message = "部件不能为空",groups = {LmAddGroup.class})
    @ApiModelProperty(value = "部件ID")
    @TableField("STRUCT_PART_ID")
    private String structPartId;

    @ApiModelProperty(value = "构件ID")
    @TableField("STRUCT_COMP_ID")
    private String structCompId;

    @NotBlank(message = "车道不能为空",groups = {LmAddGroup.class})
    @ApiModelProperty(value = "车道")
    @TableField("LANE")
    private String lane;

    @ApiModelProperty(value = "病害位置")
    @TableField("DSS_POSITION")
    private String dssPosition;

    @NotBlank(message = "病害描述不能为空",groups = {LmAddGroup.class})
    @ApiModelProperty(value = "病害描述")
    @TableField("DSS_DESC")
    private String dssDesc;

    @ApiModelProperty(value = "病害原因")
    @TableField("DSS_CAUSE")
    private String dssCause;

    @ApiModelProperty(value = "长度")
    @TableField("DSS_L")
    private Double dssL;

    @ApiModelProperty(value = "长度单位")
    @TableField("DSS_L_UNIT")
    private String dssLUnit;

    @ApiModelProperty(value = "宽度")
    @TableField("DSS_W")
    private Double dssW;

    @ApiModelProperty(value = "宽度单位")
    @TableField("DSS_W_UNIT")
    private String dssWUnit;

    @TableField("DSS_D")
    private Double dssD;

    @TableField("DSS_D_UNIT")
    private String dssDUnit;

    @TableField("DSS_N")
    private Double dssN;

    @TableField("DSS_N_UNIT")
    private String dssNUnit;

    @TableField("DSS_A")
    private Double dssA;

    @TableField("DSS_A_UNIT")
    private String dssAUnit;

    @TableField("DSS_V")
    private Double dssV;

    @TableField("DSS_V_UNIT")
    private String dssVUnit;

    @TableField("DSS_P")
    private Double dssP;

    @TableField("DSS_G")
    private Double dssG;

    @TableField("DSS_IMP_FLAG")
    private Integer dssImpFlag;

    @TableField("DSS_QUALITY")
    private String dssQuality;

    @TableField("HIS_DSS_ID")
    private String hisDssId;

    @TableField("X")
    private Double x;

    @TableField("Y")
    private Double y;

    @NotNull(message = "ISPHONE不能为空；1:APP,0:PC")
    @ApiModelProperty(value = "1:APP,0:PC")
    @TableField("ISPHONE")
    private Integer isphone;

    @ApiModelProperty(value = "匝道ID")
    @TableField("RAMP_ID")
    private String rampId;

    @TableField("TUNNEL_MOUTH")
    private String tunnelMouth;

    @ApiModelProperty(value = "开始高度")
    @TableField("START_HIGH")
    private Double startHigh;

    @ApiModelProperty(value = "终点高度")
    @TableField("END_HIGH")
    private Double endHigh;

    @ApiModelProperty(value = "起点桩号数值")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "终点桩号数值")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "桩号高度")
    @TableField("STAKE_HIGH")
    private Double stakeHigh;

    @ApiModelProperty(value = "终止桩号")
    @TableField("FINISH_STAKE")
    private Double finishStake;

    @ApiModelProperty(value = "病害来源")
    @TableField("SOURCE")
    private String source;

    @NotBlank(message = "路面类型不能为空；SN:水泥，LQ:沥青",groups = {LmAddGroup.class})
    @ApiModelProperty(value = "路面类型，LQ:沥青，SN：水泥")
    @TableField("PAVEMENT_TYPE")
    private String pavementType;

    @TableLogic
    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "病害发现日期")
    @TableField("FOUND_DATE")
    private LocalDateTime foundDate;

    @ApiModelProperty(value = "闭合方式（1：任务单闭合，2：专项闭合）")
    @TableField("CLOSE_TYPE")
    private String closeType;

    // ----------------------------------  以下为界面显示的冗余字段-----------------------------------
    //桥梁新版部件id
    @TableField(exist = false)
    private String brdgPartIdNew;

    @TableField(exist = false)
    private String lineDirectName;

    @TableField(exist = false)
    private String laneName;

    @TableField(exist = false)
    private String grade;

    /**
     * 路线编码
     */
    @TableField(exist = false)
    private String lineCode;

    /**
     * 结构物名称
     */
    @TableField(exist = false)
    private String structName;

    /**
     * 中文桩号格式
     */
    @TableField(exist = false)
    private String stakeCn;

    /**
     * 病害照片ID
     */
    @TableField(exist = false)
    private String fileIds;

    /**
     * 病害数量（描述）
     */
    @TableField(exist = false)
    private String dssNum;

    /**
     * 定量数量
     */
    @TableField(exist = false)
    private Object dlNum;

    /**
     * 定量单位
     */
    @TableField(exist = false)
    private String dlUnit;

    /**
     * 修复状态
     */
    @TableField(exist = false)
    private String repairStatus;

    //病害名称
    @TableField(exist = false)
    private String dssTypeName;

    //部件名称
    @TableField(exist = false)
    private String structPartName;
    //部位名称
    @TableField(exist = false)
    private String dssPositionName;

    //隧道子部件
    @TableField(exist = false)
    private String structChildPartName;

    // 任务单ID
    @TableField(exist = false)
    private String mtaskId;

    // 任务单编码
    @TableField(exist = false)
    private String mtaskCode;

    // 验收单ID
    @TableField(exist = false)
    private String mtaskAccptId;

    // 验收单编码
    @TableField(exist = false)
    private String mtaskAccptCode;

    @TableField(exist = false)
    private String imageHost;


    /**
     * 用于路面图片导出
     */
    @JsonIgnore
    @TableField(exist = false)
    private Object image;

}
