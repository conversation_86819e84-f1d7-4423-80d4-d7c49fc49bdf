package com.hualu.app.module.mems.nminsp.entity;

import java.math.BigDecimal;
import java.util.Date;

public class SumInspectionRecord {
    // 基础巡查信息
    private String lineCode;          // 路线编码 (LR.LINE_CODE)
    private String inspTime;            // 巡查时间 (P.INSP_TIME)
    private String stakeName;         // 桩号名称 (P.STAKE_NAME)
    private BigDecimal inspectLength; // 巡查长度 (P.INSPECT_LENGTH)

    // 环境信息
    private String weather;            // 天气 (C.ATTRIBUTE_VALUE)

    // 设施信息
    private String facilityCategory;   // 设施分类解码值 (DECODE(a.FACILITY_CAT...))
    private String lane;               // 车道 (a.LANE)
    private String a1;                 // 原始桩号 (a.STAKE)
    private String stake;              // 格式化桩号 ('K' || REPLACE...)

    // 结构物信息
    private String dssTypeName;        // 结构物类型 (N.DSS_TYPE_NAME)
    private String lineDirect;         // 路线方向解码值 (DECODE(A.LINE_DIRECT...))

    // 人员与标识信息
    private String checkPerson;        // 检查人员 (P.INSP_PERSON)
    private String dssPosition;        // 结构物位置 (a.DSS_POSITION)
    private String dssNum;             // 结构物编号 (P.DSS_NUM)
    private String dinspCode;          // 巡查记录编码 (P.DINSP_CODE)
    private String processInstId;      // 流程实例ID (p.PROCESSINSTID)

    private String mntnAdvice;
    private String structId;
    private String remark;

    private String searchDept;

    private String inspDate;

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getInspTime() {
        return inspTime;
    }

    public void setInspTime(String inspTime) {
        this.inspTime = inspTime;
    }

    public String getStakeName() {
        return stakeName;
    }

    public void setStakeName(String stakeName) {
        this.stakeName = stakeName;
    }

    public BigDecimal getInspectLength() {
        return inspectLength;
    }

    public void setInspectLength(BigDecimal inspectLength) {
        this.inspectLength = inspectLength;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public String getFacilityCategory() {
        return facilityCategory;
    }

    public void setFacilityCategory(String facilityCategory) {
        this.facilityCategory = facilityCategory;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }

    public String getA1() {
        return a1;
    }

    public void setA1(String a1) {
        this.a1 = a1;
    }

    public String getStake() {
        return stake;
    }

    public void setStake(String stake) {
        this.stake = stake;
    }

    public String getDssTypeName() {
        return dssTypeName;
    }

    public void setDssTypeName(String dssTypeName) {
        this.dssTypeName = dssTypeName;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public String getCheckPerson() {
        return checkPerson;
    }

    public void setCheckPerson(String checkPerson) {
        this.checkPerson = checkPerson;
    }

    public String getDssPosition() {
        return dssPosition;
    }

    public void setDssPosition(String dssPosition) {
        this.dssPosition = dssPosition;
    }

    public String getDssNum() {
        return dssNum;
    }

    public void setDssNum(String dssNum) {
        this.dssNum = dssNum;
    }

    public String getDinspCode() {
        return dinspCode;
    }

    public void setDinspCode(String dinspCode) {
        this.dinspCode = dinspCode;
    }

    public String getProcessInstId() {
        return processInstId;
    }

    public void setProcessInstId(String processInstId) {
        this.processInstId = processInstId;
    }

    public String getMntnAdvice() {
        return mntnAdvice;
    }

    public void setMntnAdvice(String mntnAdvice) {
        this.mntnAdvice = mntnAdvice;
    }

    public String getStructId() {
        return structId;
    }

    public void setStructId(String structId) {
        this.structId = structId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSearchDept() {
        return searchDept;
    }

    public void setSearchDept(String searchDept) {
        this.searchDept = searchDept;
    }

    public String getInspDate() {
        return inspDate;
    }

    public void setInspDate(String inspDate) {
        this.inspDate = inspDate;
    }
}
