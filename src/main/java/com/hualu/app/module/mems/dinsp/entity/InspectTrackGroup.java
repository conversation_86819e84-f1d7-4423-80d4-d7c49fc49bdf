package com.hualu.app.module.mems.dinsp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 巡查轨迹组
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="InspectTrackGroup对象", description="巡查轨迹组")
public class InspectTrackGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "巡查单ID")
    @TableField("INSPECT_ID")
    private String inspectId;

    @ApiModelProperty(value = "开始日期")
    @TableField("START_TIME")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束日期（上传日期）")
    @TableField("END_TIME")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "长度")
    @TableField("LENGTH")
    private Double length;

    @ApiModelProperty(value = "轨迹")
    @TableField(exist = false)
    private List<InspectTrack> tracks = new ArrayList<>();
}
