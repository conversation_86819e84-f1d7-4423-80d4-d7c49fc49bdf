package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspDsstype;

import java.util.List;

/**
 * <p>
 * 结构物排查病害表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface PcFinspDsstypeService extends IService<PcFinspDsstype> {

    /**
     * 根据部件类型，查询病害类型集合
     * @param partId
     * @return
     */
    List<PcFinspDssTypeDto> listByPartId(String partId);

    /**
     * 根据设施类型，查询部件集合
     * @param facilityCat
     * @return
     */
    List<PcFinspDsstype> listPart(String facilityCat);

    /**
     * 根据病害类型ID集合，查询对应的病害信息
     * @param dsstypeIds
     * @return
     */
    List<PcFinspDssTypeDto> listByDsstypeIds(List<String> dsstypeIds);
}
