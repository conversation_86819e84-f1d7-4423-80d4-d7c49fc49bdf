package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.util.StrUtil;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.data.UrlPictureRenderData;
import com.google.common.collect.Maps;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.basedata.service.BaseRouteLogicService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.module.mems.nminsp.validate.LmAddGroup;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.mongo.utils.H_ImageHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_CValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Map;

@Service("lmStructImp")
public class LmStructImp implements IStruct{

    @Autowired
    BaseRouteLogicService routeLogicService;

    @Autowired
    NmDinspRecordService dinspRecordService;

    @Autowired
    NmFinspRecordService finspRecordService;

    @Autowired
    MinioProp minioProp;

    public static Map<String,String> pavementTypeMap = Maps.newHashMap();

    static {
        pavementTypeMap.put("LQ","沥青路面");
        pavementTypeMap.put("SN","水泥路面");
    }

    @Override
    public List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps, String parentPath) {
        // 获取当前请求上下文
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        try {
            return H_WordHelper.processAndExportWord(
                    false,
                    7,
                    nmFinsps,
                    parentPath,
                    "FM",
                    NmFinsp::getFacilityCat,
                    NmFinsp::getInspFrequency,
                    NmFinsp::getFinspId,
                    NmFinsp::getFinspCode,
                    NmFinsp::getInspDate,
                    finspIds -> finspRecordService.lambdaQuery().in(NmFinspRecord::getFinspId, finspIds).list(),
                    NmFinspRecord::getFinspId,
                    (records, entity) -> {
                        // 设置请求上下文，解决dic缓存报错问题
                        RequestContextHolder.setRequestAttributes(attributes, true);
                        finspRecordService.showView(records,entity);},null,null,
                    (records)->showPavementTypeValue(records),null
            );
        } finally {
            RequestContextHolder.resetRequestAttributes();
        }
    }

    @Override
    public List<String> processAndExportNmDinspWord(List<NmDinsp> nmDinsps, String parentPath) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        nmDinsps.forEach(nmDinsp -> {
            // 路面只有一种类型，为了解决app公用单传递的2的情况，所以需要重新赋值
            nmDinsp.setInspFrequency(1);
        });
        try {
            return H_WordHelper.processAndExportWord(
                    false,
                    7,
                    nmDinsps,
                    parentPath,
                    "DM",
                    NmDinsp::getFacilityCat,
                    NmDinsp::getInspFrequency,
                    NmDinsp::getDinspId,
                    NmDinsp::getDinspCode,
                    NmDinsp::getInspDate,
                    dinspIds -> dinspRecordService.lambdaQuery().in(NmDinspRecord::getDinspId, dinspIds).list(),
                    NmDinspRecord::getDinspId,
                    (records, entity) -> {
                        RequestContextHolder.setRequestAttributes(attributes,true);
                        dinspRecordService.showView(records, entity);
                        },null, null,
                    (records)->showPavementTypeValue(records),null
            );
        } finally {
            RequestContextHolder.resetRequestAttributes();
        }
    }

    private void showPavementTypeValue(List records){
        records.forEach(record->{
            // 回显路面类型
            if (record instanceof NmFinspRecord) {
                String pavementType = ((NmFinspRecord) record).getPavementType();
                ((NmFinspRecord) record).setPavementType(pavementTypeMap.getOrDefault(pavementType, pavementType));

                String fileIds = ((NmFinspRecord) record).getFileIds();
                // 回显路面word导出图片
                if (StrUtil.isNotBlank(fileIds)) {
                    List<String> split = StrUtil.split(fileIds, ",");
                    String imageUrl = minioProp.getImagePreviewPrefixUrl() + split.get(0);
                    ((NmFinspRecord) record).setImage(Pictures.of(imageUrl).size(60,60).create());
                }
            }
            if (record instanceof NmDinspRecord) {
                String pavementType = ((NmDinspRecord) record).getPavementType();
                ((NmDinspRecord) record).setPavementType(pavementTypeMap.getOrDefault(pavementType, pavementType));

                // 回显路面word导出图片
                String fileIds = ((NmDinspRecord) record).getFileIds();
                if (StrUtil.isNotBlank(fileIds)) {
                    List<String> split = StrUtil.split(fileIds, ",");
                    String imageUrl = minioProp.getImagePreviewPrefixUrl() + split.get(0);
                    ((NmDinspRecord) record).setImage(Pictures.of(imageUrl).size(60,60).create());
                }
            }
        });
    }

    @Override
    public void validateNmDinsp(NmDinsp nmDinsp) {
        H_CValidator.validator2Exception(nmDinsp, new Class[]{LmAddGroup.class});
        // 根据路线编码，生成对应的路段编码及路段名称
        BaseRouteLogic routeLogic = routeLogicService.getRouteByLineCodeOrRouteCode(nmDinsp.getLineCode(),nmDinsp.getRouteCode());
        if (routeLogic == null) {
            throw new BaseException("路线编码无法匹配对应的路段信息");
        }
        nmDinsp.setRouteCode(routeLogic.getRouteCode()).setRouteName(routeLogic.getRoadName());
    }

    @Override
    public void validateNmDinspRecord(NmDinspRecord nmDinspRecord) {
        H_CValidator.validator2Exception(nmDinspRecord, new Class[]{LmAddGroup.class});
        //当病害类型==部位的dssType，巡查内容设置为病害类型
    }

    @Override
    public void validateNmFinsp(NmFinsp nmFinsp) {
        H_CValidator.validator2Exception(nmFinsp, new Class[]{LmAddGroup.class});
        // 根据路线编码，生成对应的路段编码及路段名称
        BaseRouteLogic routeLogic = routeLogicService.getRouteByLineCodeOrRouteCode(nmFinsp.getLineCode(),nmFinsp.getRouteCode());
        if (routeLogic == null) {
            throw new BaseException("路线编码无法匹配对应的路段信息");
        }
        nmFinsp.setRouteCode(routeLogic.getRouteCode()).setRouteName(routeLogic.getRoadName());
    }

    @Override
    public void validateNmFinspRecord(NmFinspRecord nmFinspRecord) {
        H_CValidator.validator2Exception(nmFinspRecord, new Class[]{LmAddGroup.class});
    }
}
