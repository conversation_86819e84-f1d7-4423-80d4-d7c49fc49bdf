package com.hualu.app.module.mems.mpc.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MpcDssMeasure对象", description="")
public class MpcDssMeasure implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DSS_TYPE")
    private String dssType;

    @TableField("MPITEM_ID")
    private String mpitemId;

    @TableField("ORG_ID")
    private String orgId;

    @TableField("MPITEM_SYS_ID")
    private String mpitemSysId;

    /**
     * 数据来源（有：后台补充，无：系统直接分配）
     */
    @TableField("DATA_FROM")
    private Integer dataFrom;
}
