package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.util.StrUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.entity.NmFinspResult;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspResultMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspItemService;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新版经常检查结论表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Api(tags = "NmFinspResultController",description = "新版经常检查结论表 前端控制器")
@RestController
@RequestMapping("/nmFinspResult")
public class NmFinspResultController extends M_MyBatisController<NmFinspResult,NmFinspResultMapper>{

    @Autowired
    private NmFinspResultService service;

    @Autowired
    NmFinspService nmFinspService;

    @Autowired
    NmFinspRecordService nmFinspRecordService;

    @Autowired
    NmInspItemService itemService;

    @ApiOperation("查询日常巡查单结论")
    @GetMapping("listByFinspId/{finspId}")
    public RestResult<List<NmFinspResult>> listByFinspId(@PathVariable("finspId") String dinspId){
        List<NmFinspResult> results = service.listResultsByFinspIds(Lists.newArrayList(dinspId),"");
        results.stream().forEach(item->{
            if (StrUtil.isBlank(item.getIssueDesc())){
                item.setIssueDesc("未发现病害");
            }
        });
        return RestResult.success(results);
    }

    @ApiOperation("查询交安日常巡查单结论（自定义）")
    @GetMapping("listJaByFinspId/{dinspId}")
    public RestResult<List<NmFinspResult>> listJaByDinspId(@PathVariable("dinspId") String dinspId){
        List<NmFinspResult> results = service.listResultsByFinspIds(Lists.newArrayList(dinspId),"JA");
        results.stream().forEach(item->{
            if (StrUtil.isBlank(item.getIssueDesc())){
                item.setIssueDesc("未发现病害");
            }
        });
        return RestResult.success(results);
    }

    @ApiOperation("修改检查结论")
    @PostMapping(value="/updateResult")
    public RestResult<String> updateResult(@RequestBody NmFinspResult nmDinspResult){
        // 检查结论可能会未新建，所以需要用saveOrUpdate
        //service.saveOrUpdate(nmDinspResult);
        service.supplementRemainingResult(nmDinspResult);
        return RestResult.success("操作成功");
    }
}