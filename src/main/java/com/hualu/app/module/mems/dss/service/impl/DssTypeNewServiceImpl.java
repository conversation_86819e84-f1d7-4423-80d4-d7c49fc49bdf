package com.hualu.app.module.mems.dss.service.impl;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.mapper.DssTypeNewMapper;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-15
 */
@Service
public class DssTypeNewServiceImpl extends ServiceImpl<DssTypeNewMapper, DssTypeNew> implements DssTypeNewService {

    @Override
    public DssTypeNew selectByDssType(String dssType) {
        LambdaQueryWrapper<DssTypeNew> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DssTypeNew::getDssType,dssType);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<DssTypeNew> listByDssType(List<String> dssTypes) {
        LambdaQueryWrapper<DssTypeNew> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DssTypeNew::getDssType,dssTypes);
        queryWrapper.orderByAsc(DssTypeNew::getDssOrderNumber,DssTypeNew::getDssType);
        return list(queryWrapper);
    }

    @Override
    public void setDssUnit(Object obj, String dssType) {
        if (StrUtil.isBlank(dssType)) {
            return;
        }
        DssTypeNew dssTypeNew = selectByDssType(dssType);
        if (dssTypeNew != null){
            //转换成驼峰字段
            String dssColom = StrUtil.toCamelCase(dssTypeNew.getHaveDssColom());
            String dssUnit = dssColom+"Unit";
            if (ReflectUtil.hasField(obj.getClass(),dssUnit)){
                ReflectUtil.setFieldValue(obj,dssUnit,dssTypeNew.getHaveDssUnit());
            }
        }
    }

}
