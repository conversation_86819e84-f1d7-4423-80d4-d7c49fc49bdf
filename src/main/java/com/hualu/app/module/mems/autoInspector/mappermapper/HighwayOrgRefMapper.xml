<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayOrgRefMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayOrgRef">
            <result property="orgName" column="ORG_NAME" jdbcType="VARCHAR"/>
            <result property="routeName" column="ROUTE_NAME" jdbcType="VARCHAR"/>
            <result property="yhOrgName" column="YH_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="yhRouteName" column="YH_ROUTE_NAME" jdbcType="VARCHAR"/>
            <result property="yhOrgCode" column="YH_ORG_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ORG_NAME,ROUTE_NAME,YH_ORG_NAME,
        YH_ROUTE_NAME,YH_ORG_CODE
    </sql>
</mapper>
