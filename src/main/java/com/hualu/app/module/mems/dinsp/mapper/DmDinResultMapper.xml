<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dinsp.mapper.DmDinResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dinsp.entity.DmDinResult">
        <id column="R_ID" property="rId" />
        <result column="DM_CONTENT" property="dmContent" />
        <result column="INSPECTIONS_BAK" property="inspectionsBak" />
        <result column="DSS_TREA" property="dssTrea" />
        <result column="REMARKS" property="remarks" />
        <result column="DM_DINSP_ID" property="dmDinspId" />
        <result column="XUHAO" property="xuhao" />
        <result column="INSPECTIONS" property="inspections" />
        <result column="DEGREE" property="degree" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        R_ID, DM_CONTENT, INSPECTIONS_BAK, DSS_TREA, REMARKS, DM_DINSP_ID, XUHAO, INSPECTIONS, DEGREE
    </sql>

</mapper>
