<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayUserConfigMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayUserConfig">
            <id property="configId" column="CONFIG_ID" jdbcType="DECIMAL"/>
            <result property="routeId" column="ROUTE_ID" jdbcType="VARCHAR"/>
            <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
            <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
            <result property="departName" column="DEPART_NAME" jdbcType="VARCHAR"/>
            <result property="inspType" column="INSP_TYPE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONFIG_ID,ROUTE_ID,USER_ID,
        USER_CODE,DEPART_NAME,INSP_TYPE
    </sql>
</mapper>
