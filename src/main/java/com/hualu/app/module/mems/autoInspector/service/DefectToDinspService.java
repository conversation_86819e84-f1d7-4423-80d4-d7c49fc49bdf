package com.hualu.app.module.mems.autoInspector.service;

import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayOrgRef;
import com.hualu.app.module.mems.autoInspector.entity.NmDinspVo;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;

import java.util.List;
import java.util.Map;

/**
 * 病害信息转日常巡检记录服务接口
 * <p>
 * 提供病害信息转换为日常巡检记录的服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface DefectToDinspService {

    /**
     * 批量将病害信息转换为日常巡检记录
     *
     * @param defectIds 病害ID列表
     * @return 日常巡检记录VO对象列表
     */
    List<NmDinspVo> batchConvert(List<String> defectIds);

    /**
     * 从病害信息中提取路线代码
     *
     * @param defectInfo 病害信息
     * @return 路线代码
     */
    String extractLineCode(HighwayDefectInfo defectInfo);

    /**
     * 创建日常巡检记录
     *
     * @param defectInfo 病害信息
     * @param lineMap 路线映射
     * @param orgRefs 组织参考列表
     * @return 日常巡检记录
     */
    NmDinsp createDinspForDefects(HighwayDefectInfo defectInfo, Map<String, BaseLine> lineMap, List<HighwayOrgRef> orgRefs);

    /**
     * 设置路线信息
     *
     * @param nmDinsp 日常巡检记录
     * @param defectInfo 病害信息
     */
    void setLineInfoFromSegment(NmDinsp nmDinsp, HighwayDefectInfo defectInfo);

    /**
     * 将病害信息转换为日常巡检记录项
     *
     * @param defectInfo 病害信息
     * @param dinspId 日常巡检记录ID
     * @return 日常巡检记录项
     */
    NmDinspRecord convertToDinspRecord(HighwayDefectInfo defectInfo, String dinspId);
} 