package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmFinspResult对象", description="")
public class DmFinspResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("FINSP_RES_ID")
    private String finspResId;

    @TableField("FINSP_ID")
    private String finspId;

    @TableField("FINSP_ITEM_ID")
    private String finspItemId;

    @TableField(value = "ISSUE_DESC",strategy = FieldStrategy.IGNORED)
    private String issueDesc;

    @TableField(value = "INSP_RESULT",strategy = FieldStrategy.IGNORED)
    private String inspResult;

    @TableField("MNTN_ADVICE")
    private String mntnAdvice;

    @TableField(value = "REMARK",strategy = FieldStrategy.IGNORED)
    private String remark;

    @TableField(exist = false)
    private String finspItemCode;//检查项编码

    @TableField(exist = false)
    private String inspCom;//显示桥梁、涵洞部件

    @TableField(exist = false)
    private String finDesc;//隧道检查内容

    @TableField(exist = false)
    private String inspCont;//边坡检查内容

    //检查结果：是
    @TableField(exist = false)
    private String resYes;
    //检查结果：否
    @TableField(exist = false)
    private String resNo;
}
