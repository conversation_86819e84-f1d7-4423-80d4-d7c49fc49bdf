<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.DmFinspResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.DmFinspResult">
        <id column="FINSP_RES_ID" property="finspResId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="FINSP_ITEM_ID" property="finspItemId" />
        <result column="ISSUE_DESC" property="issueDesc" />
        <result column="INSP_RESULT" property="inspResult" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FINSP_RES_ID, FINSP_ID, FINSP_ITEM_ID, ISSUE_DESC, INSP_RESULT, MNTN_ADVICE, REMARK
    </sql>
    <select id="selectResultByFinspId" resultType="com.hualu.app.module.mems.finsp.entity.DmFinspResult">
        select distinct it.FINSP_ITEM_CODE, it.INSP_COM, it.fin_desc, it.insp_cont, re.*
        from DM_FINSP_ITEM it
                 left join DM_FINSP_RESULT re
                           on re.FINSP_ITEM_ID = it.FINSP_ITEM_ID
        where re.FINSP_ID = #{finspId}
        order by cast(it.FINSP_ITEM_CODE as int)
    </select>

</mapper>
