package com.hualu.app.module.mems.comm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.comm.entity.BaseStructComp;
import com.hualu.app.module.mems.comm.mapper.BaseStructCompMapper;
import com.hualu.app.module.mems.comm.service.BaseStructCompService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.utils.H_BasedataHepler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class BaseStructCompServiceImpl extends ServiceImpl<BaseStructCompMapper, BaseStructComp> implements BaseStructCompService {

    @Override
    public List<BaseStructComp> selectStructCompByJA() {
        return baseMapper.queryStructByJA();
    }

    @Override
    public List<BaseStructComp> selectStrcutCompByQL(String partsCode) {
        LambdaQueryWrapper<BaseStructComp> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isBlank(partsCode) || "undefined".equals(partsCode)){
            queryWrapper.eq(BaseStructComp::getPartCode, H_BasedataHepler.QL);
        }else {
            queryWrapper.eq(BaseStructComp::getPartCode,H_BasedataHepler.QL+partsCode);
        }
        return list(queryWrapper);
    }

    @Override
    public List<BaseStructComp> selectStructCompByTj(String structCompId) {
        return baseMapper.selectStructCompByTj(structCompId);
    }
}
