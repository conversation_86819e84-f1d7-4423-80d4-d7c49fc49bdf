package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspGpsMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspCommonService;
import com.hualu.app.module.mems.nminsp.service.NmDinspGpsService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.util.TrackCorrector;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <p>
 * 新版日常巡查轨迹 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
public class NmDinspGpsServiceImpl extends ServiceImpl<NmDinspGpsMapper, NmDinspGps>
    implements NmDinspGpsService {

  @Resource
  private NmDinspCommonService commonService;

  @Resource
  private NmDinspService dinspService;

  @Override
  public List<NmDinspGps> getGpsByDinspIdParallel(String dinspId) {
    LambdaQueryWrapper<NmDinspGps> e = new LambdaQueryWrapper<>();
    e.select(NmDinspGps::getLat, NmDinspGps::getLon, NmDinspGps::getTrackId, NmDinspGps::getTime);
    e.eq(NmDinspGps::getDinspId, dinspId);
    List<NmDinspGps> list = getBaseMapper().selectList(e);
    if (ObjectUtil.isNotEmpty(list)) {
      //轨迹id字段太大, 重写轨迹id, 减少数据量
      Map<String, List<NmDinspGps>> trackIdMap =
          list.stream().collect(Collectors.groupingBy(NmDinspGps::getTrackId));
      int i = 0;
      for (Map.Entry<String, List<NmDinspGps>> entry : trackIdMap.entrySet()) {
        for (NmDinspGps value : entry.getValue()) {
          value.setTrackId(String.valueOf(i));
        }
        i++;
      }
    }
    return list;
  }

  @Override public boolean saveNmDinspGpsData(List<NmDinspGps> gpsList) {
    if (CollectionUtil.isEmpty(gpsList)) {
      return true;
    }

    gpsList.forEach(v -> v.setGpsId(UUID.fastUUID().toString()));
    saveBatch(gpsList, 500);

    //计算巡查里程，保存到公用单和路面巡查记录中
    String dinspId = gpsList.get(0).getDinspId();
    if (!StringUtils.hasText(dinspId)) {
      return true;
    }

    List<NmDinspGps> points = getGpsByDinspIdParallel(dinspId);
    if (CollectionUtil.isEmpty(points)) {
      return true;
    }

    double distance = TrackCorrector.processBaiduDistance(
        points.stream()
            .filter(s -> s.getLat() != null && s.getLon() != null)
            .map(s -> new TrackCorrector.TrackPoint(s.getLat(), s.getLon(),
            s.getTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            s.getTrackId())).collect(Collectors.toList()),
        300
    );

    // 按时间排序
    List<NmDinspGps> sortedPoints = points.stream()
        .sorted(Comparator.comparing(NmDinspGps::getTime))
        .collect(Collectors.toList());

    String startTime =
        sortedPoints.get(0).getTime().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

    String lastTime =
        sortedPoints.get(sortedPoints.size() - 1).getTime().format(DateTimeFormatter.ofPattern("HH:mm:ss"));

    String inspectTime = startTime + "-" + lastTime;

    //更新公用单上的巡查里程
    commonService.update(new UpdateWrapper<NmDinspCommon>()
        .lambda()
        .set(NmDinspCommon::getInspLength, distance)
        .set(NmDinspCommon::getInspTime, inspectTime)
        .eq(NmDinspCommon::getDinspId, dinspId)
    );

    //更新公用单上的巡查里程
    dinspService.update(new UpdateWrapper<NmDinsp>()
        .lambda()
        .set(NmDinsp::getInspectLength, distance)
        .set(NmDinsp::getInspTime, inspectTime)
        .eq(NmDinsp::getCommonDinspId, dinspId)
        .eq(NmDinsp::getFacilityCat, "LM")
    );

    return true;
  }
}
