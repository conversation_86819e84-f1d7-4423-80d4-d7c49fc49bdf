package com.hualu.app.module.mems.payment;

import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.autoInspector.entity.DownloadVo;
import com.hualu.app.module.mems.autoInspector.utils.HttpForAutoUtils;
import com.hualu.app.module.mems.autoInspector.utils.HttpForDownload;
import com.hualu.app.module.mems.payment.service.MeterageReportLogService;
import com.hualu.app.module.mongo.dto.MgFileDto;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Api(tags = "计量支付数据控制器")
@RestController
@RequestMapping("/meterage")
@Slf4j
public class MeterageController {

    @Autowired
    private MeterageReportLogService meterageReportLogService;

    @PostMapping("/uploadFile")
    public RestResult<MgFileDto> uploadFile(MultipartFile file,String contrId) {
        MultipartFile[] files = new MultipartFile[]{file};
        List<MgFileDto> mgFileDtos = HttpForAutoUtils.uploadFile(files);
        if(mgFileDtos == null || mgFileDtos.size() == 0){
            return RestResult.error("文件上传失败");
        }else{
            MgFileDto mgFileDto = mgFileDtos.get(0);
            meterageReportLogService.saveLog(mgFileDto,contrId);
            return RestResult.success(mgFileDto);
        }
    }

    @PostMapping("/downloaDssImage")
    public RestResult<Integer> downloaDssImage(String code) {
        List<DownloadVo> dssImageList = meterageReportLogService.getDssImageList(code);
        int i = HttpForDownload.batchDownloadImages(dssImageList, "D:\\METERAGE\\");
        return RestResult.success(i);
    }
}
