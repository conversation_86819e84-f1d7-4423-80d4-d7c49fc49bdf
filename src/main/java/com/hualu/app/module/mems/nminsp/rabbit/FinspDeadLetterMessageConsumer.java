package com.hualu.app.module.mems.nminsp.rabbit;

import com.hualu.app.config.InspQueueConfig;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

@Service
public class FinspDeadLetterMessageConsumer {

    @RabbitListener(queues = InspQueueConfig.FINSP_DEAD_LETTER_QUEUE_NAME)
    public void receiveDeadLetterMessage(Message message) {
        String content = new String(message.getBody());
        System.out.println("Received check order dead letter message: " + content);
        // 这里可以添加对死信消息的特殊处理逻辑，如记录日志、人工干预等
    }
}    