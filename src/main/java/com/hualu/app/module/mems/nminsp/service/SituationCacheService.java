package com.hualu.app.module.mems.nminsp.service;

import com.hualu.app.config.RedisCacheConfig;
import com.hualu.app.config.SituationMQConfig;
import com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow;
import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;
import com.hualu.app.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

/**
 * 巡查情况缓存服务
 */
@Slf4j
@Service
public class SituationCacheService {

    private static final String DEFAULT_ORG_CODE = "N000001";
    private static final int START_YEAR = 2025;

    @Autowired
    private NmDinspService dinspService;

    @Autowired
    private NmFinspService finspService;

    @Autowired
    private RedisUtils redisUtils;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 定时发送刷新缓存的消息到MQ
     * 每5分钟刷新一次缓存，遍历2025年及以后的年份
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void sendRefreshCacheMessage() {
        log.info("开始发送刷新巡查情况缓存的消息");
        int currentYear = LocalDate.now().getYear();
        
        try {
            // 从2025年开始到当前年份遍历
            for (int year = START_YEAR; year <= currentYear; year++) {
                // 构造消息内容
                Map<String, Object> messageMap = new HashMap<>();
                messageMap.put("orgCode", DEFAULT_ORG_CODE);
                messageMap.put("year", year);
                messageMap.put("refreshTime", System.currentTimeMillis());
                
                // 发送消息到MQ
                rabbitTemplate.convertAndSend(
                    SituationMQConfig.SITUATION_REFRESH_EXCHANGE,
                    SituationMQConfig.SITUATION_REFRESH_ROUTING_KEY,
                    messageMap
                );
                
                log.info("刷新巡查情况缓存的消息已发送, orgCode: {}, year: {}", DEFAULT_ORG_CODE, year);
            }
        } catch (Exception e) {
            log.error("发送刷新巡查情况缓存的消息失败", e);
        }
    }

    /**
     * 获取指定年份范围的日常巡查情况缓存数据
     */
    public Map<Integer, List<NmDinspSituationShow>> getDinspSituationByYears(String orgCode, int startYear, int endYear) {
        Map<Integer, List<NmDinspSituationShow>> resultMap = new HashMap<>();
        
        for (int year = startYear; year <= endYear; year++) {
            String cacheKey = String.format(RedisCacheConfig.DINSP_SITUATION_CACHE_KEY, orgCode, year);
            List<NmDinspSituationShow> yearData = redisUtils.getList(cacheKey, NmDinspSituationShow.class);
            if (yearData != null && !yearData.isEmpty()) {
                resultMap.put(year, yearData);
            }
        }
        
        return resultMap;
    }

    /**
     * 获取指定年份范围的经常检查情况缓存数据
     */
    public Map<Integer, List<NmFinspSituationShow>> getFinspSituationByYears(String orgCode, int startYear, int endYear) {
        Map<Integer, List<NmFinspSituationShow>> resultMap = new HashMap<>();
        
        for (int year = startYear; year <= endYear; year++) {
            String cacheKey = String.format(RedisCacheConfig.FINSP_SITUATION_CACHE_KEY, orgCode, year);
            List<NmFinspSituationShow> yearData = redisUtils.getList(cacheKey, NmFinspSituationShow.class);
            if (yearData != null && !yearData.isEmpty()) {
                resultMap.put(year, yearData);
            }
        }
        
        return resultMap;
    }
    
    /**
     * 刷新日常巡查情况缓存
     */
    public void refreshDinspSituation(String orgCode, int year) {
        // 只缓存N000001的数据
        if (!DEFAULT_ORG_CODE.equals(orgCode)) {
            log.info("跳过非N000001组织的缓存刷新：{}", orgCode);
            return;
        }
        
        try {
            String cacheKey = String.format(RedisCacheConfig.DINSP_SITUATION_CACHE_KEY, orgCode, year);
            
            // 1. 查询新数据
            List<NmDinspSituationShow> situationList = dinspService.querySituationShow(orgCode, year);
            
            // 2. 删除旧缓存并设置新缓存
            if (situationList != null && !situationList.isEmpty()) {
                redisUtils.delete(cacheKey);
                Thread.sleep(100); // 短暂延迟，确保删除操作完成
                redisUtils.set(cacheKey, situationList);
                log.info("日常巡查情况缓存已刷新，组织：{}，年份：{}，数据条数：{}", orgCode, year, situationList.size());
                
                // 验证缓存是否成功设置
                List<NmDinspSituationShow> cachedList = redisUtils.getList(cacheKey, NmDinspSituationShow.class);
                if (cachedList == null || cachedList.isEmpty()) {
                    log.error("日常巡查情况缓存设置失败，组织：{}，年份：{}", orgCode, year);
                }
            } else {
                log.warn("日常巡查情况数据为空，组织：{}，年份：{}", orgCode, year);
            }
        } catch (Exception e) {
            log.error("刷新日常巡查情况缓存异常，组织：{}，年份：{}", orgCode, year, e);
        }
    }
    
    /**
     * 刷新经常检查情况缓存
     */
    public void refreshFinspSituation(String orgCode, int year) {
        // 只缓存N000001的数据
        if (!DEFAULT_ORG_CODE.equals(orgCode)) {
            log.info("跳过非N000001组织的缓存刷新：{}", orgCode);
            return;
        }
        
        try {
            String cacheKey = String.format(RedisCacheConfig.FINSP_SITUATION_CACHE_KEY, orgCode, year);
            
            // 1. 查询新数据
            List<NmFinspSituationShow> situationList = finspService.querySituationShow(orgCode, year);
            
            // 2. 删除旧缓存并设置新缓存
            if (situationList != null && !situationList.isEmpty()) {
                redisUtils.delete(cacheKey);
                Thread.sleep(100); // 短暂延迟，确保删除操作完成
                redisUtils.set(cacheKey, situationList);
                log.info("经常检查情况缓存已刷新，组织：{}，年份：{}，数据条数：{}", orgCode, year, situationList.size());
                
                // 验证缓存是否成功设置
                List<NmFinspSituationShow> cachedList = redisUtils.getList(cacheKey, NmFinspSituationShow.class);
                if (cachedList == null || cachedList.isEmpty()) {
                    log.error("经常检查情况缓存设置失败，组织：{}，年份：{}", orgCode, year);
                }
            } else {
                log.warn("经常检查情况数据为空，组织：{}，年份：{}", orgCode, year);
            }
        } catch (Exception e) {
            log.error("刷新经常检查情况缓存异常，组织：{}，年份：{}", orgCode, year, e);
        }
    }
} 