package com.hualu.app.module.mems.finsp.mapper;

import com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspDsstype;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 结构物排查病害表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface PcFinspDsstypeMapper extends BaseMapper<PcFinspDsstype> {

    /**
     * 递归查询子节点数据
     * @param partId
     * @return
     */
    List<PcFinspDssTypeDto> recursionChilds(@Param("partId") String partId);

    List<PcFinspDssTypeDto> listByDsstypeIds(@Param("dsstypeIds") List<String> dsstypeIds);
}
