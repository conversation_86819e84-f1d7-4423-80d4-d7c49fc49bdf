package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.finsp.entity.AttendanceAddr;
import com.hualu.app.module.mems.finsp.mapper.AttendanceAddrMapper;
import com.hualu.app.module.mems.finsp.service.AttendanceAddrService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 打卡点表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
@Service
public class AttendanceAddrServiceImpl extends ServiceImpl<AttendanceAddrMapper, AttendanceAddr> implements AttendanceAddrService {

    @Override
    public List<AttendanceAddr> selectAddr(String structId, String facilityCat) {
        LambdaQueryWrapper<AttendanceAddr> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceAddr::getStructId,structId);
        queryWrapper.in(AttendanceAddr::getOrgCode, H_DataAuthHelper.selectOrgIds());
        if (StrUtil.isNotBlank(facilityCat)){
            queryWrapper.eq(AttendanceAddr::getFacilityCat,facilityCat);
        }
        queryWrapper.orderByDesc(AttendanceAddr::getCreateTime);
        List<AttendanceAddr> addrs = list(queryWrapper);
        if (CollectionUtil.isEmpty(addrs)){
            addrs = Lists.newArrayList();
        }

        IBaseStructFace iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        BaseStructDto structDto = iBaseStructFace.getId(structId);
        if (structDto != null && structDto.getX() != null && structDto.getY() != null) {
            AttendanceAddr addr = new AttendanceAddr();
            addr.setStructId(structId);
            addr.setStructName(structDto.getStructName());
            addr.setFacilityCat(facilityCat);
            addr.setGisX(structDto.getX());
            addr.setGisY(structDto.getY());
            addr.setIsAttAddr(1);
            addrs.add(addr);
        }
        return addrs;
    }
}
