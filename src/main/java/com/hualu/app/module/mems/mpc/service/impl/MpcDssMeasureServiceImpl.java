package com.hualu.app.module.mems.mpc.service.impl;

import com.hualu.app.module.mems.mpc.dto.MpitemSystemDto;
import com.hualu.app.module.mems.mpc.entity.MpcDssMeasure;
import com.hualu.app.module.mems.mpc.mapper.MpcDssMeasureMapper;
import com.hualu.app.module.mems.mpc.service.MpcDssMeasureService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Service
public class MpcDssMeasureServiceImpl extends ServiceImpl<MpcDssMeasureMapper, MpcDssMeasure> implements MpcDssMeasureService {

    @Override
    public List<MpitemSystemDto> fetchMpitemSystems(Integer year) {
        return baseMapper.fetchMpitemSystems(year);
    }

    @Override
    public List<String> listDssType(String orgId, String mpSystemId) {
        List<String> dssTypes = baseMapper.listDssType(orgId,mpSystemId);
        return dssTypes;
    }
}
