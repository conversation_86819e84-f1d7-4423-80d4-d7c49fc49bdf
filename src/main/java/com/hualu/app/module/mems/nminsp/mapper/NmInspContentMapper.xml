<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmInspContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmInspContent">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="PID" property="pid" />
        <result column="TYPE" property="type" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="PART_ID" property="partId" />
        <result column="PAGE_SHOW" property="pageShow" />
        <result column="XC_TYPE" property="xcType" />
        <result column="INSP_FREQUENCY" property="inspFrequency" />
        <result column="SORT_NO" property="sortNo" />
        <result column="DSS_TYPE" property="dssType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, PID, TYPE, FACILITY_CAT, PART_ID, PAGE_SHOW, XC_TYPE, INSP_FREQUENCY, SORT_NO, DSS_TYPE
    </sql>

</mapper>
