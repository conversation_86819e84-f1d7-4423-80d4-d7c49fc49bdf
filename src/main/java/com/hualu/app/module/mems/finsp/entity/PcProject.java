package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.tg.dev.api.annotation.MD5Field;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * <p>
 * 结构物专项表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcProject对象", description="结构物专项表")
public class PcProject implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID")
    @TableId("PRJ_ID")
    private String prjId;

    @ApiModelProperty(value = "项目编码")
    @TableField("PRJ_CODE")
    private String prjCode;

    @NotBlank(message = "项目名称不能为空")
    @ApiModelProperty(value = "项目名称")
    @TableField("PRJ_NAME")
    private String prjName;

    @MD5Field
    @ApiModelProperty(value = "项目年份")
    @TableField("PRJ_YEAR")
    private Integer prjYear;

    @MD5Field
    @NotBlank(message = "单位类型不能为空")
    @ApiModelProperty(value = "单位类型（1：定检单位，2：养护单位）")
    @TableField("MNT_TYPE")
    private String mntType;

    @MD5Field
    @NotBlank(message = "设施类型不能为空")
    @ApiModelProperty(value = "设施类型（BP，HD、QL）")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @NotBlank(message = "排查单位名称不能为空")
    @ApiModelProperty(value = "排查单位名称")
    @TableField("INSP_ORG_NAME")
    private String inspOrgName;

    @MD5Field
    @ApiModelProperty(value = "项目公司ID")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "最新项目（1：是，0：否）")
    @TableField("LATEST")
    private Integer latest;

    @ApiModelProperty(value = "MD5（区分项目唯一值）")
    @TableField("MD5")
    private String md5;

    @TableLogic
    @TableField(value = "DEL_FLAG",fill = FieldFill.INSERT)
    private Integer delFlag;
}
