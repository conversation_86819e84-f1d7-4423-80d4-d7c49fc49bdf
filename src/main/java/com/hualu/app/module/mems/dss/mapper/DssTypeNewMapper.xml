<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dss.mapper.DssTypeNewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dss.entity.DssTypeNew">
        <result column="DSS_TYPE_ID" property="dssTypeId" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_TYPE_NAME" property="dssTypeName" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="STRUCT_POSITION" property="structPosition" />
        <result column="QULT_DESC_WAY" property="qultDescWay" />
        <result column="DSS_REF_IMG" property="dssRefImg" />
        <result column="DSS_DRAW_ICON" property="dssDrawIcon" />
        <result column="ORG_ID" property="orgId" />
        <result column="MAX_DEGREE" property="maxDegree" />
        <result column="IS_SYS" property="isSys" />
        <result column="P_DSS_TYPE_ID" property="pDssTypeId" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="HAVE_DSS_L" property="haveDssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="HAVE_DSS_W" property="haveDssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="HAVE_DSS_D" property="haveDssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="HAVE_DSS_N" property="haveDssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="HAVE_DSS_A" property="haveDssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="HAVE_DSS_V" property="haveDssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="HAVE_DSS_P" property="haveDssP" />
        <result column="HAVE_DSS_G" property="haveDssG" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="HAVE_DSS_COLOM" property="haveDssColom" />
        <result column="HAVE_DSS_UNIT" property="haveDssUnit" />
        <result column="DSS_ORDER_NUMBER" property="dssOrderNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_TYPE_ID, DSS_TYPE, DSS_TYPE_NAME, STRUCT_COMP_ID, STRUCT_POSITION, QULT_DESC_WAY, DSS_REF_IMG, DSS_DRAW_ICON, ORG_ID, MAX_DEGREE, IS_SYS, P_DSS_TYPE_ID, IS_DELETED, HAVE_DSS_L, DSS_L_UNIT, HAVE_DSS_W, DSS_W_UNIT, HAVE_DSS_D, DSS_D_UNIT, HAVE_DSS_N, DSS_N_UNIT, HAVE_DSS_A, DSS_A_UNIT, HAVE_DSS_V, DSS_V_UNIT, HAVE_DSS_P, HAVE_DSS_G, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, HAVE_DSS_COLOM, HAVE_DSS_UNIT, DSS_ORDER_NUMBER
    </sql>

</mapper>
