package com.hualu.app.module.mems.finsp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairStatDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspGpsCountDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspRunningDto;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【DM_FINSP】的数据库操作Mapper
* @createDate 2023-04-11 17:15:31
* @Entity generator.domain.DmFinsp
*/
public interface DmFinspMapper extends BaseMapper<DmFinsp> {

    @Update("UPDATE dm_finsp d SET d.DSS_NUM = ( select count(1) from dm_Finsp_record b where b.Finsp_id = d.Finsp_id ) where d.Finsp_id=#{finspId}")
    void updateDssNum(@Param("finspId") String finspId);


    @Update("update DM_FINSP set status=#{status} where processinstid = #{processInstId}")
    void updateStatus(@Param("processInstId") long processInstId, @Param("status") int status);


    DmFinsp getLast(@Param("ew") QueryWrapper queryWrapper);


    /**
     * 用于查询BP 202305版本的检查检查单数据
     * @param mntOrgId
     * @return
     */
    List<DmFinsp> getFinVersionBy202305(@Param("mntOrgId") String mntOrgId);

    List<DmFinspRunningDto> selectRunning(@Param("orgIds") Set<String> orgIds,@Param("currentDay") String currentDay,
                                          @Param("now") String now, @Param("minDate") String minDate);

    /**
     * 统计边坡轨迹个数
     * @param routeCodes
     * @param months
     * @return
     */
    DmFinspGpsCountDto countGps(@Param("routeCodes") Set<String> routeCodes, @Param("months") List<String> months);

    List<DmFinspDssRepairDto> selectDssRepairPage(Page page,@Param("orgCodes") Set<String> orgCodes,@Param("month") String month);

    List<DmFinspDssRepairStatDto> selectDssNum(@Param("month") String month, @Param("orgCodes") List<String> orgCodes);

    List<DmFinspDssRepairStatDto> selectDssRepairNum(@Param("month") String month,@Param("orgCodes") List<String> orgCodes);


    List<DmFinsp> listByCycle(@Param("structId") String structId,@Param("facilityCat") String facilityCat,@Param("months") List<String> months);
}




