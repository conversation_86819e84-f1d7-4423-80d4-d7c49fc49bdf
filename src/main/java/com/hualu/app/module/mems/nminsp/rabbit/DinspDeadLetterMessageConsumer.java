package com.hualu.app.module.mems.nminsp.rabbit;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.amqp.core.Message;
import com.hualu.app.config.InspQueueConfig;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

@Service
public class DinspDeadLetterMessageConsumer {

    //@RabbitListener(queues = InspQueueConfig.DINSP_DEAD_LETTER_QUEUE_NAME)
    public void receiveDeadLetterMessage(Message message) {
        String content = StrUtil.str(message.getBody(), CharsetUtil.CHARSET_UTF_8);
        System.out.println("Received task order dead letter message: " + content);
        // 这里可以添加对死信消息的特殊处理逻辑，如记录日志、人工干预等
    }
}    