package com.hualu.app.module.mems.notice.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.dto.FacPartTypeDto;
import com.hualu.app.module.facility.dto.FacQueryDto;
import com.hualu.app.module.facility.impl.FacQlImpl;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.notice.entity.DmNoticeDetail;
import com.hualu.app.module.mems.notice.mapper.DmNoticeDetailMapper;
import com.hualu.app.module.mems.notice.service.DmNoticeDetailService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_UploadHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.util.hp.H_KeyWorker;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class DmNoticeDetailServiceImpl extends ServiceImpl<DmNoticeDetailMapper, DmNoticeDetail>
    implements DmNoticeDetailService {

  @Resource
  DssImageService imageService;

  @Override public IPage<DmNoticeDetail> getNoticeList(
      IPage<DmNoticeDetail> p,
      String noticeId
  ) {
    IPage<DmNoticeDetail> ps = getBaseMapper().getNoticeList(p, noticeId);
    if (!CollectionUtils.isEmpty(ps.getRecords())) {
      ps.getRecords().forEach(this::initDssDisplayNum);
    }
    return ps;
  }

  @Override public boolean deleteNoticeDetailById(String noticeDetailId) {
    return removeById(noticeDetailId);
  }

  @Override public DmNoticeDetail getRoadNoticeDetail(String noticeDetailId) {
    DmNoticeDetail dmNoticeDetail = null;
    if (StringUtils.hasText(noticeDetailId)) {
      dmNoticeDetail = getBaseMapper().getRoadNoticeDetail(noticeDetailId);
    }
    setViewRecord(dmNoticeDetail);
    if (dmNoticeDetail == null) {
      dmNoticeDetail = new DmNoticeDetail();
      dmNoticeDetail.setFacilityCat("LM");
      dmNoticeDetail.setFacilityCatName("路面");
      dmNoticeDetail.setLineDirect("1");
      dmNoticeDetail.setLineDirectName("上行");
      dmNoticeDetail.setDssImpFlag((short) 0);
      dmNoticeDetail.setDssQuality(0);
      dmNoticeDetail.setDssQualityName("新病害");
      dmNoticeDetail.setFoundDate(DateUtil.date());
    }

    return dmNoticeDetail;
  }

  @Override public boolean saveOrUpdateNoticeDetail(
      DmNoticeDetail dmNoticeDetail,
      MultipartFile[] files
  ) {
    //上传病害图片
    List<String> fileIds = H_UploadHelper.uploadFile(files);
    if (CollectionUtil.isNotEmpty(fileIds)) {
      dmNoticeDetail.setFileIds(StrUtil.join(",", fileIds));
    }

    if (!StringUtils.hasText(dmNoticeDetail.getDssId())) {
      dmNoticeDetail.setDssId(H_KeyWorker.nextIdToString());
    }
    imageService.saveDssImageForApp(dmNoticeDetail.getDssId(), StrUtil.join(",", fileIds), 1);
    return saveOrUpdate(dmNoticeDetail);
  }

  private BaseStructDto getStructDto(String structId, String facilityCat) {
    IBaseStructFace
        iFacBase =
        CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
            .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
    if (iFacBase != null) {
      return iFacBase.getId(structId);
    }
    return null;
  }

  /**
   * 前台界面数据回显
   *
   * @param item 通知书详情
   */
  private void setViewRecord(DmNoticeDetail item) {
    if (item == null) {
      return;
    }
    String facilityCat = item.getFacilityCat();

    //结构物名称
    BaseStructDto structDto = getStructDto(item.getStructId(), facilityCat);
    if (structDto != null) {
      item.setStructName(structDto.getStructName());
    }

    //桥涵部件
    List<FacPartTypeDto> partTypeDtos = selectPartTypes(item.getStructId(),
        item.getFacilityCat(), "");
    initPartType(facilityCat, item, partTypeDtos);

    //桥涵构件
    if (H_BasedataHepler.QL.equals(facilityCat) || H_BasedataHepler.HD.equals(facilityCat)) {
      item.setStructCompName(item.getStructCompId());
    }

    //桥梁病害位置、部位
    if (H_BasedataHepler.QL.equals(facilityCat)) {
      FacQueryDto queryDto = new FacQueryDto();
      queryDto.setStructId(item.getStructId());
      queryDto.setStructPartId(item.getStructPartId());
      queryDto.setStructCompId(item.getStructCompId());
      FacQlImpl bean = CustomApplicationContextHolder.getBean(FacQlImpl.class);
      bean.selectPartpost(queryDto)
          .stream()
          .filter(v -> v.getPartpostD().equals(item.getDssPosition()))
          .findFirst()
          .ifPresent(facPartpostDto -> item.setDssPositionName(facPartpostDto.getPostName()));
    }

    initDssDisplayNum(item);
    initImages(Lists.newArrayList(item));
  }

  /**
   * 设置结构物部件名称
   *
   * @param record
   */
  private void initPartType(String facilityCat, DmNoticeDetail record,
      List<FacPartTypeDto> partTypeDtos) {
    if (H_BasedataHepler.SD.equals(facilityCat)) {
      return;
    }
    //桥涵部件反查
    if (StrUtil.isNotBlank(record.getStructId()) && StrUtil.isNotBlank(record.getStructPartId())) {
      partTypeDtos.stream()
          .filter(w -> record.getStructPartId().equals(w.getPartsCode()))
          .findFirst().ifPresent(typeDto -> record.setStructPartName(typeDto.getPartstypeName()));
    }
  }

  private List<FacPartTypeDto> selectPartTypes(String structId, String facilityCat,
      String finVersion) {

    List<FacPartTypeDto> partTypeDtos = Lists.newArrayList();
    //桥涵部件反查
    if (StrUtil.isNotBlank(structId)) {
      FacQueryDto dto = new FacQueryDto();
      dto.setStructId(structId);
      dto.setFinVersion(finVersion);
      Map<String, IFacBase> beansOfType =
          CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
      for (IFacBase bean : beansOfType.values()) {
        if (!bean.getFacilityCat().equals(facilityCat)) {
          continue;
        }
        partTypeDtos = bean.selectPartTypes(dto);
        break;
      }
    }
    return partTypeDtos;
  }

  /**
   * 设置病害照片
   *
   * @param dmNoticeDetails
   */
  private void initImages(List<DmNoticeDetail> dmNoticeDetails) {
    if (CollectionUtil.isEmpty(dmNoticeDetails)) {
      return;
    }
    Set<String> dssIds =
        dmNoticeDetails.stream().map(DmNoticeDetail::getDssId).collect(Collectors.toSet());
    Map<String, List<String>> imageMap = imageService.mapByDssIds(dssIds);
    for (DmNoticeDetail item : dmNoticeDetails) {
      List<String> fileIds = imageMap.get(item.getDssId());
      if (CollectionUtil.isNotEmpty(fileIds)) {
        item.setFileIds(StrUtil.join(",", fileIds));
      }
    }
  }

  private void initDssDisplayNum(DmNoticeDetail entity) {
    String str = "";

    if (entity.getDssL() != null
        && !StringUtil.isNullOrEmpty(entity.getDssLUnit())
        && entity.getDssL() > 0) {
      str += "长：" + entity.getDssL() + entity.getDssLUnit() + " ";
    }

    if (entity.getDssW() != null
        && !StringUtil.isNullOrEmpty(entity.getDssWUnit())
        && entity.getDssW() > 0) {
      str += "宽：" + entity.getDssW() + entity.getDssWUnit() + " ";
    }

    if (entity.getDssD() != null
        && !StringUtil.isNullOrEmpty(entity.getDssDUnit())
        && entity.getDssD() > 0) {
      str += "深：" + entity.getDssD() + entity.getDssDUnit() + " ";
    }

    if (entity.getDssA() != null
        && !StringUtil.isNullOrEmpty(entity.getDssAUnit())
        && entity.getDssA() > 0) {
      str += "面积：" + entity.getDssA() + entity.getDssAUnit() + " ";
    }

    if (entity.getDssV() != null
        && !StringUtil.isNullOrEmpty(entity.getDssVUnit())
        && entity.getDssV() > 0) {
      str += "体积：" + entity.getDssV() + entity.getDssVUnit() + " ";
    }

    if (entity.getDssN() != null
        && !StringUtil.isNullOrEmpty(entity.getDssNUnit())
        && entity.getDssN() > 0) {
      str += "数量：" + entity.getDssN() + entity.getDssNUnit() + " ";
    }

    if (entity.getDssP() != null && entity.getDssP() > 0) {
      str += "百分比：" + entity.getDssP() + "% ";
    }

    if (entity.getDssG() != null && entity.getDssG() > 0) {
      str += "角度：" + entity.getDssP() + "度 ";
    }
    entity.setDssNum(str);
  }
}
