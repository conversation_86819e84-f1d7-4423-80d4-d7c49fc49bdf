package com.hualu.app.module.mems.dinsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmDinsp对象", description="")
public class DmDinsp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DINSP_ID")
    private String dinspId;

    @TableField("DINSP_CODE")
    private String dinspCode;

    @TableField("LINE_CODE")
    private String lineCode;

    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @TableField("MNTN_ORG_NM")
    private String mntnOrgNm;

    @TableField("INSP_DATE")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date inspDate;

    @TableField("INSP_TIME_INTVL_M")
    private Integer inspTimeIntvlM;

    @TableField("INSP_SCOPE_M")
    private String inspScopeM;

    @TableField("WEATHER_M")
    private String weatherM;

    @TableField("INSP_DISTANCE_M")
    private String inspDistanceM;

    @TableField("INSP_PERSON_M")
    private String inspPersonM;

    @TableField("INSP_CAR_M")
    private String inspCarM;

    @TableField("INSP_TIME_INTVL_A")
    private Integer inspTimeIntvlA;

    @TableField("INSP_SCOPE_A")
    private String inspScopeA;

    @TableField("WEATHER_A")
    private String weatherA;

    @TableField("INSP_DISTANCE_A")
    private String inspDistanceA;

    @TableField("INSP_PERSON_A")
    private String inspPersonA;

    @TableField("INSP_CAR_A")
    private String inspCarA;

    @TableField("INSP_TIME_INTVL_N")
    private Integer inspTimeIntvlN;

    @TableField("INSP_SCOPE_N")
    private String inspScopeN;

    @TableField("WEATHER_N")
    private String weatherN;

    @TableField("INSP_DISTANCE_N")
    private String inspDistanceN;

    @TableField("INSP_PERSON_N")
    private String inspPersonN;

    @TableField("INSP_CAR_N")
    private String inspCarN;

    @TableField("REMARK")
    private String remark;

    @TableField("STATUS")
    private Integer status;

    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField("PROCESSINSTID")
    private Long processinstid;

    @TableField("SEARCH_DEPT")
    private String searchDept;

    @TableField("INSP_TIME_INTVL_START_M")
    private String inspTimeIntvlStartM;

    @TableField("INSP_TIME_INTVL_END_M")
    private String inspTimeIntvlEndM;

    @TableField("INSP_TIME_INTVL_START_A")
    private String inspTimeIntvlStartA;

    @TableField("INSP_TIME_INTVL_END_A")
    private String inspTimeIntvlEndA;

    @TableField("INSP_TIME_INTVL_START_N")
    private String inspTimeIntvlStartN;

    @TableField("INSP_TIME_INTVL_END_N")
    private String inspTimeIntvlEndN;

    @ApiModelProperty(value = "日常巡查单版本")
    @TableField("DIN_VERSION")
    private String dinVersion;

    @TableField("PATROLUSERNAME")
    private String patrolusername;

    @ApiModelProperty(value = "病害数量")
    @TableField("DSS_NUM")
    private Integer dssNum;

    @TableField("TYPE")
    private String type;

    @TableField("STRUCT_INFO")
    private String structInfo;

    @ApiModelProperty(value = "负责人")
    @TableField("PRINCIPAL")
    private String principal;

    @TableField("MAINTAIN")
    private String maintain;

    @ApiModelProperty(value = "来源")
    @TableField("SOURCE")
    private String source;

    @ApiModelProperty(value = "巡查类型（1：养护单位，0：路段公司）")
    @TableField("XC_TYPE")
    private Integer xcType;

    @TableField(exist = false)
    private String lineName;
}
