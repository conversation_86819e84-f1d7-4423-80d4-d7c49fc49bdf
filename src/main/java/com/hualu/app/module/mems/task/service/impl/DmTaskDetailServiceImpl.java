package com.hualu.app.module.mems.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskHzDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskDetail;
import com.hualu.app.module.mems.task.mapper.DmTaskDetailMapper;
import com.hualu.app.module.mems.task.service.DmTaskDetailService;
import com.hualu.app.module.mems.task.utils.H_TaskDssHelper;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_StakeHelper;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Service
public class DmTaskDetailServiceImpl extends ServiceImpl<DmTaskDetailMapper, DmTaskDetail> implements DmTaskDetailService {

    @Autowired
    private BaseDatathirdDicService dicService;

    @Autowired
    private DssImageService imageService;

    @Override
    public void updateStatus(List<String> accptDtlIds) {
        baseMapper.updateStatus(accptDtlIds);
    }

    @Override
    public void delByMtaskId(String mtaskId) {
        LambdaQueryWrapper<DmTaskDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTaskDetail::getMtaskId,mtaskId);
        remove(queryWrapper);
    }

    @Override
    public List<DmTaskDetailDto> selectTaskDetailsByTaskId(String dmTaskId) {
        if (StrUtil.isBlank(dmTaskId)){
            return Lists.newArrayList();
        }
        return baseMapper.selectDetailsByTaskId(dmTaskId);
    }

    @Override
    public List<DmTaskDetailDto> selectNoAccpt(List<String> mtaskIds) {

        QueryWrapper<DmTaskDetailDto> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("dtd.mtask_id",mtaskIds);
        queryWrapper.notExists("select 1 from DM_TASK_ACCPT_DETAIL where dtd.MTASK_DTL_ID = MTASK_DTL_ID");
        List<DmTaskDetailDto> details = baseMapper.selectNoAccpt(queryWrapper);
        /*queryWrapper.in(DmTaskDetail::getMtaskId,mtaskIds);
        queryWrapper.notExists("select 1 from DM_TASK_ACCPT_DETAIL where DM_TASK_DETAIL.MTASK_DTL_ID = MTASK_DTL_ID");*/
        //List<DmTaskDetail> details = list(queryWrapper);

        List<DmTaskDetailDto> dtos = Lists.newArrayList();
        details.forEach(item->{
            DmTaskDetailDto dto = new DmTaskDetailDto();
            BeanUtil.copyProperties(item,dto);
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public DmTaskHzDto getTaskHzDto(String dmTaskId) {
        DmTaskHzDto hzDto = new DmTaskHzDto();
        hzDto.setMtaskId(dmTaskId);
        List<DmTaskDssViewDto> dtos = baseMapper.selectDssNumAndMoney(dmTaskId);
        hzDto.setDssDtos(dtos);
        if (CollectionUtil.isNotEmpty(dtos)){
            hzDto.setDssNum(dtos.size());
            double totalMoney = dtos.stream().map(DmTaskDssViewDto::getMoney).mapToDouble(Double::doubleValue).sum();
            hzDto.setTotalMoney(totalMoney);
        }
        //病害集合
        List<String> dssIds = dtos.stream().map(DmTaskDssViewDto::getDssId).collect(Collectors.toList());
        List<DmTaskDssViewDto> dssDtos = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(dssIds)){
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.in("di.dss_id",dssIds);
            dssDtos = baseMapper.selectDssInfos(queryWrapper);
        }
        if (CollectionUtil.isNotEmpty(dssDtos)){
            Map<String, List<DssImage>> imageMap = imageService.mapGroupByDssIds(dssIds);
            //查询图片地址
            //setViewInfo(dssDtos,imageMap);
            H_TaskDssHelper.setViewInfo(dssDtos,imageMap);
            String contrId = dtos.get(0).getContrId();
            LinkedHashMap<String, List<DmTaskMpItemViewDto>> mpMap = selectMpItemsByDssIds(contrId, dmTaskId, dssIds);

            //设置签到回显值
            Map<String, DmTaskDssViewDto> dssMap = dssDtos.stream().collect(Collectors.toMap(DmTaskDssViewDto::getDssId, Function.identity()));
            dtos.forEach(item->{
                DmTaskDssViewDto dssDto = dssMap.get(item.getDssId());
                BeanUtil.copyProperties(dssDto,item,"mpNum","money","contrId");
                List<DmTaskMpItemViewDto> mpItemViewDtos = mpMap.get(item.getDssId());
                item.setMpItemViewDtos(mpItemViewDtos);
            });
        }
        return hzDto;
    }

    /**
     * 获取病害对应措施
     * @param contrId
     * @param mtaskId
     * @param dssIds
     * @return
     */
    private LinkedHashMap<String, List<DmTaskMpItemViewDto>> selectMpItemsByDssIds(String contrId, String mtaskId, List<String> dssIds){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("mmp.CONTR_ID",contrId);
        queryWrapper.eq("dtd.MTASK_ID",mtaskId);
        queryWrapper.in("dtd.DSS_ID",dssIds);
        List<DmTaskMpItemViewDto> mpItemViewDtos = baseMapper.selectMpItemsByDssIds(queryWrapper);
        mpItemViewDtos.forEach(item->{
            item.setMpitemAccountName(item.getMpitemAccount()+"（"+item.getMeasureUnit()+"）");
        });
        LinkedHashMap<String, List<DmTaskMpItemViewDto>> mpMap = mpItemViewDtos.stream().collect(Collectors.groupingBy(e -> e.getDssId(), LinkedHashMap::new, Collectors.toList()));
        return mpMap;
    }

    @Override
    public List<DmTaskMpItemViewDto> selectMpItems(String contrId, String mtaskId, String dssId) {
        List<DmTaskMpItemViewDto> itemDtos = baseMapper.selectMpItems(contrId, mtaskId, dssId);
        if (CollectionUtil.isNotEmpty(itemDtos)){
            itemDtos.forEach(item->{
                item.setMpitemAccountName(item.getMpitemAccount()+"（"+item.getMeasureUnit()+"）");
            });
        }
        return itemDtos;
    }


    /**
     * 设置前端页面回显值
     * @param dtos
     */
    public void setViewInfo(List<DmTaskDssViewDto> dtos,Map<String, List<DssImage>> imageMap){
        dtos.forEach(item->{
            if (StrUtil.isNotBlank(item.getStructId())){
                BaseStructDto structDto = H_BasedataHepler.getStructDto(item.getStructId(), item.getFacilityCat());
                item.setStructName(structDto.getStructName());
            }
            item.setLineDirectName(H_BasedataHepler.directMap.get(item.getLinedirect()));
            item.setFacilityCatName(dicService.getDicName("FACILITY_CAT",item.getFacilityCat()));
            item.setLaneName(dicService.getDicName("LANE",item.getLane()));
            item.setLineName(H_BasedataHepler.getLineName(item.getMainRoadId()));
            item.setStakeName(H_StakeHelper.convertCnStake(item.getRlStakeNew().toString()));

            //设置病害位置信息
            if (StrUtil.isNotBlank(item.getStructName())){
                item.setPositionName(item.getStructName());
            }else if (StrUtil.isNotBlank(item.getLaneName())){
                item.setPositionName(item.getLaneName());
            }else {
                item.setPositionName(item.getLineDirectName());
            }
            //设置病害照片
            List<DssImage> images = imageMap.get(item.getDssId());
            if (CollectionUtil.isNotEmpty(images)){
                images.forEach(row->{
                    //病害照片为空时，表示修复前照片
                    if (ObjectUtil.isEmpty(row.getProcessing())){
                        row.setProcessing(0);
                    }
                });
                Map<Integer, List<DssImage>> processMap = images.stream().collect(Collectors.groupingBy(DssImage::getProcessing));
                //维修后照片
                List<DssImage> afterImages = processMap.get(1);
                if (afterImages != null){
                    String afterFileIds = afterImages.stream().map(DssImage::getFileId).collect(Collectors.joining(","));
                    item.setAfterFileIds(afterFileIds);
                }
                //维修前照片
                List<DssImage> preImages = processMap.get(0);
                if (preImages != null){
                    String fileIds = preImages.stream().map(DssImage::getFileId).collect(Collectors.joining(","));
                    item.setFileIds(fileIds);
                }
                item.setImageHost(C_Constant.IMAGE_HOST);
            }
        });
    }
}
