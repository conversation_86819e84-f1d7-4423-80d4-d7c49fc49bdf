package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.dto.PcFinspResultDto;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspRecordWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspItem;
import com.hualu.app.module.mems.finsp.entity.PcFinspRecord;
import com.hualu.app.module.mems.finsp.entity.PcFinspResult;
import com.hualu.app.module.mems.finsp.mapper.PcFinspResultMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspItemService;
import com.hualu.app.module.mems.finsp.service.PcFinspRecordService;
import com.hualu.app.module.mems.finsp.service.PcFinspResultService;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 结构物排查结论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Service
public class PcFinspResultServiceImpl extends ServiceImpl<PcFinspResultMapper, PcFinspResult> implements PcFinspResultService {

    @Autowired
    PcFinspItemService itemService;

    @Lazy
    @Autowired
    PcFinspRecordService recordService;

    @Override
    public void createFinspResult(String finspId, String facilityCat) {
        List<PcFinspItem> pcFinspItems = itemService.listByFacilityCat(facilityCat);

        List<PcFinspResult> pcFinspResults = new ArrayList<>();
        for (PcFinspItem pcFinspItem : pcFinspItems) {
            PcFinspResult pcFinspResult = new PcFinspResult();
            pcFinspResult.setFinspId(finspId).setFinspItemId(pcFinspItem.getFinspItemId())
                    .setFinspResult("0");
            pcFinspResults.add(pcFinspResult);
        }
        if (CollectionUtil.isNotEmpty(pcFinspResults)){
            saveBatch(pcFinspResults);
        }
    }

    @Override
    public void updateFinspResult(PcFinsp pcFinsp) {
        // 根据检查单ID,查询所有病害信息
        List<PcFinspRecord> pcFinspRecords = recordService.listRecordByFinspId(pcFinsp.getFinspId());
        Map<String, PcFinspResultDto> resMap = initRes(pcFinspRecords, pcFinsp.getFacilityCat());
        List<PcFinspResult> pcFinspResults = listByFinspId(pcFinsp.getFinspId());
        // 结论为空时，补充措施
        if (CollectionUtil.isEmpty(pcFinspResults)){
            //throw new BaseException("检查单未生成检查结论");
            return;
        }
        for (PcFinspResult result : pcFinspResults) {
            PcFinspResultDto dto = resMap.get(result.getFinspItemId());
            if (dto == null) {
                result.setFinspResult("0").setFinspDesc(null);
            }else {
                result.setFinspResult(dto.getFinspResult()).setFinspDesc(dto.getFinspDesc());
            }
        }
        updateBatchById(pcFinspResults);
    }

    @Override
    public void removeByFinspIds(List<String> finspIds) {
        LambdaQueryWrapper<PcFinspResult> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(PcFinspResult::getFinspId, finspIds);
        remove(lambdaQueryWrapper);
    }

    @Override
    public List<PcFinspRecordWordDto> getRecordWord(String finspId) {
        List<PcFinspRecordWordDto> wordDtos = baseMapper.listRecordWord(Lists.newArrayList(finspId),null);
        return wordDtos;
    }

    @Override
    public List<PcFinspRecordWordDto> listRecordWord(List<String> finspIds,String prjId) {
        List<PcFinspRecordWordDto> wordDtos = baseMapper.listRecordWord(finspIds,prjId);
        return wordDtos;
    }


    private Map<String, PcFinspResultDto> initRes(List<PcFinspRecord> pcFinspRecords,String facilityCat){
        List<PcFinspItem> pcFinspItems = itemService.listByFacilityCat(facilityCat);
        List<PcFinspResultDto> resultDtos = new ArrayList<>();
        // 根据检查项中的part_code（病害类型归类）
        for (PcFinspItem item : pcFinspItems) {
            String partCode = item.getPartCode();
            if (StrUtil.isBlank(partCode)){
                continue;
            }
            PcFinspResultDto resultDto = new PcFinspResultDto();
            List<String> dsstypeIds = StrUtil.split(partCode, ",");
            String dssDesc = pcFinspRecords.stream().filter(e -> dsstypeIds.contains(e.getDsstypeId())).map(PcFinspRecord::getDssDesc).collect(Collectors.joining(";"));
            // 表示匹配对应的病害
            if (StrUtil.isNotBlank(dssDesc)){
                resultDto.setFinspResult("1").setFinspDesc(dssDesc);
            }else {
                resultDto.setFinspResult("0");
            }
            resultDto.setFinspItemId(item.getFinspItemId());
            resultDtos.add(resultDto);
        }
        Map<String, PcFinspResultDto> resMap = resultDtos.stream().collect(Collectors.toMap(PcFinspResultDto::getFinspItemId, Function.identity()));
        return resMap;
    }
    
    private List<PcFinspResult> listByFinspId(String finspId) {
        LambdaQueryWrapper<PcFinspResult> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PcFinspResult::getFinspId,finspId);
        return list(lambdaQueryWrapper);
    }
}
