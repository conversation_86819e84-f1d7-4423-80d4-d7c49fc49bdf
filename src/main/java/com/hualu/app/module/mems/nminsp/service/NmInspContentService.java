package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新版日常巡检及经常检查巡查内容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface NmInspContentService extends IService<NmInspContent> {

    /**
     * 查询巡查内容
     * @param facilityCat 设施类型
     * @param xcType 巡查类型：DM、FM
     * @return
     */
    List<NmInspContent> listByContent(String facilityCat,String xcType);

    /**
     * 查询巡查内容
     * @param facilityCat 设施类型
     * @param xcType 巡查类型：DM、FM
     * @param pageShow 界面显隐 1:显示，0：隐藏  null：查询全部
     * @return
     */
    List<NmInspContent> listByContent(String facilityCat,String xcType,String pageShow);

    /**
     * 展示对应的病害类型信息
     * @param partId
     * @param facilityCat
     * @param xcType
     * @return
     */
    List<DssTypeNew> listDssTypeByPartId(String partId, String facilityCat, String xcType);


    /**
     * 获取巡查内容名称
     * @param ids
     * @return
     */
    Map<String,String> getContentMap(List<String> ids);
}
