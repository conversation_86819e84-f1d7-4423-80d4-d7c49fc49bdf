package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.nminsp.dto.FailDinspDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.nminsp.vo.NmDinspExtraInfo;
import com.hualu.app.module.mems.nminsp.vo.NmDinspFacilityCatGroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 新版日常巡查公共单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface NmDinspCommonMapper extends BaseMapper<NmDinspCommon> {

    NmDinspCommon lastNmDinspCommon( @Param("orgIdString") String orgIdString);

    /**
     * 根据设施分组，统计总单数
     * @param commonDinspId
     * @return
     */
    List<NmDinspFacilityCatGroupVo> countTotalCount(@Param("commonDinspId") String commonDinspId);

    /**
     * 根据设施分组，统计当前待办数
     * @param commonDinspId
     * @return
     */
    List<NmDinspFacilityCatGroupVo> countTodoCount(@Param("commonDinspId") String commonDinspId,@Param("taskSql") String taskSql);

    IPage<NmDinspCommon> selectPageFor(
        Page<NmDinspCommon> page,
        @Param("ew") QueryWrapper<NmDinspCommon> queryWrapper,
        @Param("sql") String taskSql
    );

    List<NmDinspExtraInfo> getNmDinspCommonToto(
        @Param("ids") List<String> ids,
        @Param("sql") String taskSql
    );

    List<NmDinspExtraInfo> getNmDinspCommonTotal(
        @Param("ids") List<String> ids
    );

    List<NmDinspExtraInfo> getNmDinspCommonDss(
        @Param("ids") List<String> ids
    );

    List<FailDinspDto> listFailDinsp();
}
