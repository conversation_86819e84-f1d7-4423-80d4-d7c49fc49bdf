package com.hualu.app.module.mems.nminsp.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

public class RouteInspection {
    @Excel(name = "序号")
    private Integer order;

    @Excel(name = "路段")
    private String routeName;

    @Excel(name = "路线")
    private String lineCode;

    @Excel(name = "路线方向")
    private String lineDirect;

    @Excel(name = "设施分类")
    private String facilityCategory;

    @Excel(name = "车道（结构物）桩号")
    private String stake;
    @Excel(name = "病害类型")
    private String dssTypeName;
    @Excel(name = "位置及描述")
    private String dssDetail;
    @Excel(name = "巡查单号")
    private String dinspCode;
    @Excel(name = "发现日期")
    private Date inspDate;

    private String dssId;

    private String fileEntityPath;
    @Excel(name = "任务单号")
    private String mtaskCode; //任务单
    @Excel(name = "验收单单号")
    private String mtaskAccptCode; //验收单单号
    @Excel(name = "维修日期")
    private Date repairDate; //修复日期

    private String fileUrl;

    private String remark;

    private String structId;

    private String fileEntityId;

    private Integer pothoNums;

    //图片保存
    @Excel(name = "病害照片", type = 2, width = 20, height = 40)
    private byte[] productImage;

    public RouteInspection() {
    }

    public RouteInspection(String routeName, String lineCode, String lineDirect, String facilityCategory, String stake, String dssTypeName, String dssDetail, String dinspCode, Date inspDate, String dssId, String fileEntityPath, String mtaskCode, String mtaskAccptCode, Date repairDate, String fileUrl, String remark, byte[] productImage) {
        this.order = this.order + 1;
        this.routeName = routeName;
        this.lineCode = lineCode;
        this.lineDirect = lineDirect;
        this.facilityCategory = facilityCategory;
        this.stake = stake;
        this.dssTypeName = dssTypeName;
        this.dssDetail = dssDetail;
        this.dinspCode = dinspCode;
        this.inspDate = inspDate;
        this.dssId = dssId;
        this.fileEntityPath = fileEntityPath;
        this.mtaskCode = mtaskCode;
        this.mtaskAccptCode = mtaskAccptCode;
        this.repairDate = repairDate;
        this.fileUrl = fileUrl;
        this.remark = remark;
        this.productImage = productImage;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public String getFacilityCategory() {
        return facilityCategory;
    }

    public void setFacilityCategory(String facilityCategory) {
        this.facilityCategory = facilityCategory;
    }

    public String getStake() {
        return stake;
    }

    public void setStake(String stake) {
        this.stake = stake;
    }

    public String getDssTypeName() {
        return dssTypeName;
    }

    public void setDssTypeName(String dssTypeName) {
        this.dssTypeName = dssTypeName;
    }

    public String getDssDetail() {
        return dssDetail;
    }

    public void setDssDetail(String dssDetail) {
        this.dssDetail = dssDetail;
    }

    public String getDinspCode() {
        return dinspCode;
    }

    public void setDinspCode(String dinspCode) {
        this.dinspCode = dinspCode;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getDssId() {
        return dssId;
    }

    public void setDssId(String dssId) {
        this.dssId = dssId;
    }

    public String getFileEntityPath() {
        return fileEntityPath;
    }

    public void setFileEntityPath(String fileEntityPath) {
        this.fileEntityPath = fileEntityPath;
    }

    public String getMtaskCode() {
        return mtaskCode;
    }

    public void setMtaskCode(String mtaskCode) {
        this.mtaskCode = mtaskCode;
    }

    public String getMtaskAccptCode() {
        return mtaskAccptCode;
    }

    public void setMtaskAccptCode(String mtaskAccptCode) {
        this.mtaskAccptCode = mtaskAccptCode;
    }

    public Date getRepairDate() {
        return repairDate;
    }

    public void setRepairDate(Date repairDate) {
        this.repairDate = repairDate;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public byte[] getProductImage() {
        return productImage;
    }

    public void setProductImage(byte[] productImage) {
        this.productImage = productImage;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = this.order;
    }

    public String getStructId() {
        return structId;
    }

    public void setStructId(String structId) {
        this.structId = structId;
    }

    public String getFileEntityId() {
        return fileEntityId;
    }

    public void setFileEntityId(String fileEntityId) {
        this.fileEntityId = fileEntityId;
    }

    public Integer getPothoNums() {
        return pothoNums;
    }

    public void setPothoNums(Integer pothoNums) {
        this.pothoNums = pothoNums;
    }
}
