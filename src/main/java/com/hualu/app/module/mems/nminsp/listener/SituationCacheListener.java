package com.hualu.app.module.mems.nminsp.listener;

import com.hualu.app.config.SituationMQConfig;
import com.hualu.app.module.mems.nminsp.service.SituationCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 巡查情况缓存消息监听器
 */
@Slf4j
@Component
public class SituationCacheListener {

    @Autowired
    private SituationCacheService situationCacheService;

    /**
     * 监听刷新缓存的消息
     * 使用专用的消费者容器工厂来限制消费速率
     */
    @RabbitListener(queues = SituationMQConfig.SITUATION_REFRESH_QUEUE, containerFactory = "situationRefreshContainerFactory")
    public void onRefreshCacheMessage(Map<String, Object> message) {
        try {
            log.info("收到刷新巡查情况缓存的消息: {}", message);
            
            // 获取参数
            String orgCode = (String) message.get("orgCode");
            int year = ((Number) message.get("year")).intValue();
            
            // 刷新缓存
            situationCacheService.refreshDinspSituation(orgCode, year);
            situationCacheService.refreshFinspSituation(orgCode, year);
            
            log.info("巡查情况缓存刷新完成，orgCode: {}, year: {}", orgCode, year);
        } catch (Exception e) {
            log.error("处理刷新巡查情况缓存的消息失败", e);
        }
    }
} 