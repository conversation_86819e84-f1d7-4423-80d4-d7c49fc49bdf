<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dinsp.mapper.DmDinspMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dinsp.entity.DmDinsp">
        <id column="DINSP_ID" property="dinspId" />
        <result column="DINSP_CODE" property="dinspCode" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNTN_ORG_NM" property="mntnOrgNm" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="INSP_TIME_INTVL_M" property="inspTimeIntvlM" />
        <result column="INSP_SCOPE_M" property="inspScopeM" />
        <result column="WEATHER_M" property="weatherM" />
        <result column="INSP_DISTANCE_M" property="inspDistanceM" />
        <result column="INSP_PERSON_M" property="inspPersonM" />
        <result column="INSP_CAR_M" property="inspCarM" />
        <result column="INSP_TIME_INTVL_A" property="inspTimeIntvlA" />
        <result column="INSP_SCOPE_A" property="inspScopeA" />
        <result column="WEATHER_A" property="weatherA" />
        <result column="INSP_DISTANCE_A" property="inspDistanceA" />
        <result column="INSP_PERSON_A" property="inspPersonA" />
        <result column="INSP_CAR_A" property="inspCarA" />
        <result column="INSP_TIME_INTVL_N" property="inspTimeIntvlN" />
        <result column="INSP_SCOPE_N" property="inspScopeN" />
        <result column="WEATHER_N" property="weatherN" />
        <result column="INSP_DISTANCE_N" property="inspDistanceN" />
        <result column="INSP_PERSON_N" property="inspPersonN" />
        <result column="INSP_CAR_N" property="inspCarN" />
        <result column="REMARK" property="remark" />
        <result column="STATUS" property="status" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="SEARCH_DEPT" property="searchDept" />
        <result column="INSP_TIME_INTVL_START_M" property="inspTimeIntvlStartM" />
        <result column="INSP_TIME_INTVL_END_M" property="inspTimeIntvlEndM" />
        <result column="INSP_TIME_INTVL_START_A" property="inspTimeIntvlStartA" />
        <result column="INSP_TIME_INTVL_END_A" property="inspTimeIntvlEndA" />
        <result column="INSP_TIME_INTVL_START_N" property="inspTimeIntvlStartN" />
        <result column="INSP_TIME_INTVL_END_N" property="inspTimeIntvlEndN" />
        <result column="DIN_VERSION" property="dinVersion" />
        <result column="PATROLUSERNAME" property="patrolusername" />
        <result column="DSS_NUM" property="dssNum" />
        <result column="TYPE" property="type" />
        <result column="STRUCT_INFO" property="structInfo" />
        <result column="PRINCIPAL" property="principal" />
        <result column="MAINTAIN" property="maintain" />
        <result column="SOURCE" property="source" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DINSP_ID, DINSP_CODE, LINE_CODE, MNT_ORG_ID, MNTN_ORG_NM, INSP_DATE, INSP_TIME_INTVL_M, INSP_SCOPE_M, WEATHER_M, INSP_DISTANCE_M, INSP_PERSON_M, INSP_CAR_M, INSP_TIME_INTVL_A, INSP_SCOPE_A, WEATHER_A, INSP_DISTANCE_A, INSP_PERSON_A, INSP_CAR_A, INSP_TIME_INTVL_N, INSP_SCOPE_N, WEATHER_N, INSP_DISTANCE_N, INSP_PERSON_N, INSP_CAR_N, REMARK, STATUS, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, PROCESSINSTID, SEARCH_DEPT, INSP_TIME_INTVL_START_M, INSP_TIME_INTVL_END_M, INSP_TIME_INTVL_START_A, INSP_TIME_INTVL_END_A, INSP_TIME_INTVL_START_N, INSP_TIME_INTVL_END_N, DIN_VERSION, PATROLUSERNAME, DSS_NUM, TYPE, STRUCT_INFO, PRINCIPAL, MAINTAIN, SOURCE
    </sql>

    <select id="queryGroupCount" resultType="com.hualu.app.module.mems.dinsp.entity.DmCountResult">
        with ORGS AS (select o.*
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{orgId}
        connect by prior o.ID = o.PARENT_ID)
        select count(1) as sum, nvl(sum(case when STATUS = 3 then 1 end),0)  as completed, nvl(sum(case when STATUS <![CDATA[<>]]> 3 then 1 end),0) unCompleted
        from MEMSDB.NM_DINSP P JOIN GDGS.BASE_ROUTE_LOGIC L ON P.ROUTE_CODE=L.ROUTE_CODE
        JOIN ORGS O ON L.OPRT_ORG_CODE=O.ORG_CODE
        where XC_TYPE = 2 and DEL_FLAG=0
        AND FACILITY_CAT = #{type}
    </select>

    <select id="queryFinspGroupCount" resultType="com.hualu.app.module.mems.dinsp.entity.DmCountResult">
        with ORGS AS (select o.*
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{orgId}
        connect by prior o.ID = o.PARENT_ID)
        select count(1) as sum, nvl(sum(case when STATUS = 3 then 1 end),0)  as completed, nvl(sum(case when STATUS <![CDATA[<>]]> 3 then 1 end),0) unCompleted
        from MEMSDB.NM_FINSP P JOIN GDGS.BASE_ROUTE_LOGIC L ON P.ROUTE_CODE=L.ROUTE_CODE
        JOIN ORGS O ON L.OPRT_ORG_CODE=O.ORG_CODE
        where XC_TYPE = 2 and DEL_FLAG=0
        AND FACILITY_CAT = #{type}
    </select>

    <select id="listInspectionLedgerBills" resultType="com.hualu.app.module.mems.dinsp.entity.InspectionLedgerDTO">
        with ORGS AS (select o.*
              from GDGS.FW_RIGHT_ORG o
              where o.IS_ENABLE = 1
                and o.IS_DELETED = 0
              start with o.ID = #{orgId}
              connect by prior o.ID = o.PARENT_ID)
        select decode(STATUS, 0, '未提交', 1, '已提交', 2, '审核中', 3, '已审核') as statusName,
               P.DSS_NUM as dssNums,
               P.DINSP_CODE AS dinspCode,
               P.INSP_DATE AS inspDate,
               l.LINE_CODE AS lineCode,
               l.ROUTE_NAME AS routeName,
               o.ORG_FULLNAME AS orgFullName,
               P.INSP_PERSON AS inspPerson,
               p.SEARCH_DEPT AS searchDept,
               DECODE(P.XC_TYPE, 2, '自动化巡检', '人工巡检') AS xcTypeName,
               P.WEATHER AS weather
        from MEMSDB.NM_DINSP P
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON P.ROUTE_CODE = L.ROUTE_CODE
                 JOIN ORGS O ON L.PRJ_ORG_CODE = O.ORG_CODE
        where P.DEL_FLAG = 0 and p.xc_type = 2
        <if test="status != null and status == 1">
            AND P.STATUS = 0
        </if>
        <if test="status != null and status == 2">
            AND P.STATUS = 2
        </if>
        <if test="status != null and status == 3">
            AND P.STATUS = 3
        </if>
    </select>

    <select id="listInspectionRCLedgerBills" resultType="com.hualu.app.module.mems.dinsp.entity.InspectionLedgerDTO">
        with ORGS AS (select o.*
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{orgId}
        connect by prior o.ID = o.PARENT_ID)
        select decode(STATUS, 0, '未提交', 1, '已提交', 2, '审核中', 3, '已审核') as statusName,
        P.DSS_NUM as dssNums,
        P.FINSP_CODE AS dinspCode,
        P.INSP_DATE AS inspDate,
        l.LINE_CODE AS lineCode,
        l.ROUTE_NAME AS routeName,
        o.ORG_FULLNAME AS orgFullName,
        P.INSP_PERSON AS inspPerson,
        p.SEARCH_DEPT AS searchDept,
        DECODE(P.XC_TYPE, 2, '自动化巡检', '人工巡检') AS xcTypeName,
        P.WEATHER AS weather
        from MEMSDB.NM_FINSP P
        JOIN GDGS.BASE_ROUTE_LOGIC L ON P.ROUTE_CODE = L.ROUTE_CODE
        JOIN ORGS O ON L.PRJ_ORG_CODE = O.ORG_CODE
        where P.DEL_FLAG = 0 and p.xc_type = 2
        <if test="status != null and status == 1">
            AND P.STATUS = 0
        </if>
        <if test="status != null and status == 2">
            AND P.STATUS = 2
        </if>
    </select>
    <!--日常巡查病害情况-->
    <select id="countDailyDefects" resultType="java.util.Map">
        with ORG as (select *
             from GDGS.FW_RIGHT_ORG o
             where o.IS_DELETED = 0
               and o.IS_ENABLE = 1
             start with o.ID = #{orgId}
             connect by prior o.ID = o.PARENT_ID)
        select SUM(DECODE(D.DSS_TYPE,'LMXC-020',1,0)) as A1,
               SUM(DECODE(D.DSS_TYPE,'LMXC-019',1,0)) as A2,
               SUM(DECODE(D.DSS_TYPE,'LMXC-018',1,0)) as A3,
               SUM(DECODE(D.DSS_TYPE,'LMXC-022',1,0)) as A4,
               SUM(DECODE(D.DSS_TYPE,'LMXC-017',d.DSS_L,0)) as A5,
               SUM(DECODE(D.DSS_TYPE,'LMXC-025',d.DSS_L,0)) as A6
        from MEMSDB.NM_DINSP_RECORD D JOIN MEMSDB.DSS_TYPE_NEW N ON D.DSS_TYPE=N.DSS_TYPE
            JOIN MEMSDB.NM_DINSP SP ON D.DINSP_ID=SP.DINSP_ID
            JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE=L.ROUTE_CODE
        JOIN ORG O ON L.OPRT_ORG_CODE=O.ORG_CODE
        where D.FACILITY_CAT = 'LM' AND  D.DEL_FLAG=0 and sp.xc_type = 2
    </select>
    <select id="calculateDefectsByCategory" resultType="java.util.Map">
        with ORG as (select *
                     from GDGS.FW_RIGHT_ORG o
                     where o.IS_DELETED = 0
                       and o.IS_ENABLE = 1
                     start with o.ID = #{orgId}
                     connect by prior o.ID = o.PARENT_ID)
        select SUM(DECODE(D.DSS_TYPE,'LMXC-020',1,0)) as A1,
               SUM(DECODE(D.DSS_TYPE,'LMXC-019',1,0)) as A2,
               SUM(DECODE(D.DSS_TYPE,'LMXC-018',1,0)) as A3,
               SUM(DECODE(D.DSS_TYPE,'LMXC-022',1,0)) as A4,
               SUM(DECODE(D.DSS_TYPE,'LMXC-017',d.DSS_L,0)) as A5,
               SUM(DECODE(D.DSS_TYPE,'LMXC-025',d.DSS_L,0)) as A6
        from MEMSDB.NM_FINSP_RECORD D JOIN MEMSDB.DSS_TYPE_NEW N ON D.DSS_TYPE=N.DSS_TYPE
            JOIN MEMSDB.NM_FINSP SP ON D.FINSP_ID=SP.FINSP_ID
            JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE=L.ROUTE_CODE
        JOIN ORG O ON L.OPRT_ORG_CODE=O.ORG_CODE
        where D.FACILITY_CAT = 'LM' AND  D.DEL_FLAG=0 and sp.xc_type = 2
    </select>

    <select id="indexDefectRecordsForFastQuery" resultType="com.hualu.app.module.mems.dinsp.entity.FacilityInspectionDTO">
            with ORG as (select *
             from GDGS.FW_RIGHT_ORG o
             where o.IS_DELETED = 0
               and o.IS_ENABLE = 1
             start with o.ID = #{orgId}
             connect by prior o.ID = o.PARENT_ID)
            select O.ORG_FULLNAME as organizationFullName,
                   L.ROUTE_NAME as routeName,
                   decode(D.FACILITY_CAT, 'LM', '路面', 'QL', '桥梁', 'HD', '涵洞', 'SD', '隧道', 'BP', '边坡', 'JA', '交安') as facilityType,
                   L.LINE_CODE as lineCode,
                   DECODE(L.LINE_DIRECT, 1, '上行', 2, '下行')  as direction,
                   d.STAKE as stakeNumber,
                   N.DSS_TYPE_NAME as defectTypeName,
                   '经常检查' as inspectionType,
                   D.DSS_ID as dssId
            from MEMSDB.NM_FINSP_RECORD D
                     JOIN MEMSDB.DSS_TYPE_NEW N ON D.DSS_TYPE = N.DSS_TYPE
                     JOIN MEMSDB.NM_FINSP SP ON D.FINSP_ID = SP.FINSP_ID
                     JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE = L.ROUTE_CODE
                     JOIN ORG O ON L.OPRT_ORG_CODE = O.ORG_CODE
            where D.DEL_FLAG = 0 and sp.xc_type = 2
    </select>
    <select id="indexDefectRecordsRcForFastQuery" resultType="com.hualu.app.module.mems.dinsp.entity.FacilityInspectionDTO">
        with ORG as (select *
             from GDGS.FW_RIGHT_ORG o
             where o.IS_DELETED = 0
               and o.IS_ENABLE = 1
             start with o.ID = #{orgId}
             connect by prior o.ID = o.PARENT_ID)
        select O.ORG_FULLNAME as organizationFullName,
                   L.ROUTE_NAME as routeName,
                   decode(D.FACILITY_CAT, 'LM', '路面', 'QL', '桥梁', 'HD', '涵洞', 'SD', '隧道', 'BP', '边坡', 'JA', '交安') as facilityType,
                   L.LINE_CODE as lineCode,
                   DECODE(L.LINE_DIRECT, 1, '上行', 2, '下行')  as direction,
                   d.STAKE as stakeNumber,
                   N.DSS_TYPE_NAME as defectTypeName,
                   '日常巡查' as inspectionType,
                   D.DSS_ID as dssId
            from MEMSDB.NM_DINSP_RECORD D
                     JOIN MEMSDB.DSS_TYPE_NEW N ON D.DSS_TYPE = N.DSS_TYPE
                     JOIN MEMSDB.NM_DINSP SP ON D.DINSP_ID = SP.DINSP_ID
                     JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE = L.ROUTE_CODE
                     JOIN ORG O ON L.OPRT_ORG_CODE = O.ORG_CODE
            where D.DEL_FLAG = 0 and sp.xc_type = 2
    </select>
    <select id="getImageFileId" resultType="java.lang.String">
        SELECT I.FILE_ID
        FROM MEMSDB.NM_DINSP_RECORD C JOIN MEMSDB.DSS_IMAGE I ON C.DSS_ID=I.DSS_ID
        WHERE I.DEL_FLAG = 0 AND I.DSS_ID=#{dssId}
    </select>

    <select id="InspectionResult" resultType="com.hualu.app.module.mems.dinsp.entity.InspectionResult">
        with ORG as (select *
             from GDGS.FW_RIGHT_ORG o
             where o.IS_DELETED = 0
               and o.IS_ENABLE = 1
             start with o.ID = #{orgId}
             connect by prior o.ID = o.PARENT_ID)
        select N.DSS_TYPE_NAME as dssTypeName,
               DECODE(N.HAVE_DSS_UNIT, 'm', D.DSS_L, 'm2', D.DSS_A, '处', D.DSS_N) as workNums,
               DECODE(N.HAVE_DSS_UNIT, 'm', '米', 'm2', '平方', '处', '处')            as workUnit,
               SP.INSP_DATE as inspDate,
               DECODE(O.REPAIR_STATUS, 0, '待维修', 1, '修复中', 2, '已修复')              AS repairStatus,
               A.MTASK_ACCPT_CODE                                                 as acceptanceCode,
               aa.MTASK_CODE                                                      as taskCode,
               '车载巡查' as xcType,
               SP.FINSP_CODE as dinspCode
        from MEMSDB.NM_FINSP_RECORD D
                 LEFT JOIN MEMSDB.DSS_INFO O ON D.DSS_ID = O.DSS_ID
                 LEFT JOIN MEMSDB.DM_TASK_ACCPT_DETAIL b on O.DSS_ID = b.DSS_ID
                 LEFT JOIN MEMSDB.DM_TASK_ACCPT a on a.MTASK_ACCPT_ID = b.MTASK_ACCPT_ID
                 LEFT JOIN MEMSDB.DM_TASK_DETAIL bs on O.DSS_ID = bs.DSS_ID
                 LEFT JOIN MEMSDB.DM_TASK aa on bs.MTASK_ID = aa.MTASK_ID
                 JOIN MEMSDB.DSS_TYPE_NEW N ON D.DSS_TYPE = N.DSS_TYPE
                 JOIN MEMSDB.NM_FINSP SP ON D.FINSP_ID = SP.FINSP_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE = L.ROUTE_CODE
                 JOIN ORG O ON L.OPRT_ORG_CODE = O.ORG_CODE
        where D.DEL_FLAG = 0 AND D.DSS_ID=#{dssId}
        UNION ALL
        select N.DSS_TYPE_NAME as dssTypeName,
               DECODE(N.HAVE_DSS_UNIT, 'm', D.DSS_L, 'm2', D.DSS_A, '处', D.DSS_N) as workNums,
               DECODE(N.HAVE_DSS_UNIT, 'm', '米', 'm2', '平方', '处', '处')            as workUnit,
               SP.INSP_DATE as inspDate,
               DECODE(O.REPAIR_STATUS, 0, '待维修', 1, '修复中', 2, '已修复')              AS repairStatus,
               A.MTASK_ACCPT_CODE                                                 as acceptanceCode,
               aa.MTASK_CODE                                                      as taskCode,
               '车载巡查' as xcType,
               SP.DINSP_CODE as dinspCode
        from MEMSDB.NM_DINSP_RECORD D
                 LEFT JOIN MEMSDB.DSS_INFO O ON D.DSS_ID = O.DSS_ID
                 LEFT JOIN MEMSDB.DM_TASK_ACCPT_DETAIL b on O.DSS_ID = b.DSS_ID
                 LEFT JOIN MEMSDB.DM_TASK_ACCPT a on a.MTASK_ACCPT_ID = b.MTASK_ACCPT_ID
                 LEFT JOIN MEMSDB.DM_TASK_DETAIL bs on O.DSS_ID = bs.DSS_ID
                 LEFT JOIN MEMSDB.DM_TASK aa on bs.MTASK_ID = aa.MTASK_ID
                 JOIN MEMSDB.DSS_TYPE_NEW N ON D.DSS_TYPE = N.DSS_TYPE
                 JOIN MEMSDB.NM_DINSP SP ON D.DINSP_ID = SP.DINSP_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE = L.ROUTE_CODE
                 JOIN ORG O ON L.OPRT_ORG_CODE = O.ORG_CODE
        where D.DEL_FLAG = 0 AND D.DSS_ID=#{dssId}
    </select>

    <select id="InspectionRecord" resultType="com.hualu.app.module.mems.dinsp.entity.InspectionRecord">
        with ORG as (select *
                     from GDGS.FW_RIGHT_ORG o
                     where o.IS_DELETED = 0
                       and o.IS_ENABLE = 1
                     start with o.ID = #{orgId}
                     connect by prior o.ID = o.PARENT_ID)
        select O.ORG_NAME as orgName,L.ROUTE_NAME as routeName,decode(L.LINE_DIRECT,'1','上行','2','下行') as lineDirect,
               D.STAKE as stake,L.ROUTE_LENGTH as length,
               '经常检查' as checkType ,abs(d.END_STAKE_NUM - d.START_STAKE_NUM) as checkLength,
               SP.SEARCH_DEPT as searchDept,SP.INSP_PERSON as inspPerson,SP.INSP_DATE as inspDate
        from MEMSDB.NM_FINSP_RECORD D
                 JOIN MEMSDB.NM_FINSP SP ON D.FINSP_ID = SP.FINSP_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE = L.ROUTE_CODE
                 JOIN ORG O ON L.OPRT_ORG_CODE = O.ORG_CODE
        where D.DEL_FLAG = 0 and sp.xc_type = 2
        UNION ALL
        select O.ORG_NAME as orgName,L.ROUTE_NAME as routeName,decode(L.LINE_DIRECT,'1','上行','2','下行') as lineDirect,
               D.STAKE as stake,L.ROUTE_LENGTH as length,
               '经常检查' as checkType ,abs(d.END_STAKE_NUM - d.START_STAKE_NUM) as checkLength,
               SP.SEARCH_DEPT as searchDept,SP.INSP_PERSON as inspPerson,SP.INSP_DATE as inspDate
        from MEMSDB.NM_DINSP_RECORD D
                 JOIN MEMSDB.NM_DINSP SP ON D.DINSP_ID = SP.DINSP_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON SP.ROUTE_CODE = L.ROUTE_CODE
                 JOIN ORG O ON L.OPRT_ORG_CODE = O.ORG_CODE
        where D.DEL_FLAG = 0 and sp.xc_type = 2
    </select>
</mapper>
