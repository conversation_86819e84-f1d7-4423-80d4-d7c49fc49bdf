package com.hualu.app.module.mems.task.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.mems.dss.controller.DssInfoController;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.mpc.dto.MpcMpitemDto;
import com.hualu.app.module.mems.mpc.entity.MpcMpitem;
import com.hualu.app.module.mems.mpc.service.MpcMpitemService;
import com.hualu.app.module.mems.task.dto.taskdetail.*;
import com.hualu.app.module.mems.task.entity.DmTaskDetail;
import com.hualu.app.module.mems.task.mapper.DmTaskDetailMapper;
import com.hualu.app.module.mems.task.service.DmTaskDetailService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *  任务单病害详情
 * <AUTHOR>
 * @since 2023-06-15
 */
@Validated
@RestController
@RequestMapping("/dmTaskDetail")
public class DmTaskDetailController extends M_MyBatisController<DmTaskDetail, DmTaskDetailMapper> {

    @Autowired
    DmTaskDetailService detailService;

    @Autowired
    DssInfoService dssInfoService;

    @Autowired
    DssInfoController infoController;

    @Autowired
    MpcMpitemService itemService;

    @Autowired
    DssImageService imageService;

    /**
     * 批量添加病害措施（病害关联）
     * @param details
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("addMpItem")
    public RestResult<List<DmTaskDetail>> addMpitem(@Valid @RequestBody List<DmTaskDetail> details){
        details.forEach(detail -> {
            detail.setMtaskDtlId(H_KeyWorker.nextIdToString());
            detail.setIsAdd(1);
            detail.setAcceptStatus(0);
            detail.setIsLump(0);
        });
        //设置病害管理状态为已处置 dealStatus=1
        Set<String> dssIds = details.stream().map(DmTaskDetail::getDssId).collect(Collectors.toSet());
        String mtaskId = null;
        DmTaskDetail dmTaskDetail = details.stream().findFirst().orElse(null);
        if (dmTaskDetail !=null){
            mtaskId = dmTaskDetail.getMtaskId();
        }
        dssInfoService.updateDealStatus(Lists.newArrayList(dssIds),mtaskId,1);
        detailService.saveBatch(details);
        return RestResult.success(details);
    }

    /**
     * 查询病害关联养护措施
     * @param dssId 病害ID（可以为空）
     * @param dssType 病害类型（必填）
     * @param contrId 合同ID（必填）
     * @param mpitemName 措施名称（模糊查询）
     * @return
     */
    @PostMapping("selectMpitemsByDssId")
    public RestResult<List<MpcMpitemDto>> selectMpitemsByDssId(String dssId, String dssType, @RequestParam String contrId, String mpitemName){
        List<MpcMpitem> mpcMpitems = itemService.selectMpitemsByDssId(dssId,dssType, contrId, mpitemName);
        List<MpcMpitemDto> dtos = Lists.newArrayList();
        mpcMpitems.forEach(item->{
            MpcMpitemDto dto = BeanUtil.toBean(item, MpcMpitemDto.class);
            dtos.add(dto);
        });
        return RestResult.success(dtos);
    }

    /**
     * 查询未处置病害（选择病害）
     * @return
     */
    @PostMapping("selectNoDealDss")
    public RestResult<List<DssInfoDto>> selectNoDealDss(){
        RestResult<List<DssInfoDto>> result = infoController.selectDssPage();
        List<DssInfoDto> dssDatas = result.getData();
        initViewDto(dssDatas);
        return result;
    }

    /**
     * 回显前端界面（部件名称、构建名称、车道名称、病害描述）
     * @param dssDatas
     */
    private void initViewDto(List<DssInfoDto> dssDatas) {
        if (CollectionUtil.isEmpty(dssDatas)){
            return;
        }
        dssDatas.forEach(item->{
            initDssDisplayNum(item);
            item.setPositionName(item.getDssPosition());
        });
    }

    /**
     * 显示病害
     * @param entity
     */
    private void initDssDisplayNum(DssInfoDto entity){
        StringBuffer str = new StringBuffer();
        Double dssL = entity.getDssL();
        if(dssL!= null && StrUtil.isNotBlank(entity.getDssLUnit()) && BigDecimal.valueOf(dssL).compareTo(BigDecimal.ZERO) > 0){
            str.append("长："+ dssL + entity.getDssLUnit()+" ");
        }
        Double dssW = entity.getDssW();
        if(dssW!= null && StrUtil.isNotBlank(entity.getDssWUnit()) && BigDecimal.valueOf(dssW).compareTo(BigDecimal.ZERO) > 0){
            str.append("宽："+ dssW + entity.getDssWUnit()+" ");
        }
        Double dssD = entity.getDssD();
        if(dssD!= null && StrUtil.isNotBlank(entity.getDssDUnit()) && BigDecimal.valueOf(dssD).compareTo(BigDecimal.ZERO) > 0){
            str.append("深："+ dssD + entity.getDssDUnit()+" ");
        }
        Double dssA = entity.getDssA();
        if(dssA!= null && StrUtil.isNotBlank(entity.getDssAUnit()) && BigDecimal.valueOf(dssA).compareTo(BigDecimal.ZERO) > 0){
            str.append("面积："+ dssA + entity.getDssAUnit()+" ");
        }
        Double dssV = entity.getDssV();
        if(dssV!= null && StrUtil.isNotBlank(entity.getDssVUnit()) && BigDecimal.valueOf(dssV).compareTo(BigDecimal.ZERO) > 0){
            str.append("体积："+ dssV + entity.getDssVUnit()+" ");
        }
        Double dssN = entity.getDssN();
        if(dssN!= null && StrUtil.isNotBlank(entity.getDssNUnit()) && BigDecimal.valueOf(dssN).compareTo(BigDecimal.ZERO) > 0){
            str.append("数量："+ dssN + entity.getDssNUnit()+" ");
        }
        Double dssP = entity.getDssP();
        if(dssP!= null && BigDecimal.valueOf(dssP).compareTo(BigDecimal.ZERO) > 0){
            str.append("百分比："+ dssP +"% ");
        }
        Double dssG = entity.getDssG();
        if(dssG!= null && BigDecimal.valueOf(dssG).compareTo(BigDecimal.ZERO) > 0){
            str.append("角度："+ dssG +"度 ");
        }
        entity.setDssNum(str.toString());
    }

    /**
     * 新增任务病害（病害新增）
     * @param detail
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("saveDmTaskDetail")
    public RestResult saveDmTaskDetail(@RequestBody DmTaskDetailAndMpitemDto detail){
        initDssInfo(detail.getDssInfo());
        initDetails(detail.getDssInfo().getDssId(),detail.getItemDtos());
        return RestResult.success("操作成功");
    }

    private void initDetails(String dssId,List<DmTaskMpItemDto> itemDtos){
        if (itemDtos == null){
            throw new BaseException("维修措施不能为空");
        }
        List<DmTaskDetail> detailList = Lists.newArrayList();
        itemDtos.forEach(item->{
            DmTaskDetail bean = BeanUtil.toBean(item, DmTaskDetail.class);
            bean.setAcceptStatus(0);
            bean.setIsAdd(1);
            bean.setDssId(dssId);
            detailList.add(bean);
        });
        detailService.saveBatch(detailList);
    }

    /**
     * 初始化病害信息
     * @param dssInfo
     */
    private void initDssInfo(DssInfo dssInfo){
        //生成病害ID
        dssInfo.setDssId(H_KeyWorker.nextIdToString());
        dssInfo.setDssSource(5);//任务单直接新增
        dssInfo.setRepairStatus(0);//待修复
        dssInfo.setDealStatus(1);//已处置
        dssInfo.setDealMeasure(1);//处置方式{id:1,text:'直接下达处理'},{id:2,text:'纳入下月计划'},{id:3,text:'预留下年处理'},{id:4,text:'纳入专项'},{id:5,text:'不处理'}
        dssInfo.setDealDate(LocalDateTime.now());
        dssInfo.setDealPrsn(CustomRequestContextHolder.getUserCode());
        if (StrUtil.isNotBlank(dssInfo.getStructId())){
            BaseStructDto structDto = H_BasedataHepler.getStructDto(dssInfo.getStructId(), dssInfo.getFacilityCat());
            dssInfo.setRoutecode(structDto.getRouteCode());
            dssInfo.setRlStakeNew(structDto.getRlCntrStake());
        }else {
            //获取当前路段信息
            Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth(CustomRequestContextHolder.getUserCode(), dssInfo.getMainRoadId());
            if (CollectionUtil.isNotEmpty(routeCodes)){
                dssInfo.setRoutecode(routeCodes.stream().findFirst().get());
            }
        }
        //todo 物理桩号未存储起来
        dssInfoService.save(dssInfo);
    }
    /**
     * 分页查询
     * @param mtaskId
     * @return
     */
    @PostMapping("selectPage")
    public RestResult<List<DmTaskDetailDto>> selectPage(@RequestParam String mtaskId){
        List<DmTaskDetailDto> dmTaskDetailDtos = detailService.selectTaskDetailsByTaskId(mtaskId);
        return RestResult.success(dmTaskDetailDtos);
    }

    /**
     * 查询病害清单
     * @param mtaskId 任务单ID
     * @return
     */
    @PostMapping("selectTaskDss")
    public RestResult<DmTaskHzDto> selectTaskDetails(@RequestParam String mtaskId){
        DmTaskHzDto taskHzDto = detailService.getTaskHzDto(mtaskId);
        return RestResult.success(taskHzDto);
    }

    /**
     * 查询措施清单（已废弃）
     * @param contrId 合同ID
     * @param mtaskId 任务单ID
     * @param dssId 病害ID
     * @return
     */
    @PostMapping("selectMpItems")
    public RestResult<List<DmTaskMpItemViewDto>> selectMpItems(String contrId, String mtaskId, String dssId){
        List<DmTaskMpItemViewDto> itemDtos = detailService.selectMpItems(contrId, mtaskId, dssId);
        return RestResult.success(itemDtos);
    }

    /**
     * 删除措施（多个措施以逗号分隔）
     * @param mtaskDtlId 主键ID
     * @return
     */
    @PostMapping("delMpItem")
    public RestResult delMpItem(@RequestParam String mtaskDtlId){
        List<String> ids = StrUtil.split(mtaskDtlId, ",");
        if (CollectionUtil.isNotEmpty(ids)){
            Collection<DmTaskDetail> dmTaskDetails = detailService.listByIds(ids);
            //获取历史病害ID
            Set<String> dssIds = dmTaskDetails.stream().map(DmTaskDetail::getDssId).collect(Collectors.toSet());
            detailService.removeByIds(ids);

            Collection<DmTaskDetail> newDetails = detailService.listByIds(ids);
            //现有数据库ID
            Set<String> newDssIds = newDetails.stream().map(DmTaskDetail::getDssId).collect(Collectors.toSet());

            dssIds.removeAll(newDssIds);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 修改勘估数量
     * @param mtaskDtlId 主键ID
     * @param mpitemAccount 勘估数量
     * @return
     */
    @PostMapping("updateMpItemAccount")
    public RestResult updateMpItemAccount(@RequestParam String mtaskDtlId,@RequestParam Double mpitemAccount){
        DmTaskDetail item = new DmTaskDetail();
        item.setMtaskDtlId(mtaskDtlId);
        item.setMpitemAccount(mpitemAccount);
        detailService.updateById(item);
        return RestResult.success("操作成功");
    }

    /**
     * 照片上传
     * @param dssId 病害ID
     * @param processing 0：维修前  1：维修后
     * @param files 图片文件
     * @return
     */
    @PostMapping("uploadDssImage")
    public RestResult uploadDssImage(@RequestParam(value = "dssId",required = true) String dssId, @RequestParam(value = "processing",required = true) Integer processing,
                                     @RequestParam(value = "files") MultipartFile[] files){
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            imageService.saveDssImageForApp(dssId,StrUtil.join(",",fileIds),1,processing);
        }
        return RestResult.success(StrUtil.join(",",fileIds), "操作成功");
    }
}
