package com.hualu.app.module.mems.mpc.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.mpc.dto.MpcContractDto;
import com.hualu.app.module.mems.mpc.service.MpcContractService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *  养护合同（维修项目）
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@RestController
@RequestMapping("/mpcContract")
public class MpcContractController {

    @Autowired
    MpcContractService service;

    /**
     * 根据当前机构，查询合同列表
     * @return
     */
    @GetMapping("list")
    public RestResult<List<MpcContractDto>> list(){
        List<MpcContractDto> dtos = service.selectMpcContracts(CustomRequestContextHolder.getOrgIdString());
        return RestResult.success(dtos);
    }
}
