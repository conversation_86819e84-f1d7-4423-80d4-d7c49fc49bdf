package com.hualu.app.module.mems.dinsp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.mems.dinsp.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface DmDinspService extends IService<DmDinsp> {

    DmDinsp getDmDinspByProcessInstId(Long processInstId);

    /**
     * 日常巡查审批办理（
     * @param userCode 当前用户
     * @param processInstIds  流程实例ID
     * @param nextUserId 下一节点审批人
     * @param apprOpinion 审批意见
     * @param status 状态
     */
    void approveDmDinsp(String userCode, String processInstIds, String nextUserId, String apprOpinion,int status);

    /**
     * 修改日常巡查单审批状态
     * @param processInstId
     * @param status
     */
    void updateDmDinspStatus(long processInstId, int status);

    /**
     * 删除日常巡查病害
     * @param ids
     * @param dinspId
     */
    void deleteRecord(String[] ids, String dinspId);

    /**
     * 删除日常巡查主单
     * @param dinspId
     */
    void deleteDmDinsp(String dinspId);

    /**
     * 更新检查结论
     * @param result
     */
    void updateResult(DmDinResult result);

    List<DmCountResult> queryGroupCount(String orgId);

    List<DmCountResult> queryFinspGroupCount(String orgId);

    IPage<InspectionLedgerDTO> listInspectionLedgerBills(int pageNum, int pageSize, String orgId, String status);

    IPage<InspectionLedgerDTO> listInspectionRCLedgerBills(int pageNum, int pageSize, String orgId, String status);

    Map<String, Object> countDailyDefects(String orgId);

    Map<String, Object> calculateDefectsByCategory(String orgId);

    IPage<FacilityInspectionDTO> indexDefectRecordsForFastQuery(int pageNum, int pageSize, String orgId);

    IPage<FacilityInspectionDTO> indexDefectRecordsRcForFastQuery(int pageNum, int pageSize, String orgId);

    List<String> getImageFileId(String dssId);

    List<InspectionResult> InspectionResult(String orgId, String dssId);

    IPage<InspectionRecord> InspectionRecord(int pageNum, int pageSize, String orgId);
}
