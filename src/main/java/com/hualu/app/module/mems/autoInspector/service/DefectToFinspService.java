package com.hualu.app.module.mems.autoInspector.service;

import com.hualu.app.module.mems.autoInspector.dto.ConvertInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayOrgRef;
import com.hualu.app.module.mems.autoInspector.entity.NmFinspVo;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;

import java.util.List;
import java.util.Map;

/**
 * 病害信息转巡查记录服务接口
 * <p>
 * 提供病害信息转换为巡查记录的服务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface DefectToFinspService {

    /**
     * 批量转换病害为巡查记录
     *
     * @param defectIds 病害ID列表
     * @return 转换结果的巡查单VO列表
     */
    List<NmFinspVo> batchConvert(List<String> defectIds);

    List<NmFinspVo> batchConvert(ConvertInfo convertInfo);

    /**
     * 从病害信息提取路线编码
     *
     * @param defectInfo 病害信息
     * @return 路线编码
     */
    String extractLineCode(HighwayDefectInfo defectInfo);

    /**
     * 根据病害信息创建巡查单
     *
     * @param defectInfo 病害信息
     * @param lineMap 路线信息
     * @param orgRefs 管养单位信息
     * @return 巡查单
     */
    NmFinsp createFinspForDefects(HighwayDefectInfo defectInfo, Map<String, BaseLine> lineMap, List<HighwayOrgRef> orgRefs);

    /**
     * 从路段名称设置路线信息
     *
     * @param nmFinsp 巡查单
     * @param defectInfo 病害信息
     */
    void setLineInfoFromSegment(NmFinsp nmFinsp, HighwayDefectInfo defectInfo);

    /**
     * 将病害信息转换为巡查记录项
     *
     * @param defectInfo 病害信息
     * @param finspId 巡查单ID
     * @return 巡查记录项
     */
    NmFinspRecord convertToFinspRecord(HighwayDefectInfo defectInfo, String finspId);

    /**
     * 设置管养单位信息
     *
     * @param nmFinsp 巡查单
     * @param defectInfo 病害信息
     * @param orgRefs 管养单位信息
     */
    void setMaintenanceOrg(NmFinsp nmFinsp, HighwayDefectInfo defectInfo, List<HighwayOrgRef> orgRefs);

    /**
     * 将桩号转换为公里数值
     *
     * @param stake 桩号
     * @return 公里数值
     */
    Double stakeToKilometer(String stake);

    /**
     * 将数字车道号转换为字母表示
     *
     * @param laneNumber 数字车道号
     * @param direction 行车方向
     * @return 字母车道号
     */
    String convertLaneNumberToLetter(String laneNumber, String direction);
} 