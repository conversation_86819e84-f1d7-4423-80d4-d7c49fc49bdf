package com.hualu.app.module.mems.finsp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class PcFinspDssTypeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病害类型ID
     */
    private String dssTypeId;

    /**
     * 病害名称
     */
    private String dssTypeName;

    /**
     * 分组名称，没有构件时，该字段为空
     */
    private String groupName;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 部件名称
     */
    private String partTypeName;

    /**
     * 部件ID
     */
    private String partType;

    @JsonIgnore
    private String pid;
}
