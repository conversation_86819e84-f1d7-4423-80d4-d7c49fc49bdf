<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dss.mapper.DssImageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dss.entity.DssImage">
        <id column="DSS_IMAGE_ID" property="dssImageId" />
        <result column="DSS_ID" property="dssId" />
        <result column="FILE_ID" property="fileId" />
        <result column="IMAGE_TYPE" property="imageType" />
        <result column="PROCESSING" property="processing" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_IMAGE_ID, DSS_ID, FILE_ID, IMAGE_TYPE, PROCESSING
    </sql>

</mapper>
