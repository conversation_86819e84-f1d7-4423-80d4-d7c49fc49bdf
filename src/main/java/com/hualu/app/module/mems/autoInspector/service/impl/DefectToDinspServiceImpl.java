package com.hualu.app.module.mems.autoInspector.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.mems.autoInspector.entity.*;
import com.hualu.app.module.mems.autoInspector.service.*;
import com.hualu.app.module.mems.autoInspector.utils.DefectToDinspConverter;
import com.hualu.app.module.mems.autoInspector.utils.HttpForAutoUtils;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.api.util.hp.H_KeyWorker;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 病害信息转日常巡检记录服务实现类
 * <p>
 * 提供病害信息转换为日常巡检记录的服务实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
@Service
public class DefectToDinspServiceImpl implements DefectToDinspService {

    @Autowired
    private HighwayDefectInfoService highwayDefectInfoService;

    @Autowired
    private NmDinspService nmDinspService;

    @Autowired
    private BaseLineService baseLineService;

    @Autowired
    private HighwayOrgRefService orgRefService;

    @Autowired
    private HighwaySegmentService highwaySegmentService;

    @Autowired
    private HighwayUserConfigService highwayUserConfigService;

    @Autowired
    private NmDinspRecordService recordService;

    @Autowired
    private HighwayDefectMediaService mediaService;

    @Autowired
    private DssImageService dssImageService;

    // 路段缓存，提高性能
    private Map<String, HighwaySegment> segmentCache;

    private static String processDefName="gdcg.emdc.mems.dm.NDinspWorkFlow";

    /**
     * 将URL图片列表转换为MultipartFile数组
     * @param imageUrls 图片URL列表
     * @return MultipartFile数组
     */
    @SneakyThrows
    private MultipartFile[] convertUrlsToMultipartFiles(List<String> imageUrls) {
        if (imageUrls == null || imageUrls.isEmpty()) {
            return new MultipartFile[0];
        }

        List<MultipartFile> multipartFiles = new ArrayList<>();
        for (String imageUrl : imageUrls) {
            if (StrUtil.isBlank(imageUrl)) {
                continue;
            }

            try {
                URL url = new URL(imageUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                connection.connect();

                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                    // 从URL获取文件名
                    String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
                    if (fileName.contains("?")) {
                        fileName = fileName.substring(0, fileName.indexOf("?"));
                    }

                    // 读取图片数据
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    try (InputStream inputStream = connection.getInputStream()) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }

                    byte[] imageData = outputStream.toByteArray();

                    // 创建MultipartFile
                    MultipartFile multipartFile = new MockMultipartFile(
                            fileName,
                            fileName,
                            connection.getContentType(),
                            imageData
                    );

                    multipartFiles.add(multipartFile);
                }
            } catch (Exception e) {
                log.error("转换URL到MultipartFile失败: {}", imageUrl, e);
            }
        }

        return multipartFiles.toArray(new MultipartFile[0]);
    }

    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public List<NmDinspVo> batchConvert(List<String> defectIds) {
        // 查询病害信息
        List<HighwayDefectInfo> defectInfoList = new ArrayList<>(highwayDefectInfoService.listByIds(defectIds));
        if (CollectionUtil.isEmpty(defectInfoList)) {
            return new ArrayList<>();
        }

        // 预加载所有路段数据
        loadAllSegments();

        // 按路线分组
        Map<String, List<HighwayDefectInfo>> lineGroupedDefects = defectInfoList.stream()
                .collect(Collectors.groupingBy(this::extractLineCode));

        // 获取路线信息和管养单位信息
        Map<String, BaseLine> lineMap = baseLineService.getAllLineMap();
        List<HighwayOrgRef> orgRefs = orgRefService.list();

        // 为每个路线创建日常巡检单，并添加病害记录
        List<NmDinspVo> nmDinspVoList = new ArrayList<>();
        for (Map.Entry<String, List<HighwayDefectInfo>> entry : lineGroupedDefects.entrySet()) {
            String lineCode = entry.getKey();
            List<HighwayDefectInfo> lineDefects = entry.getValue();

            // 创建日常巡检单
            NmDinsp nmDinsp = createDinspForDefects(lineDefects.get(0), lineMap, orgRefs);

            // 批量查询和处理病害相关图片，减少数据库访问
            List<String> currentDefectIds = lineDefects.stream()
                    .map(HighwayDefectInfo::getId)
                    .collect(Collectors.toList());

            // 一次性查询所有相关的病害媒体
            LambdaQueryWrapper<HighwayDefectMedia> mediaQuery = new LambdaQueryWrapper<>();
            mediaQuery.in(HighwayDefectMedia::getDefectId, currentDefectIds);
            List<HighwayDefectMedia> allMedias = mediaService.list(mediaQuery);

            // 按病害ID分组
            Map<String, List<HighwayDefectMedia>> mediaMap = allMedias.stream()
                    .collect(Collectors.groupingBy(HighwayDefectMedia::getDefectId));

            // 为每个病害创建日常巡检记录项
            List<NmDinspRecord> recordList = new ArrayList<>();
            List<NmDinspRecord> recordsToSave = new ArrayList<>();

            for (HighwayDefectInfo defect : lineDefects) {
                NmDinspRecord record = convertToDinspRecord(defect, nmDinsp.getDinspId());
                record.setDinspId(nmDinsp.getDinspId());
                H_CValidator.validator2Exception(record);
                H_StructHelper.validateNmDinspRecord(record);

                // 从内存中获取该病害相关的媒体
                List<HighwayDefectMedia> medias = mediaMap.getOrDefault(defect.getId(), Collections.emptyList());

                // 获取本地文件路径列表
                List<String> localPaths = medias.stream()
                        .map(HighwayDefectMedia::getLocalPath)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                MultipartFile[] files = convertLocalPathsToMultipartFiles(localPaths);

                // 处理文件上传
                List<String> fileIds = HttpForAutoUtils.uploadImage(files);
                dssImageService.saveDssImageForPC(record.getDssId(),fileIds.stream().filter(Objects::nonNull).collect(Collectors.joining(",")),1);

                // 添加到批量保存列表，而不是立即保存
                recordsToSave.add(record);
                recordList.add(record);
                defect.setIsCreated(1);

                log.info("病害[{}]转为日常巡检记录项，处理了{}张图片", defect.getId(), localPaths.size());
            }

            // 批量保存记录
            if (!recordsToSave.isEmpty()) {
                recordService.saveBatch(recordsToSave);
                H_StructHelper.refreshNmDinspResult(nmDinsp);
                log.info("批量保存了{}条巡检记录", recordsToSave.size());
            }

            // 批量更新病害状态
            if (!lineDefects.isEmpty()) {
                highwayDefectInfoService.updateBatchById(lineDefects);
            }

            nmDinsp.setDssNum(recordList.size());
            nmDinspService.updateById(nmDinsp);

            // 创建NmDinspVo并设置记录
            NmDinspVo nmDinspVo = new NmDinspVo();
            BeanUtils.copyProperties(nmDinsp, nmDinspVo);
            nmDinspVo.setRecords(recordList);

            nmDinspVoList.add(nmDinspVo);

            log.info("为路线[{}]创建了日常巡检单，id为{}，包含{}条病害记录", lineCode, nmDinsp.getDinspId(), recordList.size());
        }

        log.info("病害批量转换成功：共{}个日常巡检单", nmDinspVoList.size());
        return nmDinspVoList;
    }

    /**
     * 加载所有路段数据到缓存
     */
    private void loadAllSegments() {
        if (segmentCache == null || segmentCache.isEmpty()) {
            synchronized (this) {
                if (segmentCache == null) {
                    segmentCache = new ConcurrentHashMap<>();
                }
                // 加载所有路段数据
                List<HighwaySegment> allSegments = highwaySegmentService.list();
                if (!CollectionUtils.isEmpty(allSegments)) {
                    for (HighwaySegment segment : allSegments) {
                        segmentCache.put(segment.getId(), segment);
                    }
                    log.info("已加载{}条路段数据到缓存", allSegments.size());
                }
            }
        }
    }

    @Override
    public String extractLineCode(HighwayDefectInfo defectInfo) {
        // 优先使用路段ID直接从路段表查询
        if (defectInfo.getSegmentId() != null && !defectInfo.getSegmentId().trim().isEmpty()) {
            String segmentId = defectInfo.getSegmentId();

            // 从缓存中获取路段信息
            HighwaySegment segment = segmentCache.get(segmentId);
            if (segment != null) {
                // 从路段名称或路段编码中提取路线信息
                if (segment.getSegcode() != null && !segment.getSegcode().trim().isEmpty()) {
                    return segment.getSegcode();
                } else if (segment.getSegname() != null) {
                    // 从路段名称中提取路线代码
                    String segmentName = segment.getSegname();
                    if (segmentName.matches(".*[GS]\\d+.*")) {
                        Matcher matcher = Pattern.compile("([GS]\\d+)").matcher(segmentName);
                        if (matcher.find()) {
                            return matcher.group(1);
                        }
                    }
                    // 使用路段名称的哈希作为路线代码
                    return "SEG_" + segmentName.hashCode();
                }
                // 如果无法提取，使用路段ID作为路线代码
                return "SEG_" + segmentId;
            }
        }

        // 如果无法从路段表获取信息，则使用原有的提取逻辑
        if (defectInfo.getSegmentName() != null) {
            String segmentName = defectInfo.getSegmentName();
            // 尝试提取路线名称和编号（根据实际命名规则调整）
            if (segmentName.matches(".*[GS]\\d+.*")) {                   // 国道/省道编号识别
                // 例如从"G15沈海高速温岭段"提取"G15"作为路线编码
                Matcher matcher = Pattern.compile("([GS]\\d+)").matcher(segmentName);
                if (matcher.find()) {
                    String lineCode = matcher.group(1);                  // 例如"G15"
                    return lineCode;
                }
            } else {
                // 没有明确编码，使用路段名称的前几个字符作为区分
                return "SEG_" + segmentName.hashCode();
            }
        }

        // 最后使用病害ID作为分组标识
        return "UNKNOWN_" + defectInfo.getId();
    }

    @SneakyThrows
    @Override
    public NmDinsp createDinspForDefects(HighwayDefectInfo defectInfo, Map<String, BaseLine> lineMap, List<HighwayOrgRef> orgRefs) {
        // 使用DefectToDinspConverter的基本转换功能，但创建不带记录项的日常巡检单
        NmDinsp nmDinsp = DefectToDinspConverter.convertToNmDinsp(defectInfo, segmentCache);

        // 从路段名称设置路线信息
        setLineInfoFromSegment(nmDinsp, defectInfo);

        // 设置日常巡检类型
        nmDinsp.setInspFrequency(1);  // 日常巡检

        // 设置路线名称（如果lineMap中有对应信息）
        if (nmDinsp.getLineCode() != null && lineMap.containsKey(nmDinsp.getLineCode())) {
            nmDinsp.setLineName(lineMap.get(nmDinsp.getLineCode()).getLineSname());
        }

        // 设置管养单位信息
        setMaintenanceOrg(nmDinsp, defectInfo, orgRefs);
        StringUtil.checkCode(nmDinsp.getDinspCode(),4);
        if (nmDinsp.getProcessinstid() == null || nmDinsp.getProcessinstid() == 0) {
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "日常巡查", "");
            log.info("日常巡查实例ID："+processInstId);
            nmDinsp.setProcessinstid(processInstId);
            nmDinsp.setStatus(0);
        }
        nmDinsp.setDinspId(H_KeyWorker.nextIdToString());
        nmDinspService.saveOrUpdate(nmDinsp);
        log.debug("根据病害[{}]创建了日常巡检单", defectInfo.getId());
        return nmDinsp;
    }

    @Override
    public void setLineInfoFromSegment(NmDinsp nmDinsp, HighwayDefectInfo defectInfo) {
        // 优先从路段缓存中获取路段信息
        if (defectInfo.getSegmentId() != null && !defectInfo.getSegmentId().trim().isEmpty()) {
            HighwaySegment segment = segmentCache.get(defectInfo.getSegmentId());
            if (segment != null) {
                // 如果有路段编码，直接设置为路线编码
                if (segment.getSegcode() != null && !segment.getSegcode().trim().isEmpty()) {
                    nmDinsp.setLineCode(segment.getSegcode());
                    log.debug("从路段缓存中获取路线编码：{}", segment.getSegcode());
                }

                // 设置路线名称
                if (segment.getSegname() != null && !segment.getSegname().trim().isEmpty()) {
                    nmDinsp.setLineName(segment.getSegname());
                    log.debug("从路段缓存中获取路线名称：{}", segment.getSegname());
                    return;
                }
            }
        }

        // 如果缓存中没有相关信息，则从路段名称中提取
        String segmentName = defectInfo.getSegmentName();
        if (segmentName != null && !segmentName.trim().isEmpty()) {
            // 尝试提取标准道路编号（如G15、S30等）
            Pattern lineCodePattern = Pattern.compile("([GS]\\d+)");
            Matcher matcher = lineCodePattern.matcher(segmentName);
            if (matcher.find()) {
                String lineCode = matcher.group(1);
                // 如果没有通过缓存设置路线编码，才通过解析设置
                if (nmDinsp.getLineCode() == null) {
                    nmDinsp.setLineCode(lineCode);
                    log.debug("从路段名称中解析路线编码：{}", lineCode);
                }

                // 如果没有通过缓存设置路线名称，才通过解析设置
                if (nmDinsp.getLineName() == null) {
                    // 尝试提取完整道路名称（如G15沈海高速）
                    int endPos = segmentName.indexOf("段");
                    if (endPos > 0) {
                        String fullLineName = segmentName.substring(0, endPos);
                        nmDinsp.setLineName(fullLineName);
                        log.debug("从路段名称中解析路线名称：{}", fullLineName);
                    } else {
                        nmDinsp.setLineName(segmentName);
                        log.debug("使用路段名称作为路线名称：{}", segmentName);
                    }
                }
            } else if (nmDinsp.getLineName() == null) {
                // 没有标准编号，使用全名作为路线名称
                nmDinsp.setLineName(segmentName);
                log.debug("使用路段名称作为路线名称：{}", segmentName);
            }
        }
    }

    private void setMaintenanceOrg(NmDinsp nmDinsp, HighwayDefectInfo defectInfo, List<HighwayOrgRef> orgRefs) {
        if (CollectionUtils.isEmpty(orgRefs)) {
            log.warn("未获取到管养单位信息");
            return;
        }

        String segmentId = defectInfo.getSegmentId();
        if (segmentId == null || segmentId.trim().isEmpty()) {
            log.warn("病害信息中路段ID为空，无法设置管养单位信息");
            return;
        }

        // 根据路段ID查找对应的组织机构
        for (HighwayOrgRef orgRef : orgRefs) {
            if (segmentId.equals(orgRef.getRouteId())) {
                // 找到匹配的管养单位，设置相关信息
                nmDinsp.setMntOrgId(orgRef.getYhOrgCode());
                nmDinsp.setMntOrgNm(orgRef.getYhOrgName());
                nmDinsp.setRouteName(orgRef.getYhRouteName());

                // 处理yh_route_code格式为"1|234,2|235"的数据
                String yhRouteCode = orgRef.getYhRouteCode();
                if (yhRouteCode != null && !yhRouteCode.trim().isEmpty()) {
                    // 如果包含逗号，只取第一部分
                    int commaIndex = yhRouteCode.indexOf(",");
                    if (commaIndex > 0) {
                        yhRouteCode = yhRouteCode.substring(0, commaIndex);
                    }

                    // 获取|后面的部分作为实际的路线编码
                    int pipeIndex = yhRouteCode.indexOf("|");
                    if (pipeIndex > 0) {
                        yhRouteCode = yhRouteCode.substring(pipeIndex + 1);
                    }
                }

                nmDinsp.setRouteCode(yhRouteCode);

                // 根据路段ID和巡查类型(2-经常检查)查询用户配置
                LambdaQueryWrapper<HighwayUserConfig> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(HighwayUserConfig::getRouteId, segmentId)
                        .eq(HighwayUserConfig::getInspType, 1)
                        .last("AND ROWNUM <= 1"); // Oracle中获取第一条记录

                HighwayUserConfig userConfig = highwayUserConfigService.getOne(queryWrapper);
                if (userConfig != null) {
                    FwRightUserService bean = CustomApplicationContextHolder.getBean(FwRightUserService.class);
                    UserLoginDto user = bean.loginUser(userConfig.getUserCode());
                    nmDinsp.setDinspCode(getNextCode("LM",user.getOrgEn(), LocalDate.now()));
                    // 设置创建用户
                    nmDinsp.setCreateUserId(userConfig.getUserId());
                    nmDinsp.setUpdateUserId(userConfig.getUserId());
                    // 设置检查人
                    nmDinsp.setInspPerson(userConfig.getUserName());
                    // 设置部门名称
                    nmDinsp.setSearchDept(userConfig.getDepartName());
                    log.debug("为日常巡检记录设置用户信息：创建用户ID={}, 检查人={}, 部门={}",
                            userConfig.getUserId(), userConfig.getUserName(), userConfig.getDepartName());
                } else {
                    log.warn("未找到与路段ID[{}]和巡查类型[{}]匹配的用户配置信息", segmentId, 2);
                    throw new RuntimeException("<UNK>ID[" + segmentId + "]<UNK>");
                }
                log.debug("为日常巡检记录设置管养单位信息：机构ID={}, 机构名称={}, 路线名称={}, 路线编码={}",
                        nmDinsp.getMntOrgId(),
                        nmDinsp.getMntOrgNm(), nmDinsp.getRouteName(), nmDinsp.getRouteCode());
                return;
            }
        }

        // 未找到匹配的管养单位
        log.warn("未找到与路段ID[{}]匹配的管养单位信息", segmentId);
    }

    @Override
    public NmDinspRecord convertToDinspRecord(HighwayDefectInfo defectInfo, String dinspId) {
        NmDinspRecord record = new NmDinspRecord();

        // 设置基本信息
        record.setDssId(H_KeyWorker.nextIdToString());
        record.setHisDssId(defectInfo.getId());
        record.setDinspId(dinspId);
        String direction = defectInfo.getInspectDirection();
        record.setLineDirect("1".equals(direction) ? "2" : "0".equals(direction) ? "1" : direction);
        record.setStake(stakeToKilometer(defectInfo.getStakeStart()));
        record.setDssType(defectInfo.getYhDssType());
        record.setDssTypeName(defectInfo.getDssTypeName());
        record.setFacilityCat("LM");
        record.setLane(convertLaneNumberToLetter(defectInfo.getLaneNum(), direction));
        record.setDssPosition(defectInfo.getStakeStart()+"~"+defectInfo.getStakeEnd()+","+convertLaneNumberToLetter(defectInfo.getLaneNum(), direction));
        record.setDssL(defectInfo.getTargetLen().doubleValue());
        record.setDssLUnit("m");
        record.setDssA(defectInfo.getTargetArea().doubleValue());
        record.setDssAUnit("m2");
        record.setX(defectInfo.getLongitude().doubleValue());
        record.setY(defectInfo.getLatitude().doubleValue());
        record.setSource("1");
        record.setDelFlag(0);
        record.setIssueType("2");
        record.setIsphone(2);
        String pavementType = "LQ";
        if("水泥路面".equals(defectInfo.getTypeName())){
            pavementType = "SN";
        }
        record.setPavementType(pavementType);

        // 设置时间信息
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());

        log.debug("将病害[{}]转换为日常巡检记录项[{}]", defectInfo.getId(), record.getDssId());
        return record;
    }

    private Double stakeToKilometer(String stake) {
        if (stake == null || stake.trim().isEmpty()) {
            return null;
        }

        // 如果已经是数字，检查是否为公里单位，不是则转换
        if (NumberUtil.isNumber(stake)) {
            Double value = Double.valueOf(stake);
            // 如果大于1000，可能是以米为单位，需要除以1000转成公里
            if (value > 1000) {
                return value / 1000;
            }
            return value; // 已经是公里单位
        }

        // 处理K100+500这种标准桩号格式
        if (stake.toUpperCase().startsWith("K")) {
            try {
                // 去掉K前缀
                String temp = stake.trim().toUpperCase().replaceAll("K", "");
                // 按+分割
                String[] parts = temp.split("\\+");

                // 提取公里部分
                Double kilometers = Double.valueOf(parts[0]);

                // 如果有米部分，转换为公里并加上
                if (parts.length > 1) {
                    Double meters = Double.valueOf(parts[1]);
                    kilometers += meters / 1000;
                }

                return kilometers;
            } catch (Exception e) {
                return null; // 转换出错返回null
            }
        }

        return null; // 不符合格式返回null
    }

    private String convertLaneNumberToLetter(String laneNumber, String direction) {
        if (laneNumber == null || laneNumber.isEmpty()) {
            return "";
        }

        boolean isUpDirection = "1".equals(direction); // 1=上行，2=下行
        StringBuilder result = new StringBuilder();

        // 判断是否包含逗号
        String[] lanes;
        if (laneNumber.contains(",")) {
            lanes = laneNumber.split(",");
        } else {
            // 没有逗号，按单个字符拆分
            lanes = new String[laneNumber.length()];
            for (int i = 0; i < laneNumber.length(); i++) {
                lanes[i] = String.valueOf(laneNumber.charAt(i));
            }
        }

        // 转换每个车道号
        for (int i = 0; i < lanes.length; i++) {
            if (i > 0) {
                result.append(",");
            }

            String lane = lanes[i].trim();
            try {
                int laneNum = Integer.parseInt(lane);
                if (isUpDirection) { // 上行
                    switch (laneNum) {
                        case 1: result.append("C"); break;
                        case 2: result.append("A"); break;
                        case 3: result.append("E"); break;
                        case 4: result.append("G"); break;
                        default: result.append(lane); break;
                    }
                } else { // 下行
                    switch (laneNum) {
                        case 1: result.append("D"); break;
                        case 2: result.append("B"); break;
                        case 3: result.append("F"); break;
                        case 4: result.append("H"); break;
                        default: result.append(lane); break;
                    }
                }
            } catch (NumberFormatException e) {
                // 如果不是数字，保持原样
                result.append(lane);
            }
        }

        return result.toString();
    }

    private static final String DINSP_CODE_TEMPLATE = "XCD-{}-{}-{}";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private String getNextCode(String facilityCat, String orgEn,LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return String.format("%s-%04d", generateBaseCode(facilityCat, orgEn,localDate), getNextSerial(facilityCat, orgEn,localDate)+1);
    }

    public Integer getNextSerial(String facilityCat, String orgEn,LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return queryMaxSerialFromDB(facilityCat, orgEn,localDate);
    }

    // 提取公共方法：参数校验
    private void validateParams(String facilityCat, String orgEn,LocalDate localDate) {
        Objects.requireNonNull(facilityCat, "设施类型不能为空[2,9](@ref)");
        Objects.requireNonNull(orgEn, "机构编码不能为空[2,9](@ref)");
    }

    // 提取公共方法：生成基础编码
    private String generateBaseCode(String facilityCat, String orgEn,LocalDate localDate) {
        return StrUtil.format(DINSP_CODE_TEMPLATE,
                facilityCat,
                orgEn.toUpperCase(),
                localDate.format(DATE_FORMATTER)
        );
    }

    private Integer queryMaxSerialFromDB(String facilityCat, String orgEn,LocalDate localDate) {
        QueryWrapper<NmDinsp> wrapper = new QueryWrapper<>();
        wrapper.select("COALESCE(MAX(TO_NUMBER(SUBSTR(DINSP_CODE, -4))), 0) AS max_serial")
                .likeRight("DINSP_CODE", generateBaseCode(facilityCat, orgEn,localDate))
                .eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString());

        return Optional.ofNullable(nmDinspService.getObj(wrapper, obj -> Integer.parseInt(obj.toString())))
                .orElse(0);
    }

    /**
     * 将本地文件路径列表转换为MultipartFile数组
     * @param localPaths 本地文件路径列表
     * @return MultipartFile数组
     */
    private MultipartFile[] convertLocalPathsToMultipartFiles(List<String> localPaths) {
        if (localPaths == null || localPaths.isEmpty()) {
            return new MultipartFile[0];
        }

        List<MultipartFile> files = new ArrayList<>();

        for (String localPath : localPaths) {
            try {
                File file = new File(localPath);
                if (file.exists() && file.isFile()) {
                    // 创建MultipartFile对象
                    MultipartFile multipartFile = new MockMultipartFile(
                            file.getName(),
                            file.getName(),
                            Files.probeContentType(file.toPath()),
                            Files.readAllBytes(file.toPath())
                    );
                    files.add(multipartFile);
                } else {
                    log.warn("文件不存在或不是文件: {}", localPath);
                }
            } catch (Exception e) {
                log.error("转换文件失败: {}", localPath, e);
            }
        }

        return files.toArray(new MultipartFile[0]);
    }
} 