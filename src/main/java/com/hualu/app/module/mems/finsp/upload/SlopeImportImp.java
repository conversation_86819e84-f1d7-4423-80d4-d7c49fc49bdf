package com.hualu.app.module.mems.finsp.upload;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.word.WordUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.google.common.collect.Maps;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspRecordExcelDto;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspRecordWordDto;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.service.PcFinspRecordService;
import com.hualu.app.module.mems.finsp.service.PcFinspResultService;
import com.hualu.app.module.mems.finsp.service.PcFinspService;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SlopeImportImp implements IImportStruct{

    private static List<String> excelHeaderTemplates = Lists.newArrayList("路线编码","路线方向","边坡桩号/名称","边坡位置","边坡级数","路段类型","是否重点边坡");

    @Lazy
    @Autowired
    PcProjectScopeService scopeService;

    @Lazy
    @Autowired
    PcFinspService pcFinspService;

    @Autowired
    PcFinspResultService resultService;

    @Autowired
    PcFinspRecordService recordService;

    @Value("${fileReposity}")
    String fileReposity;

    @Override
    public String getFacilityCat() {
        return "BP";
    }

    @Override
    public List<PcProjectScope> doImport(PcProject pcProject, File uploadFile) {
        ExcelReader reader = ExcelUtil.getReader(uploadFile);
        //获取excel表头
        List<Object> excelHeaders = reader.read(0).get(0);
        excelHeaderTemplates.removeAll(excelHeaders);
        if (excelHeaderTemplates.size()>0){
            throw new BaseException("excel模版不匹配,未包含列："+ StrUtil.join("2",excelHeaderTemplates));
        }
        reader.addHeaderAlias("路线编码","lineCode");
        reader.addHeaderAlias("路线方向","lineDirect");
        reader.addHeaderAlias("边坡桩号/名称","structName");
        reader.addHeaderAlias("边坡位置","structPosition");
        reader.addHeaderAlias("边坡级数","structLevel");
        reader.addHeaderAlias("路段类型","structType");
        reader.addHeaderAlias("是否重点边坡","important");

        List<PcProjectScope> pcProjectScopes = reader.readAll(PcProjectScope.class);
        return pcProjectScopes;
    }

    @Override
    public List<BaseNearStructDto> toBaseNearStructDto(List<PcProjectScope> pcProjectScopes) {
        List<BaseNearStructDto> structDtos = Lists.newArrayList();
        for (PcProjectScope item : pcProjectScopes) {
            BaseNearStructDto dto = new BaseNearStructDto();
            dto.setStructName(item.getStructName());
            dto.setSlopeLevel(Convert.toLong(item.getStructLevel()));
            dto.setSlopeType(item.getStructType());
            dto.setSlopeTypeName(item.getStructType());
            dto.setSlopePosition(item.getStructPosition());
            dto.setSlopePositionName(item.getStructPosition());
            dto.setX(item.getX());
            dto.setY(item.getY());
            dto.setStructId(item.getStructId());
            dto.setFacilityCat(item.getFacilityCat());
            dto.setLineDirect(item.getLineDirect());
            dto.setOprtOrgCode(item.getMntOrgId());
            structDtos.add(dto);
        }
        return structDtos;
    }

    @SneakyThrows
    @Override
    public String exportWord(PcFinsp pcFinsp,String qlType) {
        List<PcFinspWordDto> wordDtos = pcFinspService.listFinspWord(Lists.newArrayList(pcFinsp.getFinspId()), null);
        if (CollectionUtil.isEmpty(wordDtos)){
            throw new BaseException("该检查单不存在");
        }

        List<PcFinspRecordWordDto> recordWordDtos = resultService.listRecordWord(Lists.newArrayList(pcFinsp.getFinspId()), null);
        LinkedHashMap<String, List<PcFinspRecordWordDto>> finspRecordMap = recordWordDtos.stream().collect(Collectors.groupingBy(e -> e.getFinspId(), LinkedHashMap::new, Collectors.toList()));

        PcFinspWordDto wordDto = wordDtos.get(0);
        Map xxMap = BeanUtil.toBean(wordDto, Map.class);
        xxMap.put("recList", finspRecordMap.get(wordDto.getFinspId()));
        String fileName = File.separator + wordDto.getFinspCode() + ".docx";
        //ClassPathResource classPathResource = new ClassPathResource("excel/pcFinsp/pcFinsp.docx");
        InputStream resourceAsStream = WordUtil.class.getClassLoader().getResourceAsStream("excel/pcFinsp/pcFinsp.docx");
        //渲染表格
        Configure config = Configure.builder().bind("recList", new LoopRowTableRenderPolicy()).build();
        XWPFTemplate template = XWPFTemplate.compile(resourceAsStream, config).render(xxMap);
        try (OutputStream outputStream = Files.newOutputStream(new File(fileReposity + fileName).toPath())) {
            template.write(outputStream);
            template.close();
        }
        resourceAsStream.close();
        return fileName;
    }

    @SneakyThrows
    @Override
    public void exportWordByZip(String filePath, List<String> finspIds, String prjId) {
        List<PcFinspWordDto> wordDtos = pcFinspService.listFinspWord(finspIds, prjId);
        List<PcFinspRecordWordDto> recordWordDtos = resultService.listRecordWord(finspIds, prjId);
        LinkedHashMap<String, List<PcFinspRecordWordDto>> finspRecordMap = recordWordDtos.stream().collect(Collectors.groupingBy(e -> e.getFinspId(), LinkedHashMap::new, Collectors.toList()));
        for (PcFinspWordDto wordDto : wordDtos) {
            Map xxMap = BeanUtil.toBean(wordDto, Map.class);
            xxMap.put("recList",finspRecordMap.get(wordDto.getFinspId()));
            String fileName = filePath+File.separator + wordDto.getFinspCode() + ".docx";
            InputStream resourceAsStream = WordUtil.class.getClassLoader().getResourceAsStream("excel/pcFinsp/pcFinsp.docx");
            //渲染表格
            Configure config = Configure.builder().bind("recList", new LoopRowTableRenderPolicy()).build();
            XWPFTemplate template = XWPFTemplate.compile(resourceAsStream, config).render(xxMap);
            try(OutputStream outputStream = Files.newOutputStream(new File(fileName).toPath())){
                template.write(outputStream);
                template.close();
            }
            resourceAsStream.close();
        }
        List<PcFinspRecordExcelDto> excelDtos = recordService.listRecordExcelDtoByPrjId(Lists.newArrayList(prjId));
        // 导出excel表
        String excelTemplatPath = "excel/pcFinsp/slopeDssInfo.xlsx";
        TemplateExportParams exportParams = new TemplateExportParams(excelTemplatPath,true,null);
        Map<String, Object> xxMap = Maps.newHashMap();
        xxMap.put("recList",excelDtos);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,xxMap);
        String fileName = filePath+File.separator + IdUtil.fastSimpleUUID() + ".xlsx";
        try(OutputStream outputStream = Files.newOutputStream(new File(fileName).toPath())){
            workbook.write(outputStream);
            workbook.close();
        }

        //导出照片，按照检查单位（定检、日养）路线编码+路线方向+边坡名称+位置（左侧，右侧）、部件、病害类型
    }
}
