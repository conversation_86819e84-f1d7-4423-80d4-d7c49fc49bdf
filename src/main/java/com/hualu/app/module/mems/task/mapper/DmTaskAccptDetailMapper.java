package com.hualu.app.module.mems.task.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 验收单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskAccptDetailMapper extends BaseMapper<DmTaskAccptDetail> {

    List<DmTaskAccptDetailDto> selectPageRecord(Page page, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<DmTaskDssViewDto> selectDssNumAndMoney(String mtaskAccptId);

    List<DmTaskMpItemViewDto> selectMpItemsByDssIds(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<DmTaskDssViewDto> selectWaitAccptDssPage(Page page,@Param("orgId") String orgId);

    List<DmTaskAccptDetailDto> selectPageByDss(Page page,@Param(Constants.WRAPPER)  QueryWrapper queryWrapper);

    DmTaskAccptDetail selectOneByDssId(@Param("dssId") String dssId);
}
