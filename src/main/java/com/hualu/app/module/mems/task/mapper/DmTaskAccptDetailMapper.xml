<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.task.mapper.DmTaskAccptDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.task.entity.DmTaskAccptDetail">
        <id column="MTASK_ACCPT_DTL_ID" property="mtaskAccptDtlId" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="MTASK_ACCPT_ID" property="mtaskAccptId" />
        <result column="MTASK_DTL_ID" property="mtaskDtlId" />
        <result column="DSS_ID" property="dssId" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="STAKE" property="stake" />
        <result column="LANE" property="lane" />
        <result column="MPITEM_ID" property="mpitemId" />
        <result column="MEASURE_UNIT" property="measureUnit" />
        <result column="IS_LUMP" property="isLump" />
        <result column="REMARK" property="remark" />
        <result column="APPLY_AMOUNT" property="applyAmount" />
        <result column="ACCEPT_AMOUNT" property="acceptAmount" />
        <result column="REPAIR_DATE" property="repairDate" />
        <result column="ACCEPT_STATUS" property="acceptStatus" />
        <result column="MPITEM_NUM_BAK" property="mpitemNumBak" />
        <result column="MAIN_ROAD_ID" property="mainRoadId" />
        <result column="IS_TASK_RECORD" property="isTaskRecord" />
        <result column="RAMP_ID" property="rampId" />
        <result column="CONTR_ID" property="contrId" />
        <result column="MTASK_ACCPT_DTL_CODE" property="mtaskAccptDtlCode" />
        <result column="MTASK_ID" property="mtaskId" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="LOGIC_STAKE_NUM" property="logicStakeNum" />
        <result column="RL_STAKE" property="rlStake" />
        <result column="MPITEM_NUM" property="mpitemNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MTASK_ACCPT_DTL_ID, BUSINESS_TYPE, MTASK_ACCPT_ID, MTASK_DTL_ID, DSS_ID, RP_INTRVL_ID, STAKE, LANE, MPITEM_ID, MEASURE_UNIT, IS_LUMP, REMARK, APPLY_AMOUNT, ACCEPT_AMOUNT, REPAIR_DATE, ACCEPT_STATUS, MPITEM_NUM_BAK, MAIN_ROAD_ID, IS_TASK_RECORD, RAMP_ID, CONTR_ID, MTASK_ACCPT_DTL_CODE, MTASK_ID, LINE_DIRECT, LOGIC_STAKE_NUM, RL_STAKE, MPITEM_NUM
    </sql>
    <select id="selectPageRecord" resultType="com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto">
        select * from (
        select distinct d.*,
        dt.SEND_TIME,
        DA.MTASK_ACCPT_CODE,
        DA.PROCESSINSTID,
        (case when (dt.ASKED_COMPL_DATE &lt; d.repair_date) then 1 else 0 end) isover,
        gl.line_code,
        gl.LINE_ALLNAME,
        gl.LINE_ALLNAME || '(' || gl.LINE_CODE || ')' as line_Name,
        dt.mtask_code,
        mmp.mmp_code,
        mp.mpitem_name,
        td.mpitem_account,
        '(' || c.contr_code || ')' || c.contr_name as contr_nm,
        di.facility_cat,
        di.struct_id,
        di.struct_part_id,
        di.rl_stake_new,
        di.dss_desc
        from DM_TASK_ACCPT_DETAIL d
        left join dm_task_detail td on td.mtask_dtl_id = d.mtask_dtl_id
        left join DM_TASK_ACCPT DA on DA.MTASK_ACCPT_ID = d.MTASK_ACCPT_ID
        left join dm_task dt on dt.mtask_id = d.mtask_id
        inner join mpc_contract c on c.contr_id = d.contr_id
        inner join MPC_MPITEM mp on d.mpitem_id = mp.mpitem_id
        inner join MPC_MPITEM_PRICE mmp on mmp.MP_ITEM_ID = d.MPITEM_ID and d.contr_id = mmp.contr_id
        inner join GDGS.V_BASE_LINE gl on d.main_road_id = gl.LINE_ID
        inner join dss_info di
        on di.dss_id = d.dss_id ) v ${ew.customSqlSegment}
    </select>
    <select id="selectDssNumAndMoney"
            resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto">
        select dss_id, CONTR_ID, count(1) as mp_num, sum(MONEY) as money
        from (select distinct dtad.dss_id,
                     dtad.contr_id,
                     to_number(to_char(MMP.UNIT_PRICE * dtad.ACCEPT_AMOUNT, '*********.00')) AS money
              from DM_TASK_ACCPT_DETAIL dtad
                       join DM_TASK_ACCPT dta on dtad.MTASK_ACCPT_ID = dta.MTASK_ACCPT_ID
                       join MPC_MPITEM_PRICE mmp on dtad.MPITEM_ID = mmp.MP_ITEM_ID and dtad.CONTR_ID = mmp.CONTR_ID
              where dtad.MTASK_ACCPT_ID = #{mtaskAccptId})
        group by DSS_ID, CONTR_ID
    </select>
    <select id="selectMpItemsByDssIds"
            resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto">
        select distinct dtd.mtask_dtl_id,mmp.mpitem_code,dtd.dss_id,dtd.mtask_accpt_dtl_id,
                        dtd.is_task_record,
               mmp.mpitem_name,
               mmp.unit_price,
               dtd.apply_amount,
               dtd.accept_amount,
               mmp.measure_unit,
               dd.mpitem_account,
               dtd.mpitem_num,
               to_number(to_char(mmp.unit_price * dtd.mpitem_num, '*********.00')) as money,
               to_number(to_char(mmp.unit_price * dtd.accept_amount, '*********.00')) as accept_money
        from DM_TASK_ACCPT_DETAIL dtd
                 join MPC_MPITEM_PRICE mmp on dtd.MPITEM_ID = mmp.MP_ITEM_ID
                 left join DM_TASK_DETAIL dd on dd.MTASK_DTL_ID = dtd.MTASK_DTL_ID
            ${ew.customSqlSegment}
        order by dtd.dss_id,TO_NUMBER(translate (mmp.MPITEM_CODE, '#' || translate (mmp.MPITEM_CODE, '**********', '#'), '/'))
    </select>
    <select id="selectWaitAccptDssPage"
            resultType="com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto">
        SELECT ad.DSS_ID, x.MTASK_ACCPT_CODE,x.MTASK_ACCPT_ID,ad.CONTR_ID
        FROM memsdb.DM_TASK_ACCPT x
                 join MEMSDB.DM_TASK_ACCPT_DETAIL ad on x.MTASK_ACCPT_ID = ad.MTASK_ACCPT_ID
        WHERE x.mnt_org_id = #{orgId}
          and x.STATUS = 1
          and EXISTS (select 1 from bps.WFPROCESSINST where PROCESSINSTID = x.PROCESSINSTID and CURRENTSTATE = 2)
        group by ad.dss_id, x.MTASK_ACCPT_CODE,x.MTASK_ACCPT_ID,ad.CONTR_ID
        order by x.MTASK_ACCPT_CODE
    </select>
    <select id="selectPageByDss" resultType="com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto">
        select *
        from (select distinct d.DSS_ID,
                              d.business_type,
                              d.ACCEPT_STATUS,
                              d.MAIN_ROAD_ID,
                              d.LINE_DIRECT,
                              d.contr_id,
                              d.REPAIR_DATE,
                              d.rl_stake,
                              dt.SEND_TIME,
                              dt.MTASK_CODE,
                              DA.MTASK_ACCPT_CODE,
                              DA.PROCESSINSTID,
                              gl.line_code,
                              gl.LINE_ALLNAME,
                              gl.LINE_ALLNAME || '(' || gl.LINE_CODE || ')' as                    line_Name,
                              '(' || c.contr_code || ')' || c.contr_name    as                    contr_nm,
                              (case when (dt.ASKED_COMPL_DATE &lt; d.repair_date) then 1 else 0 end) isover,
                              dt.mtask_id,
                              dsstype.DSS_TYPE_NAME,
                              dsstype.DSS_TYPE,
                              di.facility_cat,
                              di.struct_id,
                              di.struct_part_id,
                              di.rl_stake_new,
                              di.dss_desc,
                              di.finish_stake,
                              di.lane,
                              di.DSS_L,
                              di.DSS_L_UNIT,
                              di.DSS_W,
                              di.DSS_W_UNIT,
                              di.DSS_D,
                              di.DSS_D_UNIT,
                              di.DSS_N,
                              di.DSS_N_UNIT,
                              di.DSS_A,
                              di.DSS_A_UNIT,
                              di.DSS_V,
                              di.DSS_V_UNIT,
                              di.DSS_P,
                              di.DSS_G,
                              dt.have_dss_colom,di.DSS_DESC as remark
              from DM_TASK_ACCPT_DETAIL d
                       left join dm_task_detail td on td.mtask_dtl_id = d.mtask_dtl_id
                       left join DM_TASK_ACCPT DA on DA.MTASK_ACCPT_ID = d.MTASK_ACCPT_ID
                       left join dm_task dt on dt.mtask_id = d.mtask_id
                       inner join mpc_contract c on c.contr_id = d.contr_id
                       inner join GDGS.V_BASE_LINE gl on d.main_road_id = gl.LINE_ID
                       inner join dss_info di on di.dss_id = d.dss_id
                       inner join DSS_TYPE_NEW dsstype on di.DSS_TYPE = dsstype.DSS_TYPE
                       inner join dss_type_new dt on dt.dss_type = dsstype.dss_type) v ${ew.customSqlSegment}
    </select>
    <select id="selectOneByDssId" resultType="com.hualu.app.module.mems.task.entity.DmTaskAccptDetail">
        select * from DM_TASK_ACCPT_DETAIL where dss_id = #{dssId} and rownum=1
    </select>
</mapper>
