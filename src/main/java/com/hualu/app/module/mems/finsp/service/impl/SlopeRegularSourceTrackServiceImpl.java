package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.entity.SlopeRegularSourceTrack;
import com.hualu.app.module.mems.finsp.mapper.SlopeRegularSourceTrackMapper;
import com.hualu.app.module.mems.finsp.service.SlopeRegularSourceTrackService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SlopeRegularSourceTrackServiceImpl extends ServiceImpl<SlopeRegularSourceTrackMapper, SlopeRegularSourceTrack> implements SlopeRegularSourceTrackService{

    @Override
    public Set<String> hasSourceTack(List<String> slopeIds) {
        if (CollectionUtil.isEmpty(slopeIds)){
            return Collections.emptySet();
        }
        LambdaQueryWrapper<SlopeRegularSourceTrack> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SlopeRegularSourceTrack::getSlopeId, slopeIds);
        wrapper.select(SlopeRegularSourceTrack::getSlopeId).groupBy(SlopeRegularSourceTrack::getSlopeId);
        Set<String> res = list(wrapper).stream().map(SlopeRegularSourceTrack::getSlopeId).collect(Collectors.toSet());
        return res;
    }
}
