package com.hualu.app.module.mems.autoInspector.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.autoInspector.dto.DefectTypeMappingDTO;
import com.hualu.app.module.mems.autoInspector.entity.*;
import com.hualu.app.module.mems.autoInspector.service.*;
import com.hualu.app.module.mems.autoInspector.utils.HttpForAutoUtils;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.util.hp.H_KeyWorker;
import io.minio.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.stream.Collectors;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.nio.file.Files;

@Api(tags = "同步数据控制器")
@RestController
@RequestMapping("/data/sync")
@Slf4j
public class DataSyncController {

    @Value("${autoInspect.enable}")
    private boolean enable;

    @Value("${autoInspect.domain}")
    private String domain;

    @Value("${autoInspect.version}")
    private String version;

    @Value("${autoInspect.sik}")
    private String sik;

    @Value("${autoInspect.sis}")
    private String sis;

    private String url;

    private String now;

    @Autowired
    private HighwayDefectInfoService highwayDefectInfoService;

    @Autowired
    private HighwayDefectMediaService highwayDefectMediaService;

    @Autowired
    private HighwayEventInfoService highwayEventInfoService;

    @Autowired
    private HighwayEventMediaService highwayEventMediaService;

    @Autowired
    private HighwayVehicleDeviceService highwayVehicleDeviceService;

    @Autowired
    private HighwayCategoryDictionaryService categoryDictionaryService;

    @Autowired
    private NmInspContentService nmInspContentService;

    @Autowired
    private HighwaySegmentService highwaySegmentService;

    @Autowired
    private HighwayTaskService highwayTaskService;

    @Autowired
    private HighwayOrgRefService orgRefService;

    @PostConstruct
    public void init() {
        this.url = "https://" + domain + "/openapi/rest/" + version + "/{endpoint}";
        this.now = HttpForAutoUtils.getCurrentUtcTime();
    }

    @GetMapping("/syncDevice")
    public void syncDevice() {
        String url = this.url.replace("{endpoint}", "aiot");
        int page = 1;
        int size = 1000; // 每页获取1000条数据
        int totalCount = 0;
        int processedCount = 0;

        try {
            // 先获取第一页数据，同时获取总数
            String timestamp = HttpForAutoUtils.getTimestamp();
            Map<String, Object> params = new TreeMap<>();
            params.put("page", page);
            params.put("size", size);
            String sign = getSign("/aiot", params, timestamp);

            JSON json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
            if (json != null) {
                JSONObject jsonObject = JSONObject.parseObject(json.toJSONString());
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    totalCount = data.getInteger("count");
                    log.info("开始同步设备数据，总数：{}", totalCount);

                    // 处理第一页数据
                    int count = highwayVehicleDeviceService.syncAndSaveDeviceData(json);
                    processedCount += count;
                    log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);

                    // 计算总页数
                    int totalPages = (totalCount + size - 1) / size;

                    // 循环获取剩余页数据
                    for (page = 2; page <= totalPages; page++) {
                        timestamp = HttpForAutoUtils.getTimestamp();
                        params = new TreeMap<>();
                        params.put("page", page);
                        params.put("size", size);
                        sign = getSign("/aiot", params, timestamp);

                        json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
                        if (json != null) {
                            count = highwayVehicleDeviceService.syncAndSaveDeviceData(json);
                            processedCount += count;
                            log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);
                        }

                        // 每处理10页数据后暂停1秒，避免请求过于频繁
                        if (page % 10 == 0) {
                            Thread.sleep(1000);
                        }
                    }

                    log.info("设备数据同步完成，总处理{}条数据", processedCount);
                } else {
                    log.error("同步设备数据失败：{}", jsonObject.getString("message"));
                }
            }
        } catch (Exception e) {
            log.error("同步设备数据异常", e);
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/syncDss")
    public RestResult<String> syncDss(String dateStr, String routeName) {
        String url = this.url.replace("{endpoint}", "inspect/defect");
        int page = 1;
        int size = 1000; // 每页获取1000条数据
        int totalCount = 0;
        int processedCount = 0;
        // 锁定查询时间，确保整个分页过程使用相同的时间
        final String lockedDateTime;
        if(!StringUtils.isEmpty(dateStr)){
            lockedDateTime = HttpForAutoUtils.convertToUtcDateTime(dateStr);
        } else {
            lockedDateTime = this.now; // 使用默认时间
        }
        log.info("请求参数 dateStr: {}, 锁定查询时间: {}", dateStr, lockedDateTime);
        try {
            // 先获取第一页数据，同时获取总数
            String timestamp = HttpForAutoUtils.getTimestamp();
            Map<String, Object> params = new TreeMap<>();
            params.put("page", page);
            params.put("size", size);
            params.put("datetime", lockedDateTime);
            List<String> defectTypeList = new ArrayList<>(Arrays.asList("0", "1", "2", "10", "20", "30", "40", "50",
                    "60","70","80","90","1000"));
            params.put("defecttype", defectTypeList);
            if(!StringUtils.isEmpty(routeName)){
                params.put("segmentname", routeName);
            }
            String sign = getSign("/inspect/defect", params, timestamp);

            JSON json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
            if (json != null) {
                removeOldData(dateStr,routeName);
                JSONObject jsonObject = JSONObject.parseObject(json.toJSONString());
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    totalCount = data.getInteger("count");
                    log.info("开始同步病害数据，总数：{}", totalCount);

                    // 处理第一页数据
                    int count = syncAndSaveDefectData(json.toJSONString());
                    processedCount += count;
                    log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);

                    // 计算总页数
                    int totalPages = (totalCount + size - 1) / size;

                    // 循环获取剩余页数据
                    for (page = 2; page <= totalPages; page++) {
                        timestamp = HttpForAutoUtils.getTimestamp();
                        params = new TreeMap<>();
                        params.put("page", page);
                        params.put("size", size);
                        params.put("datetime", lockedDateTime);
                        params.put("defecttype", defectTypeList);
                        if(!StringUtils.isEmpty(routeName)){
                            params.put("segmentname", routeName);
                        }
                        sign = getSign("/inspect/defect", params, timestamp);

                        json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
                        if (json != null) {
                            // 添加详细日志来诊断问题
                            log.info("第{}页请求参数: {}", page, params);
                            JSONObject responseObj = JSONObject.parseObject(json.toJSONString());
                            if (responseObj.getInteger("code") == 200) {
                                JSONObject responseData = responseObj.getJSONObject("data");
                                JSONArray items = responseData.getJSONArray("items");
                                log.info("第{}页API返回: code={}, items数量={}", page, responseObj.getInteger("code"),
                                        items != null ? items.size() : 0);
                            } else {
                                log.warn("第{}页API返回错误: code={}, message={}", page,
                                        responseObj.getInteger("code"), responseObj.getString("message"));
                            }

                            count = syncAndSaveDefectData(json.toJSONString());
                            processedCount += count;
                            log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);
                        } else {
                            log.warn("第{}页请求返回null", page);
                        }

                        // 每处理10页数据后暂停1秒，避免请求过于频繁
                        if (page % 10 == 0) {
                            Thread.sleep(1000);
                        }
                    }

                    log.info("病害数据同步完成，总处理{}条数据", processedCount);
                } else {
                    log.error("同步病害数据失败：{}", jsonObject.getString("message"));
                }
            }
        } catch (Exception e) {
            log.error("同步病害数据异常", e);
            throw new RuntimeException(e);
        }
        return RestResult.success("同步数据完成，共"+ totalCount + "条");
    }

    private void removeOldData(String dateStr, String routeName) {
        highwayDefectInfoService.removeOldData(dateStr,routeName);
    }

    @GetMapping("/syncEvent")
    public void syncEvent() {
        String url = this.url.replace("{endpoint}", "inspect/event");
        int page = 1;
        int size = 1000; // 每页获取1000条数据
        int totalCount = 0;
        int processedCount = 0;

        try {
            // 先获取第一页数据，同时获取总数
            String timestamp = HttpForAutoUtils.getTimestamp();
            Map<String, Object> params = new TreeMap<>();
            params.put("page", page);
            params.put("size", size);
            params.put("datetime", this.now);
            String sign = getSign("/inspect/event", params, timestamp);

            JSON json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
            if (json != null) {
                JSONObject jsonObject = JSONObject.parseObject(json.toJSONString());
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    totalCount = data.getInteger("count");
                    log.info("开始同步交安数据，总数：{}", totalCount);

                    // 处理第一页数据
                    int count = syncAndSaveEventData(json.toJSONString());
                    processedCount += count;
                    log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);

                    // 计算总页数
                    int totalPages = (totalCount + size - 1) / size;

                    // 循环获取剩余页数据
                    for (page = 2; page <= totalPages; page++) {
                        timestamp = HttpForAutoUtils.getTimestamp();
                        params = new TreeMap<>();
                        params.put("page", page);
                        params.put("size", size);
                        sign = getSign("/inspect/event", params, timestamp);

                        json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
                        if (json != null) {
                            count = syncAndSaveEventData(json.toJSONString());
                            processedCount += count;
                            log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);
                        }

                        // 每处理10页数据后暂停1秒，避免请求过于频繁
                        if (page % 10 == 0) {
                            Thread.sleep(1000);
                        }
                    }

                    log.info("交安数据同步完成，总处理{}条数据", processedCount);
                } else {
                    log.error("同步交安数据失败：{}", jsonObject.getString("message"));
                }
            }
        } catch (Exception e) {
            log.error("同步交安数据异常", e);
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/syncSegment")
    public void syncSegment() {
        String url = this.url.replace("{endpoint}", "segment");
        int page = 1;
        int size = 1000; // 每页获取1000条数据
        int totalCount = 0;
        int processedCount = 0;

        try {
            // 先获取第一页数据，同时获取总数
            String timestamp = HttpForAutoUtils.getTimestamp();
            Map<String, Object> params = new TreeMap<>();
            params.put("page", page);
            params.put("size", size);
            String sign = getSign("/segment", params, timestamp);

            JSON json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
            if (json != null) {
                JSONObject jsonObject = JSONObject.parseObject(json.toJSONString());
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    totalCount = data.getInteger("count");
                    log.info("开始同步路段数据，总数：{}", totalCount);

                    // 处理第一页数据
                    int count = syncAndSaveSegmentData(json.toJSONString());
                    processedCount += count;
                    log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);

                    // 计算总页数
                    int totalPages = (totalCount + size - 1) / size;

                    // 循环获取剩余页数据
                    for (page = 2; page <= totalPages; page++) {
                        timestamp = HttpForAutoUtils.getTimestamp();
                        params = new TreeMap<>();
                        params.put("page", page);
                        params.put("size", size);
                        sign = getSign("/segment", params, timestamp);

                        json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
                        if (json != null) {
                            count = syncAndSaveSegmentData(json.toJSONString());
                            processedCount += count;
                            log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);
                        }

                        // 每处理10页数据后暂停1秒，避免请求过于频繁
                        if (page % 10 == 0) {
                            Thread.sleep(1000);
                        }
                    }

                    log.info("路段数据同步完成，总处理{}条数据", processedCount);
                } else {
                    log.error("同步路段数据失败：{}", jsonObject.getString("message"));
                }
            }
        } catch (Exception e) {
            log.error("同步路段数据异常", e);
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/syncTask")
    public void syncTask() {
        // 同步MQI数据
        syncTaskData("roadindex/mqi");
        // 同步PQI数据
        syncTaskData("roadindex/pqi");
    }

    /**
     * 同步任务数据
     * @param endpoint 接口端点
     */
    private void syncTaskData(String endpoint) {
        String url = this.url.replace("{endpoint}", endpoint);
        int page = 1;
        int size = 1000; // 每页获取1000条数据
        int totalCount = 0;
        int processedCount = 0;

        try {
            // 先获取第一页数据，同时获取总数
            String timestamp = HttpForAutoUtils.getTimestamp();
            Map<String, Object> params = new TreeMap<>();
            params.put("page", page);
            params.put("size", size);
            String sign = getSign("/" + endpoint, params, timestamp);

            JSON json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
            if (json != null) {
                JSONObject jsonObject = JSONObject.parseObject(json.toJSONString());
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject data = jsonObject.getJSONObject("data");
                    totalCount = data.getInteger("count");
                    log.info("开始同步{}数据，总数：{}", endpoint, totalCount);

                    // 处理第一页数据
                    int count = syncAndSaveTaskData(json.toJSONString());
                    processedCount += count;
                    log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);

                    // 计算总页数
                    int totalPages = (totalCount + size - 1) / size;

                    // 循环获取剩余页数据
                    for (page = 2; page <= totalPages; page++) {
                        timestamp = HttpForAutoUtils.getTimestamp();
                        params = new TreeMap<>();
                        params.put("page", page);
                        params.put("size", size);
                        sign = getSign("/" + endpoint, params, timestamp);

                        json = HttpForAutoUtils.doGet(url, timestamp, sik, sign, params);
                        if (json != null) {
                            count = syncAndSaveTaskData(json.toJSONString());
                            processedCount += count;
                            log.info("已处理第{}页数据，处理{}条，累计处理{}条", page, count, processedCount);
                        }

                        // 每处理10页数据后暂停1秒，避免请求过于频繁
                        if (page % 10 == 0) {
                            Thread.sleep(1000);
                        }
                    }

                    log.info("{}数据同步完成，总处理{}条数据", endpoint, processedCount);
                } else {
                    log.error("同步{}数据失败：{}", endpoint, jsonObject.getString("message"));
                }
            }
        } catch (Exception e) {
            log.error("同步{}数据异常", endpoint, e);
            throw new RuntimeException(e);
        }
    }

    private String getSign(String url, Map<String, Object> params,String timestamp){
        return HttpForAutoUtils.doHmacSHA2(url, params, sis, sik, timestamp);
    }

    /**
     * 同步病害数据到本地数据库
     * @param jsonResult 接口返回的JSON结果
     * @return 同步的记录数
     */
    private int syncAndSaveDefectData(String jsonResult) {
        if (StringUtils.isEmpty(jsonResult)) {
            log.error("同步病害数据失败：返回数据为空");
            return 0;
        }

        try {
            // 解析JSON数据
            JSONObject jsonObject = JSONObject.parseObject(jsonResult);
            if (jsonObject.getInteger("code") != 200) {
                log.error("同步病害数据失败：{}", jsonObject.getString("message"));
                return 0;
            }

            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray items = data.getJSONArray("items");

            // 添加详细日志
            log.info("API返回数据解析: data对象存在={}, items数组存在={}, items大小={}",
                    data != null, items != null, items != null ? items.size() : 0);

            if (items == null || items.isEmpty()) {
                log.info("没有需要同步的病害数据");
                return 0;
            }

            // 1. 预先加载所有字典数据
            Map<String, HighwayCategoryDictionary> dictionaryMap = loadCategoryDictionaries();

            // 2. 从NmInspContent中加载病害类型信息
            List<NmInspContent> nmInspContents = loadNmInspContents();

            // 3. 处理病害数据，使用Map来存储，确保每个ID只保留最新的数据
            List<HighwayDefectInfo> defectInfoList = new ArrayList<>();
            List<HighwayDefectMedia> defectMediaList = new ArrayList<>();

            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);

                // 处理病害信息和媒体信息
                defectInfoList.add(processDefectItemWithDuplicateCheck(item, defectMediaList, dictionaryMap, nmInspContents));
            }

            // 4. 批量保存数据
            saveBatchDefectInfo(defectInfoList);
            saveBatchDefectMedia(defectMediaList);

            log.info("同步病害数据成功，共处理病害数据{}条，媒体数据{}条", defectInfoList.size(), defectMediaList.size());

            // 5. 在数据入库后下载图片
            if (!defectMediaList.isEmpty()) {
                // 使用线程池异步下载图片，不阻塞主流程
                downloadDefectMediaImages(defectMediaList);
            }

            return defectInfoList.size();

        } catch (Exception e) {
            log.error("同步病害数据异常", e);
            return 0;
        }
    }

    /**
     * 加载所有字典数据
     */
    private Map<String, HighwayCategoryDictionary> loadCategoryDictionaries() {
        List<HighwayCategoryDictionary> allDictionaries = categoryDictionaryService.list();
        Map<String, HighwayCategoryDictionary> dictionaryMap = new HashMap<>();
        for (HighwayCategoryDictionary dict : allDictionaries) {
            dictionaryMap.put(dict.getCategoryCode(), dict);
        }
        return dictionaryMap;
    }

    /**
     * 从NmInspContent中加载病害类型信息
     */
    private List<NmInspContent> loadNmInspContents() {
        return nmInspContentService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<NmInspContent>()
                        .eq("FACILITY_CAT", "LM")
        );
    }

    /**
     * 处理单个病害数据项，检查重复ID并保留最新的数据
     */
    private HighwayDefectInfo processDefectItemWithDuplicateCheck(JSONObject item,
    List<HighwayDefectMedia> defectMediaList, Map<String, HighwayCategoryDictionary> dictionaryMap, List<NmInspContent> nmInspContents) {

        // 1. 处理病害主信息
        HighwayDefectInfo defectInfo = convertToDefectInfo(item);

        // 2. 应用字典数据
        String defectTypeCode = defectInfo.getDefectType();
        String objectSubtypeCode = defectInfo.getObjectSubtype();

        // 强制设置cat_name为路面
        defectInfo.setCatName("路面");
        //设置巡查类型
        defectInfo.setInspType(2);
        defectInfo.setIsCreated(0);

        // 获取对象子类型名称
        String typeName = null;
        HighwayCategoryDictionary objectSubtype = dictionaryMap.get(objectSubtypeCode);
        if (objectSubtype != null) {
            typeName = objectSubtype.getCategoryName();
            defectInfo.setTypeName(typeName);
        }

        // 获取病害类型名称
        String categoryName = null;
        HighwayCategoryDictionary defectCategory = dictionaryMap.get(defectTypeCode);
        if (defectCategory != null) {
            categoryName = defectCategory.getCategoryName();
            // 处理特殊情况
            if ("带状修补".equals(categoryName)) {
                categoryName = "条状修补";
            } else if ("网状裂缝".equals(categoryName)) {
                categoryName = "龟裂";
            }

            // 从NmInspContent中查找匹配的病害类型
            if (categoryName != null) {
                // 保存最终的病害名称为final变量，以便在lambda表达式中使用
                final String finalCategoryName = categoryName;

                // 筛选type='ITEM'的记录
                NmInspContent matchedInspContent = nmInspContents.stream()
                        .filter(content -> "ITEM".equals(content.getType()) && finalCategoryName.equals(content.getName()))
                        .findFirst()
                        .orElse(null);

                if (matchedInspContent != null) {
                    // 找到匹配的病害类型，设置dssType为yhDssType
                    defectInfo.setYhDssType(matchedInspContent.getDssType());
                    defectInfo.setDssTypeName(matchedInspContent.getName());

                    // 查找父节点作为路面类型
                    String pid = matchedInspContent.getPid();
                    if (pid != null) {
                        NmInspContent parentContent = nmInspContents.stream()
                                .filter(content -> pid.equals(content.getId()))
                                .findFirst()
                                .orElse(null);

                        if (parentContent != null) {
                            // 用父节点名称作为路面类型
                            defectInfo.setTypeName(parentContent.getName());
                        }
                    }
                }
            }
        }

        // 3. 处理病害媒体信息
        JSONArray mediaArray = item.getJSONArray("media");
        if (mediaArray != null && !mediaArray.isEmpty()) {
            for (int j = 0; j < mediaArray.size(); j++) {
                JSONObject mediaItem = mediaArray.getJSONObject(j);
                HighwayDefectMedia defectMedia = convertToDefectMedia(mediaItem, defectInfo.getId());
                defectMediaList.add(defectMedia);
            }
        }
        return defectInfo;
    }

    /**
     * 将JSON对象转换为HighwayDefectInfo实体
     * @param jsonObject JSON对象
     * @return HighwayDefectInfo实体
     */
    private HighwayDefectInfo convertToDefectInfo(JSONObject jsonObject) {
        HighwayDefectInfo defectInfo = new HighwayDefectInfo();

        // 设置主键ID
        String id = jsonObject.getString("id");
        defectInfo.setId(StringUtil.getUUID());
        defectInfo.setOgId(id);

        // 设置基本属性
        defectInfo.setObjectType(jsonObject.getString("objecttype"));
        defectInfo.setObjectSubtype(jsonObject.getString("objectsubtype"));
        defectInfo.setDefectType(jsonObject.getString("defecttype"));
        defectInfo.setState(jsonObject.getString("state"));
        defectInfo.setTaskId(jsonObject.getString("taskid"));
        defectInfo.setSnapshotId(jsonObject.getString("snapshotid"));
        defectInfo.setDataStatus(jsonObject.getInteger("datastatus"));
        defectInfo.setTargetLen(convertToBigDecimal(jsonObject.getDouble("targetlen")));
        defectInfo.setTargetArea(convertToBigDecimal(jsonObject.getDouble("targetarea")));
        defectInfo.setObjDistance(convertToBigDecimal(jsonObject.getDouble("objdistance")));
        defectInfo.setIsCalibrated(jsonObject.getInteger("iscalibrated"));
        defectInfo.setTransformation(jsonObject.getInteger("transformation"));
        defectInfo.setWarningFlag(jsonObject.getInteger("warningflag"));
        defectInfo.setDataFrom(jsonObject.getString("datafrom"));

        // 设置几何信息
        defectInfo.setGeomType("Point"); // 固定为Point类型

        // 设置时间字段
        defectInfo.setCreatedTime(parseDateTime(jsonObject.getString("createdtime")));
        defectInfo.setUpdatedTime(parseDateTime(jsonObject.getString("updatedtime")));

        // 设置车道信息
        JSONArray laneNum = jsonObject.getJSONArray("lanenum");
        if (laneNum != null && !laneNum.isEmpty()) {
            defectInfo.setLaneNum(StringUtils.collectionToCommaDelimitedString(laneNum));
        }

        // 设置地理位置信息
        JSONObject geometry = jsonObject.getJSONObject("geometry");
        if (geometry != null) {
            JSONArray coordinates = geometry.getJSONArray("coordinates");
            if (coordinates != null && coordinates.size() >= 2) {
                defectInfo.setLongitude(convertToBigDecimal(coordinates.getDouble(0)));
                defectInfo.setLatitude(convertToBigDecimal(coordinates.getDouble(1)));
            }
        }

        // 设置可选属性
        if (jsonObject.containsKey("segmentname")) {
            defectInfo.setSegmentName(jsonObject.getString("segmentname"));
        }
        if (jsonObject.containsKey("segmentid")) {
            defectInfo.setSegmentId(jsonObject.getString("segmentid"));
        }
        if (jsonObject.containsKey("inspectdirection")) {
            defectInfo.setInspectDirection(jsonObject.getString("inspectdirection"));
        }
        if (jsonObject.containsKey("stakestart")) {
            defectInfo.setStakeStart(jsonObject.getString("stakestart"));
        }
        if (jsonObject.containsKey("stakeend")) {
            defectInfo.setStakeEnd(jsonObject.getString("stakeend"));
        }

        return defectInfo;
    }

    /**
     * 将JSON对象转换为HighwayDefectMedia实体
     * @param jsonObject JSON对象
     * @param defectId 病害ID
     * @return HighwayDefectMedia实体
     */
    private HighwayDefectMedia convertToDefectMedia(JSONObject jsonObject, String defectId) {
        HighwayDefectMedia defectMedia = new HighwayDefectMedia();

        // 设置主键ID，使用生成的唯一ID
        String mediaId = jsonObject.getString("id");
        defectMedia.setId(StringUtil.getUUID());
        defectMedia.setDefectId(defectId);
        defectMedia.setOgId(mediaId);

        // 设置基本属性
        defectMedia.setSubId(jsonObject.getInteger("subid"));
        defectMedia.setName(jsonObject.getString("name"));
        String imgUrl = jsonObject.getString("img");
        defectMedia.setImgUrl(imgUrl);
        defectMedia.setRoiUrl(jsonObject.getString("roi"));
        defectMedia.setRect(jsonObject.getString("rect"));
        defectMedia.setTargetLen(convertToBigDecimal(jsonObject.getDouble("targetlen")));
        defectMedia.setTargetArea(convertToBigDecimal(jsonObject.getDouble("targetarea")));
        defectMedia.setIgnoreCalc(jsonObject.getBoolean("ignorecalc") ? 1 : 0);

        // 预先设置本地路径，但不立即下载图片
        if (imgUrl != null && !imgUrl.isEmpty()) {
            try {
                String name = jsonObject.getString("name");
                // 处理文件名，确保有正确的扩展名
                String fileName = name;
                if (!fileName.contains(".")) {
                    // 如果没有扩展名，从URL中提取或默认使用.jpg
                    String extension = ".jpg";
                    if (imgUrl.contains(".")) {
                        extension = imgUrl.substring(imgUrl.lastIndexOf("."));
                    }
                    fileName = fileName + extension;
                }

                // 构建本地文件路径
                String localPath = "D:"+File.separator+"INSP_IMAGES" +File.separator+ fileName;

                // 设置本地路径，但不立即下载
                defectMedia.setLocalPath(localPath);
            } catch (Exception e) {
                log.error("生成本地路径失败: {}", imgUrl, e);
            }
        }

        return defectMedia;
    }

    /**
     * 批量下载病害媒体图片
     * @param defectMediaList 媒体信息列表
     */
    private void downloadDefectMediaImages(List<HighwayDefectMedia> defectMediaList) {
        if (defectMediaList == null || defectMediaList.isEmpty()) {
            return;
        }

        log.info("开始下载病害图片，共 {} 张", defectMediaList.size());

        // 创建保存目录
        File dir = new File("D://INSP_IMAGES");
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // 计算线程数：根据CPU核心数，但不超过20
        int threadCount = Math.min(20, Runtime.getRuntime().availableProcessors() * 2);
        log.info("使用{}个线程进行图片下载", threadCount);

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(defectMediaList.size());

        // 记录下载结果
        final int[] successCount = {0};
        final int[] failCount = {0};

        // 分批提交下载任务
        for (HighwayDefectMedia media : defectMediaList) {
            executorService.submit(() -> {
                String imgUrl = media.getImgUrl();
                String localPath = media.getLocalPath();

                try {
                    if (imgUrl != null && !imgUrl.isEmpty() && localPath != null && !localPath.isEmpty()) {
                        File localFile = new File(localPath);

                        // 检查目录是否存在，不存在则创建
                        File parentDir = localFile.getParentFile();
                        if (parentDir != null && !parentDir.exists()) {
                            parentDir.mkdirs();
                        }

                        // 如果文件已存在，跳过下载
                        if (localFile.exists() && localFile.length() > 0) {
                            log.debug("图片已存在，跳过下载: {}", localPath);
                            synchronized (successCount) {
                                successCount[0]++;
                            }
                        } else {
                            // 下载图片
                            URL url = new URL(imgUrl);
                            try (InputStream in = url.openStream();
                                 FileOutputStream out = new FileOutputStream(localFile)) {
                                byte[] buffer = new byte[8192]; // 使用更大的缓冲区
                                int bytesRead;
                                while ((bytesRead = in.read(buffer)) != -1) {
                                    out.write(buffer, 0, bytesRead);
                                }
                                synchronized (successCount) {
                                    successCount[0]++;
                                }
                                log.debug("图片下载成功: {}", localPath);
                            }
                        }
                    }
                } catch (Exception e) {
                    synchronized (failCount) {
                        failCount[0]++;
                    }
                    log.error("下载图片失败: {} -> {}", imgUrl, localPath, e);
                } finally {
                    latch.countDown();

                    // 每下载100张图片输出一次进度
                    int completed = (int)(defectMediaList.size() - latch.getCount());
                    if (completed % 100 == 0 || completed == defectMediaList.size()) {
                        log.info("图片下载进度: {}/{}，成功: {}，失败: {}",
                                completed, defectMediaList.size(), successCount[0], failCount[0]);
                    }
                }
            });
        }

        try {
            // 等待所有下载任务完成，最多等待30分钟
            boolean completed = latch.await(30, TimeUnit.MINUTES);
            if (!completed) {
                log.warn("图片下载超时，30分钟内未完成全部下载任务");
            }
        } catch (InterruptedException e) {
            log.error("等待下载完成时发生中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(1, TimeUnit.MINUTES)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }

        log.info("病害图片下载完成，共{}张，成功: {}，失败: {}",
                defectMediaList.size(), successCount[0], failCount[0]);
    }

    /**
     * 批量保存或更新病害信息
     * @param defectInfoList 病害信息列表
     */
    private void saveBatchDefectInfo(List<HighwayDefectInfo> defectInfoList) {
        if (defectInfoList == null || defectInfoList.isEmpty()) {
            return;
        }

        try {
            // 批量新增
            if (!defectInfoList.isEmpty()) {
                // 分批保存，避免批量插入时的参数过多
                int insertBatchSize = 500;
                for (int i = 0; i < defectInfoList.size(); i += insertBatchSize) {
                    int endIndex = Math.min(i + insertBatchSize, defectInfoList.size());
                    List<HighwayDefectInfo> batchToInsert = defectInfoList.subList(i, endIndex);
                    highwayDefectInfoService.saveBatch(batchToInsert);
                    log.debug("已保存病害数据 {}/{}", Math.min(endIndex, defectInfoList.size()), defectInfoList.size());
                }
                log.info("新增病害数据 {} 条", defectInfoList.size());
            }

        } catch (Exception e) {
            log.error("批量保存或更新病害信息异常", e);
            throw e;
        }
    }

    /**
     * 批量保存或更新媒体信息
     * @param defectMediaList 媒体信息列表
     */
    private void saveBatchDefectMedia(List<HighwayDefectMedia> defectMediaList) {
        if (defectMediaList == null || defectMediaList.isEmpty()) {
            return;
        }

        try {
            // 提取所有ID
            List<String> ids = defectMediaList.stream()
                    .map(HighwayDefectMedia::getId)
                    .collect(Collectors.toList());

            // 分批查询现有记录，避免Oracle的IN子句1000个参数限制
            int batchSize = 900; // 设置批次大小小于1000
            Set<String> existingIds = new HashSet<>();

            for (int i = 0; i < ids.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, ids.size());
                List<String> batchIds = ids.subList(i, endIndex);

                if (!batchIds.isEmpty()) {
                    List<HighwayDefectMedia> batchExisting = new ArrayList<>(highwayDefectMediaService.listByIds(batchIds));
                    batchExisting.forEach(media -> existingIds.add(media.getId()));
                }

                log.debug("已查询媒体记录 {}/{}", Math.min(endIndex, ids.size()), ids.size());
            }

            // 批量新增
            if (!defectMediaList.isEmpty()) {
                // 分批保存，避免批量插入时的参数过多
                int insertBatchSize = 500;
                for (int i = 0; i < defectMediaList.size(); i += insertBatchSize) {
                    int endIndex = Math.min(i + insertBatchSize, defectMediaList.size());
                    List<HighwayDefectMedia> batchToInsert = defectMediaList.subList(i, endIndex);
                    highwayDefectMediaService.saveBatch(batchToInsert);
                    log.debug("已保存媒体数据 {}/{}", Math.min(endIndex, defectMediaList.size()), defectMediaList.size());
                }
                log.info("新增媒体数据 {} 条", defectMediaList.size());
            }

        } catch (Exception e) {
            log.error("批量保存或更新媒体信息异常", e);
            throw e;
        }
    }

    /**
     * 解析日期时间字符串
     * @param dateTimeStr 日期时间字符串，格式如：2025-04-13T08:09:00.746Z
     * @return LocalDateTime对象，使用ZonedDateTime解析ISO格式的日期时间字符串，考虑时区信息
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }

        try {
            // 使用Java 8的时间API解析ISO格式的日期时间字符串
            Instant instant = Instant.parse(dateTimeStr);
            return LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
        } catch (Exception e) {
            log.error("解析日期时间失败: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 将Double值转换为BigDecimal
     * @param value Double值
     * @return BigDecimal对象
     */
    private BigDecimal convertToBigDecimal(Double value) {
        if (value == null) {
            return null;
        }
        return BigDecimal.valueOf(value);
    }

    /**
     * 同步交安事件数据到本地数据库
     * @param jsonResult 接口返回的JSON结果
     * @return 同步的记录数
     */
    private int syncAndSaveEventData(String jsonResult) {
        if (StringUtils.isEmpty(jsonResult)) {
            log.error("同步交安数据失败：返回数据为空");
            return 0;
        }

        int totalCount = 0;

        try {
            // 解析JSON数据
            JSONObject jsonObject = JSONObject.parseObject(jsonResult);
            if (jsonObject.getInteger("code") != 200) {
                log.error("同步交安数据失败：{}", jsonObject.getString("message"));
                return 0;
            }

            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray items = data.getJSONArray("items");

            if (items == null || items.isEmpty()) {
                log.info("没有需要同步的交安数据");
                return 0;
            }

            // 批量处理记录
            List<HighwayEventInfo> eventInfoList = new ArrayList<>();
            List<HighwayEventMedia> eventMediaList = new ArrayList<>();

            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);

                // 1. 处理交安主信息
                HighwayEventInfo eventInfo = convertToEventInfo(item);
                eventInfoList.add(eventInfo);

                // 2. 处理交安媒体信息
                JSONArray mediaArray = item.getJSONArray("media");
                if (mediaArray != null && !mediaArray.isEmpty()) {
                    for (int j = 0; j < mediaArray.size(); j++) {
                        JSONObject mediaItem = mediaArray.getJSONObject(j);
                        HighwayEventMedia eventMedia = convertToEventMedia(mediaItem, eventInfo.getId());
                        eventMediaList.add(eventMedia);
                    }
                }
            }

            // 批量保存或更新交安信息
            saveBatchEventInfo(eventInfoList);
            totalCount = eventInfoList.size();

            // 批量保存或更新媒体信息
            saveBatchEventMedia(eventMediaList);

            log.info("同步交安数据成功，共处理交安数据{}条，媒体数据{}条", totalCount, eventMediaList.size());

        } catch (Exception e) {
            log.error("同步交安数据异常", e);
        }

        return totalCount;
    }

    /**
     * 将JSON对象转换为HighwayEventInfo实体
     * @param jsonObject JSON对象
     * @return HighwayEventInfo实体
     */
    private HighwayEventInfo convertToEventInfo(JSONObject jsonObject) {
        HighwayEventInfo eventInfo = new HighwayEventInfo();

        // 设置主键ID
        String id = jsonObject.getString("id");
        eventInfo.setId(id);

        // 设置基本属性
        eventInfo.setObjectType(jsonObject.getString("objecttype"));
        if (jsonObject.containsKey("objectsubtype")) {
            eventInfo.setObjectSubtype(jsonObject.getString("objectsubtype"));
        }
        if (jsonObject.containsKey("defecttype")) {
            eventInfo.setDefectType(jsonObject.getString("defecttype"));
        }
        eventInfo.setState(jsonObject.getString("state"));
        eventInfo.setTaskId(jsonObject.getString("taskid"));
        eventInfo.setSnapshotId(jsonObject.getString("snapshotid"));
        eventInfo.setDataStatus(jsonObject.getInteger("datastatus"));
        eventInfo.setDataFrom(jsonObject.getString("datafrom"));

        // 设置几何信息
        eventInfo.setGeomType("Point"); // 固定为Point类型

        // 设置时间字段
        eventInfo.setCreatedTime(parseDateTime(jsonObject.getString("createdtime")));
        eventInfo.setUpdatedTime(parseDateTime(jsonObject.getString("updatedtime")));

        // 设置地理位置信息
        JSONObject geometry = jsonObject.getJSONObject("geometry");
        if (geometry != null) {
            JSONArray coordinates = geometry.getJSONArray("coordinates");
            if (coordinates != null && coordinates.size() >= 2) {
                eventInfo.setLongitude(convertToBigDecimal(coordinates.getDouble(0)));
                eventInfo.setLatitude(convertToBigDecimal(coordinates.getDouble(1)));
            }
        }

        // 设置可选属性
        if (jsonObject.containsKey("segmentname")) {
            eventInfo.setSegmentName(jsonObject.getString("segmentname"));
        }
        if (jsonObject.containsKey("segmentid")) {
            eventInfo.setSegmentId(jsonObject.getString("segmentid"));
        }
        if (jsonObject.containsKey("inspectdirection")) {
            eventInfo.setInspectDirection(jsonObject.getString("inspectdirection"));
        }
        if (jsonObject.containsKey("stakestart")) {
            eventInfo.setStakeStart(jsonObject.getString("stakestart"));
        }
        if (jsonObject.containsKey("stakeend")) {
            eventInfo.setStakeEnd(jsonObject.getString("stakeend"));
        }
        if (jsonObject.containsKey("transformation")) {
            eventInfo.setTransformation(jsonObject.getInteger("transformation"));
        }
        if (jsonObject.containsKey("iscalibrated")) {
            eventInfo.setIsCalibrated(jsonObject.getInteger("iscalibrated"));
        }
        if (jsonObject.containsKey("trafficsigntype")) {
            eventInfo.setTrafficSignType(jsonObject.getString("trafficsigntype"));
        }

        return eventInfo;
    }

    /**
     * 将JSON对象转换为HighwayEventMedia实体
     * @param jsonObject JSON对象
     * @param eventId 交安事件ID
     * @return HighwayEventMedia实体
     */
    private HighwayEventMedia convertToEventMedia(JSONObject jsonObject, String eventId) {
        HighwayEventMedia eventMedia = new HighwayEventMedia();

        // 设置主键ID，使用事件ID + 媒体ID组合
        String mediaId = jsonObject.getString("id");
        eventMedia.setId(eventId + "_" + mediaId);
        eventMedia.setEventId(eventId);

        // 设置基本属性
        eventMedia.setSubId(jsonObject.getInteger("subid"));
        eventMedia.setName(jsonObject.getString("name"));
        eventMedia.setImgUrl(jsonObject.getString("img"));
        eventMedia.setRoiUrl(jsonObject.getString("roi"));
        eventMedia.setRect(jsonObject.getString("rect"));
        eventMedia.setIgnoreCalc(jsonObject.getBoolean("ignorecalc") ? 1 : 0);

        return eventMedia;
    }

    /**
     * 批量保存或更新交安事件信息
     * @param eventInfoList 交安事件信息列表
     */
    private void saveBatchEventInfo(List<HighwayEventInfo> eventInfoList) {
        if (eventInfoList == null || eventInfoList.isEmpty()) {
            return;
        }

        // 使用MybatisPlus提供的saveOrUpdateBatch方法处理批量保存或更新
        highwayEventInfoService.saveOrUpdateBatch(eventInfoList);
    }

    /**
     * 批量保存或更新交安事件媒体信息
     * @param eventMediaList 交安事件媒体信息列表
     */
    private void saveBatchEventMedia(List<HighwayEventMedia> eventMediaList) {
        if (eventMediaList == null || eventMediaList.isEmpty()) {
            return;
        }

        // 使用MybatisPlus提供的saveOrUpdateBatch方法处理批量保存或更新
        highwayEventMediaService.saveOrUpdateBatch(eventMediaList);
    }

    /**
     * 同步路段数据到本地数据库
     * @param jsonResult 接口返回的JSON结果
     * @return 同步的记录数
     */
    private int syncAndSaveSegmentData(String jsonResult) {
        if (StringUtils.isEmpty(jsonResult)) {
            log.error("同步路段数据失败：返回数据为空");
            return 0;
        }

        try {
            // 解析JSON数据
            JSONObject jsonObject = JSONObject.parseObject(jsonResult);
            if (jsonObject.getInteger("code") != 200) {
                log.error("同步路段数据失败：{}", jsonObject.getString("message"));
                return 0;
            }

            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray items = data.getJSONArray("items");

            if (items == null || items.isEmpty()) {
                log.info("没有需要同步的路段数据");
                return 0;
            }

            // 处理记录
            List<HighwaySegment> segmentList = new ArrayList<>();

            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);
                // 转换为路段实体
                HighwaySegment segment = convertToSegment(item);
                segmentList.add(segment);
            }

            // 批量保存或更新路段信息
            highwaySegmentService.saveOrUpdateBatch(segmentList);

            log.info("同步路段数据成功，共处理路段数据{}条", segmentList.size());

            return segmentList.size();

        } catch (Exception e) {
            log.error("同步路段数据异常", e);
            return 0;
        }
    }

    /**
     * 同步计算任务数据到本地数据库
     * @param jsonResult 接口返回的JSON结果
     * @return 同步的记录数
     */
    private int syncAndSaveTaskData(String jsonResult) {
        if (StringUtils.isEmpty(jsonResult)) {
            log.error("同步计算任务数据失败：返回数据为空");
            return 0;
        }

        try {
            // 解析JSON数据
            JSONObject jsonObject = JSONObject.parseObject(jsonResult);
            if (jsonObject.getInteger("code") != 200) {
                log.error("同步计算任务数据失败：{}", jsonObject.getString("message"));
                return 0;
            }

            JSONObject data = jsonObject.getJSONObject("data");
            JSONArray items = data.getJSONArray("items");

            if (items == null || items.isEmpty()) {
                log.info("没有需要同步的计算任务数据");
                return 0;
            }

            // 处理记录
            List<HighwayTask> taskList = new ArrayList<>();

            for (int i = 0; i < items.size(); i++) {
                JSONObject item = items.getJSONObject(i);
                // 转换为计算任务实体
                HighwayTask task = convertToTask(item);
                taskList.add(task);
            }

            // 批量保存或更新计算任务信息
            highwayTaskService.saveOrUpdateBatch(taskList);

            log.info("同步计算任务数据成功，共处理计算任务数据{}条", taskList.size());

            return taskList.size();

        } catch (Exception e) {
            log.error("同步计算任务数据异常", e);
            return 0;
        }
    }

    /**
     * 将JSON对象转换为HighwaySegment实体
     * @param jsonObject JSON对象
     * @return HighwaySegment实体
     */
    private HighwaySegment convertToSegment(JSONObject jsonObject) {
        HighwaySegment segment = new HighwaySegment();

        // 设置ID
        segment.setId(jsonObject.getString("id"));

        // 设置基本属性
        segment.setSegname(jsonObject.getString("segname"));
        segment.setStakestart(jsonObject.getString("stakestart"));
        segment.setStakeend(jsonObject.getString("stakeend"));
        segment.setRoadtype(jsonObject.getString("roadtype"));

        // 设置数值类型
        if (jsonObject.containsKey("mileage")) {
            segment.setMileage(convertToBigDecimal(jsonObject.getDouble("mileage")));
        }
        if (jsonObject.containsKey("lanecount")) {
            segment.setLanecount(jsonObject.getInteger("lanecount"));
        }
        if (jsonObject.containsKey("maxspeedlimit")) {
            segment.setMaxspeedlimit(jsonObject.getInteger("maxspeedlimit"));
        }

        // 设置其他可选属性
        if (jsonObject.containsKey("mark")) {
            segment.setMark(jsonObject.getString("mark"));
        }
        if (jsonObject.containsKey("city")) {
            segment.setCity(jsonObject.getString("city"));
        }
        if (jsonObject.containsKey("district")) {
            segment.setDistrict(jsonObject.getString("district"));
        }
        if (jsonObject.containsKey("curinglevel")) {
            segment.setCuringlevel(jsonObject.getString("curinglevel"));
        }
        if (jsonObject.containsKey("direction")) {
            segment.setDirection(jsonObject.getString("direction"));
        }
        if (jsonObject.containsKey("segcode")) {
            segment.setSegcode(jsonObject.getString("segcode"));
        }

        // 设置时间字段
        if (jsonObject.containsKey("createdtime")) {
            segment.setCreatedtime(parseDateTime(jsonObject.getString("createdtime")));
        }
        if (jsonObject.containsKey("updatedtime")) {
            segment.setUpdatedtime(parseDateTime(jsonObject.getString("updatedtime")));
        }

        return segment;
    }

    /**
     * 将JSON对象转换为HighwayTask实体
     * @param jsonObject JSON对象
     * @return HighwayTask实体
     */
    private HighwayTask convertToTask(JSONObject jsonObject) {
        HighwayTask task = new HighwayTask();

        // 设置ID - 注意JSON中的"id"对应实体中的"taskId"
        task.setTaskId(jsonObject.getString("id"));

        // 设置基本属性
        if (jsonObject.containsKey("type")) {
            // JSON中的"type"对应实体中的"taskType"
            task.setTaskType(jsonObject.getString("type"));
        }
        if (jsonObject.containsKey("segmentid")) {
            // JSON中的"segmentid"对应实体中的"segmentId"
            task.setSegmentId(jsonObject.getString("segmentid"));
        }
        if (jsonObject.containsKey("segmentcode")) {
            // JSON中的"segmentcode"对应实体中的"segmentCode"
            task.setSegmentCode(jsonObject.getString("segmentcode"));
        }
        if (jsonObject.containsKey("segmentname")) {
            // JSON中的"segmentname"对应实体中的"segmentName"
            task.setSegmentName(jsonObject.getString("segmentname"));
        }
        if (jsonObject.containsKey("inspectdirection")) {
            // JSON中的"inspectdirection"对应实体中的"inspectDirection"
            task.setInspectDirection(jsonObject.getString("inspectdirection"));
        }
        if (jsonObject.containsKey("roadtype")) {
            // JSON中的"roadtype"对应实体中的"roadType"
            task.setRoadType(jsonObject.getString("roadtype"));
        }
        if (jsonObject.containsKey("stakestart")) {
            // JSON中的"stakestart"对应实体中的"stakeStart"
            task.setStakeStart(jsonObject.getString("stakestart"));
        }
        if (jsonObject.containsKey("stakeend")) {
            // JSON中的"stakeend"对应实体中的"stakeEnd"
            task.setStakeEnd(jsonObject.getString("stakeend"));
        }

        // 设置数值类型
        if (jsonObject.containsKey("length")) {
            // JSON中的"length"对应实体中的"segmentLength"
            task.setSegmentLength(convertToBigDecimal(jsonObject.getDouble("length")));
        }
        if (jsonObject.containsKey("pqi")) {
            // JSON中的"pqi"对应实体中的"pqiValue"
            task.setPqiValue(convertToBigDecimal(jsonObject.getDouble("pqi")));
        }
        if (jsonObject.containsKey("level")) {
            // JSON中的"level"对应实体中的"qualityLevel"
            task.setQualityLevel(jsonObject.getInteger("level"));
        }

        // 设置状态
        if (jsonObject.containsKey("status")) {
            // JSON中的"status"对应实体中的"taskStatus"
            task.setTaskStatus(jsonObject.getString("status"));
        }

        // 设置时间字段
        if (jsonObject.containsKey("starttime")) {
            // JSON中的"starttime"对应实体中的"startTime"
            task.setStartTime(parseDateTime(jsonObject.getString("starttime")));
        }
        if (jsonObject.containsKey("endtime")) {
            // JSON中的"endtime"对应实体中的"endTime"
            task.setEndTime(parseDateTime(jsonObject.getString("endtime")));
        }
        if (jsonObject.containsKey("createdtime")) {
            // JSON中的"createdtime"对应实体中的"createdTime"
            task.setCreatedTime(parseDateTime(jsonObject.getString("createdtime")));
        }
        if (jsonObject.containsKey("updatedtime")) {
            // JSON中的"updatedtime"对应实体中的"updatedTime"
            task.setUpdatedTime(parseDateTime(jsonObject.getString("updatedtime")));
        }

        return task;
    }

    /**
     * 将本地文件路径列表转换为MultipartFile数组
     * @param localPaths 本地文件路径列表
     * @return MultipartFile数组
     */
    private MultipartFile[] convertLocalPathsToMultipartFiles(List<String> localPaths) {
        if (localPaths == null || localPaths.isEmpty()) {
            return new MultipartFile[0];
        }

        List<MultipartFile> files = new ArrayList<>();

        for (String localPath : localPaths) {
            try {
                File file = new File(localPath);
                if (file.exists() && file.isFile()) {
                    // 创建MultipartFile对象
                    MultipartFile multipartFile = new MockMultipartFile(
                            file.getName(),
                            file.getName(),
                            Files.probeContentType(file.toPath()),
                            Files.readAllBytes(file.toPath())
                    );
                    files.add(multipartFile);
                } else {
                    log.warn("文件不存在或不是文件: {}", localPath);
                }
            } catch (Exception e) {
                log.error("转换文件失败: {}", localPath, e);
            }
        }

        return files.toArray(new MultipartFile[0]);
    }

    @GetMapping("/queryRoute")
    public RestResult<List<HighwayOrgRef>> queryRoute(){
        List<HighwayOrgRef> list = orgRefService.list();
        return RestResult.success(list);
    }
}
