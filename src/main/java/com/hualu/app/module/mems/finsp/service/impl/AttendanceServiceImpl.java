package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.HsmsSlopeInfoService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto;
import com.hualu.app.module.mems.finsp.entity.Attendance;
import com.hualu.app.module.mems.finsp.entity.AttendanceAddr;
import com.hualu.app.module.mems.finsp.entity.SlopeBatchAttentionGs;
import com.hualu.app.module.mems.finsp.mapper.AttendanceMapper;
import com.hualu.app.module.mems.finsp.service.AttendanceAddrService;
import com.hualu.app.module.mems.finsp.service.AttendanceService;
import com.hualu.app.module.mems.finsp.service.SlopeBatchAttentionGsService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.mems.GeoUtils;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
@Slf4j
@Service
public class AttendanceServiceImpl extends ServiceImpl<AttendanceMapper, Attendance> implements AttendanceService {

    /**
     * 日期yyyy-MM-dd正则表达式
     */
    private static final String REGEX_DATE_YYYY_MM_DD = "(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)";

    @Autowired
    AttendanceAddrService addrService;

    @Resource
    private SlopeBatchAttentionGsService slopeBatchAttentionGsService;

    @Resource
    private HsmsSlopeInfoService slopeInfoService;

    @Override
    public boolean checkIsAttendance(String userId, String structId) {
        LambdaQueryWrapper<Attendance> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Attendance::getCreateUserId,userId);
        queryWrapper.eq(Attendance::getStructId,structId);
        queryWrapper.apply("attendance_time = to_date({0},'YYYY-MM-dd')",new Date());
        Integer integer = baseMapper.selectCount(queryWrapper);
        return integer == 0 ? false : true;
    }

    @Override
    public void attendance(Attendance attendance) {
        //一个用户一个结构物半小时内只打一次卡
        Attendance lastAttendance = getLastAttendance(CustomRequestContextHolder.getUserId(), attendance.getStructId());
        boolean allow = validAttendanceTime(lastAttendance);

        if (!allow) {
            throw new BaseException("一个用户一个结构物15秒内只能打卡一次");
        }

        attendance.setOrgCode(CustomRequestContextHolder.getOrgIdString());
        attendance.setOrgName(CustomRequestContextHolder.getOrgName());
        attendance.setInspPerson(CustomRequestContextHolder.getUserName());
        attendance.setAttendanceTime(DateUtil.now());

        //获取打卡点位置
        List<AttendanceAddr> attendanceAddrs = addrService.selectAddr(attendance.getStructId(), attendance.getType());
        if (CollectionUtil.isEmpty(attendanceAddrs)){
            throw new BaseException(-11,"未设置打卡点位置");
        }
        //needValidLocation=0是未定位（没有经纬度），直接拿打卡点的位置直接通过，没有打卡点提示
        if (attendance.getNeedValidLocation() == 0){
            AttendanceAddr addr = attendanceAddrs.get(0);
            attendance.setLongitude(addr.getGisY().toString());
            attendance.setLatitude(addr.getGisX().toString());
        }else {
            for (int i = 0 ; i < attendanceAddrs.size() ; i++){
                AttendanceAddr item = attendanceAddrs.get(i);
                double distance = GeoUtils.getdistance(item.getGisX(),item.getGisY(),Double.valueOf(attendance.getLongitude()), Double.valueOf(attendance.getLatitude()) );
                //相距卡位置10公里以内，说明有效
                if (distance < 10000){
                    break;
                }
                if (i == attendanceAddrs.size() - 1 ){
                    throw new BaseException(-11,"未在指定打卡位置，相距"+distance+"米，请在打卡点页面新增打卡点");
                }
            }
        }

        if (lastAttendance == null){
            attendance.setLastAttendanceTime("-");
            attendance.setLastAttendanceUserId("-");
            attendance.setLastAttendancePerson("-");
            attendance.setOrders(1d);
        }else {
            attendance.setLastAttendancePerson(lastAttendance.getLastAttendancePerson());
            attendance.setLastAttendanceTime(attendance.getLastAttendanceTime());
            attendance.setLastAttendanceUserId(attendance.getLastAttendanceUserId());
            attendance.setOrders(lastAttendance.getOrders()+1);
        }

        IBaseStructFace iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(attendance.getType())).findFirst().orElse(null);
        BaseStructDto structDto = iBaseStructFace.getId(attendance.getStructId());
        if (structDto == null){
            throw new BaseException("未找到结构物");
        }
        attendance.setStructName(structDto.getStructName());
        //用于数据返回
        attendance.setId(H_KeyWorker.nextIdToString());
        save(attendance);
    }

    @Override public RestResult<List<SlopeBatchAttentionDto>> batchSaveAttendance(String batchId,
        String validLocation, Double lat, Double lng) {
        boolean needValidLocation = "1".equals(validLocation) && lat != null && lng != null;
        SlopeBatchAttentionGs batchItem = slopeBatchAttentionGsService.getById(batchId);
        if (batchItem == null) {
            throw new BaseException("无效二维码");
        }

        double startStake = batchItem.getStartStake() != null ? batchItem.getStartStake().doubleValue() : -1;
        double endStake = batchItem.getEndStake() != null ? batchItem.getEndStake().doubleValue() : -1;
        List<SlopeBatchAttentionDto> list =
            slopeInfoService.listByStakeRange(batchItem.getLineCode(), startStake, endStake);
        if (CollectionUtil.isEmpty(list)) {
            return RestResult.success(list);
        }

        List<Attendance> attendances = new ArrayList<>();
        for (SlopeBatchAttentionDto slopeBatchAttentionDto : list) {
            //一个用户一个结构物半小时内只打一次卡
            Attendance lastAttendance = getLastAttendance(
                CustomRequestContextHolder.getUserId(),
                slopeBatchAttentionDto.getSlopeId()
            );
            boolean allow = validAttendanceTime(lastAttendance);

            if (!allow) {
                slopeBatchAttentionDto.setStatus("一个用户一个结构物15秒内只能打卡一次");
                continue;
            }
            Attendance attendance = new Attendance();
            attendance.setOrgCode(CustomRequestContextHolder.getOrgIdString());
            attendance.setOrgName(CustomRequestContextHolder.getOrgName());
            attendance.setInspPerson(CustomRequestContextHolder.getUserName());
            attendance.setAttendanceTime(DateUtil.now());

            //获取打卡点位置
            List<AttendanceAddr> attendanceAddrs = addrService.selectAddr(
                slopeBatchAttentionDto.getSlopeId(), "BP");
            if (CollectionUtil.isEmpty(attendanceAddrs)){
                slopeBatchAttentionDto.setStatus("未设置打卡点位置");
                continue;
            }
            //needValidLocation=0是未定位（没有经纬度），直接拿打卡点的位置直接通过，没有打卡点提示
            if (!needValidLocation) {
                AttendanceAddr addr = attendanceAddrs.get(0);
                attendance.setLongitude(addr.getGisY().toString());
                attendance.setLatitude(addr.getGisX().toString());
            } else {
                for (int i = 0; i < attendanceAddrs.size(); i++) {
                    AttendanceAddr item = attendanceAddrs.get(i);
                    if (item.getGisX() == 0 || item.getGisY() == 0) {
                        //slopeBatchAttentionDto.setStatus("边坡无经纬度数据");
                        break;
                    }
                    double distance = GeoUtils.getdistance(
                        item.getGisX(), item.getGisY(), lng, lat
                    );
                    //相距卡位置10公里以内，说明有效
                    if (distance < 10000) {
                        break;
                    }
                    if (i == attendanceAddrs.size() - 1) {
                        slopeBatchAttentionDto.setStatus(
                            "未在指定打卡位置，相距" + String.format("%.3f", distance / 1000f) + "千米，请在打卡点页面新增打卡点");
                    }
                }
            }

            if (lastAttendance == null) {
                attendance.setLastAttendanceTime("-");
                attendance.setLastAttendanceUserId("-");
                attendance.setLastAttendancePerson("-");
                attendance.setOrders(1d);
            } else {
                attendance.setLastAttendancePerson(lastAttendance.getLastAttendancePerson());
                attendance.setLastAttendanceTime(attendance.getLastAttendanceTime());
                attendance.setLastAttendanceUserId(attendance.getLastAttendanceUserId());
                attendance.setOrders(lastAttendance.getOrders() + 1);
            }
            attendance.setStructName(slopeBatchAttentionDto.getSlopeName());
            attendance.setStructId(slopeBatchAttentionDto.getSlopeId());
            attendance.setType("BP");
            attendance.setId(H_KeyWorker.nextIdToString());
            if (StringUtils.isEmpty(slopeBatchAttentionDto.getStatus())) {
                slopeBatchAttentionDto.setStatus("打卡成功");
            }
            if ("打卡成功".equals(slopeBatchAttentionDto.getStatus())) {
                attendances.add(attendance);
            }
        }
        if (!CollectionUtil.isEmpty(attendances)) {
            saveBatch(attendances, 100);
        }
        return RestResult.success(list);
    }

    private static boolean validAttendanceTime(Attendance lastAttendance) {
        boolean allow = true;
        try {
            String attendanceTime = lastAttendance.getAttendanceTime();
            DateTime lastAttentionDate = DateUtil.parse(attendanceTime, "yyyy-MM-dd HH:mm:ss");
            DateTime after15Second = lastAttentionDate.offset(DateField.SECOND, 15);
            DateTime now = new DateTime();
            if (now.isBefore(after15Second)) {
                allow = false;
            }
        } catch (Exception ignored) {
        }
        return true;
    }

    @Override
    public Attendance getLastAttendance(String userId, String structId) {
        return baseMapper.getLastAttendance(userId, structId);
    }

    @Override public IPage<Attendance> getAttentionList(
        String startDate,
        String endDate,
        String type,
        int page,
        int pageSize
    ) {
        if (StringUtils.hasText(startDate) && !startDate.matches(REGEX_DATE_YYYY_MM_DD)) {
            throw new BaseException("开始日期格式不对");
        }

        if (StringUtils.hasText(endDate) && !endDate.matches(REGEX_DATE_YYYY_MM_DD)) {
            throw new BaseException("结束日期格式不对");
        }

        String orgId = CustomRequestContextHolder.getOrgIdString();
        if (!StringUtils.hasText(orgId)) {
            throw new BaseException("没有管养单位ID");
        }

        IPage<Attendance> p = new Page<>(page, pageSize);
        String format = "yyy-MM-dd";
        String nextDay = "";
        if (endDate != null && endDate.matches(REGEX_DATE_YYYY_MM_DD)) {
            nextDay = DateUtil.parseLocalDateTime(endDate, format)
                .plusDays(1)
                .format(DateTimeFormatter.ofPattern(format));
        }

        LambdaQueryWrapper<Attendance> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StringUtils.hasText(type), Attendance::getType, type);
        lambdaQueryWrapper.in(Attendance::getOrgCode, H_DataAuthHelper.selectOrgIds());
        lambdaQueryWrapper.apply(
            StringUtils.hasText(startDate),
            " to_date(ATTENDANCE_TIME,'yyyy-MM-dd HH24:mi:ss') >= to_date({0}, 'yyyy-mm-dd')",
            startDate
        );
        lambdaQueryWrapper.apply(
            StringUtils.hasText(endDate),
            " to_date(ATTENDANCE_TIME,'yyyy-MM-dd HH24:mi:ss') <= to_date({0}, 'yyyy-mm-dd')",
            nextDay
        );
        lambdaQueryWrapper.last("order by to_date(ATTENDANCE_TIME,'yyyy-MM-dd HH24:mi:ss') desc");
        return page(p, lambdaQueryWrapper);
    }
}
