<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.AttendanceAddrMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.AttendanceAddr">
        <result column="ID" property="id" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="GIS_X" property="gisX" />
        <result column="GIS_Y" property="gisY" />
        <result column="ADDR" property="addr" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_USER_ID" property="createUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, STRUCT_ID, STRUCT_NAME, FACILITY_CAT, GIS_X, GIS_Y, ADDR, CREATE_TIME, CREATE_USER_ID
    </sql>

</mapper>
