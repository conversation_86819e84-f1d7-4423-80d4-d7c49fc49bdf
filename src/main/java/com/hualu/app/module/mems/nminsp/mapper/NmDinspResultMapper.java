package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.nminsp.entity.NmDinspResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 新版日常巡查检查结论表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface NmDinspResultMapper extends BaseMapper<NmDinspResult> {

    List<NmDinspResult> selectByDinspId(@Param("dinspId") String dinspId);

    List<NmDinspResult> listByDinspIds(@Param("dinspIds") List<String> dinspIds);
}
