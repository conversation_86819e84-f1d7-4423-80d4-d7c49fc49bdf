package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairStatDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspGpsCountDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspRunningDto;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface DmFinspService extends IService<DmFinsp> {


    /**
     * 根据流程获取经常检查单
     * @param processInstId
     * @return
     */
    DmFinsp getDmFinspByProcessInstId(Long processInstId);

    /**
     * 无病害经常检查单办理
     * @param userCode
     * @param processInstIds
     * @param nextUserId
     * @param apprOpinion
     */
    void approveDmFinspNoDss(String userCode, String processInstIds, String nextUserId, String apprOpinion);


    /**
     * 经常检查单审批办理（包含有病害和无病害）
     * @param userCode 当前用户
     * @param processInstIds  流程实例ID
     * @param nextUserId 下一节点审批人
     * @param apprOpinion 审批意见
     * @param status 状态
     */
    void approveDmFinsp(String userCode, String processInstIds, String nextUserId, String apprOpinion,int status);

    /**
     * 修改经常检查单结论
     * @param dmFinsp
     */
    void updateDmFinspResult(DmFinsp dmFinsp);


    /**
     * 修改经常检查单审批状态
     * @param processInstId
     * @param status
     */
    void updateDmFinspStatus(long processInstId, int status);

    /**
     * 删除经常检查单
     * @param finspId
     */
    void delDmFinsp(String finspId);

    /**
     *是否存在当前单号  不存在：FALSE  存在：TRUE
     * @param finspCode
     * @param finspId
     * @return
     */
    boolean isContainCode(String facilityCat,String finspCode,String finspId);

    /**
     * 获取经常检查单号
     * @param facilityCat 结构物类型
     * @param orgEn 机构英文缩写
     * @return
     */
    String getNextCode(@NotBlank String facilityCat, String orgEn);


    /**
     * 添加或者修改经常检查单
     * @param dmFinsp
     */
    void saveOrUpdateFinsp(DmFinsp dmFinsp);

    /**
     * 获取上张经常检查单
     * @param dmFinsp
     * @param status 状态
     * @return
     */
    DmFinsp getLastFinsp(DmFinsp dmFinsp,Integer status);

    /**
     * 设置经常检查单病害数
     * @param finspId
     */
    void updateDssNum(String finspId);

    /**
     * 正在巡查的经常检查单
     * @return
     */
    List<DmFinspRunningDto> selectRunning();

    /**
     * 判断是否存在单
     * @param structId
     * @param facilityCat
     * @param xcDate
     * @return
     */
    String getFinspId(String structId, String facilityCat, Date xcDate);

    /**
     * 根据一个季度内，查询对应的经常检查单
     * @param structId
     * @param facilityCat
     * @param xcDate
     * @return
     */
    List<DmFinsp> listByQuarter(String structId,String facilityCat,Date xcDate);

    /**
     * 边坡轨迹完成率统计
     * @param orgRouteMap
     * @return
     */
    List<DmFinspGpsCountDto> countGpsCompleteStat(Map<String, Set<String>> orgRouteMap,String startMonth,String endMonth);

    /**
     * 经常检查病害维修表
     * @param page
     * @param month
     * @return
     */
    List<DmFinspDssRepairDto> selectDssRepairPage(Page page, String month,String orgId);

    List<DmFinspDssRepairStatDto> selectDssRepairStat(String month, String orgId);
}
