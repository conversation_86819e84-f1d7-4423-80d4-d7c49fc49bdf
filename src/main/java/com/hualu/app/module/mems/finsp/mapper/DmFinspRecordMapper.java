package com.hualu.app.module.mems.finsp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DmFinspRecordMapper extends BaseMapper<DmFinspRecord> {

    void insertRecordByQH(@Param("newFinspId") String newFinspId, @Param("oldFinspId") String oldFinspId);

    void insertRecordByOther(@Param("newFinspId") String newFinspId, @Param("oldFinspId") String oldFinspId);

    //todo 方向暂时使用车道代替 20230414
    void insertRecordByJA(@Param("newFinspId") String newFinspId, @Param("lineId") String lineId, @Param("startStake") Double startStake
            , @Param("endStake") Double endStake, @Param("startDate") String startDate);


    /**
     * 根据经常检查单ID，查询病害明细
     * @param finspId
     * @return
     */
    List<DmFinspRecord> queryHisRecordByFinspId(@Param("finspId") String finspId, @Param("facilityCat") String facilityCat);


    /**
     * 根据检查检查单，返回病害信息（用于界面显示）
     * @param page
     * @param finspId
     * @return
     */
    List<DmFinspRecord> selectViewRecordByFinspId(IPage page, @Param("finspId") String finspId);


    /**
     * 根据病害ID，返回病害信息（用于界面显示）
     * @param dssId
     * @return
     */
    List<DmFinspRecord> selectViewRecordById(@Param("dssId") String dssId);

    /**
     * 根据结构物，返回历史病害信息
     * @param page
     * @param structId
     * @param facilityCat
     * @return
     */
    List<DmFinspRecord> selectHisRecordByStructId(Page page,@Param("structId") String structId,@Param("facilityCat") String facilityCat);

    List<DssInfoDto> selectDssRecordPage(Page page,@Param(Constants.WRAPPER) QueryWrapper queryWrapper);
}
