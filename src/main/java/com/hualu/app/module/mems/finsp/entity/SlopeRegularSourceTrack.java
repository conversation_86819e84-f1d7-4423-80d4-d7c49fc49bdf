package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 经常检查原始轨迹、用户巡检边坡时需要覆盖这个轨迹，才算检查完成
 */
@Setter @Getter
@ApiModel(description = "经常检查原始轨迹、用户巡检边坡时需要覆盖这个轨迹，才算检查完成")
@TableName(value = "MEMSDB.SLOPE_REGULAR_SOURCE_TRACK")
public class SlopeRegularSourceTrack {

  @TableId(value = "ID", type = IdType.INPUT)
  @ApiModelProperty(value = "")
  private String id;

  @TableField(value = "SLOPE_ID")
  @ApiModelProperty(value = "边坡id")
  private String slopeId;

  @TableField(value = "LAT")
  @ApiModelProperty(value = "纬度")
  private BigDecimal lat;

  @TableField(value = "LNG")
  @ApiModelProperty(value = "经度")
  private BigDecimal lng;

  @TableField(value = "\"TIME\"")
  @ApiModelProperty(value = "采集时间")
  private LocalDateTime time;
}