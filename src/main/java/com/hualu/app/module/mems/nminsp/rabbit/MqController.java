package com.hualu.app.module.mems.nminsp.rabbit;

import cn.hutool.core.util.RandomUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.dto.NmDinspEmptDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.hualu.app.module.mems.nminsp.util.H_RabbitHelper;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/mq")
public class MqController {

    @GetMapping("sendGps")
    public RestResult<String> sendMsg(){

        for (int i = 0; i < 10; i++) {
            NmDinspGps nmDinspGps = new NmDinspGps().setGpsId(H_KeyWorker.nextIdToString()).setTime(LocalDateTime.now());
            H_RabbitHelper.sendGpsMessage(nmDinspGps);
        }
        return RestResult.success("");
    }

    @GetMapping("sendDinsp")
    public RestResult<String> sendDinsp(){
        for (int i = 0; i < 10; i++) {
            NmDinspEmptDto dto = new NmDinspEmptDto().setUserId(H_KeyWorker.nextIdToString()).setUserName(RandomUtil.randomString(10));
            H_RabbitHelper.sendDinspMessage(dto);
        }
        return RestResult.success("");
    }

}
