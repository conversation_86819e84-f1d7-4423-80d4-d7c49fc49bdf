package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspRecordMapper;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspResultMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspItemService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 新版经常检查结论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Service
public class NmFinspResultServiceImpl extends ServiceImpl<NmFinspResultMapper, NmFinspResult> implements NmFinspResultService {

    private static final String JA_ISSUE_DESC_FORMAT = "{}存在{}，{}";

    @Autowired
    NmInspItemService inspItemService;

    @Lazy
    @Autowired
    NmFinspService nmFinspService;

    @Lazy
    @Autowired
    NmFinspRecordService nmFinspRecordService;

    @Autowired
    NmFinspRecordMapper nmFinspRecordMapper;

    @Override
    public void supplementRemainingResult(NmFinspResult result) {
        NmFinsp nmFinsp = nmFinspService.getById(result.getFinspId());
        if (null == nmFinsp) {
            return;
        }
        // 数据库存在的结论项
        List<NmFinspResult> dbResults = lambdaQuery().eq(NmFinspResult::getFinspId, result.getFinspId()).list();
        List<NmInspItem> inspItems = inspItemService.listXcTypeByFinsp(nmFinsp, "FM");
        // 说明检查结论已经补充完整
        if (inspItems.size() == dbResults.size()) {
            updateById(result);
            return;
        }
        List<NmFinspResult> remainResults = Lists.newArrayList();
        // 查询未生成的结论项
        //List<NmInspItem> noCreateItems = inspItems.stream().filter(e -> !e.getItemId().equals(result.getItemId())).collect(Collectors.toList());
        inspItems.forEach(row->{
            if (row.getItemId().equals(result.getItemId())) {
                remainResults.add(result);
            }else {
                NmFinspResult newResult = BeanUtil.copyProperties(row, NmFinspResult.class);
                newResult.setFinspId(result.getFinspId());
                remainResults.add(newResult);
            }
        });
        if (CollectionUtil.isNotEmpty(remainResults)) {
            saveOrUpdateBatch(remainResults);
        }
    }

    @Override
    public List<NmFinspResult> createNmFinspResult(NmFinsp nmDinsp) {
        List<NmFinspResult> results = baseMapper.selectByFinspId(nmDinsp.getFinspId());
        //生成检查结论
        if (CollectionUtil.isEmpty(results)){
            List<NmInspItem> items = inspItemService.listXcTypeByFinsp(nmDinsp, "FM");
            results = CollectionUtil.newArrayList();
            for (NmInspItem item : items) {
                NmFinspResult result = new NmFinspResult();
                result.setFinspId(nmDinsp.getFinspId()).setItemId(item.getItemId()).setResId(H_KeyWorker.nextIdToString())
                        .setDssType(item.getDssType()).setPartId(item.getPartId());
                results.add(result);
            }
            saveBatch(results);
        }
        // 清除病害描述，系统会根据最新病害自动生成结论
        results.stream().forEach(e->e.setIssueDesc(null));
        return results;
    }

    @Override
    public void delBatchByDinspIds(List<String> inspIds) {
        if (CollectionUtil.isEmpty(inspIds)){
            return;
        }
        LambdaQueryWrapper<NmFinspResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(NmFinspResult::getFinspId, inspIds);
        remove(wrapper);
    }

    @Override
    public List<NmFinspResult> listResultsByJA(List<String> finspIds, String inspCont) {
        List<NmFinspRecord> nmFinspRecords = nmFinspRecordService.lambdaQuery().in(NmFinspRecord::getFinspId, finspIds).list();
        nmFinspRecordService.showView(nmFinspRecords,null);
        LinkedHashMap<String, List<NmFinspRecord>> finspMap = nmFinspRecords.stream().filter(e -> StrUtil.isNotBlank(e.getFinspId())).collect(Collectors.groupingBy(NmFinspRecord::getFinspId, LinkedHashMap::new, Collectors.toList()));

        List<NmFinspResult> resultsAll = Lists.newArrayList();
        finspMap.forEach((k,v)->{
            List<NmFinspResult> tempResults = Lists.newArrayList();
            v.forEach(record->{
                NmFinspResult nmDinspResult = new NmFinspResult();
                String stake = record.getStake() == null ? null : record.getStake().toString();
                nmDinspResult.setInspCont(inspCont).setInspCom(H_StakeHelper.convertCnStake(stake))
                        .setResId(H_KeyWorker.nextIdToString()).setFinspId(k);
                String issueDesc = StrUtil.format(JA_ISSUE_DESC_FORMAT, record.getStructPartName(), record.getDssTypeName(), record.getDssNum());
                if (StrUtil.isNotBlank(record.getDssDesc())){
                    issueDesc = issueDesc + "，病害描述："+record.getDssDesc();
                }
                issueDesc = record.getLineDirectName()+"方向，"+issueDesc;
                nmDinspResult.setIssueDesc(issueDesc);
                tempResults.add(nmDinspResult);
            });
            resultsAll.addAll(tempResults);
        });

        // 移除已经生成结论的数据
        boolean b = finspIds.removeAll(finspMap.keySet());
        //如果系统没有病害，初始化一项检查结论
        for (String dinspId : finspIds) {
            NmFinspResult nmDinspResult = new NmFinspResult().setResId(H_KeyWorker.nextIdToString()).setInspCont(inspCont).setFinspId(dinspId);
            resultsAll.add(nmDinspResult);
        }
        return resultsAll;
    }

    @Override
    public List<NmFinspResult> listResultsByFinspIds(List<String> finspIds,String cat) {
        return buildNmFinspResultByFinspIds(finspIds,true);
    }

    /**
     * 构件经常检查单结论（包含已生成、未生成的结论）
     * @param finspIds
     * @param buildResult
     * @return
     */
    private List<NmFinspResult> buildNmFinspResultByFinspIds(List<String> finspIds,boolean buildResult) {
        if (CollectionUtil.isEmpty(finspIds)){
            return Collections.emptyList();
        }
        Map<String, NmInspItem> itemMap = inspItemService.listXcTypeToMap("FM");
        List<NmFinspResult> allResults = Lists.newArrayList();
        ListUtil.partition(finspIds,500).forEach(rows->{
            List<NmFinspResult> dbRes = lambdaQuery().in(NmFinspResult::getFinspId, finspIds).list();
            if (CollectionUtil.isNotEmpty(dbRes)){
                allResults.addAll(dbRes);
            }
        });
        initInspItem(allResults,itemMap);
        // 不自动补全，直接返回
        if (!buildResult){
            return allResults;
        }
        LinkedHashMap<String, List<NmFinspResult>> finspMap = allResults.stream().collect(Collectors.groupingBy(NmFinspResult::getFinspId, LinkedHashMap::new, Collectors.toList()));

        // 移除已经有检查记录的数据
        finspIds.removeAll(finspMap.keySet());
        buildNoResultList(finspIds,allResults);
        for (String finspId : finspIds) {
            List<NmFinspResult> dbRes = finspMap.get(finspId);
            initInspItem(dbRes, itemMap);
        }
        allResults.forEach(item->{
            if (StrUtil.isBlank(item.getIssueDesc())){
                item.setIssueDesc("未发现病害");
            }
        });
        return allResults;

    }

    /**
     * 组装未生成结论的数据
     * @param noResultFinspIds
     * @param allResults
     */
    private void buildNoResultList(List<String> noResultFinspIds, List<NmFinspResult> allResults) {
        List<NmFinsp> nmFinsps = nmFinspService.listFinspIdAndInspFrequency(noResultFinspIds);
        if (CollectionUtil.isNotEmpty(nmFinsps)){
            Map<String, List<NmFinsp>> groupMap = nmFinsps.stream().collect(Collectors.groupingBy(e -> e.getFacilityCat() + "_" + e.getInspFrequency()));
            groupMap.forEach((k,v)->{
                // 导出都是一种设施的数据
                List<NmInspItem> allItems = inspItemService.listXcTypeByFinsp(new NmFinsp().setFacilityCat(nmFinsps.get(0).getFacilityCat()), "FM");
                allResults.addAll(buildFinspResult(nmFinsps,allItems));
            });
        }
    }

    /**
     * 构建经常检查结论
     * @param nmFinsps
     * @param allItems
     * @return
     */
    private List<NmFinspResult> buildFinspResult(List<NmFinsp> nmFinsps, List<NmInspItem> allItems) {
        List<NmFinspResult> results = Lists.newArrayList();

        nmFinsps.forEach(finsp->{
            Integer inspFrequency = ObjectUtil.isEmpty(finsp.getInspFrequency())?1:Integer.parseInt(finsp.getInspFrequency());
            if (H_BasedataHepler.JA.equals(finsp.getFacilityCat())){
                inspFrequency = 1;
            }
            final Integer targetInspFrequency = inspFrequency;
            String targetFacilityCat = finsp.getFacilityCat();
            //涵洞检查频率属性为空
            List<NmInspItem> items = allItems.stream()
                    .filter(e -> {
                        // 先检查设施类别是否有效且匹配
                        if (StrUtil.isBlank(targetFacilityCat) || !targetFacilityCat.equals(e.getFacilityCat())) {
                            return false;
                        }
                        // 再检查巡检频率条件
                        return (H_BasedataHepler.HD.equals(e.getFacilityCat()))
                                || (e.getInspFrequency() != null && e.getInspFrequency().equals(targetInspFrequency));
                    }).collect(Collectors.toList());

            items.forEach(row->{
                NmFinspResult nmDinspResult = new NmFinspResult();
                nmDinspResult.setFinspId(finsp.getFinspId())
                        .setItemId(row.getItemId())
                        .setInspCom(row.getInspCom())
                        .setInspCont(row.getInspCont())
                        .setIssueDesc("未发现病害");
                results.add(nmDinspResult);
            });
        });
        return results;
    }

    /**
     * 初始化检查项内容
     * @param allResults
     * @param itemMap
     */
    private void initInspItem(List<NmFinspResult> allResults, Map<String, NmInspItem> itemMap) {
        if (CollectionUtil.isEmpty(allResults)){
            return;
        }
        allResults.forEach(row->{
            NmInspItem item = itemMap.get(row.getItemId());
            if (item != null){
                row.setItemId(item.getItemId())
                        .setDssType(item.getDssType())
                        .setPartId(item.getPartId())
                        .setInspCom(item.getInspCom())
                        .setInspCont(item.getInspCont());
                if (StrUtil.isBlank(row.getResId())){
                    row.setResId(H_KeyWorker.nextIdToString());
                }
            }
        });
    }

    @Override
    public void createBatchNmFinspResult(NmFinsp nmfinsp, List<String> finspIds) {
        // 防御性校验：确保输入参数非空
        if (nmfinsp == null || CollectionUtil.isEmpty(finspIds)) {
            return ;
        }

        // 使用 Optional 避免显式空检查，并直接返回空列表
        List<NmFinspResult> results = Optional.ofNullable(inspItemService.listXcTypeByFinsp(nmfinsp, "FM"))
                .filter(items -> !items.isEmpty())
                .map(items -> finspIds.stream()
                        // 并行流提升性能（需确保线程安全）
                        .parallel()
                        // 扁平化笛卡尔积生成结果对象
                        .flatMap(dinspId -> items.stream()
                                .map(item -> buildNmFinspResult(dinspId, item))
                        )
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        saveBatch(results);
    }



    @Override
    public boolean isNmFinspResultSet(String orgCode, String userCode) {
        StringBuilder sb = new StringBuilder("select * from NM_FINSP_RESULT_SET where org_code = '");
        sb.append(orgCode).append("' and user_code = '").append(userCode).append("' ");
        List<Map> list = this.nmFinspRecordMapper.findNmFinspResultSet(orgCode,userCode);
        if(null != list && !list.isEmpty()) {
            return true;
        }
        return false;
    }

    @Override
    public void updateFinspResultsByRecords(String finspId, String facilityCat, List<NmFinspRecord> records) {
        if(StringUtils.isNotBlank(finspId)) {
            List<NmFinspResult> results = this.nmFinspRecordMapper.findNmFinspResultBySql(finspId);
            if(null != records && !records.isEmpty() && null != results && !results.isEmpty()) {
                List<String> sqlList = new ArrayList<String>();
                for(Iterator<NmFinspResult> i = results.iterator(); i.hasNext();) {
                    NmFinspResult re = i.next();
                    String finspResId = re.getResId();
                    StringBuilder remark = new StringBuilder();
                    StringBuilder dssType = new StringBuilder();

                    if(remark.length() > 0 && remark.length() < 2000) {
                        StringBuilder sb = new StringBuilder("update NM_FINSP_RESULT set ");
                        if("SD".equals(facilityCat)) {
                            sb.append("ISSUE_DESC = '").append(remark).append("', INSP_RESULT = 'B' ");

                        }else {
                            sb.append("remark = '");
                            sb.append(remark).append("' ");
                            if(dssType.length() > 0 && dssType.toString().contains(",")) {
                                dssType.delete(dssType.length() - 1, dssType.length());
                                sb.append(", ISSUE_DESC = '").append(dssType).append("' ");
                            }
                        }
                        sb.append("where FINSP_RES_ID = '").append(finspResId).append("' ");
                        sqlList.add(sb.toString());
                    }
                }
                if(sqlList.size() > 0) {
                    for(String sql:sqlList) {
                        this.nmFinspRecordMapper.saveOrUpdateBySql(sql);
                    }
                }
            }
        }
    }

    // 封装对象构建逻辑
    private NmFinspResult buildNmFinspResult(String dinspId, NmInspItem item) {
        return new NmFinspResult()
                .setFinspId(dinspId)
                .setItemId(item.getItemId())
                .setResId(H_KeyWorker.nextIdToString())
                .setDssType(item.getDssType())
                .setPartId(item.getPartId());
    }
}
