package com.hualu.app.module.mems.finsp.dto.word;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

@Accessors(chain = true)
@Data
public class PcFinspWordDto implements Serializable {

    private String finspId;

    private String mntType;
    //项目公司
    private String orgName;

    //检查单号
    private String finspCode;

    //边坡层级
    private String slopeLevel;

    //排查日期
    private LocalDate inspDate;

    //日常养护单位
    private String inspOrgName;

    // 路线编号及名称
    private String lineName;

    // 桩号
    private String structName;

    //检查人
    private String inspPerson;

    //复核人
    private String reviewPerson;
}
