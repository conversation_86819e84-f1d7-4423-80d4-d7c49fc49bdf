<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.comm.mapper.AppUpdateVersionMapper">
  <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.comm.entity.AppUpdateVersion">
    <!--@mbg.generated-->
    <!--@Table GDGS.APP_UPDATE_VERSION-->
    <result column="VERSION_ID" jdbcType="VARCHAR" property="versionId" />
    <result column="VERSION_CODE" jdbcType="DECIMAL" property="versionCode" />
    <result column="VERSION_NAME" jdbcType="VARCHAR" property="versionName" />
    <result column="APP_URL" jdbcType="VARCHAR" property="appUrl" />
    <result column="VERSION_INFO" jdbcType="VARCHAR" property="versionInfo" />
    <result column="APPLICATION_ID" jdbcType="VARCHAR" property="applicationId" />
    <result column="FORCE_UPDATE" jdbcType="DECIMAL" property="forceUpdate" />
    <result column="RELEASE_DATE" jdbcType="TIMESTAMP" property="releaseDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    VERSION_ID, VERSION_CODE, VERSION_NAME, APP_URL, VERSION_INFO, APPLICATION_ID, FORCE_UPDATE, 
    RELEASE_DATE
  </sql>
</mapper>