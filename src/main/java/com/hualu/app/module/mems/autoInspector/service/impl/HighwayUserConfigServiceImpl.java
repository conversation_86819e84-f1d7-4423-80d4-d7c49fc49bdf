package com.hualu.app.module.mems.autoInspector.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.autoInspector.entity.HighwayUserConfig;
import com.hualu.app.module.mems.autoInspector.service.HighwayUserConfigService;
import com.hualu.app.module.mems.autoInspector.mapper.HighwayUserConfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_USER_CONFIG(巡检用户配置表)】的数据库操作Service实现
* @createDate 2025-05-14 09:08:02
*/
@Service
public class HighwayUserConfigServiceImpl extends ServiceImpl<HighwayUserConfigMapper, HighwayUserConfig>
    implements HighwayUserConfigService{

}




