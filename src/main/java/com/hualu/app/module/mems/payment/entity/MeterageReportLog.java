package com.hualu.app.module.mems.payment.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 上传报表日志
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("METERAGE_REPORT_LOG")
@ApiModel(value="MeterageReportLog对象", description="上传报表日志")
public class MeterageReportLog implements Serializable {

    
    @NotBlank(message="[]不能为空")
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("")
    @TableField("ID")
    private String id;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("用户")
    @TableField("USER_CODE")
    private String userCode;
    
    @Size(max= 400,message="编码长度不能超过400")
    @ApiModelProperty("文件名")
    @TableField("FILE_NAME")
    private String fileName;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("文件ID")
    @TableField("FILE_ID")
    private String fileId;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("创建时间")
    @TableField("CREATE_TIME")
    private String createTime;
    
    @Size(max= 40,message="编码长度不能超过40")
    @ApiModelProperty("合同ID")
    @TableField("CONTR_ID")
    private String contrId;
}
