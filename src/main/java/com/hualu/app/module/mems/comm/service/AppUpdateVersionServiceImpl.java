package com.hualu.app.module.mems.comm.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.comm.entity.AppUpdateVersion;
import com.hualu.app.module.mems.comm.mapper.AppUpdateVersionMapper;
import com.hualu.app.module.mems.comm.service.impl.AppUpdateVersionService;
import java.math.BigDecimal;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class AppUpdateVersionServiceImpl
    extends ServiceImpl<AppUpdateVersionMapper, AppUpdateVersion>
    implements AppUpdateVersionService {

  @Override public AppUpdateVersion checkAppVersion(int versionCode, String appId) {
    return lambdaQuery()
        .eq(StringUtils.isNotEmpty(appId), AppUpdateVersion::getApplicationId, appId)
        .gt(AppUpdateVersion::getVersionCode, BigDecimal.valueOf(versionCode))
        .one();
  }
}
