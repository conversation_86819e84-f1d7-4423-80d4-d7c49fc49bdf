package com.hualu.app.module.mems.dss.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.segments.MergeSegments;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.dto.DssRepairTimelinessDto;
import com.hualu.app.module.mems.dss.dto.DssWxDto;
import com.hualu.app.module.mems.dss.dto.InfoForSlopeBase;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.mapper.DssInfoMapper;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.dss.service.IDssRecordService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_RestResultHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_ListSortHelper;
import com.tg.dev.excelimport.util.H_ExcelDownloadHelper;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 *  病害管理
 * <AUTHOR>
 * @since 2023-04-14
 */
@RestController
@RequestMapping("/dssInfo")
public class DssInfoController extends M_MyBatisController<DssInfo, DssInfoMapper> {

    @Autowired
    DssInfoService dssInfoService;

    /**
     * 边坡日常巡查/经常检查
     * @param type
     * @param slopeId
     * @return
     */
    @GetMapping("/selectSlopeDm")
    public RestResult<List<InfoForSlopeBase>> selectSlopeDm(@RequestParam("type") String type, @RequestParam("slopeId") String slopeId) {
        return RestResult.success(dssInfoService.selectSlopeDm(type, slopeId));
    }


    /**
     * 病害闭合方式（南粤公司的结构物巡查APP）
     * @param dssIds 病害ID,已逗号分隔
     * @param closeType 闭合方式（1：任务单闭合、2：专项闭合）
     * @param dssSource 病害来源 1：日常巡查，2：经常检查，3：定检，9：专项
     * @return
     */
    @PostMapping("updateDssRecordByCloseType")
    public RestResult<String> updateDssRecordByCloseType(String dssIds,String closeType,String dssSource){
        Map<String, IDssRecordService> beansOfType = CustomApplicationContextHolder.getBeansOfType(IDssRecordService.class);
        IDssRecordService iDssRecordService = beansOfType.values().stream().filter(e -> e.getDssSource().equals(dssSource)).findFirst().orElse(null);
        if (iDssRecordService != null){
            iDssRecordService.updateDssRecordByCloseType(StrUtil.split(dssIds,","), closeType);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 病害修复分页查询（南粤公司的结构物巡查APP）
     * @param dssSource 病害来源 1：日常巡查，2：经常检查，3：定检，9：专项
     * @param repairStatus 修复状态 0:未修复，1：修复中，2：已修复
     * @param startDate 发现日期-开始时间
     * @return
     */
    @PostMapping("selectDssRecordPage")
    public RestResult<List<DssInfoDto>> selectDssRecordPage(@RequestParam(required = true) String dssSource
            ,String repairStatus,String startDate){
        Page page = getPage();
        Map reqParam = getReqParam();
        QueryWrapper<DssInfoDto> queryWrapper = setWrapper(reqParam,false);
        setDssRecordWrapper(queryWrapper,reqParam);
        Map<String, IDssRecordService> beansOfType = CustomApplicationContextHolder.getBeansOfType(IDssRecordService.class);
        IDssRecordService iDssRecordService = beansOfType.values().stream().filter(e -> e.getDssSource().equals(dssSource)).findFirst().orElse(null);
        if (iDssRecordService != null){
            List<DssInfoDto> dssInfoDtos = iDssRecordService.selectDssRecordPage(page, queryWrapper);
            page.setRecords(dssInfoDtos);
        }
        return H_RestResultHelper.returnPage(page);
    }

    /**
     * 病害维修及时率统计
     * @param month 月份：2024-08
     * @return
     */
    @GetMapping("/getDssRepairTimeliness")
    public RestResult<DssRepairTimelinessDto> getDssRepairTimeliness(@RequestParam("month") String month) {
        return RestResult.success(dssInfoService.getRepairTimeliness(month));
    }

    /**
     * excel数据导出
     */
    @SneakyThrows
    @RequestMapping("exportExcel")
    public void exportExcel(HttpServletResponse response){
        RestResult<List<DssInfoDto>> result = selectDssPage();
        TemplateExportParams exportParams = new TemplateExportParams("excel/dssinfo.xlsx",true,null);
        Map<String, Object> dmFinspMap = Maps.newHashMap();
        result.getData().forEach(item->{
            if (item.getFoundDate() != null){
                String format = DateUtil.format(item.getFoundDate(), "yyyy-MM-dd");
                item.setFoundDateStr(format);
            }
        });
        H_ListSortHelper.sort(result.getData(),"foundDateStr",true);
        dmFinspMap.put("recList",result.getData());
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,dmFinspMap);
        H_ExcelDownloadHelper.downLoad("病害详情表.xls",response,workbook);
        workbook.close();
    }

    /**
     * 小修保养台账
     * @param response
     */
    @SneakyThrows
    @RequestMapping("exportXxWxExport")
    public void exportXxWxExport(HttpServletResponse response){
        RestResult<List<DssInfoDto>> result = selectDssPage();
        List<String> dssIds = result.getData().stream().map(DssInfoDto::getDssId).collect(Collectors.toList());
        Map<String, List<DssWxDto>> wxMap = dssInfoService.selectDssWxList(dssIds);
        List<DssInfoDto> resDtos = Lists.newArrayList();
        result.getData().forEach(item->{
            String dssSourceName = H_BasedataHepler.dssSourceMap.get(Convert.toStr(item.getDssSource(),"5"));
            item.setDssSourceName(dssSourceName);
            if (item.getFoundDate() != null){
                String format = DateUtil.format(item.getFoundDate(), "yyyy-MM-dd");
                item.setFoundDateStr(format);
            }
            List<DssWxDto> wxDtos = wxMap.get(item.getDssId());
            if (CollectionUtil.isNotEmpty(wxDtos)){
                wxDtos.forEach(row->{
                    DssInfoDto resRow = BeanUtil.toBean(item,DssInfoDto.class);
                    BeanUtil.copyProperties(row,resRow);
                    resDtos.add(resRow);
                });
            }else {
                resDtos.add(item);
            }
        });
        H_ListSortHelper.sort(resDtos,"foundDateStr",true);
        List<DssInfoDto> xxList = Lists.newArrayList();
        List<DssInfoDto> bjList = Lists.newArrayList();
        AtomicInteger xxIdx = new AtomicInteger(0);
        AtomicInteger bjIdx = new AtomicInteger(0);

        Set<Date> foundDateList = Sets.newHashSet();
        //分解小修部分及保洁部分
        resDtos.forEach(item->{
            if (item.getFoundDate() != null){
                foundDateList.add(item.getFoundDate());
            }
            String foundDateStr = DateUtil.format(item.getFoundDate(), "yyyy/MM/dd");
            String repairDateStr = DateUtil.format(item.getRepairDate(), "yyyy/MM/dd");
            String askComplDateStr = DateUtil.format(item.getAskedComplDate(), "yyyy/MM/dd");
            String acceptDateStr = DateUtil.format(item.getAcceptDate(), "yyyy/MM/dd");
            item.setRepairDateStr(repairDateStr)
                    .setAcceptDateStr(acceptDateStr)
                    .setAskedComplDateStr(askComplDateStr)
                    .setFoundDateStr(foundDateStr);
            if (item.getDssType().startsWith("BJ-")){
                item.setIndex(bjIdx.incrementAndGet());
                bjList.add(item);
            }else {
                item.setIndex(xxIdx.incrementAndGet());
                xxList.add(item);
            }
        });
        Date max = CollectionUtil.max(foundDateList);
        String yearMonth = null;
        if (max != null){
            int year = DateUtil.date(max).year();
            int month = DateUtil.date(max).month() + 1;
            yearMonth=year+"年"+month+"月";
        }

        TemplateExportParams exportParams = new TemplateExportParams("excel/dssWx.xlsx",true);
        Map<String, Object> xxMap = Maps.newHashMap();
        xxMap.put("recList",xxList);
        xxMap.put("yearMonth",yearMonth);
        Map<String, Object> bjMap = Maps.newHashMap();
        bjMap.put("recList",bjList);
        bjMap.put("yearMonth",yearMonth);
        Map<Integer,Map<String,Object>> excelMap = Maps.newHashMap();
        excelMap.put(0,xxMap);
        excelMap.put(1,bjMap);
        Workbook workbook = ExcelExportUtil.exportExcel(excelMap,exportParams);
        H_ExcelDownloadHelper.downLoad("小修保养台账.xls",response,workbook);
        workbook.close();
    }



    /**
     * 病害管理分页查询
     * @return
     */
    @PostMapping("selectPage")
    public RestResult<List<DssInfoDto>> selectDssPage(){
        Page page = getPage();
        Map reqParam = getReqParam();
        QueryWrapper<DssInfoDto> queryWrapper = setWrapper(reqParam,true);
        String orderSql = getOrderSql(queryWrapper);
        List<DssInfoDto> dssInfoDtos = dssInfoService.selectDssPage(page, queryWrapper,orderSql);
        page.setRecords(dssInfoDtos);
        return H_RestResultHelper.returnPage(page);
    }

    //对排序字段进行处理
    private void filterSortField(Map reqParam,QueryWrapper queryWrapper){
        String sortField = (String) reqParam.getOrDefault("sortField", null);
        if (StrUtil.isBlank(sortField)){
            return;
        }
        String sortOrder = (String) reqParam.get("sortOrder");
        //如果排序字段为lineAllname时，需要替换成main_road_id
        if (sortField.contains("lineAllname")){
            sortField = sortField.replace("lineAllname", "mainRoadId");
        }
        //如果排序字段为oprtOrgName时，需要替换成orgId
        if (sortField.contains("oprtOrgName")){
            sortField = sortField.replace("oprtOrgName", "orgId");
        }
        //病害位置排序时，替换成桩号进行排序
        if (sortField.contains("dssPosition")){
            sortField = sortField.replace("dssPosition", "rl_stake_new");
        }
        H_BatisQuery.setWrapperSortParam(queryWrapper,sortField,sortOrder);
    }

    /**
     * 病害明细参数设置
     * @param queryWrapper
     * @param reqParam
     */
    private void setDssRecordWrapper(QueryWrapper queryWrapper,Map reqParam){
        Set<String> orgIds = H_DataAuthHelper.selectOrgIds();
        queryWrapper.in("t.org_id",orgIds);

        //如果不是三级单位系统管理员，只能查看自己下单的数据
        if (!H_DataAuthHelper.isSystemManager()){
            queryWrapper.eq("t.create_user_id", CustomRequestContextHolder.getUserCode());
        }
        queryWrapper.orderByDesc("FOUND_DATE");
    }

    /**
     * 设置查询参数
     * @param reqParam 系统参数
     * @param isDssDatabase 是否病害库  true:查询dss_info表，false:查询日常巡查、经常检查、定检的原始的病害明细
     * @return
     */
    private QueryWrapper setWrapper(Map reqParam,boolean isDssDatabase){
        QueryWrapper<DssInfoDto> queryWrapper = new QueryWrapper();

        //发现日期
        Object startDate = reqParam.get("startDate");
        if (!StrUtil.isBlankIfStr(startDate)) {
            queryWrapper.apply("found_date >= to_date({0},'YYYY-MM-dd')", startDate);
            reqParam.remove("startDate");
        }

        Object endDate = reqParam.get("endDate");
        if (!StrUtil.isBlankIfStr(endDate)) {
            queryWrapper.apply("found_date <= to_date({0},'YYYY-MM-dd hh24:mi:ss')", endDate + " 23:59:59");
            reqParam.remove("endDate");
        }

        //dealStatus=2时，表示查询全部
        Object dealStatus = reqParam.get("dealStatus");
        if (!StrUtil.isBlankIfStr(dealStatus) && dealStatus.equals("2")){
            reqParam.remove("dealStatus");
        }

        //病害名称模糊查询
        Object dssTypeName = reqParam.get("dssTypeName");
        if (!StrUtil.isBlankIfStr(dssTypeName)) {
            queryWrapper.like("dt.dss_type_name", dssTypeName);
        }

        if (isDssDatabase){
            Object linedirect = reqParam.get("lineDirect");
            if (!StrUtil.isBlankIfStr(linedirect)) {
                reqParam.put("linedirect", linedirect);
            }

            //桩号范围
            Object startStake = reqParam.get("startStake");
            if (!StrUtil.isBlankIfStr(startStake)) {
                queryWrapper.ge("t.rl_stake_new", startStake);
                reqParam.remove("startStake");
            }

            Object endStake = reqParam.get("endStake");
            if (!StrUtil.isBlankIfStr(endStake)) {
                queryWrapper.le("t.rl_stake_new", endStake);
                reqParam.remove("endStake");
            }

            Object lineId = reqParam.get("lineId");
            if (!StrUtil.isBlankIfStr(lineId)) {
                reqParam.put("mainRoadId", lineId);
            }

            //裂缝宽度
            Object startWidth = reqParam.get("startWidth");
            if (!StrUtil.isBlankIfStr(startWidth)) {
                queryWrapper.ge("t.dss_w", startWidth);
                reqParam.remove("startWidth");
            }

            Object endWidth = reqParam.get("endWidth");
            if (!StrUtil.isBlankIfStr(endWidth)) {
                queryWrapper.le("t.dss_w", endWidth);
                reqParam.remove("endWidth");
            }
            Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
            //设置权限
            queryWrapper.in("t.routecode", routeCodes);
            filterSortField(reqParam,queryWrapper);
        }

        //病害来源
        Object dssSource = reqParam.get("dssSource");
        if (ObjectUtil.isNotEmpty(dssSource) ){
            //如果数据来源选择6、7时，
            if (StrUtil.containsAny(dssSource.toString(),"6","7")) {
                String tempDssSource = StrUtil.replace(dssSource.toString(), "6", "1").replace("7", "1");
                reqParam.put("dssSource", tempDssSource);
            }
            int xcType = -1;
            //养护单位巡查
            if (dssSource.equals("6")){
                xcType = 1;
            }
            //路段单位巡查
            if (dssSource.equals("7")){
                xcType = 0;
            }
            // 如果病害来源为单项，才进行日常巡查过滤
            if (xcType != -1){
                queryWrapper.exists("select 1 from DM_DINSP where DINSP_ID = t.REL_TASK_CODE and XC_TYPE="+xcType);
            }
        }
        H_BatisQuery.setFieldValue2In(queryWrapper,reqParam,DssInfoDto.class,"t",null);
        return queryWrapper;
    }


    /**
     * 获取排序sql
     * @param queryWrapper
     * @return
     */
    private String getOrderSql(QueryWrapper queryWrapper){
        MergeSegments expression = queryWrapper.getExpression();
        StringBuffer orderSql = new StringBuffer();
        expression.getOrderBy().forEach(item->{
            orderSql.append(" ").append(item.getSqlSegment());
        });

        if (StrUtil.isNotBlank(orderSql.toString())){
            return " order by "+orderSql.toString();
        }
        //return " order by v.routecode,v.main_road_id,v.linedirect,v.rl_stake_new asc";
        return null;
    }
}
