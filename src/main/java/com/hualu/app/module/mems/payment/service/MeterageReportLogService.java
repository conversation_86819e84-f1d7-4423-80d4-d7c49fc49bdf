package com.hualu.app.module.mems.payment.service;

import com.hualu.app.module.mems.autoInspector.entity.DownloadVo;
import com.hualu.app.module.mems.payment.entity.MeterageReportLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mongo.dto.MgFileDto;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【METERAGE_REPORT_LOG(上传报表日志)】的数据库操作Service
* @createDate 2025-06-19 08:09:51
*/
public interface MeterageReportLogService extends IService<MeterageReportLog> {

    void saveLog(MgFileDto mgFileDto,String contrId);

    List<DownloadVo> getDssImageList(String code);
}
