package com.hualu.app.module.mems.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccpt;

import java.util.List;

/**
 * <p>
 * 维修验收单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskAccptService extends IService<DmTaskAccpt> {


    /**
     * 是否为清障项目
     * @param mtaskAccptId
     * @return
     */
    boolean isClear(String mtaskAccptId);

    /**
     * 删除验收单
     * @param mtaskAccptId
     */
    void delAccpt(String mtaskAccptId);
    /**
     * 添加或修改验收单
     * @param dmTaskAccpt
     */
    void saveOrUpdateAccpt(DmTaskAccpt dmTaskAccpt);

    /**
     * 查询验收单明细数据
     * @param mtaskId
     * @param mtaskdtlIds
     * @return
     */
    List<DmTaskAccptDetailDto> selectAccptDetails(String mtaskId, String mtaskdtlIds);

    /**
     * 获取下一张单
     * @return
     */
    DmTaskAccpt getNextCode();
}
