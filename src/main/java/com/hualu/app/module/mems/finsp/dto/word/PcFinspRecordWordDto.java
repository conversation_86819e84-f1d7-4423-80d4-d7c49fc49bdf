package com.hualu.app.module.mems.finsp.dto.word;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class PcFinspRecordWordDto implements Serializable {

    //主单ID
    private String finspId;

    //巡查部位
    private String inspCom;

    //巡查内容
    private String inspCont;

    // 检查结果 0：否  1：是
    private String finspResult;

    //病害描述
    private String finspDesc=" ";

    //是
    private String yesResult;

    //否
    private String noResult;

    public void setFinspResult(String finspResult) {
        this.finspResult = finspResult;
        this.setYesResult(finspResult.equals("1")?"√":" ");
        this.setNoResult(finspResult.equals("1")?" ":"√");
    }
}
