package com.hualu.app.module.mems.autoInspector.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eos.workflow.api.BPSServiceClientFactory;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.mems.autoInspector.dto.ConvertInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayOrgRef;
import com.hualu.app.module.mems.autoInspector.entity.HighwaySegment;
import com.hualu.app.module.mems.autoInspector.entity.HighwayUserConfig;
import com.hualu.app.module.mems.autoInspector.entity.NmFinspVo;
import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectMedia;
import com.hualu.app.module.mems.autoInspector.service.*;
import com.hualu.app.module.mems.autoInspector.utils.DefectToFinspConverter;
import com.hualu.app.module.mems.autoInspector.utils.HttpForAutoUtils;
import com.hualu.app.module.mems.autoInspector.utils.ImageAnnotationUtil;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_KeyWorker;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.mock.web.MockMultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 病害信息转巡查记录服务实现类
 * <p>
 * 提供病害信息转换为巡查记录的服务实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
@Service
public class DefectToFinspServiceImpl implements DefectToFinspService {

    @Autowired
    private HighwayDefectInfoService highwayDefectInfoService;

    @Autowired
    private NmFinspService nmFinspService;

    @Autowired
    private BaseLineService baseLineService;

    @Autowired
    private HighwayOrgRefService orgRefService;

    @Autowired
    private HighwaySegmentService highwaySegmentService;

    @Autowired
    private HighwayUserConfigService highwayUserConfigService;

    @Autowired
    private HighwayDefectMediaService mediaService;

    @Autowired
    private NmFinspRecordService nmFinspRecordService;

    @Autowired
    private NmInspContentService nmInspContentService;

    @Autowired
    private DssImageService dssImageService;

    @Autowired
    private HighwayDefectInfoService defectInfoService;

    // 路段缓存，提高性能
    private Map<String, HighwaySegment> segmentCache;

    private static String processDefName="gdcg.emdc.mems.dm.NFinspWorkFlow";

    /**
     * 将URL图片列表转换为MultipartFile数组
     * @param imageUrls 图片URL列表
     * @return MultipartFile数组
     */
    @SneakyThrows
    private MultipartFile[] convertUrlsToMultipartFiles(List<String> imageUrls) {
        if (imageUrls == null || imageUrls.isEmpty()) {
            return new MultipartFile[0];
        }

        List<MultipartFile> multipartFiles = new ArrayList<>();
        for (String imageUrl : imageUrls) {
            if (StrUtil.isBlank(imageUrl)) {
                continue;
            }

            try {
                URL url = new URL(imageUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                connection.setReadTimeout(5000);
                connection.connect();

                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                    // 从URL获取文件名
                    String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
                    if (fileName.contains("?")) {
                        fileName = fileName.substring(0, fileName.indexOf("?"));
                    }

                    // 读取图片数据
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    try (InputStream inputStream = connection.getInputStream()) {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }
                    }

                    byte[] imageData = outputStream.toByteArray();

                    // 创建MultipartFile
                    MultipartFile multipartFile = new MockMultipartFile(
                            fileName,
                            fileName,
                            connection.getContentType(),
                            imageData
                    );

                    multipartFiles.add(multipartFile);
                }
            } catch (Exception e) {
                log.error("转换URL到MultipartFile失败: {}", imageUrl, e);
            }
        }

        return multipartFiles.toArray(new MultipartFile[0]);
    }

    /**
     * 将本地文件路径列表转换为MultipartFile数组
     * @param localPaths 本地文件路径列表
     * @return MultipartFile数组
     */
    private MultipartFile[] convertLocalPathsToMultipartFiles(List<String> localPaths) {
        if (localPaths == null || localPaths.isEmpty()) {
            return new MultipartFile[0];
        }

        List<MultipartFile> files = new ArrayList<>();

        for (String localPath : localPaths) {
            try {
                File file = new File(localPath);
                if (file.exists() && file.isFile()) {
                    // 创建MultipartFile对象
                    MultipartFile multipartFile = new MockMultipartFile(
                            file.getName(),
                            file.getName(),
                            Files.probeContentType(file.toPath()),
                            Files.readAllBytes(file.toPath())
                    );
                    files.add(multipartFile);
                } else {
                    log.warn("文件不存在或不是文件: {}", localPath);
                }
            } catch (Exception e) {
                log.error("转换文件失败: {}", localPath, e);
            }
        }

        return files.toArray(new MultipartFile[0]);
    }

    /**
     * 将病害媒体列表转换为MultipartFile数组（带标注处理）
     * @param medias 病害媒体列表
     * @param defectInfo 病害信息
     * @return MultipartFile数组
     */
    private MultipartFile[] convertLocalPathsToMultipartFilesWithAnnotation(List<HighwayDefectMedia> medias, HighwayDefectInfo defectInfo) {
        if (medias == null || medias.isEmpty()) {
            return new MultipartFile[0];
        }

        List<MultipartFile> files = new ArrayList<>();

        for (HighwayDefectMedia media : medias) {
            try {
                String localPath = media.getLocalPath();
                if (localPath == null) {
                    continue;
                }

                File originalFile = new File(localPath);
                if (originalFile.exists() && originalFile.isFile()) {
                    // 创建带标注的临时文件
                    File annotatedFile = createAnnotatedImage(originalFile, media, defectInfo);

                    // 创建MultipartFile对象
                    MultipartFile multipartFile = new MockMultipartFile(
                            originalFile.getName(),
                            originalFile.getName(),
                            Files.probeContentType(originalFile.toPath()),
                            Files.readAllBytes(annotatedFile.toPath())
                    );
                    files.add(multipartFile);

                    // 清理临时文件
                    if (annotatedFile.exists() && !annotatedFile.equals(originalFile)) {
                        annotatedFile.delete();
                    }
                } else {
                    log.warn("文件不存在或不是文件: {}", localPath);
                }
            } catch (Exception e) {
                log.error("转换文件失败: {}", media.getLocalPath(), e);
            }
        }

        return files.toArray(new MultipartFile[0]);
    }

    /**
     * 为图片添加标注
     * @param originalFile 原始图片文件
     * @param media 媒体信息（包含rect标注框信息）
     * @param defectInfo 病害信息（包含病害类型名称）
     * @return 带标注的图片文件
     */
    private File createAnnotatedImage(File originalFile, HighwayDefectMedia media, HighwayDefectInfo defectInfo) {
        try {
            // 检查文件是否为图片格式
            String fileName = originalFile.getName().toLowerCase();
            if (!isImageFile(fileName)) {
                log.info("文件不是图片格式，跳过标注处理: {}", fileName);
                return originalFile;
            }

            // 创建临时文件用于保存带标注的图片
            String tempDir = System.getProperty("java.io.tmpdir");
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));
            File annotatedFile = new File(tempDir, "annotated_" + System.currentTimeMillis() + fileExtension);

            // 解析rect字段获取标注框位置
            String rect = media.getRect();
            if (rect == null || rect.trim().isEmpty()) {
                log.warn("媒体[{}]的rect字段为空，跳过标注处理", media.getId());
                return originalFile;
            }

            // 解析rect字段，格式如：682,558,268,181 (x,y,width,height)
            String[] rectParts = rect.split(",");
            if (rectParts.length != 4) {
                log.warn("媒体[{}]的rect字段格式不正确: {}", media.getId(), rect);
                return originalFile;
            }

            int x = Integer.parseInt(rectParts[0].trim());
            int y = Integer.parseInt(rectParts[1].trim());
            int width = Integer.parseInt(rectParts[2].trim());
            int height = Integer.parseInt(rectParts[3].trim());

            // 获取标注文字（使用病害类型名称）
            String annotationText = defectInfo.getDssTypeName();
            if (annotationText == null || annotationText.trim().isEmpty()) {
                annotationText = "病害"; // 默认文字
            }

            // 使用ImageAnnotationUtil添加标注
            ImageAnnotationUtil.annotateImage(
                    originalFile.getAbsolutePath(),
                    annotatedFile.getAbsolutePath(),
                    x, // x坐标
                    y, // y坐标
                    width, // 宽度
                    height, // 高度
                    annotationText, // 标注文字
                    java.awt.Color.RED, // 边框颜色
                    java.awt.Color.WHITE, // 文字颜色
                    14, // 字体大小
                    2, // 边框粗细
                    true // 显示文字背景
            );

            log.info("成功为图片添加标注: {} -> {}, 标注框: {}, 文字: {}",
                    originalFile.getName(), annotatedFile.getName(), rect, annotationText);
            return annotatedFile;
        } catch (Exception e) {
            log.error("添加标注失败，使用原始文件: {}", originalFile.getName(), e);
            return originalFile;
        }
    }

    /**
     * 检查文件是否为图片格式
     * @param fileName 文件名
     * @return 是否为图片格式
     */
    private boolean isImageFile(String fileName) {
        String[] imageExtensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"};
        for (String ext : imageExtensions) {
            if (fileName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }

    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public List<NmFinspVo> batchConvert(List<String> defectIds) {
        // 查询病害信息
        List<HighwayDefectInfo> defectInfoList = new ArrayList<>(highwayDefectInfoService.listByIds(defectIds));
        if (CollectionUtil.isEmpty(defectInfoList)) {
            return new ArrayList<>();
        }

        // 预加载所有路段数据
        loadAllSegments();

        // 获取路面类型的NmInspContent列表
        List<NmInspContent> nmInspContents = nmInspContentService.list(
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<NmInspContent>()
                        .eq("FACILITY_CAT", "LM")
        );

        // 按路线分组
        Map<String, List<HighwayDefectInfo>> lineGroupedDefects = defectInfoList.stream()
                .collect(Collectors.groupingBy(this::extractLineCode));

        // 获取路线信息和管养单位信息
        Map<String, BaseLine> lineMap = baseLineService.getAllLineMap();
        List<HighwayOrgRef> orgRefs = orgRefService.list();

        // 为每个路线创建巡查单，并添加病害记录
        List<NmFinspVo> nmFinspVoList = new ArrayList<>();
        for (Map.Entry<String, List<HighwayDefectInfo>> entry : lineGroupedDefects.entrySet()) {
            String lineCode = entry.getKey();
            List<HighwayDefectInfo> lineDefects = entry.getValue();

            // 创建巡查单
            NmFinsp nmFinsp = createFinspForDefects(lineDefects.get(0), lineMap, orgRefs);

            // 批量查询和处理病害相关图片，减少数据库访问
            List<String> currentDefectIds = lineDefects.stream()
                    .map(HighwayDefectInfo::getId)
                    .collect(Collectors.toList());

            // 一次性查询所有相关的病害媒体
            LambdaQueryWrapper<HighwayDefectMedia> mediaQuery = new LambdaQueryWrapper<>();
            mediaQuery.in(HighwayDefectMedia::getDefectId, currentDefectIds);
            List<HighwayDefectMedia> allMedias = mediaService.list(mediaQuery);

            // 按病害ID分组
            Map<String, List<HighwayDefectMedia>> mediaMap = allMedias.stream()
                    .collect(Collectors.groupingBy(HighwayDefectMedia::getDefectId));

            // 为每个病害创建巡查记录项
            List<NmFinspRecord> recordList = new ArrayList<>();
            List<NmFinspRecord> recordsToSave = new ArrayList<>();

            for (HighwayDefectInfo defect : lineDefects) {
                NmFinspRecord record = convertToFinspRecord(defect, nmFinsp.getFinspId());

                // 根据dssType匹配NmInspContent
                if (defect.getYhDssType() != null) {
                    // 在NmInspContent中查找匹配的记录
                    Optional<NmInspContent> matchedContent = nmInspContents.stream()
                            .filter(content -> defect.getYhDssType().equals(content.getDssType()))
                            .findFirst();

                    if (matchedContent.isPresent()) {
                        // 如果找到匹配的记录，设置structPartId
                        record.setStructPartId(matchedContent.get().getPid());
                        log.debug("为病害[{}]找到匹配的NmInspContent，设置structPartId为{}",
                                defect.getId(), matchedContent.get().getPid());
                    } else {
                        log.warn("未找到与病害[{}]的dssType[{}]匹配的NmInspContent记录",
                                defect.getId(), defect.getYhDssType());
                    }
                }

                // 从内存中获取该病害相关的媒体
                List<HighwayDefectMedia> medias = mediaMap.getOrDefault(defect.getId(), Collections.emptyList());

                // 使用带标注的转换方法
                MultipartFile[] files = convertLocalPathsToMultipartFilesWithAnnotation(medias, defect);

                // 处理文件上传
                List<String> fileIds = HttpForAutoUtils.uploadImage(files);
                dssImageService.saveDssImageForPC(record.getDssId(),fileIds.stream().filter(Objects::nonNull).collect(Collectors.joining(",")),1);

                // 添加到批量保存列表，而不是立即保存
                recordsToSave.add(record);
                recordList.add(record);
                defect.setIsCreated(1);

                log.info("病害[{}]转为巡查记录项，处理了{}张图片", defect.getId(), medias.size());
            }

            // 批量保存记录
            if (!recordsToSave.isEmpty()) {
                // 使用NmFinspRecordService进行批量保存
                nmFinspRecordService.saveBatch(recordsToSave);
                H_StructHelper.refreshNmFinspResult(nmFinsp);
                log.info("批量保存了{}条巡查记录", recordsToSave.size());
            }

            // 批量更新病害状态
            if (!lineDefects.isEmpty()) {
                highwayDefectInfoService.updateBatchById(lineDefects);
            }

            nmFinsp.setDssNum(recordList.size());
            nmFinspService.updateById(nmFinsp);

            // 创建NmFinspVo并设置记录
            NmFinspVo nmFinspVo = new NmFinspVo();
            BeanUtils.copyProperties(nmFinsp, nmFinspVo);
            nmFinspVo.setRecords(recordList);

            nmFinspVoList.add(nmFinspVo);

            log.info("为路线[{}]创建了巡查单，id为{}，包含{}条病害记录", lineCode, nmFinsp.getFinspId(), recordList.size());
        }

        log.info("病害批量转换成功：共{}个巡查单", nmFinspVoList.size());
        return nmFinspVoList;
    }

    @Override
    public List<NmFinspVo> batchConvert(ConvertInfo convertInfo) {
        // 检查输入参数
        if (convertInfo == null || convertInfo.getStartDate() == null || convertInfo.getEndDate() == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        QueryWrapper<HighwayDefectInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.between("to_char(CREATED_TIME,'yyyy-MM-dd')", convertInfo.getStartDate(), convertInfo.getEndDate());
        LambdaQueryWrapper<HighwayDefectInfo> lambda = queryWrapper.lambda();
        lambda.eq(HighwayDefectInfo::getIsCreated, 0);
        lambda.in(HighwayDefectInfo::getSegmentName,convertInfo.getRoutes());
        lambda.notLike(HighwayDefectInfo::getDssTypeName,"%修补%");
        List<String> ids = new ArrayList<>();
        defectInfoService.list(lambda).forEach(defect -> {
            ids.add(defect.getId());
        });
        // 分批处理（每1000个ID一组）
        final int BATCH_SIZE = 1000;
        List<NmFinspVo> allResults = new ArrayList<>();

        for (int i = 0; i < ids.size(); i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, ids.size());
            List<String> batchIds = ids.subList(i, end);

            // 处理当前批次并合并结果
            List<NmFinspVo> batchResult = batchConvert(batchIds);
            allResults.addAll(batchResult);
        }
        return allResults;
    }

    /**
     * 加载所有路段数据到缓存
     */
    private void loadAllSegments() {
        if (segmentCache == null || segmentCache.isEmpty()) {
            synchronized (this) {
                if (segmentCache == null) {
                    segmentCache = new ConcurrentHashMap<>();
                }
                // 加载所有路段数据
                List<HighwaySegment> allSegments = highwaySegmentService.list();
                if (!CollectionUtils.isEmpty(allSegments)) {
                    for (HighwaySegment segment : allSegments) {
                        segmentCache.put(segment.getId(), segment);
                    }
                    log.info("已加载{}条路段数据到缓存", allSegments.size());
                }
            }
        }
    }

    @Override
    public String extractLineCode(HighwayDefectInfo defectInfo) {
        // 优先使用路段ID直接从路段表查询
        if (defectInfo.getSegmentId() != null && !defectInfo.getSegmentId().trim().isEmpty()) {
            String segmentId = defectInfo.getSegmentId();

            // 从缓存中获取路段信息
            HighwaySegment segment = segmentCache.get(segmentId);
            if (segment != null) {
                // 从路段名称或路段编码中提取路线信息
                if (segment.getSegcode() != null && !segment.getSegcode().trim().isEmpty()) {
                    return segment.getSegcode();
                } else if (segment.getSegname() != null) {
                    // 从路段名称中提取路线代码
                    String segmentName = segment.getSegname();
                    if (segmentName.matches(".*[GS]\\d+.*")) {
                        Matcher matcher = Pattern.compile("([GS]\\d+)").matcher(segmentName);
                        if (matcher.find()) {
                            return matcher.group(1);
                        }
                    }
                    // 使用路段名称的哈希作为路线代码
                    return "SEG_" + segmentName.hashCode();
                }
                // 如果无法提取，使用路段ID作为路线代码
                return "SEG_" + segmentId;
            }
        }

        // 如果无法从路段表获取信息，则使用原有的提取逻辑
        if (defectInfo.getSegmentName() != null) {
            String segmentName = defectInfo.getSegmentName();
            // 尝试提取路线名称和编号（根据实际命名规则调整）
            if (segmentName.matches(".*[GS]\\d+.*")) {                   // 国道/省道编号识别
                // 例如从"G15沈海高速温岭段"提取"G15"作为路线编码
                Matcher matcher = Pattern.compile("([GS]\\d+)").matcher(segmentName);
                if (matcher.find()) {
                    String lineCode = matcher.group(1);                  // 例如"G15"
                    return lineCode;
                }
            } else {
                // 没有明确编码，使用路段名称的前几个字符作为区分
                return "SEG_" + segmentName.hashCode();
            }
        }

        // 最后使用病害ID作为分组标识
        return "UNKNOWN_" + defectInfo.getId();
    }

    @SneakyThrows
    @Override
    public NmFinsp createFinspForDefects(HighwayDefectInfo defectInfo, Map<String, BaseLine> lineMap, List<HighwayOrgRef> orgRefs) {
        // 使用DefectToFinspConverter的基本转换功能，但创建不带记录项的巡查单
        NmFinsp nmFinsp = DefectToFinspConverter.convertToNmFinsp(defectInfo, segmentCache);

        // 从路段名称设置路线信息
        setLineInfoFromSegment(nmFinsp, defectInfo);

        // 设置巡查类型
        nmFinsp.setXcType(2);

        // 设置路线名称（如果lineMap中有对应信息）
        if (nmFinsp.getLineCode() != null && lineMap.containsKey(nmFinsp.getLineCode())) {
            nmFinsp.setLineName(lineMap.get(nmFinsp.getLineCode()).getLineSname());
        }

        // 设置管养单位信息
        setMaintenanceOrg(nmFinsp, defectInfo, orgRefs);
        StringUtil.checkCode(nmFinsp.getFinspCode(),4);
        if (nmFinsp.getProcessinstid() == null || nmFinsp.getProcessinstid() == 0) {
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "经常检查", "");
            log.info("经常检查实例ID："+processInstId);
            nmFinsp.setProcessinstid(processInstId);
            nmFinsp.setStatus(0);
        }
        nmFinsp.setFinspId(H_KeyWorker.nextIdToString());
        nmFinspService.saveOrUpdate(nmFinsp);
        log.debug("根据病害[{}]创建了巡查单", defectInfo.getId());
        return nmFinsp;
    }

    @Override
    public void setLineInfoFromSegment(NmFinsp nmFinsp, HighwayDefectInfo defectInfo) {
        // 优先从路段缓存中获取路段信息
        if (defectInfo.getSegmentId() != null && !defectInfo.getSegmentId().trim().isEmpty()) {
            HighwaySegment segment = segmentCache.get(defectInfo.getSegmentId());
            if (segment != null) {
                // 如果有路段编码，直接设置为路线编码
                if (segment.getSegcode() != null && !segment.getSegcode().trim().isEmpty()) {
                    nmFinsp.setLineCode(segment.getSegcode());
                    log.debug("从路段缓存中获取路线编码：{}", segment.getSegcode());
                }

                // 设置路线名称
                if (segment.getSegname() != null && !segment.getSegname().trim().isEmpty()) {
                    nmFinsp.setLineName(segment.getSegname());
                    log.debug("从路段缓存中获取路线名称：{}", segment.getSegname());
                    return;
                }
            }
        }

        // 如果缓存中没有相关信息，则从路段名称中提取
        String segmentName = defectInfo.getSegmentName();
        if (segmentName != null && !segmentName.trim().isEmpty()) {
            // 尝试提取标准道路编号（如G15、S30等）
            Pattern lineCodePattern = Pattern.compile("([GS]\\d+)");
            Matcher matcher = lineCodePattern.matcher(segmentName);
            if (matcher.find()) {
                String lineCode = matcher.group(1);
                // 如果没有通过缓存设置路线编码，才通过解析设置
                if (nmFinsp.getLineCode() == null) {
                    nmFinsp.setLineCode(lineCode);
                    log.debug("从路段名称中解析路线编码：{}", lineCode);
                }

                // 如果没有通过缓存设置路线名称，才通过解析设置
                if (nmFinsp.getLineName() == null) {
                    // 尝试提取完整道路名称（如G15沈海高速）
                    int endPos = segmentName.indexOf("段");
                    if (endPos > 0) {
                        String fullLineName = segmentName.substring(0, endPos);
                        nmFinsp.setLineName(fullLineName);
                        log.debug("从路段名称中解析路线名称：{}", fullLineName);
                    } else {
                        nmFinsp.setLineName(segmentName);
                        log.debug("使用路段名称作为路线名称：{}", segmentName);
                    }
                }
            } else if (nmFinsp.getLineName() == null) {
                // 没有标准编号，使用全名作为路线名称
                nmFinsp.setLineName(segmentName);
                log.debug("使用路段名称作为路线名称：{}", segmentName);
            }
        }
    }

    @Override
    public NmFinspRecord convertToFinspRecord(HighwayDefectInfo defectInfo, String finspId) {
        NmFinspRecord record = new NmFinspRecord();

        // 设置基本信息
        record.setDssId(H_KeyWorker.nextIdToString());
        record.setHisDssId(defectInfo.getId());
        record.setFinspId(finspId);
        String direction = defectInfo.getInspectDirection();
        record.setLineDirect("1".equals(direction) ? "2" : "0".equals(direction) ? "1" : direction);
        record.setStartStakeNum(stakeToKilometer(defectInfo.getStakeStart()));
        record.setEndStakeNum(stakeToKilometer(defectInfo.getStakeEnd()));
        record.setStake(stakeToKilometer(defectInfo.getStakeStart()));
        record.setDssType(defectInfo.getYhDssType());
        record.setDssTypeName(defectInfo.getDssTypeName());
        record.setFacilityCat("LM");
        record.setLane(convertLaneNumberToLetter(defectInfo.getLaneNum(),direction));
        record.setDssPosition(defectInfo.getStakeStart()+"~"+defectInfo.getStakeEnd()+","+convertLaneNumberToLetter(defectInfo.getLaneNum(),direction));
        record.setDssL(defectInfo.getTargetLen().doubleValue());
        record.setDssLUnit("m");
        record.setDssA(defectInfo.getTargetArea().doubleValue());
        record.setDssAUnit("m2");
        record.setX(defectInfo.getLongitude().doubleValue());
        record.setY(defectInfo.getLatitude().doubleValue());
        record.setSource("2");
        String pavementType = "LQ";
        if("水泥路面".equals(defectInfo.getTypeName())){
            pavementType = "SN";
        }
        record.setPavementType(pavementType);
        record.setDelFlag(0);
        record.setIssueType("2");
        record.setIsphone(2);

        // 设置时间信息
        record.setCreateTime(LocalDateTime.now());
        record.setUpdateTime(LocalDateTime.now());

        // 设置病害发现日期（已经是LocalDateTime类型，直接赋值）
        record.setFoundDate(defectInfo.getCreatedTime());

        log.debug("将病害[{}]转换为巡查记录项[{}]", defectInfo.getId(), record.getDssId());
        return record;
    }

    @Override
    public void setMaintenanceOrg(NmFinsp nmFinsp, HighwayDefectInfo defectInfo, List<HighwayOrgRef> orgRefs) {
        if (CollectionUtils.isEmpty(orgRefs)) {
            log.warn("未获取到管养单位信息");
            return;
        }

        String segmentId = defectInfo.getSegmentId();
        if (segmentId == null || segmentId.trim().isEmpty()) {
            log.warn("病害信息中路段ID为空，无法设置管养单位信息");
            return;
        }

        // 根据路段ID查找对应的组织机构
        for (HighwayOrgRef orgRef : orgRefs) {
            if (segmentId.equals(orgRef.getRouteId())) {
                // 找到匹配的管养单位，设置相关信息
                nmFinsp.setMntOrgId(orgRef.getYhOrgCode());
                nmFinsp.setMntOrgNm(orgRef.getYhOrgName());
                nmFinsp.setRouteName(orgRef.getYhRouteName());

                // 处理yh_route_code格式为"1|234,2|235"的数据
                String yhRouteCode = orgRef.getYhRouteCode();
                if (yhRouteCode != null && !yhRouteCode.trim().isEmpty()) {
                    // 如果包含逗号，只取第一部分
                    int commaIndex = yhRouteCode.indexOf(",");
                    if (commaIndex > 0) {
                        yhRouteCode = yhRouteCode.substring(0, commaIndex);
                    }

                    // 获取|后面的部分作为实际的路线编码
                    int pipeIndex = yhRouteCode.indexOf("|");
                    if (pipeIndex > 0) {
                        yhRouteCode = yhRouteCode.substring(pipeIndex + 1);
                    }
                }

                nmFinsp.setRouteCode(yhRouteCode);

                // 根据路段ID和巡查类型(1-经常性检查)查询用户配置
                LambdaQueryWrapper<HighwayUserConfig> queryWrapper = new LambdaQueryWrapper<>();
//                queryWrapper.eq(HighwayUserConfig::getRouteId, segmentId)
//                        .eq(HighwayUserConfig::getInspType, 2)
//                        .last("AND ROWNUM <= 1"); // Oracle中获取第一条记录
                queryWrapper.eq(HighwayUserConfig::getInspType, 2)
                        .last("AND ROWNUM <= 1"); // Oracle中获取第一条记录

                HighwayUserConfig userConfig = highwayUserConfigService.getOne(queryWrapper);
                if (userConfig != null) {
                    FwRightUserService bean = CustomApplicationContextHolder.getBean(FwRightUserService.class);
                    UserLoginDto user = bean.loginUser(userConfig.getUserCode());
                    BPSServiceClientFactory.getLoginManager().setCurrentUser(user.getUserCode(), user.getUserName());
                    nmFinsp.setFinspCode(getNextCode("LM",orgRef.getOrgEn(), LocalDate.now()));
                    // 设置创建用户
                    nmFinsp.setCreateUserId(userConfig.getUserId());
                    nmFinsp.setUpdateUserId(userConfig.getUserId());
                    // 设置巡查人
                    nmFinsp.setInspPerson(userConfig.getUserName());
                    // 设置部门名称
                    nmFinsp.setSearchDept(userConfig.getDepartName());
                    log.debug("为经常巡查记录设置用户信息：创建用户ID={}, 巡查人={}, 部门={}",
                            userConfig.getUserId(), userConfig.getUserName(), userConfig.getDepartName());
                } else {
                    log.warn("未找到与路段ID[{}]和巡查类型[{}]匹配的用户配置信息", segmentId, 1);
                }
                log.debug("为经常巡查记录设置管养单位信息：机构ID={}, 机构名称={}, 路线名称={}, 路线编码={}",
                        nmFinsp.getMntOrgId(),
                        nmFinsp.getMntOrgNm(), nmFinsp.getRouteName(), nmFinsp.getRouteCode());
                return;
            }
        }

        // 未找到匹配的管养单位
        log.warn("未找到与路段ID[{}]匹配的管养单位信息", segmentId);
    }

    @Override
    public Double stakeToKilometer(String stake) {
        if (stake == null || stake.trim().isEmpty()) {
            return null;
        }

        // 如果已经是数字，检查是否为公里单位，不是则转换
        if (NumberUtil.isNumber(stake)) {
            Double value = Double.valueOf(stake);
            // 如果大于1000，可能是以米为单位，需要除以1000转成公里
            if (value > 1000) {
                return value / 1000;
            }
            return value; // 已经是公里单位
        }

        // 处理K100+500这种标准桩号格式
        if (stake.toUpperCase().startsWith("K")) {
            try {
                // 去掉K前缀
                String temp = stake.trim().toUpperCase().replaceAll("K", "");
                // 按+分割
                String[] parts = temp.split("\\+");

                // 提取公里部分
                Double kilometers = Double.valueOf(parts[0]);

                // 如果有米部分，转换为公里并加上
                if (parts.length > 1) {
                    Double meters = Double.valueOf(parts[1]);
                    kilometers += meters / 1000;
                }

                return kilometers;
            } catch (Exception e) {
                return null; // 转换出错返回null
            }
        }

        return null; // 不符合格式返回null
    }

    @Override
    public String convertLaneNumberToLetter(String laneNumber, String direction) {
        if (laneNumber == null || laneNumber.isEmpty()) {
            return "";
        }

        boolean isUpDirection = "0".equals(direction); // 0=上行，1=下行
        StringBuilder result = new StringBuilder();

        // 判断是否包含逗号
        String[] lanes;
        if (laneNumber.contains(",")) {
            lanes = laneNumber.split(",");
        } else {
            // 没有逗号，按单个字符拆分
            lanes = new String[laneNumber.length()];
            for (int i = 0; i < laneNumber.length(); i++) {
                lanes[i] = String.valueOf(laneNumber.charAt(i));
            }
        }

        // 转换每个车道号
        for (int i = 0; i < lanes.length; i++) {
            if (i > 0) {
                result.append(",");
            }

            String lane = lanes[i].trim();
            try {
                int laneNum = Integer.parseInt(lane);
                if (isUpDirection) { // 上行
                    switch (laneNum) {
                        case 1: result.append("C"); break;
                        case 2: result.append("A"); break;
                        case 3: result.append("E"); break;
                        case 4: result.append("G"); break;
                        default: result.append(lane); break;
                    }
                } else { // 下行
                    switch (laneNum) {
                        case 1: result.append("D"); break;
                        case 2: result.append("B"); break;
                        case 3: result.append("F"); break;
                        case 4: result.append("H"); break;
                        default: result.append(lane); break;
                    }
                }
            } catch (NumberFormatException e) {
                // 如果不是数字，保持原样
                result.append(lane);
            }
        }

        return result.toString();
    }

    private static final String FINSP_CODE_TEMPLATE = "JCJC-{}-{}-{}";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private String getNextCode(String facilityCat, String orgEn, LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return String.format("%s-%04d", generateBaseCode(facilityCat, orgEn,localDate), getNextSerial(facilityCat, orgEn,localDate)+1);
    }

    public Integer getNextSerial(String facilityCat, String orgEn,LocalDate localDate) {
        validateParams(facilityCat, orgEn,localDate);
        return queryMaxSerialFromDB(facilityCat, orgEn,localDate);
    }

    // 提取公共方法：参数校验
    private void validateParams(String facilityCat, String orgEn,LocalDate localDate) {
        Objects.requireNonNull(facilityCat, "设施类型不能为空[2,9](@ref)");
        Objects.requireNonNull(orgEn, "机构编码不能为空[2,9](@ref)");
    }

    // 提取公共方法：生成基础编码
    private String generateBaseCode(String facilityCat, String orgEn,LocalDate localDate) {
        return StrUtil.format(FINSP_CODE_TEMPLATE,
                facilityCat,
                orgEn.toUpperCase(),
                localDate.format(DATE_FORMATTER)
        );
    }

    private Integer queryMaxSerialFromDB(String facilityCat, String orgEn,LocalDate localDate) {
        QueryWrapper<NmFinsp> wrapper = new QueryWrapper<>();
        wrapper.select("COALESCE(MAX(TO_NUMBER(SUBSTR(FINSP_CODE, -4))), 0) AS max_serial")
                .likeRight("FINSP_CODE", generateBaseCode(facilityCat, orgEn,localDate))
                .eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString());

        return Optional.ofNullable(nmFinspService.getObj(wrapper, obj -> Integer.parseInt(obj.toString())))
                .orElse(0);
    }
} 