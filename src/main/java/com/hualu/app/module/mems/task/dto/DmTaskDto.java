package com.hualu.app.module.mems.task.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class DmTaskDto implements Serializable {

    @ApiModelProperty(value = "任务单ID")
    @TableId("MTASK_ID")
    private String mtaskId;

    @ApiModelProperty(value = "任务单编码")
    @TableField("MTASK_CODE")
    private String mtaskCode;

    @ApiModelProperty(value = "维修项目名称")
    @TableField("MTASK_NAME")
    private String mtaskName;

    @ApiModelProperty(value = "管养单位编码")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    //管养单位名称
    private String mntOrgName;

    @ApiModelProperty(value = "发单部门名称")
    @TableField("MNG_DEPT_NM")
    private String mngDeptNm;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "指定开工日期")
    @TableField("ASKED_START_DATE")
    private LocalDate askedStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "指定完工日期")
    @TableField("ASKED_COMPL_DATE")
    private LocalDate askedComplDate;

    @ApiModelProperty(value = "养护单位名称")
    @TableField("MNTN_ORG_NAME")
    private String mntnOrgName;

    @ApiModelProperty(value = "紧急程度")
    @TableField("EMERGY_DEGREE")
    private String emergyDegree;

    //紧急程度中文显示
    private String emergyDegreeName;

    @ApiModelProperty(value = "发单人")
    @TableField("SEND_USER")
    private String sendUser;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "发单时间")
    @TableField("SEND_TIME")
    private LocalDate sendTime;

    @ApiModelProperty(value = "接收人")
    @TableField("RECEIVE_USER")
    private String receiveUser;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "接收时间")
    @TableField("RECEIVE_TIME")
    private LocalDate receiveTime;

    @ApiModelProperty(value = "申诉理由")
    @TableField("CANCEL_OPINION")
    private String cancelOpinion;

    @ApiModelProperty(value = "技术要求")
    @TableField("TECH_REQ")
    private String techReq;

    @TableField("STATUS")
    private Integer status;

    //状态中文显示
    private String statusName;

    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDate createTime;

    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "流程ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "任务单相关附件")
    @TableField("MTASK_FILE")
    private String mtaskFile;

    @ApiModelProperty(value = "合同id")
    @TableField("CONTR_ID")
    private String contrId;

    @ApiModelProperty(value = "地点")
    @TableField("PLACE")
    private String place;

    @ApiModelProperty(value = "主要维修项目")
    @TableField("MAJOR_PROJECT")
    private String majorProject;

    @ApiModelProperty(value = "项目公司名称")
    @TableField("PRJ_ORG_NAME")
    private String prjOrgName;

    @ApiModelProperty(value = "项目公司编码")
    @TableField("PRJ_ORG_CODE")
    private String prjOrgCode;

    @ApiModelProperty(value = "任务单明细数量")
    @TableField("TOTAL_COUNT")
    private Integer totalCount;

    @ApiModelProperty(value = "任务单验收明细数量")
    @TableField("ACCPT_COUNT")
    private Integer accptCount;

    //项目名称
    private String contrName;
    //路线名称（导出时使用）
    private String lineName;

    //当前年度
    private String curYear;
    //任务单编码+名称（维修验收单选择工程记录时使用）
    private String mtaskAllName;
    //任务单状态
    private String statusTemp;
    private String totalNum;   //任务单明细数量
    private String accptNum;   //任务单明细验收数量
    private String partiname;//审批人
    private int overTime;//是否超时 0：未超时  1：已超时
}
