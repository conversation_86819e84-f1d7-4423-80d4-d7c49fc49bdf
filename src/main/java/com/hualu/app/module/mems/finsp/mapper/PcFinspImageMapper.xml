<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspImageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinspImage">
        <id column="IMAGE_ID" property="imageId" />
        <result column="RECORD_ID" property="recordId" />
        <result column="IMAGE_TYPE" property="imageType" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        IMAGE_ID, RECORD_ID, IMAGE_TYPE, X, Y, CREATE_TIME, UPDATE_TIME
    </sql>

</mapper>
