<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.machineclean.mapper.DmMachineCleaningMapper">
  <select id="getMachineCleanList"
    resultType="com.hualu.app.module.mems.machineclean.entity.DmMachineCleaning">
    SELECT MC.MC_ID AS mc_Id,
      (select l.LINE_ALLNAME
       from GDGS.BASE_LINE l
       where l.LINE_ID = mc.LINE_ID and l.IS_ENABLE = 1 and l.IS_DELETED = 0 and
         ROWNUM = 1) AS lineName,
      (select l.LINE_CODE
      from GDGS.BASE_LINE l
      where l.LINE_ID = mc.LINE_ID and l.IS_ENABLE = 1 and l.IS_DELETED = 0 and
      ROWNUM = 1) AS lineCode,
      mc.LINE_ID line_Id,
      (select dd.ATTRIBUTE_VALUE
       from GDGS.base_datathird_dic dd
       where dd.attribute_item = 'LINE_DIRECT' and dd.attribute_code = MC.DIRECT and
         ROWNUM = 1) AS lineDirectName,
      mc.DIRECT,
      mc.lane,
      mc.START_STAKE,
      mc.END_STAKE,
      (select dd.ATTRIBUTE_VALUE
       from GDGS.base_datathird_dic dd
       where dd.attribute_item = 'LANE' and dd.attribute_code = MC.LANE and ROWNUM = 1) AS laneName,
      ('K' || trunc(MC.START_STAKE, 0) || '+' ||
       substr(trim(to_char(MC.START_STAKE, '9999999.999')),
              instr(trim(to_char(MC.START_STAKE, '9999999.999')), '.', 1, 1) +
              1)) AS startStakeStr,
      ('K' || trunc(MC.END_STAKE, 0) || '+' || substr(trim(to_char(MC.END_STAKE, '9999999.999')),
                                                      instr(
                                                        trim(to_char(MC.END_STAKE, '9999999.999')),
                                                        '.', 1, 1) +
                                                      1)) AS endStakeStr,
      MC.QS_LENGTH AS qs_Length,
      MC.START_IMAGE AS start_Image,
      MC.END_IMAGE AS end_Image,
      mc.ORG_ID org_Id,
      (select o.ORG_FULLNAME
       from GDGS.FW_RIGHT_ORG o
       where o.IS_DELETED = 0 and o.IS_ENABLE = 1 and o.ID = MC.ORG_ID and ROWNUM = 1) AS org_Name,
      MC.START_TIME AS start_Time,
      MC.END_TIME AS end_Time,
      MC.REMARK AS remark
    FROM HSMSDB.DM_MACHINE_CLEANING MC
    ${ew.customSqlSegment}
  </select>
</mapper>