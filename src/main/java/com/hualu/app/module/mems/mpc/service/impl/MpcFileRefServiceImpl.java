package com.hualu.app.module.mems.mpc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.mpc.entity.MpcFileRef;
import com.hualu.app.module.mems.mpc.mapper.MpcFileRefMapper;
import com.hualu.app.module.mems.mpc.service.MpcFileRefService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Service
public class MpcFileRefServiceImpl extends ServiceImpl<MpcFileRefMapper, MpcFileRef> implements MpcFileRefService {

    @Override
    public List<MpcFileRef> listFileByBillId(String billId) {
        LambdaQueryWrapper<MpcFileRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpcFileRef::getBillId,billId);
        return list(queryWrapper);
    }

    @Override
    public void delByBillId(String billId) {
        LambdaQueryWrapper<MpcFileRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpcFileRef::getBillId,billId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public void saveFile(String billId, String fileIds) {
        if (StrUtil.isBlank(fileIds)){
            return;
        }
        //app照片只做追加操作，所以无需先移除
        //delFileByBillId(billId);
        String[] split = fileIds.split(",");
        List<MpcFileRef> mpcFileRefs = Lists.newArrayList();
        for (String fid : split) {
            MpcFileRef item = new MpcFileRef();
            item.setFileId(fid);
            item.setBillId(billId);
            mpcFileRefs.add(item);
        }
        if (CollectionUtil.isNotEmpty(mpcFileRefs)){
            saveBatch(mpcFileRefs);
        }
    }

    @Override
    public void delByFileId(String fileId) {
        LambdaQueryWrapper<MpcFileRef> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpcFileRef::getFileId,fileId);
        remove(queryWrapper);
    }
}
