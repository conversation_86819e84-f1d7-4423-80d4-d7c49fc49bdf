package com.hualu.app.module.mems.finsp.mapper;

import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 结构物排查表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface PcFinspMapper extends BaseMapper<PcFinsp> {

    List<PcFinsp> listByCycle(@Param("structId") String structId, @Param("facilityCat") String facilityCat, @Param("months") List<String> months,@Param("mntType") String mntType);

    @Update("UPDATE pc_finsp d SET d.DSS_NUM = ( select count(1) from pc_Finsp_record b where b.Finsp_id = d.Finsp_id and b.del_flag=0) where d.Finsp_id=#{finspId}")
    void updateDssNum(@Param("finspId") String finspId);

    List<PcFinspWordDto> listFinspWord(@Param("finspIds") List<String> finspIds, @Param("prjId") String prjId);

    List<PcProjectExportDto> exportData(@Param("finspIds") List<String> finspIds, @Param("prjId") String prjId);

    List<String> listFinspIdByPrjId(@Param("prjId") String prjId);

    List<PcFinsp> listDssNumByStructIds(@Param("structIds") List<String> structIds);
}
