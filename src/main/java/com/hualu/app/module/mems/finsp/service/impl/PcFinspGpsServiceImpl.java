package com.hualu.app.module.mems.finsp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.finsp.entity.PcFinspGps;
import com.hualu.app.module.mems.finsp.mapper.PcFinspGpsMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspGpsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 结构物排查轨迹表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class PcFinspGpsServiceImpl extends ServiceImpl<PcFinspGpsMapper, PcFinspGps> implements PcFinspGpsService {

    @Override
    public void removeByFinspIds(List<String> finspIds) {
        LambdaQueryWrapper<PcFinspGps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PcFinspGps::getFinspId, finspIds);
        remove(queryWrapper);
    }

    @Override
    public PcFinspGps getXyByFinspId(String finspId) {
        LambdaQueryWrapper<PcFinspGps> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspGps::getFinspId, finspId);
        //queryWrapper.last("and rownum <=1");
        PcFinspGps one = getOne(queryWrapper, false);
        return one;
    }
}
