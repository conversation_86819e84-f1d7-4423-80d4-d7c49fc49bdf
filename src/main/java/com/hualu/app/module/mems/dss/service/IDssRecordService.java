package com.hualu.app.module.mems.dss.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;

import java.util.List;

/**
 * 日常巡查、经常检查、定检、专项病害明细库接口
 */
public interface IDssRecordService {


    /**
     * 病害来源 1：日常巡查，2：经常检查，3：定检，9：专项
     * @return
     */
    String getDssSource();

    /**
     * 病害明细分页查询
     * @param page
     * @param queryWrapper
     * @return
     */
    List<DssInfoDto> selectDssRecordPage(Page page, QueryWrapper queryWrapper);

    /**
     * 病害闭合方式
     * @param dssIds 病害类型集合
     * @param closeType 闭合方式（1：任务单闭合、2：专项闭合）
     * @return
     */
    void updateDssRecordByCloseType(List<String> dssIds, String closeType);
}
