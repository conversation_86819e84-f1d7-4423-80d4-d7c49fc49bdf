<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmDinspMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmDinsp">
        <id column="DINSP_ID" property="dinspId" />
        <result column="DINSP_CODE" property="dinspCode" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNT_ORG_NM" property="mntOrgNm" />
        <result column="SEARCH_DEPT" property="searchDept" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="INSP_FREQUENCY" property="inspFrequency" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_NAME" property="lineName" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="STAKE_NAME" property="stakeName" />
        <result column="WEATHER" property="weather" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="STATUS" property="status" />
        <result column="DSS_NUM" property="dssNum" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DINSP_ID, DINSP_CODE, PROCESSINSTID, FACILITY_CAT, STRUCT_ID, STRUCT_NAME, MNT_ORG_ID, MNT_ORG_NM, SEARCH_DEPT, INSP_PERSON, INSP_DATE, INSP_TIME, INSP_FREQUENCY, LINE_CODE, LINE_NAME, LINE_DIRECT, ROUTE_NAME, STAKE_NAME, WEATHER, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, REMARK, STATUS, DSS_NUM, ROUTE_CODE, PAVEMENT_TYPE, DEL_FLAG
    </sql>
    <select id="querySituation" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow">
        with orgtable as (select o.ORG_CODE,o.ORG_NAME,o.PARENT_ID from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1 and o.IS_DELETED = 0
        start with o.ID = #{orgCode} connect by prior o.ID = o.PARENT_ID),
        ORG_DATA as (select oo.org_code as SECOND_ORG_CODE,oo.ORG_NAME as SECOND_ORG_NAME,o.ORG_NAME, o.ORG_CODE, l.ROUTE_NAME,
        ROUTE_CODE from orgtable o inner join GDGS.BASE_ROUTE_LOGIC l on l.OPRT_ORG_CODE = o.ORG_CODE
        inner join gdgs.FW_RIGHT_ORG oo on o.PARENT_ID = oo.ORG_CODE
        where l.IS_ENABLE = 1 and oo.IS_DELETED = 0 and oo.IS_ENABLE = 1
        group by oo.ORG_CODE,oo.org_name, o.ORG_NAME, o.ORG_CODE, l.ROUTE_CODE,l.ROUTE_NAME),
        MONTHS_DATA AS(select month,EXTRACT(DAY FROM LAST_DAY(TO_DATE(month,'yyyy-MM'))) as days from
        (SELECT TO_CHAR(ADD_MONTHS(TO_DATE(#{year} || '-01-01', 'YYYY-MM-DD'), LEVEL - 1), 'yyyy-MM') AS month FROM dual CONNECT BY LEVEL &lt;= 12)),
        SPE_QL_DATA as (select y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.ACROSS_FTR_TYPE = '2' and br.VALID_FLAG = 1
        and exists( select 1 from BCTCMSDB.T_BRDG_BRDGTYPE bt inner join BCTCMSDB.T_BRDG_TOPTYPE t on t.BRDG_TYPE = bt.BRDGTYPE_ID
        where bt.P_BRDGTYPE_ID in ('1','2', '3','4') and t.BRDGRECOG_ID = br.BRDGRECOG_ID and t.VALID_FLAG = 1  and bt.VALID_FLAG = 1)
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1)
        union all
        select  y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.VALID_FLAG = 1
        and exists( select 1 from BCTCMSDB.T_BRDG_BRDGTYPE bt inner join BCTCMSDB.T_BRDG_TOPTYPE t on t.BRDG_TYPE = bt.BRDGTYPE_ID
        where bt.P_BRDGTYPE_ID in ('5','6') and t.BRDGRECOG_ID = br.BRDGRECOG_ID and t.VALID_FLAG = 1  and bt.VALID_FLAG = 1)
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1)),
        ONE_QL_DATA as (select y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.VALID_FLAG = 1
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1
        ) and br.MAINTAIN_GRADE = 1 and not exists(select 1 from SPE_QL_DATA c where br.BRDGRECOG_ID = c.struct_id)),
        TWO_QL_DATA as (select y.month comp_date,ROUTE_CODE,BRDGRECOG_ID as struct_id
        from BCTCMSDB.T_BRDG_BRDGRECOG br inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where br.VALID_FLAG = 1
        and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b where b.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and b.VALID_FLAG = 1
        ) and br.MAINTAIN_GRADE = 2 and not exists(select 1 from SPE_QL_DATA c where br.BRDGRECOG_ID = c.struct_id)),
        SD_TOTAL as (select y.month comp_date,ROUTE_CODE,count(*) as total
        from MTMSDB.MTMS_TUNNEL_BASIC t inner join MONTHS_DATA y on y.month &gt;= to_char(BUILT_DATE,'yyyy-MM') where IS_DELETED = 0 and IS_ENABLE = 1 group by y.month, ROUTE_CODE ),
        BP_TOTAL as (select y.month comp_date,ROUTE_CODE,count(*) as total
        from HSMSDB.HSMS_SLOPE_INFO x inner join MONTHS_DATA y on y.month &gt;= to_char(COMPLETION_TIME_DATE,'yyyy-MM') where IS_DELETED = 0 and COMPLETION_TIME_DATE is not null group by y.month, ROUTE_CODE ),
        GEN_QL_TOTAL as (select y.month comp_date,ROUTE_CODE,count(*) as total
        from BCTCMSDB.T_BRDG_BRDGRECOG a inner join MONTHS_DATA y on y.month &gt;= to_char(COMP_TIME,'yyyy-MM') where VALID_FLAG = 1 and exists (select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE b
        where b.MAIN_BRDGRECOG_ID = a.BRDGRECOG_ID and b.VALID_FLAG = 1) and a.MAINTAIN_GRADE in (1,2) group by y.month, ROUTE_CODE),
        DINSP_ONE_QL_NIGHT_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total_night from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_dinsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL' AND INSP_FREQUENCY = 2
        and exists(select 1 from ONE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        DINSP_TWO_QL_NIGHT_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total_night from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_dinsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL' AND INSP_FREQUENCY = 2
        and exists(select 1 from TWO_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        DINSP_SPE_QL_NIGHT_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,count(*) as total_night from (
        SELECT trunc(INSP_DATE) AS year_month,STRUCT_ID,ROUTE_CODE,MNT_ORG_ID FROM memsdb.nm_dinsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year} AND FACILITY_CAT = 'QL' AND INSP_FREQUENCY = 2
        and exists(select 1 from SPE_QL_DATA b where a.STRUCT_ID = b.struct_id and a.ROUTE_CODE = b.ROUTE_CODE)
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,STRUCT_ID)
        group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        DINSP_DATA as (select TO_CHAR(year_month, 'YYYY-MM') as year_month, ROUTE_CODE, MNT_ORG_ID,
        sum(case when FACILITY_CAT = 'LM' THEN total else 0 end) as lm_total,sum(case when FACILITY_CAT = 'JA' THEN total else 0 end) as ja_total,
        sum(case when FACILITY_CAT = 'QL' THEN total else 0 end) as ql_total,sum(case when FACILITY_CAT = 'SD' THEN total else 0 end) as sd_total,
        sum(case when FACILITY_CAT = 'BP' THEN total else 0 end) as bp_total from (SELECT trunc(INSP_DATE) AS year_month,FACILITY_CAT,ROUTE_CODE,MNT_ORG_ID,
        case when FACILITY_CAT in ('LM','JA') then count(1) else count(distinct STRUCT_ID) end as total FROM memsdb.nm_dinsp a
        WHERE EXTRACT(YEAR FROM INSP_DATE) = #{year}
        and DEL_FLAG = 0 group by trunc(INSP_DATE),ROUTE_CODE,MNT_ORG_ID,FACILITY_CAT)
    group by TO_CHAR(year_month, 'YYYY-MM'), ROUTE_CODE, MNT_ORG_ID),
        BASE_DATA as (select #{year} as year,second_org_code, second_org_name,org_name,org_code, route_name, route_code,TO_CHAR(TO_DATE(month, 'YYYY-MM'),'MON','NLS_DATE_LANGUAGE=AMERICAN') as month_abbr,
        case when NVL(dm_lm,0) &gt; days then days else NVL(dm_lm,0) end || ' | ' || days as lm,
        case when NVL(dm_lm,0) &lt; days then '0' else '1' end as lm_result,
        case when NVL(dm_ja,0) &gt; days then days else NVL(dm_ja,0) end || ' | ' || days as ja,
        case when NVL(dm_ja,0) &lt; days then '0' else '1' end as ja_result,
        case when NVL(bp,0) = 0 then '-' else (case when dm_bp &gt; bp * days then bp * days else dm_bp end) || ' | ' || bp || 'x' || days end as bp,
        case when NVL(bp,0) = 0 then '1' else (case when dm_bp &lt; bp * days then '0' else '1' end) end as bp_result,
        case when NVL(gen_ql,0) = 0 then '-' else (case when dm_ql &gt; gen_ql * days then gen_ql * days else dm_ql end) || ' | ' || gen_ql || 'x' || days end as ql,
        case when NVL(gen_ql,0) = 0 then '1' else (case when dm_ql &lt; gen_ql * days then '0' else '1' end) end as ql_result,
        case when NVL(one_ql,0) = 0 then '-' else (case when dm_one_ql_night &gt; one_ql then one_ql else dm_one_ql_night end) || ' | ' || one_ql end as one_ql_night,
        case when NVL(one_ql,0) = 0 then '1' else (case when dm_one_ql_night &lt; one_ql then '0' else '1' end) end as one_ql_night_result,
        case when NVL(two_ql,0) = 0 then '-' else (case when MAX(dm_two_ql_night) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(month, 'YYYY-MM')) - 1)/2)) > two_ql then two_ql else MAX(dm_two_ql_night) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(month, 'YYYY-MM')) - 1)/2)) end) || ' | ' || two_ql end as two_ql_night,
        case when NVL(two_ql,0) = 0 then '1' else (case when MAX(dm_two_ql_night) OVER (PARTITION BY route_code, TRUNC((EXTRACT(MONTH FROM TO_DATE(month, 'YYYY-MM')) - 1)/2)) &gt;= two_ql then '1' else '0' end)end as two_ql_night_result,
        case when NVL(spe_ql,0) = 0 then '-' else (case when dm_spe_ql_night &gt; spe_ql * 4 then spe_ql * 4 else dm_spe_ql_night end) || ' | ' || spe_ql || 'x4' end as spe_ql_night,
        case when NVL(spe_ql,0) = 0 then '1' else (case when dm_spe_ql_night &lt; spe_ql * 4 then '0' else '1' end) end as spe_ql_night_result,
        case when NVL(sd,0) = 0 then '-' else (case when dm_sd &gt; sd * days then sd * days else dm_sd end) || ' | ' || sd || 'x' || days end as sd,
        case when NVL(sd,0) = 0 then '1' else (case when dm_sd &lt; sd * days then '0' else '1' end) end as sd_result
        from (select t1.second_org_code,t1.second_org_name,t1.org_name,t1.org_code,t1.route_name,to_char(WM_CONCAT(distinct route_code)) route_code,b.month,SUM(NVL(bp,0)) as bp,
            SUM(NVL(sd,0)) as sd,SUM(NVL(gen_ql,0)) as gen_ql,SUM(NVL(one_ql,0)) as one_ql,SUM(NVL(two_ql,0)) as two_ql,SUM(NVL(spe_ql,0)) as spe_ql,SUM(NVL(dm_spe_ql_night,0)) as dm_spe_ql_night,
            SUM(NVL(dm_one_ql_night,0)) as dm_one_ql_night,SUM(NVL(dm_two_ql_night,0)) as dm_two_ql_night,SUM(NVL(dm_bp,0)) as dm_bp,SUM(NVL(dm_ja,0)) as dm_ja,max(days) days,
            SUM(NVL(dm_lm,0)) as dm_lm,SUM(NVL(dm_sd,0)) as dm_sd,SUM(NVL(dm_ql,0)) as dm_ql from
            (select a.second_org_code,a.second_org_name,a.org_name,a.org_code,a.route_name,a.route_code,b.month,max(NVL(c.total,0)) as bp,
            max(NVL(d.total,0)) as sd,max(NVL(e.total,0)) as gen_ql,max(NVL(m.total,0)) as one_ql,max(NVL(g.total,0)) as two_ql,max(NVL(h.total,0)) as spe_ql,max(NVL(i.total_night,0)) as dm_spe_ql_night,
            max(NVL(j.total_night,0)) as dm_one_ql_night,max(NVL(k.total_night,0)) as dm_two_ql_night,max(NVL(l.bp_total,0)) as dm_bp,max(NVL(l.ja_total,0)) as dm_ja,
            max(NVL(l.lm_total,0)) as dm_lm,max(NVL(l.sd_total,0)) as dm_sd,max(NVL(l.ql_total,0)) as dm_ql
            from ORG_DATA a inner join MONTHS_DATA b on 1=1
            left join BP_TOTAL c on a.ROUTE_CODE = c.ROUTE_CODE and c.comp_date = b.month
            left join SD_TOTAL d on a.ROUTE_CODE = d.ROUTE_CODE and d.comp_date = b.month
            left join GEN_QL_TOTAL e on a.ROUTE_CODE = e.ROUTE_CODE and e.comp_date = b.month
            left join (select comp_date, ROUTE_CODE, count(*) as total from ONE_QL_DATA group by comp_date, ROUTE_CODE) m on a.ROUTE_CODE = m.ROUTE_CODE and m.comp_date = b.month
            left join (select comp_date, ROUTE_CODE, count(*) as total from TWO_QL_DATA group by comp_date, ROUTE_CODE) g on a.ROUTE_CODE = g.ROUTE_CODE and g.comp_date = b.month
            left join (select comp_date, ROUTE_CODE, count(*) as total from SPE_QL_DATA group by comp_date, ROUTE_CODE) h on a.ROUTE_CODE = h.ROUTE_CODE and h.comp_date = b.month
            left join DINSP_SPE_QL_NIGHT_DATA i on a.ROUTE_CODE = i.ROUTE_CODE and i.year_month = b.month
            left join DINSP_ONE_QL_NIGHT_DATA j on a.ROUTE_CODE = j.ROUTE_CODE and j.year_month = b.month
            left join DINSP_TWO_QL_NIGHT_DATA k on a.ROUTE_CODE = k.ROUTE_CODE and k.year_month = b.month
            left join DINSP_DATA l on a.ROUTE_CODE = l.ROUTE_CODE and l.year_month = b.month group by a.second_org_code,a.second_org_name,a.org_name,a.org_code,a.route_name,a.route_code,b.month) t1
            inner join MONTHS_DATA b on t1.month = b.month group by t1.second_org_code,t1.second_org_name,t1.org_name,t1.org_code,t1.route_name,b.month)
            )
         select * from base_data
         pivot ( max(lm) lm,max(bp) bp,max(sd) sd,max(ql) gen_ql,max(ja) ja,max(spe_ql_night) spe_ql_night,max(one_ql_night) one_ql_night,max(two_ql_night) two_ql_night,
         max(lm_result) lm_result,max(bp_result) bp_result,max(sd_result) sd_result,max(ql_result) gen_ql_result,max(ja_result) ja_result,max(spe_ql_night_result) spe_ql_night_result,
         max(one_ql_night_result) one_ql_night_result,max(two_ql_night_result) two_ql_night_result
        for month_abbr in ('JAN' jan,'FEB' feb,'MAR' mar,'APR' apr,'MAY' may,'JUN' jun,'JUL' jul,'AUG' aug,'SEP' sep,'OCT' oct,'NOV' nov,'DEC' dec))
        order by second_org_name, org_name, route_name
    </select>
    <select id="lastNmDinsp" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinsp">
        select * from (select * from nm_dinsp where facility_cat = #{facilityCat} and mnt_org_id = #{orgIdString} and del_flag=0 order by CREATE_TIME desc ) where rownum &lt;= 1
    </select>

    <select id="getBridgeList" resultType="com.hualu.app.module.mems.comm.entity.BaseStruct">
        select br.BRDGRECOG_ID as STRUCT_ID,br.BRDG_NAME||'('||br.BRDG_LINE_TYPE||br.FRAME_NUM||')' as STRUCT_NAME from BCTCMSDB.T_BRDG_BRDGRECOG br
        where br.OPRT_ORG_CODE=#{orgCode}
        and br.VALID_FLAG=1
        <if test="structName != null and structName != ''">
            and br.BRDG_NAME like concat(concat('',#{structName,jdbcType=VARCHAR}),'%')
        </if>
        and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.MAIN_BRDGRECOG_ID=m.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID=br.BRDGRECOG_ID and m.VALID_FLAG=1)
        order by br.ROAD_NUM,br.LOGIC_CNTR_STAKE_NUM
    </select>

    <select id="getCulvertList" resultType="com.hualu.app.module.mems.comm.entity.BaseStruct">
        select br.CLVRTRECOG_ID as STRUCT_ID,br.CLVRT_NAME as STRUCT_NAME from BCTCMSDB.T_CLVRT_CLVRTRECOG br
        where br.OPRT_ORG_CODE=#{orgCode}
        and br.VALID_FLAG=1
        <if test="structName != null and structName != ''">
            and br.CLVRT_NAME like concat(concat('',#{structName,jdbcType=VARCHAR}),'%')
        </if>
        order by br.LINE_CODE,br.LOGIC_CNTR_STAKE_NUM
    </select>

    <select id="selectStsList" resultType="com.hualu.app.module.basedata.dto.NmDinspStsTreeDto">
        select to_char(d.INSP_DATE,'FMMM')||'.'||to_char(d.INSP_DATE,'dd') as month,count(distinct d.STRUCT_ID) as check_num,sum(decode(t.DSS_ID,null,0,1)) as dis from MEMSDB.NM_DINSP  d
            left join MEMSDB.NM_DINSP_RECORD t
        on d.DINSP_ID=t.DINSP_ID and t.del_flag=0
        where d.INSP_DATE>=to_date(#{startDate},'yyyy-MM-dd')
        and d.INSP_DATE &lt;=to_date(#{endDate},'yyyy-MM-dd')
        and d.FACILITY_CAT=#{cat}
        and d.MNT_ORG_ID in (#{orgCode}) and d.DEL_FLAG=0
<!--        <if test="status != null and status != '' and status != 3">-->
<!--            and d.STATUS = #{status}-->
<!--        </if>-->
        <if test="dinspCode != null and dinspCode != ''">
            and d.dinsp_Code  like concat(concat('',#{dinspCode,jdbcType=VARCHAR}),'%')
        </if>
        <if test="hasDssStatus != null and hasDssStatus != ''">
            and d.dss_Num = #{hasDssStatus}
        </if>
        group by d.INSP_DATE
        order by d.INSP_DATE
    </select>

    <select id="getBridgeNum" resultType="int">
        select count(1) from BCTCMSDB.T_BRDG_BRDGRECOG br
        where br.OPRT_ORG_CODE in (#{orgIds})
        and br.VALID_FLAG=1
        and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.MAIN_BRDGRECOG_ID=m.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID=br.BRDGRECOG_ID and m.VALID_FLAG=1)
    </select>

    <select id="getTunnelNum" resultType="int">
        select count(1) from MTMSDB.MTMS_TUNNEL_BASIC t where t.OPRT_ORG_CODE
            in (#{orgIds}) and t.IS_ENABLE=1 and t.IS_DELETED=0
    </select>

    <select id="getSlopeNum" resultType="int">
        select count(1) from HSMSDB.HSMS_SLOPE_INFO t
                                 inner JOIN gdgs.FW_RIGHT_DATA_PERMISSION frd ON t.ROUTE_CODE = frd.ROUTE_CODE
        where frd.OPRT_ORG_CODE
            in (#{orgIds})  and t.IS_DELETED=0
    </select>

    <select id="getNmDinspFacilityStat"
    resultType="com.hualu.app.module.mems.nminsp.dto.NmDinspFacilityStat">
        select COMMON_DINSP_ID commonDinspId,
        decode(FACILITY_CAT, 'LM', '路面', 'QL', '桥梁','HD', '涵洞','SD', '隧道','BP', '边坡','JA', '交安', '') facilityCat,
        count(*) count from memsdb.NM_DINSP c
        ${ew.customSqlSegment}
        group by FACILITY_CAT, COMMON_DINSP_ID
  </select>

    <select id="getDssInfoImageExport" resultType="com.hualu.app.module.mems.nminsp.entity.RoadInspectionRecord">
        select P.LINE_CODE as lineCode, A.STAKE as stake, P.INSP_DATE as inspDate, N.DSS_TYPE_NAME as pavementType, p.INSP_PERSON as inspPerson,
        c.FILE_ENTITY_PATH as fileEntityPath,p.DINSP_CODE as dinspCode,P.STRUCT_ID as structId
        from MEMSDB.NM_DINSP_RECORD a
        JOIN MEMSDB.NM_DINSP P ON A.DINSP_ID = P.DINSP_ID
        JOIN MEMSDB.DSS_TYPE_NEW N ON A.DSS_TYPE = N.DSS_TYPE
        join MEMSDB.DSS_IMAGE b on a.DSS_ID = b.DSS_ID
        join gdgs.BASE_FILE_ENTITY c on b.FILE_ID = c.FILE_ENTITY_ID
        WHERE a.FACILITY_CAT = #{facilityCat}
        and a.DINSP_ID in
        <foreach collection="dinspIds" item="dinspId" index="index" open="(" close=")" separator=",">
            #{dinspId}
        </foreach>
        AND c.FILE_ENTITY_PATH IS NOT NULL
    </select>
    <select id="listWorkDinspByInspDate" resultType="com.hualu.app.module.mems.nminsp.dto.WorkNmDinspDto">
        select MNT_ORG_ID, FACILITY_CAT, to_char(INSP_DATE, 'yyyy-MM-dd') as insp_date, count(1) as insp_num,create_user_id
        from NM_DINSP
        where to_char(INSP_DATE, 'yyyy-MM')=#{inspDate} and FACILITY_CAT in ('QL','BP') and status =0 and del_flag = 0
        group by MNT_ORG_ID, FACILITY_CAT, insp_date,create_user_id having count(1) > 1
        order by insp_date desc
    </select>

    <!--病害台账-->
    <select id="DailyInspectionLedger" resultType="com.hualu.app.module.mems.nminsp.entity.RouteInspection">
        with ORG AS (
        select *
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        start with o.ORG_CODE = #{orgCode}
        connect by prior O.ID = O.PARENT_ID
        )
        select DISTINCT O.ORG_NAME as routeName,
        LR.LINE_CODE as lineCode,
        DECODE(A.LINE_DIRECT, '1', '上行', '2', '下行', '4', '匝道')                             as lineDirect,
        DECODE(a.FACILITY_CAT, 'LM', '路面', 'QL', '桥梁', 'SD', '隧道', 'HD', '涵洞', 'BP', '边坡','JA','交安') as facilityCategory,
        a.STAKE as stake,
        N.DSS_TYPE_NAME as dssTypeName,
        A.DSS_POSITION || A.DSS_DESC as dssDetail,
        P.DINSP_CODE as dinspCode,
        p.INSP_DATE as inspDate,
        a.DSS_ID as dssId,
        TO_CHAR(DBMS_LOB.SUBSTR(
        WM_CONCAT(c.FILE_ENTITY_PATH),
        4000,  -- 最大长度
        1      -- 起始位置
        )) as fileEntityPath,
        TO_CHAR(DBMS_LOB.SUBSTR(
        WM_CONCAT(c.FILE_ENTITY_ID),
        4000,  -- 最大长度
        1      -- 起始位置
        )) AS fileEntityId,
        p.STRUCT_ID as structId
        from MEMSDB.NM_DINSP_RECORD a
        LEFT JOIN MEMSDB.NM_DINSP P ON A.DINSP_ID = P.DINSP_ID
        JOIN MEMSDB.DSS_TYPE_NEW N ON A.DSS_TYPE = N.DSS_TYPE
        join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
        JOIN ORG O ON LR.OPRT_ORG_CODE=O.ORG_CODE
        LEFT join MEMSDB.DSS_IMAGE b on a.DSS_ID = b.DSS_ID
        LEFT join gdgs.BASE_FILE_ENTITY c on b.FILE_ID = c.FILE_ENTITY_ID
        WHERE  b.DEL_FLAG = 0
        <if test="dinspCode != null and dinspCode != ''">
            AND P.DINSP_CODE like #{dinspCode}
        </if>
        <if test="facilityCat != null and facilityCat != ''">
            AND P.FACILITY_CAT=#{facilityCat}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND LR.LINE_CODE=#{lineCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND to_char(P.INSP_DATE,'YYYY-MM') <![CDATA[=]]>#{startDate}
        </if>
        <if test="startStake != null and startDate != ''">
            AND a.STAKE <![CDATA[>=]]>#{startStake}
        </if>
        <if test="endStake != null and endStake != ''">
            AND a.STAKE <![CDATA[<=]]>#{endStake}
        </if>
        <if test="dssTypeName != null and dssTypeName != ''">
            AND (
            <foreach item="type" collection="dssTypeName.split(',')" separator=" OR ">
                N.dss_type_name LIKE '%' || #{type} || '%'
            </foreach>
            )
        </if>
        <if test="facilityCategory != null and facilityCategory != ''">
            AND (
            <foreach item="category" collection="facilityCategory.split(',')" separator=" OR ">
                P.FACILITY_CAT = #{category}
            </foreach>
            )
        </if>
        <if test="lineDirect != null and lineDirect != ''">
            AND (
            <foreach item="direction" collection="lineDirect.split(',')" separator=" OR ">
                A.LINE_DIRECT = #{direction}
            </foreach>
            )
        </if>
        <if test="routeName != null and routeName != ''">
            AND (
            <foreach item="name" collection="routeName.split(',')" separator=" OR ">
                LR.ROUTE_NAME = #{name}
            </foreach>
            )
        </if>
        GROUP BY
        O.ORG_NAME,
        LR.LINE_CODE,
        A.LINE_DIRECT,
        a.FACILITY_CAT,
        a.STAKE,
        N.DSS_TYPE_NAME,
        A.DSS_POSITION,
        A.DSS_DESC,
        P.DINSP_CODE,
        p.INSP_DATE,
        a.DSS_ID,
        p.STRUCT_ID
        ORDER BY routeName,lineCode,lineDirect,a.STAKE
    </select>

    <select id="findDssInfoId" resultType="com.hualu.app.module.mems.nminsp.entity.MtaskEntity">
        SELECT DISTINCT D.MTASK_CODE AS mtaskCode, A.MTASK_ACCPT_CODE AS mtaskAccptCode,T.DSS_ID AS dssId,O.REPAIR_DATE AS repairDate
        FROM MEMSDB.DM_TASK D
        left JOIN MEMSDB.DM_TASK_DETAIL T ON D.MTASK_ID=T.MTASK_ID
        left JOIN MEMSDB.DM_TASK_ACCPT_DETAIL TS ON TS.mtask_dtl_id=T.mtask_dtl_id
        left JOIN MEMSDB.DM_TASK_ACCPT A ON TS.mtask_accpt_id = A.MTASK_ACCPT_ID
        left JOIN MEMSDB.DSS_INFO O ON T.DSS_ID=O.DSS_ID
        WHERE O.DSS_ID IN
        <foreach collection="dssIds" item="dssId" index="index" open="(" close=")" separator=",">
            #{dssId}
        </foreach>
        <if test="mtaskCode != null and mtaskCode != ''">
            AND D.MTASK_CODE = #{mtaskCode}
        </if>
        <if test="mtaskAccptCode != null and mtaskAccptCode != ''">
            AND A.MTASK_ACCPT_CODE = #{mtaskAccptCode}
        </if>
    </select>
    <!--边坡日常巡查台账-->
    <select id="findBpDinspRecord" resultType="com.hualu.app.module.mems.nminsp.entity.SlopeInspectionRecord">
        with ORG AS (select *
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        start with o.ORG_CODE = #{orgCode}
        connect by prior O.ID = O.PARENT_ID)
        select LR.ROUTE_NAME                                                                        as routeName,
        LR.LINE_CODE                                                                         as lineCode,
        OS.SLOPE_NAME as slopeName,
        OS.SLOPE_LENGTH as slopeLength,
        OS.SLOPE_LEVEL as slopeLevel,
        DECODE(P.DSS_NUM, 0, '无', P.DSS_NUM)                                                 AS DssNums,
        decode(HHS.SLOPE_TC_GRADE, 0, '未评定', '1', '稳定', '2', '基本稳定', '3', '欠稳定', '4', '不稳定') as slopeTcGrade,
        P.DINSP_CODE                                                                         as dinspCode,
        P.INSP_FREQUENCY as inspFrequency,
        A.DSS_POSITION || A.DSS_DESC                                                         as dssDetail,
        p.INSP_DATE                                                                          as inspDate,
        a.MNTN_ADVICE as mntnAdvice,
        OS.ORG_FULLNAME as orgFullname
        from MEMSDB.NM_DINSP_RECORD a
        LEFT JOIN MEMSDB.NM_DINSP P ON A.DINSP_ID = P.DINSP_ID
        LEFT JOIN HSMSDB.HSMS_SLOPE_INFO OS ON P.STRUCT_ID = OS.SLOPE_ID
        LEFT JOIN (
            SELECT
            SLOPE_ID,SLOPE_TC_GRADE,
            ROW_NUMBER() OVER (
            PARTITION BY SLOPE_ID
            ORDER BY UPDATE_TIME DESC
            ) AS rn
            FROM HSMSDB.HSMS_SFEVAL
            WHERE IS_DELETED = 0
        ) HHS ON OS.SLOPE_ID = HHS.SLOPE_ID AND HHS.rn = 1
        JOIN MEMSDB.DSS_TYPE_NEW N ON A.DSS_TYPE = N.DSS_TYPE
        join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
        JOIN ORG O ON LR.OPRT_ORG_CODE = O.ORG_CODE
        join gdgs.FW_RIGHT_ORG os on OS.OPT_ORG_ID=OS.ORG_CODE
        WHERE a.FACILITY_CAT = 'BP'
        <if test="dinspCode != null and dinspCode != ''">
            AND P.DINSP_CODE=#{dinspCode}
        </if>
        <if test="status != null and status != ''">
            AND P.STATUS=#{status}
        </if>
        <if test="lineCode != null and lineCode != ''">
            AND LR.LINE_CODE=#{lineCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND P.INSP_DATE <![CDATA[>=]]>TO_DATE(#{startDate},'YYYY-MM-DD')
        </if>
        <if test="endDate != null and endDate != ''">
            AND P.INSP_DATE <![CDATA[<=]]>TO_DATE(#{endDate},'YYYY-MM-DD')
        </if>
        <if test="hasDssStatus != null and hasDssStatus != ''">
            <choose>
                <when test="hasDssStatus == '0'.toString()">
                    AND P.DSS_NUM=0
                </when>
                <when test="hasDssStatus == '1'.toString()">
                    AND P.DSS_NUM>=1
                </when>
            </choose>
        </if>
        ORDER BY routeName, lineCode, p.INSP_DATE
    </select>

    <!--巡查单汇总表-->
    <select id="findDinspCheckSum" resultType="com.hualu.app.module.mems.nminsp.entity.SumInspectionRecord">
        with ORG AS (select *
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        start with o.ORG_CODE = #{orgCode}
        connect by prior O.ID = O.PARENT_ID)
        select DISTINCT LR.LINE_CODE as lineCode,
        P.INSP_TIME as inspTime,
        p.STAKE_NAME as stakeName,
        p.INSPECT_LENGTH as inspectLength,
        C.ATTRIBUTE_VALUE as weather,
        DECODE(P.FACILITY_CAT, 'LM', '路面', 'QL', '桥梁', 'SD', '隧道', 'HD', '涵洞', 'BP', '边坡','JA','交安') as facilityCategory,
        a.LANE as lane,a.STAKE AS A1,
        'K' || REPLACE(a.STAKE, '.', '+') as stake,
        N.DSS_TYPE_NAME,DECODE(A.LINE_DIRECT,'1','上行','2','下行','4','匝道') as lineDirect,
        P.INSP_PERSON as checkPerson,a.DSS_POSITION as dssPosition,P.DSS_NUM as dssNum,
        P.DINSP_CODE as dinspCode,p.PROCESSINSTID as processInstId, a.MNTN_ADVICE as mntnAdvice,
        P.STRUCT_ID as structId,P.SEARCH_DEPT as searchDept,to_char(P.INSP_DATE,'YYYY-MM-DD') as inspDate
        from MEMSDB.NM_DINSP P
        LEFT JOIN MEMSDB.NM_DINSP_RECORD a ON A.DINSP_ID = P.DINSP_ID
        LEFT JOIN MEMSDB.DSS_TYPE_NEW N ON A.DSS_TYPE = N.DSS_TYPE
        join gdgs.BASE_ROUTE_LOGIC LR on p.ROUTE_CODE = LR.ROUTE_CODE
        JOIN ORG O ON LR.OPRT_ORG_CODE = O.ORG_CODE
        JOIN GDGS.BASE_DATATHIRD_DIC C ON P.WEATHER = C.ATTRIBUTE_CODE AND C.ATTRIBUTE_ITEM = 'WEATHER'
        WHERE P.INSP_DATE <![CDATA[>=]]> TO_DATE(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
        AND P.INSP_DATE <![CDATA[<=]]> TO_DATE(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
        AND P.DEL_FLAG=0
        ORDER BY LR.LINE_CODE,facilityCategory,lineDirect,a.LANE,A1
    </select>
</mapper>
