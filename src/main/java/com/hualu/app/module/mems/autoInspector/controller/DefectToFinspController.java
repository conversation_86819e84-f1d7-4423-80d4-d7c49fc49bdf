package com.hualu.app.module.mems.autoInspector.controller;

import com.hualu.app.module.mems.autoInspector.dto.ConvertInfo;
import com.hualu.app.module.mems.autoInspector.entity.NmFinspVo;
import com.hualu.app.module.mems.autoInspector.service.DefectToFinspService;
import com.hualu.app.comm.RestResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 病害信息转巡查记录控制器
 * <p>
 * 提供病害信息转换为巡查记录的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
@RestController
@RequestMapping("/mems/auto/defect-to-finsp")
@Api(tags = "病害转巡查接口")
public class DefectToFinspController {

    @Autowired
    private DefectToFinspService defectToFinspService;

    /**
     * 批量转换病害为巡查记录
     *
     * @param defectIds 病害ID列表
     * @return 转换结果
     */
    @PostMapping("/batch-convert")
    @ApiOperation("批量转换病害为巡查记录")
    public RestResult<List<NmFinspVo>> batchConvert(
            @ApiParam(value = "病害ID列表", required = true)
            @RequestBody List<String> defectIds) {
        try {
            List<NmFinspVo> result = defectToFinspService.batchConvert(defectIds);
            if (result.isEmpty()) {
                return RestResult.error("未找到指定的病害信息");
            }
            return RestResult.success(result);
        } catch (Exception e) {
            log.error("病害批量转换异常", e);
            return RestResult.error("批量转换异常：" + e.getMessage());
        }
    }

    /**
     * 批量转换病害为巡查记录
     *
     * @param convertInfo
     * @return 转换结果
     */
    @PostMapping("/batch-convert-byInfo")
    @ApiOperation("批量转换病害为巡查记录")
    public RestResult<String> batchConvertByInfo(
            @ApiParam(value = "病害日期和路段", required = true)
            @RequestBody ConvertInfo convertInfo) {
        try {
            List<NmFinspVo> result = defectToFinspService.batchConvert(convertInfo);
            if (result.isEmpty()) {
                return RestResult.error("未找到指定的病害信息");
            }
            int[] total = {0};
            result.forEach(item->{
                item.getRecords().forEach(record->{
                    total[0]++;
                });
            });
            return RestResult.success("已生成"+result.size()+"条检查单,非修补类病害共"+total[0]+"条");
        } catch (Exception e) {
            log.error("病害批量转换异常", e);
            return RestResult.error("批量转换异常：" + e.getMessage());
        }
    }
} 