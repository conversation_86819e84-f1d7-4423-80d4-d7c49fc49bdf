package com.hualu.app.module.mems.dinsp.entity;

public class FacilityInspectionDTO {
    private String organizationFullName;

    // 路线名称（例：G4京港澳高速）
    private String routeName;

    // 设施类型（使用枚举更规范）
    private String facilityType;

    // 线路编码（例：GL0001）
    private String lineCode;

    // 路线方向（枚举类型）
    private String direction;

    // 桩号（格式：K123+500）
    private String stakeNumber;

    // 病害类型名称（例：裂缝、沉降）
    private String defectTypeName;

    // 检查类型（固定值）
    private String inspectionType;

    private String dssId;

    public String getOrganizationFullName() {
        return organizationFullName;
    }

    public void setOrganizationFullName(String organizationFullName) {
        this.organizationFullName = organizationFullName;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getFacilityType() {
        return facilityType;
    }

    public void setFacilityType(String facilityType) {
        this.facilityType = facilityType;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getStakeNumber() {
        return stakeNumber;
    }

    public void setStakeNumber(String stakeNumber) {
        this.stakeNumber = stakeNumber;
    }

    public String getDefectTypeName() {
        return defectTypeName;
    }

    public void setDefectTypeName(String defectTypeName) {
        this.defectTypeName = defectTypeName;
    }

    public String getInspectionType() {
        return inspectionType;
    }

    public void setInspectionType(String inspectionType) {
        this.inspectionType = inspectionType;
    }

    public String getDssId() {
        return dssId;
    }

    public void setDssId(String dssId) {
        this.dssId = dssId;
    }
}
