package com.hualu.app.module.mems.autoInspector.utils;

import com.hualu.app.module.mems.autoInspector.entity.DownloadVo;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class HttpForDownload {

    private static final String BASE_URL = "http://172.29.0.20:7001/hwaymems/baseFileEntity/previewImage/";
    private static final int MAX_RETRIES = 3; // 最大重试次数
    private static final int RETRY_DELAY = 1000; // 重试间隔时间（毫秒）

    /**
     * 批量下载图片到指定文件夹
     * @param downloadVoList 下载信息列表
     * @param targetDir 目标文件夹路径
     * @return 成功下载的图片数量
     */
    public static int batchDownloadImages(List<DownloadVo> downloadVoList, String targetDir) {
        if (downloadVoList == null || downloadVoList.isEmpty()) {
            return 0;
        }

        // 创建目标文件夹
        File dir = new File(targetDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        final int[] successCount = {0};
        CountDownLatch latch = new CountDownLatch(downloadVoList.size());

        // 使用线程池并发下载
        ExecutorService executorService = Executors.newFixedThreadPool(10);

        for (DownloadVo downloadVo : downloadVoList) {
            executorService.execute(() -> {
                try {
                    boolean success = downloadSingleImage(downloadVo, targetDir);
                    if (success) {
                        synchronized (HttpForDownload.class) {
                            successCount[0]++;
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await(); // 等待所有下载任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        executorService.shutdown();
        return successCount[0];
    }

    /**
     * 下载单张图片
     * @param downloadVo 下载信息对象
     * @param targetDir 目标文件夹路径
     * @return 是否下载成功
     */
    private static boolean downloadSingleImage(DownloadVo downloadVo, String targetDir) {
        String fileId = downloadVo.getFileId();
        String fileName = downloadVo.getFileName();
        String dssType = downloadVo.getDssType();
        BigDecimal stake = downloadVo.getStake();

        if (fileId == null || fileId.isEmpty()) {
            System.err.println("文件ID为空，无法下载");
            return false;
        }

        // 创建病害类型子文件夹
        String dssTypeDirPath = targetDir + File.separator + dssType;
        File dssTypeDir = new File(dssTypeDirPath);
        if (!dssTypeDir.exists()) {
            dssTypeDir.mkdirs();
        }

        // 创建桩号子文件夹
        String stakeDirPath = dssTypeDirPath + File.separator + (stake != null ? stake.toString() : "未知桩号");
        File stakeDir = new File(stakeDirPath);
        if (!stakeDir.exists()) {
            stakeDir.mkdirs();
        }

        // 添加重试机制
        int retries = 0;
        while (retries <= MAX_RETRIES) {
            if (retries > 0) {
                System.out.println("正在进行第" + retries + "次重试下载，文件ID: " + fileId);
                try {
                    Thread.sleep(RETRY_DELAY); // 重试前等待一段时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            HttpURLConnection connection = null;
            InputStream inputStream = null;
            FileOutputStream outputStream = null;

            try {
                String imageUrl = BASE_URL + fileId;
                URL url = new URL(imageUrl);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000); // 增加超时时间
                connection.setReadTimeout(15000); // 增加读取超时时间

                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    if (retries < MAX_RETRIES && (responseCode == 504 || responseCode == 502 || responseCode == 500)) {
                        System.err.println("下载图片失败，文件ID: " + fileId + "，响应码: " + responseCode + "，准备重试...");
                        retries++;
                        continue; // 如果是网关超时等错误，进行重试
                    } else {
                        System.err.println("下载图片失败，文件ID: " + fileId + "，响应码: " + responseCode + "，重试次数已用完或不需要重试");
                        return false;
                    }
                }

                // 获取文件类型
                String contentType = connection.getContentType();
                String fileExtension = getFileExtension(contentType);

                // 如果fileName已经包含扩展名，则不添加
                String finalFileName = fileName;
                if (!fileName.contains(".")) {
                    finalFileName = fileName + fileExtension;
                }

                // 处理重名文件
                File outputFile = new File(stakeDirPath, finalFileName);
                int count = 1;
                String nameWithoutExt = finalFileName;
                String ext = "";

                if (finalFileName.contains(".")) {
                    int dotIndex = finalFileName.lastIndexOf(".");
                    nameWithoutExt = finalFileName.substring(0, dotIndex);
                    ext = finalFileName.substring(dotIndex);
                }

                // 如果文件已存在，则重命名
                while (outputFile.exists()) {
                    finalFileName = nameWithoutExt + "(" + count + ")" + ext;
                    outputFile = new File(stakeDirPath, finalFileName);
                    count++;
                }

                outputStream = new FileOutputStream(outputFile);
                inputStream = connection.getInputStream();

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                System.out.println("成功下载图片: " + fileId + ", 保存为: " + finalFileName +
                        ", 病害类型: " + dssType + ", 桩号: " + (stake != null ? stake.toString() : "未知桩号") +
                        (retries > 0 ? "，重试" + retries + "次后成功" : ""));
                return true;

            } catch (IOException e) {
                if (retries < MAX_RETRIES) {
                    System.err.println("下载图片出错，文件ID: " + fileId + "，错误: " + e.getMessage() + "，准备重试...");
                    retries++;
                    continue; // 如果发生IO异常，进行重试
                } else {
                    System.err.println("下载图片出错，文件ID: " + fileId + "，错误: " + e.getMessage() + "，重试次数已用完");
                    return false;
                }
            } finally {
                // 关闭资源
                try {
                    if (outputStream != null) {
                        outputStream.close();
                    }
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    if (connection != null) {
                        connection.disconnect();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return false; // 所有重试都失败
    }

    /**
     * 根据Content-Type获取文件扩展名
     * @param contentType 内容类型
     * @return 文件扩展名（包括点号）
     */
    private static String getFileExtension(String contentType) {
        if (contentType == null) {
            return ".jpg"; // 默认扩展名
        }

        contentType = contentType.toLowerCase();
        if (contentType.contains("jpeg") || contentType.contains("jpg")) {
            return ".jpg";
        } else if (contentType.contains("png")) {
            return ".png";
        } else if (contentType.contains("gif")) {
            return ".gif";
        } else if (contentType.contains("bmp")) {
            return ".bmp";
        } else if (contentType.contains("webp")) {
            return ".webp";
        } else {
            return ".jpg"; // 默认扩展名
        }
    }
}
