package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.entity.DmFinspGps;
import com.hualu.app.module.mems.finsp.mapper.DmFinspGpsMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspGpsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 经常检查单GPS位置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Service
public class DmFinspGpsServiceImpl extends ServiceImpl<DmFinspGpsMapper, DmFinspGps> implements DmFinspGpsService {

    @Override
    public void delByFinspId(String finspId) {
        if (StrUtil.isNotBlank(finspId)){
            LambdaQueryWrapper<DmFinspGps> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DmFinspGps::getFinspId, finspId);
            this.remove(queryWrapper);
        }
    }
}
