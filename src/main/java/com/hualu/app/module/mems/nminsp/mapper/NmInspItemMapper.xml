<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmInspItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmInspItem">
        <id column="ITEM_ID" property="itemId" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="ITEM_CODE" property="itemCode" />
        <result column="INSP_COM" property="inspCom" />
        <result column="INSP_CONT" property="inspCont" />
        <result column="FIN_VERSION" property="finVersion" />
        <result column="IS_DELETE" property="isDelete" />
        <result column="FIN_DESC" property="finDesc" />
        <result column="FIN_REMARK" property="finRemark" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="INSP_FREQUENCY" property="inspFrequency" />
        <result column="XC_TYPE" property="xcType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ITEM_ID, FACILITY_CAT, ITEM_CODE, INSP_COM, INSP_CONT, FIN_VERSION, IS_DELETE, FIN_DESC, FIN_REMARK, DSS_TYPE, INSP_FREQUENCY, XC_TYPE
    </sql>

    <select id="findBrdgPartRel" resultType="java.util.Map">
        select b.PART_CODE_OLD as OLD,b.PART_CODE_NEW as NEW from BCTCMSDB.BRIDGE_PART_REL b
    </select>
</mapper>
