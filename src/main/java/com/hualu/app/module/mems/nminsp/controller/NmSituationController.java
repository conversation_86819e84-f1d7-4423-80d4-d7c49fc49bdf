package com.hualu.app.module.mems.nminsp.controller;

import com.hualu.app.comm.RestResult;
import com.hualu.app.config.RedisCacheConfig;
import com.hualu.app.config.SituationMQConfig;
import com.hualu.app.module.mems.nminsp.dto.OrgAndYear;
import com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow;
import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.SituationCacheService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.utils.RedisUtils;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 新版巡查情况 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@Validated
@Api(tags = "NmSituationController",description = "新版巡查情况 前端控制器")
@RestController
@RequestMapping("/nmSituation")
public class NmSituationController {

    @Autowired
    private NmDinspService dinspService;

    @Autowired
    private NmFinspService finspService;
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private SituationCacheService situationCacheService;

    @Autowired
    private FwRightOrgService orgService;

    /**
     * 日常巡查
     * @return
     */
    @ApiRegister(value = "nmSituation:dinsp",businessType = BusinessType.OTHER)
    @ApiOperation("日常巡查")
    @PostMapping("dinsp")
    public RestResult<List<NmDinspSituationShow>> dinsp(@RequestBody OrgAndYear orgAndYear) {
        Integer year = orgAndYear.getYear();
        String orgCode = orgAndYear.getOrgCode();
        if (year == null) year = LocalDate.now().getYear();
        if(StringUtils.isBlank(orgCode)){
            orgCode = CustomRequestContextHolder.getOrgIdString();
        }
        // 先从缓存中获取数据（使用N000001的缓存数据）
        String cacheKey = String.format(RedisCacheConfig.DINSP_SITUATION_CACHE_KEY, "N000001", year);
        List<NmDinspSituationShow> allData = redisUtils.getList(cacheKey, NmDinspSituationShow.class);
        
        if (allData != null && !allData.isEmpty()) {
            Set<String> orgSet = new HashSet<>(orgService.selectChildOprtOrgCodes(orgCode));
            // 使用并行流处理，提高大数据量时的处理速度
            List<NmDinspSituationShow> result = allData.parallelStream()
                .filter(item -> orgSet.contains(item.getOrgCode()))
                .collect(Collectors.toList());
            
            log.info("从缓存中获取日常巡查情况数据, orgCode: {}, year: {}", orgCode, year);
            return RestResult.success(result);
        }
        
        // 缓存未命中，从数据库查询
        log.info("缓存未命中，从数据库获取日常巡查情况数据, orgCode: {}, year: {}", orgCode, year);
        List<NmDinspSituationShow> result = dinspService.querySituationShow(orgCode, year);
        
        return RestResult.success(result);
    }

    /**
     * 经常检查
     * @return
     */
    @ApiRegister(value = "nmSituation:finsp",businessType = BusinessType.OTHER)
    @ApiOperation("经常检查")
    @PostMapping("finsp")
    public RestResult<List<NmFinspSituationShow>> finsp(@RequestBody OrgAndYear orgAndYear) {
        Integer year = orgAndYear.getYear();
        String orgCode = orgAndYear.getOrgCode();
        if (year == null) year = LocalDate.now().getYear();
        if(StringUtils.isBlank(orgCode)){
            orgCode = CustomRequestContextHolder.getOrgIdString();
        }
        // 先从缓存中获取数据（使用N000001的缓存数据）
        String cacheKey = String.format(RedisCacheConfig.FINSP_SITUATION_CACHE_KEY, "N000001", year);
        List<NmFinspSituationShow> allData = redisUtils.getList(cacheKey, NmFinspSituationShow.class);
        
        if (allData != null && !allData.isEmpty()) {
            Set<String> orgSet = new HashSet<>(orgService.selectChildOprtOrgCodes(orgCode));
            // 使用并行流处理，提高大数据量时的处理速度
            List<NmFinspSituationShow> result = allData.parallelStream()
                .filter(item -> orgSet.contains(item.getOrgCode()))
                .collect(Collectors.toList());
            
            log.info("从缓存中获取经常检查情况数据, orgCode: {}, year: {}", orgCode, year);
            return RestResult.success(result);
        }
        
        // 缓存未命中，从数据库查询
        log.info("缓存未命中，从数据库获取经常检查情况数据, orgCode: {}, year: {}", orgCode, year);
        List<NmFinspSituationShow> result = finspService.querySituationShow(orgCode, year);
        
        return RestResult.success(result);
    }
    
    /**
     * 手动刷新缓存
     * @return
     */
    @ApiRegister(value = "nmSituation:refreshCache",businessType = BusinessType.OTHER)
    @ApiOperation("手动刷新缓存")
    @PostMapping("refreshCache")
    public RestResult<String> refreshCache(@RequestBody OrgAndYear orgAndYear) {
        Integer year = orgAndYear.getYear();
        String orgCode = orgAndYear.getOrgCode();
        if (year == null) year = LocalDate.now().getYear();
        if(StringUtils.isBlank(orgCode)){
            orgCode = CustomRequestContextHolder.getOrgIdString();
        }
        
        try {
            // 构造消息内容
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("orgCode", orgCode);
            messageMap.put("year", year);
            messageMap.put("refreshTime", System.currentTimeMillis());
            messageMap.put("manual", true);  // 标记为手动刷新
            
            // 发送消息到MQ
            rabbitTemplate.convertAndSend(
                SituationMQConfig.SITUATION_REFRESH_EXCHANGE,
                SituationMQConfig.SITUATION_REFRESH_ROUTING_KEY,
                messageMap
            );
            
            log.info("手动刷新巡查情况缓存的消息已发送, orgCode: {}, year: {}", orgCode, year);
            return RestResult.success("缓存刷新指令已发送，请稍后查询");
        } catch (Exception e) {
            log.error("发送刷新巡查情况缓存的消息失败", e);
            return RestResult.error("缓存刷新失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定年份范围的日常巡查情况
     */
    @ApiRegister(value = "nmSituation:dinspByYears",businessType = BusinessType.OTHER)
    @ApiOperation("获取指定年份范围的日常巡查情况")
    @PostMapping("dinspByYears")
    public RestResult<Map<Integer, List<NmDinspSituationShow>>> dinspByYears(@RequestBody OrgAndYear orgAndYear) {
        String orgCode = StringUtils.defaultIfBlank(orgAndYear.getOrgCode(), "N000001");
        int currentYear = LocalDate.now().getYear();
        int startYear = 2025;
        
        Map<Integer, List<NmDinspSituationShow>> result = situationCacheService.getDinspSituationByYears(
            orgCode, startYear, currentYear);
        
        return RestResult.success(result);
    }

    /**
     * 获取指定年份范围的经常检查情况
     */
    @ApiRegister(value = "nmSituation:finspByYears",businessType = BusinessType.OTHER)
    @ApiOperation("获取指定年份范围的经常检查情况")
    @PostMapping("finspByYears")
    public RestResult<Map<Integer, List<NmFinspSituationShow>>> finspByYears(@RequestBody OrgAndYear orgAndYear) {
        String orgCode = StringUtils.defaultIfBlank(orgAndYear.getOrgCode(), "N000001");
        int currentYear = LocalDate.now().getYear();
        int startYear = 2025;
        
        Map<Integer, List<NmFinspSituationShow>> result = situationCacheService.getFinspSituationByYears(
            orgCode, startYear, currentYear);
        
        return RestResult.success(result);
    }
}
