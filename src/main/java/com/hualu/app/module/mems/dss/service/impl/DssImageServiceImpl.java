package com.hualu.app.module.mems.dss.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.mapper.DssImageMapper;
import com.hualu.app.module.mems.dss.service.DssImageService;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Service
public class DssImageServiceImpl extends ServiceImpl<DssImageMapper, DssImage> implements DssImageService {

    @Override
    public void saveDssImageForApp(String dssId, String fileIds, int imageType) {
        saveDssImageForApp(dssId,fileIds,imageType,null);
    }

    @Override
    public void saveDssImageForPC(String dssId, String fileIds, int imageType) {
        saveDssImageForPC(dssId,fileIds,imageType,null);
    }

    @Override
    public void saveDssImageForApp(String dssId, String fileIds, Integer imageType, Integer processing) {
        if (StrUtil.isBlank(dssId) || StrUtil.isBlank(fileIds)){
            return;
        }
        String[] split = fileIds.split(",");
        List<DssImage> dssImageList = Lists.newArrayList();
        for (String s : split) {
            DssImage item = new DssImage();
            item.setDssId(dssId);
            item.setFileId(s);
            item.setImageType(imageType);
            item.setProcessing(processing);
            dssImageList.add(item);
        }
        if (CollectionUtil.isNotEmpty(dssImageList)){
            saveBatch(dssImageList);
        }
    }

    @Override
    public void saveDssImageForPC(String dssId, String fileIds, Integer imageType, Integer processing) {
        if (StrUtil.isBlank(dssId)){
            return;
        }
        LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DssImage::getDssId,dssId).eq(DssImage::getImageType,imageType);
        remove(queryWrapper);
        if (StrUtil.isBlank(fileIds)){
            return;
        }
        String[] split = fileIds.split(",");
        List<DssImage> dssImageList = Lists.newArrayList();
        for (String s : split) {
            DssImage item = new DssImage();
            item.setDssId(dssId);
            item.setFileId(s);
            item.setImageType(imageType);
            item.setProcessing(processing);
            dssImageList.add(item);
        }
        if (CollectionUtil.isNotEmpty(dssImageList)){
            saveBatch(dssImageList);
        }
    }

    @Override
    public void delByFileId(String fileId) {
        LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DssImage::getFileId,fileId);
        remove(queryWrapper);
    }

    @Override
    public void delByDssId(String dssId) {
        LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DssImage::getDssId,dssId);
        remove(queryWrapper);
    }

    @Override
    public void delBatchByDssIds(List<String> dssIds) {
        LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DssImage::getDssId,dssIds);
        remove(queryWrapper);
    }

    @Override
    public List<DssImage> listByDssId(String dssId) {
        LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DssImage::getDssId,dssId);
        return list(queryWrapper);
    }

    @Override
    public List<String> listFileIdsByDssId(String dssId) {
        List<DssImage> dssImages = listByDssId(dssId);
        if (CollectionUtil.isEmpty(dssImages)){
            return Lists.newArrayList();
        }
        return dssImages.stream().filter(e->StrUtil.isNotBlank(e.getFileId())).map(DssImage::getFileId).collect(Collectors.toList());
    }

    @Override
    public Map<String, List<String>> mapByDssIds(Set<String> dssIds) {
        List<DssImage> images = Lists.newArrayList();
        ListUtil.partition(Lists.newArrayList(dssIds),500).forEach(rows->{
            LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DssImage::getDssId,rows);
            List<DssImage> dbImages = list(queryWrapper);
            if (CollectionUtil.isNotEmpty(dbImages)){
                images.addAll(dbImages);
            }
        });
        Map<String, List<DssImage>> groupMap = images.stream().filter(e->StrUtil.isNotBlank(e.getFileId())).collect(Collectors.groupingBy(DssImage::getDssId));
        Map<String,List<String>> imageMap = Maps.newHashMap();
        groupMap.keySet().forEach(key->{
            List<String> fileIds = groupMap.get(key).stream().map(DssImage::getFileId).collect(Collectors.toList());
            imageMap.put(key,fileIds);
        });
        return imageMap;
    }

    @Override
    public Map<String, List<DssImage>> mapGroupByDssIds(List<String> dssIds) {
        List<DssImage> images = Lists.newArrayList();
        ListUtil.partition(dssIds,500).forEach(items->{
            List<DssImage> list = lambdaQuery().in(DssImage::getDssId, items).list();
            images.addAll(list);
        });
        if (CollectionUtil.isEmpty(images)){
            return Maps.newHashMap();
        }
        Map<String, List<DssImage>> groupMap = images.stream().filter(e->StrUtil.isNotBlank(e.getFileId())).collect(Collectors.groupingBy(DssImage::getDssId));
        return groupMap;
    }
}
