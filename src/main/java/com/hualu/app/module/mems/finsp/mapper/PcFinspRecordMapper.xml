<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinspRecord">
        <id column="RECORD_ID" property="recordId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        RECORD_ID, FINSP_ID, DSS_ID, DSS_DESC, CREATE_USER_ID, CREATE_TIME, UPDATE_TIME, X, Y
    </sql>
    <select id="listByMonths" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspRecordExcelDto">
        SELECT
            a.LINE_DIRECT as LINE_DIRECTION,
            a.STRUCT_LEVEL,
            a.STRUCT_TYPE,
            a.important,
            nvl(prj.prj_name,o.org_name) as org_name,
            b.INSP_DATE,
            b.STRUCT_NAME,
            b.insp_person,
            b.INSP_ORG_NAME,
            c.DEAL_TYPE,
            c.DSS_DESC,
            c.DSSTYPE_ID
        FROM
            memsdb.PC_PROJECT_SCOPE a
        JOIN memsdb.PC_FINSP b ON a.STRUCT_ID = b.STRUCT_ID
        JOIN memsdb.PC_FINSP_RECORD c ON b.finsp_id = c.finsp_id
        join gdgs.FW_RIGHT_ORG o on b.mnt_org_id = o.org_code
        join memsdb.PC_PROJECT prj on a.prj_id = prj.prj_id
        where 1=1 and a.del_flag=0 and b.del_flag=0 and c.del_flag=0 and prj.del_flag=0
        <if test="orgCodes != null">
            and b.mnt_org_id in
            <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="prjIds != null and prjIds.size > 0">
            and prj.prj_id in
            <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="listRecordByStructId" resultType="com.hualu.app.module.mems.finsp.entity.PcFinspRecord">
        select b.*
        from PC_FINSP a join PC_FINSP_RECORD b on a.FINSP_ID = b.FINSP_ID and a.STRUCT_ID =#{structId} and b.DEAL_STATUS =#{dealStatus}
                                                      and a.del_flag=0 and b.del_flag=0
        and TO_CHAR(a.INSP_DATE, 'yyyy-MM') in
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getFinspStat" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspStatDto">
        SELECT
            o.org_name,
	        COUNT(1) as DSS_NUM,0 as deal_Dss_Num,
	        'PC' as type
        FROM
            memsdb.pc_finsp a
            JOIN memsdb.PC_FINSP_RECORD b ON a.FINSP_ID = b.FINSP_ID
            join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.org_code
        WHERE
            a.mnt_org_id in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="mntType == 1 || mntType == 2">
           and a.mnt_Type = #{mntType}
        </if>
        and EXISTS (select 1 from memsdb.PC_PROJECT_SCOPE s where a.STRUCT_ID = s.STRUCT_ID and s.prj_id in
        <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
        and a.del_flag=0 and b.del_flag=0
        GROUP BY
            o.org_name

        UNION all

        SELECT
            o.org_name,0 as DSS_NUM,
            COUNT(1) as deal_Dss_Num,
            'WX' as type
        FROM
            memsdb.pc_finsp a
            JOIN memsdb.PC_FINSP_RECORD b ON a.FINSP_ID = b.FINSP_ID and b.deal_status = 1
            join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.org_code
        WHERE
            a.mnt_org_id in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="mntType == 1 || mntType == 2">
            and a.mnt_Type = #{mntType}
        </if>
        and EXISTS (select 1 from memsdb.PC_PROJECT_SCOPE s where a.STRUCT_ID = s.STRUCT_ID and s.prj_id in
        <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
        GROUP BY
            o.org_name
    </select>
    <select id="getFinspStatByDealType" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspStatDto">
        select c.ORG_NAME,sum( case when c.DEAL_TYPE=1 then num else 0 end) xx_deal_num,
        sum( case when c.DEAL_TYPE=2 then num else 0 end) zx_deal_num,
        sum( case when c.DEAL_TYPE=3 then num else 0 end) yj_deal_num,
        sum( case when c.DEAL_TYPE=4 then num else 0 end) no_deal_num from (
        SELECT
        o.org_name,
        b.DEAL_TYPE,
        count(1) as num
        FROM
        memsdb.pc_finsp a
        JOIN memsdb.PC_FINSP_RECORD b ON a.FINSP_ID = b.FINSP_ID
        join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.org_code
        WHERE
        a.mnt_org_id in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="mntType == 1 || mntType == 2">
            and a.mnt_Type = #{mntType}
        </if>
        and EXISTS (select 1 from memsdb.PC_PROJECT_SCOPE s where a.STRUCT_ID = s.STRUCT_ID and s.prj_id in
        <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
        GROUP BY
        o.org_name,b.deal_type ) c group by c.ORG_NAME
    </select>
    <select id="getGeometryDeal" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspDealDto">
        select t.MNT_ORG_ID, t.STRUCT_ID as slope_id, t.DEAL_TYPE, t.X, t.Y, t.PRJ_ID,t.STRUCT_NAME slope_name
        from (select a.*,
                     b.MNT_ORG_ID,
                     b.STRUCT_ID,
                     c.PRJ_ID,
                     c.STRUCT_NAME,
                     row_number() over (partition by b.STRUCT_ID,b.DEAL_TYPE order by b.STRUCT_ID,b.DEAL_TYPE desc) R
              from memsdb.PC_FINSP_RECORD a
                       join memsdb.PC_FINSP b on a.FINSP_ID = b.FINSP_ID
                       join PC_PROJECT_SCOPE c on b.STRUCT_ID = c.STRUCT_ID
              where c.PRJ_ID in
                <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>

                <if test="paramDto!=null">
                    and a.x >= #{paramDto.minX} and a.x &lt;= #{paramDto.maxX} and a.y >= #{paramDto.minY} and a.y &lt;= #{paramDto.maxY}
                </if>
                and a.DEAL_TYPE in (1, 2, 3)) t
        where t.R = 1
    </select>

</mapper>
