package com.hualu.app.module.mems.mpc.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MpcContract对象", description="")
public class MpcContract implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("CONTR_ID")
    private String contrId;

    @TableField("CONTR_CODE")
    private String contrCode;

    @TableField("CONTR_NAME")
    private String contrName;

    @TableField("CONTR_TYPE")
    private String contrType;

    @TableField("SP_PRJ_ID")
    private String spPrjId;

    @TableField("SP_PRJ_CODE")
    private String spPrjCode;

    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @TableField("IMPL_ORG_ID")
    private String implOrgId;

    @TableField("IMPL_ORG_NAME")
    private String implOrgName;

    @TableField("CONTR_NO")
    private String contrNo;

    @TableField("CONTR_MOENY")
    private Double contrMoeny;

    @TableField("SIGN_DATE")
    private LocalDateTime signDate;

    @TableField("RPT_DATE")
    private LocalDateTime rptDate;

    @TableField("START_DATE")
    private LocalDateTime startDate;

    @TableField("END_DATE")
    private LocalDateTime endDate;

    @TableField("STLMT_DESC")
    private String stlmtDesc;

    @TableField("REMARK")
    private String remark;

    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("SUBMIT_TIME")
    private LocalDateTime submitTime;

    @TableField("STATUS")
    private String status;

    @TableField("SUBMIT_OPINION")
    private String submitOpinion;

    @TableField("MPITEM_SYS_ID")
    private String mpitemSysId;

    @TableField("ENABLED")
    private String enabled;


}
