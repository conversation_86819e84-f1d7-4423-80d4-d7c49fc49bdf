package com.hualu.app.module.mems.nminsp.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Accessors(chain = true)
@Data
public class NmDinspFacilityCatGroupVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 结构物设施
    private String facilityCat;
    // 总单数
    private Integer totalCount = 0;
    // 待办数量
    private Integer todoCount = 0;
    // 公用单ID
    private String commonDinspId;
}
