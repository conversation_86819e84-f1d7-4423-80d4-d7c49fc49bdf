package com.hualu.app.module.mems.finsp.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.finsp.service.DmFinspResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Api(tags = "经常检查_结论接口")
@RestController
@RequestMapping("/dmFinspResult")
public class DmFinspResultController {

    @Autowired
    DmFinspResultService resultService;

    @ApiOperation("查询经常检查单结论")
    @GetMapping("loadDmFinspResultByFinspId/{finspId}")
    public RestResult<List<DmFinspResult>> loadDmFinspResultByFinspId(@PathVariable("finspId") String finspId){
        return RestResult.success(resultService.selectResultByFinspId(finspId));
    }

    @ApiOperation("添加获取修改检查结论")
    @PostMapping(value="/saveOrUpdateDmFinspResult")
    public RestResult saveOrUpdateDmFinspResult(
            @RequestBody DmFinspResult results) {
        resultService.saveOrUpdate(results);
        return RestResult.success("操作成功");
    }
}
