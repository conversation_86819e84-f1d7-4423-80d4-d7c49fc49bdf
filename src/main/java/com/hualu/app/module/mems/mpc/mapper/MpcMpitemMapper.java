package com.hualu.app.module.mems.mpc.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.mpc.entity.MpcMpitem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
public interface MpcMpitemMapper extends BaseMapper<MpcMpitem> {

    List<MpcMpitem> selectMpitems(Page page, @Param(Constants.WRAPPER)QueryWrapper queryWrapper);

    List<MpcMpitem> selectMpitemsByDssId(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);
}
