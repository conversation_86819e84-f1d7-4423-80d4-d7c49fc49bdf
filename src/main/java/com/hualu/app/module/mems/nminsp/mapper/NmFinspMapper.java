package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.nminsp.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新版经常巡查 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface NmFinspMapper extends BaseMapper<NmFinsp> {

    NmFinsp lastNmFinsp(@Param("facilityCat") String facilityCat, @Param("orgIdString") String orgIdString);

    @Update("UPDATE NM_FINSP d SET d.DSS_NUM = ( select count(1) from NM_FINSP_RECORD b where b.finsp_id = d.finsp_id and b.del_flag =0) where d.finsp_id=#{finspId}")
    void updateDssNum(@Param("finspId") String finspId);

    @Update("update MEMSDB.NM_FINSP set status=#{status} where processinstid = #{processInstId}")
    void updateStatus(@Param("processInstId") long processInstId, @Param("status") int status);

    List<NmFinspSituationShow> querySituationShow(@Param("orgCode") String orgCode,@Param("year") int year);
    List<RoadInspectionRecord> getDssInfoFImageExport(@Param("dinspIds") List<String> dinspIds,
                                                      @Param("facilityCat") String orgId);

    IPage<RouteInspection> DailyInspectionLedger(IPage page,
                                                 @Param("orgCode") String orgCode,
                                                 @Param("dinspCode") String dinspCode,
                                                 @Param("facilityCat") String facilityCat,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("startStake") Double startStake,
                                                 @Param("endStake") Double endStake,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("dssTypeName") String dssTypeName,
                                                 @Param("facilityCategory") String facilityCategory,
                                                 @Param("lineDirect") String lineDirect,
                                                 @Param("routeName") String routeName);

    IPage<SlopeInspectionRecord> findBpDailyRecord(IPage page,
                                                   @Param("orgCode") String orgCode,
                                                   @Param("dinspCode") String dinspCode,
                                                   @Param("status") String status,
                                                   @Param("lineCode") String lineCode,
                                                   @Param("hasDssStatus") String hasDssStatus,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    IPage<BridgeInspection> findQLDailyRecord(IPage page,
                                              @Param("orgCode") String orgCode,
                                              @Param("dinspCode") String dinspCode,
                                              @Param("status") String status,
                                              @Param("lineCode") String lineCode,
                                              @Param("hasDssStatus") String hasDssStatus,
                                              @Param("startDate") String startDate,
                                              @Param("endDate") String endDate);

    IPage<HdInspectionRecord> findHdDailyRecord(IPage page,
                                              @Param("orgCode") String orgCode,
                                              @Param("dinspCode") String dinspCode,
                                              @Param("status") String status,
                                              @Param("lineCode") String lineCode,
                                              @Param("hasDssStatus") String hasDssStatus,
                                              @Param("startDate") String startDate);

    List<MtaskEntity> findDssInfoId(
            @Param("dssIds") List<String> dssIds,
            @Param("mtaskCode") String mtaskCode,
            @Param("mtaskAccptCode") String mtaskAccptCode
    );

    IPage<NmFinsp> selectPageExtra(Page<NmFinsp> page, @Param(Constants.WRAPPER) QueryWrapper<NmFinsp> ew);

  List<NmFinsp> getList();

    List<Map> getQlDetail(@Param("structId") String structId);

    List<NmFinspRecord> selectViewRecord(@Param("finspIds") List<String> finspIds,
                                         @Param("facilityCat") String facilityCat);

    List<String> deleteStructData(@Param("structsIds") List<String> structsIds,
                                  @Param("facilityCat") String facilityCat,
                                  @Param("startDate")String startDate);
}
