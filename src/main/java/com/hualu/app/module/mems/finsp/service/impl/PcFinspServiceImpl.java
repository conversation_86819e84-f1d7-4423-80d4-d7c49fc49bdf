package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspDownload;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.mapper.PcFinspMapper;
import com.hualu.app.module.mems.finsp.service.*;
import com.hualu.app.module.mems.finsp.upload.IImportStruct;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 结构物排查表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class PcFinspServiceImpl extends ServiceImpl<PcFinspMapper, PcFinsp> implements PcFinspService {

    public static String finspCode = "PCZZ-{}-{}-{}";

    @Autowired
    PcFinspResultService resultService;

    @Autowired
    PcFinspRecordService recordService;

    @Autowired
    PcFinspGpsService gpsService;

    @Lazy
    @Autowired
    PcFinspImageService imageService;

    @Autowired
    PcFinspDownloadService downloadService;


    @Autowired
    PcProjectService projectService;

    @Value("${fileReposity}")
    String fileReposity;

    @Override
    public List<PcFinsp> listByQuarter(String structId, String facilityCat, Date date,String mntType) {
        List<String> months = H_FacQueryHelper.rangeList(date);
        List<PcFinsp> dmFinsps = baseMapper.listByCycle(structId, facilityCat, months,mntType);
        return dmFinsps;
    }

    @Override
    public String getFinspId(String structId, String facilityCat, Date date,String mntType) {
        LambdaQueryWrapper<PcFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PcFinsp::getFinspId);
        queryWrapper.eq(PcFinsp::getStructId,structId).eq(PcFinsp::getFacilityCat,facilityCat);
        queryWrapper.eq(StrUtil.isNotBlank(mntType),PcFinsp::getMntType,mntType);
        //queryWrapper.apply("TO_CHAR( INSP_DATE, 'yyyy' ) = {0}", DateUtil.format(date,"yyyy"));
        List<PcFinsp> list = list(queryWrapper);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        return list.get(0).getFinspId();
    }


    @Override
    public String getNextCode(String facilityCat, String orgEn) {
        String curMonth = String.valueOf(DateTimeUtil.getCurMonth());
        curMonth= StringUtil.generateCode(curMonth, '0', 2) ;
        String strCode = StrUtil.format(finspCode,facilityCat,orgEn,DateTimeUtil.getCurYear()+curMonth);

        QueryWrapper<PcFinsp> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(substr(FINSP_CODE,length(FINSP_CODE)-3)) as finsp_code");
        queryWrapper.like("FINSP_CODE",strCode);
        queryWrapper.eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString());

        PcFinsp dmFinsp = baseMapper.selectOne(queryWrapper);

        long nextId = dmFinsp == null ? 0 : Math.abs(Long.valueOf(dmFinsp.getFinspCode()));
        ++nextId;
        String day = String.valueOf(DateTimeUtil.getCurDay());
        day = StringUtil.generateCode(day, '0', 2);
        return strCode + day + "-" + StringUtil.generateCode(String.valueOf(nextId), '0', 4);
    }

    @Override
    public void updateDssNum(String finspId) {
        baseMapper.updateDssNum(finspId);
    }

    @Override
    public void saveOrUpdateFinsp(PcFinsp finsp) {
        boolean isAdd = StrUtil.isBlank(finsp.getFinspId()) ? true : false;
        if (isAdd){
            finsp.setFinspId(H_KeyWorker.nextIdToString());
            resultService.createFinspResult(finsp.getFinspId(),finsp.getFacilityCat());
        }
        saveOrUpdate(finsp);
    }

    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delPcFinsp(String finspId) {
        try {
            PcFinsp pcFinsp = getById(finspId);
            if (pcFinsp == null){
                return;
            }
            List<String> finspIds = Lists.newArrayList(finspId);
            recordService.removeByFinspIds(finspIds);
            resultService.removeByFinspIds(finspIds);
            gpsService.removeByFinspIds(finspIds);
            imageService.removeByRecordIds(finspIds);
            removeByIds(finspIds);
            //todo 暂未实现流程，先隐藏
            //H_WorkFlowHelper.deleteProcessInstance(pcFinsp.getProcessinstid());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException("经常检查单删除失败:"+e.getMessage());
        }
    }

    @Override
    public PcFinspWordDto getFinspWord(String finspId) {
        List<PcFinspWordDto> wordDtos = baseMapper.listFinspWord(Lists.newArrayList(finspId),null);
        if (CollectionUtil.isEmpty(wordDtos)){
            return null;
        }
        return wordDtos.get(0);
    }

    @Override
    public List<PcFinspWordDto> listFinspWord(List<String> finspIds,String prjId) {
        List<PcFinspWordDto> wordDtos = baseMapper.listFinspWord(finspIds,prjId);
        return wordDtos;
    }

    @Override
    public void removeByStructIds(List<String> structIds) {
        if (CollectionUtil.isEmpty(structIds)){
            return;
        }
        LambdaQueryWrapper<PcFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PcFinsp::getStructId,structIds);
        queryWrapper.select(PcFinsp::getFinspId);
        List<PcFinsp> finsps = list(queryWrapper);

        List<String> finspIds = finsps.stream().map(PcFinsp::getFinspId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(finspIds)){
            return;
        }
        recordService.removeByFinspIds(finspIds);
        resultService.removeByFinspIds(finspIds);
        gpsService.removeByFinspIds(finspIds);
        imageService.removeByRecordIds(finspIds);
        removeByIds(finspIds);
        //todo 暂未实现流程，先隐藏
        //H_WorkFlowHelper.deleteProcessInstance(pcFinsp.getProcessinstid());
    }

    @Override
    public String exportWordByZip(String finspIds, String prjId, String facilityCat) {
        String fileName = "/"+IdUtil.fastSimpleUUID();
        String filePath = fileReposity+fileName+"/";
        if (!FileUtil.exist(filePath)){
            FileUtil.mkdir(filePath);
        }
        PcFinspDownload download = new PcFinspDownload();
        download.setDataId(H_KeyWorker.nextIdToString()).setPrjId(prjId).setState(0).setRemark("数据正在生成")
                .setCreateUserId(CustomRequestContextHolder.getUserCode()).setDataName(getDownloadName(prjId))
                .setDataUrl("fileResposity"+fileName+".zip").setMntOrgId(CustomRequestContextHolder.getOrgIdString());
        downloadService.checkRunning(download);
        List<String> finspIdList = Lists.newArrayList();
        if (StrUtil.isNotBlank(finspIds)){
            finspIdList = StrUtil.split(finspIds, ",");
        }

        try {
            Map<String, IImportStruct> beans = CustomApplicationContextHolder.getBeansOfType(IImportStruct.class);
            for (IImportStruct value : beans.values()) {
                if (value.getFacilityCat().equals(facilityCat)){
                    value.exportWordByZip(filePath,finspIdList,prjId);
                }
            }
            File zip = ZipUtil.zip(filePath);
            FileUtil.del(filePath);
            downloadService.updateState(download.getDataId(),1);
            //导出水中桩
            return zip.getName();
        }catch (Exception e){
            downloadService.updateState(download.getDataId(),-1);
            throw new BaseException(e.getMessage());
        }
    }

    /**
     * 获取下载名称
     * @param prjId
     * @return
     */
    private String getDownloadName(String prjId){
        PcProject pcProject = projectService.getById(prjId);
        if (pcProject == null){
            throw new BaseException("项目不存在");
        }
        String facilityCat = pcProject.getFacilityCat();
        String fileName = "";
        if ("QL".equals(facilityCat)){
            fileName = "桥梁水中桩";
        }else if ("HD".equals(facilityCat)){
            fileName = "过水涵洞";
        }else if ("BP".equals(facilityCat)){
            fileName ="边坡"+ (pcProject.getMntType().equals("1") ? "(定检单位)" : "(养护单位)");
        }
        return fileName;
    }

    @Override
    public void removeByPrjId(String prjId) {
        List<String> finspIds = baseMapper.listFinspIdByPrjId(prjId);
        if (CollectionUtil.isEmpty(finspIds)){
            return;
        }
        recordService.removeByFinspIds(finspIds);
        resultService.removeByFinspIds(finspIds);
        gpsService.removeByFinspIds(finspIds);
        imageService.removeByRecordIds(finspIds);
        removeByIds(finspIds);
    }

    @Override
    public String exportWord(String finspId, String qlType) {
        PcFinsp pcFinsp = getById(finspId);
        String fileName = null;
        Map<String, IImportStruct> beans = CustomApplicationContextHolder.getBeansOfType(IImportStruct.class);
        for (IImportStruct value : beans.values()) {
            if (value.getFacilityCat().equals(pcFinsp.getFacilityCat())){
                fileName = value.exportWord(pcFinsp,qlType);
                break;
            }
        }
        return fileName;
    }

    @Override
    public List<PcProjectExportDto> exportWordData(List<String> finspIds, String prjId) {
        List<PcProjectExportDto> projectDtos = baseMapper.exportData(finspIds,prjId);
        return projectDtos;
    }

    @Override
    public Map<String, PcFinsp> getDssNum(List<String> structIds) {
        if (CollectionUtil.isEmpty(structIds)){
            return Maps.newHashMap();
        }
        List<PcFinsp> dssList = baseMapper.listDssNumByStructIds(structIds);

        Map<String, PcFinsp> finspMap = dssList.stream().collect(Collectors.toMap(PcFinsp::getStructId, Function.identity()));
        return finspMap;
    }
}
