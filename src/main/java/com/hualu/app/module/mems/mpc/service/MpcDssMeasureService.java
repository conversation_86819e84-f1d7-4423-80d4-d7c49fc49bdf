package com.hualu.app.module.mems.mpc.service;

import com.hualu.app.module.mems.mpc.dto.MpitemSystemDto;
import com.hualu.app.module.mems.mpc.entity.MpcDssMeasure;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
public interface MpcDssMeasureService extends IService<MpcDssMeasure> {


    List<MpitemSystemDto> fetchMpitemSystems(Integer year);

    List<String> listDssType(String orgId,String mpSystemId);
}
