package com.hualu.app.module.mems.dss.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 病害维修
 */
@Data
public class DssWxDto implements Serializable {

    //病害ID
    private String dssId;

    //细目名称
    private String mpitemName;

    //工程数量
    private Double mpitemAccount;

    //单位
    private String measureUnit;

    //要求完成时间
    private Date askedComplDate;

    //复核人
    private String acceptUser;

    //复核时间
    private Date acceptDate;

    //复核结果
    private String acceptComment;
}
