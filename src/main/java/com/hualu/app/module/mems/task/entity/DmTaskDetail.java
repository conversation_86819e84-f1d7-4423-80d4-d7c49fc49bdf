package com.hualu.app.module.mems.task.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmTaskDetail对象", description="")
public class DmTaskDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    //措施编码
    @TableField(exist = false)
    private String mpitemCode;

    //措施名称
    @TableField(exist = false)
    private String mpitemName;

    //是否选中
    @TableField(exist = false)
    private Boolean selected = false;

    //单价
    @TableField(exist = false)
    private Double unitPrice;

    /**
     * 主键
     */
    @TableId("MTASK_DTL_ID")
    private String mtaskDtlId;

    /**
     * 任务单ID
     */
    @NotBlank
    @TableField("MTASK_ID")
    private String mtaskId;

    /**
     * 病害ID
     */
    @NotBlank
    @TableField("DSS_ID")
    private String dssId;

    /**
     * 措施ID
     */
    @NotBlank
    @TableField("MPITEM_ID")
    private String mpitemId;

    /**
     * 措施单位
     */
    @NotBlank
    @TableField("MEASURE_UNIT")
    private String measureUnit;

    /**
     * 勘估数量
     */
    @NotNull
    @TableField("MPITEM_ACCOUNT")
    private Double mpitemAccount;

    /**
     * 是否包干
     */
    @TableField("IS_LUMP")
    private Integer isLump;

    /**
     * 备注
     */
    @TableField("REMARK")
    private String remark;

    /**
     * 验收状态
     */
    @TableField("ACCEPT_STATUS")
    private Integer acceptStatus;

    /**
     * 是否添加
     */
    @TableField("IS_ADD")
    private Integer isAdd;
}
