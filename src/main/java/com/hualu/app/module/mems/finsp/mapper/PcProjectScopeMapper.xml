<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcProjectScopeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcProjectScope">
        <result column="PRJ_ID" property="prjId" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="STRUCT_TYPE" property="structType" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="STRUCT_LEVEL" property="structLevel" />
        <result column="STRUCT_POSITION" property="structPosition" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="MD5" property="md5" />
        <result column="STRUCT_ID" property="structId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        SCOPE_ID, PRJ_ID, LINE_DIRECT, STRUCT_TYPE, STRUCT_NAME, STRUCT_LEVEL, STRUCT_POSITION, FACILITY_CAT, X, Y, MNT_ORG_ID, CREATE_TIME, MD5, STRUCT_ID
    </sql>
    <update id="updateIgnoreDelField">
        update memsdb.pc_project_scope a set a.del_flag = 0 where a.struct_id in
        <foreach collection="structIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <select id="selectByStructId" resultType="com.hualu.app.module.mems.finsp.entity.PcProjectScope">
        SELECT
            b.*,
            a.INSP_ORG_NAME
        FROM
            memsdb.pc_project a
                JOIN memsdb.pc_project_scope b ON a.PRJ_ID = b.PRJ_ID
                AND b.STRUCT_ID = #{structId} and a.del_flag=0 and b.del_flag=0
    </select>
    <select id="checkStat" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspStatDto">
        SELECT
        o.org_name,
        count( 1 ) as checked_Num,0 as not_checked_Num,
        'PC' as type
        FROM
        memsdb.PC_PROJECT_SCOPE a join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.ORG_CODE
        join MEMSDB.PC_FINSP c on a.struct_id = c.struct_id and c.mnt_type = 1
        WHERE  a.del_flag=0 and c.del_flag=0 and
        EXISTS ( SELECT 1 FROM memsdb.PC_FINSP b WHERE a.STRUCT_ID = b.STRUCT_ID )
        and a.mnt_org_id in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND a.PRJ_ID in
        <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="mntType != null">
            and c.mnt_type = #{mntType}
        </if>
        GROUP BY o.org_name

        union all

        SELECT
        o.org_name,0 as checked_Num,
        count( 1 ) as not_checked_Num,
        'WX' as type
        FROM
        memsdb.PC_PROJECT_SCOPE a join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.ORG_CODE and a.del_flag=0
        WHERE
        NOT EXISTS ( SELECT 1 FROM memsdb.PC_FINSP b WHERE a.STRUCT_ID = b.STRUCT_ID and b.del_flag=0)
        and a.mnt_org_id in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND a.PRJ_ID in
        <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="mntType != null">
            and c.mnt_type = #{mntType}
        </if>
        GROUP BY o.org_name
    </select>
    <select id="listStructType" resultType="java.lang.String">
        select struct_type from memsdb.pc_project_scope a where a.del_flag=0 and
         a.PRJ_ID in
        <foreach collection="prjIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="listByMd5" resultType="com.hualu.app.module.mems.finsp.entity.PcProjectScope">
        select * from memsdb.pc_project_scope a where a.md5 in
        <foreach collection="md5s" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and a.prj_id = #{prjId}
    </select>
</mapper>
