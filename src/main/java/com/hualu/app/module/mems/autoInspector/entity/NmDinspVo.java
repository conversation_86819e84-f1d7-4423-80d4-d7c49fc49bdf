package com.hualu.app.module.mems.autoInspector.entity;

import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 日常巡检记录VO
 * <p>
 * 封装日常巡检记录和巡检记录项的值对象
 * </p>
 * 
 * <AUTHOR>
 * @since 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NmDinspVo extends NmDinsp {
    
    /**
     * 日常巡检记录项列表
     */
    private List<NmDinspRecord> records;
} 