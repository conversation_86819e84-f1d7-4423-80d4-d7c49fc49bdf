package com.hualu.app.module.mems.notice.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.notice.entity.DmNotice;
import com.hualu.app.module.mems.notice.entity.DmNoticeDetail;
import com.hualu.app.module.mems.notice.service.DmNoticeDetailService;
import com.hualu.app.module.mems.notice.service.DmNoticeService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 路政通知书
 *
 * <AUTHOR>
 * @since 2023-08-30 09:57
 */
@RestController
@RequestMapping("/roadNotice")
@Validated
public class RoadNoticeController {

  @Resource
  private DmNoticeService dmNoticeService;

  @Resource
  private DmNoticeDetailService detailService;

  /**
   * 获取路政通知数主单
   *
   * @param startDate 开始日期
   * @param endDate   结束日期
   * @param keyword   模糊搜索、单号、内容、桩号
   * @param lineCode  路线编码
   * @param status    状态、0待办，1已办，2办结
   * @return
   */
  @GetMapping("/getRoadNoticeList")
  public RestResult<List<DmNotice>> getRoadNoticeList(
      @RequestParam(value = "startDate", required = false) String startDate,
      @RequestParam(value = "endDate", required = false) String endDate,
      @RequestParam(value = "keyword", required = false) String keyword,
      @RequestParam(value = "lineCode", required = false) String lineCode,
      @RequestParam(value = "status", defaultValue = "0", required = false) int status,
      @RequestParam(value = "page", defaultValue = "1", required = false) long page,
      @RequestParam(value = "pageSize", defaultValue = "20", required = false) long pageSize
  ) {
    IPage<DmNotice> p = new Page<>(page, pageSize);
    p = dmNoticeService.getRoadNoticeList(p, startDate, endDate, keyword, lineCode, status);
    return RestResult.success(p.getRecords(), p.getTotal(), page, pageSize);
  }

  /**
   * 保存或者修改路政通知单
   *
   * @param dmNotice 路政通知单
   * @param files    文件
   * @return
   */
  @PostMapping("/saveOrUpdateDmNotice")
  public RestResult<DmNotice> saveOrUpdateDmNotice(
      @Validated
      @RequestPart(value = "dmNotice") DmNotice dmNotice,
      @RequestPart(value = "files", required = false) MultipartFile[] files
  ) {
    return RestResult.success(dmNoticeService.saveOrUpdateDmNotice(dmNotice, files), "保存成功");
  }

  /**
   * 获取默认路政通知单
   *
   * @return
   */
  @PostMapping("/getDefaultNotice")
  public RestResult<DmNotice> getDefaultNotice() {
    return RestResult.success(dmNoticeService.getDefaultNotice(), "获取成功");
  }

  /**
   * 获取路政通知书详情
   *
   * @param noticeId 路政通知书id
   * @return
   */
  @PostMapping("/getNoticeDetailList")
  public RestResult<List<DmNoticeDetail>> getNoticeDetailList(
      String noticeId,
      @RequestParam(value = "page", defaultValue = "1", required = false) long page,
      @RequestParam(value = "pageSize", defaultValue = "20", required = false) long pageSize
  ) {
    IPage<DmNoticeDetail> p = new Page<>(page, pageSize);
    p = detailService.getNoticeList(p, noticeId);
    return RestResult.success(p.getRecords(), p.getTotal(), page, pageSize);
  }

  /**
   * 删除路政通知书
   *
   * @param noticeId 通知书id
   * @return
   */
  @PostMapping("/deleteNoticeById")
  public RestResult<String> deleteNoticeById(
      String noticeId
  ) {
    boolean successful = dmNoticeService.deleteNoticeById(noticeId);
    return successful ? RestResult.success("删除成功") : RestResult.error("删除失败");
  }

  /**
   * 删除路政通知书详情
   *
   * @param noticeDetailId 通知书详情id
   * @return
   */
  @PostMapping("/deleteNoticeDetailById")
  public RestResult<String> deleteNoticeDetailById(
      String noticeDetailId
  ) {
    boolean successful = detailService.deleteNoticeDetailById(noticeDetailId);
    return successful ? RestResult.success("删除成功") : RestResult.error("删除失败");
  }

  /**
   * 获取路政通知书详情
   *
   * @param noticeDetailId 为空时默认一条数据
   */
  @PostMapping("/getRoadNoticeDetail")
  public RestResult<DmNoticeDetail> getRoadNoticeDetail(
      @RequestParam(value = "noticeDetailId", required = false) String noticeDetailId
  ) {
    DmNoticeDetail noticeDetail = detailService.getRoadNoticeDetail(noticeDetailId);
    return RestResult.success(noticeDetail);
  }

  @PostMapping("/saveOrUpdateNoticeDetail")
  public RestResult<String> saveOrUpdateNoticeDetail(
      @Validated
      @RequestPart(value = "dmNoticeDetail") DmNoticeDetail dmNoticeDetail,
      @RequestPart(value = "files", required = false) MultipartFile[] files
  ) {
    boolean successful = detailService.saveOrUpdateNoticeDetail(dmNoticeDetail, files);
    return successful ? RestResult.success("保存成功") : RestResult.error("保存失败");
  }
}
