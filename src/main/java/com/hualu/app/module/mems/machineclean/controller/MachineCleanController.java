package com.hualu.app.module.mems.machineclean.controller;

import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.machineclean.entity.DmMachineCleaning;
import com.hualu.app.module.mems.machineclean.entity.MachineCleaningTrack;
import com.hualu.app.module.mems.machineclean.service.DmMachineCleaningService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 机械清扫控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-24 15:44
 */
@RestController
@RequestMapping("/machineClean")
public class MachineCleanController {

  @Resource
  private DmMachineCleaningService machineCleaningService;

  /**
   * 获取机械清扫列表
   *
   * @param page     当前页
   * @param pageSize 页数
   * @return
   */
  @GetMapping("/getMachineCleanList")
  public RestResult<List<DmMachineCleaning>> getMachineCleanList(
      @RequestParam(value = "page", defaultValue = "1") int page,
      @RequestParam(value = "pageSize", defaultValue = "20") int pageSize
  ) {
    return machineCleaningService.getMachineCleanList(page, pageSize);
  }

  /**
   * 获取上次机械清扫记录
   * @return
   */
  @GetMapping("/getLastMachineCleanRecord")
  public RestResult<DmMachineCleaning> getLastMachineCleanRecord() {
    return machineCleaningService.getLastMachineCleanRecord();
  }

  /**
   * 保存机械清扫主单
   * @param machineClean 机械清扫主单
   * @param startImage   清扫前照片
   * @param endImage     清扫后照片
   * @return
   */
  @PostMapping("/saveMachineClean")
  public RestResult<String> saveMachineClean(
      @RequestPart(value = "machineClean") DmMachineCleaning machineClean,
      @RequestPart(value = "startImage", required = false) MultipartFile startImage,
      @RequestPart(value = "endImage", required = false) MultipartFile endImage
  ) {
    return machineCleaningService.saveMachineClean(machineClean, startImage, endImage);
  }

  /**
   * 上传机械清扫轨迹
   * @param tracks 轨迹集合
   * @return
   */
  @PostMapping("/uploadTrack")
  public RestResult<String> uploadTrack(
      @RequestPart(value = "trackList") List<MachineCleaningTrack> tracks
  ) {
    return machineCleaningService.uploadTrack(tracks);
  }

  /**
   * 获取机械清扫轨迹
   * @param machineCleanId 机械清扫id
   * @return
   */
  @GetMapping("/getMachineCleanTrack")
  public RestResult<List<MachineCleaningTrack>> getTrack(String machineCleanId) {
    return machineCleaningService.getTrack(machineCleanId);
  }

  /**
   * 删除机械清扫记录
   * @param machineCleanId 机械清扫id
   * @return
   */
  @GetMapping("/deleteMachineClean")
  public RestResult<String> deleteMachineClean(String machineCleanId) {
    return machineCleaningService.deleteMachineClean(machineCleanId);
  }

  /**
   * 删除机械清扫图片
   * @param machineCleanId 机械清扫id
   * @param fileId 文件id
   * @param whetherBeforeImage 是否清扫前照片
   * @return
   */
  @GetMapping("/deleteMachineCleanImage")
  public RestResult<String> deleteMachineCleanImage(
      String machineCleanId,
      String fileId,
      Boolean whetherBeforeImage
  ) {
    return machineCleaningService.deleteMachineCleanImage(
        machineCleanId,
        fileId,
        whetherBeforeImage
    );
  }

}
