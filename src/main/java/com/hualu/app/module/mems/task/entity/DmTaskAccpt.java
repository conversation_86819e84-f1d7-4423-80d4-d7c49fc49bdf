package com.hualu.app.module.mems.task.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 维修验收单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmTaskAccpt对象", description="维修验收单")
public class DmTaskAccpt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("MTASK_ACCPT_ID")
    private String mtaskAccptId;

    @ApiModelProperty(value = "验收单编码")
    @TableField("MTASK_ACCPT_CODE")
    private String mtaskAccptCode;

    @ApiModelProperty(value = "管养单位ID")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "养护单位名称")
    @TableField("MNTN_ORG_NAME")
    private String mntnOrgName;

    @ApiModelProperty(value = "养护项目部")
    @TableField("MNTN_DEPT_NM")
    private String mntnDeptNm;

    @ApiModelProperty(value = "维修业务类型  1 计划性维修保养 2 突发性维修保养")
    @TableField("BUSINESS_TYPE")
    private Integer businessType;

    @ApiModelProperty(value = "申请验收人")
    @TableField("APPLY_USER")
    private String applyUser;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "申请验收日期")
    @TableField("APPLY_DATE")
    private LocalDate applyDate;

    @ApiModelProperty(value = "申请验收说明")
    @TableField("APPLY_COMMENT")
    private String applyComment;

    @ApiModelProperty(value = "验收人")
    @TableField("ACCEPT_USER")
    private String acceptUser;

    @ApiModelProperty(value = "验收日期")
    @TableField("ACCEPT_DATE")
    private LocalDate acceptDate;

    @ApiModelProperty(value = "验收评语")
    @TableField("ACCEPT_COMMENT")
    private String acceptComment;

    @ApiModelProperty(value = "是否按时完成 0 否 1 是")
    @TableField("IS_IN_TIME")
    private Integer isInTime;

    @ApiModelProperty(value = "备注说明")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "流程ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private Integer status;

    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "任务单主单ID")
    @TableField("MTASK_ID")
    private String mtaskId;

    @ApiModelProperty(value = "计量日期")
    @TableField("METER_DATE")
    private String meterDate;

    @ApiModelProperty(value = "是否清账，1：是，0：否")
    @TableField("IS_CLEAR")
    private Integer isClear;

    //修复状态显示名称
    @TableField(exist = false)
    private String statusName;

    //是否按时完成显示名称
    @TableField(exist = false)
    private String isInTimeName;

    //验收单明细ID
    @TableField(exist = false)
    private List<String> mtaskdtlIds = new ArrayList<>();
}
