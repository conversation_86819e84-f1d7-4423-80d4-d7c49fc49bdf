package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 高速公路病害媒体资源表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_DEFECT_MEDIA")
@ApiModel(value="HighwayDefectMedia对象", description="高速公路病害媒体资源表")
public class HighwayDefectMedia implements Serializable {

    
    @NotBlank(message="[媒体ID]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("媒体ID")
    @TableField("ID")
    private String id;
    
    @NotBlank(message="[关联病害ID]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("关联病害ID")
    @TableField("DEFECT_ID")
    private String defectId;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("图片名称")
    @TableField("NAME")
    private String name;
    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("图片地址")
    @TableField("IMG_URL")
    private String imgUrl;
    
    @Size(max= 500,message="编码长度不能超过500")
    @ApiModelProperty("缩略图地址")
    @TableField("ROI_URL")
    private String roiUrl;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("图片框，如：682,558,268,181")
    @TableField("RECT")
    private String rect;
    
    @ApiModelProperty("子目标ID")
    @TableField("SUB_ID")
    private Integer subId;
    
    @ApiModelProperty("此媒体记录的病害长度（米）")
    @TableField("TARGET_LEN")
    private BigDecimal targetLen;
    
    @ApiModelProperty("此媒体记录的病害面积（平方米）")
    @TableField("TARGET_AREA")
    private BigDecimal targetArea;
    
    @ApiModelProperty("是否忽略计算，0：否，1：是")
    @TableField("IGNORE_CALC")
    private Integer ignoreCalc;
    
    @Size(max= 200,message="编码长度不能超过200")
    @ApiModelProperty("本地文件地址")
    @TableField("LOCAL_PATH")
    private String localPath;

    @ApiModelProperty("原ID")
    @TableField("OG_ID")
    private String ogId;
}
