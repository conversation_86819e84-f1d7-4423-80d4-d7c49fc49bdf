package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 新版日常巡查检查结论表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmDinspResult对象", description="新版日常巡查检查结论表")
public class NmDinspResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("RES_ID")
    private String resId;

    @ApiModelProperty(value = "检查单ID")
    @TableField("DINSP_ID")
    private String dinspId;

    @ApiModelProperty(value = "配置项ID")
    @TableField("ITEM_ID")
    private String itemId;

    @ApiModelProperty(value = "问题描述/病害描述/检查情况")
    @TableField(value = "ISSUE_DESC",strategy = FieldStrategy.IGNORED)
    private String issueDesc;

    @ApiModelProperty(value = "处理结果")
    @TableField(value = "DEAL_RESULT",strategy = FieldStrategy.IGNORED)
    private String dealResult;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @TableLogic
    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 检查内容
     */
    @TableField(exist = false)
    private String inspCont;

    /**
     * 检查项
     */
    @TableField(exist = false)
    private String inspCom;

    /**
     * 关联病害类型
     */
    @TableField(exist = false)
    private String dssType;

    /**
     * 关联部位ID
     */
    @TableField(exist = false)
    private String partId;
}
