<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.DmFinspRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.DmFinspRecord">
        <id column="DSS_ID" property="dssId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="STAKE" property="stake" />
        <result column="STRUCT_PART_ID" property="structPartId" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_DEGREE" property="dssDegree" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="LANE" property="lane" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="DSS_L" property="dssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="DSS_W" property="dssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="DSS_D" property="dssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="DSS_N" property="dssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="DSS_A" property="dssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="DSS_V" property="dssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="DSS_P" property="dssP" />
        <result column="DSS_G" property="dssG" />
        <result column="DSS_IMP_FLAG" property="dssImpFlag" />
        <result column="DSS_QUALITY" property="dssQuality" />
        <result column="HIS_DSS_ID" property="hisDssId" />
        <result column="DSS_CAUSE" property="dssCause" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="FINSP_ITEM_ID" property="finspItemId" />
        <result column="ISPHONE" property="isphone" />
        <result column="RAMP_ID" property="rampId" />
        <result column="STRUCT_ID" property="structId" />
        <result column="L_DSS_ID" property="lDssId" />
        <result column="TUNNEL_MOUTH" property="tunnelMouth" />
        <result column="START_HIGH" property="startHigh" />
        <result column="END_HIGH" property="endHigh" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="STAKE_HIGH" property="stakeHigh" />
        <result column="LINING_STRUCTURE" property="liningStructure" />
        <result column="MARKING_LINE_POSITION" property="markingLinePosition" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_ID, FINSP_ID, LINE_DIRECT, STAKE, STRUCT_PART_ID, STRUCT_COMP_ID, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, LANE, DSS_POSITION, DSS_DESC, DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V, DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY, HIS_DSS_ID, DSS_CAUSE, X, Y, FINSP_ITEM_ID, ISPHONE, RAMP_ID, STRUCT_ID, L_DSS_ID, TUNNEL_MOUTH, START_HIGH, END_HIGH, START_STAKE_NUM, END_STAKE_NUM, STAKE_HIGH, LINING_STRUCTURE, MARKING_LINE_POSITION
    </sql>

    <insert id="insertRecordByQH">
        insert into dm_finsp_record
        (DSS_ID, FINSP_ID, LINE_DIRECT, STAKE, STRUCT_PART_ID, STRUCT_COMP_ID, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, LANE,
         DSS_POSITION, DSS_DESC,
         DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V,
         DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY,
         HIS_DSS_ID, DSS_CAUSE, X, Y, FINSP_ITEM_ID, ISPHONE, RAMP_ID, STRUCT_ID, L_DSS_ID)
        Select get_uuid(),
               #{newFinspId},
               t.LINE_DIRECT,
               t.STAKE,
               t.STRUCT_PART_ID,
               t.STRUCT_COMP_ID,
               t.DSS_TYPE,
               t.DSS_DEGREE,
               t.MNTN_ADVICE,
               t.LANE,
               t.DSS_POSITION,
               t.DSS_DESC,
               t.DSS_L,
               t.DSS_L_UNIT,
               t.DSS_W,
               t.DSS_W_UNIT,
               t.DSS_D,
               t.DSS_D_UNIT,
               t.DSS_N,
               t.DSS_N_UNIT,
               t.DSS_A,
               t.DSS_A_UNIT,
               t.DSS_V,
               t.DSS_V_UNIT,
               t.DSS_P,
               t.DSS_G,
               t.DSS_IMP_FLAG,
               t.DSS_QUALITY,
               t.his_dss_id,
               t.DSS_CAUSE,
               t.X,
               t.Y,
               t.FINSP_ITEM_ID,
               t.ISPHONE,
               t.RAMP_ID,
               t.STRUCT_ID,
               info.dss_id
        From DM_FINSP_RECORD t
                 left join dss_info info on (info.dss_id = t.dss_id or info.dss_id = t.l_dss_id)
        where t.finsp_id = #{oldFinspId}
          and info.deal_status = 0
    </insert>


    <insert id="insertRecordByOther">
        insert into dm_finsp_record
        (DSS_ID, FINSP_ID, LINE_DIRECT, STAKE, STRUCT_PART_ID, STRUCT_COMP_ID, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, LANE,
         DSS_POSITION, DSS_DESC,
         DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V,
         DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY,
         HIS_DSS_ID, DSS_CAUSE, X, Y, FINSP_ITEM_ID, ISPHONE, RAMP_ID, STRUCT_ID, L_DSS_ID, TUNNEL_MOUTH, STAKE_HIGH,
         START_STAKE_NUM, END_STAKE_NUM, START_HIGH, END_HIGH)
        Select get_uuid(),
               #{newFinspId},
               t.LINE_DIRECT,
               t.STAKE,
               t.STRUCT_PART_ID,
               t.STRUCT_COMP_ID,
               t.DSS_TYPE,
               t.DSS_DEGREE,
               t.MNTN_ADVICE,
               t.LANE,
               t.DSS_POSITION,
               t.DSS_DESC,
               t.DSS_L,
               t.DSS_L_UNIT,
               t.DSS_W,
               t.DSS_W_UNIT,
               t.DSS_D,
               t.DSS_D_UNIT,
               t.DSS_N,
               t.DSS_N_UNIT,
               t.DSS_A,
               t.DSS_A_UNIT,
               t.DSS_V,
               t.DSS_V_UNIT,
               t.DSS_P,
               t.DSS_G,
               t.DSS_IMP_FLAG,
               t.DSS_QUALITY,
               t.his_dss_id,
               t.DSS_CAUSE,
               t.X,
               t.Y,
               t.FINSP_ITEM_ID,
               t.ISPHONE,
               t.RAMP_ID,
               t.STRUCT_ID,
               info.dss_id,
               t.TUNNEL_MOUTH,
               t.STAKE_HIGH,
               t.START_STAKE_NUM,
               t.END_STAKE_NUM,
               t.START_HIGH,
               t.END_HIGH
        From DM_FINSP_RECORD t
                 left join dss_info info on (info.dss_id = t.dss_id or info.dss_id = t.l_dss_id)
        where t.finsp_id = #{oldFinspId}
          and info.deal_status = 0
    </insert>

    <insert id="insertRecordByJA">
        insert into DM_FINSP_RECORD(dss_id, finsp_id, line_direct, stake, struct_part_id, struct_comp_id, dss_type,
        dss_degree, mntn_advice, lane, dss_position, dss_desc, dss_l, dss_l_unit, dss_w, dss_w_unit, dss_d, dss_d_unit,
        dss_n, dss_n_unit, dss_a, dss_a_unit, dss_v, dss_v_unit, dss_p, dss_g, dss_imp_flag, dss_quality, his_dss_id,
        dss_cause, x, y, finsp_item_id, isphone, ramp_id, struct_id, l_dss_id, tunnel_mouth, stake_high,
        start_stake_num, end_stake_num, start_high, end_high) select sys_guid() ,#{newFinspId},lane as
        line_direct,rl_stake_new as stake, struct_part_id, struct_comp_id, dss_type, dss_degree, mntn_advice, lane,
        dss_position, dss_desc, dss_l, dss_l_unit, dss_w, dss_w_unit, dss_d, dss_d_unit, dss_n, dss_n_unit, dss_a,
        dss_a_unit, dss_v, dss_v_unit, dss_p, dss_g, dss_imp_flag, dss_quality, his_dss_id, dss_cause, x, y,null,'0',
        ramp_id, struct_id,dss_id as l_dss_id,null as tunnel_mouth, null as stake_high,null as start_stake_num,null as
        end_stake_num,null as start_high, end_high from dss_info info where (#{startStake} - nvl(info.rl_stake_new,0)) *(#{endStake} - nvl(info.rl_stake_new,0)) &lt;= 0
        and info.deal_status = 0 and info.facility_cat='JA' and info.main_road_id=#{lineId} and info.DSS_SOURCE='1' and
        info.dss_type!='BJ-0001' and info.dss_type!='BJ-0006' and info.FOUND_DATE &lt;= to_date(#{startDate},'yyyy-mm-dd') and and STRUCT_ID is  null
    </insert>

    <select id="queryHisRecordByFinspId" resultType="com.hualu.app.module.mems.finsp.entity.DmFinspRecord">
        select a.FINSP_RES_ID as FINSP_ITEM_ID,c.STRUCT_PART_ID,d.DSS_TYPE_NAME,
               c.DSS_TYPE,LINE_DIRECT, STAKE, STRUCT_PART_ID, c.STRUCT_COMP_ID, LANE, DSS_DESC,
               DSS_L, c.DSS_L_UNIT, DSS_W, c.DSS_W_UNIT, DSS_D, c.DSS_D_UNIT, DSS_N, c.DSS_N_UNIT, DSS_A, c.DSS_A_UNIT, DSS_V,
               c.DSS_V_UNIT, DSS_P, DSS_G, DSS_QUALITY,b.FIN_DESC as dss_desc_q
         from DM_FINSP_RESULT a
         inner join DM_FINSP_ITEM b on a.FINSP_ITEM_ID = b.FINSP_ITEM_ID
         inner join BASE_STRUCT_COMP k on k.STRUCT_COMP_NAME = b.INSP_COM
         <choose>
             <when test="facilityCat=='SD'">
                 inner join DM_FINSP_RECORD c on instr(',' || k.STRUCT_COMP_ID || ',',',' || c.TUNNEL_MOUTH || ',') > 0 and a.FINSP_ID = c.FINSP_ID
             </when>
             <when test="facilityCat=='QL'">
                 inner join DM_FINSP_RECORD c on instr(',' || b.PART_CODE || ',',',' || c.STRUCT_PART_ID || ',') > 0 and a.FINSP_ID = c.FINSP_ID
             </when>
             <otherwise>
                 inner join DM_FINSP_RECORD c on a.FINSP_ID = c.FINSP_ID and a.finsp_res_id = c.finsp_item_id
             </otherwise>
         </choose>
         inner join DSS_TYPE_NEW d on c.DSS_TYPE = d.DSS_TYPE
         where a.FINSP_ID = #{finspId}
    </select>

    <select id="selectViewRecordByFinspId" resultType="com.hualu.app.module.mems.finsp.entity.DmFinspRecord">
        SELECT
            d.repair_status,
            line.line_sname || '(' || line.line_code || ')' AS line_id,
            ( SELECT count( 1 ) FROM dss_image d WHERE d.dss_id = f.dss_id ) AS IMG,
            (select br.brdg_name || '(' || br.brdg_line_type || br.frame_num || ')' from BCTCMSDB.T_BRDG_BRDGRECOG br where br.BRDGRECOG_ID = f.struct_id and br.VALID_FLAG = 1)  as struct_Name,
            f.*,
            pp.post_name AS dss_Desc_Q,
            t.DSS_TYPE_NAME,
            dt.MTASK_CODE,
            dt.MTASK_ID,
            dta.PROCESSINSTID AS MTASK_ACCPT_ID,
            dta.MTASK_ACCPT_CODE
        FROM
            DM_FINSP_RECORD f
                LEFT JOIN DSS_TYPE_NEW t ON f.DSS_TYPE = t.DSS_TYPE
                LEFT JOIN bctcmsdb.v_partpost pp ON pp.partpost_d = f.dss_Position
                LEFT JOIN dss_info d ON ( f.dss_id = d.dss_id OR d.dss_id = f.l_dss_id )
                LEFT JOIN gdgs.base_line line ON line.line_id = d.main_road_id
                LEFT JOIN DM_TASK_DETAIL dtd ON dtd.DSS_ID = d.DSS_ID
                LEFT JOIN DM_TASK dt ON dt.MTASK_ID = dtd.MTASK_ID
                LEFT JOIN DM_TASK_ACCPT dta ON dta.MTASK_ID = dt.MTASK_ID
        WHERE
            f.FINSP_ID = #{finspId} order by f.FOUND_DATE desc
    </select>
    <select id="selectHisRecordByStructId" resultType="com.hualu.app.module.mems.finsp.entity.DmFinspRecord">
        SELECT
            df.FACILITY_CAT,
            df.fin_version,
            d.repair_status,
            line.line_sname || '(' || line.line_code || ')' AS line_id,
            ( SELECT count( 1 ) FROM dss_image d WHERE d.dss_id = f.dss_id ) AS IMG,
            (select br.brdg_name || '(' || br.brdg_line_type || br.frame_num || ')' from BCTCMSDB.T_BRDG_BRDGRECOG br where br.BRDGRECOG_ID = f.struct_id and br.VALID_FLAG = 1)  as struct_Name,
            f.*,
            pp.post_name AS dss_Desc_Q,
            t.DSS_TYPE_NAME,
            dt.MTASK_CODE,
            dt.MTASK_ID,
            dta.PROCESSINSTID AS MTASK_ACCPT_ID,
            dta.MTASK_ACCPT_CODE
        FROM
            DM_FINSP_RECORD f
            INNER JOIN DM_FINSP df on f.FINSP_ID = df.FINSP_ID
            LEFT JOIN DSS_TYPE_NEW t ON f.DSS_TYPE = t.DSS_TYPE
            LEFT JOIN bctcmsdb.v_partpost pp ON pp.partpost_d = f.dss_Position
            LEFT JOIN dss_info d ON ( f.dss_id = d.dss_id OR d.dss_id = f.l_dss_id )
            LEFT JOIN gdgs.base_line line ON line.line_id = d.main_road_id
            LEFT JOIN DM_TASK_DETAIL dtd ON dtd.DSS_ID = d.DSS_ID
            LEFT JOIN DM_TASK dt ON dt.MTASK_ID = dtd.MTASK_ID
            LEFT JOIN DM_TASK_ACCPT dta ON dta.MTASK_ID = dt.MTASK_ID
        WHERE df.STRUCT_ID = #{structId} and df.FACILITY_CAT=#{facilityCat}
    </select>
    <select id="selectViewRecordById" resultType="com.hualu.app.module.mems.finsp.entity.DmFinspRecord">
        SELECT
            df.FACILITY_CAT,
            df.fin_version,
            d.repair_status,
            line.line_sname || '(' || line.line_code || ')' AS line_id,
            ( SELECT count( 1 ) FROM dss_image d WHERE d.dss_id = f.dss_id ) AS IMG,
            (select br.brdg_name || '(' || br.brdg_line_type || br.frame_num || ')' from BCTCMSDB.T_BRDG_BRDGRECOG br where br.BRDGRECOG_ID = f.struct_id and br.VALID_FLAG = 1)  as struct_Name,
            f.*,
            pp.post_name AS dss_Desc_Q,
            t.DSS_TYPE_NAME,
            dt.MTASK_CODE,
            dt.MTASK_ID,
            dta.PROCESSINSTID AS MTASK_ACCPT_ID,
            dta.MTASK_ACCPT_CODE
        FROM
            DM_FINSP_RECORD f
                inner join DM_FINSP df on df.FINSP_ID = f.FINSP_ID
                LEFT JOIN DSS_TYPE_NEW t ON f.DSS_TYPE = t.DSS_TYPE
                LEFT JOIN bctcmsdb.v_partpost pp ON pp.partpost_d = f.dss_Position
                LEFT JOIN dss_info d ON ( f.dss_id = d.dss_id OR d.dss_id = f.l_dss_id )
                LEFT JOIN gdgs.base_line line ON line.line_id = d.main_road_id
                LEFT JOIN DM_TASK_DETAIL dtd ON dtd.DSS_ID = d.DSS_ID
                LEFT JOIN DM_TASK dt ON dt.MTASK_ID = dtd.MTASK_ID
                LEFT JOIN DM_TASK_ACCPT dta ON dta.MTASK_ID = dt.MTASK_ID
        WHERE f.dss_id = #{dssId}
    </select>
    <select id="selectDssRecordPage" resultType="com.hualu.app.module.mems.dss.dto.DssInfoDto">
        select * from (SELECT
            df.FINSP_CODE as code_source,
            df.INSP_PERSON as user_name,
            df.FACILITY_CAT,
            df.LINE_CODE,
            df.MNTN_ORG_NM as oprt_Org_Name,
            df.MNT_ORG_ID as org_id,
            df.SEARCH_DEPT,
            df.STRUCT_NAME as dss_position,
            df.INSP_DATE as FOUND_DATE,
            dr.DSS_ID,
            dr.DSS_TYPE,
            dr.DSS_DESC,
            dr.dss_l,
            dr.dss_l_unit,
            dr.dss_w,
            dr.dss_w_unit,
            dr.dss_d,
            dr.dss_d_unit,
            dr.dss_n,
            dr.dss_n_unit,
            dr.dss_a,
            dr.dss_a_unit,
            dr.dss_v,
            dr.dss_v_unit,
            dr.dss_p,
            dr.dss_g,
            dt.HAVE_DSS_COLOM,
            dt.HAVE_DSS_UNIT,
            dt.DSS_TYPE_NAME,
            nvl(dss.REPAIR_STATUS,0) as REPAIR_STATUS,
            dss.DEAL_STATUS,
            '2' as DSS_SOURCE,
            dr.close_type,
            df.CREATE_USER_ID
        FROM
            memsdb.DM_FINSP df
                JOIN memsdb.DM_FINSP_RECORD dr ON df.FINSP_ID = dr.FINSP_ID
                JOIN memsdb.DSS_TYPE_NEW dt on dr.dss_type = dt.DSS_TYPE
                left join memsdb.DSS_INFO dss on dr.dss_id = dss.dss_id and DSS_SOURCE=2) t ${ew.customSqlSegment}
    </select>
</mapper>
