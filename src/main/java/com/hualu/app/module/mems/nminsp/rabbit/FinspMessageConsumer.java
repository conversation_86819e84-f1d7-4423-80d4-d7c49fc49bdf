package com.hualu.app.module.mems.nminsp.rabbit;

import com.hualu.app.config.InspQueueConfig;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;
import java.io.IOException;

@Service
public class FinspMessageConsumer {

    @RabbitListener(queues = InspQueueConfig.FINSP_QUEUE_NAME)
    public void receiveMessage(Message message, Channel channel) throws IOException {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        try {
            // 模拟业务处理，这里可能会抛出异常
            processMessage(message);
            // 手动确认消息
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            // 消费失败，拒绝消息并将其发送到死信队列
            channel.basicNack(deliveryTag, false, false);
            System.err.println("Check order message consumption failed: " + new String(message.getBody()) + ", error: " + e.getMessage());
        }
    }

    private void processMessage(Message message) {
        // 模拟业务逻辑，这里可以添加具体的数据库操作等
        String content = new String(message.getBody());
        System.out.println("Processing check order message: " + content);
        // 模拟异常
        if (Math.random() < 0.2) {
            throw new RuntimeException("Simulated processing error for check order");
        }
    }
}    