package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmFinspItem对象", description="")
public class DmFinspItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("FINSP_ITEM_ID")
    private String finspItemId;

    @TableField("FACILITY_CAT")
    private String facilityCat;

    @TableField("FINSP_ITEM_CODE")
    private String finspItemCode;

    @TableField("INSP_COM")
    private String inspCom;

    @TableField("INSP_CONT")
    private String inspCont;

    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @TableField("FIN_VERSION")
    private String finVersion;

    @TableField("IS_DELETE")
    private String isDelete;

    @TableField("FIN_DESC")
    private String finDesc;

    @TableField("FIN_REMARK")
    private String finRemark;

    @TableField("PART_CODE")
    private String partCode;

    public DmFinspItem() {
    }

    public DmFinspItem(String facilityCat, String finspItemCode, String inspCom, String inspCont, String mntOrgId, String isDelete,String finRemark,String finVersion) {
        this.facilityCat = facilityCat;
        this.finspItemCode = finspItemCode;
        this.inspCom = inspCom;
        this.inspCont = inspCont;
        this.mntOrgId = mntOrgId;
        this.isDelete = isDelete;
        this.finRemark = finRemark;
        this.finVersion = finVersion;
    }
}
