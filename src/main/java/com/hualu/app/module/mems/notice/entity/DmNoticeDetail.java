package com.hualu.app.module.mems.notice.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

@ApiModel(description = "MEMSDB.DM_NOTICE_DETAIL")
@TableName(value = "MEMSDB.DM_NOTICE_DETAIL")
public class DmNoticeDetail {
  @TableId(value = "NOTICE_DTL_ID")
  @ApiModelProperty(value = "")
  private String noticeDtlId;

  @TableField(value = "NOTICE_ID")
  @ApiModelProperty(value = "")
  private String noticeId;

  @TableField(value = "FACILITY_CAT")
  @ApiModelProperty(value = "")
  private String facilityCat;

  @TableField(value = "LINE_DIRECT")
  @ApiModelProperty(value = "")
  private String lineDirect;

  @TableField(value = "STAKE")
  @ApiModelProperty(value = "")
  private Double stake;

  @TableField(value = "LANE")
  @ApiModelProperty(value = "")
  private String lane;

  @TableField(value = "DSS_TYPE")
  @ApiModelProperty(value = "")
  private String dssType;

  @TableField(value = "DSS_DEGREE")
  @ApiModelProperty(value = "")
  private String dssDegree;

  @TableField(value = "DSS_DESC")
  @ApiModelProperty(value = "")
  private String dssDesc;

  @TableField(value = "DSS_L")
  @ApiModelProperty(value = "")
  private Double dssL;

  @TableField(value = "DSS_L_UNIT")
  @ApiModelProperty(value = "")
  private String dssLUnit;

  @TableField(value = "DSS_W")
  @ApiModelProperty(value = "")
  private Double dssW;

  @TableField(value = "DSS_W_UNIT")
  @ApiModelProperty(value = "")
  private String dssWUnit;

  @TableField(value = "DSS_D")
  @ApiModelProperty(value = "")
  private Double dssD;

  @TableField(value = "DSS_D_UNIT")
  @ApiModelProperty(value = "")
  private String dssDUnit;

  @TableField(value = "DSS_N")
  @ApiModelProperty(value = "")
  private Double dssN;

  @TableField(value = "DSS_N_UNIT")
  @ApiModelProperty(value = "")
  private String dssNUnit;

  @TableField(value = "DSS_A")
  @ApiModelProperty(value = "")
  private Double dssA;

  @TableField(value = "DSS_A_UNIT")
  @ApiModelProperty(value = "")
  private String dssAUnit;

  @TableField(value = "DSS_V")
  @ApiModelProperty(value = "")
  private Double dssV;

  @TableField(value = "DSS_V_UNIT")
  @ApiModelProperty(value = "")
  private String dssVUnit;

  @TableField(value = "DSS_P")
  @ApiModelProperty(value = "")
  private Double dssP;

  @TableField(value = "DSS_G")
  @ApiModelProperty(value = "")
  private Double dssG;

  @TableField(value = "MPITEM_ID")
  @ApiModelProperty(value = "")
  private String mpitemId;

  @TableField(value = "MEASURE_UNIT")
  @ApiModelProperty(value = "")
  private String measureUnit;

  @TableField(value = "MPITEM_ACCOUNT")
  @ApiModelProperty(value = "")
  private Double mpitemAccount;

  @TableField(value = "REMARK")
  @ApiModelProperty(value = "")
  private String remark;

  @TableField(value = "FOUND_DATE")
  @ApiModelProperty(value = "")
  private Date foundDate;

  @TableField(value = "DSS_QUALITY")
  @ApiModelProperty(value = "")
  private Integer dssQuality;

  @TableField(value = "DSS_IMP_FLAG")
  @ApiModelProperty(value = "")
  private Short dssImpFlag;

  @TableField(value = "HIS_DSS_ID")
  @ApiModelProperty(value = "")
  private String hisDssId;

  @TableField(value = "DSS_POSITION")
  @ApiModelProperty(value = "")
  private String dssPosition;

  @TableField(value = "DSS_CAUSE")
  @ApiModelProperty(value = "")
  private String dssCause;

  @TableField(value = "MNTN_ADVICE")
  @ApiModelProperty(value = "")
  private String mntnAdvice;

  @TableField(value = "IS_LUMP")
  @ApiModelProperty(value = "")
  private String isLump;

  @TableField(value = "RAMP_ID")
  @ApiModelProperty(value = "")
  private String rampId;

  /**
   * 病害ID
   */
  @TableField(value = "DSS_ID")
  @ApiModelProperty(value = "病害ID")
  private String dssId;

  @TableField(value = "FINISH_STAKE")
  @ApiModelProperty(value = "")
  private Double finishStake;

  @TableField(value = "STAKE_NAME")
  @ApiModelProperty(value = "")
  private String stakeName;

  @TableField(value = "STRUCT_ID")
  @ApiModelProperty(value = "")
  private String structId;

  @TableField(value = "STRUCT_PART_ID")
  @ApiModelProperty(value = "")
  private String structPartId;

  @TableField(value = "STRUCT_COMP_ID")
  @ApiModelProperty(value = "")
  private String structCompId;

  @TableField(value = "RL_STAKE_NUM")
  @ApiModelProperty(value = "")
  private Double rlStakeNum;

  @TableField(value = "RL_FINISH_STAKE_NUM")
  @ApiModelProperty(value = "")
  private Double rlFinishStakeNum;

  @TableField(exist = false)
  private String repairStatusName;

  @TableField(exist = false)
  private String lineDirectName;

  @TableField(exist = false)
  private String facilityCatName;

  @TableField(exist = false)
  private String dssTypeName;

  @TableField(exist = false)
  private String dssDegreeName;

  @TableField(exist = false)
  private String fileIds;

  @TableField(exist = false)
  private String laneName;

  /**
   * 病害定量信息拼接
   */
  @TableField(exist = false)
  private String dssNum;

  @TableField(exist = false)
  private String structName;

  @TableField(exist = false)
  private String structPartName;

  @TableField(exist = false)
  private String structCompName;

  @TableField(exist = false)
  private String dssPositionName;

  @TableField(exist = false)
  private String dssQualityName;

  @TableField(exist = false)
  private String issueTypeName;

  @TableField(exist = false)
  private String issueType;

  @TableField(exist = false)
  private String scope;

  public String getStructName() {
    return structName;
  }

  public void setStructName(String structName) {
    this.structName = structName;
  }

  public String getStructPartName() {
    return structPartName;
  }

  public void setStructPartName(String structPartName) {
    this.structPartName = structPartName;
  }

  public String getStructCompName() {
    return structCompName;
  }

  public void setStructCompName(String structCompName) {
    this.structCompName = structCompName;
  }

  public String getDssPositionName() {
    return dssPositionName;
  }

  public void setDssPositionName(String dssPositionName) {
    this.dssPositionName = dssPositionName;
  }

  public String getDssQualityName() {
    return dssQualityName;
  }

  public void setDssQualityName(String dssQualityName) {
    this.dssQualityName = dssQualityName;
  }

  public String getIssueTypeName() {
    return issueTypeName;
  }

  public void setIssueTypeName(String issueTypeName) {
    this.issueTypeName = issueTypeName;
  }

  public String getIssueType() {
    return issueType;
  }

  public void setIssueType(String issueType) {
    this.issueType = issueType;
  }

  public String getScope() {
    return scope;
  }

  public void setScope(String scope) {
    this.scope = scope;
  }

  public String getLaneName() {
    return laneName;
  }

  public void setLaneName(String laneName) {
    this.laneName = laneName;
  }

  public String getRepairStatusName() {
    return repairStatusName;
  }

  public void setRepairStatusName(String repairStatusName) {
    this.repairStatusName = repairStatusName;
  }

  public String getLineDirectName() {
    return lineDirectName;
  }

  public void setLineDirectName(String lineDirectName) {
    this.lineDirectName = lineDirectName;
  }

  public String getFacilityCatName() {
    return facilityCatName;
  }

  public void setFacilityCatName(String facilityCatName) {
    this.facilityCatName = facilityCatName;
  }

  public String getDssTypeName() {
    return dssTypeName;
  }

  public void setDssTypeName(String dssTypeName) {
    this.dssTypeName = dssTypeName;
  }

  public String getDssDegreeName() {
    return dssDegreeName;
  }

  public void setDssDegreeName(String dssDegreeName) {
    this.dssDegreeName = dssDegreeName;
  }

  public String getFileIds() {
    return fileIds;
  }

  public void setFileIds(String fileIds) {
    this.fileIds = fileIds;
  }

  public String getDssNum() {
    return dssNum;
  }

  public void setDssNum(String dssNum) {
    this.dssNum = dssNum;
  }

  public static final String COL_NOTICE_DTL_ID = "NOTICE_DTL_ID";

  public static final String COL_NOTICE_ID = "NOTICE_ID";

  public static final String COL_FACILITY_CAT = "FACILITY_CAT";

  public static final String COL_LINE_DIRECT = "LINE_DIRECT";

  public static final String COL_STAKE = "STAKE";

  public static final String COL_LANE = "LANE";

  public static final String COL_DSS_TYPE = "DSS_TYPE";

  public static final String COL_DSS_DEGREE = "DSS_DEGREE";

  public static final String COL_DSS_DESC = "DSS_DESC";

  public static final String COL_DSS_L = "DSS_L";

  public static final String COL_DSS_L_UNIT = "DSS_L_UNIT";

  public static final String COL_DSS_W = "DSS_W";

  public static final String COL_DSS_W_UNIT = "DSS_W_UNIT";

  public static final String COL_DSS_D = "DSS_D";

  public static final String COL_DSS_D_UNIT = "DSS_D_UNIT";

  public static final String COL_DSS_N = "DSS_N";

  public static final String COL_DSS_N_UNIT = "DSS_N_UNIT";

  public static final String COL_DSS_A = "DSS_A";

  public static final String COL_DSS_A_UNIT = "DSS_A_UNIT";

  public static final String COL_DSS_V = "DSS_V";

  public static final String COL_DSS_V_UNIT = "DSS_V_UNIT";

  public static final String COL_DSS_P = "DSS_P";

  public static final String COL_DSS_G = "DSS_G";

  public static final String COL_MPITEM_ID = "MPITEM_ID";

  public static final String COL_MEASURE_UNIT = "MEASURE_UNIT";

  public static final String COL_MPITEM_ACCOUNT = "MPITEM_ACCOUNT";

  public static final String COL_REMARK = "REMARK";

  public static final String COL_FOUND_DATE = "FOUND_DATE";

  public static final String COL_DSS_QUALITY = "DSS_QUALITY";

  public static final String COL_DSS_IMP_FLAG = "DSS_IMP_FLAG";

  public static final String COL_HIS_DSS_ID = "HIS_DSS_ID";

  public static final String COL_DSS_POSITION = "DSS_POSITION";

  public static final String COL_DSS_CAUSE = "DSS_CAUSE";

  public static final String COL_MNTN_ADVICE = "MNTN_ADVICE";

  public static final String COL_IS_LUMP = "IS_LUMP";

  public static final String COL_RAMP_ID = "RAMP_ID";

  public static final String COL_DSS_ID = "DSS_ID";

  public static final String COL_FINISH_STAKE = "FINISH_STAKE";

  public static final String COL_STAKE_NAME = "STAKE_NAME";

  public static final String COL_STRUCT_ID = "STRUCT_ID";

  public static final String COL_STRUCT_PART_ID = "STRUCT_PART_ID";

  public static final String COL_STRUCT_COMP_ID = "STRUCT_COMP_ID";

  public static final String COL_RL_STAKE_NUM = "RL_STAKE_NUM";

  public static final String COL_RL_FINISH_STAKE_NUM = "RL_FINISH_STAKE_NUM";

  /**
   * @return NOTICE_DTL_ID
   */
  public String getNoticeDtlId() {
    return noticeDtlId;
  }

  /**
   * @param noticeDtlId
   */
  public void setNoticeDtlId(String noticeDtlId) {
    this.noticeDtlId = noticeDtlId;
  }

  /**
   * @return NOTICE_ID
   */
  public String getNoticeId() {
    return noticeId;
  }

  /**
   * @param noticeId
   */
  public void setNoticeId(String noticeId) {
    this.noticeId = noticeId;
  }

  /**
   * @return FACILITY_CAT
   */
  public String getFacilityCat() {
    return facilityCat;
  }

  /**
   * @param facilityCat
   */
  public void setFacilityCat(String facilityCat) {
    this.facilityCat = facilityCat;
  }

  /**
   * @return LINE_DIRECT
   */
  public String getLineDirect() {
    return lineDirect;
  }

  /**
   * @param lineDirect
   */
  public void setLineDirect(String lineDirect) {
    this.lineDirect = lineDirect;
  }

  /**
   * @return STAKE
   */
  public Double getStake() {
    return stake;
  }

  /**
   * @param stake
   */
  public void setStake(Double stake) {
    this.stake = stake;
  }

  /**
   * @return LANE
   */
  public String getLane() {
    return lane;
  }

  /**
   * @param lane
   */
  public void setLane(String lane) {
    this.lane = lane;
  }

  /**
   * @return DSS_TYPE
   */
  public String getDssType() {
    return dssType;
  }

  /**
   * @param dssType
   */
  public void setDssType(String dssType) {
    this.dssType = dssType;
  }

  /**
   * @return DSS_DEGREE
   */
  public String getDssDegree() {
    return dssDegree;
  }

  /**
   * @param dssDegree
   */
  public void setDssDegree(String dssDegree) {
    this.dssDegree = dssDegree;
  }

  /**
   * @return DSS_DESC
   */
  public String getDssDesc() {
    return dssDesc;
  }

  /**
   * @param dssDesc
   */
  public void setDssDesc(String dssDesc) {
    this.dssDesc = dssDesc;
  }

  /**
   * @return DSS_L
   */
  public Double getDssL() {
    return dssL;
  }

  /**
   * @param dssL
   */
  public void setDssL(Double dssL) {
    this.dssL = dssL;
  }

  /**
   * @return DSS_L_UNIT
   */
  public String getDssLUnit() {
    return dssLUnit;
  }

  /**
   * @param dssLUnit
   */
  public void setDssLUnit(String dssLUnit) {
    this.dssLUnit = dssLUnit;
  }

  /**
   * @return DSS_W
   */
  public Double getDssW() {
    return dssW;
  }

  /**
   * @param dssW
   */
  public void setDssW(Double dssW) {
    this.dssW = dssW;
  }

  /**
   * @return DSS_W_UNIT
   */
  public String getDssWUnit() {
    return dssWUnit;
  }

  /**
   * @param dssWUnit
   */
  public void setDssWUnit(String dssWUnit) {
    this.dssWUnit = dssWUnit;
  }

  /**
   * @return DSS_D
   */
  public Double getDssD() {
    return dssD;
  }

  /**
   * @param dssD
   */
  public void setDssD(Double dssD) {
    this.dssD = dssD;
  }

  /**
   * @return DSS_D_UNIT
   */
  public String getDssDUnit() {
    return dssDUnit;
  }

  /**
   * @param dssDUnit
   */
  public void setDssDUnit(String dssDUnit) {
    this.dssDUnit = dssDUnit;
  }

  /**
   * @return DSS_N
   */
  public Double getDssN() {
    return dssN;
  }

  /**
   * @param dssN
   */
  public void setDssN(Double dssN) {
    this.dssN = dssN;
  }

  /**
   * @return DSS_N_UNIT
   */
  public String getDssNUnit() {
    return dssNUnit;
  }

  /**
   * @param dssNUnit
   */
  public void setDssNUnit(String dssNUnit) {
    this.dssNUnit = dssNUnit;
  }

  /**
   * @return DSS_A
   */
  public Double getDssA() {
    return dssA;
  }

  /**
   * @param dssA
   */
  public void setDssA(Double dssA) {
    this.dssA = dssA;
  }

  /**
   * @return DSS_A_UNIT
   */
  public String getDssAUnit() {
    return dssAUnit;
  }

  /**
   * @param dssAUnit
   */
  public void setDssAUnit(String dssAUnit) {
    this.dssAUnit = dssAUnit;
  }

  /**
   * @return DSS_V
   */
  public Double getDssV() {
    return dssV;
  }

  /**
   * @param dssV
   */
  public void setDssV(Double dssV) {
    this.dssV = dssV;
  }

  /**
   * @return DSS_V_UNIT
   */
  public String getDssVUnit() {
    return dssVUnit;
  }

  /**
   * @param dssVUnit
   */
  public void setDssVUnit(String dssVUnit) {
    this.dssVUnit = dssVUnit;
  }

  /**
   * @return DSS_P
   */
  public Double getDssP() {
    return dssP;
  }

  /**
   * @param dssP
   */
  public void setDssP(Double dssP) {
    this.dssP = dssP;
  }

  /**
   * @return DSS_G
   */
  public Double getDssG() {
    return dssG;
  }

  /**
   * @param dssG
   */
  public void setDssG(Double dssG) {
    this.dssG = dssG;
  }

  /**
   * @return MPITEM_ID
   */
  public String getMpitemId() {
    return mpitemId;
  }

  /**
   * @param mpitemId
   */
  public void setMpitemId(String mpitemId) {
    this.mpitemId = mpitemId;
  }

  /**
   * @return MEASURE_UNIT
   */
  public String getMeasureUnit() {
    return measureUnit;
  }

  /**
   * @param measureUnit
   */
  public void setMeasureUnit(String measureUnit) {
    this.measureUnit = measureUnit;
  }

  /**
   * @return MPITEM_ACCOUNT
   */
  public Double getMpitemAccount() {
    return mpitemAccount;
  }

  /**
   * @param mpitemAccount
   */
  public void setMpitemAccount(Double mpitemAccount) {
    this.mpitemAccount = mpitemAccount;
  }

  /**
   * @return REMARK
   */
  public String getRemark() {
    return remark;
  }

  /**
   * @param remark
   */
  public void setRemark(String remark) {
    this.remark = remark;
  }

  /**
   * @return FOUND_DATE
   */
  public Date getFoundDate() {
    return foundDate;
  }

  /**
   * @param foundDate
   */
  public void setFoundDate(Date foundDate) {
    this.foundDate = foundDate;
  }

  /**
   * @return DSS_QUALITY
   */
  public Integer getDssQuality() {
    return dssQuality;
  }

  /**
   * @param dssQuality
   */
  public void setDssQuality(Integer dssQuality) {
    this.dssQuality = dssQuality;
  }

  /**
   * @return DSS_IMP_FLAG
   */
  public Short getDssImpFlag() {
    return dssImpFlag;
  }

  /**
   * @param dssImpFlag
   */
  public void setDssImpFlag(Short dssImpFlag) {
    this.dssImpFlag = dssImpFlag;
  }

  /**
   * @return HIS_DSS_ID
   */
  public String getHisDssId() {
    return hisDssId;
  }

  /**
   * @param hisDssId
   */
  public void setHisDssId(String hisDssId) {
    this.hisDssId = hisDssId;
  }

  /**
   * @return DSS_POSITION
   */
  public String getDssPosition() {
    return dssPosition;
  }

  /**
   * @param dssPosition
   */
  public void setDssPosition(String dssPosition) {
    this.dssPosition = dssPosition;
  }

  /**
   * @return DSS_CAUSE
   */
  public String getDssCause() {
    return dssCause;
  }

  /**
   * @param dssCause
   */
  public void setDssCause(String dssCause) {
    this.dssCause = dssCause;
  }

  /**
   * @return MNTN_ADVICE
   */
  public String getMntnAdvice() {
    return mntnAdvice;
  }

  /**
   * @param mntnAdvice
   */
  public void setMntnAdvice(String mntnAdvice) {
    this.mntnAdvice = mntnAdvice;
  }

  /**
   * @return IS_LUMP
   */
  public String getIsLump() {
    return isLump;
  }

  /**
   * @param isLump
   */
  public void setIsLump(String isLump) {
    this.isLump = isLump;
  }

  /**
   * @return RAMP_ID
   */
  public String getRampId() {
    return rampId;
  }

  /**
   * @param rampId
   */
  public void setRampId(String rampId) {
    this.rampId = rampId;
  }

  /**
   * 获取病害ID
   *
   * @return DSS_ID - 病害ID
   */
  public String getDssId() {
    return dssId;
  }

  /**
   * 设置病害ID
   *
   * @param dssId 病害ID
   */
  public void setDssId(String dssId) {
    this.dssId = dssId;
  }

  /**
   * @return FINISH_STAKE
   */
  public Double getFinishStake() {
    return finishStake;
  }

  /**
   * @param finishStake
   */
  public void setFinishStake(Double finishStake) {
    this.finishStake = finishStake;
  }

  /**
   * @return STAKE_NAME
   */
  public String getStakeName() {
    return stakeName;
  }

  /**
   * @param stakeName
   */
  public void setStakeName(String stakeName) {
    this.stakeName = stakeName;
  }

  /**
   * @return STRUCT_ID
   */
  public String getStructId() {
    return structId;
  }

  /**
   * @param structId
   */
  public void setStructId(String structId) {
    this.structId = structId;
  }

  /**
   * @return STRUCT_PART_ID
   */
  public String getStructPartId() {
    return structPartId;
  }

  /**
   * @param structPartId
   */
  public void setStructPartId(String structPartId) {
    this.structPartId = structPartId;
  }

  /**
   * @return STRUCT_COMP_ID
   */
  public String getStructCompId() {
    return structCompId;
  }

  /**
   * @param structCompId
   */
  public void setStructCompId(String structCompId) {
    this.structCompId = structCompId;
  }

  /**
   * @return RL_STAKE_NUM
   */
  public Double getRlStakeNum() {
    return rlStakeNum;
  }

  /**
   * @param rlStakeNum
   */
  public void setRlStakeNum(Double rlStakeNum) {
    this.rlStakeNum = rlStakeNum;
  }

  /**
   * @return RL_FINISH_STAKE_NUM
   */
  public Double getRlFinishStakeNum() {
    return rlFinishStakeNum;
  }

  /**
   * @param rlFinishStakeNum
   */
  public void setRlFinishStakeNum(Double rlFinishStakeNum) {
    this.rlFinishStakeNum = rlFinishStakeNum;
  }


}