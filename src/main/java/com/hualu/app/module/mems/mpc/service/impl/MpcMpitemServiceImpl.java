package com.hualu.app.module.mems.mpc.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.mpc.entity.MpcMpitem;
import com.hualu.app.module.mems.mpc.mapper.MpcMpitemMapper;
import com.hualu.app.module.mems.mpc.service.MpcMpitemService;
import com.hualu.app.utils.H_OrgHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Service
public class MpcMpitemServiceImpl extends ServiceImpl<MpcMpitemMapper, MpcMpitem> implements MpcMpitemService {

    @Override
    public List<MpcMpitem> selectMpitems(Page page, String contrId, String dssType, String mpitemName) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("PRC.CONTR_ID",contrId);

        if (StrUtil.isNotBlank(dssType)){
            queryWrapper.eq("SURE.DSS_TYPE",dssType);
        }
        if (StrUtil.isNotBlank(mpitemName)){
            queryWrapper.like("mpc.MPITEM_NAME",mpitemName);
        }
        String currentOrgId = CustomRequestContextHolder.getOrgIdString();
        List<String> orgIds = Lists.newArrayList("N000001", currentOrgId, H_OrgHelper.getParentOrgId(currentOrgId));
        queryWrapper.in("sure.org_id",orgIds);
        queryWrapper.orderByAsc("MPC.mpitem_code");
        return baseMapper.selectMpitems(page,queryWrapper);
    }

    @Override
    public List<MpcMpitem> selectMpitemsByDssId(String dssId, String dssType, String contrId, String mpitemName) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.apply("mpc.mpitem_sys_id = MM.MPITEM_SYS_ID AND MM.CONTR_ID = PRC.CONTR_ID and MPC.MPITEM_CODE = PRC.MPITEM_CODE AND MPC.MPITEM_ID = SURE.MPITEM_ID");
        queryWrapper.eq("PRC.CONTR_ID",contrId);
        queryWrapper.notIn("MPC.mpitem_id","select yy.mpitem_id from memsdb.dm_task_detail yy where yy.dss_id={0}",dssId);
        if (StrUtil.isNotBlank(dssType)){
            queryWrapper.eq("SURE.DSS_TYPE",dssType);
        }
        String currentOrgId = CustomRequestContextHolder.getOrgIdString();
        List<String> orgIds = Lists.newArrayList("N000001", currentOrgId, H_OrgHelper.getParentOrgId(currentOrgId));
        queryWrapper.in("sure.org_id",orgIds);
        queryWrapper.orderByAsc("MPC.mpitem_code");
        List<MpcMpitem> items = baseMapper.selectMpitemsByDssId(queryWrapper);
        return items;
    }

    @Override
    public List<MpcMpitem> selectMpitemsByMpSysIdAndMpitemCode(String mpSystemId, List<String> mpitemCodePrefixs) {
        Assert.hasText(mpSystemId,"养护体系ID不能为空");
        Assert.notEmpty(mpitemCodePrefixs,"mpitemCodePrefixs不能为空");

        LambdaQueryWrapper<MpcMpitem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpcMpitem::getMpitemSysId,mpSystemId);
        queryWrapper.and(e->{
            mpitemCodePrefixs.forEach(prefix->{
                e.or().likeRight(MpcMpitem::getMpitemCode,prefix);
            });
            return e;
        });
        return list(queryWrapper);
    }
}
