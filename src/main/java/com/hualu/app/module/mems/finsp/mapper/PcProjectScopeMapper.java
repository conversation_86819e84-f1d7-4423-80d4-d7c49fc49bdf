package com.hualu.app.module.mems.finsp.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 结构物专项范围表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
public interface PcProjectScopeMapper extends BaseMapper<PcProjectScope> {


    PcProjectScope selectByStructId(@Param("structId") String structId);

    List<PcFinspStatDto> checkStat(@Param("orgCodes") List<String> orgCodes, @Param("prjIds") List<String> prjIds,@Param("mntType") String mntType);

    List<String> listStructType(@Param("prjIds") List<String> prjIds);

    List<PcProjectScope> listByMd5(@Param("md5s") List<String> md5s, @Param("prjId") String prjId);

    void updateIgnoreDelField(@Param("structIds") List<String> structIds);
}
