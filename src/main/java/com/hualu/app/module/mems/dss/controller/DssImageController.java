package com.hualu.app.module.mems.dss.controller;


import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.utils.H_UploadHelper;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import oracle.jdbc.proxy.annotation.Post;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@RestController
@RequestMapping("/dssImage")
public class DssImageController {

  @Resource
  DssImageService imageService;

  /**
   * 保存文件
   * @param businessId 业务id
   * @param imageType  图片类型 1：病害照片  6：经常检查工照
   * @param files 文件集合
   * @return
   */
  @PostMapping("/uploadImageForApp")
  public RestResult<String> uploadImageForApp(
      @RequestParam(value = "businessId") String businessId,
      @RequestParam(value = "imageType", defaultValue = "6") String imageType,
      MultipartFile[] files
  ) {
    if (files.length == 0) {
      return RestResult.error("没有文件");
    }
    List<String> fileIds = H_UploadHelper.uploadFile(files);
    String fileId = fileIds.stream().filter(Objects::nonNull).collect(Collectors.joining(","));
    imageService.saveDssImageForApp(businessId, fileId, NumberUtil.parseInt(imageType, 6));
    return RestResult.success(fileId, "保存成功");
  }

  /**
   * 获取文件
   * @param businessId 业务id
   * @param imageType  图片类型 1：病害照片  6：经常检查工照
   * @return
   */
  @GetMapping("/getImageForApp")
  public RestResult<List<DssImage>> getImageForApp(
      @RequestParam(value = "businessId") String businessId,
      @RequestParam(value = "imageType", defaultValue = "6", required = false) String imageType
  ) {
    LambdaQueryWrapper<DssImage> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(DssImage::getDssId, businessId);
    queryWrapper.eq(ObjectUtil.isNotEmpty(imageType), DssImage::getImageType, imageType);
    return RestResult.success(imageService.list(queryWrapper), "获取成功");
  }

}
