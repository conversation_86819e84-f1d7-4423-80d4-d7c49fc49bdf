<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcCleanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcClean">
        <result column="CLEAN_ID" property="cleanId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="STRUCT_STAKE" property="structStake" />
        <result column="STRUCT_LEVEL" property="structLevel" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="INSP_PERSON_NUM" property="inspPersonNum" />
        <result column="CLEAN_CONTENT" property="cleanContent" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CLEAN_ID, STRUCT_NAME, STRUCT_STAKE, STRUCT_LEVEL, MNT_ORG_ID, INSP_DATE, INSP_PERSON_NUM, CLEAN_CONTENT, CREATE_TIME, CREATE_USER_ID, UPDATE_TIME, DEL_FLAG
    </sql>

</mapper>
