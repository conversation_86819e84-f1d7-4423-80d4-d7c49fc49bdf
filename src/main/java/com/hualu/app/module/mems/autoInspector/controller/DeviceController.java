package com.hualu.app.module.mems.autoInspector.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.autoInspector.entity.HighwayVehicleDevice;
import com.hualu.app.module.mems.autoInspector.service.HighwayVehicleDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 设备信息控制器
 * 提供设备信息的查询和管理功能
 */
@Api(tags = "设备信息控制器")
@RestController
@RequestMapping("/device")
@Slf4j
public class DeviceController {

    @Autowired
    private HighwayVehicleDeviceService deviceService;

    /**
     * 分页查询设备信息
     * @param params 查询参数
     * @return 分页结果
     */
    @ApiOperation("分页查询设备信息")
    @PostMapping("/page")
    public RestResult<List<HighwayVehicleDevice>> page(@RequestBody(required = false) Map<String, Object> params) {
        // 设置默认分页参数
        int current = 1;
        int size = 10;
        
        // 解析分页参数
        if (params != null) {
            if (params.containsKey("current") && params.get("current") != null) {
                try {
                    if (params.get("current") instanceof Number) {
                        current = ((Number) params.get("current")).intValue();
                    } else {
                        current = Integer.parseInt(params.get("current").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid current page parameter: {}", params.get("current"));
                }
            }
            
            if (params.containsKey("pageSize") && params.get("pageSize") != null) {
                try {
                    if (params.get("pageSize") instanceof Number) {
                        size = ((Number) params.get("pageSize")).intValue();
                    } else {
                        size = Integer.parseInt(params.get("pageSize").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid page size parameter: {}", params.get("pageSize"));
                }
            } else if (params.containsKey("size") && params.get("size") != null) {
                try {
                    if (params.get("size") instanceof Number) {
                        size = ((Number) params.get("size")).intValue();
                    } else {
                        size = Integer.parseInt(params.get("size").toString());
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid page size parameter: {}", params.get("size"));
                }
            }
        }
        
        // 构建查询条件
        LambdaQueryWrapper<HighwayVehicleDevice> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (params != null) {
            // 车牌号模糊查询
            if (params.containsKey("plateNo") && !StringUtils.isEmpty(params.get("plateNo"))) {
                queryWrapper.like(HighwayVehicleDevice::getPlateNo, params.get("plateNo"));
            }
            
            // 状态精确查询
            if (params.containsKey("status") && !StringUtils.isEmpty(params.get("status"))) {
                queryWrapper.eq(HighwayVehicleDevice::getStatus, params.get("status"));
            }
            
            // 在线状态精确查询
            if (params.containsKey("serverStatus") && !StringUtils.isEmpty(params.get("serverStatus"))) {
                queryWrapper.eq(HighwayVehicleDevice::getServerStatus, params.get("serverStatus"));
            }
            
            // 设备状态精确查询
            if (params.containsKey("aiotStatus") && !StringUtils.isEmpty(params.get("aiotStatus"))) {
                queryWrapper.eq(HighwayVehicleDevice::getAiotStatus, params.get("aiotStatus"));
            }
            
            // 检查标志精确查询
            if (params.containsKey("inspectingFlag") && params.get("inspectingFlag") != null) {
                queryWrapper.eq(HighwayVehicleDevice::getInspectingFlag, params.get("inspectingFlag"));
            }
            
            // 客户端ID精确查询
            if (params.containsKey("clientId") && !StringUtils.isEmpty(params.get("clientId"))) {
                queryWrapper.eq(HighwayVehicleDevice::getClientId, params.get("clientId"));
            }
        }
        
        // 默认按更新时间降序排序
        queryWrapper.orderByDesc(HighwayVehicleDevice::getUpdatedTime);
        
        // 执行分页查询
        Page<HighwayVehicleDevice> page = new Page<>(current, size);
        IPage<HighwayVehicleDevice> resultPage = deviceService.page(page, queryWrapper);
        
        // 构建返回结果
        RestResult<List<HighwayVehicleDevice>> result = RestResult.success(resultPage.getRecords());
        result.setTotal(resultPage.getTotal());
        result.setPage(resultPage.getCurrent());
        result.setPageSize(resultPage.getSize());
        
        return result;
    }
    
    /**
     * 获取设备详情
     * @param id 设备ID
     * @return 设备详细信息
     */
    @ApiOperation("获取设备详情")
    @GetMapping("/{id}")
    public RestResult<HighwayVehicleDevice> getDetail(@PathVariable("id") @ApiParam("设备ID") String id) {
        HighwayVehicleDevice device = deviceService.getById(id);
        if (device != null) {
            return RestResult.success(device);
        } else {
            return RestResult.error("设备不存在");
        }
    }
    
    /**
     * 获取设备列表（不分页）
     * @return 设备列表
     */
    @ApiOperation("获取设备列表（不分页）")
    @PostMapping("/list")
    public RestResult<List<HighwayVehicleDevice>> list(@RequestBody(required = false) Map<String, Object> params) {
        // 构建查询条件
        LambdaQueryWrapper<HighwayVehicleDevice> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (params != null) {
            // 车牌号模糊查询
            if (params.containsKey("plateNo") && !StringUtils.isEmpty(params.get("plateNo"))) {
                queryWrapper.like(HighwayVehicleDevice::getPlateNo, params.get("plateNo"));
            }

            // 状态精确查询
            if (params.containsKey("status") && !StringUtils.isEmpty(params.get("status"))) {
                queryWrapper.eq(HighwayVehicleDevice::getStatus, params.get("status"));
            }

            // 在线状态精确查询
            if (params.containsKey("serverStatus") && !StringUtils.isEmpty(params.get("serverStatus"))) {
                queryWrapper.eq(HighwayVehicleDevice::getServerStatus, params.get("serverStatus"));
            }

            // 设备状态精确查询
            if (params.containsKey("aiotStatus") && !StringUtils.isEmpty(params.get("aiotStatus"))) {
                queryWrapper.eq(HighwayVehicleDevice::getAiotStatus, params.get("aiotStatus"));
            }

            // 检查标志精确查询
            if (params.containsKey("inspectingFlag") && params.get("inspectingFlag") != null) {
                queryWrapper.eq(HighwayVehicleDevice::getInspectingFlag, params.get("inspectingFlag"));
            }

            // 客户端ID精确查询
            if (params.containsKey("clientId") && !StringUtils.isEmpty(params.get("clientId"))) {
                queryWrapper.eq(HighwayVehicleDevice::getClientId, params.get("clientId"));
            }
        }
        
        // 执行查询
        List<HighwayVehicleDevice> devices = deviceService.list(queryWrapper);
        
        return RestResult.success(devices);
    }
} 