package com.hualu.app.module.mems.mpc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.mpc.entity.MpcFileRef;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
public interface MpcFileRefService extends IService<MpcFileRef> {

    List<MpcFileRef> listFileByBillId(String billId);

    void delByBillId(String billId);

    void saveFile(String billId,String fileIds);

    void delByFileId(String fileId);
}
