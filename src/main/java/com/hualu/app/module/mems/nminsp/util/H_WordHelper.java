package com.hualu.app.module.mems.nminsp.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.*;
import cn.hutool.poi.word.WordUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.util.TableTools;
import com.hualu.app.module.mems.nminsp.dto.DownloadWordDto;
import com.hualu.app.module.mems.nminsp.plugin.ExistingLoopRowTableRenderPolicy;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_FutureHelper;
import com.hualu.app.utils.H_MergeWordToPdf;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.assertj.core.util.Lists;
import org.springframework.core.env.Environment;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文档导出
 */
public class H_WordHelper {

    public static final String FILE_RESPOSITY = "fileResposity";

    /**
     * word下载
     * @param wordDto
     * @param response
     * @throws IOException
     */
    public static void download(DownloadWordDto wordDto, HttpServletResponse response) throws IOException {
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        if (CollectionUtil.isEmpty(wordDto.getWordPaths())) {
            throw new BaseException("未找到对应的数据");
        }
        String filePath = wordDto.getWordPaths().get(0);
        // 对文件名进行编码，防止中文文件名乱码
        String fileName = URLEncoder.encode(FileUtil.getName(filePath), "UTF-8");
        if(wordDto.getWordPaths().size() != 1){
            File zip = ZipUtil.zip(wordDto.getParentPath());
            filePath = zip.getAbsolutePath();
            fileName = URLEncoder.encode(zip.getName(), "UTF-8");
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        ServletOutputStream outputStream = response.getOutputStream();
        InputStream fileInput = Files.newInputStream(Paths.get(filePath));
        IoUtil.copy(Files.newInputStream(Paths.get(filePath)),outputStream);
        IoUtil.close(fileInput);
        IoUtil.close(outputStream);
    }

    /**
     * word转pdf实现文件预览
     * @param wordDto
     * @param response
     * @throws IOException
     */
    public static void wordToPdf(DownloadWordDto wordDto, HttpServletResponse response) throws Exception {
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=report.pdf");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        List<String> wordPaths = wordDto.getWordPaths();

        String output = wordDto.getParentPath() + File.separator + IdUtil.fastSimpleUUID() + ".pdf";
        H_MergeWordToPdf.wordToPdf(wordPaths,output);
        ServletOutputStream outputStream = response.getOutputStream();
        InputStream fileInput = Files.newInputStream(Paths.get(output));
        IoUtil.copy(Files.newInputStream(Paths.get(output)),outputStream);
        IoUtil.close(fileInput);
        IoUtil.close(outputStream);
    }

    /**
     * 输出文件预览地址
     * @param wordDto
     * @param parentPath
     * @return
     * @throws Exception
     */
    public static String wordToPdf(DownloadWordDto wordDto,String parentPath) throws Exception {
        List<String> wordPaths = wordDto.getWordPaths();
        String tempPath = File.separator + "PDF" +File.separator + IdUtil.fastSimpleUUID() + ".pdf";
        String output = parentPath + tempPath;
        H_MergeWordToPdf.wordToPdf(wordPaths,output);
        if (!FileUtil.exist(output)) {
            FileUtil.mkdir(output);
        }
        return H_WordHelper.FILE_RESPOSITY+tempPath;
    }

    /**
     * 获取公用存储地址
     * @param facilityCat 设施类型
     * @param xcType 巡查类型  NmDinsp:日常巡查  NmFinsp:经常检查
     * @return
     */
    public static String getParentPath(String facilityCat,String xcType){
        Environment bean = CustomApplicationContextHolder.getBean(Environment.class);
        String fileReposity = bean.getProperty("fileReposity");
        if (StrUtil.isBlank(fileReposity)) {
            throw new BaseException("fileReposity不能为空");
        }
        // 需要按照  结构物+经常检查+当前日期来判断
        String parentPath = fileReposity + File.separator + xcType+ File.separator + facilityCat.toUpperCase() + File.separator
                + DateUtil.date().toDateStr() + File.separator + H_KeyWorker.nextIdToString()+File.separator+getZipName(facilityCat,xcType)+File.separator;
        if (!FileUtil.exist(parentPath)) {
            FileUtil.mkdir(parentPath);
        }
        return parentPath;
    }

    private static String getZipName(String facilityCat,String xcType){
        String zipName = H_BasedataHepler.facilityCatMap.getOrDefault(facilityCat, "未知")
                + ("NmDinsp".equals(xcType) ? "日常巡查" : "经常检查")+"_"
                +DateUtil.date().toDateStr();
        return zipName;
    }

    /**
     * 导出word单
     * @param isLoopRow true：采用LoopRowTableRenderPolicy策略，false:采用自定义策略
     * @param entities 主单对象集合
     * @param parentPath word存储路径地址
     * @param xcType 巡查类型：DM、FM
     * @param facilityCatGetter 设施类型
     * @param inspFrequencyGetter 巡查频率
     * @param inspIdGetter 主单ID
     * @param inspCodeGetter 单号名称
     * @param inspDateGetter 巡查日期
     * @param fetchRecordsFunction 列表记录ID集合
     * @param recordEntityIdGetter 列表记录ID
     * @param processRecordsConsumer 回显word病害值
     * @return
     * @param <T>
     * @param <R>
     */
    public static  <T, R> List<String> processAndExportWord(
            Boolean isLoopRow,
            List<T> entities,
            String parentPath,
            String xcType,
            Function<T, String> facilityCatGetter,
            Function<T, Object> inspFrequencyGetter,
            Function<T, String> inspIdGetter,
            Function<T, String> inspCodeGetter,
            Function<T, Date> inspDateGetter,
            Function<List<String>, List<R>> fetchRecordsFunction,
            Function<R, String> recordEntityIdGetter,
            BiConsumer<List<R>, T> processRecordsConsumer){
        return processAndExportWord(isLoopRow,0,entities,parentPath,xcType,facilityCatGetter,inspFrequencyGetter,inspIdGetter,
                inspCodeGetter,inspDateGetter,fetchRecordsFunction,recordEntityIdGetter,processRecordsConsumer,null,null,null,null);
    }

    public static  <T, R> List<String> processAndExportWordByFixedRow(
            Integer fixedRow,
            List<T> entities,
            String parentPath,
            String xcType,
            Function<T, String> facilityCatGetter,
            Function<T, Object> inspFrequencyGetter,
            Function<T, String> inspIdGetter,
            Function<T, String> inspCodeGetter,
            Function<T, Date> inspDateGetter,
            Function<List<String>, List<R>> fetchRecordsFunction,
            Function<R, String> recordEntityIdGetter,
            BiConsumer<List<R>, T> processRecordsConsumer){
        return processAndExportWord(true,fixedRow,entities,parentPath,xcType,facilityCatGetter,inspFrequencyGetter,inspIdGetter,
                inspCodeGetter,inspDateGetter,fetchRecordsFunction,recordEntityIdGetter,processRecordsConsumer,null,null,null,null);
    }

    /**
     * 导出word单
     * @param isLoopRow true：采用LoopRowTableRenderPolicy策略，false:采用自定义策略
     * @param entities 主单对象集合
     * @param parentPath word存储路径地址
     * @param xcType 巡查类型：DM、FM
     * @param facilityCatGetter 设施类型
     * @param inspFrequencyGetter 巡查频率
     * @param inspIdGetter 主单ID
     * @param inspCodeGetter 单号名称
     * @param inspDateGetter 巡查日期
     * @param fetchRecordsFunction 列表记录ID集合
     * @param recordEntityIdGetter 列表记录ID
     * @param processRecordsConsumer 回显word病害值
     * @param mergeVerticalColumns 列纵向合并
     * @param mergeHorizontalColumns 列横向合并
     * @return
     * @param <T>
     * @param <R>
     */
    public static  <T, R> List<String> processAndExportWord(
            Boolean isLoopRow,
            List<T> entities,
            String parentPath,
            String xcType,
            Function<T, String> facilityCatGetter,
            Function<T, Object> inspFrequencyGetter,
            Function<T, String> inspIdGetter,
            Function<T, String> inspCodeGetter,
            Function<T, Date> inspDateGetter,
            Function<List<String>, List<R>> fetchRecordsFunction,
            Function<R, String> recordEntityIdGetter,
            BiConsumer<List<R>, T> processRecordsConsumer,
            List<Integer> mergeVerticalColumns,
            List<Integer> mergeHorizontalColumns
    ){
        return processAndExportWord(isLoopRow,0,entities,parentPath,xcType,facilityCatGetter,inspFrequencyGetter,inspIdGetter,
                inspCodeGetter,inspDateGetter,fetchRecordsFunction,recordEntityIdGetter,processRecordsConsumer,mergeVerticalColumns,mergeHorizontalColumns,null,null);
    }

    /**
     * 导出word单
     * @param isLoopRow true：采用LoopRowTableRenderPolicy策略，false:采用自定义策略
     * @param entities 主单对象集合
     * @param parentPath word存储路径地址
     * @param xcType 巡查类型：DM、FM
     * @param facilityCatGetter 设施类型
     * @param inspFrequencyGetter 巡查频率
     * @param inspIdGetter 主单ID
     * @param inspCodeGetter 单号名称
     * @param inspDateGetter 巡查日期
     * @param fetchRecordsFunction 列表记录ID集合
     * @param recordEntityIdGetter 列表记录ID
     * @param processRecordsConsumer 回显word病害值
     * @param dealDataConsumer 数据二次处理
     * @return
     * @param <T>
     * @param <R>
     */
    public static  <T, R> List<String> processAndExportWord(
            Boolean isLoopRow,
            List<T> entities,
            String parentPath,
            String xcType,
            Function<T, String> facilityCatGetter,
            Function<T, Object> inspFrequencyGetter,
            Function<T, String> inspIdGetter,
            Function<T, String> inspCodeGetter,
            Function<T, Date> inspDateGetter,
            Function<List<String>, List<R>> fetchRecordsFunction,
            Function<R, String> recordEntityIdGetter,
            BiConsumer<List<R>, T> processRecordsConsumer,
            Consumer<List<R>> dealDataConsumer){
        return processAndExportWord(isLoopRow,0,entities,parentPath,xcType,facilityCatGetter,inspFrequencyGetter,inspIdGetter,
                inspCodeGetter,inspDateGetter,fetchRecordsFunction,recordEntityIdGetter,processRecordsConsumer,null,null,dealDataConsumer,null);
    }

    /**
     * 导出word单
     * @param isLoopRow true：采用LoopRowTableRenderPolicy策略，false:采用自定义策略
     * @param fixedRow 是否固定行
     * @param entities 主单对象集合
     * @param parentPath word存储路径地址
     * @param xcType 巡查类型：DM、FM
     * @param facilityCatGetter 设施类型
     * @param inspFrequencyGetter 巡查频率
     * @param inspIdGetter 主单ID
     * @param inspCodeGetter 单号名称
     * @param inspDateGetter 巡查日期
     * @param fetchRecordsFunction 列表记录ID集合
     * @param recordEntityIdGetter 列表记录ID
     * @param processRecordsConsumer 回显word病害值
     * @param mergeVerticalColumns 列纵向合并
     * @param mergeHorizontalColumns 列横向合并
     * @param dealDataConsumer 数据二次处理
     * @param wordPathConsumer 生成的word路径，用于word后续二次处理
     * @return
     * @param <T>
     * @param <R>
     */
    public static  <T, R> List<String> processAndExportWord(
            Boolean isLoopRow,
            Integer fixedRow,
            List<T> entities,
            String parentPath,
            String xcType,
            Function<T, String> facilityCatGetter,
            Function<T, Object> inspFrequencyGetter,
            Function<T, String> inspIdGetter,
            Function<T, String> inspCodeGetter,
            Function<T, Date> inspDateGetter,
            Function<List<String>, List<R>> fetchRecordsFunction,
            Function<R, String> recordEntityIdGetter,
            BiConsumer<List<R>, T> processRecordsConsumer,
            List<Integer> mergeVerticalColumns,
            List<Integer> mergeHorizontalColumns,
            Consumer<List<R>> dealDataConsumer,
            Consumer<String> wordPathConsumer
    ) {
        List<String> wordPathList = Lists.newArrayList();
        if (CollectionUtil.isEmpty(entities)) {
            return wordPathList;
        }
        // 获取主单ID列表并查询对应记录
        List<String> entityIds = entities.stream().map(inspIdGetter).collect(Collectors.toList());
        List<R> records = fetchRecordsFunction.apply(entityIds);

        // 按主单ID分组记录
        Map<String, List<R>> recordMap = records.stream().collect(Collectors.groupingBy(recordEntityIdGetter::apply, LinkedHashMap::new, Collectors.toList()));

        List<CompletableFuture<Void>> futures = Lists.newArrayList();

        entities.forEach(entity -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                String entityId = inspIdGetter.apply(entity);
                List<R> entityRecords = recordMap.getOrDefault(entityId, Lists.newArrayList());

                // 处理记录数据
                if (CollectionUtil.isNotEmpty(entityRecords)) {
                    processRecordsConsumer.accept(entityRecords, entity);
                    if (dealDataConsumer != null) {
                        dealDataConsumer.accept(entityRecords);
                    }
                }
                // 构建Word数据模型
                Map<String, Object> wordModel = new HashMap<>();
                // 使用BeanUtil.beanToMap替代toBean避免潜在类型问题
                BeanUtil.beanToMap(entity, wordModel, false, false);
                wordModel.put("inspDate", DateUtil.format(inspDateGetter.apply(entity), "yyyy-MM-dd"));
                wordModel.put("recList", entityRecords);

                //设施类型
                String facilityCat = facilityCatGetter.apply(entity);
                Object frequency = ObjectUtil.isEmpty(inspFrequencyGetter.apply(entity)) ? 1 : inspFrequencyGetter.apply(entity);

                String templatePath = StrUtil.format("excel/nmFinsp/{}_{}_{}.docx", facilityCat, xcType.toUpperCase(), frequency);

                // 生成Word文件
                try (InputStream templateStream = WordUtil.class.getClassLoader().getResourceAsStream(templatePath)) {
                    String fileName = String.format("%s_%s.docx", inspCodeGetter.apply(entity), RandomUtil.randomString(6));
                    File outputFile = new File(parentPath, fileName);
                    exportWordDocument(templateStream, wordModel, outputFile, mergeVerticalColumns, mergeHorizontalColumns, isLoopRow,fixedRow);
                    String absolutePath = outputFile.getAbsolutePath();
                    if (wordPathConsumer != null) {
                        wordPathConsumer.accept(absolutePath);
                    }
                    wordPathList.add(absolutePath);
                } catch (Exception e) {
                    throw new BaseException("导出Word失败: " + e.getMessage(), e);
                }
            });
            futures.add(future);
        });
        H_FutureHelper.sequence(futures).join();
        return wordPathList;
    }

    /**
     * poi-tl word导出
     * @param templateStream
     * @param dataModel
     * @param outputFile
     * @throws IOException
     */
    private static void exportWordDocument(InputStream templateStream, Map<String, Object> dataModel, File outputFile,List<Integer> mergeVerticalColumns
            ,List<Integer> mergeHorizontalColumns,Boolean isLoopRow,Integer fixedRow) throws IOException {
        Configure config = Configure.builder()
                .bind("recList", new ExistingLoopRowTableRenderPolicy(false,isLoopRow,fixedRow))
                .build();
        //  new LoopRowTableRenderPolicy();
        // 采用try-with-resources确保流自动关闭，增强异常处理健壮性
        try (OutputStream out = Files.newOutputStream(outputFile.toPath())) {
            XWPFTemplate.compile(templateStream, config).render(dataModel).writeAndClose(out);
        }

        //单元个合并
        if (CollectionUtil.isNotEmpty(mergeVerticalColumns) || CollectionUtil.isNotEmpty(mergeHorizontalColumns)){
            XWPFTemplate compile = XWPFTemplate.compile(outputFile);
            List<XWPFTable> allTables = compile.getXWPFDocument().getAllTables();
            for (XWPFTable table : allTables) {
                if (CollectionUtil.isNotEmpty(mergeVerticalColumns)){
                    mergeVerticalColumns(table,mergeVerticalColumns);
                }

                if (CollectionUtil.isNotEmpty(mergeHorizontalColumns)){
                    mergeHorizonalColumns(table,mergeHorizontalColumns);
                }
            }
            try(OutputStream out = new FileOutputStream(outputFile)){
                compile.writeAndClose(out);
            }
        }
    }


    public static void dynamicMerge(XWPFTable table ,List<Integer> mergeColumns){
        mergeVerticalColumns(table,mergeColumns);
        mergeHorizonalColumns(table,mergeColumns);
    }

    /**
     * 纵向合并
     *
     * @param table
     * @param mergeColumns
     */
    private static void mergeVerticalColumns(XWPFTable table , List<Integer> mergeColumns){
        for (int col : mergeColumns) {
            if (col >= table.getRow(0).getTableCells().size()) continue;

            int mergeStartRow = -1;
            String prevValue = "";
            for (int row = 0; row < table.getNumberOfRows(); row++) {
                String currentValue = getCellText(table.getRow(row),col);
                if (currentValue.equals(prevValue)) {
                    if (mergeStartRow == -1) mergeStartRow = row - 1;
                    if (row == table.getNumberOfRows() - 1) { // 处理最后一行
                        TableTools.mergeCellsVertically(table, col, mergeStartRow, row);
                    }
                } else {
                    if (mergeStartRow != -1) {
                        TableTools.mergeCellsVertically(table, col, mergeStartRow, row - 1);
                    }
                    mergeStartRow = -1;
                }
                prevValue = currentValue;
            }
        }
    }

    /**
     * 横向合并
     * @param table
     * @param mergeColumns
     */
    private static void mergeHorizonalColumns(XWPFTable table, List<Integer> mergeColumns){
        for (int rowNum = 0; rowNum < table.getNumberOfRows(); rowNum++) {
            XWPFTableRow row = table.getRow(rowNum);
            for (int col : mergeColumns) {
                if (col >= row.getTableCells().size()) continue;
                String prevValue = getCellText(row, col);
                String nextValue = getCellText(row, col + 1);
                if (prevValue.equals(nextValue) && StrUtil.isNotBlank(prevValue)){
                    TableTools.mergeCellsHorizonal(table, rowNum, col, col+1);
                }
            }
        }
    }

    private static String getCellText(XWPFTableRow row, int col) {
        if (row.getCell(col) == null) {
            return null;
        }
        return row.getCell(col).getText().trim();
    }
}
