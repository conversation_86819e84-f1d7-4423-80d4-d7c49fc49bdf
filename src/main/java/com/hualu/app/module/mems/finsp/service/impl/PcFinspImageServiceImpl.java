package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.mapper.PcFinspImageMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspImageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 结构物排查照片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class PcFinspImageServiceImpl extends ServiceImpl<PcFinspImageMapper, PcFinspImage> implements PcFinspImageService {

    @Override
    public void removeByRecordIds(List<String> recordIds) {
        LambdaQueryWrapper<PcFinspImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PcFinspImage::getRecordId, recordIds);
        this.remove(wrapper);
    }

    @Override
    public Map<String,List<PcFinspImage>> selectByRecordIds(List<String> recordIds) {
        if (CollectionUtil.isEmpty(recordIds)) {
            return Maps.newHashMap();
        }
        LambdaQueryWrapper<PcFinspImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(PcFinspImage::getRecordId, recordIds);
        List<PcFinspImage> imageList = list(wrapper);
        Map<String, List<PcFinspImage>> recordMap = imageList.stream().collect(Collectors.groupingBy(PcFinspImage::getRecordId));
        return recordMap;
    }

    @Override
    public boolean isDealFinsh(String recordId) {
        LambdaQueryWrapper<PcFinspImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcFinspImage::getRecordId, recordId).in(PcFinspImage::getImageType,"2","3");
        return count(wrapper) > 0 ? true : false;
    }

    @Override
    public String getRecordIdByFileId(String fileId) {
        LambdaQueryWrapper<PcFinspImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PcFinspImage::getImageId,fileId);
        wrapper.select(PcFinspImage::getRecordId);

        List<PcFinspImage> recordIds = list(wrapper).stream().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(recordIds)){
            return null;
        }
        return recordIds.get(0).getRecordId();
    }
}
