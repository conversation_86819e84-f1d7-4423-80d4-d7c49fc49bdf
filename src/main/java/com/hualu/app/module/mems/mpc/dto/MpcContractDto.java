package com.hualu.app.module.mems.mpc.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 合同数据
 */
@Data
public class MpcContractDto implements Serializable {

    //合同ID
    private String contrId;
    //合同编码
    private String contrCode;
    //合同类型
    private String contrType;

    //合同名称
    private String contrName;
    //合同金额
    private Double contrMoeny;

    //管养单位
    private String mntOrgId;
    //管养单位名称
    private String mntOrgName;

    //养护单位
    private String implOrgId;
    //养护单位名称
    private String implOrgName;
}
