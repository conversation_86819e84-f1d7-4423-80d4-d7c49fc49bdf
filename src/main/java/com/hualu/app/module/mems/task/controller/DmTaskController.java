package com.hualu.app.module.mems.task.controller;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.task.dto.DmTaskDto;
import com.hualu.app.module.mems.task.entity.DmTask;
import com.hualu.app.module.mems.task.mapper.DmTaskMapper;
import com.hualu.app.module.mems.task.service.DmTaskService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_RestResultHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 任务单
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Slf4j
@RestController
@RequestMapping("/dmTask")
public class DmTaskController extends M_MyBatisController<DmTask, DmTaskMapper> {

    @Autowired
    DmTaskService taskService;

    /**
     * 删除任务单
     * @param mtaskId
     * @return
     */
    @GetMapping("/delete/{mtaskId}")
    public RestResult<String> deleteDmTask(@PathVariable("mtaskId") String mtaskId){
        taskService.delDmTask(mtaskId);
        return RestResult.success("操作成功");
    }

    /**
     * 添加或修改任务单
     * @return
     * @throws Exception
     */
    @SneakyThrows
    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("添加或修改任务单")
    @PostMapping(value = "/saveOrUpdate")
    public RestResult<DmTask> saveOrUpdate(@RequestBody DmTask dmTask){
        if (dmTask.getProcessinstid() == null || dmTask.getProcessinstid()==0){
            dmTask.setStatus(0);
            String processDefName = "gdcg.emdc.mems.dm.DmTaskWorkflow";
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "维修任务单["+ dmTask.getMtaskCode()+"]", "");
            log.info("维修任务单实例ID："+processInstId);
            dmTask.setProcessinstid(processInstId);
        }
        taskService.saveOrUpdateTask(dmTask);
        return RestResult.success(dmTask);
    }




    /**
     * 获取下一张单信息
     * @return
     */
    @GetMapping("getNextCode")
    public RestResult<DmTaskDto> getNextCode(){
        DmTaskDto nextCode = taskService.getNextCode();
        return RestResult.success(nextCode);
    }


    /**
     * 未验收的任务单
     * @return
     */
    @PostMapping("selectNoAcceptTask")
    public RestResult<List<DmTaskDto>> selectNoAcceptTask(){
        Map reqParam = getReqParam();
        QueryWrapper<DmTask> queryWrapper = setWrapper(reqParam);
        queryWrapper.eq("status",2);
        queryWrapper.eq("mnt_org_id", CustomRequestContextHolder.getOrgIdString());
        queryWrapper.apply("TOTAL_COUNT!=ACCPT_COUNT");
        queryWrapper.orderByDesc("asked_start_Date");
        IPage iPage = baseMapper.selectPage(getPage(), queryWrapper);
        return H_RestResultHelper.returnPage(iPage);
    }

    /**
     * 分页查询
     * @return
     */
    @PostMapping("selectPage")
    public RestResult<List<DmTaskDto>> selectPageTaskRecord(){
        Map reqParam = getReqParam();
        Page page = getPage();
        QueryWrapper queryWrapper = setWrapper(reqParam);
        IPage iPage = taskService.selectPage(page,queryWrapper);
        return H_RestResultHelper.returnPage(iPage);
    }

    public QueryWrapper setWrapper(Map reqParam){
        QueryWrapper<DmTask> queryWrapper = new QueryWrapper();
        queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());

        String startDate = (String) reqParam.getOrDefault("startDate","");
        if (StrUtil.isNotBlank(startDate)){
            queryWrapper.apply("SEND_TIME >= to_date({0},'YYYY-MM-dd')",startDate);
            reqParam.remove("startDate");
        }

        String endDate = (String) reqParam.getOrDefault("endDate","");
        if (StrUtil.isNotBlank(endDate)){
            queryWrapper.apply("SEND_TIME <= to_date({0},'YYYY-MM-dd')",endDate);
            reqParam.remove("endDate");
        }

        String status = reqParam.getOrDefault("status", "").toString();
        String taskSql = H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.valueOf(status), "dm_task");

        if (StrUtil.isNotBlank(taskSql)){
            queryWrapper.exists(taskSql);
            reqParam.remove("status");
        }

        //查询超期
        String overTime = reqParam.getOrDefault("overTime", "").toString();
        if (StrUtil.isNotBlank(overTime)){
            reqParam.remove("overTime");
            //未超期
            if ("0".equals(overTime)){
                queryWrapper.exists("select 1 from memsdb.dm_task_detail d left join memsdb.dm_task_accpt_detail a on a.mtask_dtl_id = d.mtask_dtl_id left join memsdb.dm_task_accpt ac on ac.mtask_accpt_id = a.mtask_accpt_id where d.mtask_id = dm_task.mtask_id and (to_date(a.repair_date) <= dm_task.asked_compl_date or a.repair_date is null and sysdate <= dm_task.asked_compl_date)");
            }
            //已超期
            if ("1".equals(overTime)){
                queryWrapper.exists("select 1 from memsdb.dm_task_detail d left join memsdb.dm_task_accpt_detail a on a.mtask_dtl_id = d.mtask_dtl_id left join memsdb.dm_task_accpt ac on ac.mtask_accpt_id=a.mtask_accpt_id where d.mtask_id = dm_task.mtask_id and (to_date(a.repair_date)>dm_task.asked_compl_date or a.repair_date is null and sysdate>dm_task.asked_compl_date)");
            }
        }
        if (ObjectUtil.isEmpty(reqParam.get("sortField"))){
            queryWrapper.orderByDesc("send_time");
        }
        H_BatisQuery.setFieldValue2In(queryWrapper,reqParam, DmTaskDto.class);
        return queryWrapper;
    }

}
