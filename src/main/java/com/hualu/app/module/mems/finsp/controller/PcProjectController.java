package com.hualu.app.module.mems.finsp.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.file.FileNameUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.dto.PcProjectDto;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.mapper.PcProjectMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspService;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.hualu.app.module.mems.finsp.service.PcProjectService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.api.util.hp.H_UUIDUtils;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;


@Api(tags = "结构物专项表",description = "结构物专项表 前端控制器")
@RestController
@RequestMapping("/pcProject")
public class PcProjectController extends M_MyBatisController<PcProject, PcProjectMapper> {

    @Autowired
    PcProjectService pcProjectService;

    @Autowired
    PcFinspService pcFinspService;

    @Autowired
    PcProjectScopeService scopeService;

    @Value("${fileReposity}")
    String fileReposity;


    /**
     * 查询最新专项项目
     * @param mntType 单位类型（1：定检单位，2：养护单位）
     * @param facilityCat 设施类型：BP、HD
     * @return
     */
    @GetMapping("getLastProject")
    public RestResult<PcProject> getLastProject(@RequestParam(defaultValue = "1") String mntType, @RequestParam(defaultValue = "BP") String facilityCat){
        LambdaQueryWrapper<PcProject> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcProject::getMntType,mntType).eq(PcProject::getFacilityCat,facilityCat)
                .eq(PcProject::getLatest,1).eq(PcProject::getMntOrgId, CustomRequestContextHolder.getOrgIdString()).orderByDesc(PcProject::getCreateTime);
        PcProject one = pcProjectService.getOne(queryWrapper,false);
        return RestResult.success(one);
    }

    /**
     * 创建结构专项项目，并导入基础数据
     * @param data
     * @param file
     * @return
     */
    @PostMapping("createProject")
    @SneakyThrows
    public RestResult<PcProject> createProject(PcProjectDto data, @RequestParam(value = "file",required = true) MultipartFile file){
        PcProject pcProject = BeanUtil.toBean(data, PcProject.class);
        H_CValidator.validator2Exception(pcProject);
        //后缀名称
        String extName = FileNameUtil.extName(file.getOriginalFilename());
        File uploadFile = new File(fileReposity + File.separator + H_UUIDUtils.generateUUID() + "." + extName);
        file.transferTo(uploadFile);
        PcProject project = pcProjectService.createProject(pcProject, uploadFile);
        return RestResult.success(project);
    }

    /**
     * 删除项目（把该项目涉及的结构物逻辑删除，检查单不删除）
     * @param prjId
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @GetMapping("deleteProject")
    public RestResult<String> deleteProject(String prjId){
        //pcFinspService.removeByPrjId(prjId);
        scopeService.removeByPrjId(prjId);
        //pcProjectService.removeById(prjId);
        return RestResult.success("操作成功");
    }
}
