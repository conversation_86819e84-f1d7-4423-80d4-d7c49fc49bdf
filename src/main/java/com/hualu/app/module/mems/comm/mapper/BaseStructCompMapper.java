package com.hualu.app.module.mems.comm.mapper;

import com.hualu.app.module.mems.comm.entity.BaseStructComp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface BaseStructCompMapper extends BaseMapper<BaseStructComp> {

    @Select("select g.dss_type,g.STRUCT_COMP_NAME,g.STRUCT_COMP_CODE  from memsdb.BASE_STRUCT_COMP h join memsdb.DSS_TYPE g on h.STRUCT_COMP_CODE = g.STRUCT_COMP_CODE and h.p_struct_comp_id = '9455226EAA1F48B381DD35FCEC15C9A6' ")
    List<BaseStructComp> queryStructByJA();


    List<BaseStructComp> selectStructCompByTj(String structCompId);
}
