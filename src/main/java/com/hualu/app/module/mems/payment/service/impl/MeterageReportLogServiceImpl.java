package com.hualu.app.module.mems.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.autoInspector.entity.DownloadVo;
import com.hualu.app.module.mems.payment.entity.MeterageReportLog;
import com.hualu.app.module.mems.payment.service.MeterageReportLogService;
import com.hualu.app.module.mems.payment.mapper.MeterageReportLogMapper;
import com.hualu.app.module.mongo.dto.MgFileDto;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.joda.time.DateTimeUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【METERAGE_REPORT_LOG(上传报表日志)】的数据库操作Service实现
* @createDate 2025-06-19 08:09:51
*/
@Service
public class MeterageReportLogServiceImpl extends ServiceImpl<MeterageReportLogMapper, MeterageReportLog>
    implements MeterageReportLogService{

    @Override
    public void saveLog(MgFileDto mgFileDto,String contrId) {
        MeterageReportLog meterageReportLog = new MeterageReportLog();
        meterageReportLog.setFileId(mgFileDto.getId());
        meterageReportLog.setFileName(mgFileDto.getFilename());
        meterageReportLog.setCreateTime(DateTimeUtil.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
        meterageReportLog.setId(StringUtil.getUUID());
        meterageReportLog.setUserCode(CustomRequestContextHolder.getUserCode());
        meterageReportLog.setContrId(contrId);
        this.save(meterageReportLog);
    }

    @Override
    public List<DownloadVo> getDssImageList(String code) {
        return this.baseMapper.getDssImageList(code);
    }
}




