package com.hualu.app.module.mems.machineclean.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.mems.machineclean.entity.DmMachineCleaning;
import org.apache.ibatis.annotations.Param;

public interface DmMachineCleaningMapper extends BaseMapper<DmMachineCleaning> {

  IPage<DmMachineCleaning> getMachineCleanList(
      @Param("p") IPage<DmMachineCleaning> p,
      @Param("ew") Wrapper<DmMachineCleaning> wrapper
  );
}