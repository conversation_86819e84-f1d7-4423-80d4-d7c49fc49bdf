package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.dto.FailDinspDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.vo.NmDinspExtraInfo;
import com.hualu.app.module.mems.nminsp.vo.NmDinspFacilityCatGroupVo;

import java.util.List;

/**
 * <p>
 * 新版日常巡查公共单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
public interface NmDinspCommonService extends IService<NmDinspCommon> {

    void saveOrUpdateRecord(NmDinspRecord entity);

    NmDinspCommon initNmDinspCommon(String orgIdString);

    /**
     * 关联公单
     * @param nmDinsp
     */
    String bindDinspCommon(NmDinsp nmDinsp);

    List<NmDinspFacilityCatGroupVo> listNmDinspFacilityGroupVo(String commonDinspId);

    List<NmDinspExtraInfo> listNmDinspExtraInfo(List<String> commonDinspIdList);

    IPage<NmDinspCommon> selectPageFor(Page<NmDinspCommon> page, QueryWrapper<NmDinspCommon> queryWrapper, String taskSql);


    List<FailDinspDto> listFailDinsp();
}
