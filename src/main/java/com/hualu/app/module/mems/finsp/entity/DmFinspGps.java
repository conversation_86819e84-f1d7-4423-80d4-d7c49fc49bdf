package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
/**
 * <p>
 * 经常检查单GPS位置 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmFinspGps对象", description="经常检查单GPS位置")
public class DmFinspGps implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("GPS_ID")
    private String gpsId;

    @ApiModelProperty(value = "经常检查单ID")
    @TableField("FINSP_ID")
    private String finspId;

    @ApiModelProperty(value = "高程")
    @TableField("HEIGHT")
    private Double height;

    @ApiModelProperty(value = "速度")
    @TableField("SPEED")
    private Double speed;

    @ApiModelProperty(value = "经度")
    @TableField("LON")
    private Double lon;

    @ApiModelProperty(value = "纬度")
    @TableField("LAT")
    private Double lat;

    @ApiModelProperty(value = "时间")
    @TableField("TIME")
    private LocalDateTime time;

    @ApiModelProperty(value = "轨迹ID")
    @TableField("TRACK_ID")
    private String trackId;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    /**
     * 巡查人
     */
    @TableField(value = "INSP_PERSON")
    private String inspPerson;
}
