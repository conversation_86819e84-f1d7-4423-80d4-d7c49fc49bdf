package com.hualu.app.module.mems.dss.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DssImage对象", description="")
public class DssImage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DSS_IMAGE_ID")
    private String dssImageId;

    @TableField("DSS_ID")
    private String dssId;

    @TableField("FILE_ID")
    private String fileId;

    @ApiModelProperty(value = "6检查检查 7日常巡查 8路政通知书的工作照片")
    @TableField("IMAGE_TYPE")
    private Integer imageType;

    @ApiModelProperty(value = "处理前后区分（null和0-处理前，1处理后）")
    @TableField("PROCESSING")
    private Integer processing;

    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag=0;

}
