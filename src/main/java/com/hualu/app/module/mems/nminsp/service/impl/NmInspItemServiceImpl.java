package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmInspItem;
import com.hualu.app.module.mems.nminsp.mapper.NmInspItemMapper;
import com.hualu.app.module.mems.nminsp.service.NmInspItemService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 新版日常巡查及经常检查结论配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Service
public class NmInspItemServiceImpl extends ServiceImpl<NmInspItemMapper, NmInspItem> implements NmInspItemService {

    @Override
    public List<NmInspItem> listXcTypeByDinsp(NmDinsp nmDinsp, String xcType) {
        if (StrUtil.isBlank(xcType)){
            throw new RuntimeException("xcType is null");
        }
        LambdaQueryWrapper<NmInspItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmInspItem::getFacilityCat,nmDinsp.getFacilityCat());
        queryWrapper.eq(NmInspItem::getXcType,xcType.toUpperCase());
        queryWrapper.eq(NmInspItem::getIsDelete,0);
        queryWrapper.eq(ObjectUtil.isNotEmpty(nmDinsp.getInspFrequency()),NmInspItem::getInspFrequency,nmDinsp.getInspFrequency());
        return list(queryWrapper);
    }

    @Override
    public Map<String, NmInspItem> listXcTypeToMap(String xcType) {

        List<NmInspItem> list = lambdaQuery().eq(NmInspItem::getXcType, xcType).list();
        Map<String, NmInspItem> itemMap = list.stream().collect(Collectors.toMap(NmInspItem::getItemId, Function.identity()));
        return itemMap;
    }

    @Override
    public List<NmInspItem> listXcTypeByFinsp(NmFinsp nmDinsp, String xcType) {
        if (StrUtil.isBlank(xcType)){
            throw new RuntimeException("xcType is null");
        }
        LambdaQueryWrapper<NmInspItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmInspItem::getFacilityCat,nmDinsp.getFacilityCat());
        queryWrapper.eq(NmInspItem::getXcType,xcType.toUpperCase());
        queryWrapper.eq(NmInspItem::getIsDelete,0);
        queryWrapper.eq(ObjectUtil.isNotEmpty(nmDinsp.getInspFrequency()),NmInspItem::getInspFrequency,nmDinsp.getInspFrequency());
        return list(queryWrapper);
    }
}
