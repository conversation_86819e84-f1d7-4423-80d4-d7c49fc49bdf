package com.hualu.app.module.mems.finsp.dto.export;

import lombok.Data;
import lombok.experimental.Accessors;
import org.assertj.core.util.Lists;

import java.io.Serializable;
import java.util.List;

@Accessors(chain = true)
@Data
public class CulvertWordDto implements Serializable {
    private static final long serialVersionUID = 1L;

    List<DicDto> structType = Lists.newArrayList();

    List<DicDto> partType = Lists.newArrayList();

    List<DicDto> functionType = Lists.newArrayList();

    List<DicDto> attr1 = Lists.newArrayList();

    List<DicDto> attr3 = Lists.newArrayList();

    List<DicDto> attr5 = Lists.newArrayList();

    List<DicDto> attr7 = Lists.newArrayList();

    List<DicDto> attr9 = Lists.newArrayList();

    List<DicDto> attr11 = Lists.newArrayList();

    List<DicDto> attr13 = Lists.newArrayList();

    List<DicDto> attr15 = Lists.newArrayList();

    List<DicDto> attr17 = Lists.newArrayList();

    List<DicDto> attr18 = Lists.newArrayList();

    List<DicDto> attr19 = Lists.newArrayList();

}
