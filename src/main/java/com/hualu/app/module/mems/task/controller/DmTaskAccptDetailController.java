package com.hualu.app.module.mems.task.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.task.dto.accpt.DmAccptUpdateDto;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDssViewDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import com.hualu.app.module.mems.task.mapper.DmTaskAccptDetailMapper;
import com.hualu.app.module.mems.task.service.DmTaskAccptDetailService;
import com.hualu.app.module.mems.task.service.DmTaskDetailService;
import com.hualu.app.module.mems.task.service.DmTaskService;
import com.hualu.app.utils.H_RestResultHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 维修工程记录
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@RestController
@RequestMapping("/dmTaskAccptDetail")
public class DmTaskAccptDetailController extends M_MyBatisController<DmTaskAccptDetail, DmTaskAccptDetailMapper> {

    @Autowired
    DmTaskAccptDetailService detailService;

    @Autowired
    DmTaskDetailService taskDetailService;

    @Autowired
    DmTaskService taskService;

    @Autowired
    DssImageService imageService;

    @Autowired
    DssInfoService dssInfoService;


    /**
     * 维修工程记录-补充措施（PC端）
     * @param dtos
     * @return
     */
    @PostMapping("addAccptDetail")
    public RestResult addAccptDetail(@RequestBody List<DmTaskAccptDetail> dtos) {
        //查询病害信息
        DmTaskAccptDetail dbAccpt = baseMapper.selectOneByDssId(dtos.get(0).getDssId());

        dtos.forEach(dto->{
            dto.setLineDirect(dbAccpt.getLineDirect());
            dto.setMainRoadId(dbAccpt.getMainRoadId());
            dto.setStake(dbAccpt.getStake());
            dto.setRlStake(dbAccpt.getRlStake());
            dto.setLogicStakeNum(dbAccpt.getLogicStakeNum());
            dto.setAcceptStatus(0);
            dto.setIsTaskRecord(0);
            dto.setLane(dbAccpt.getLane());
            dto.setRpIntrvlId(dbAccpt.getRpIntrvlId());
            dto.setBusinessType(dbAccpt.getBusinessType());
            dto.setRampId(dbAccpt.getRampId());
            dto.setAcceptAmount(0d);
            dto.setApplyAmount(0d);
        });
        detailService.saveBatch(dtos);
        return RestResult.success("操作成功");
    }

    /**
     * 删除工程记录
     * @param idsByTask 任务单添加的措施
     * @param idsByOther 维修工程界面添加的措施
     * @return
     */
    @PostMapping("/delByIds")
    public RestResult delByIds(@RequestParam(value = "idsByTask",required = false) String idsByTask
            ,@RequestParam(value = "idsByOther",required = false)String idsByOther){
        if (StrUtil.isBlank(idsByOther) && StrUtil.isBlank(idsByTask)) {
            return RestResult.error("病害ID不能为空");
        }
        List<String> taskIds = StrUtil.split(idsByTask, ",",true,true);;
        List<String> otherIds = StrUtil.split(idsByOther, ",",true,true);;

        if (CollectionUtil.isNotEmpty(taskIds)){
            //设置任务单明细的状态
            taskDetailService.updateStatus(taskIds);
            detailService.removeByIds(taskIds);
        }
        if (CollectionUtil.isNotEmpty(otherIds)){
            detailService.removeByIds(otherIds);
        }
        return RestResult.success("操作成功");
    }


    /**
     * 生成任务单详情（验收单选择任务单）
     * @param mtaskIds 任务单ID（多个以逗号分隔）
     * @param mtaskAccptId 验收单ID
     * @return
     */
    @PostMapping("createAccptDetails")
    @Transactional(rollbackFor = Exception.class)
    public RestResult createAccptDetails(@RequestParam String mtaskIds,@RequestParam String mtaskAccptId){
        //查询任务单未验收的措施明细
        List<String> mtaskIdList = StrUtil.split(mtaskIds, ",");
        if (mtaskIdList.size() > 3){
            throw new BaseException("最多不能超过3张任务单");
        }
        if (CollectionUtil.isNotEmpty(mtaskIdList)){
            List<DmTaskDetailDto> detailDtos = taskDetailService.selectNoAccpt(mtaskIdList);
            //初始化申请验收及验收数量
            detailDtos.forEach(item->{
                item.setApplyAmount(item.getMpitemAccount());
                item.setAcceptAmount(item.getMpitemAccount());
            });

            Set<String> dssIds = detailDtos.stream().map(DmTaskDetailDto::getDssId).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            dssInfoService.updateRepairStatus(Lists.newArrayList(dssIds),1);

            Map<String, List<DmTaskDetailDto>> mtaskMap = detailDtos.stream().collect(Collectors.groupingBy(DmTaskDetailDto::getMtaskId));
            //生成维修工程记录
            mtaskMap.forEach((k,v)->{
                detailService.createDmTaskAccptDetails(mtaskAccptId,k,v);
                //修改任务单状态
                taskService.updateDmtaskStatus(k,3);
            });
        }
        return RestResult.success("操作成功");
    }

    /**
     * 申请验收
     * @param mtaskAccptDtlIds 维修工程记录ID（多个以逗号分隔）
     * @param accptId 验收单ID （验收单ID来源于待办验收单）
     * @return
     */
    @PostMapping("requestAccept")
    public RestResult requestAccept(@RequestParam String mtaskAccptDtlIds,@RequestParam String accptId){
        List<String> ids = StrUtil.split(mtaskAccptDtlIds, ",");
        List<DmTaskAccptDetail> items = Lists.newArrayList();
        ids.forEach(dtlId->{
            DmTaskAccptDetail bean = new DmTaskAccptDetail();
            bean.setMtaskAccptDtlId(dtlId);
            bean.setMtaskAccptId(accptId);
            items.add(bean);
        });
        if (items.size() != 0){
            detailService.updateBatchById(items);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 根据病害ID，查询工程细目ID集合
     * @param dssIds 病害id,以逗号分隔
     * @param noAccept 是否未验收 1：是 ，2：否
     * @return
     */
    @GetMapping("getAcceptIdsByDssIds")
    public RestResult<List<String>> getAcceptIdsByDssIds(@RequestParam(required = true) String dssIds,Integer noAccept) {
        List<String> split = StrUtil.split(dssIds, ",");
        LambdaQueryWrapper<DmTaskAccptDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DmTaskAccptDetail::getDssId,split);
        if (1 == noAccept){
            queryWrapper.isNull(DmTaskAccptDetail::getMtaskAccptId);
        }
        List<String> idList = detailService.list(queryWrapper).stream().map(DmTaskAccptDetail::getMtaskAccptDtlId).collect(Collectors.toList());
        return RestResult.success(idList);
    }

    /**
     * 编辑工程记录
     * @param data 数据（参考返回值DmAccptUpdateDto）
     * @param files 维修前照片
     * @param afterFiles 维修后照片
     * @return
     */
    @PostMapping("updateAcceptDetails")
    public RestResult<List<DmAccptUpdateDto>> updateAcceptDetails(@RequestParam(value = "data") String data,@RequestParam(value = "files",required = false) MultipartFile[] files
            ,@RequestParam(value = "afterFiles",required = false) MultipartFile[] afterFiles){
        List<DmAccptUpdateDto> dtos = JSONUtil.toList(data, DmAccptUpdateDto.class);
        List<DmTaskAccptDetail> details = Lists.newArrayList();
        dtos.forEach(item->{
            H_CValidator.validator2Exception(item);
            DmTaskAccptDetail bean = BeanUtil.toBean(item, DmTaskAccptDetail.class);
            details.add(bean);
        });
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        List<String> afterFilesIds = H_UploadHelper.uploadFile(afterFiles);

        String dssId = dtos.stream().collect(Collectors.groupingBy(DmAccptUpdateDto::getDssId)).keySet().stream().findFirst().get();
        if (CollectionUtil.isNotEmpty(fileIds)){
            imageService.saveDssImageForApp(dssId,StrUtil.join(",",fileIds),1,0);
        }

        if (CollectionUtil.isNotEmpty(afterFilesIds)){
            imageService.saveDssImageForApp(dssId,StrUtil.join(",",afterFilesIds),1,1);
        }
        detailService.updateBatchById(details);
        return RestResult.success(dtos);
    }

    /**
     * 照片上传
     * @param dssId 病害ID
     * @param processing 0：维修前  1：维修后
     * @param files 图片文件
     * @return
     */
    @PostMapping("uploadDssImage")
    public RestResult uploadDssImage(@RequestParam(value = "dssId",required = true) String dssId, @RequestParam(value = "processing",required = true) Integer processing,
                                     @RequestParam(value = "files") MultipartFile[] files){
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            imageService.saveDssImageForApp(dssId,StrUtil.join(",",fileIds),1,processing);
        }
        return RestResult.success(StrUtil.join(",",fileIds), "操作成功");
    }

    /**
     * 任务分配（养护单位内部安排）
     */
    @PostMapping("taskPlan")
    public Object taskPlan(@RequestBody DmTaskAccptDetailDto detailDto){
        if (StrUtil.isBlank(detailDto.getMtaskAccptDtlId())){
            return ApiResult.fail("主键不能为空");
        }
        List<String> ids = StrUtil.split(detailDto.getMtaskAccptDtlId(), ",");
        LambdaUpdateWrapper<DmTaskAccptDetail> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.in(DmTaskAccptDetail::getMtaskAccptDtlId,ids);
        updateWrapper.set(DmTaskAccptDetail::getRemark,detailDto.getRemark());
        detailService.update(updateWrapper);
        return ApiResult.ok();
    }

    /**
     * 分页查询（PC端：以措施为基准）
     * @return
     */
    @PostMapping("selectPage")
    public RestResult<List<DmTaskAccptDetailDto>> selectPageTaskRecord(){
        Map reqParam = getReqParam();
        Page page = getPage();
        QueryWrapper queryWrapper = setWrapper(reqParam,true);
        List<DmTaskAccptDetailDto> dtos = detailService.selectPageTaskRecord(page,queryWrapper);
        page.setRecords(dtos);
        return H_RestResultHelper.returnPage(page);
    }

    /**
     * 维修工程记录分页查询（PC端：以病害为基准）
     * @param startDate 修复开始日期
     * @param endDate 修复结束日期
     * @param acceptStatus 维修状态 0：待修复；1：验收中；2：已验收；3：全部
     * @param businessType 业务类型 1: 无病害保洁 2：有病害维修保养
     * @param contrId 合同ID
     * @param remark 任务分配内容
     * @return
     */
    @PostMapping("selectPageByDss")
    public RestResult<List<DmTaskAccptDetailDto>> selectPageByDss(String startDate,String endDate,String acceptStatus,String businessType,String contrId,String remark){
        Map reqParam = getReqParam();
        Page page = getPage();
        QueryWrapper queryWrapper = setWrapper(reqParam,false);
        List<DmTaskAccptDetailDto> dtos = detailService.selectPageByDss(page,queryWrapper);
        page.setRecords(dtos);
        return H_RestResultHelper.returnPage(page);
    }

    /**
     * 获取病害对应的措施列表（PC端）
     * @param dssId 病害ID
     * @return
     */
    @GetMapping("getMpItemByDssId")
    public RestResult<List<DmTaskMpItemViewDto>> getMpItemByDssId(String dssId){
        List<DmTaskMpItemViewDto> mpItemViewDto = detailService.getMpItemViewDto(dssId);
        RestResult<List<DmTaskMpItemViewDto>> success = RestResult.success(mpItemViewDto);
        success.setTotal(mpItemViewDto.size());
        return success;
    }

    /**
     * 待验收病害分页查询（移动端：以病害为基准）
     * @return
     */
    @PostMapping("selectWaitAccptDssPage")
    public RestResult<List<DmTaskDssViewDto>> selectWaitAccptDssPage(){
        Map reqParam = getReqParam();
        Page page = getPage();
        //QueryWrapper queryWrapper = setWrapper(reqParam);
        List<DmTaskDssViewDto> dtos = detailService.selectWaitAccptDssPage(page,null);
        page.setRecords(dtos);
        return H_RestResultHelper.returnPage(page);
    }

    public QueryWrapper setWrapper(Map reqParam,boolean isMp){
        QueryWrapper queryWrapper = new QueryWrapper();
        String acceptStatus = (String) reqParam.getOrDefault("acceptStatus", "");
        //3表示查询全部
        if (StrUtil.isNotBlank(acceptStatus) && "3".equals(acceptStatus)){
            reqParam.remove("acceptStatus");
        }
        //QueryWrapper queryWrapper = initQueryWrapper(reqParam);
        String startDate = (String) reqParam.getOrDefault("startDate","");
        if (StrUtil.isNotBlank(startDate)){
            queryWrapper.apply("REPAIR_DATE >= to_date({0},'YYYY-MM-dd')",startDate);
            reqParam.remove("startDate");
        }

        String endDate = (String) reqParam.getOrDefault("endDate","");
        if (StrUtil.isNotBlank(endDate)){
            queryWrapper.apply("REPAIR_DATE <= to_date({0},'YYYY-MM-dd')",endDate);
            reqParam.remove("endDate");
        }
        queryWrapper.apply("contr_id in (select x.contr_id from memsdb.mpc_contract x where x.mnt_org_id={0})", CustomRequestContextHolder.getOrgIdString());
        // 措施查询时，按照工程细目排序
        if (isMp){
            queryWrapper.orderByAsc("mtask_code,TO_NUMBER(translate (mmp_Code, '#' || translate (mmp_Code, '0123456789', '#'), '/'))");
        }else {
            queryWrapper.orderByAsc("mtask_code");
        }

        queryWrapper.orderByAsc("MAIN_ROAD_ID,LINE_DIRECT,RL_STAKE_NEW");

        H_BatisQuery.setFieldValue2In(queryWrapper,reqParam, DmTaskAccptDetailDto.class);
        return queryWrapper;
    }

}
