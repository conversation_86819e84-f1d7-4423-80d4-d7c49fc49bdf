package com.hualu.app.module.mems.nminsp.entity;

import java.util.Date;

public class BridgeInspection {

    // 基础字段
    private String routeName;      // 路线名称
    private String lineCode;       // 线路代码
    private String brdgSpanKind; // 桥梁跨度类型（枚举）
    private String brdgName;       // 桥梁名称
    private String designStake;    // 设计桩号
    private String logicCntrStake; // 逻辑中心桩号
    private String dssNums;        // 检测次数（"无"或数字）
    private Date inspDate;    // 检测日期
    private String checkNums; // 检测频率要求（枚举）
    private String finspCode;      // 检测代码
    private String brdgRating; // 桥梁评级（枚举）

    private String mntnAdvice = "/";

    private String orgFullname;

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getBrdgSpanKind() {
        return brdgSpanKind;
    }

    public void setBrdgSpanKind(String brdgSpanKind) {
        this.brdgSpanKind = brdgSpanKind;
    }

    public String getBrdgName() {
        return brdgName;
    }

    public void setBrdgName(String brdgName) {
        this.brdgName = brdgName;
    }

    public String getDesignStake() {
        return designStake;
    }

    public void setDesignStake(String designStake) {
        this.designStake = designStake;
    }

    public String getLogicCntrStake() {
        return logicCntrStake;
    }

    public void setLogicCntrStake(String logicCntrStake) {
        this.logicCntrStake = logicCntrStake;
    }

    public String getDssNums() {
        return dssNums;
    }

    public void setDssNums(String dssNums) {
        this.dssNums = dssNums;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getCheckNums() {
        return checkNums;
    }

    public void setCheckNums(String checkNums) {
        this.checkNums = checkNums;
    }

    public String getFinspCode() {
        return finspCode;
    }

    public void setFinspCode(String finspCode) {
        this.finspCode = finspCode;
    }

    public String getBrdgRating() {
        return brdgRating;
    }

    public void setBrdgRating(String brdgRating) {
        this.brdgRating = brdgRating;
    }

    @Override
    public String toString() {
        return "BridgeInspection{" +
                "routeName='" + routeName + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", brdgSpanKind='" + brdgSpanKind + '\'' +
                ", brdgName='" + brdgName + '\'' +
                ", designStake='" + designStake + '\'' +
                ", logicCntrStake='" + logicCntrStake + '\'' +
                ", dssNums='" + dssNums + '\'' +
                ", inspDate=" + inspDate +
                ", checkNums='" + checkNums + '\'' +
                ", finspCode='" + finspCode + '\'' +
                ", brdgRating='" + brdgRating + '\'' +
                '}';
    }

    public String getOrgFullname() {
        return orgFullname;
    }

    public void setOrgFullname(String orgFullname) {
        this.orgFullname = orgFullname;
    }

    public String getMntnAdvice() {
        return mntnAdvice;
    }

    public void setMntnAdvice(String mntnAdvice) {
        this.mntnAdvice = mntnAdvice;
    }
}
