<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.mpc.mapper.MpcContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.mpc.entity.MpcContract">
        <id column="CONTR_ID" property="contrId" />
        <result column="CONTR_CODE" property="contrCode" />
        <result column="CONTR_NAME" property="contrName" />
        <result column="CONTR_TYPE" property="contrType" />
        <result column="SP_PRJ_ID" property="spPrjId" />
        <result column="SP_PRJ_CODE" property="spPrjCode" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="IMPL_ORG_ID" property="implOrgId" />
        <result column="IMPL_ORG_NAME" property="implOrgName" />
        <result column="CONTR_NO" property="contrNo" />
        <result column="CONTR_MOENY" property="contrMoeny" />
        <result column="SIGN_DATE" property="signDate" />
        <result column="RPT_DATE" property="rptDate" />
        <result column="START_DATE" property="startDate" />
        <result column="END_DATE" property="endDate" />
        <result column="STLMT_DESC" property="stlmtDesc" />
        <result column="REMARK" property="remark" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="SUBMIT_TIME" property="submitTime" />
        <result column="STATUS" property="status" />
        <result column="SUBMIT_OPINION" property="submitOpinion" />
        <result column="MPITEM_SYS_ID" property="mpitemSysId" />
        <result column="ENABLED" property="enabled" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        CONTR_ID, CONTR_CODE, CONTR_NAME, CONTR_TYPE, SP_PRJ_ID, SP_PRJ_CODE, MNT_ORG_ID, IMPL_ORG_ID, IMPL_ORG_NAME, CONTR_NO, CONTR_MOENY, SIGN_DATE, RPT_DATE, START_DATE, END_DATE, STLMT_DESC, REMARK, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, SUBMIT_TIME, STATUS, SUBMIT_OPINION, MPITEM_SYS_ID, ENABLED
    </sql>
    <select id="selectMpcContract" resultType="com.hualu.app.module.mems.mpc.dto.MpcContractDto">
        select c.contr_id,
               c.contr_name,
               o.org_code as mnt_org_id,
               o.org_fullname as mnt_org_name,
               c.impl_org_id,
               c.impl_org_name,
               c.contr_code,
               c.contr_moeny
        from MPC_CONTRACT c
                 left join gdgs.FW_RIGHT_ORG o on c.MNT_ORG_ID = o.ID
        where c.CONTR_TYPE = 'YH'
          and c.MNT_ORG_ID = #{mngOrgId}
        ORDER BY C.SIGN_DATE DESC
    </select>

</mapper>
