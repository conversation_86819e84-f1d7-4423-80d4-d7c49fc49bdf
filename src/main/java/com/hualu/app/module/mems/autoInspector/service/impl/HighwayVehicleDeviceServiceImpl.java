package com.hualu.app.module.mems.autoInspector.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.autoInspector.entity.HighwayVehicleDevice;
import com.hualu.app.module.mems.autoInspector.service.HighwayVehicleDeviceService;
import com.hualu.app.module.mems.autoInspector.mapper.HighwayVehicleDeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_VEHICLE_DEVICE(车辆设备状态信息表)】的数据库操作Service实现
* @createDate 2025-04-27 09:02:29
*/
@Slf4j
@Service
public class HighwayVehicleDeviceServiceImpl extends ServiceImpl<HighwayVehicleDeviceMapper, HighwayVehicleDevice>
    implements HighwayVehicleDeviceService{

    @Override
    public int syncAndSaveDeviceData(JSON json) {
        int count = 0;
        int updateCount = 0;
        int insertCount = 0;
        
        if (json == null) {
            log.warn("同步设备数据为空");
            return count;
        }
        
        try {
            // 解析返回数据并保存到数据库
            JSONObject jsonObj = (JSONObject) json;
            if (jsonObj.getInteger("code") == 200) {
                JSONObject data = jsonObj.getJSONObject("data");
                JSONArray items = data.getJSONArray("items");
                
                if (items != null && !items.isEmpty()) {
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        String deviceId = item.getString("id");
                        
                        // 将JSON对象转换为实体对象
                        HighwayVehicleDevice device = new HighwayVehicleDevice();
                        device.setId(deviceId);
                        device.setClientId(item.getString("clientid"));
                        device.setPlateNo(item.getString("plateno"));
                        device.setStatus(item.getString("status"));
                        device.setAiotType(item.getString("aiottype"));
                        device.setServerStatus(item.getString("serverstatus"));
                        device.setAiotStatus(item.getString("aiotstatus"));
                        device.setAppId(item.getString("appid"));
                        device.setVideoStatus(item.getString("videostatus"));
                        device.setInspectingFlag(item.getInteger("inspectingflag"));
                        
                        // 设置新增的三个属性（检查字段是否存在，并尝试多种可能的字段名称）
                        // 联系人
                        if (item.containsKey("contactPerson")) {
                            device.setContactPerson(item.getString("contactPerson"));
                        } else if (item.containsKey("contactperson")) {
                            device.setContactPerson(item.getString("contactperson"));
                        } else if (item.containsKey("contact_person")) {
                            device.setContactPerson(item.getString("contact_person"));
                        }
                        
                        // 联系电话
                        if (item.containsKey("contactPhone")) {
                            device.setContactPhone(item.getString("contactPhone"));
                        } else if (item.containsKey("contactphone")) {
                            device.setContactPhone(item.getString("contactphone"));
                        } else if (item.containsKey("contact_phone")) {
                            device.setContactPhone(item.getString("contact_phone"));
                        } else if (item.containsKey("phone")) {
                            device.setContactPhone(item.getString("phone"));
                        }
                        
                        // 组织/单位编码
                        if (item.containsKey("orgCode")) {
                            device.setOrgCode(item.getString("orgCode"));
                        } else if (item.containsKey("orgcode")) {
                            device.setOrgCode(item.getString("orgcode"));
                        } else if (item.containsKey("org_code")) {
                            device.setOrgCode(item.getString("org_code"));
                        } else if (item.containsKey("organization")) {
                            device.setOrgCode(item.getString("organization"));
                        }
                        
                        // 日志记录新字段的值
                        if (log.isDebugEnabled()) {
                            log.debug("设备[{}]附加信息: 联系人={}, 联系电话={}, 所属单位={}",
                                    deviceId, device.getContactPerson(), device.getContactPhone(), device.getOrgCode());
                        }
                        
                        // 处理日期字段
                        try {
                            // 日期格式为：2025-04-11T09:36:17.000Z
                            String createdTimeStr = item.getString("createdtime");
                            String updatedTimeStr = item.getString("updatedtime");
                            
                            if (createdTimeStr != null && !createdTimeStr.isEmpty()) {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                                Date createdTime = sdf.parse(createdTimeStr);
                                device.setCreatedTime(createdTime);
                            }
                            
                            if (updatedTimeStr != null && !updatedTimeStr.isEmpty()) {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                                sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                                Date updatedTime = sdf.parse(updatedTimeStr);
                                device.setUpdatedTime(updatedTime);
                            }
                        } catch (Exception e) {
                            log.error("日期解析错误", e);
                        }
                        
                        // 检查数据是否已存在
                        boolean exists = this.getById(deviceId) != null;
                        
                        // 保存或更新数据
                        boolean success = this.saveOrUpdate(device);
                        if (success) {
                            count++;
                            if (exists) {
                                updateCount++;
                                log.debug("更新设备数据: {}, 车牌号: {}", deviceId, device.getPlateNo());
                            } else {
                                insertCount++;
                                log.debug("新增设备数据: {}, 车牌号: {}", deviceId, device.getPlateNo());
                            }
                        }
                    }
                    log.info("成功同步{}条设备数据, 其中新增{}条, 更新{}条", count, insertCount, updateCount);
                } else {
                    log.info("没有设备数据需要同步");
                }
            } else {
                log.error("同步设备数据失败：{}", jsonObj.getString("message"));
            }
        } catch (Exception e) {
            log.error("同步设备数据异常", e);
        }
        
        return count;
    }
}




