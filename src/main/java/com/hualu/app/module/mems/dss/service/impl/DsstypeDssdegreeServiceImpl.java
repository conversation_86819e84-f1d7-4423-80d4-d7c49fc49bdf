package com.hualu.app.module.mems.dss.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.dss.mapper.DsstypeDssdegreeMapper;
import com.hualu.app.module.mems.dss.service.DsstypeDssdegreeService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 病害类型严重程度关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
@Service
public class DsstypeDssdegreeServiceImpl extends ServiceImpl<DsstypeDssdegreeMapper, DsstypeDssdegree> implements DsstypeDssdegreeService {

    @Override
    public List<DsstypeDssdegree> selectDegressByDssType(String dssType) {
        LambdaQueryWrapper<DsstypeDssdegree> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DsstypeDssdegree::getDssType,dssType);
        queryWrapper.orderByAsc(DsstypeDssdegree::getDssDegree);
        return list(queryWrapper);
    }

    @Override
    public String getDssdegressName(String dssType, String dssDegree) {
        String key = "dsstype:"+dssType;
        List<DsstypeDssdegree> dtos = (List<DsstypeDssdegree>) CustomRequestContextHolder.get(key);
        if (CollectionUtil.isEmpty(dtos)){
            dtos = selectDegressByDssType(dssType);
            CustomRequestContextHolder.set(key,dtos);
        }
        return dtos.stream().filter(e->e.getDssType().equals(dssType) && e.getDssDegree().equals(dssDegree)).map(DsstypeDssdegree::getDssDegreeName).findFirst().orElse(null);
    }
}
