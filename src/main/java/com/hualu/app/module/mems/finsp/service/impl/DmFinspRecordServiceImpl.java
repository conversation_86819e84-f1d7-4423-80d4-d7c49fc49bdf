package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.dto.FacPartTypeDto;
import com.hualu.app.module.facility.dto.FacQueryDto;
import com.hualu.app.module.facility.impl.FacQlImpl;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DsstypeDssdegreeService;
import com.hualu.app.module.mems.dss.service.IDssRecordService;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.mapper.DmFinspRecordMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspRecordService;
import com.hualu.app.module.mems.finsp.service.DmFinspService;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_StakeHelper;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.hualu.app.utils.mems.H_DssHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.service.PageService;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class DmFinspRecordServiceImpl extends ServiceImpl<DmFinspRecordMapper, DmFinspRecord> implements DmFinspRecordService, IDssRecordService {


    @Lazy
    @Autowired
    DmFinspService dmFinspService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    DssImageService imageService;

    @Autowired
    PageService pageService;

    @Autowired
    FacQlImpl qlImpl;

    @Autowired
    DsstypeDssdegreeService dssdegreeService;

    @Autowired
    HttpServletRequest request;

    @Override
    public DmFinspRecord getDmFinspRecordById(String id) {
        List<DmFinspRecord> records = baseMapper.selectViewRecordById(id);
        if (records == null || records.size() == 0){
            throw new BaseException("找不到对应的病害信息");
        }
        DmFinspRecord record = records.get(0);
        List<FacPartTypeDto> partTypeDtos = selectPartTypes(record.getStructId(), record.getFacilityCat(),record.getFinVersion());
        setViewRecord(record.getFacilityCat(), Lists.newArrayList(record),partTypeDtos);
        return record;
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteFinspRecordByIds(String[] dssIds) {
        if (dssIds.length == 0){
            throw new BaseException("病害ID不能为空");
        }
        for (String dssId : dssIds) {
            imageService.delByDssId(dssId);
        }
        DmFinspRecord record = getById(dssIds[0]);
        removeByIds(Arrays.asList(dssIds));
        DmFinsp dmFinsp = dmFinspService.getById(record.getFinspId());
        dmFinspService.saveOrUpdateFinsp(dmFinsp);
        dmFinspService.updateDmFinspResult(dmFinsp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DmFinspRecord saveOrUpdateFinspRecord(DmFinspRecord dmFinspRecord) {
        DmFinsp dmFinsp = dmFinspService.getById(dmFinspRecord.getFinspId());
        //填充结构物ID，边坡经常检查单查询病害时，需要用到结构物ID关联查询
        if (StrUtil.isNotBlank(dmFinsp.getStructId())){
            dmFinspRecord.setStructId(dmFinsp.getStructId());
        }
        //根据dssType匹配finspItemId，用于生成结论依据,1:根据部件名称匹配inspCom;2:根据部件名称匹配inspCont;3:根据病害名称匹配inspCont
        IFacBase base = CustomApplicationContextHolder.getBeansOfType(IFacBase.class).values().stream().filter(e -> e.getFacilityCat().equals(dmFinsp.getFacilityCat()))
                .findFirst().orElse(null);
        if (base != null){
            base.setFinspItemId(dmFinspRecord,dmFinsp);
        }
        if (StrUtil.isBlank(dmFinspRecord.getDssId())){
            saveRecord(dmFinspRecord);
        }else {
            updateRecord(dmFinspRecord);
        }
        dmFinspService.updateDssNum(dmFinspRecord.getFinspId());
        dmFinspService.updateDmFinspResult(dmFinsp);
        return getDmFinspRecordById(dmFinspRecord.getDssId());
    }

    private void saveRecord(DmFinspRecord dmFinspRecord){
        if (StrUtil.isNotBlank(dmFinspRecord.getDssId())){
            return;
        }
        dmFinspRecord.setDssId(H_KeyWorker.nextIdToString());
        dmFinspRecord.setFoundDate(new Date());
        save(dmFinspRecord);
        imageService.saveDssImageForApp(dmFinspRecord.getDssId(),dmFinspRecord.getFileIds(),1);

        //日常巡查：选择多车道时，病害分成多病害入库 ，经常检查不存在该情况
        /*if (StrUtil.isNotBlank(dmFinspRecord.getLane())){
            String[] lanes = dmFinspRecord.getLane().split(",");
            List<DmFinspRecord> res = Lists.newArrayList();
            for (String lane : lanes) {
                DmFinspRecord item = new DmFinspRecord();
                item.setDssId(H_KeyWorker.nextIdToString());
                BeanUtils.copyProperties(dmFinspRecord,item);
                item.setLane(lane);
                res.add(item);
            }
            if (CollectionUtil.isNotEmpty(res)){
                saveBatch(res);
                res.forEach(item->{
                    imageService.saveDssImage(item.getDssId(),dmFinspRecord.getFileIds());
                });
            }
        }else {
            save(dmFinspRecord);
            imageService.saveDssImage(dmFinspRecord.getDssId(),dmFinspRecord.getFileIds());
        }*/
    }

    private void updateRecord(DmFinspRecord dmFinspRecord){
        updateById(dmFinspRecord);
        String dssId = StrUtil.isNotBlank(dmFinspRecord.getLDssId())?dmFinspRecord.getLDssId():dmFinspRecord.getDssId();
        imageService.saveDssImageForApp(dssId,dmFinspRecord.getFileIds(),1);
    }

    @Override
    public void delDmFinspRecord(String finspId) {
        LambdaQueryWrapper<DmFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspRecord::getFinspId,finspId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public void insertRecord(DmFinsp dmFinsp, String lastFinspId) {

        if (H_BasedataHepler.JA.equals(dmFinsp.getFacilityCat())){

            if (dmFinsp.getStartStake() == null || dmFinsp.getEndStake() == null){
                throw new BaseException("交安经常检查单桩号范围不能为空");
            }
            baseMapper.insertRecordByJA(dmFinsp.getFinspId(),dmFinsp.getLineCode(),
                    dmFinsp.getStartStake().doubleValue(),dmFinsp.getEndStake().doubleValue(), DateTimeUtil.getNowDate());
            return;
        }

        //如果上次经常检查单不存在
        if (StrUtil.isBlank(lastFinspId)){
            return;
        }

        if (H_BasedataHepler.QL.equals(dmFinsp.getFacilityCat()) || H_BasedataHepler.HD.equals(dmFinsp.getFacilityCat())){
            baseMapper.insertRecordByQH(dmFinsp.getFinspId(),lastFinspId);
            return;
        }
        baseMapper.insertRecordByOther(dmFinsp.getFinspId(), lastFinspId);
    }

    @Override
    public List<DmFinspRecord> queryHisRecordByFinspId(String finspId, String facilityCat) {
        List<DmFinspRecord> dmFinspRecords = baseMapper.queryHisRecordByFinspId(finspId,facilityCat);
        for (DmFinspRecord item : dmFinspRecords) {
            String stake = item.getStake() == null ? null : item.getStake().toString();
            item.setKStake(H_StakeHelper.convertCnStake(stake));
            initDssDisplayNum(item);
        }
        return dmFinspRecords;
    }

    @Override
    public IPage<DmFinspRecord> selectViewRecordByFinspId(String finspId) {

        Page page = pageService.parsePageParam(request);
        DmFinsp dmFinsp = dmFinspService.getById(finspId);
        if (dmFinsp == null){
            return page;
        }
        List<FacPartTypeDto> partTypeDtos = selectPartTypes(dmFinsp.getStructId(), dmFinsp.getFacilityCat(), dmFinsp.getFinVersion());
        List<DmFinspRecord> dmFinspRecords = baseMapper.selectViewRecordByFinspId(page,finspId);
        setViewRecord(dmFinsp.getFacilityCat(), dmFinspRecords,partTypeDtos);
        return page.setRecords(dmFinspRecords);
    }

    /**
     * 前台界面数据回显
     * @param facilityCat 结构物类型
     * @param dmFinspRecords 病害集合
     * @param partTypeDtos 桥梁病害部位集合
     */
    private void setViewRecord(String facilityCat,List<DmFinspRecord> dmFinspRecords,List<FacPartTypeDto> partTypeDtos){
        if (CollectionUtil.isEmpty(dmFinspRecords)){
            return;
        }
        //获取桥梁病害部位
        Set<String> partpostDs = dmFinspRecords.stream().filter(e -> StrUtil.isNotBlank(e.getDssPosition())).map(DmFinspRecord::getDssPosition).collect(Collectors.toSet());
        for (DmFinspRecord item : dmFinspRecords) {
            item.setLineDirect(dicService.getDicName("LANE_DIRECTION", item.getLineDirect()));
            item.setQualityName(H_BasedataHepler.getDssXzName(item.getDssQuality()));

           IFacBase baseBean =  CustomApplicationContextHolder.getBeansOfType(IFacBase.class).values().stream()
                    .filter(e->e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
           if (baseBean != null){
               baseBean.initDmFinspRecordView(item);
           }

            //生成经常检查单中，病害明细信息加载使用的构件编号
            if(StrUtil.isNotBlank(item.getStructCompId()))
            {
                String[] iStructCompIds = item.getStructCompId().split(",");
                item.setIStructCompId(iStructCompIds[0]);
            }

            String stake = item.getStake() == null ? null : item.getStake().toString();
            item.setKStake(H_StakeHelper.convertCnStake(stake));
            initDssDisplayNum(item);
            initPartType(facilityCat,item,partTypeDtos);
            initStatus(item);
        }
        initImages(dmFinspRecords);
    }

    /**
     * 设置经常检查病害照片
     * @param dmFinspRecords
     */
    private void initImages(List<DmFinspRecord> dmFinspRecords) {
        if (CollectionUtil.isEmpty(dmFinspRecords)){
            return;
        }
        Set<String> dssIds = dmFinspRecords.stream().map(DmFinspRecord::getDssId).collect(Collectors.toSet());
        Map<String, List<String>> imageMap = imageService.mapByDssIds(dssIds);
        for (DmFinspRecord item : dmFinspRecords) {
            List<String> fileIds = imageMap.get(item.getDssId());
            if (CollectionUtil.isNotEmpty(fileIds)){
                item.setFileIds(StrUtil.join(",",fileIds));
                item.setImageHost(C_Constant.IMAGE_HOST);
            }
        }
    }

    @Override
    public List<DmFinspRecord> selectDssRecordByFinspId(String finspId) {
        Page page = pageService.parsePageParam(request);
        return baseMapper.selectViewRecordByFinspId(page,finspId);
    }

    @Override
    public IPage<DmFinspRecord> selectHisRecordByStructId(String structId, String facilityCat) {
        Page page = pageService.parsePageParam(request);
        List<DmFinspRecord> records = baseMapper.selectHisRecordByStructId(page, structId,facilityCat);
        List<FacPartTypeDto> partTypeDtos = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(records)){
            DmFinspRecord record = records.get(0);
            partTypeDtos = selectPartTypes(structId, facilityCat,record.getFinVersion());
        }
        setViewRecord(facilityCat,records,partTypeDtos);
        return page.setRecords(records);
    }

    /**
     * 初始化车道（边坡在该字段显示边坡级数）
     * @param facilityCat
     * @param item
     */
    private void initLane(String facilityCat,DmFinspRecord item){
        if (H_BasedataHepler.BP.equals(facilityCat)){
            item.setLaneName(H_BasedataHepler.bpLevel.get(item.getLane()));
            return;
        }
        if (H_BasedataHepler.SD.equals(facilityCat)){
            item.setLaneName(dicService.getDicName("LANE12",item.getLane()));
            return;
        }
        item.setLaneName(dicService.getDicName("LANE", item.getLane()));
    }

    /**
     * 病害严重程度
     * @param facilityCat
     * @param item
     */
    private void initDssgress(String facilityCat,DmFinspRecord item){
        if (H_BasedataHepler.QL.equals(facilityCat) || H_BasedataHepler.HD.equals(facilityCat)){
            item.setDssDegree(dssdegreeService.getDssdegressName(item.getDssType(),item.getDssDegree()));
        }else {
            item.setDssDegree(dicService.getDicName("DSS_DEGREE",item.getDssDegree()));
        }
    }

    /**
     * 初始化桥梁部位
     * @param facilityCat
     * @param item
     * @param partpostDs
     */
    private void initDssPositionName(String facilityCat,DmFinspRecord item,Set<String> partpostDs){
        //dssPosition  病害位置回显
        if (H_BasedataHepler.QL.equals(facilityCat)){
            String postName = qlImpl.getDssPositionName(partpostDs, item.getDssPosition());
            item.setDssPositionName(postName);
        }
    }

    /**
     * 修复状态
     * @param record
     */
    private void initStatus(DmFinspRecord record){

        if(StrUtil.isBlank(record.getRepairStatus())||record.getRepairStatus().equals("0")){
            record.setRepairStatus("待修复");
        }else if(record.getRepairStatus().equals("1")){
            record.setRepairStatus("修复中");
        }else{
            record.setRepairStatus("已修复");
        }
    }


    /**
     * 经常检查是针对单个结构物，所以部件都是确定的
     * @param structId
     * @param facilityCat
     * @param finVersion
     * @return
     */
    private List<FacPartTypeDto> selectPartTypes(String structId,String facilityCat,String finVersion){

        List<FacPartTypeDto> partTypeDtos = Lists.newArrayList();
        //桥涵部件反查
        if (StrUtil.isNotBlank(structId)){
            FacQueryDto dto = new FacQueryDto();
            dto.setStructId(structId);
            dto.setFinVersion(finVersion);
            Map<String, IFacBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
            for (IFacBase bean : beansOfType.values()) {
                if (!bean.getFacilityCat().equals(facilityCat)){
                    continue;
                }
                partTypeDtos = bean.selectPartTypes(dto);
                break;
            }
        }
        return partTypeDtos;
    }

    /**
     * 设置结构物部件名称
     * @param record
     */
    private void initPartType(String facilityCat,DmFinspRecord record,List<FacPartTypeDto> partTypeDtos){
        if (H_BasedataHepler.SD.equals(facilityCat)){
            return;
        }
        //桥涵部件反查
        if (StrUtil.isNotBlank(record.getStructId()) && StrUtil.isNotBlank(record.getStructPartId())){
            FacPartTypeDto typeDto = partTypeDtos.stream().filter(w -> record.getStructPartId().equals(w.getPartsCode())).findFirst().orElse(null);
            if (typeDto != null){
                record.setStructPartName(typeDto.getPartstypeName());
            }
        }
    }

    /**
     * 显示病害
     * @param entity
     */
    private void initDssDisplayNum(DmFinspRecord entity){
        StringBuffer str = new StringBuffer();
        Double dssL = entity.getDssL();
        if(dssL!= null && StrUtil.isNotBlank(entity.getDssLUnit()) && BigDecimal.valueOf(dssL).compareTo(BigDecimal.ZERO) > 0){
            str.append("长："+ dssL + entity.getDssLUnit()+" ");
        }
        Double dssW = entity.getDssW();
        if(dssW!= null && StrUtil.isNotBlank(entity.getDssWUnit()) && BigDecimal.valueOf(dssW).compareTo(BigDecimal.ZERO) > 0){
            str.append("宽："+ dssW + entity.getDssWUnit()+" ");
        }
        Double dssD = entity.getDssD();
        if(dssD!= null && StrUtil.isNotBlank(entity.getDssDUnit()) && BigDecimal.valueOf(dssD).compareTo(BigDecimal.ZERO) > 0){
            str.append("深："+ dssD + entity.getDssDUnit()+" ");
        }
        Double dssA = entity.getDssA();
        if(dssA!= null && StrUtil.isNotBlank(entity.getDssAUnit()) && BigDecimal.valueOf(dssA).compareTo(BigDecimal.ZERO) > 0){
            str.append("面积："+ dssA + entity.getDssAUnit()+" ");
        }
        Double dssV = entity.getDssV();
        if(dssV!= null && StrUtil.isNotBlank(entity.getDssVUnit()) && BigDecimal.valueOf(dssV).compareTo(BigDecimal.ZERO) > 0){
            str.append("体积："+ dssV + entity.getDssVUnit()+" ");
        }
        Double dssN = entity.getDssN();
        if(dssN!= null && StrUtil.isNotBlank(entity.getDssNUnit()) && BigDecimal.valueOf(dssN).compareTo(BigDecimal.ZERO) > 0){
            str.append("数量："+ dssN + entity.getDssNUnit()+" ");
        }
        Double dssP = entity.getDssP();
        if(dssP!= null && BigDecimal.valueOf(dssP).compareTo(BigDecimal.ZERO) > 0){
            str.append("百分比："+ dssP +"% ");
        }
        Double dssG = entity.getDssG();
        if(dssG!= null && BigDecimal.valueOf(dssG).compareTo(BigDecimal.ZERO) > 0){
            str.append("角度："+ dssG +"度 ");
        }
        entity.setDssNum(str.toString());
    }

    @Override
    public String getDssSource() {
        return "2";
    }

    @Override
    public List<DssInfoDto> selectDssRecordPage(Page page, QueryWrapper queryWrapper) {
        List<DssInfoDto> dssInfoDtos = baseMapper.selectDssRecordPage(page,queryWrapper);
        H_DssHelper.setCheckInfo(dssInfoDtos);
        H_DssHelper.setMtaskCode(dssInfoDtos);
        return dssInfoDtos;
    }

    @Override
    public void updateDssRecordByCloseType(List<String> dssIds, String closeType) {
        if (CollectionUtil.isEmpty(dssIds)){
            return;
        }
        LambdaUpdateWrapper<DmFinspRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(DmFinspRecord::getDssId, dssIds);
        updateWrapper.set(DmFinspRecord::getCloseType, closeType);
        update(updateWrapper);
    }
}
