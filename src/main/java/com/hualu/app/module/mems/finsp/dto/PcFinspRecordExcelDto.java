package com.hualu.app.module.mems.finsp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

@Accessors(chain = true)
@Data
public class PcFinspRecordExcelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    //序号
    private Integer index;

    //项目名称
    private String orgName;

    //结构物名称（桩号）
    private String structName;

    //路段类型
    private String structType;

    //方向
    private String lineDirection;

    //级数
    private String structLevel;

    //是否重要边坡
    private String important;

    //排查日期
    @JsonFormat(pattern="yyyy/MM/dd")
    private LocalDate inspDate;

    //边坡是否完好
    private String pgStatus;

    //急流槽是否完好
    private String jlcStatus;

    //截水沟是否完好
    private String jsgStatus;

    //深层排水是否完好
    private String scpsStatus;

    //支挡防护措施是否完好
    private String zdfhStatus;

    //主要隐患情况描述
    private String dssDesc;

    //隐患处治类型
    private String dealType;

    //排查单位
    private String inspOrgName;

    //排查人
    private String inspPerson;

    //病害类型ID
    private String dsstypeId;
}
