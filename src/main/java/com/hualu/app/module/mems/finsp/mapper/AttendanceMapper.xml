<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.AttendanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.Attendance">
        <id column="ID" property="id" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="ATTENDANCE_TIME" property="attendanceTime" />
        <result column="LAST_ATTENDANCE_TIME" property="lastAttendanceTime" />
        <result column="ORG_NAME" property="orgName" />
        <result column="ORG_CODE" property="orgCode" />
        <result column="LAST_ATTENDANCE_PERSON" property="lastAttendancePerson" />
        <result column="ORDERS" property="orders" />
        <result column="LAST_ATTENDANCE_USER_ID" property="lastAttendanceUserId" />
        <result column="TYPE" property="type" />
        <result column="LOCATION" property="location" />
        <result column="LONGITUDE" property="longitude" />
        <result column="LATITUDE" property="latitude" />
        <result column="FIELD_ID" property="fieldId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, STRUCT_ID, STRUCT_NAME, INSP_PERSON, CREATE_USER_ID, ATTENDANCE_TIME, LAST_ATTENDANCE_TIME, ORG_NAME, ORG_CODE, LAST_ATTENDANCE_PERSON, ORDERS, LAST_ATTENDANCE_USER_ID, TYPE, LOCATION, LONGITUDE, LATITUDE, FIELD_ID
    </sql>
    <select id="getLastAttendance" resultType="com.hualu.app.module.mems.finsp.entity.Attendance">
        select * from (
        SELECT a.*
        FROM MEMSDB.ATTENDANCE a
        WHERE CREATE_USER_ID = #{userId}
        and STRUCT_ID = #{structId}
        order by to_date(a.ATTENDANCE_TIME, 'yyyy-mm-dd hh24:mi:ss') desc) m where ROWNUM = 1
    </select>
</mapper>
