package com.hualu.app.module.mems.nminsp.controller;

import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.mapper.NmFinspRecordMapper;
import com.hualu.app.module.mems.nminsp.service.NmFinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 新版经常检查记录表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Api(tags = "NmFinspRecordController",description = "新版经常检查记录表 前端控制器")
@RestController
@RequestMapping("/nmFinspRecord")
public class NmFinspRecordController extends M_MyBatisController<NmFinspRecord,NmFinspRecordMapper>{

    @Autowired
    private NmFinspRecordService service;

    @Lazy
    @Autowired
    NmFinspService nmFinspService;

    @Autowired
    DssImageService imageService;

    @Autowired
    NmInspContentService contentService;

    /**
     * 分页查询
     * @return
     */
    @ApiRegister(value = "NmFinspRecord:page",businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    public RestResult<List<NmFinspRecord>> selectListByDinspId(String finspId) {
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        List<NmFinspRecord> records = iPage.getRecords();
        service.showView(records,nmFinspService.getById(finspId));
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    /**
     * 路面检查结论
     * @param finspId
     * @return
     */
    @GetMapping("listLmResultByFinspId/{finspId}")
    public RestResult<List<NmFinspRecord>> listLmResultByDinspId(@PathVariable("finspId") String finspId) {
        List<NmFinspRecord> nmDinspRecords = service.selectRecordByFinspId(finspId);
        if (CollectionUtil.isEmpty(nmDinspRecords)){
            return RestResult.success(Lists.newArrayList());
        }
        service.showView(nmDinspRecords,nmFinspService.getById(finspId));
        List<NmInspContent> contents = contentService.listByContent("LM", null,null);

        for (NmFinspRecord item : nmDinspRecords) {
            //当病害记录的病害类型==部位的dssType，巡查内容设置为病害类型
            if (contents == null || item == null) {
                return null;
            }
            NmInspContent nmInspContent = contents.stream()
                    .filter(e -> e != null
                            && StrUtil.isNotBlank(e.getDssType())
                            && StrUtil.isNotBlank(item.getDssType())  // 新增检查
                            && StrUtil.isNotBlank(item.getStructPartId())  // 修改条件顺序
                            && Objects.equals(e.getPartId(), item.getStructPartId())  // 安全比较
                            && e.getDssType().contains(item.getDssType()))
                    .findFirst()
                    .orElse(null);
            if (nmInspContent != null) {
                item.setDssTypeName(nmInspContent.getName());
            }
        }
        return RestResult.success(nmDinspRecords);
    }

    /**
     * 根据主键查询
     * @return
     */
    @ApiRegister(value = "nmDinspRecord:getById",businessType = BusinessType.OTHER)
    @ApiOperation("根据主键查询")
    @GetMapping("getById/{id}")
    public Object getById(@PathVariable String id) {
        return service.getById(id);
    }

    /**
     * 添加或者修改(PC端)
     * @param entity
     * @return
     */
    @ApiRegister(value = "NmFinspRecord:saveOrUpdate",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改")
    @PostMapping("saveOrUpdate")
    @Timed(value = "refreshResult4QL.time", description = "刷新桥梁经常检查结论接口耗时")
    public RestResult<String> saveOrUpdateForPC(@RequestBody @Validated NmFinspRecord entity) {
        H_StructHelper.validateNmFinspRecord(entity);
        service.saveOrUpdateRecord(entity,false);
//        service.refreshResult4QL();
        return RestResult.success("操作成功");
    }
    @ApiRegister(value = "NmFinspRecord:refreshResult4QL",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改")
    @GetMapping("refreshResult4QL")
    @ResponseBody
    public RestResult<String> refreshResult4QL() {
        service.refreshResult4QL();
        return RestResult.success("操作成功");
    }

    /**
     * 添加或者修改(移动端)
     * @param data
     * @return
     */
    @ApiRegister(value = "NmFinspRecord:saveOrUpdate",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改")
    @PostMapping("saveOrUpdateForApp")
    public RestResult<String> saveOrUpdateForApp(@RequestParam("data") String data,@RequestParam(value = "files",required = false) MultipartFile[] files) {
        NmFinspRecord entity = JSONUtil.toBean(data, NmFinspRecord.class);
        H_CValidator.validator2Exception(entity);
        H_StructHelper.validateNmFinspRecord(entity);
        // 移动端调用
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        entity.setFileIds(fileIds.stream().filter(Objects::nonNull).collect(Collectors.joining(",")));
        service.saveOrUpdateRecord(entity,true);
        return RestResult.success("操作成功");
    }

    /**
     * 删除
     */
    @ApiRegister(value = "nmDinspRecord:delIds",businessType = BusinessType.DELETE)
    @ApiOperation("删除（dssIds为字符串，以逗号分割）")
    @PostMapping("delIds")
    public RestResult<String> delIds(String dssIds){
        List<String> idList = StrUtil.split(dssIds, ",");
        service.deleteRecord(idList);
        return RestResult.success("操作成功");
    }

    /**
     * 删除病害照片
     * @param fileId
     * @return
     */
    @GetMapping("delImage")
    public RestResult<String> deleteImage(String fileId){
        imageService.delByFileId(fileId);
        return RestResult.success("操作成功");
    }
}