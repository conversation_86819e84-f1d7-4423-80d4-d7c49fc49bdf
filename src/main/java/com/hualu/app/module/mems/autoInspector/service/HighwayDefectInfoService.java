package com.hualu.app.module.mems.autoInspector.service;

import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【HIGHWAY_DEFECT_INFO(高速公路病害信息表)】的数据库操作Service
* @createDate 2025-04-27 09:02:29
*/
public interface HighwayDefectInfoService extends IService<HighwayDefectInfo> {

    /**
     * 获取病害类型映射关系
     * @param defectType 病害类型编码
     * @param objectType 对象类型编码
     * @param objectSubtype 对象子类型编码
     * @return 病害类型映射信息
     */
    DssTypeNew getDefectTypeMapping(String defectType, String objectType, String objectSubtype);

    void removeOldData(String dateStr, String routeName);
}
