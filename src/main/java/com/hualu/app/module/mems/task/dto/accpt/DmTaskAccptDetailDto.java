package com.hualu.app.module.mems.task.dto.accpt;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

@Data
public class DmTaskAccptDetailDto implements Serializable {

    @ApiModelProperty(value = "验收单明细ID")
    @TableId("MTASK_ACCPT_DTL_ID")
    private String mtaskAccptDtlId;

    @ApiModelProperty(value = "维修业务类型  1 计划性维修保养 2 突发性维修保养")
    @TableField("BUSINESS_TYPE")
    private Integer businessType;

    @ApiModelProperty(value = "对应验收单ID")
    @TableField("MTASK_ACCPT_ID")
    private String mtaskAccptId;

    @ApiModelProperty(value = "任务单明细ID")
    @TableField("MTASK_DTL_ID")
    private String mtaskDtlId;

    @ApiModelProperty(value = "病害id")
    @TableField("DSS_ID")
    private String dssId;

    @ApiModelProperty(value = "路段区间id")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "桩号")
    @TableField("STAKE")
    private Double stake;

    @ApiModelProperty(value = "车道位置")
    @TableField("LANE")
    private String lane;

    @ApiModelProperty(value = "养护工程细目")
    @TableField("MPITEM_ID")
    private String mpitemId;

    @ApiModelProperty(value = "计量单位")
    @TableField("MEASURE_UNIT")
    private String measureUnit;

    @ApiModelProperty(value = "是否包干")
    @TableField("IS_LUMP")
    private Integer isLump;

    @ApiModelProperty(value = "备注（任务分配填写内容）")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "申请验收数量")
    @TableField("APPLY_AMOUNT")
    private Double applyAmount;

    @ApiModelProperty(value = "验收数量")
    @TableField("ACCEPT_AMOUNT")
    private Double acceptAmount;

    /**
     * 维修日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("REPAIR_DATE")
    private LocalDate repairDate;

    @ApiModelProperty(value = "验收状态")
    @TableField("ACCEPT_STATUS")
    private Integer acceptStatus;

    @ApiModelProperty(value = "工程数量(精度不够替换掉)")
    @TableField("MPITEM_NUM_BAK")
    private Double mpitemNumBak;

    @TableField("MAIN_ROAD_ID")
    private String mainRoadId;

    @TableField("IS_TASK_RECORD")
    private Integer isTaskRecord;

    @TableField("RAMP_ID")
    private String rampId;

    @ApiModelProperty(value = "日常养护合同ID")
    @TableField("CONTR_ID")
    private String contrId;

    @ApiModelProperty(value = "维修工程记录编码")
    @TableField("MTASK_ACCPT_DTL_CODE")
    private String mtaskAccptDtlCode;

    @ApiModelProperty(value = "来源任务单的ID")
    @TableField("MTASK_ID")
    private String mtaskId;

    @ApiModelProperty(value = "路线方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "逻辑桩号")
    @TableField("LOGIC_STAKE_NUM")
    private Double logicStakeNum;

    @ApiModelProperty(value = "逻辑桩号显示")
    @TableField("RL_STAKE")
    private String rlStake;

    @ApiModelProperty(value = "工程数量")
    @TableField("MPITEM_NUM")
    private Double mpitemNum;


    private Double finishStake;
    private String dssDesc;
    private String structPartId;
    private String rStake;
    private String laneNm;//车道名称
    private String lineDirectNm;//方向名称

    //private String mpitemCode;
    //任务单估算数量
    private Double mpitemAccount;
    private String mmpCode;//工程条目编码
    private String mpitemName;//工程条目名称

    private String seniorId;//一级联动
    private String juniorId;//二级联动
    private Date foundDate;
    private String dssPosition;
    private String lump;
    private String lineName;
    private String fromTask;

    private String isover;

    private String repairDateI;//从任务单进入申请验收页面时使用，维修时间冗余字段，暂时使用String接收，代码中转换为date类型存入repairDate（miniUI直接使用Date类型有误）

    private String fileIds;

    private String afterFileIds;

    private String mtaskAccptCode; //冗余字段  验收单编号 MTASK_ACCPT_CODE
    private String mtaskCode;//任务单编号

    private Double unitPrice;

    private Double money;

    /**
     * 发单日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String sendTime; //发送时间

    /**
     * 病害类型
     */
    private String dssTypeName;
    private String dssType;

    private String contrNm;

    private String facilityCat;

    private String structId;
    private Integer processinstId;



    @JsonIgnore
    @TableField("DSS_L")
    private Double dssL;

    @JsonIgnore
    @TableField("DSS_L_UNIT")
    private String dssLUnit;

    @JsonIgnore
    @TableField("DSS_W")
    private Double dssW;

    @JsonIgnore
    @TableField("DSS_W_UNIT")
    private String dssWUnit;

    @JsonIgnore
    @TableField("DSS_D")
    private Double dssD;

    @JsonIgnore
    @TableField("DSS_D_UNIT")
    private String dssDUnit;

    @JsonIgnore
    @TableField("DSS_N")
    private Double dssN;

    @JsonIgnore
    @TableField("DSS_N_UNIT")
    private String dssNUnit;

    @JsonIgnore
    @TableField("DSS_A")
    private Double dssA;

    @JsonIgnore
    @TableField("DSS_A_UNIT")
    private String dssAUnit;

    @JsonIgnore
    @TableField("DSS_V")
    private Double dssV;

    @JsonIgnore
    @TableField("DSS_V_UNIT")
    private String dssVUnit;

    @JsonIgnore
    @TableField("DSS_P")
    private Double dssP;

    @JsonIgnore
    @TableField("DSS_G")
    private Double dssG;

    //病害定量字段
    private String haveDssColom;
    //计量单位
    private String dssUnit;
}
