package com.hualu.app.module.mems.mpc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.mpc.entity.MpcMpitem;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
public interface MpcMpitemService extends IService<MpcMpitem> {

    List<MpcMpitem> selectMpitems(Page page, String contrId, String dssType, String mpitemName);

    List<MpcMpitem> selectMpitemsByDssId(String dssId,String dssType,String contrId, String mpitemName);

    /**
     * 根据养护体系ID及条目前缀编码，查询对应的措施ID
     * @param mpSystemId
     * @param mpitemCodePrefixs 措施前缀 例如：Y1104
     * @return
     */
    List<MpcMpitem> selectMpitemsByMpSysIdAndMpitemCode(String mpSystemId,List<String> mpitemCodePrefixs);
}
