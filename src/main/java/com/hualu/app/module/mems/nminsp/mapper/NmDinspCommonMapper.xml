<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.nminsp.mapper.NmDinspCommonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.nminsp.entity.NmDinspCommon">
        <id column="DINSP_ID" property="dinspId" />
        <result column="DINSP_CODE" property="dinspCode" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNT_ORG_NM" property="mntOrgNm" />
        <result column="SEARCH_DEPT" property="searchDept" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="INSP_FREQUENCY" property="inspFrequency" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_NAME" property="lineName" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="STAKE_NAME" property="stakeName" />
        <result column="WEATHER" property="weather" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="STATUS" property="status" />
        <result column="DSS_NUM" property="dssNum" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="INSP_TIME_END" property="inspTimeEnd" />
        <result column="XC_TYPE" property="xcType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DINSP_ID, DINSP_CODE, MNT_ORG_ID, MNT_ORG_NM, SEARCH_DEPT, INSP_PERSON, INSP_DATE, INSP_TIME, INSP_FREQUENCY, LINE_CODE, LINE_NAME, LINE_DIRECT, ROUTE_NAME, STAKE_NAME, WEATHER, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, REMARK, STATUS, DSS_NUM, ROUTE_CODE, DEL_FLAG, INSP_TIME_END, XC_TYPE
    </sql>
    <select id="lastNmDinspCommon" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinspCommon">
        select * from (
        select * from nm_dinsp_common where  mnt_org_id = #{orgIdString} and del_flag=0 order by CREATE_TIME desc) where rownum = 1
    </select>
    <select id="countTotalCount" resultType="com.hualu.app.module.mems.nminsp.vo.NmDinspFacilityCatGroupVo">
        select FACILITY_CAT,count(1) as total_count from memsdb.NM_DINSP where COMMON_DINSP_ID=#{commonDinspId} group by FACILITY_CAT
    </select>
    <select id="countTodoCount" resultType="com.hualu.app.module.mems.nminsp.vo.NmDinspFacilityCatGroupVo">
        select FACILITY_CAT,count(1) as todo_count from memsdb.NM_DINSP where COMMON_DINSP_ID=#{commonDinspId} and exists(${taskSql}) group by FACILITY_CAT
    </select>

    <select id="selectPageFor" resultType="com.hualu.app.module.mems.nminsp.entity.NmDinspCommon">
        select *
        from MEMSDB.NM_DINSP_COMMON d
        where d.DINSP_ID in (select nm_dinsp.COMMON_DINSP_ID
        from MEMSDB.NM_DINSP nm_dinsp
        where nm_dinsp.COMMON_DINSP_ID is not null
      <if test="sql != null and sql != ''">
        and exists(${sql})
      </if>
        group by nm_dinsp.COMMON_DINSP_ID)
        <trim prefix="AND" prefixOverrides="WHERE">
            ${ew.customSqlSegment}
        </trim>
    </select>

    <select id="getNmDinspCommonToto"
      resultType="com.hualu.app.module.mems.nminsp.vo.NmDinspExtraInfo">
        select d.DINSP_ID commonDinspId, count(*) todoCount
        from MEMSDB.NM_DINSP_COMMON d
        inner join MEMSDB.NM_DINSP cc on d.DINSP_ID = cc.COMMON_DINSP_ID
        where cc.DINSP_ID in (
        select b.DINSP_ID from MEMSDB.NM_DINSP b where exists(${sql})
        and b.del_flag = 0 group by b.DINSP_ID
        )
          and d.DINSP_ID in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and cc.del_flag = 0 and d.del_flag = 0
        group by d.DINSP_ID
    </select>

    <select id="getNmDinspCommonTotal"
      resultType="com.hualu.app.module.mems.nminsp.vo.NmDinspExtraInfo">
        select d.DINSP_ID commonDinspId, count(*) totalCount
        from MEMSDB.NM_DINSP_COMMON d
        inner join MEMSDB.NM_DINSP cc on d.DINSP_ID = cc.COMMON_DINSP_ID
        where d.DINSP_ID in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and cc.del_flag = 0 and d.del_flag = 0
        group by d.DINSP_ID
    </select>

    <select id="getNmDinspCommonDss"
      resultType="com.hualu.app.module.mems.nminsp.vo.NmDinspExtraInfo">
        select d.DINSP_ID commonDinspId, count(*) dssNum
        from MEMSDB.NM_DINSP_COMMON d
                 inner join MEMSDB.NM_DINSP cc on d.DINSP_ID = cc.COMMON_DINSP_ID
                 inner join MEMSDB.NM_DINSP_RECORD dss on dss.DINSP_ID = cc.DINSP_ID
        where d.DINSP_ID in
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach> and d.DEL_FLAG = 0 and cc.DEL_FLAG = 0 and dss.DEL_FLAG = 0
        group by d.DINSP_ID
    </select>
    <select id="listFailDinsp" resultType="com.hualu.app.module.mems.nminsp.dto.FailDinspDto">
        SELECT DISTINCT d.common_dinsp_id,
                        d.FACILITY_CAT,
                        d.create_insp_person,
                        u0.ORG_ID        AS create_org,
                        d.CREATE_USER_ID,
                        u0.USER_CODE     as create_user_code,
                        u0.USER_NAME     as create_user_name,
                        d.com_insp_person,
                        d.com_user_id as com_user_id,
                        u1.USER_CODE     as com_user_code,
                        u1.USER_NAME     as com_user_name,
                        u1.org_id        AS com_org
        FROM (SELECT a.FACILITY_CAT,
                     a.CREATE_USER_ID,
                     a.INSP_PERSON    as create_insp_person,
                     c.CREATE_USER_ID AS com_user_id,
                     c.INSP_PERSON    AS com_insp_person,
                     a.REMARK,
                     c.DINSP_ID          common_dinsp_id
              FROM NM_DINSP a
                       JOIN NM_DINSP_COMMON c ON a.COMMON_DINSP_ID = c.DINSP_ID and a.DEL_FLAG=0 and c.DEL_FLAG=0
              WHERE TO_CHAR(a.INSP_DATE, 'yyyy-MM') = '2025-05') d
                 inner JOIN gdgs.FW_RIGHT_USER u0 ON d.CREATE_USER_ID = u0.id
                 inner JOIN gdgs.FW_RIGHT_USER u1 ON d.com_user_id = u1.id
        WHERE d.CREATE_USER_ID != d.com_user_id
                and u0.ORG_ID != u1.ORG_ID
          ORDER BY com_user_id
    </select>
</mapper>
