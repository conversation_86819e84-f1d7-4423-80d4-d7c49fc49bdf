package com.hualu.app.module.mems.dinsp.entity;

import java.math.BigDecimal;

public class DmCountResult {
    private Integer order;

    private String type;

    private BigDecimal sum;

    private BigDecimal completed;

    private BigDecimal unCompleted;

    private BigDecimal lack;

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getSum() {
        return sum;
    }

    public void setSum(BigDecimal sum) {
        this.sum = sum;
    }

    public BigDecimal getCompleted() {
        return completed;
    }

    public void setCompleted(BigDecimal completed) {
        this.completed = completed;
    }

    public BigDecimal getUnCompleted() {
        return unCompleted;
    }

    public void setUnCompleted(BigDecimal unCompleted) {
        this.unCompleted = unCompleted;
    }

    public BigDecimal getLack() {
        return lack;
    }

    public void setLack(BigDecimal lack) {
        this.lack = lack;
    }
}
