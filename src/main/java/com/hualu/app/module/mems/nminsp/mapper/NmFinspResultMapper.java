package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.nminsp.entity.NmFinspResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 新版经常检查结论表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface NmFinspResultMapper extends BaseMapper<NmFinspResult> {

    List<NmFinspResult> selectByFinspId(@Param("finspId") String finspId);

    List<NmFinspResult> listByFinspIds(@Param("finspIds") List<String> finspIds);

  List<NmFinspResult> findNmFinspResult(@Param("finspId") String finspId);
}
