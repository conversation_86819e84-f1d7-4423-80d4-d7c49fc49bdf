package com.hualu.app.module.mems.dinsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmDinResult对象", description="")
public class DmDinResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键")
    @TableId("R_ID")
    private String rId;

    @ApiModelProperty(value = "检查内容结果")
    @TableField("DM_CONTENT")
    private String dmContent;

    @ApiModelProperty(value = "检查情况")
    @TableField("INSPECTIONS_BAK")
    private String inspectionsBak;

    @ApiModelProperty(value = "发现病害处理情况")
    @TableField(value= "DSS_TREA",fill = FieldFill.UPDATE)
    private String dssTrea;

    @ApiModelProperty(value = "检查内容模板")
    @TableField("REMARKS")
    private String remarks;

    @ApiModelProperty(value = "巡查单ID")
    @TableField("DM_DINSP_ID")
    private String dmDinspId;

    @ApiModelProperty(value = "序号")
    @TableField("XUHAO")
    private String xuhao;

    @TableField("INSPECTIONS")
    private String inspections;

    @ApiModelProperty(value = "维修紧迫度")
    @TableField("DEGREE")
    private String degree;


}
