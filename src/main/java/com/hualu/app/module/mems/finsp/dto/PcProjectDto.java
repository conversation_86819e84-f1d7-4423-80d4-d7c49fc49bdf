package com.hualu.app.module.mems.finsp.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tg.dev.api.annotation.MD5Field;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 专项
 */
@Accessors(chain = true)
@Data
public class PcProjectDto implements Serializable {

    @ApiModelProperty(value = "项目ID")
    @TableId("PRJ_ID")
    private String prjId;

    @NotBlank(message = "项目名称不能为空")
    @ApiModelProperty(value = "项目名称")
    @TableField("PRJ_NAME")
    private String prjName;

    @MD5Field
    @NotBlank(message = "单位类型不能为空")
    @ApiModelProperty(value = "单位类型（1：定检单位，2：养护单位）")
    @TableField("MNT_TYPE")
    private String mntType;

    @MD5Field
    @NotBlank(message = "设施类型不能为空")
    @ApiModelProperty(value = "设施类型（BP，HD、QL）")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @NotBlank(message = "排查单位名称不能为空")
    @ApiModelProperty(value = "排查单位名称")
    @TableField("INSP_ORG_NAME")
    private String inspOrgName;
}
