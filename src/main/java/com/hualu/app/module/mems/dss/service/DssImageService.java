package com.hualu.app.module.mems.dss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.dss.entity.DssImage;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-21
 */
public interface DssImageService extends IService<DssImage> {

    /**
     * 存储照片
     * @param dssId
     * @param fileIds
     * @param imageType 1：病害照片  6：经常检查工照
     */
    void saveDssImageForApp(String dssId, String fileIds, int imageType);

    /**
     * PC端照片先移除，再添加
     * @param dssId
     * @param fileIds
     * @param imageType
     */
    void saveDssImageForPC(String dssId, String fileIds, int imageType);

    /**
     * 存储照片
     * @param dssId 病害ID
     * @param fileIds 文件ID
     * @param imageType 图片类型
     * @param processing 0：维修前，1：维修后
     */
    void saveDssImageForApp(String dssId, String fileIds, Integer imageType, Integer processing);

    void saveDssImageForPC(String dssId, String fileIds, Integer imageType, Integer processing);

    /**
     * 根据文件ID，删除照片
     * @param fileId
     */
    void delByFileId(String fileId);

    /**
     * 根据业务ID，删除照片
     * @param dssId
     */
    void delByDssId(String dssId);

    void delBatchByDssIds(List<String> dssIds);

    /**
     * 查询病害所有照片
     * @param dssId
     * @return
     */
    List<DssImage> listByDssId(String dssId);

    /**
     * 根据病害ID，获取文件ID集合
     * @param dssId
     * @return
     */
    List<String> listFileIdsByDssId(String dssId);

    /**
     * 病害ID分组，查询病害照片文件ID
     * @param dssIds
     * @return
     */
    Map<String,List<String>> mapByDssIds(Set<String> dssIds);

    /**
     * 根据病害ID分组，查询照片集合
     * @param dssIds
     * @return
     */
    Map<String,List<DssImage>> mapGroupByDssIds(List<String> dssIds);
}
