package com.hualu.app.module.mems.task.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.task.dto.accpt.DmAccptHzDto;
import com.hualu.app.module.mems.task.entity.DmTaskAccpt;
import com.hualu.app.module.mems.task.entity.DmTaskAccptDetail;
import com.hualu.app.module.mems.task.mapper.DmTaskAccptMapper;
import com.hualu.app.module.mems.task.service.DmTaskAccptDetailService;
import com.hualu.app.module.mems.task.service.DmTaskAccptService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_RestResultHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 维修验收单
 * <AUTHOR>
 * @since 2023-06-15
 */
@Slf4j
@RestController
@RequestMapping("/dmTaskAccpt")
public class DmTaskAccptController extends M_MyBatisController<DmTaskAccpt, DmTaskAccptMapper> {

    @Autowired
    DmTaskAccptService accptService;

    @Autowired
    DmTaskAccptDetailService detailService;

    @Autowired
    DssImageService imageService;

    /**
     * 删除验收单
     * @param mtaskAccptId 验收单ID
     * @return
     */
    @GetMapping("/delete/{mtaskAccptId}")
    public RestResult deleteAccpt(@PathVariable("mtaskAccptId") String mtaskAccptId){
        accptService.delAccpt(mtaskAccptId);
        return RestResult.success("操作成功");
    }

    /**
     * 查询验收单明细（以病害为基准）
     * @param mtaskAccptId
     * @return
     */
    @PostMapping("selectAccptDss")
    public RestResult<DmAccptHzDto> selectAccptDss(String mtaskAccptId){
        DmAccptHzDto accptHzDto = detailService.getAcceptHzDto(mtaskAccptId);
        return RestResult.success(accptHzDto);
    }

    /**
     * 维修后照片上传
     * @param dssId 病害ID
     * @param files 图片文件
     * @return
     */
    @PostMapping("uploadDssImage")
    public RestResult uploadDssImage(@RequestParam(value = "dssId",required = true) String dssId,
                                     @RequestParam(value = "files") MultipartFile[] files){
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        if (CollectionUtil.isNotEmpty(fileIds)){
            imageService.saveDssImageForApp(dssId,StrUtil.join(",",fileIds),1,1);
        }
        return RestResult.success(StrUtil.join(",",fileIds));
    }

    /**
     * 删除照片
     * @param fileId
     * @return
     */
    @GetMapping("removeImage")
    public RestResult removeImage(@RequestParam String fileId){
        imageService.delByFileId(fileId);
        return RestResult.success("操作成功");
    }


    /**
     * 修改验收数量
     * @param mtaskAccptDtlId 验收单明细ID
     * @param acceptAmount 验收数量
     * @return
     */
    @PostMapping("updateAccptAmount")
    public RestResult updateAcceptAmount(@RequestParam String mtaskAccptDtlId,@RequestParam Double acceptAmount){
        DmTaskAccptDetail detail = new DmTaskAccptDetail();
        detail.setMtaskAccptDtlId(mtaskAccptDtlId);
        detail.setAcceptAmount(acceptAmount);
        detailService.updateById(detail);
        return RestResult.success("操作成功");
    }

    /**
     * 删除验收明细数据
     * @param mtaskAccptDtlIds 验收明细ID（多个以逗号分隔）
     * @return
     */
    @PostMapping("delAcceptDetails")
    public RestResult delAcceptDetails(@RequestParam String mtaskAccptDtlIds){
        List<String> ids = StrUtil.split(mtaskAccptDtlIds, ",");
        if (CollectionUtil.isNotEmpty(ids)){
            LambdaQueryWrapper<DmTaskAccptDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DmTaskAccptDetail::getMtaskAccptDtlId,ids);
            baseMapper.updateAccptNull(queryWrapper);
        }
        return RestResult.success("操作成功");
    }

    /**
     * 获取下一张单信息
     * @return
     */
    @GetMapping("getNextCode")
    public RestResult<DmTaskAccpt> getNextCode(){
        DmTaskAccpt nextCode = accptService.getNextCode();
        return RestResult.success(nextCode);
    }

    /**
     * 添加或修改验收单（PC端）
     * @return
     * @throws Exception
     */
    @SneakyThrows
    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("添加或修改验收单（PC端）")
    @PostMapping(value = "/saveOrUpdate")
    public RestResult<DmTaskAccpt> saveOrUpdate(@RequestBody DmTaskAccpt dmTaskAccpt){
        if (dmTaskAccpt.getProcessinstid() == null || dmTaskAccpt.getProcessinstid()==0){
            dmTaskAccpt.setStatus(0);
            String processDefName = "gdcg.emdc.mems.dm.DmTaskAcceptWorkFlow";
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "维修验收单["+ dmTaskAccpt.getMtaskAccptCode()+"]", "");
            dmTaskAccpt.setProcessinstid(processInstId);
            log.info("维修验收单实例ID："+processInstId);
        }
        accptService.saveOrUpdateAccpt(dmTaskAccpt);
        return RestResult.success(dmTaskAccpt);
    }

    /**
     * 添加或修改验收单（移动端）
     * @return
     * @throws Exception
     */
    @SneakyThrows
    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation("添加或修改验收单（移动端）")
    @PostMapping(value = "/saveOrUpdateApp")
    public RestResult<DmTaskAccpt> saveOrUpdateApp(@RequestBody DmTaskAccpt dmTaskAccpt){
        if (dmTaskAccpt.getProcessinstid() == null || dmTaskAccpt.getProcessinstid()==0){
            dmTaskAccpt.setStatus(0);
            String processDefName = "gdcg.emdc.mems.dm.DmTaskAcceptWorkFlow";
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "维修验收单["+ dmTaskAccpt.getMtaskAccptCode()+"]", "");
            dmTaskAccpt.setProcessinstid(processInstId);
            log.info("维修验收单实例ID："+processInstId);
        }
        dmTaskAccpt.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
        accptService.saveOrUpdate(dmTaskAccpt);
        return RestResult.success(dmTaskAccpt);
    }
    /**
     * 分页查询
     * @return
     */
    @PostMapping("selectPage")
    public RestResult<List<DmTaskAccpt>> selectPage(){
        Map reqParam = getReqParam();
        Page page = getPage();
        QueryWrapper queryWrapper = setWrapper(reqParam);
        IPage iPage = accptService.page(page,queryWrapper);
        List<DmTaskAccpt> records = iPage.getRecords();
        records.forEach(item->{
            item.setStatusName(H_BasedataHepler.acceptStatusMap.get(item.getStatus()));
            item.setIsInTimeName(H_BasedataHepler.acceptIsTimeMap.get(item.getIsInTime()));
        });
        return H_RestResultHelper.returnPage(iPage);
    }

    public QueryWrapper setWrapper(Map reqParam){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());

        String startDate = (String) reqParam.getOrDefault("startDate","");
        if (StrUtil.isNotBlank(startDate)){
            queryWrapper.apply("APPLY_DATE >= to_date({0},'YYYY-MM-dd')",startDate);
            reqParam.remove("startDate");
        }

        String endDate = (String) reqParam.getOrDefault("endDate","");
        if (StrUtil.isNotBlank(endDate)){
            queryWrapper.apply("APPLY_DATE <= to_date({0},'YYYY-MM-dd')",endDate);
            reqParam.remove("endDate");
        }

        String status = reqParam.getOrDefault("status", "").toString();
        String taskSql = H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.valueOf(status), "dm_task_accpt");

        if (StrUtil.isNotBlank(taskSql)){
            queryWrapper.exists(taskSql);
            reqParam.remove("status");
        }
        H_BatisQuery.setFieldValue2In(queryWrapper,reqParam, DmTaskAccpt.class);
        return queryWrapper;
    }
}
