package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmFinspResult;

import java.util.List;

/**
 * <p>
 * 新版经常检查结论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface NmFinspResultService extends IService<NmFinspResult> {

    /**
     * 补录未生成的检查结论
     * @param result
     */
    void supplementRemainingResult(NmFinspResult result);

    List<NmFinspResult> createNmFinspResult(NmFinsp nmDinsp);

    void delBatchByDinspIds(List<String> inspIds);

    List<NmFinspResult> listResultsByJA(List<String> finspIds, String inspCont);

    List<NmFinspResult> listResultsByFinspIds(List<String> finspIds,String cat);

    void createBatchNmFinspResult(NmFinsp nmfinsp, List<String> finspIds);

    boolean isNmFinspResultSet(String orgCode, String userCode);

    void updateFinspResultsByRecords(String finspId, String facilityCat, List<NmFinspRecord> nmFinspRecords);
}
