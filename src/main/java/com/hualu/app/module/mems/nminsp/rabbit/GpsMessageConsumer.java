package com.hualu.app.module.mems.nminsp.rabbit;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hualu.app.config.InspQueueConfig;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.hualu.app.module.mems.nminsp.service.NmDinspGpsService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Service
public class GpsMessageConsumer {

    private static final int BATCH_SIZE = 100;

    private static final String RETRY_NAME = "retryCount";

    // 使用线程安全的集合
    private final ConcurrentLinkedQueue<Message> batchMessages = new ConcurrentLinkedQueue<>();
    private final ConcurrentLinkedQueue<Long> deliveryTags = new ConcurrentLinkedQueue<>();

    private static final long MAX_INTERVAL = 60 * 1000; // 一分钟，单位：毫秒
    // 使用AtomicLong保证时间戳的原子性
    private final AtomicLong lastProcessTime = new AtomicLong(System.currentTimeMillis());

    @Autowired
    NmDinspGpsService gpsService;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @RabbitListener(queues = InspQueueConfig.GPS_QUEUE_NAME,containerFactory = "gpsQueueContainerFactory")
    public void batchConsume(Message message, Channel channel) {
        long deliveryTag = message.getMessageProperties().getDeliveryTag();
        batchMessages.add(message);
        deliveryTags.add(deliveryTag);
        long currentTime = System.currentTimeMillis();
        long elapsedTime = currentTime - lastProcessTime.get();
        System.out.println(StrUtil.format("当前时间: {}, 上次处理时间: {}, 时间间隔: {} 毫秒",currentTime, lastProcessTime, elapsedTime));
        if (batchMessages.size() >= BATCH_SIZE || elapsedTime >= MAX_INTERVAL) {
            processBatchMessages(batchMessages, deliveryTags, channel);
            batchMessages.clear();
            deliveryTags.clear();
            lastProcessTime.set(currentTime);
        }
    }


    public void processBatchMessages(ConcurrentLinkedQueue<Message> messages, ConcurrentLinkedQueue<Long> tags, Channel channel){
        log.info("开始批量处理，消息数量: {}", messages.size());
        try {
            // 将消息转换为实体类列表
            List<NmDinspGps> entities = new ArrayList<>();
            for (Message message : messages) {
                entities.add(convertToBean(message));
            }
            // 批量插入数据库
            gpsService.saveOrUpdateBatch(entities);
            // 全部确认消息
            /*for (Long deliveryTag : tags) {
                channel.basicAck(deliveryTag, false);
            }*/
            log.info("批量消息处理成功，已确认所有消息");
        } catch (Exception e) {
            // 消费失败，不进行消息确认，事务会自动回滚
            nackMessages(channel, messages, tags);
        }
    }

    private void nackMessages(Channel channel, ConcurrentLinkedQueue<Message> messages, ConcurrentLinkedQueue<Long> tags) {
        messages.forEach(message -> {
            int retryCount = getRetryCount(message);
            // 重试次数
            if (InspQueueConfig.RETRY_COUNT >= retryCount) {
                message.getMessageProperties().setHeader(RETRY_NAME, retryCount + 1);
                rabbitTemplate.send(message.getMessageProperties().getReceivedExchange(),
                        message.getMessageProperties().getReceivedRoutingKey(), message);
            }else {
                // 超过重试次数，加入死信队列
                rabbitTemplate.send(InspQueueConfig.GPS_DEAD_LETTER_EXCHANGE_NAME,InspQueueConfig.GPS_DEAD_LETTER_ROUTING_KEY,message);
            }
        });
    }

    private NmDinspGps convertToBean(Message message) {
        String content = StrUtil.str(message.getBody(), CharsetUtil.CHARSET_UTF_8);
        NmDinspGps bean = JSONUtil.toBean(content, NmDinspGps.class);
        return bean;
    }

    private int getRetryCount(Message message) {
        Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get(RETRY_NAME);
        return retryCount == null ? 0 : retryCount;
    }
}    