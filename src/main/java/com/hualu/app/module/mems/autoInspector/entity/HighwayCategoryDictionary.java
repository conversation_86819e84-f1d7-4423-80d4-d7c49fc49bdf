package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 巡检分类字典表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_CATEGORY_DICTIONARY")
@ApiModel(value="HighwayCategoryDictionary对象", description="巡检分类字典表")
public class HighwayCategoryDictionary implements Serializable {

    
    @NotBlank(message="[主键ID]不能为空")
    @Size(max= 36,message="编码长度不能超过36")
    @ApiModelProperty("主键ID")
    @TableField("ID")
    private String id;
    
    @NotBlank(message="[类别编码，如：100000(路面)、0(横向裂缝)等]不能为空")
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("类别编码，如：100000(路面)、0(横向裂缝)等")
    @TableField("CATEGORY_CODE")
    private String categoryCode;
    
    @NotBlank(message="[类别名称，如：路面、横向裂缝等]不能为空")
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("类别名称，如：路面、横向裂缝等")
    @TableField("CATEGORY_NAME")
    private String categoryName;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("父级编码，用于构建层级关系")
    @TableField("PARENT_CODE")
    private String parentCode;
    
    @NotNull(message="[级别类型：1-大类 2-一级类别 3-二级类别]不能为空")
    @ApiModelProperty("级别类型：1-大类 2-一级类别 3-二级类别")
    @TableField("LEVEL_TYPE")
    private Integer levelType;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("对象类型，对应objecttype字段")
    @TableField("OBJECT_TYPE")
    private String objectType;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("缺陷类型，对应defecttype字段")
    @TableField("DEFECT_TYPE")
    private String defectType;
    
    @Size(max= 20,message="编码长度不能超过20")
    @ApiModelProperty("状态编码")
    @TableField("STATUS_CODE")
    private String statusCode;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("状态名称")
    @TableField("STATUS_NAME")
    private String statusName;
    
    @ApiModelProperty("排序号")
    @TableField("SORT_ORDER")
    private Integer sortOrder;
    
    @ApiModelProperty("是否启用：1-启用 0-禁用")
    @TableField("IS_ENABLED")
    private Integer isEnabled;
}
