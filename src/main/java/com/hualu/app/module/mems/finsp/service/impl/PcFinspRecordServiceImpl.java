package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.finsp.dto.PcFinspDealDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspRecordExcelDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.entity.PcFinspRecord;
import com.hualu.app.module.mems.finsp.mapper.PcFinspRecordMapper;
import com.hualu.app.module.mems.finsp.service.*;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.utils.H_CoordinateTransformUtil;
import com.hualu.app.utils.H_OrgHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_UniqueConstraintHelper;
import com.tg.dev.mybatisplus.service.PageService;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 结构物排查记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
public class PcFinspRecordServiceImpl extends ServiceImpl<PcFinspRecordMapper, PcFinspRecord> implements PcFinspRecordService {

    @Autowired
    HttpServletRequest request;

    @Autowired
    PageService pageService;

    @Autowired
    PcFinspImageService imageService;

    @Lazy
    @Autowired
    PcFinspService finspService;

    @Autowired
    PcFinspDsstypeService dsstypeService;

    @Autowired
    PcFinspResultService resultService;

    @Autowired
    FwRightOrgService orgService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    PcProjectService projectService;

    @Autowired
    PcProjectScopeService projectScopeService;

    //边沟、急流槽、截水沟、深层排水措施、支挡防护措施 对应的病害类型
    static Map<String,List<String>> dsstypeMap = Maps.newHashMap();
    static Map<String,String> dealTypeMap = new HashMap<String,String>();

    // 养护处治类型(1:小修处治,2:专项处治,3:应急处治,4:无需处治)
    static {
        dealTypeMap.put("1","小修处治");
        dealTypeMap.put("2","专项处治");
        dealTypeMap.put("3","应急处治");
        dealTypeMap.put("4","无需处治");
        //边沟
        dsstypeMap.put("pg",Lists.newArrayList("34","35","36"));
        //急流槽
        dsstypeMap.put("jlc",Lists.newArrayList("46","47","48"));
        dsstypeMap.put("jsg",Lists.newArrayList());
        dsstypeMap.put("scps",Lists.newArrayList());
        //支挡防护措施
        dsstypeMap.put("zdfh",Lists.newArrayList("10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","28","29","30","31"));
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteFinspRecordByIds(List<String> recordIds) {
        if (CollectionUtil.isEmpty(recordIds)){
            return;
        }
        PcFinsp dbFinsp = getByRecordId(recordIds.get(0));
        imageService.removeByRecordIds(recordIds);
        resultService.updateFinspResult(dbFinsp);
        removeByIds(recordIds);
    }

    private PcFinsp getByRecordId(String recordId) {
        PcFinspRecord record = getById(recordId);
        return finspService.getById(record.getFinspId());
    }

    @Override
    public IPage selectViewRecordByFinspId(String finspId) {
        Page page = pageService.parsePageParam(request);
        page.setSize(200);
        LambdaQueryWrapper<PcFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotBlank(finspId),PcFinspRecord::getFinspId, finspId);
        queryWrapper.orderByDesc(PcFinspRecord::getCreateTime);
        IPage iPage = baseMapper.selectPage(page, queryWrapper);
        List<PcFinspRecord> records = iPage.getRecords();
        initViews(records);
        return iPage;
    }

    @Override
    public List<PcFinspRecord> listRecordByFinspId(String finspId) {
        LambdaQueryWrapper<PcFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspRecord::getFinspId, finspId);
        return list(queryWrapper);
    }

    private void initViews(List<PcFinspRecord> records) {
        if (CollectionUtil.isEmpty(records)){
            return;
        }
        //获取病害信息
        Set<String> typeIds = records.stream().map(PcFinspRecord::getDsstypeId).collect(Collectors.toSet());
        List<PcFinspDssTypeDto> typeDtos = dsstypeService.listByDsstypeIds(Lists.newArrayList(typeIds));
        Map<String, PcFinspDssTypeDto> dssTypeDtoMap = typeDtos.stream().collect(Collectors.toMap(PcFinspDssTypeDto::getDssTypeId, Function.identity()));

        //获取照片信息
        List<String> recordIds = records.stream().map(PcFinspRecord::getRecordId).collect(Collectors.toList());
        Map<String, List<PcFinspImage>> imageMap = imageService.selectByRecordIds(recordIds);
        records.forEach(item->{
            item.setFileIds(imageMap.get(item.getRecordId()));
            item.setImageHost(C_Constant.IMAGE_HOST);
            item.setDealTypeName(dealTypeMap.get(item.getDealType()));
            //初始化病害类型相关信息
            PcFinspDssTypeDto dto = dssTypeDtoMap.get(item.getDsstypeId());
            if (dto != null) {
                item.setPartType(dto.getPartType());
                item.setDsstypeName(dto.getDssTypeName());
                item.setGroupId(dto.getGroupId()).setGroupName(dto.getGroupName()).setPartTypeName(dto.getPartTypeName());
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PcFinspRecord saveOrUpdateFinspRecord(PcFinspRecord dmFinspRecord) {
        if (CollectionUtil.isNotEmpty(dmFinspRecord.getFileIds())){
            imageService.saveBatch(dmFinspRecord.getFileIds());
        }
        saveOrUpdate(dmFinspRecord);
        PcFinsp pcFinsp = finspService.getById(dmFinspRecord.getFinspId());
        finspService.updateDssNum(pcFinsp.getFinspId());
        //修改检查结论
        resultService.updateFinspResult(pcFinsp);
        initViews(Lists.newArrayList(dmFinspRecord));
        return dmFinspRecord;
    }


    @Override
    public List<PcFinspStatDto> getFinspStat(String startMonth, String endMonth, String orgId,String mntType,String facilityCat) {
        List<String> prjIds = projectService.listPrjId(orgId, DateUtil.thisYear(), mntType, facilityCat);
        if (CollectionUtil.isEmpty(prjIds)){
            return Lists.newArrayList();
        }
        //List<String> months = H_FacQueryHelper.rangeList(startMonth, endMonth);
        List<String> orgCodes = orgService.selectChildOprtOrgCodes(orgId);
        List<PcFinspStatDto> statDtos = baseMapper.getFinspStat(orgCodes,null,mntType,prjIds);

        //根据处治类型，统计病害数
        List<PcFinspStatDto> dealDtos = baseMapper.getFinspStatByDealType(orgCodes,mntType,prjIds);
        Map<String, PcFinspStatDto> dealMap = dealDtos.stream().collect(Collectors.toMap(PcFinspStatDto::getOrgName, Function.identity()));

        //生成md5值，用于分组判断
        statDtos.forEach(item->{
            String md5 = H_UniqueConstraintHelper.createUniqueArgs(item.getOrgName(), item.getMonth(), item.getMntType(), item.getDealType());
            item.setMd5(md5);
        });

        Map<String, String> orgMap = orgService.selectByOrgCodes(orgCodes);
        // 根据PC、WX分组
        Map<String, List<PcFinspStatDto>> typeMap = statDtos.stream().collect(Collectors.groupingBy(PcFinspStatDto::getType));
        Map<String, Integer> dssMap = typeMap.getOrDefault("PC",Lists.newArrayList()).stream().collect(Collectors.toMap(PcFinspStatDto::getMd5, PcFinspStatDto::getDssNum));
        Map<String, Integer> dealDssMap = typeMap.getOrDefault("WX",Lists.newArrayList()).stream().collect(Collectors.toMap(PcFinspStatDto::getMd5, PcFinspStatDto::getDealDssNum));

        //已检查结构物数量
        List<PcFinspStatDto> structList  = projectScopeService.checkStat(orgCodes, prjIds, null);
        Map<String, PcFinspStatDto> structMap = structList.stream().collect(Collectors.toMap(PcFinspStatDto::getMd5, Function.identity()));
        List<PcFinspStatDto> resultDtos = Lists.newArrayList();
        orgMap.values().forEach(item->{
            PcFinspStatDto statDto = new PcFinspStatDto();
            statDto.setOrgName(item);
            String md5 = H_UniqueConstraintHelper.createUniqueArgs(statDto.getOrgName(), statDto.getMonth(), statDto.getMntType(), statDto.getDealType());

            //int dssNumRandom = RandomUtil.randomInt(300, 1000);
            //int dealNumRandom = RandomUtil.randomInt(200, 268);

            int dssNumRandom = 0;
            int dealNumRandom = 0;

            // 如果有检查的真实数据，就按真实数据显示
            if (dssMap.get(md5) != null){
                statDto.setDssNum(dssMap.get(md5));
                statDto.setDealDssNum(dealDssMap.getOrDefault(md5, 0));
            }else {
                statDto.setDssNum(dssMap.getOrDefault(md5, dssNumRandom));
                statDto.setDealDssNum(dealDssMap.getOrDefault(md5, dealNumRandom));
            }

            PcFinspStatDto structDto = structMap.get(md5);
            if (structDto != null){
                statDto.setCheckedNum(structDto.getCheckedNum()).setNotCheckedNum(structDto.getNotCheckedNum());
            }else {
                //int structCheckRandom = RandomUtil.randomInt(300, 600);
                //int structNoCheckRandom = RandomUtil.randomInt(200, 300);
                int structCheckRandom = 0;
                int structNoCheckRandom = 0;
                statDto.setCheckedNum(structCheckRandom).setNotCheckedNum(structNoCheckRandom);
            }
            // 根据机构获取处治对象
            PcFinspStatDto orgDto = dealMap.get(statDto.getOrgName());
            if (orgDto != null){
                statDto.setXxDealNum(orgDto.getXxDealNum()).setNoDealNum(orgDto.getNoDealNum())
                        .setYjDealNum(orgDto.getYjDealNum()).setZxDealNum(orgDto.getZxDealNum());
            }

            resultDtos.add(statDto);
        });
        return resultDtos;
    }

    @Override
    public void removeByFinspIds(List<String> finspIds) {
        LambdaQueryWrapper<PcFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PcFinspRecord::getFinspId, finspIds);
        List<PcFinspRecord> recordList = list(queryWrapper);
        if (CollectionUtil.isNotEmpty(recordList)){
            List<String> recordIds = recordList.stream().map(PcFinspRecord::getRecordId).collect(Collectors.toList());
            imageService.removeByRecordIds(recordIds);
            removeByIds(recordIds);
        }
    }

    @Override
    public IPage listRecord(String status) {
        Page page = pageService.parsePageParam(request);
        //Map requestParams = PageHelper.getRequestParams(this.request);
        LambdaQueryWrapper<PcFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspRecord::getDealStatus,status);
        queryWrapper.orderByDesc(PcFinspRecord::getCreateTime);
        //H_BatisQuery.setFieldValue2In(queryWrapper,requestParams,PcFinspRecord.class);
        IPage iPage = baseMapper.selectPage(page, queryWrapper);
        List<PcFinspRecord> records = iPage.getRecords();
        initViews(records);
        initFinspInfo(records);
        return iPage;
    }

    /**
     * 初始化结构物名称及边坡单号
     * @param records
     */
    private void initFinspInfo(List<PcFinspRecord> records) {
        if (CollectionUtil.isEmpty(records)){
            return;
        }
        Set<String> finspIds = records.stream().map(PcFinspRecord::getFinspId).collect(Collectors.toSet());
        Collection<PcFinsp> pcFinsps = finspService.listByIds(finspIds);
        Map<String, PcFinsp> finspMap = pcFinsps.stream().collect(Collectors.toMap(PcFinsp::getFinspId, Function.identity()));
        records.forEach(item->{
            PcFinsp pcFinsp = finspMap.get(item.getFinspId());
            if (pcFinsp != null){
                item.setStructName(pcFinsp.getStructName());
                item.setFinspCode(pcFinsp.getFinspCode());
            }
        });
    }

    @Override
    public List<PcFinspRecordExcelDto> exportRecords(String orgId, String startMonth, String endMonth) {
        List<String> prjIds = projectService.listPrjId(orgId, DateUtil.thisYear(), null, null);
        if (CollectionUtil.isEmpty(prjIds)){
            return Lists.newArrayList();
        }
        List<String> orgCodes = H_OrgHelper.getChildOprtOrgCodes(orgId);
        //List<String> months = H_FacQueryHelper.rangeList(startMonth, endMonth);
        List<PcFinspRecordExcelDto> excelDtos = baseMapper.listByMonths(orgCodes,null,prjIds);
        initExcelData(excelDtos);
        return excelDtos;
    }

    @Override
    public List<PcFinspRecord> listRecordByStructId(String structId, String dealStatus) {
        List<String> months = H_FacQueryHelper.rangeList(new Date());
        List<PcFinspRecord> records = baseMapper.listRecordByStructId(structId,dealStatus,months);
        initViews(records);
        initFinspInfo(records);
        return records;
    }

    @Override
    public void updateStatus(String recordId) {
        if (StrUtil.isBlank(recordId)){
            return;
        }
        boolean dealFinsh = imageService.isDealFinsh(recordId);
        PcFinspRecord record = getById(recordId);
        if (record == null) {
            return;
        }
        record.setDealStatus(dealFinsh?"1":"0");
        updateById(record);
    }

    @Override
    public List<PcFinspRecordExcelDto> listRecordExcelDtoByPrjId(List<String> prjIds) {
        List<PcFinspRecordExcelDto> excelDtos = baseMapper.listByMonths(null,null,prjIds);
        initExcelData(excelDtos);
        return excelDtos;
    }

    @Override
    public List<PcFinspDealDto> getGeometryDeal(GeometryParamDto paramDto) {

        double[] minXy = H_CoordinateTransformUtil.bd09towgs84(paramDto.getMinX(), paramDto.getMinY());
        double[] maxXy = H_CoordinateTransformUtil.bd09towgs84(paramDto.getMaxX(), paramDto.getMaxY());
        paramDto.setMinX(minXy[0]).setMinY(minXy[1]).setMaxX(maxXy[0]).setMaxY(maxXy[1]);

        //地图层级小于16时,不查询数据
        if (paramDto.getLevel() <= 16){
            return Lists.newArrayList();
        }

        List<String> prjIds = projectService.listPrjId(CustomRequestContextHolder.getOrgIdString(), DateUtil.thisYear(), null, "BP");
        List<PcFinspDealDto> dealDtos = baseMapper.getGeometryDeal(prjIds,paramDto);
        return dealDtos;
    }

    @Override
    public List<Double> getXyByFinspId(String finspId) {
        LambdaQueryWrapper<PcFinspRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PcFinspRecord::getFinspId,finspId).isNotNull(PcFinspRecord::getX).isNotNull(PcFinspRecord::getY);

        List<PcFinspRecord> recordList = list(queryWrapper);
        if (CollectionUtil.isEmpty(recordList)){
            return null;
        }
        return Lists.newArrayList(recordList.get(0).getX(),recordList.get(0).getY());
    }

    private void initExcelData(List<PcFinspRecordExcelDto> excelDtos) {
        if (CollectionUtil.isEmpty(excelDtos)){
            return;
        }
        AtomicInteger index = new AtomicInteger(0);
        excelDtos.forEach(item->{
            item.setIndex(index.getAndIncrement());
            item.setDealType(dealTypeMap.get(item.getDealType()));
            dsstypeMap.forEach((k,v)->{
                Object fieldValue = ReflectUtil.getFieldValue(item, k + "Status");
                if (fieldValue != null && CollectionUtil.isNotEmpty(v)) {
                    boolean contains = v.contains(fieldValue);
                    ReflectUtil.setFieldValue(item, k + "Status", contains?"否":"是");
                }else {
                    ReflectUtil.setFieldValue(item, k + "Status", "是");
                }
            });
        });
    }
}
