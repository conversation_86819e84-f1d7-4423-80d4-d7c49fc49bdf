package com.hualu.app.module.mems.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskHzDto;
import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskMpItemViewDto;
import com.hualu.app.module.mems.task.entity.DmTaskDetail;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface DmTaskDetailService extends IService<DmTaskDetail> {

    /**
     * 维修工程记录删除时，修改任务单明细状态
     * @param accptDtlIds
     */
    void updateStatus(List<String> accptDtlIds);

    /**
     * 删除任务单详情数据
     * @param mtaskId
     */
    void delByMtaskId(String mtaskId);

    /**
     * 根据任务单ID，查询任务单明细
     * @param dmTaskId
     * @return
     */
    List<DmTaskDetailDto> selectTaskDetailsByTaskId(String dmTaskId);


    /**
     * 未验收的措施明细
     * @param mtaskIds
     * @return
     */
    List<DmTaskDetailDto> selectNoAccpt(List<String> mtaskIds);

    /**
     * 获取任务单病害汇总信息
     * @param dmTaskId
     * @return
     */
    DmTaskHzDto getTaskHzDto(String dmTaskId);

    /**
     * 查询措施信息
     * @param contrId
     * @param mtaskId
     * @param dssId
     * @return
     */
    List<DmTaskMpItemViewDto> selectMpItems(String contrId, String mtaskId, String dssId);
}
