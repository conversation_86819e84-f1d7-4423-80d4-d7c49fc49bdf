package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eos.workflow.omservice.WFParticipant;
import com.google.common.collect.Sets;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairStatDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspGpsCountDto;
import com.hualu.app.module.mems.finsp.dto.DmFinspRunningDto;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.finsp.mapper.DmFinspMapper;
import com.hualu.app.module.mems.finsp.service.*;
import com.hualu.app.module.mems.mpc.service.MpcFileRefService;
import com.hualu.app.module.platform.service.FwPlSystemParamsService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.hualu.app.module.workflow.service.WfprocessinstService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_FutureHelper;
import com.hualu.app.utils.mems.DateTimeUtil;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.api.util.hp.H_UniqueConstraintHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Validated
@Slf4j
@Service
public class DmFinspServiceImpl extends ServiceImpl<DmFinspMapper, DmFinsp> implements DmFinspService, IWorkItemEventHandler, OrderInfoHandler {

    private byte[] lock = new byte[0];

    @Autowired
    DmFinspItemService itemService;

    @Autowired
    DmFinspRecordService recordService;

    @Autowired
    DmFinspResultService resultService;

    @Autowired
    DmFinspResultSetService setService;

    @Autowired
    MpcFileRefService fileRefService;

    @Autowired
    DssInfoService dssInfoService;

    @Autowired
    WfprocessinstService instService;

    @Autowired
    DssImageService imageService;

    @Autowired
    FwPlSystemParamsService paramsService;

    @Autowired
    DmFinspGpsService gpsService;

    @Autowired
    FwRightOrgService orgService;

    public static String finspCode = "JCJC-{}-{}-{}";
    public static List<String> finspFacilities = Lists.newArrayList("QL","HD","SD","BP");

    @Override
    public DmFinsp getDmFinspByProcessInstId(Long processInstId) {
        LambdaQueryWrapper<DmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinsp::getProcessinstid,processInstId);
        return baseMapper.selectOne(queryWrapper);
    }

    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approveDmFinspNoDss(String userCode, String processInstIds, String nextUserId, String apprOpinion) {
        String[] processInstId = processInstIds.split(",");
        for(int i = 0 ; i<processInstId.length; i++){

            long instId =Long.valueOf(processInstId[i]);
            List<WFParticipant> wfParticipants = H_WorkFlowHelper.setWFParticipantsByPerson(nextUserId);
            if (CollectionUtil.isEmpty(wfParticipants)){
                throw new BaseException("审批人找不到:"+nextUserId);
            }
            H_WorkFlowHelper.setRelativeData(instId,"auditUser",wfParticipants.get(0));
            H_WorkFlowHelper.finshWorkItem(instId,apprOpinion);
            updateDmFinspStatus(instId,1);
        }
    }

    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approveDmFinsp(String userCode, String processInstIds, String nextUserId, String apprOpinion,int status) {

        String[] proInstIds = processInstIds.split(",");
        for (String proInstId : proInstIds) {
            long instId = Long.valueOf(proInstId);
            H_WorkFlowHelper.setRelativeData(instId,"status",status);
            H_WorkFlowHelper.finshWorkItem(instId,apprOpinion);
            int iStatus = initStatus(status,instId);
            updateDmFinspStatus(instId,status);

            if (2 == iStatus){
                DmFinsp dmFinsp = getDmFinspByProcessInstId(instId);
                BaseStructDto structDto = getStructDto(dmFinsp.getFacilityCat(),dmFinsp.getStructId());
                List<DmFinspRecord> dmFinspRecords = recordService.selectDssRecordByFinspId(dmFinsp.getFinspId());
                saveDssInfo(dmFinsp,structDto,dmFinspRecords);
            }
        }
    }

    /**
     * 保存病害信息
     * @param dmFinsp
     * @param dto
     * @param records
     * @return
     */
    private void saveDssInfo(DmFinsp dmFinsp,BaseStructDto dto,List<DmFinspRecord> records){
        if (CollectionUtil.isEmpty(records)){
            return ;
        }
        records.forEach(item->{
            DssInfo ds = new DssInfo();
            BeanUtils.copyProperties(item,ds);
            if (dto != null){
                ds.setRpIntrvlId(dto.getRpIntrvlId());
                ds.setRampId(dto.getRampId());
                ds.setStake(dto.getCntrStake());
                ds.setRlStakeNew(dto.getRlCntrStake());
                ds.setRoutecode(dto.getRouteCode());
                ds.setRouteversion(dto.getRouteVersion());
            }
            //todo 20230428 dss_info 全部存储逻辑桩号，无需转换成物理桩号
            /*if ("4".equals(item.getLineDirect()) && StrUtil.isNotBlank(item.getRampId())){
                H_DataAuthHelper.getRouteDto(item.getRampId(),item.getLineDirect(),item.getStake());
            }*/
            /*if (!"4".equals(item.getLineDirect())){
                BaseRouteDto routeDto = H_DataAuthHelper.getRouteDto(item.getLineId(), ds.getLinedirect(), ds.getStake());
                ds.setRlStakeNew(item.getStake());
            }*/
            //String idToString = H_KeyWorker.nextIdToString();
            //ds.setDssId(idToString);
            //必须采用 经常检查单明细的dssId，否则在病害库中，无法匹配来源单号
            //ds.setDssCode(ds.getDssId());
            ds.setDssImpFlag(0);
            ds.setRlStakeNew(item.getStake());
            ds.setFindDssUserName(dmFinsp.getInspPerson());
            ds.setFacilityCat(dmFinsp.getFacilityCat());
            ds.setStructId(dmFinsp.getStructId());
            ds.setRelTaskCode(dmFinsp.getFinspId());
            ds.setDssSource(2);
            ds.setRepairStatus(0);
            ds.setDealStatus(0);
            ds.setFoundDate(dmFinsp.getInspDate());
            ds.setMainRoadId(dmFinsp.getLineCode());

            if (StrUtil.isNotBlank(item.getLDssId())){
                ds.setDssId(item.getLDssId());
                dssInfoService.updateById(ds);
            }else {
                dssInfoService.save(ds);
            }
        });
    }

    /**
     * 获取结构物信息
     * @param facilityCat
     * @param structId
     * @return
     */
    private BaseStructDto getStructDto(String facilityCat,String structId){
        BaseStructDto structDto = null;
        IBaseStructFace iFacBase = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        if (iFacBase != null){
            structDto = iFacBase.getId(structId);
        }
        return structDto;
    }

    /**
     * 设置经常检查单状态
     * @param status
     * @param instId
     * @return
     */
    private int initStatus(int status,Long instId){
        int iStatus =1;
        boolean finish = H_WorkFlowHelper.isFinish(instId);
        if (1 == status) {
            iStatus = finish ? 2 : 1;
        }else {
            iStatus = 3;
        }
        return iStatus;
    }

    @Override
    public void updateDmFinspResult(DmFinsp dmFinsp) {
        boolean isUpdate = setService.isUpdateResultSet(dmFinsp.getMntOrgId(), CustomRequestContextHolder.getUserId());
        boolean updateFlag = false;
        //是否有权限修改经常检查结论
        if (isUpdate && (H_BasedataHepler.QL.equals(dmFinsp.getFacilityCat()) || H_BasedataHepler.SD.equals(dmFinsp.getFacilityCat()))){
            updateFlag = true;
        }
        if (H_BasedataHepler.BP.equals(dmFinsp.getFacilityCat()) && H_BasedataHepler.BPJC_VERSION.equals(dmFinsp.getFinVersion())){
            updateFlag = true;
        }
        if (updateFlag){
            //获取经常检查病害信息
            List<DmFinspRecord> dmFinspRecords = recordService.queryHisRecordByFinspId(dmFinsp.getFinspId(), dmFinsp.getFacilityCat());
            resultService.updateFinspResultsByRecords(dmFinsp.getFinspId(),dmFinsp.getFacilityCat(),dmFinspRecords);
        }
    }


    @Override
    public void updateDmFinspStatus(long processInstId, int status) {
        baseMapper.updateStatus(processInstId,status);
    }

    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delDmFinsp(String finspId) {
        try {
            DmFinsp item = this.getById(finspId);
            recordService.delDmFinspRecord(finspId);
            resultService.delDmFinspResult(finspId);
            imageService.delByDssId(finspId);
            fileRefService.delByBillId(finspId);
            gpsService.delByFinspId(finspId);
            removeById(finspId);
            H_WorkFlowHelper.deleteProcessInstance(item.getProcessinstid());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException("经常检查单删除失败:"+e.getMessage());
        }
    }

    @Override
    public boolean isContainCode(String facilityCat, String finspCode, String finspId) {
        if (StrUtil.isBlank(finspCode)){
            throw new BaseException("经常检查单号不能为空");
        }
        LambdaQueryWrapper<DmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinsp::getFinspCode,finspCode);
        queryWrapper.eq(DmFinsp::getFacilityCat,facilityCat);
        /*if (StrUtil.isNotBlank(finspId)){
            queryWrapper.eq(DmFinsp::getFinspId,finspId);
        }*/
        int count = 0;
        synchronized (lock){
            count = this.count(queryWrapper);
        }
        return count == 0 ? false : true;
    }

    @Override
    public String getNextCode(@NotBlank String facilityCat, String orgEn) {

        String curMonth = String.valueOf(DateTimeUtil.getCurMonth());
        curMonth= StringUtil.generateCode(curMonth, '0', 2) ;
        String strCode = StrUtil.format(finspCode,facilityCat,orgEn,DateTimeUtil.getCurYear()+curMonth);

        QueryWrapper<DmFinsp> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(substr(FINSP_CODE,length(FINSP_CODE)-3)) as finsp_code");
        queryWrapper.like("FINSP_CODE",strCode);
        queryWrapper.eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString());

        DmFinsp dmFinsp = baseMapper.selectOne(queryWrapper);

        long nextId = dmFinsp == null ? 0 : Math.abs(Long.valueOf(dmFinsp.getFinspCode()));
        ++nextId;
        String day = String.valueOf(DateTimeUtil.getCurDay());
        day = StringUtil.generateCode(day, '0', 2);
        return strCode + day + "-" + StringUtil.generateCode(String.valueOf(nextId), '0', 4);
    }


    @SneakyThrows
    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateFinsp(DmFinsp dmFinsp){
        if (StrUtil.isBlank(dmFinsp.getFinspCode())){
            throw new BaseException("经常检查单编号不能为空");
        }
        //验证单号
        String finspCode = dmFinsp.getFinspCode();
        StringUtil.checkCode(finspCode,4);
        if (dmFinsp.getProcessinstid() == null || dmFinsp.getProcessinstid()==0){
            String processDefName="gdcg.emdc.mems.dm.DFinspWorkFlow";
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "经常检查单["+ dmFinsp.getFinspCode()+"]", "");
            log.info("经常检查单实例ID："+processInstId);
            dmFinsp.setProcessinstid(processInstId);
            dmFinsp.setStatus(0);
            insertDmFinsp(dmFinsp);
            //添加或修改附件信息
            //fileRefService.saveFile(dmFinsp.getFinspId(), dmFinsp.getFileIds());
        }else {
            updateDmFinsp(dmFinsp);
        }
        imageService.saveDssImageForApp(dmFinsp.getFinspId(),dmFinsp.getFileIds(),6);
        //设置经常检查单病害数
        updateDssNum(dmFinsp.getFinspId());
        log.info("添加经常检查单："+dmFinsp.getFinspId());
    }

    /**
     * 添加经常检查
     * @param dmFinsp
     */
    private void insertDmFinsp(DmFinsp dmFinsp){
        if (dmFinsp.getInspDate() == null){
            dmFinsp.setInspDate(new Date());
        }
        //设置管养单位
        dmFinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
        if (finspFacilities.contains(dmFinsp.getFacilityCat())){
            Map<String, IBaseStructFace> beans = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class);
            //初始化检查单结构物名称
            beans.values().forEach(key->{
                if (dmFinsp.getFacilityCat().equals(key.getFacilityCat())){
                    BaseStructDto dto = key.getId(dmFinsp.getStructId());
                    dmFinsp.setStructName(dto.getStructName());
                }
            });
        }
        H_CValidator.validator2Exception(dmFinsp);
        boolean isAdd = StrUtil.isBlank(dmFinsp.getFinspId()) ? true : false;
        //添加时赋值经常检查单ID，用于生成检查检查项使用
        if (isAdd){
            dmFinsp.setFinspId(H_KeyWorker.nextIdToString());
        }
        setFinVersion(dmFinsp);
        //保存经常检查单
        saveOrUpdate(dmFinsp);
        //生成检查检查结论
        insertFinspResult(dmFinsp,isAdd);

        DmFinsp lastFinsp = getLastFinsp(dmFinsp,3);
        String lastFinspId = lastFinsp == null ? "" : lastFinsp.getFinspId();
        //生成经常检查病害明细
        recordService.insertRecord(dmFinsp,lastFinspId);
        updateDmFinspResult(dmFinsp);
    }

    private void updateDmFinsp(DmFinsp dmFinsp){
        DmFinsp dbFinsp = getById(dmFinsp.getFinspId());
        dmFinsp.setStatus(dbFinsp.getStatus());
        updateById(dmFinsp);
    }

    @Override
    public DmFinsp getLastFinsp(DmFinsp dmFinsp,Integer status) {
        QueryWrapper<DmFinsp> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("facility_cat",dmFinsp.getFacilityCat())
        .eq("mnt_org_id",dmFinsp.getMntOrgId());
        //.lt("rownum",2);
        //status=3（办理完成）复制病害时才能用到
        if (status != null){
            queryWrapper.eq("status",3);
        }
        queryWrapper.orderByDesc("insp_date");
        if (StrUtil.isNotBlank(dmFinsp.getStructId())){
            queryWrapper.eq("struct_id",dmFinsp.getStructId());
        }
        DmFinsp dbFinsp = baseMapper.getLast(queryWrapper);
        return dbFinsp;
    }

    @Override
    public void updateDssNum(String finspId) {
        baseMapper.updateDssNum(finspId);
    }

    @Override
    public List<DmFinspRunningDto> selectRunning() {
        Set<String> orgIds = H_DataAuthHelper.selectOrgIds();
        String currentDay = DateUtil.formatDate(new Date());
        String currentDate = DateUtil.now();
        String jgDate = DateUtil.offsetSecond(DateUtil.date(), -20).toString();
        List<DmFinspRunningDto> dtos = baseMapper.selectRunning(orgIds,currentDay,currentDate,jgDate);
        //List<DmFinspRunningDto> dtos = baseMapper.selectRunning(orgIds,"2024-09-29","2024-09-29 12:34:32","2024-09-29 11:00:32");
        return dtos;
    }

    @Override
    public String getFinspId(String structId, String facilityCat, Date xcDate) {
        //List<String> months = H_FacQueryHelper.getMonths(facilityCat, "1", DateUtil.format(xcDate, "yyyy-MM"));
        LambdaQueryWrapper<DmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DmFinsp::getFinspId);
        queryWrapper.eq(DmFinsp::getStructId,structId).eq(DmFinsp::getFacilityCat,facilityCat);
        queryWrapper.apply("TO_CHAR( INSP_DATE, 'yyyy-MM-dd' ) = {0}",DateUtil.formatDate(xcDate));
        List<DmFinsp> list = list(queryWrapper);
        if (CollectionUtil.isEmpty(list)){
            return null;
        }
        return list.get(0).getFinspId();
    }

    @Override
    public List<DmFinsp> listByQuarter(String structId, String facilityCat, Date xcDate) {
        List<String> months = H_FacQueryHelper.rangeList(xcDate);
        List<DmFinsp> dmFinsps = baseMapper.listByCycle(structId, facilityCat, months);
        return dmFinsps;
    }

    @Override
    public List<DmFinspGpsCountDto> countGpsCompleteStat(Map<String, Set<String>> orgRouteMap,String startMonth,String endMonth) {
        List<String> months = H_FacQueryHelper.rangeList(startMonth,endMonth);
        List<CompletableFuture<DmFinspGpsCountDto>> futureList = new ArrayList<>();
        orgRouteMap.forEach((k,v)->{
            CompletableFuture<DmFinspGpsCountDto> future = CompletableFuture.supplyAsync(() -> {
                DmFinspGpsCountDto countDto = baseMapper.countGps(v, months);
                countDto.setOrgCode(k);
                return countDto;
            });
            futureList.add(future);
        });
        List<DmFinspGpsCountDto> join = H_FutureHelper.sequence(futureList).join();
        List<String> orgList = join.stream().map(DmFinspGpsCountDto::getOrgCode).collect(Collectors.toList());
        Map<String, String> orgMap = orgService.selectByOrgCodes(orgList);
        //设置机构名称
        join.forEach(item->{
            item.setOrgName(orgMap.get(item.getOrgCode()));
        });
        return join;
    }

    @Override
    public List<DmFinspDssRepairDto> selectDssRepairPage(Page page, String month,String orgId) {
        List<String> orgCodes = orgService.selectChildOprtOrgCodes(orgId);
        List<DmFinspDssRepairDto> dssRepairDtos = baseMapper.selectDssRepairPage(page, Sets.newHashSet(orgCodes),month);
        return dssRepairDtos;
    }

    @Override
    public List<DmFinspDssRepairStatDto> selectDssRepairStat(String month, String orgId) {
        List<String> orgCodes = orgService.selectChildOprtOrgCodes(orgId);

        //查询病害数
        CompletableFuture<List<DmFinspDssRepairStatDto>> dssNumFuture = CompletableFuture.supplyAsync(() -> {
            List<DmFinspDssRepairStatDto> dtos = baseMapper.selectDssNum(month, orgCodes);
            return dtos;
        });

        //病害维修数
        CompletableFuture<List<DmFinspDssRepairStatDto>> repairNumFuture = CompletableFuture.supplyAsync(() -> {
            List<DmFinspDssRepairStatDto> dtos = baseMapper.selectDssRepairNum(month, orgCodes);
            return dtos;
        });

        List<DmFinspDssRepairStatDto> dssNumList = dssNumFuture.join();
        List<DmFinspDssRepairStatDto> repairNumList = repairNumFuture.join();
        // 组装病害维修数据到dssNumList中
        dssNumList.forEach(item->{
            if (CollectionUtil.isNotEmpty(repairNumList)){
                List<DmFinspDssRepairStatDto> statDtos = repairNumList.stream().filter(e -> e.getOrgName().equals(item.getOrgName()) && e.getFacilityCat().equals(item.getFacilityCat())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(statDtos)){
                    item.setRepairNum(statDtos.get(0).getRepairNum());
                }
            }
            String uniqueArgs = H_UniqueConstraintHelper.createUniqueArgs(item.getOrgName(), item.getFacilityCatName());
            item.setMd5(uniqueArgs);
        });

        List<DmFinspDssRepairStatDto> dssResult = Lists.newArrayList();

        //结构物分组
        Map<String, List<DmFinspDssRepairStatDto>> facMap = dssNumList.stream().collect(Collectors.groupingBy(DmFinspDssRepairStatDto::getMd5));
        facMap.forEach((k,v)->{
            DmFinspDssRepairStatDto entity = v.get(0);
            Integer dssNum = v.stream().map(DmFinspDssRepairStatDto::getDssNum).reduce(Integer::sum).orElse(0);
            Integer repairNum = v.stream().filter(e->ObjectUtil.isNotEmpty(e.getRepairNum())).map(DmFinspDssRepairStatDto::getRepairNum).reduce(Integer::sum).orElse(0);
            DmFinspDssRepairStatDto dto = new DmFinspDssRepairStatDto().setOrgName(entity.getOrgName()).setFacilityCatName(entity.getFacilityCatName()).setDssNum(dssNum).setRepairNum(repairNum);
            dssResult.add(dto);
        });
        return dssResult;
    }

    /**
     * 生成经常检查项
     * @param dmFinsp
     * @param isAdd
     */
    private void insertFinspResult(DmFinsp dmFinsp,boolean isAdd){
        //获取检查项内容
        List<DmFinspItem> dmFinspItems = itemService.queryItems(dmFinsp, CustomRequestContextHolder.getOrgIdString());
        //生成或者修改检查结论
        if (isAdd){
            //获取检查结论
            Map<String, IFacBase> beans = CustomApplicationContextHolder.getBeansOfType(IFacBase.class);
            beans.values().forEach(key->{
                if (dmFinsp.getFacilityCat().equals(key.getFacilityCat())){
                    List<DmFinspResult> dmFinspResults = key.initDmFinspItems(dmFinspItems, dmFinsp);
                    resultService.saveBatch(dmFinspResults);
                }
            });
        }
    }

    /**
     * 设置经常检查单版本
     * @param dmFinsp
     */
    private void setFinVersion(DmFinsp dmFinsp){
        if (H_BasedataHepler.HD.equals(dmFinsp.getFacilityCat())) {
            dmFinsp.setFinVersion("20221101");
        }

        if (H_BasedataHepler.QL.equals(dmFinsp.getFacilityCat()) || H_BasedataHepler.SD.equals(dmFinsp.getFacilityCat())) {
            dmFinsp.setFinVersion("201801");
        }

        if (H_BasedataHepler.BP.equals(dmFinsp.getFacilityCat())){
            dmFinsp.setFinVersion(getBpVersion());
        }
    }

    /**
     * 匹配边坡版本
     * @return
     */
    private String getBpVersion(){
        String bpVersion = "201801";
        String code = paramsService.selectSystemParamByParamCode("FINSP_BP");
        //根据配置的组织机构，采用新版本
        if (StrUtil.isBlank(code)){
            bpVersion = H_BasedataHepler.BPJC_VERSION;
        }else if (code.contains(CustomRequestContextHolder.getOrgIdString())){
            bpVersion = H_BasedataHepler.BPJC_VERSION;
        }
        return bpVersion;
    }

    @Override
    public Map<String, String> initAction() {
        return null;
    }

    @Override
    public String getProcessDefName() {
        return "gdcg.emdc.mems.dm.DFinspWorkFlow";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savedoEvent(long processInstId, boolean isEnd, String nextAction,String actId) {

        if (isEnd){
            DmFinsp dmFinsp = getDmFinspByProcessInstId(processInstId);
            BaseStructDto structDto = getStructDto(dmFinsp.getFacilityCat(),dmFinsp.getStructId());
            List<DmFinspRecord> dmFinspRecords = recordService.selectDssRecordByFinspId(dmFinsp.getFinspId());

            //String createUserName = instService.getProcessCreateUserName(processInstId);
            saveDssInfo(dmFinsp,structDto,dmFinspRecords);
            updateDmFinspStatus(processInstId,3);
            return;
        }
        if ("manualActivity".equals(actId)){
            updateDmFinspStatus(processInstId,1);
        }else if (nextAction.contains("审核")){
            updateDmFinspStatus(processInstId,2);
        }else if (nextAction.equals("退回")){
            updateDmFinspStatus(processInstId,-1);
        }
    }

    @Override
    public List<OrderInfo> queryOrderInfo(long processInstId) {
        DmFinsp dmFinsp = getDmFinspByProcessInstId(processInstId);
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderCode(dmFinsp.getFinspCode());
        orderInfo.setOrderType("JCJC");
        orderInfo.setOrderId(dmFinsp.getFinspId());
        orderInfo.setCat(dmFinsp.getFacilityCat());
        orderInfo.setStatus(dmFinsp.getStatus());
        orderInfo.setOrgCode(dmFinsp.getMntOrgId());
        return Arrays.asList(orderInfo);
    }
}
