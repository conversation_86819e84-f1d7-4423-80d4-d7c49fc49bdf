package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 打卡点表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AttendanceAddr对象", description="打卡点表")
public class AttendanceAddr implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private String id;

    @ApiModelProperty(value = "归属机构")
    @TableField("ORG_CODE")
    private String orgCode;

    @NotBlank(message = "结构物ID不能为空")
    @ApiModelProperty(value = "结构物名称ID")
    @TableField("STRUCT_ID")
    private String structId;

    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @NotBlank(message = "设施类型不能为空")
    @ApiModelProperty(value = "设施类型")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @NotNull(message = "纬度不能为空")
    @ApiModelProperty(value = "纬度")
    @TableField("GIS_X")
    private Double gisX;

    @NotNull(message = "经度不能为空")
    @ApiModelProperty(value = "经度")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "打卡点名称")
    @TableField("ADDR")
    private String addr;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(exist = false)
    private int isAttAddr = 0;//0：打卡点，1：结构物坐标
}
