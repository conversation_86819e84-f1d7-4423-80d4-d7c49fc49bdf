package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.util.StrUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.nminsp.entity.NmDinspResult;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspResultMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspItemService;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 新版日常巡查检查结论表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Api(tags = "NmDinspResultController",description = "新版日常巡查检查结论表 前端控制器")
@RestController
@RequestMapping("/nmDinspResult")
public class NmDinspResultController extends M_MyBatisController<NmDinspResult,NmDinspResultMapper>{

    private static final String JA_ISSUE_DESC_FORMAT = "{}存在{}，{}";

    @Autowired
    private NmDinspResultService service;

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    NmDinspRecordService nmDinspRecordService;

    @Autowired
    NmInspItemService itemService;

    @ApiOperation("查询日常巡查单结论（通用）")
    @GetMapping("listByDinspId/{dinspId}")
    public RestResult<List<NmDinspResult>> listByDinspId(@PathVariable("dinspId") String dinspId){
        List<NmDinspResult> results = service.listResultsByDinspIds(Lists.newArrayList(dinspId));
        results.stream().forEach(item->{
            if (StrUtil.isBlank(item.getIssueDesc())){
                item.setIssueDesc("未发现病害");
            }
        });
        return RestResult.success(results);
    }

    @ApiOperation("查询交安日常巡查单结论（自定义）")
    @GetMapping("listJaByDinspId/{dinspId}")
    public RestResult<List<NmDinspResult>> listJaByDinspId(@PathVariable("dinspId") String dinspId){
        List<NmDinspResult> results = service.listResultsByDinspIds(Lists.newArrayList(dinspId));
        results.stream().forEach(item->{
            if (StrUtil.isBlank(item.getIssueDesc())){
                item.setIssueDesc("未发现病害");
            }
        });
        return RestResult.success(results);
    }

    @ApiOperation("修改检查结论")
    @PostMapping(value="/updateResult")
    public RestResult<String> updateResult(@RequestBody NmDinspResult nmDinspResult){
        // 检查结论在会未新建，所以需要用saveOrUpdate
        service.supplementRemainingResult(nmDinspResult);
        return RestResult.success("操作成功");
    }
}
