package com.hualu.app.module.mems.dinsp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.module.mems.dinsp.entity.DmDinspRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface DmDinspRecordMapper extends BaseMapper<DmDinspRecord> {

    List<DmDinspRecord> selectRecords(String[] dssIds);

    List<DssInfoDto> selectDssRecordPage(Page page,@Param(Constants.WRAPPER) QueryWrapper queryWrapper);
}
