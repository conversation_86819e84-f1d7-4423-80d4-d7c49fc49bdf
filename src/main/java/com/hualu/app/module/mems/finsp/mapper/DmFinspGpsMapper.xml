<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.DmFinspGpsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.DmFinspGps">
        <id column="GPS_ID" property="gpsId" />
        <result column="FINSP_ID" property="finspId" />
        <result column="HEIGHT" property="height" />
        <result column="SPEED" property="speed" />
        <result column="LON" property="lon" />
        <result column="LAT" property="lat" />
        <result column="TIME" property="time" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        GPS_ID, FINSP_ID, HEIGHT, SPEED, LON, LAT, TIME
    </sql>
    <select id="getRealTimeLocationByFinspId" resultType="com.hualu.app.module.mems.finsp.entity.DmFinspGps">
        <![CDATA[
        select * from (select * from memsdb.dm_finsp_gps where finsp_id = #{finspId} order by time desc) a where rownum <= 1
        ]]>
    </select>

</mapper>
