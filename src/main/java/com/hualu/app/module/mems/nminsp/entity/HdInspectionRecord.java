package com.hualu.app.module.mems.nminsp.entity;


import java.util.Date;

public class HdInspectionRecord {
    private String routeName;       // 线路名称
    private String lineCode;        // 线路编码
    private String clvrtName;       // 变电站名称
    private String cntrStake;       // 杆塔编号
    private String dssNums;         // 地线数量(0显示为"无")
    private Date inspDate;        // 巡检日期
    private String inspFrequency;   // 巡检频率(固定为"每月一次")
    private String dinspCode;       // 巡检代码
    private String searchDept;

    // 构造方法
    public HdInspectionRecord() {}

    // Getter和Setter方法
    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getClvrtName() {
        return clvrtName;
    }

    public void setClvrtName(String clvrtName) {
        this.clvrtName = clvrtName;
    }

    public String getCntrStake() {
        return cntrStake;
    }

    public void setCntrStake(String cntrStake) {
        this.cntrStake = cntrStake;
    }

    public String getDssNums() {
        return dssNums;
    }

    public void setDssNums(String dssNums) {
        this.dssNums = dssNums;
    }

    public Date getInspDate() {
        return inspDate;
    }

    public void setInspDate(Date inspDate) {
        this.inspDate = inspDate;
    }

    public String getInspFrequency() {
        return inspFrequency;
    }

    public void setInspFrequency(String inspFrequency) {
        this.inspFrequency = inspFrequency;
    }

    public String getDinspCode() {
        return dinspCode;
    }

    public void setDinspCode(String dinspCode) {
        this.dinspCode = dinspCode;
    }

    @Override
    public String toString() {
        return "InspectionRecord{" +
                "routeName='" + routeName + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", clvrtName='" + clvrtName + '\'' +
                ", cntrStake='" + cntrStake + '\'' +
                ", dssNums='" + dssNums + '\'' +
                ", inspDate='" + inspDate + '\'' +
                ", inspFrequency='" + inspFrequency + '\'' +
                ", dinspCode='" + dinspCode + '\'' +
                '}';
    }

    public String getSearchDept() {
        return searchDept;
    }

    public void setSearchDept(String searchDept) {
        this.searchDept = searchDept;
    }
}
