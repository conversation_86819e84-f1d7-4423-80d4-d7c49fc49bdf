package com.hualu.app.module.mems.nminsp.rabbit;

import com.hualu.app.config.InspQueueConfig;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GpsMessageSender {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendMessage(Object message) {
        rabbitTemplate.convertAndSend(InspQueueConfig.GPS_EXCHANGE_NAME, InspQueueConfig.GPS_ROUTING_KEY, message);
    }
}    