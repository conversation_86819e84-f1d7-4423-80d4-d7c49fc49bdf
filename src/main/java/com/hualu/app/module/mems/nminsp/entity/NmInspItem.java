package com.hualu.app.module.mems.nminsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 新版日常巡查及经常检查结论配置表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmInspItem对象", description="新版日常巡查及经常检查结论配置表")
public class NmInspItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ITEM_ID")
    private String itemId;

    @ApiModelProperty(value = "设施类型：LM、QL")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "配置项编码")
    @TableField("ITEM_CODE")
    private String itemCode;

    @ApiModelProperty(value = "检查项目")
    @TableField("INSP_COM")
    private String inspCom;

    @ApiModelProperty(value = "检查内容/缺损项")
    @TableField("INSP_CONT")
    private String inspCont;

    @ApiModelProperty(value = "版本号")
    @TableField("FIN_VERSION")
    private String finVersion;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETE")
    private String isDelete;

    @ApiModelProperty(value = "描述")
    @TableField("FIN_DESC")
    private String finDesc;

    @ApiModelProperty(value = "备注")
    @TableField("FIN_REMARK")
    private String finRemark;

    @ApiModelProperty(value = "关联病害类型")
    @TableField("DSS_TYPE")
    private String dssType;

    @ApiModelProperty(value = "巡查频率;1：白天、2：夜间")
    @TableField("INSP_FREQUENCY")
    private Integer inspFrequency;

    @ApiModelProperty(value = "巡查类型；DM:日常、FM：经常")
    @TableField("XC_TYPE")
    private String xcType;

    @ApiModelProperty(value = "关联部位ID")
    @TableField("PART_ID")
    private String partId;

}
