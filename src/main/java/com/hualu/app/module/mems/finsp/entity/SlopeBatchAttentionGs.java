package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;

@ApiModel(description="HSMSDB.SLOPE_BATCH_ATTENTION_GS")
@TableName(value = "HSMSDB.SLOPE_BATCH_ATTENTION_GS")
public class SlopeBatchAttentionGs {
    @TableId(value = "ID", type = IdType.INPUT)
    @ApiModelProperty(value="")
    private String id;

    @TableField(value = "LINE_CODE")
    @ApiModelProperty(value="")
    private String lineCode;

    @TableField(value = "START_STAKE")
    @ApiModelProperty(value="")
    private BigDecimal startStake;

    @TableField(value = "END_STAKE")
    @ApiModelProperty(value="")
    private BigDecimal endStake;

    @TableField(value = "LINE_DIRECT")
    @ApiModelProperty(value="")
    private String lineDirect;

    @TableField(value = "SLOPE_LEVEL")
    @ApiModelProperty(value="")
    private String slopeLevel;

    @TableField(value = "SLOPE_TYPE")
    @ApiModelProperty(value="")
    private String slopeType;

    @TableField(value = "BLDM")
    @ApiModelProperty(value="")
    private BigDecimal bldm;

    @TableField(value = "IMPORTANT")
    @ApiModelProperty(value="")
    private BigDecimal important;

    @TableField(value = "YBBP")
    @ApiModelProperty(value="")
    private BigDecimal ybbp;

    @TableField(value = "YTB")
    @ApiModelProperty(value="")
    private BigDecimal ytb;

    @TableField(value = "SLOPE_LEADER")
    @ApiModelProperty(value="")
    private String slopeLeader;

    @TableField(value = "ROUTE_LEADER")
    @ApiModelProperty(value="")
    private String routeLeader;

    @TableField(value = "REMARK")
    @ApiModelProperty(value="")
    private String remark;

    @TableField(value = "START_LAT")
    @ApiModelProperty(value="")
    private BigDecimal startLat;

    @TableField(value = "START_LNG")
    @ApiModelProperty(value="")
    private BigDecimal startLng;

    @TableField(value = "END_LAT")
    @ApiModelProperty(value="")
    private BigDecimal endLat;

    @TableField(value = "END_LNG")
    @ApiModelProperty(value="")
    private BigDecimal endLng;

    /**
     * @return ID
     */
    public String getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return LINE_CODE
     */
    public String getLineCode() {
        return lineCode;
    }

    /**
     * @param lineCode
     */
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    /**
     * @return START_STAKE
     */
    public BigDecimal getStartStake() {
        return startStake;
    }

    /**
     * @param startStake
     */
    public void setStartStake(BigDecimal startStake) {
        this.startStake = startStake;
    }

    /**
     * @return END_STAKE
     */
    public BigDecimal getEndStake() {
        return endStake;
    }

    /**
     * @param endStake
     */
    public void setEndStake(BigDecimal endStake) {
        this.endStake = endStake;
    }

    /**
     * @return LINE_DIRECT
     */
    public String getLineDirect() {
        return lineDirect;
    }

    /**
     * @param lineDirect
     */
    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    /**
     * @return SLOPE_LEVEL
     */
    public String getSlopeLevel() {
        return slopeLevel;
    }

    /**
     * @param slopeLevel
     */
    public void setSlopeLevel(String slopeLevel) {
        this.slopeLevel = slopeLevel;
    }

    /**
     * @return SLOPE_TYPE
     */
    public String getSlopeType() {
        return slopeType;
    }

    /**
     * @param slopeType
     */
    public void setSlopeType(String slopeType) {
        this.slopeType = slopeType;
    }

    /**
     * @return BLDM
     */
    public BigDecimal getBldm() {
        return bldm;
    }

    /**
     * @param bldm
     */
    public void setBldm(BigDecimal bldm) {
        this.bldm = bldm;
    }

    /**
     * @return IMPORTANT
     */
    public BigDecimal getImportant() {
        return important;
    }

    /**
     * @param important
     */
    public void setImportant(BigDecimal important) {
        this.important = important;
    }

    /**
     * @return YBBP
     */
    public BigDecimal getYbbp() {
        return ybbp;
    }

    /**
     * @param ybbp
     */
    public void setYbbp(BigDecimal ybbp) {
        this.ybbp = ybbp;
    }

    /**
     * @return YTB
     */
    public BigDecimal getYtb() {
        return ytb;
    }

    /**
     * @param ytb
     */
    public void setYtb(BigDecimal ytb) {
        this.ytb = ytb;
    }

    /**
     * @return SLOPE_LEADER
     */
    public String getSlopeLeader() {
        return slopeLeader;
    }

    /**
     * @param slopeLeader
     */
    public void setSlopeLeader(String slopeLeader) {
        this.slopeLeader = slopeLeader;
    }

    /**
     * @return ROUTE_LEADER
     */
    public String getRouteLeader() {
        return routeLeader;
    }

    /**
     * @param routeLeader
     */
    public void setRouteLeader(String routeLeader) {
        this.routeLeader = routeLeader;
    }

    /**
     * @return REMARK
     */
    public String getRemark() {
        return remark;
    }

    /**
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * @return START_LAT
     */
    public BigDecimal getStartLat() {
        return startLat;
    }

    /**
     * @param startLat
     */
    public void setStartLat(BigDecimal startLat) {
        this.startLat = startLat;
    }

    /**
     * @return START_LNG
     */
    public BigDecimal getStartLng() {
        return startLng;
    }

    /**
     * @param startLng
     */
    public void setStartLng(BigDecimal startLng) {
        this.startLng = startLng;
    }

    /**
     * @return END_LAT
     */
    public BigDecimal getEndLat() {
        return endLat;
    }

    /**
     * @param endLat
     */
    public void setEndLat(BigDecimal endLat) {
        this.endLat = endLat;
    }

    /**
     * @return END_LNG
     */
    public BigDecimal getEndLng() {
        return endLng;
    }

    /**
     * @param endLng
     */
    public void setEndLng(BigDecimal endLng) {
        this.endLng = endLng;
    }
}