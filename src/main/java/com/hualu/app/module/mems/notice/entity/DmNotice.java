package com.hualu.app.module.mems.notice.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "MEMSDB.DM_NOTICE")
@TableName(value = "MEMSDB.DM_NOTICE")
public class DmNotice {
  @TableId(value = "NOTICE_ID")
  @ApiModelProperty(value = "")
  private String noticeId;

  @TableField(value = "NOTICE_CODE")
  @ApiModelProperty(value = "")
  @NotBlank(message = "通知单编码不能为空")
  private String noticeCode;

  @TableField(value = "LINE_CODE")
  @ApiModelProperty(value = "")
  @NotBlank(message = "路线不能为空")
  private String lineCode;

  @TableField(value = "MNTN_ORG_NM")
  @ApiModelProperty(value = "")
  private String mntnOrgNm;

  @TableField(value = "MNT_ORG_ID")
  @ApiModelProperty(value = "")
  private String mntOrgId;

  @TableField(value = "NOTICE_DATE")
  @ApiModelProperty(value = "")
  @NotNull(message = "日期不能为空")
  @JsonFormat(pattern = "yyyy-MM-dd")
  private Date noticeDate;

  @TableField(value = "REMARK")
  @ApiModelProperty(value = "")
  private String remark;

  @TableField(value = "MTASK_ID")
  @ApiModelProperty(value = "")
  private String mtaskId;

  @TableField(value = "MTASK_CODE")
  @ApiModelProperty(value = "")
  private String mtaskCode;

  @TableField(value = "SEND_USER")
  @ApiModelProperty(value = "")
  private String sendUser;

  @TableField(value = "SEND_TIME")
  @ApiModelProperty(value = "")
  private Date sendTime;

  @TableField(value = "CONFIRM_USER")
  @ApiModelProperty(value = "")
  private String confirmUser;

  @TableField(value = "CONFIRM_TIME")
  @ApiModelProperty(value = "")
  private Date confirmTime;

  @TableField(value = "AUDIT_OPINION")
  @ApiModelProperty(value = "")
  private String auditOpinion;

  @TableField(value = "CREATE_USER_ID")
  @ApiModelProperty(value = "")
  private String createUserId;

  @TableField(value = "CREATE_TIME")
  @ApiModelProperty(value = "")
  private Date createTime;

  @TableField(value = "UPDATE_USER_ID")
  @ApiModelProperty(value = "")
  private String updateUserId;

  @TableField(value = "UPDATE_TIME")
  @ApiModelProperty(value = "")
  private Date updateTime;

  @TableField(value = "\"STATUS\"")
  @ApiModelProperty(value = "")
  private Integer status;

  @TableField(value = "PROCESSINSTID")
  @ApiModelProperty(value = "")
  private Long processinstid;

  /**
   * 合同ID
   */
  @TableField(value = "CONTR_ID")
  @ApiModelProperty(value = "合同ID")
  private String contrId;

  /**
   * 状态名称
   */
  @TableField(exist = false)
  private String statusName;

  /**
   * 任务单号
   */
  @TableField(exist = false)
  private String taskCode;

  /**
   * 验收情况全部
   */
  @TableField(exist = false)
  private String totalNum;

  /**
   * 验收情况已验收
   */
  @TableField(exist = false)
  private String acceptNum;

  /**
   * 验收情况已关闭
   */
  @TableField(exist = false)
  private String undoNum;

  /**
   * 路线名称
   */
  @TableField(exist = false)
  private String lineName;

  /**
   * 签发人
   */
  @TableField(exist = false)
  private String sendUserName;

  /**
   * 接收人
   */
  @TableField(exist = false)
  private String confirmUserName;

  /**
   * 检查工照
   */
  @TableField(exist = false)
  private String fileIds;

  @TableField(exist = false)
  private String imageHost;

  public String getImageHost() {
    return imageHost;
  }

  public void setImageHost(String imageHost) {
    this.imageHost = imageHost;
  }

  public String getFileIds() {
    return fileIds;
  }

  public void setFileIds(String fileIds) {
    this.fileIds = fileIds;
  }

  public String getSendUserName() {
    return sendUserName;
  }

  public void setSendUserName(String sendUserName) {
    this.sendUserName = sendUserName;
  }

  public String getConfirmUserName() {
    return confirmUserName;
  }

  public void setConfirmUserName(String confirmUserName) {
    this.confirmUserName = confirmUserName;
  }

  public String getLineName() {
    return lineName;
  }

  public void setLineName(String lineName) {
    this.lineName = lineName;
  }

  public String getStatusName() {
    return statusName;
  }

  public void setStatusName(String statusName) {
    this.statusName = statusName;
  }

  public String getTaskCode() {
    return taskCode;
  }

  public void setTaskCode(String taskCode) {
    this.taskCode = taskCode;
  }

  public String getTotalNum() {
    return totalNum;
  }

  public void setTotalNum(String totalNum) {
    this.totalNum = totalNum;
  }

  public String getAcceptNum() {
    return acceptNum;
  }

  public void setAcceptNum(String acceptNum) {
    this.acceptNum = acceptNum;
  }

  public String getUndoNum() {
    return undoNum;
  }

  public void setUndoNum(String undoNum) {
    this.undoNum = undoNum;
  }

  public static final String COL_NOTICE_ID = "NOTICE_ID";

  public static final String COL_NOTICE_CODE = "NOTICE_CODE";

  public static final String COL_LINE_CODE = "LINE_CODE";

  public static final String COL_MNTN_ORG_NM = "MNTN_ORG_NM";

  public static final String COL_MNT_ORG_ID = "MNT_ORG_ID";

  public static final String COL_NOTICE_DATE = "NOTICE_DATE";

  public static final String COL_REMARK = "REMARK";

  public static final String COL_MTASK_ID = "MTASK_ID";

  public static final String COL_MTASK_CODE = "MTASK_CODE";

  public static final String COL_SEND_USER = "SEND_USER";

  public static final String COL_SEND_TIME = "SEND_TIME";

  public static final String COL_CONFIRM_USER = "CONFIRM_USER";

  public static final String COL_CONFIRM_TIME = "CONFIRM_TIME";

  public static final String COL_AUDIT_OPINION = "AUDIT_OPINION";

  public static final String COL_CREATE_USER_ID = "CREATE_USER_ID";

  public static final String COL_CREATE_TIME = "CREATE_TIME";

  public static final String COL_UPDATE_USER_ID = "UPDATE_USER_ID";

  public static final String COL_UPDATE_TIME = "UPDATE_TIME";

  public static final String COL_STATUS = "STATUS";

  public static final String COL_PROCESSINSTID = "PROCESSINSTID";

  public static final String COL_CONTR_ID = "CONTR_ID";

  /**
   * @return NOTICE_ID
   */
  public String getNoticeId() {
    return noticeId;
  }

  /**
   * @param noticeId
   */
  public void setNoticeId(String noticeId) {
    this.noticeId = noticeId;
  }

  /**
   * @return NOTICE_CODE
   */
  public String getNoticeCode() {
    return noticeCode;
  }

  /**
   * @param noticeCode
   */
  public void setNoticeCode(String noticeCode) {
    this.noticeCode = noticeCode;
  }

  /**
   * @return LINE_CODE
   */
  public String getLineCode() {
    return lineCode;
  }

  /**
   * @param lineCode
   */
  public void setLineCode(String lineCode) {
    this.lineCode = lineCode;
  }

  /**
   * @return MNTN_ORG_NM
   */
  public String getMntnOrgNm() {
    return mntnOrgNm;
  }

  /**
   * @param mntnOrgNm
   */
  public void setMntnOrgNm(String mntnOrgNm) {
    this.mntnOrgNm = mntnOrgNm;
  }

  /**
   * @return MNT_ORG_ID
   */
  public String getMntOrgId() {
    return mntOrgId;
  }

  /**
   * @param mntOrgId
   */
  public void setMntOrgId(String mntOrgId) {
    this.mntOrgId = mntOrgId;
  }

  /**
   * @return NOTICE_DATE
   */
  public Date getNoticeDate() {
    return noticeDate;
  }

  /**
   * @param noticeDate
   */
  public void setNoticeDate(Date noticeDate) {
    this.noticeDate = noticeDate;
  }

  /**
   * @return REMARK
   */
  public String getRemark() {
    return remark;
  }

  /**
   * @param remark
   */
  public void setRemark(String remark) {
    this.remark = remark;
  }

  /**
   * @return MTASK_ID
   */
  public String getMtaskId() {
    return mtaskId;
  }

  /**
   * @param mtaskId
   */
  public void setMtaskId(String mtaskId) {
    this.mtaskId = mtaskId;
  }

  /**
   * @return MTASK_CODE
   */
  public String getMtaskCode() {
    return mtaskCode;
  }

  /**
   * @param mtaskCode
   */
  public void setMtaskCode(String mtaskCode) {
    this.mtaskCode = mtaskCode;
  }

  /**
   * @return SEND_USER
   */
  public String getSendUser() {
    return sendUser;
  }

  /**
   * @param sendUser
   */
  public void setSendUser(String sendUser) {
    this.sendUser = sendUser;
  }

  /**
   * @return SEND_TIME
   */
  public Date getSendTime() {
    return sendTime;
  }

  /**
   * @param sendTime
   */
  public void setSendTime(Date sendTime) {
    this.sendTime = sendTime;
  }

  /**
   * @return CONFIRM_USER
   */
  public String getConfirmUser() {
    return confirmUser;
  }

  /**
   * @param confirmUser
   */
  public void setConfirmUser(String confirmUser) {
    this.confirmUser = confirmUser;
  }

  /**
   * @return CONFIRM_TIME
   */
  public Date getConfirmTime() {
    return confirmTime;
  }

  /**
   * @param confirmTime
   */
  public void setConfirmTime(Date confirmTime) {
    this.confirmTime = confirmTime;
  }

  /**
   * @return AUDIT_OPINION
   */
  public String getAuditOpinion() {
    return auditOpinion;
  }

  /**
   * @param auditOpinion
   */
  public void setAuditOpinion(String auditOpinion) {
    this.auditOpinion = auditOpinion;
  }

  /**
   * @return CREATE_USER_ID
   */
  public String getCreateUserId() {
    return createUserId;
  }

  /**
   * @param createUserId
   */
  public void setCreateUserId(String createUserId) {
    this.createUserId = createUserId;
  }

  /**
   * @return CREATE_TIME
   */
  public Date getCreateTime() {
    return createTime;
  }

  /**
   * @param createTime
   */
  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  /**
   * @return UPDATE_USER_ID
   */
  public String getUpdateUserId() {
    return updateUserId;
  }

  /**
   * @param updateUserId
   */
  public void setUpdateUserId(String updateUserId) {
    this.updateUserId = updateUserId;
  }

  /**
   * @return UPDATE_TIME
   */
  public Date getUpdateTime() {
    return updateTime;
  }

  /**
   * @param updateTime
   */
  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  /**
   * @return STATUS
   */
  public Integer getStatus() {
    return status;
  }

  /**
   * @param status
   */
  public void setStatus(Integer status) {
    this.status = status;
  }

  /**
   * @return PROCESSINSTID
   */
  public Long getProcessinstid() {
    return processinstid;
  }

  /**
   * @param processinstid
   */
  public void setProcessinstid(Long processinstid) {
    this.processinstid = processinstid;
  }

  /**
   * 获取合同ID
   *
   * @return CONTR_ID - 合同ID
   */
  public String getContrId() {
    return contrId;
  }

  /**
   * 设置合同ID
   *
   * @param contrId 合同ID
   */
  public void setContrId(String contrId) {
    this.contrId = contrId;
  }
}