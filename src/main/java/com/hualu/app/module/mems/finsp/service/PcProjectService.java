package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.PcProject;

import java.io.File;
import java.util.List;

/**
 * <p>
 * 结构物专项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
public interface PcProjectService extends IService<PcProject> {

    /**
     * 根据年份查询最新专项
     * @param orgId 公司ID
     * @param year 项目年份
     * @param mntType 单位类型（1：定检单位，2：养护单位）
     * @param facilityCat 设施类型（BP、HD）
     * @return
     */
    List<PcProject> listProjectByLast(String orgId, int year, String mntType, String facilityCat);

    /**
     * 查询项目ID
     * @param orgId
     * @param year
     * @param mntType
     * @param facilityCat
     * @return
     */
    List<String> listPrjId(String orgId, int year, String mntType, String facilityCat);

    /**
     * 创建专项项目
     * @param pcProject
     * @return
     */
    PcProject createProject(PcProject pcProject, File uploadFile);
}
