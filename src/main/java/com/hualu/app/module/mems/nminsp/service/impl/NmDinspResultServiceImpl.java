package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspResultMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.NmInspItemService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 新版日常巡查检查结论表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Service
public class NmDinspResultServiceImpl extends ServiceImpl<NmDinspResultMapper, NmDinspResult> implements NmDinspResultService {

    private static final String JA_ISSUE_DESC_FORMAT = "{}存在{}，{}";

    @Autowired
    NmInspItemService inspItemService;

    @Lazy
    @Autowired
    NmDinspRecordService dmRecordService;

    @Lazy
    @Autowired
    NmDinspService nmDinspService;

    @Override
    public List<NmDinspResult> createNmDinspResult(NmDinsp nmDinsp) {
        List<NmDinspResult> results = buildNmDinspResultByDinspIds(Lists.newArrayList(nmDinsp.getDinspId()),false);
        //生成检查结论
        if (CollectionUtil.isEmpty(results)){
            List<NmInspItem> items = inspItemService.listXcTypeByDinsp(nmDinsp, "DM");
            results = CollectionUtil.newArrayList();
            for (NmInspItem item : items) {
                NmDinspResult result = new NmDinspResult();
                result.setDinspId(nmDinsp.getDinspId()).setItemId(item.getItemId()).setResId(H_KeyWorker.nextIdToString())
                        .setDssType(item.getDssType()).setPartId(item.getPartId());
                results.add(result);
            }
            saveBatch(results);
        }
        // 清除病害描述，系统会根据最新病害自动生成结论
        results.stream().forEach(e->e.setIssueDesc(null));
        return results;
    }

    /**
     * 构建检查结论
     * @param dinspIds
     * @param buildResult  true：如果没有检查结论，也自动补全
     * @return
     */
    private List<NmDinspResult> buildNmDinspResultByDinspIds(List<String> dinspIds,boolean buildResult){
        if (CollectionUtil.isEmpty(dinspIds)){
            return Collections.emptyList();
        }
        Map<String, NmInspItem> itemMap = inspItemService.listXcTypeToMap("DM");
        List<NmDinspResult> allResults = Lists.newArrayList();
        ListUtil.partition(dinspIds,500).forEach(rows->{
            List<NmDinspResult> dbRes = lambdaQuery().in(NmDinspResult::getDinspId, dinspIds).list();
            if (CollectionUtil.isNotEmpty(dbRes)){
                allResults.addAll(dbRes);
            }
        });
        initInspItem(allResults,itemMap);
        // 不自动补全，直接返回
        if (!buildResult){
            return allResults;
        }

        LinkedHashMap<String, List<NmDinspResult>> dinspMap = allResults.stream().collect(Collectors.groupingBy(NmDinspResult::getDinspId, LinkedHashMap::new, Collectors.toList()));

        // 移除已经有检查记录的数据
        dinspIds.removeAll(dinspMap.keySet());

        buildNoResultList(dinspIds,allResults);

        for (String dinspId : dinspIds) {
            List<NmDinspResult> dbRes = dinspMap.get(dinspId);
            initInspItem(dbRes, itemMap);
        }
        allResults.forEach(item->{
            if (StrUtil.isBlank(item.getIssueDesc())){
                item.setIssueDesc("未发现病害");
            }
        });
        return allResults;
    }

    private static void initInspItem(List<NmDinspResult> dbRes, Map<String, NmInspItem> itemMap) {
        // 如果有结论，不做处理
        if (CollectionUtil.isNotEmpty(dbRes)){
            dbRes.forEach(result->{
                NmInspItem item = itemMap.get(result.getItemId());
                if (item != null){
                    result.setItemId(item.getItemId())
                            .setDssType(item.getDssType())
                            .setPartId(item.getPartId())
                            .setInspCom(item.getInspCom())
                            .setInspCont(item.getInspCont());
                    if (StrUtil.isBlank(result.getResId())){
                        result.setResId(H_KeyWorker.nextIdToString());
                    }
                }
            });
        }
    }

    @Override
    public void createBatchNmDinspResult(NmDinsp nmDinsp, List<String> dinspIds) {
        // 防御性校验：确保输入参数非空
        if (nmDinsp == null || CollectionUtil.isEmpty(dinspIds)) {
            return ;
        }

        // 使用 Optional 避免显式空检查，并直接返回空列表
        List<NmDinspResult> results = Optional.ofNullable(inspItemService.listXcTypeByDinsp(nmDinsp, "DM"))
                .filter(items -> !items.isEmpty())
                .map(items -> dinspIds.stream()
                        // 并行流提升性能（需确保线程安全）
                        .parallel()
                        // 扁平化笛卡尔积生成结果对象
                        .flatMap(dinspId -> items.stream()
                                .map(item -> buildNmDinspResult(dinspId, item))
                        )
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        saveBatch(results);
    }

    // 封装对象构建逻辑
    private NmDinspResult buildNmDinspResult(String dinspId, NmInspItem item) {
        return new NmDinspResult()
                .setDinspId(dinspId)
                .setItemId(item.getItemId())
                .setResId(H_KeyWorker.nextIdToString())
                .setDssType(item.getDssType())
                .setPartId(item.getPartId());
    }

    @Override
    public void delBatchByDinspIds(List<String> dinspIds) {
        if (CollectionUtil.isEmpty(dinspIds)){
            return;
        }
        LambdaQueryWrapper<NmDinspResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(NmDinspResult::getDinspId, dinspIds);
        remove(wrapper);
    }

    @Override
    public List<NmDinspResult> listResultsByJA(List<String> dinspIds,String inspCont) {
        List<NmDinspResult> resultAll = Lists.newArrayList();
        List<NmDinspRecord> nmDinspRecords = dmRecordService.lambdaQuery().in(NmDinspRecord::getDinspId, dinspIds).list();
        dmRecordService.showView(nmDinspRecords,null);

        LinkedHashMap<String, List<NmDinspRecord>> dinspMap = nmDinspRecords.stream().filter(e -> StrUtil.isNotBlank(e.getDinspId())).collect(Collectors.groupingBy(NmDinspRecord::getDinspId, LinkedHashMap::new, Collectors.toList()));
        // 有病害的数据才会回显
        dinspMap.forEach((k,v)->{
            List<NmDinspResult> tempResults = Lists.newArrayList();
            v.forEach(record->{
                NmDinspResult nmDinspResult = new NmDinspResult();
                String stake = record.getStake() == null ? null : record.getStake().toString();
                nmDinspResult.setResId(H_KeyWorker.nextIdToString()).setInspCont(inspCont).setInspCom(H_StakeHelper.convertCnStake(stake))
                        .setDinspId(k);
                String issueDesc = StrUtil.format(JA_ISSUE_DESC_FORMAT, record.getStructPartName(), record.getDssTypeName(), record.getDssNum());
                if (StrUtil.isNotBlank(record.getDssDesc())){
                    issueDesc = issueDesc + "，病害描述："+record.getDssDesc();
                }
                issueDesc = record.getLineDirectName()+"方向，"+issueDesc;
                nmDinspResult.setIssueDesc(issueDesc);
                tempResults.add(nmDinspResult);
            });
            resultAll.addAll(tempResults);
        });

        // 移除已经生成结论的数据
        boolean b = dinspIds.removeAll(dinspMap.keySet());
        //如果系统没有病害，初始化一项检查结论
        for (String dinspId : dinspIds) {
            NmDinspResult nmDinspResult = new NmDinspResult().setResId(H_KeyWorker.nextIdToString()).setInspCont(inspCont).setDinspId(dinspId);
            resultAll.add(nmDinspResult);
        }
        return resultAll;
    }

    @Override
    public List<NmDinspResult> listResultsByDinspIds(List<String> dinspIds) {
        return buildNmDinspResultByDinspIds(dinspIds,true);
    }

    @Override
    public void supplementRemainingResult(NmDinspResult result) {
        NmDinsp nmDinsp = nmDinspService.getById(result.getDinspId());
        if (null == nmDinsp) {
            return;
        }
        // 数据库存在的结论项
        List<NmDinspResult> dbResults = lambdaQuery().eq(NmDinspResult::getDinspId, result.getDinspId()).list();
        List<NmInspItem> inspItems = inspItemService.listXcTypeByDinsp(nmDinsp, "DM");
        // 说明检查结论已经补充完整
        if (inspItems.size() == dbResults.size()) {
            updateById(result);
            return;
        }
        List<NmDinspResult> remainResults = Lists.newArrayList();
        // 查询未生成的结论项
        inspItems.forEach(row->{
            if (row.getItemId().equals(result.getItemId())) {
                remainResults.add(result);
            }else {
                NmDinspResult newResult = BeanUtil.copyProperties(row, NmDinspResult.class);
                newResult.setDinspId(result.getDinspId());
                remainResults.add(newResult);
            }
        });
        if (CollectionUtil.isNotEmpty(remainResults)) {
            saveOrUpdateBatch(remainResults);
        }
    }

    /**
     * 组装未生成结论的数据
     * @param noResultDinspIds
     * @param allResults
     */
    private void buildNoResultList(List<String> noResultDinspIds, List<NmDinspResult> allResults) {
        List<NmDinsp> nmDinsps = nmDinspService.listDinspIdAndInspFrequency(noResultDinspIds);
        if (CollectionUtil.isNotEmpty(nmDinsps)){
            Map<String, List<NmDinsp>> groupMap = nmDinsps.stream().collect(Collectors.groupingBy(e -> e.getFacilityCat() + "_" + e.getInspFrequency()));
            groupMap.forEach((k,v)->{
                // 导出都是一种设施的数据
                List<NmInspItem> allItems = inspItemService.listXcTypeByDinsp(new NmDinsp().setFacilityCat(nmDinsps.get(0).getFacilityCat()), "DM");
                allResults.addAll(buildDinspResult(nmDinsps,allItems));
            });
        }
    }

    /**
     * 构件未生成结论的检查单
     * @param nmDinsps
     * @param allItems
     * @return
     */
    private List<NmDinspResult> buildDinspResult(List<NmDinsp> nmDinsps,List<NmInspItem> allItems){
        List<NmDinspResult> results = Lists.newArrayList();

        nmDinsps.forEach(dinsp->{
            Integer inspFrequency;
            if (H_BasedataHepler.BP.equals(dinsp.getFacilityCat())){
                inspFrequency = 1;
            } else {
                inspFrequency = dinsp.getInspFrequency();
            }
            List<NmInspItem> items = allItems.stream().filter(e -> e.getInspFrequency().equals(inspFrequency)
                    && StrUtil.isNotBlank(dinsp.getFacilityCat()) && e.getFacilityCat().equals(dinsp.getFacilityCat()))
                    .collect(Collectors.toList());

            items.forEach(row->{
                NmDinspResult nmDinspResult = new NmDinspResult();
                nmDinspResult.setDinspId(dinsp.getDinspId())
                        .setItemId(row.getItemId())
                        .setInspCom(row.getInspCom())
                        .setInspCont(row.getInspCont())
                        .setIssueDesc("未发现病害");
                results.add(nmDinspResult);
            });
        });
        return results;
    }
}
