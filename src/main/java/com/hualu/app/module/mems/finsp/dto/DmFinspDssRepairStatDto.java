package com.hualu.app.module.mems.finsp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 病害维修统计图
 */
@Accessors(chain = true)
@Data
public class DmFinspDssRepairStatDto implements Serializable {

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 结构物类型 QL、SD、HD、BP
     */
    @JsonIgnore
    private String facilityCat;

    /**
     * 结构物类型
     */
    private String facilityCatName;

    /**
     * 病害数量
     */
    private Integer dssNum;

    /**
     * 病害维修病害
     */
    private Integer repairNum;

    @JsonIgnore
    private String md5;
}
