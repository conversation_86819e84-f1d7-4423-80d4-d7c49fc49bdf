<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.dss.mapper.DssInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.dss.entity.DssInfo">
        <id column="DSS_ID" property="dssId" />
        <result column="DSS_CODE" property="dssCode" />
        <result column="RP_INTRVL_ID" property="rpIntrvlId" />
        <result column="STAKE" property="stake" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="DSS_TYPE" property="dssType" />
        <result column="DSS_DEGREE" property="dssDegree" />
        <result column="MNTN_ADVICE" property="mntnAdvice" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_TYPE" property="structType" />
        <result column="STRUCT_PART_ID" property="structPartId" />
        <result column="STRUCT_COMP_ID" property="structCompId" />
        <result column="LANE" property="lane" />
        <result column="DSS_POSITION" property="dssPosition" />
        <result column="DSS_CAUSE" property="dssCause" />
        <result column="DSS_DESC" property="dssDesc" />
        <result column="DSS_L" property="dssL" />
        <result column="DSS_L_UNIT" property="dssLUnit" />
        <result column="DSS_W" property="dssW" />
        <result column="DSS_W_UNIT" property="dssWUnit" />
        <result column="DSS_D" property="dssD" />
        <result column="DSS_D_UNIT" property="dssDUnit" />
        <result column="DSS_N" property="dssN" />
        <result column="DSS_N_UNIT" property="dssNUnit" />
        <result column="DSS_A" property="dssA" />
        <result column="DSS_A_UNIT" property="dssAUnit" />
        <result column="DSS_V" property="dssV" />
        <result column="DSS_V_UNIT" property="dssVUnit" />
        <result column="DSS_P" property="dssP" />
        <result column="DSS_G" property="dssG" />
        <result column="DSS_IMP_FLAG" property="dssImpFlag" />
        <result column="DSS_QUALITY" property="dssQuality" />
        <result column="HIS_DSS_ID" property="hisDssId" />
        <result column="DSS_SOURCE" property="dssSource" />
        <result column="REL_TASK_CODE" property="relTaskCode" />
        <result column="FOUND_DATE" property="foundDate" />
        <result column="REPAIR_STATUS" property="repairStatus" />
        <result column="REPAIR_DATE" property="repairDate" />
        <result column="DEAL_STATUS" property="dealStatus" />
        <result column="DEAL_MEASURE" property="dealMeasure" />
        <result column="DEAL_OPINION" property="dealOpinion" />
        <result column="DEAL_PRSN" property="dealPrsn" />
        <result column="DEAL_DATE" property="dealDate" />
        <result column="DEAL_BILL_ID" property="dealBillId" />
        <result column="X" property="x" />
        <result column="Y" property="y" />
        <result column="MTASK_ID" property="mtaskId" />
        <result column="MTASK_ACCPT_ID" property="mtaskAccptId" />
        <result column="MAIN_ROAD_ID" property="mainRoadId" />
        <result column="RAMP_ID" property="rampId" />
        <result column="ISIGN" property="isign" />
        <result column="FIND_DSS_USER_NAME" property="findDssUserName" />
        <result column="TUNNEL_MOUTH" property="tunnelMouth" />
        <result column="START_HIGH" property="startHigh" />
        <result column="END_HIGH" property="endHigh" />
        <result column="START_STAKE_NUM" property="startStakeNum" />
        <result column="END_STAKE_NUM" property="endStakeNum" />
        <result column="STAKE_HIGH" property="stakeHigh" />
        <result column="LINING_STRUCTURE" property="liningStructure" />
        <result column="MARKING_LINE_POSITION" property="markingLinePosition" />
        <result column="MEANS" property="means" />
        <result column="FINISH_STAKE" property="finishStake" />
        <result column="LANE_DIRECTION" property="laneDirection" />
        <result column="YEAR" property="year" />
        <result column="UNIT_MARGE_ID" property="unitMargeId" />
        <result column="STRUCT_DESC" property="structDesc" />
        <result column="X_GD" property="xGd" />
        <result column="Y_GD" property="yGd" />
        <result column="DIR" property="dir" />
        <result column="ROUTECODE" property="routecode" />
        <result column="ROUTEVERSION" property="routeversion" />
        <result column="RL_STAKE_NEW" property="rlStakeNew" />
        <result column="LINEDIRECT" property="linedirect" />
        <result column="RL_FINISH_STAKE" property="rlFinishStake" />
        <result column="CRACKPOOR" property="crackpoor" />
        <result column="OPERT_ORG_ID" property="opertOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DSS_ID, DSS_CODE, RP_INTRVL_ID, STAKE, FACILITY_CAT, DSS_TYPE, DSS_DEGREE, MNTN_ADVICE, STRUCT_ID, STRUCT_TYPE, STRUCT_PART_ID, STRUCT_COMP_ID, LANE, DSS_POSITION, DSS_CAUSE, DSS_DESC, DSS_L, DSS_L_UNIT, DSS_W, DSS_W_UNIT, DSS_D, DSS_D_UNIT, DSS_N, DSS_N_UNIT, DSS_A, DSS_A_UNIT, DSS_V, DSS_V_UNIT, DSS_P, DSS_G, DSS_IMP_FLAG, DSS_QUALITY, HIS_DSS_ID, DSS_SOURCE, REL_TASK_CODE, FOUND_DATE, REPAIR_STATUS, REPAIR_DATE, DEAL_STATUS, DEAL_MEASURE, DEAL_OPINION, DEAL_PRSN, DEAL_DATE, DEAL_BILL_ID, X, Y, MTASK_ID, MTASK_ACCPT_ID, MAIN_ROAD_ID, RAMP_ID, ISIGN, FIND_DSS_USER_NAME, TUNNEL_MOUTH, START_HIGH, END_HIGH, START_STAKE_NUM, END_STAKE_NUM, STAKE_HIGH, LINING_STRUCTURE, MARKING_LINE_POSITION, MEANS, FINISH_STAKE, LANE_DIRECTION, YEAR, UNIT_MARGE_ID, STRUCT_DESC, X_GD, Y_GD, DIR, ROUTECODE, ROUTEVERSION, RL_STAKE_NEW, LINEDIRECT, RL_FINISH_STAKE, CRACKPOOR, OPERT_ORG_ID
    </sql>
    <select id="selectDssPage" resultType="com.hualu.app.module.mems.dss.dto.DssInfoDto">
        select * from (with tt as (select t.dss_id,
        t.rp_intrvl_id,
        t.dss_type,
        t.lane,
        t.dss_degree,
        t.dss_position,
        t.mtask_id,
        t.linedirect   as line_direct,
        t.main_road_id,
        t.ramp_id,
        t.stake,
        t.RL_STAKE_NEW,
        t.facility_cat,
        t.struct_id,
        t.struct_part_id,
        t.struct_comp_id,
        t.rl_stake_new as rl_stake,
        t.dss_source,
        t.dss_l,
        t.dss_l_unit,
        t.dss_w,
        t.dss_w_unit,
        t.dss_d,
        t.dss_d_unit,
        t.dss_n,
        t.dss_n_unit,
        t.dss_a,
        t.dss_a_unit,
        t.dss_v,
        t.dss_v_unit,
        t.dss_p,
        t.dss_g,
        t.found_date,
        t.find_dss_user_name,
        t.mntn_advice,
        t.dss_desc,
        t.dss_cause,
        t.deal_date,
        t.deal_status,
        t.deal_opinion,
        t.REPAIR_DATE,t.REPAIR_STATUS,
        t.routecode,
        t.close_type,
        t.org_id,
        dt.have_dss_colom,
        case when DSS_SOURCE = 3 and t.FACILITY_CAT = 'LM' then dtt.dss_type_name else dt.DSS_TYPE_NAME end as DSS_TYPE_NAME
        from MEMSDB.dss_info t
        left join MEMSDB.dss_type_new dt on dt.dss_type = t.dss_type
        left join PTCMSDB.PTCD_HPAS_SD_PAVEMENT dtt on t.DSS_TYPE = dtt.DSS_TYPE and dtt.MEANS = 0 and t.DSS_SOURCE = 3 and t.FACILITY_CAT = 'LM'
         ${ew.customSqlSegment} )
        select DISTINCT t2.*,
        l.line_code,
        l.line_allname                              as line_name,
        l.line_allname || '(' || l.line_code || ')' as line_allname
        from tt t2,
        GDGS.BASE_LINE l
        where t2.main_road_id = l.line_id
        and (t2.line_direct is null or t2.line_direct &lt;&gt;   '4')
        union all
        select DISTINCT t2.*,
        l.line_code,
        l.line_allname                              as line_name,
        l.line_allname || '(' || l.line_code || ')' as line_allname
        from tt t2,
        GDGS.BASE_RAMP_LINE l
        where t2.RAMP_ID = l.line_id
        and t2.line_direct = '4') v
        <choose>
            <when test="orderSql == null">
                ORDER BY v.routecode asc, v.main_road_id, v.line_direct, v.rl_stake_new DESC
            </when>
            <otherwise>
                ${orderSql}
            </otherwise>
        </choose>
    </select>
    <select id="selectByDinsp" resultType="com.hualu.app.module.mems.dss.dto.DssCheckInfo">
        select distinct tr.dss_id,t.DINSP_CODE as code_source, u.USER_NAME, t.SEARCH_DEPT
        from DM_DINSP t
         join DM_DINSP_RECORD tr on t.DINSP_ID = tr.DINSP_ID
         join gdgs.FW_RIGHT_USER u on t.CREATE_USER_ID = u.USER_CODE ${ew.customSqlSegment}
    </select>
    <select id="selectByFinsp" resultType="com.hualu.app.module.mems.dss.dto.DssCheckInfo">
        select distinct tr.dss_id,t.FINSP_CODE as code_source, t.INSP_PERSON as user_name, t.SEARCH_DEPT
        from DM_FINSP t join DM_FINSP_RECORD tr on t.FINSP_ID = tr.FINSP_ID ${ew.customSqlSegment}
    </select>
    <select id="selectByNotice" resultType="com.hualu.app.module.mems.dss.dto.DssCheckInfo">
        select distinct tr.dss_id,t.NOTICE_CODE as code_source, u.USER_NAME, t.MNTN_ORG_NM as search_dept
        from DM_NOTICE t
         join DM_NOTICE_DETAIL tr on t.NOTICE_ID = tr.NOTICE_ID
         join gdgs.FW_RIGHT_USER u on t.CREATE_USER_ID = u.USER_CODE ${ew.customSqlSegment}
    </select>
    <select id="selectDssWx" resultType="com.hualu.app.module.mems.dss.dto.DssWxDto">
        select a.DSS_ID,
               mp.MPITEM_NAME,
               a.MPITEM_ACCOUNT,
               b.ASKED_COMPL_DATE,
               a.MEASURE_UNIT,
               d.ACCEPT_USER,
               d.ACCEPT_DATE,
               d.ACCEPT_COMMENT
        from memsdb.DM_TASK_DETAIL a
                 join memsdb.DM_TASK b on a.MTASK_ID = b.MTASK_ID
                 join memsdb.DM_TASK_ACCPT_DETAIL c on a.MTASK_DTL_ID = c.MTASK_DTL_ID
                 join memsdb.DM_TASK_ACCPT d on c.MTASK_ACCPT_ID = d.MTASK_ACCPT_ID
                 join memsdb.MPC_MPITEM mp on a.MPITEM_ID = mp.MPITEM_ID
            ${ew.customSqlSegment}
    </select>
    <select id="selectDssRepairInfos" resultType="com.hualu.app.module.mems.dss.dto.DssRepairInfoDto">
        SELECT
            m.asked_compl_date as compl_date,dss.REPAIR_DATE,dss.REPAIR_STATUS
        FROM
            memsdb.DM_TASK m
                JOIN memsdb.DM_TASK_DETAIL d ON m.MTASK_ID = d.MTASK_ID
                join memsdb.DSS_INFO dss on d.DSS_ID = dss.dss_id
        WHERE
            m.MNT_ORG_ID in
        <foreach collection="orgIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
          AND TO_CHAR( m.asked_compl_date, 'yyyy-MM' ) = #{month}
    </select>

    <select id="selectMtaskCode" resultType="com.hualu.app.module.mems.dss.dto.DssInfoDto">
        select t.dss_id,
               dtk.MTASK_CODE,
               dac.MTASK_ACCPT_CODE
        from MEMSDB.dss_info t
            left join MEMSDB.DM_TASK dtk on dtk.MTASK_ID = t.MTASK_ID
            left join memsdb.DM_TASK_ACCPT_DETAIL dacd on  dacd.DSS_ID = t.DSS_ID
            left join memsdb.DM_TASK_ACCPT dac on dac.MTASK_ACCPT_ID = dacd.MTASK_ACCPT_ID
        where t.DSS_ID in
        <foreach collection="dssIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
            group by t.dss_id,dtk.MTASK_CODE,dac.MTASK_ACCPT_CODE
    </select>

    <select id="selectSlopeDm" resultType="com.hualu.app.module.mems.dss.dto.InfoForSlopeBase">
        <choose>
            <when test="type == 'XCD'">
                select DINSP_ID as id,PROCESSINSTID,DINSP_CODE as code,
                decode(a.STATUS,0,'未提交',1,'已提交',2,'审核中',3,'已审核',-1,'被退回','') as status,
                PATROLUSERNAME as check_person,
                to_char(INSP_DATE,'yyyy-MM-dd') as check_time,
                b.LINE_ALLNAME || '(' || b.LINE_CODE || ')' as line_name,
                c.ORG_FULLNAME as org_name
                from MEMSDB.DM_DINSP a
                inner join gdgs.BASE_LINE b on a.LINE_CODE = b.LINE_ID
                inner join gdgs.FW_RIGHT_ORG c on a.MNT_ORG_ID = c.ORG_CODE
                where exists (
                select 1 from MEMSDB.DM_DINSP_RECORD b
                where a.DINSP_ID = b.DINSP_ID
                and b.FACILITY_CAT = 'BP'
                and b.STRUCT_ID = #{slopeId}
                ) and TYPE = '1'
                order by INSP_DATE desc
            </when>
            <otherwise>
                select FINSP_ID as id,PROCESSINSTID,FINSP_CODE as code,
                decode(a.STATUS,0,'未提交',1,'已提交',2,'审核中',3,'已审核',-1,'被退回','') as status,
                INSP_PERSON as check_person,
                to_char(INSP_DATE,'yyyy-MM-dd') as check_time,
                b.LINE_ALLNAME || '(' || b.LINE_CODE || ')' as line_name,
                c.ORG_FULLNAME as org_name
                from MEMSDB.DM_FINSP a
                inner join gdgs.BASE_LINE b on a.LINE_CODE = b.LINE_ID
                inner join gdgs.FW_RIGHT_ORG c on a.MNT_ORG_ID = c.ORG_CODE
                where a.FACILITY_CAT = 'BP' and PROJECT_TYPE = 1
                and STRUCT_ID = #{slopeId}
                order by INSP_DATE desc
            </otherwise>
        </choose>
    </select>
</mapper>
