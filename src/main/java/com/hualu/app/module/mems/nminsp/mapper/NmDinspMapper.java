package com.hualu.app.module.mems.nminsp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.basedata.dto.NmDinspStsTreeDto;
import com.hualu.app.module.mems.comm.entity.BaseStruct;
import com.hualu.app.module.mems.nminsp.dto.NmDinspFacilityStat;
import com.hualu.app.module.mems.nminsp.dto.WorkNmDinspDto;
import com.hualu.app.module.mems.nminsp.entity.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新版日常巡查 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface NmDinspMapper extends BaseMapper<NmDinsp> {

    @Update("update MEMSDB.NM_DINSP set status=#{status} where processinstid = #{processInstId}")
    void updateStatus(@Param("processInstId") long processInstId, @Param("status") int status);

    @Update("UPDATE NM_DINSP d SET d.DSS_NUM = ( select count(1) from NM_DINSP_RECORD b where b.dinsp_id = d.dinsp_id and b.del_flag =0 ) where d.dinsp_id=#{dinspId}")
    void updateDssNum(@Param("dinspId") String dinspId);


    NmDinsp lastNmDinsp(@Param("facilityCat") String facilityCat, @Param("orgIdString") String orgIdString);

    List<BaseStruct> getBridgeList(@Param("orgCode") Object orgCode, @Param("structName") Object structName);

    List<BaseStruct> getCulvertList(@Param("orgCode") Object orgCode, @Param("structName") Object structName);

    List<NmDinspSituationShow> querySituation(@Param("orgCode") String orgCode, @Param("year") Integer year);

    List<NmDinspStsTreeDto> selectStsList(@Param("orgCode") String orgCode, @Param("cat") String cat, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("status") String status, @Param("dinspCode") String dinspCode, @Param("hasDssStatus") String hasDssStatus);

    int getBridgeNum(@Param("orgIds") String orgIds);

    int getTunnelNum(@Param("orgIds") String orgIds);

    int getSlopeNum(@Param("orgIds") String orgIds);

    List<NmDinspFacilityStat> getNmDinspFacilityStat(@Param(Constants.WRAPPER) LambdaQueryWrapper<NmDinsp> ew);

    List<RoadInspectionRecord> getDssInfoImageExport(@Param("dinspIds") List<String> dinspIds,
                                                     @Param("facilityCat") String orgId);

    List<WorkNmDinspDto> listWorkDinspByInspDate(@Param("inspDate") String inspDate);

    IPage<RouteInspection> DailyInspectionLedger(IPage page,
                                                 @Param("orgCode") String orgCode,
                                                 @Param("dinspCode") String dinspCode,
                                                 @Param("facilityCat") String facilityCat,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("startStake") Double startStake,
                                                 @Param("endStake") Double endStake,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate,
                                                 @Param("dssTypeName") String dssTypeName,
                                                 @Param("facilityCategory") String facilityCategory,
                                                 @Param("lineDirect") String lineDirect,
                                                 @Param("routeName") String routeName);
    List<MtaskEntity> findDssInfoId(
                                    @Param("dssIds") List<String> dssIds,
                                    @Param("mtaskCode") String mtaskCode,
                                    @Param("mtaskAccptCode") String mtaskAccptCode
                                    );

    IPage<SlopeInspectionRecord> findBpDinspRecord(IPage page,
                                                   @Param("orgCode") String orgCode,
                                                   @Param("dinspCode") String dinspCode,
                                                   @Param("status") String status,
                                                   @Param("lineCode") String lineCode,
                                                   @Param("hasDssStatus") String hasDssStatus,
                                                   @Param("startDate") String startDate,
                                                   @Param("endDate") String endDate);

    List<SumInspectionRecord> findDinspCheckSum(@Param("orgCode") String orgCode,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);
}
