package com.hualu.app.module.mems.autoInspector.utils;

import com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo;
import com.hualu.app.module.mems.autoInspector.entity.HighwaySegment;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.utils.mems.StringUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;

/**
 * 病害信息转巡查记录转换工具
 * <p>
 * 用于将自动巡检系统中的病害信息（HighwayDefectInfo）
 * 转换为新版经常巡查系统中的巡查记录（NmFinsp）
 * </p>
 * 
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
public class DefectToFinspConverter {
    
    /**
     * 将HighwayDefectInfo全面映射到NmFinsp对象用于传参，可以使用路段信息
     * @param defectInfo 原始病害信息
     * @param segmentCache 路段缓存
     * @return 填充了相关病害数据的NmFinsp对象
     */
    public static NmFinsp convertToNmFinsp(HighwayDefectInfo defectInfo, Map<String, HighwaySegment> segmentCache) {
        if (defectInfo == null) {
            return null;
        }
        
        NmFinsp nmFinsp = new NmFinsp();
        String segmentId = defectInfo.getSegmentId();
        
        // ===== 路线和行车方向信息 =====
        String direction = defectInfo.getInspectDirection();
        if (direction != null) {
            nmFinsp.setLineDirect("1".equals(direction) ? "2" : "0".equals(direction) ? "1" : direction);
        }
        
        // 从路段名称提取路线信息
        if (defectInfo.getSegmentName() != null) {
            String segmentName = defectInfo.getSegmentName();
            // 尝试提取路线名称和编号（根据实际命名规则调整）
            if (segmentName.matches(".*[GS]\\d+.*")) {                   // 国道/省道编号识别
                // 例如从"G15沈海高速温岭段"提取"G15"作为路线编码，"G15沈海高速"作为路线名称
                Matcher matcher = Pattern.compile("([GS]\\d+)").matcher(segmentName);
                if (matcher.find()) {
                    String lineCode = matcher.group(1);                  // 例如"G15"
                    nmFinsp.setLineCode(lineCode);
                }
            } else {
                // 没有明确编码，直接使用段名作为路线名
                nmFinsp.setLineName(segmentName);
            }
        }
        
        // 如果提供了路段缓存，可以使用其中的路段数据进行补充
        if (segmentCache != null && segmentId != null && !segmentId.trim().isEmpty()) {
            HighwaySegment segment = segmentCache.get(segmentId);
            if (segment != null) {
                // 可以从segment中获取更多数据进行设置
                log.debug("使用路段[{}]的数据补充巡查记录信息", segmentId);
            } else {
                log.warn("未找到路段ID[{}]对应的路段信息", segmentId);
            }
        } else if (segmentCache == null) {
            log.warn("路段缓存为空，无法获取路段信息");
        }
        
        nmFinsp.setFacilityCat("LM");                                // 默认路面
        
        // ===== 时间信息映射 =====
        if (defectInfo.getCreatedTime() != null) {
            // 将LocalDateTime转换为Date
            Date inspDate = Date.from(defectInfo.getCreatedTime().atZone(ZoneId.systemDefault()).toInstant());
            nmFinsp.setInspDate(inspDate);
            
            // 设置巡查时间（格式化为时分秒）
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
            nmFinsp.setInspTime(defectInfo.getCreatedTime().format(timeFormatter));
            
            // 设置创建时间
            nmFinsp.setCreateTime(defectInfo.getCreatedTime());
        }
        
        if (defectInfo.getUpdatedTime() != null) {
            nmFinsp.setUpdateTime(defectInfo.getUpdatedTime());
        }
        
        // ===== 病害信息映射 =====
        // 病害数量默认为1（一个病害信息对应一个巡查记录）
        nmFinsp.setDssNum(1);

        // ===== 特殊和额外信息 =====
        // 天气信息（需要从其他来源获取或默认）
        nmFinsp.setWeather("01");                                        // 默认天气
        nmFinsp.setWeatherName("晴");                                    // 天气名称

        // 设置默认删除标志
        nmFinsp.setDelFlag(0);                                          // 未删除

        return nmFinsp;
    }
} 