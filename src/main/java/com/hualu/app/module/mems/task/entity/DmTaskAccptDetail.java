package com.hualu.app.module.mems.task.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 验收单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DmTaskAccptDetail对象", description="验收单明细")
public class DmTaskAccptDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "验收单明细ID")
    @TableId("MTASK_ACCPT_DTL_ID")
    private String mtaskAccptDtlId;

    @ApiModelProperty(value = "维修业务类型  1 计划性维修保养 2 突发性维修保养")
    @TableField("BUSINESS_TYPE")
    private Integer businessType;

    @ApiModelProperty(value = "对应验收单ID")
    @TableField("MTASK_ACCPT_ID")
    private String mtaskAccptId;

    @ApiModelProperty(value = "任务单明细ID")
    @TableField("MTASK_DTL_ID")
    private String mtaskDtlId;

    @NotNull
    @ApiModelProperty(value = "病害id")
    @TableField("DSS_ID")
    private String dssId;

    @ApiModelProperty(value = "路段区间id")
    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @ApiModelProperty(value = "桩号")
    @TableField("STAKE")
    private Double stake;

    @ApiModelProperty(value = "车道位置")
    @TableField("LANE")
    private String lane;

    @NotNull
    @ApiModelProperty(value = "养护工程细目")
    @TableField("MPITEM_ID")
    private String mpitemId;

    @NotNull
    @ApiModelProperty(value = "计量单位")
    @TableField("MEASURE_UNIT")
    private String measureUnit;

    @ApiModelProperty(value = "是否包干")
    @TableField("IS_LUMP")
    private Integer isLump;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "申请验收数量")
    @TableField("APPLY_AMOUNT")
    private Double applyAmount;

    @ApiModelProperty(value = "验收数量")
    @TableField("ACCEPT_AMOUNT")
    private Double acceptAmount;

    @NotNull
    @TableField("REPAIR_DATE")
    private LocalDateTime repairDate;

    @ApiModelProperty(value = "验收状态")
    @TableField("ACCEPT_STATUS")
    private Integer acceptStatus;

    @ApiModelProperty(value = "工程数量(精度不够替换掉)")
    @TableField("MPITEM_NUM_BAK")
    private Double mpitemNumBak;

    @TableField("MAIN_ROAD_ID")
    private String mainRoadId;

    @TableField("IS_TASK_RECORD")
    private Integer isTaskRecord;

    @TableField("RAMP_ID")
    private String rampId;

    @ApiModelProperty(value = "日常养护合同ID")
    @TableField("CONTR_ID")
    private String contrId;

    @ApiModelProperty(value = "维修工程记录编码")
    @TableField("MTASK_ACCPT_DTL_CODE")
    private String mtaskAccptDtlCode;

    @ApiModelProperty(value = "来源任务单的ID")
    @TableField("MTASK_ID")
    private String mtaskId;

    @ApiModelProperty(value = "路线方向")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "逻辑桩号")
    @TableField("LOGIC_STAKE_NUM")
    private Double logicStakeNum;

    @ApiModelProperty(value = "逻辑桩号显示")
    @TableField("RL_STAKE")
    private String rlStake;

    @ApiModelProperty(value = "工程数量")
    @TableField("MPITEM_NUM")
    private Double mpitemNum;



}
