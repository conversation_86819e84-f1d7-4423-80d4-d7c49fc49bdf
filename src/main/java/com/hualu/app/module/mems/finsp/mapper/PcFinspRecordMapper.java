package com.hualu.app.module.mems.finsp.mapper;

import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspDealDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspRecordExcelDto;
import com.hualu.app.module.mems.finsp.dto.PcFinspStatDto;
import com.hualu.app.module.mems.finsp.entity.PcFinspRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 结构物排查记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface PcFinspRecordMapper extends BaseMapper<PcFinspRecord> {

    List<PcFinspRecordExcelDto> listByMonths(@Param("orgCodes") List<String> orgCodes,@Param("months") List<String> months,@Param("prjIds") List<String> prjIds);

    List<PcFinspRecord> listRecordByStructId(@Param("structId")String structId,@Param("dealStatus") String dealStatus,@Param("months") List<String> months);

    List<PcFinspStatDto> getFinspStat(@Param("orgCodes") List<String> orgCodes,@Param("months") List<String> months,@Param("mntType") String mntType,@Param("prjIds") List<String> prjIds);

    List<PcFinspStatDto> getFinspStatByDealType(@Param("orgCodes") List<String> orgCodes,@Param("mntType") String mntType,@Param("prjIds") List<String> prjIds);

    List<PcFinspDealDto> getGeometryDeal(@Param("prjIds") List<String> prjIds,@Param("paramDto")  GeometryParamDto paramDto);
}
