<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwaySegmentMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwaySegment">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="segname" column="SEGNAME" jdbcType="VARCHAR"/>
            <result property="stakestart" column="STAKESTART" jdbcType="VARCHAR"/>
            <result property="stakeend" column="STAKEEND" jdbcType="VARCHAR"/>
            <result property="roadtype" column="ROADTYPE" jdbcType="VARCHAR"/>
            <result property="mileage" column="MILEAGE" jdbcType="DECIMAL"/>
            <result property="lanecount" column="LANECOUNT" jdbcType="DECIMAL"/>
            <result property="mark" column="MARK" jdbcType="VARCHAR"/>
            <result property="city" column="CITY" jdbcType="VARCHAR"/>
            <result property="district" column="DISTRICT" jdbcType="VARCHAR"/>
            <result property="curinglevel" column="CURINGLEVEL" jdbcType="VARCHAR"/>
            <result property="direction" column="DIRECTION" jdbcType="VARCHAR"/>
            <result property="maxspeedlimit" column="MAXSPEEDLIMIT" jdbcType="DECIMAL"/>
            <result property="segcode" column="SEGCODE" jdbcType="VARCHAR"/>
            <result property="createdtime" column="CREATEDTIME" jdbcType="TIMESTAMP"/>
            <result property="updatedtime" column="UPDATEDTIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SEGNAME,STAKESTART,
        STAKEEND,ROADTYPE,MILEAGE,
        LANECOUNT,MARK,CITY,
        DISTRICT,CURINGLEVEL,DIRECTION,
        MAXSPEEDLIMIT,SEGCODE,CREATEDTIME,
        UPDATEDTIME
    </sql>
</mapper>
