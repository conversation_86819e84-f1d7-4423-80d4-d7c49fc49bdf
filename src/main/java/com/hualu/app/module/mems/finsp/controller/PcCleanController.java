package com.hualu.app.module.mems.finsp.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.service.PcFinspImageService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.excelimport.util.H_ValidatorHelper;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import org.assertj.core.util.Lists;
import com.hualu.app.module.mems.finsp.entity.PcClean;
import com.hualu.app.module.mems.finsp.mapper.PcCleanMapper;
import com.hualu.app.module.mems.finsp.service.PcCleanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 打草清疏 前端控制器
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Api(tags = "PcCleanController",description = "打草清疏 前端控制器")
@RestController
@RequestMapping("/pcClean")
public class PcCleanController extends M_MyBatisController<PcClean,PcCleanMapper>{

    @Autowired
    private PcCleanService service;

    @Autowired
    private PcFinspImageService imageService;

    /**
    * 分页查询
    * @return
    */
    @ApiRegister(value = "pcClean:page",businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    @Override
    public Object selectPage() {
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        queryWrapper.eq("MNT_ORG_ID", CustomRequestContextHolder.getOrgIdString());
        queryWrapper.orderByDesc("create_time");
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        return this.pageService.returnPageResult(iPage);
    }

    /**
     * 获取主单详情
     * @param cleanId
     * @return
     */
    @GetMapping("/getById/{cleanId}")
    public RestResult<PcClean> getById(@PathVariable("cleanId") String cleanId){
        PcClean pcFinsp = service.getById(cleanId);
        Map<String, List<PcFinspImage>> imageMap = imageService.selectByRecordIds(Lists.newArrayList(pcFinsp.getCleanId()));
        if (CollectionUtil.isNotEmpty(imageMap)){
            List<PcFinspImage> images = imageMap.get(pcFinsp.getCleanId());
            pcFinsp.setFinspImages(images);
        }
        return RestResult.success(pcFinsp);
    }

    /**
    * 添加或者修改
     * @param data
     * @param beforeFiles 清理前照片
     * @param afterFiles 清理后照片
    * @return
    */
    @ApiRegister(value = "pcClean:saveOrUpdate",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改")
    @PostMapping("saveOrUpdate")
    public RestResult<PcClean> saveOrUpdate(@RequestParam("data") String data
            , @RequestParam(value = "beforeFiles",required = false) MultipartFile[] beforeFiles
            , @RequestParam(value = "afterFiles",required = false) MultipartFile[] afterFiles) {
        PcClean bean = JSONUtil.toBean(data, PcClean.class);
        H_ValidatorHelper.validator2Exception(data,null);
        service.saveOrUpdateClean(bean,beforeFiles,afterFiles);
        return RestResult.success(bean);
    }

    /**
    * 删除
    */
    @ApiRegister(value = "pcClean:delIds",businessType = BusinessType.DELETE)
    @ApiOperation("删除（ids为字符串，以逗号分割）")
    @PostMapping("delIds")
    public RestResult<String> delIds(@RequestParam(value = "ids")String ids){
        service.removeClean(StrUtil.split(ids,","));
        return RestResult.success("操作成功");
    }
}