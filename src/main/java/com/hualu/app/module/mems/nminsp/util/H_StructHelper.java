package com.hualu.app.module.mems.nminsp.util;

import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.service.struct.IStruct;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;

import java.util.List;

public class H_StructHelper {

    /**
     * 获取设施分类结构物
     * @param facilityCat
     * @return
     */
    public static IStruct getStructBean(String facilityCat){
        if (StrUtil.isBlank(facilityCat)){
            throw new BaseException("facilityCat为空");
        }
        String beanName = facilityCat.toLowerCase()+"StructImp";
        IStruct bean = CustomApplicationContextHolder.getBean(beanName, IStruct.class);
        return bean;
    }

    /**
     * 更新检查结论
     * @param dinsp
     */
    public static void refreshNmDinspResult(NmDinsp dinsp){
        IStruct bean = getStructBean(dinsp.getFacilityCat());
        bean.refreshNmDinspResult(dinsp);
    }

    /**
     * 校验日常巡查记录参数是否合规
     * @param entity
     */
    public static void validateNmDinspRecord(NmDinspRecord entity) {
        getStructBean(entity.getFacilityCat()).validateNmDinspRecord(entity);
    }

    /**
     * 各设施回显病害数据
     * @param facilityCat
     * @param records
     */
    public static void showNmDinspView(String facilityCat,List<NmDinspRecord> records){
        getStructBean(facilityCat).showNmDinspView(records);
    }

    public static void validateNmFinspRecord(NmFinspRecord entity) {
        getStructBean(entity.getFacilityCat()).validateNmFinspRecord(entity);
    }

    public static void refreshNmFinspResult(NmFinsp entity) {
        getStructBean(entity.getFacilityCat()).refreshNmFinspResult(entity);
    }

    public static void showNmFinspView(String facilityCat, List<NmFinspRecord> records) {
        getStructBean(facilityCat).showNmFinspView(records);
    }
}
