package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspRecordWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcFinspResult;

import java.util.List;

/**
 * <p>
 * 结构物排查结论表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
public interface PcFinspResultService extends IService<PcFinspResult> {

    /**
     * 根据检查单，生成检查结论
     * @param finspId
     * @param facilityCat
     */
    void createFinspResult(String finspId,String facilityCat);

    /**
     * 修改检查结论
     * @param pcFinsp
     */
    void updateFinspResult(PcFinsp pcFinsp);

    void removeByFinspIds(List<String> finspIds);

    List<PcFinspRecordWordDto> getRecordWord(String finspId);

    List<PcFinspRecordWordDto> listRecordWord(List<String> finspIds,String prjId);
}
