package com.hualu.app.module.mems.nminsp.service.struct;

import cn.hutool.core.collection.CollectionUtil;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.mems.nminsp.entity.*;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import org.assertj.core.util.Sets;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public interface IStruct {

    default void beforeCreateEmptyDinsp(NmDinspCommon dinspCommon,String facilityCat){
        NmDinspService nmDinspService = CustomApplicationContextHolder.getBean(NmDinspService.class);
        List<NmDinsp> nmDinsps = nmDinspService.listStructId(dinspCommon,facilityCat);
        IBaseStructFace structFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class)
                .values().stream().filter(e -> e.getFacilityCat().equals(facilityCat))
                .findFirst().orElse(null);
        if (structFace == null) {
            return;
        }
        // 获取已经新建巡查单的结构物ID
        Set<String> structIds = CollectionUtil.isEmpty(nmDinsps)? Sets.newHashSet():nmDinsps.stream().map(NmDinsp::getStructId).collect(Collectors.toSet());
        List<BaseStructDto> baseStructDtos = structFace.listByRouteCodes(H_DataAuthHelper.selectRouteCodeAuth());
        if (CollectionUtil.isEmpty(baseStructDtos)) {
            // 未新建的结构物
            List<BaseStructDto> noCreateStructDtos = baseStructDtos.stream().filter(e -> !structIds.contains(e.getStructId())).collect(Collectors.toList());
            // 自动去创建单

        }
    }
    /**
     * 加工导出表数据
     * @param nmDinsps
     */
    default List<String> processAndExportNmDinspWord(List<NmDinsp> nmDinsps,String parentPath) {return null;}

    default List<String> processAndExportNmFinspWord(List<NmFinsp> nmFinsps,String parentPath) {return null;}

    /**
     * 初始化日常巡查单信息
     * @param nmDinsp
     * @return
     */
    default void validateNmDinsp(NmDinsp nmDinsp){};

    default void validateNmFinsp(NmFinsp nmFinsp){};

    /**
     * 验证日常巡查记录
     * @param nmDinspRecord
     * @return
     */
    default void validateNmDinspRecord(NmDinspRecord nmDinspRecord){};

    default void validateNmFinspRecord(NmFinspRecord nmFinspRecord){};

    /**
     * 生成检查结论
     * @param nmDinsp
     */
    default void refreshNmDinspResult(NmDinsp nmDinsp){};

    default void refreshNmFinspResult(NmFinsp nmFinsp){};

    /**
     * 回显前端界面值
     * @param nmDinspRecords
     */
    default void showNmDinspView(List<NmDinspRecord> nmDinspRecords){};

    default void showNmFinspView(List<NmFinspRecord> nmFinspRecords){};

    default BaseStructDto getStructInfo(String facilityCat,String structId){
        IBaseStructFace iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        if (iBaseStructFace == null){
            throw new BaseException(facilityCat+"无对应的实现类");
        }
        BaseStructDto structDto = iBaseStructFace.getId(structId);
        if (structDto == null){
            throw new BaseException("结构物不存在");
        }
        return structDto;
    }

    default List<BaseStructDto> getStructInfos(String facilityCat,List<String> structIds){
        IBaseStructFace iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        if (iBaseStructFace == null){
            throw new BaseException(facilityCat+"无对应的实现类");
        }
        List<BaseStructDto> structDto = iBaseStructFace.listByStructIds(structIds);
        if (structDto == null){
            throw new BaseException("结构物不存在");
        }
        return structDto;
    }

    default IFacBase getFacBase(String facilityCat){
        IFacBase iBaseStructFace = CustomApplicationContextHolder.getBeansOfType(IFacBase.class).values().stream()
            .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        if (iBaseStructFace == null){
            throw new BaseException(facilityCat+"无对应的实现类");
        }
        return iBaseStructFace;
    }
}
