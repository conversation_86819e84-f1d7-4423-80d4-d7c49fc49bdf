package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.finsp.dto.word.PcFinspWordDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 结构物排查表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface PcFinspService extends IService<PcFinsp> {

    List<PcFinsp> listByQuarter(String structId, String facilityCat, Date date,String mntType);

    /**
     * 专项一个年份只生成一单
     * @param structId
     * @param facilityCat
     * @param date
     * @return
     */
    String getFinspId(String structId, String facilityCat, Date date,String mntType);

    String getNextCode(String facilityCat, String orgEn);

    void updateDssNum(String finspId);

    void saveOrUpdateFinsp(PcFinsp finsp);

    void delPcFinsp(String finspId);

    PcFinspWordDto getFinspWord(String finspId);

    List<PcFinspWordDto> listFinspWord(List<String> finspIds,String prjId);

    String exportWordByZip(String finspIds, String prjId, String facilityCat);

    void removeByStructIds(List<String> structIds);

    void removeByPrjId(String prjId);

    /**
     * 导出单个word
     * @param finspId
     * @param qlType
     * @return
     */
    String exportWord(String finspId, String qlType);

    /**
     * 查询导出主单信息
     * @param finspIds
     * @param prjId
     * @return
     */
    List<PcProjectExportDto> exportWordData(List<String> finspIds, String prjId);

    /**
     * 获取病害数量
     * @param structIds
     * @return
     */
    Map<String,PcFinsp> getDssNum(List<String> structIds);
}
