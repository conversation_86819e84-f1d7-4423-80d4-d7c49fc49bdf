package com.hualu.app.module.mems.autoInspector.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
* 巡检用户配置表
*/
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("HIGHWAY_USER_CONFIG")
@ApiModel(value="HighwayUserConfig对象", description="巡检用户配置表")
public class HighwayUserConfig implements Serializable {

    
    @NotNull(message="[配置ID]不能为空")
    @ApiModelProperty("配置ID")
    @TableField("CONFIG_ID")
    private Long configId;
    
    @NotBlank(message="[巡查路段ID]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("巡查路段ID")
    @TableField("ROUTE_ID")
    private String routeId;
    
    @NotBlank(message="[用户ID]不能为空")
    @Size(max= 50,message="编码长度不能超过50")
    @ApiModelProperty("用户ID")
    @TableField("USER_ID")
    private String userId;
    
    @NotBlank(message="[用户编码]不能为空")
    @Size(max= 60,message="编码长度不能超过60")
    @ApiModelProperty("用户编码")
    @TableField("USER_CODE")
    private String userCode;
    
    @Size(max= 100,message="编码长度不能超过100")
    @ApiModelProperty("部门名称")
    @TableField("DEPART_NAME")
    private String departName;
    
    @NotNull(message="[巡检类型(1:经常性检查 2:经常检查)]不能为空")
    @ApiModelProperty("巡检类型(1:经常性检查 2:经常检查)")
    @TableField("INSP_TYPE")
    private Integer inspType;
    
    @Size(max= 60,message="编码长度不能超过60")
    @ApiModelProperty("用户名")
    @TableField("USER_NAME")
    private String userName;
}
