package com.hualu.app.module.mems.sign;

import com.alibaba.fastjson.JSONObject;
import com.tg.dev.api.context.CustomRequestContextHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.ResponseBody;
import org.springframework.web.bind.annotation.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Api("签章相关")
@RestController
@RequestMapping("/sign")
public class SignController {

    private static final String AUTH_URL = "http://172.29.0.20:7001/highwayAppSign/signature/isAuth";
    private static final String FILE_INFOS = "http://172.29.0.20:7001/highwayAppSign/signature/signInfos";

    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    @ApiOperation("签章权限")
    @PostMapping("/isAuth")
    public Object checkAuth() throws Exception {

        okhttp3.RequestBody body = new FormBody.Builder().build();

        Request request = new Request.Builder()
                .url(AUTH_URL)
                .addHeader("userCode", CustomRequestContextHolder.getUserCode())
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("请求失败，HTTP状态码: " + response.code());
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new Exception("响应体为空");
            }

            String jsonData = responseBody.string();
            return JSONObject.parseObject(jsonData);
        }
    }

    @ApiOperation("签章文件")
    @PostMapping("/fileInfos")
    public Object fileInfos(String processInstID) throws Exception {
        FormBody.Builder formBuilder = new FormBody.Builder()
                .add("processInstID", processInstID);
        okhttp3.RequestBody body = formBuilder.build();

        Request request = new Request.Builder()
                .url(FILE_INFOS)
                .addHeader("userCode", CustomRequestContextHolder.getUserCode())
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("请求失败，HTTP状态码: " + response.code());
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new Exception("响应体为空");
            }

            String jsonData = responseBody.string();
            return JSONObject.parseObject(jsonData);
        }
    }
}
