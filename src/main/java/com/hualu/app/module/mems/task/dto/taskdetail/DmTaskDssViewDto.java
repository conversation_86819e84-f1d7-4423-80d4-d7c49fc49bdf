package com.hualu.app.module.mems.task.dto.taskdetail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DmTaskDssViewDto implements Serializable {

    //合同ID
    private String contrId;
    //病害ID
    private String dssId;
    //措施数量
    private Integer mpNum;
    //措施总额
    private Double money;
    //病害类型
    private String dssType;
    //病害类型名称
    private String dssTypeName;

    @JsonIgnore
    //路线方向
    private String linedirect;

    //路线车道方向
    private String lineDirectName;

    @JsonIgnore
    //路线编码
    private String mainRoadId;

    //路线名称
    private String lineName;

    //中文桩号
    private String stakeName;

    @JsonIgnore
    //桩号
    private Double rlStakeNew;
    @JsonIgnore
    //止点桩号
    private Double rlFinshStake;
    @JsonIgnore
    //设施类型
    private String facilityCat;
    //设施类型名称
    private String facilityCatName;
    @JsonIgnore
    //车道
    private String lane;
    @JsonIgnore
    private String laneName;

    @JsonIgnore
    //结构物ID
    private String structId;
    @JsonIgnore
    //结构物名称
    private String structName;

    //位置信息：车道|结构物
    private String positionName;

    //维修前照片
    private String fileIds;
    //维修后照片
    private String afterFileIds;

    //照片访问前缀地址
    private String imageHost;

    //病害养护措施
    List<DmTaskMpItemViewDto> mpItemViewDtos;

    //验收单编码
    private String mtaskAccptCode;

    //验收单ID
    private String mtaskAccptId;
}
