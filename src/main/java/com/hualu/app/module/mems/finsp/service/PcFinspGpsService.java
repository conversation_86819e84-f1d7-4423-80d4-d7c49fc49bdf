package com.hualu.app.module.mems.finsp.service;

import com.hualu.app.module.mems.finsp.entity.PcFinspGps;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 结构物排查轨迹表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
public interface PcFinspGpsService extends IService<PcFinspGps> {

    void removeByFinspIds(List<String> finspIds);

    PcFinspGps getXyByFinspId(String finspId);
}
