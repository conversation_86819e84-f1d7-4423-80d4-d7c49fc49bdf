<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.DmFinspMapper">

    <select id="getLast" resultType="com.hualu.app.module.mems.finsp.entity.DmFinsp">
        select * from (select * from dm_finsp ${ew.customSqlSegment}) where rownum &lt;  2
    </select>
    <select id="getFinVersionBy202305" resultType="com.hualu.app.module.mems.finsp.entity.DmFinsp">
        select d.FINSP_ID, d.FINSP_CODE
        from memsdb.DM_FINSP_RESULT r
                 join memsdb.DM_FINSP d
                      on r.FINSP_ID = d.FINSP_ID and d.MNT_ORG_ID = #{mntOrgId} and
                         d.FACILITY_CAT = 'BP' and r.finsp_item_id = '1661172250033786882'
        GROUP BY d.FINSP_ID, d.FINSP_CODE
        order by FINSP_CODE
    </select>
    <select id="selectRunning" resultType="com.hualu.app.module.mems.finsp.dto.DmFinspRunningDto">
        select df.FINSP_ID,df.STRUCT_ID,df.INSP_PERSON,df.STRUCT_NAME,df.LINE_CODE,df.MNTN_ORG_NM,df.MNT_ORG_ID
        from memsdb.DM_FINSP_GPS gps join memsdb.DM_FINSP df on gps.FINSP_ID = df.FINSP_ID
        where to_char(time, 'yyyy-MM-dd') = #{currentDay}
        and time >= to_date(#{minDate}, 'yyyy-MM-dd HH24:MI:SS')
        and time &lt;= to_date(#{now}, 'yyyy-MM-dd HH24:MI:SS')
        group by df.FINSP_ID,df.STRUCT_ID,df.INSP_PERSON,df.STRUCT_NAME,df.LINE_CODE,df.MNTN_ORG_NM,df.MNT_ORG_ID
    </select>
    <select id="countGps" resultType="com.hualu.app.module.mems.finsp.dto.DmFinspGpsCountDto">
        select
        (select count(1) from hsmsdb.HSMS_SLOPE_INFO m where m.IS_DELETED = 0
        and m.ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) as struct_num,
        (
        select count(1)
        from hsmsdb.HSMS_SLOPE_INFO m
        where m.IS_DELETED = 0
        and m.ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and exists (select 1 from memsdb.DM_FINSP d where d.FACILITY_CAT = 'BP' and d.STRUCT_ID = m.SLOPE_ID
        AND TO_CHAR(d.INSP_DATE, 'yyyy-MM') IN
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        )
        and m.SLOPE_ID in (select b.SLOPE_ID from memsdb.SLOPE_REGULAR_SOURCE_TRACK b) ) as source_gps_num,
        (
        select count(1)
        from hsmsdb.HSMS_SLOPE_INFO m
        where m.IS_DELETED = 0
        and m.ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and exists
        ( select 1 from (select b.STRUCT_ID from memsdb.DM_FINSP_GPS a join memsdb.DM_FINSP b
        on a.FINSP_ID = b.FINSP_ID and b.FACILITY_CAT = 'BP'
        AND TO_CHAR(b.INSP_DATE, 'yyyy-MM') IN
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) c where c.STRUCT_ID = m.SLOPE_ID) ) as xc_gps_num from dual

    </select>
    <select id="selectDssRepairPage" resultType="com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairDto">

        select a.STRUCT_NAME,decode(a.FACILITY_CAT,'QL','桥梁','HD','涵洞','SD','隧道','BP','边坡') as facility_cat_name
               ,a.FINSP_CODE,b.DSS_DESC,a.INSP_DATE,o.ORG_NAME,o.ORG_CODE,
        (select v3.MTASK_CODE from (select v1.MTASK_CODE,v2.DSS_ID from memsdb.DM_TASK v1 join memsdb.DM_TASK_DETAIL v2 on v1.MTASK_ID = v2.MTASK_ID) v3
        where v3.DSS_ID = b.DSS_ID and rownum = 1) as mtask_code,
        (select x3.MTASK_ACCPT_CODE from (select x1.MTASK_ACCPT_CODE,x2.DSS_ID from memsdb.DM_TASK_ACCPT x1 join memsdb.DM_TASK_ACCPT_DETAIL x2 on x1.MTASK_ACCPT_ID = x2.MTASK_ACCPT_ID) x3
        where x3.DSS_ID = b.DSS_ID and rownum = 1) as mtask_accpt_code
        from memsdb.DM_FINSP a join memsdb.DM_FINSP_RECORD b
        on a.FINSP_ID = b.FINSP_ID and a.MNT_ORG_ID in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and  to_char(a.INSP_DATE,'yyyy-MM') = #{month}
        and a.FACILITY_CAT in ('QL','HD','BP','SD') and a.PROJECT_TYPE = 1 and a.STATUS = 3
        join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.ORG_CODE
        where exists ( select 1 from memsdb.DSS_INFO dss where dss.DSS_ID = b.DSS_ID and dss.DSS_SOURCE = 2)
        order by a.INSP_DATE
    </select>
    <select id="selectDssNum" resultType="com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairStatDto">

        select o.ORG_NAME,a.FACILITY_CAT,decode(a.FACILITY_CAT,'QL','桥梁','HD','涵洞','SD','隧道','BP','边坡') as facility_cat_name,count(1) as dss_num
        from memsdb.DM_FINSP a join memsdb.DM_FINSP_RECORD b on a.FINSP_ID = b.FINSP_ID and a.status = 3
            and a.FACILITY_CAT in ('QL','HD','BP','SD')
            and a.MNT_ORG_ID in
            <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            and to_char(a.INSP_DATE,'yyyy-MM') = #{month}
            join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.ORG_CODE
        group by o.ORG_NAME,a.FACILITY_CAT
    </select>
    <select id="selectDssRepairNum" resultType="com.hualu.app.module.mems.finsp.dto.DmFinspDssRepairStatDto">
        select o.ORG_NAME,a.FACILITY_CAT,decode(a.FACILITY_CAT,'QL','桥梁','HD','涵洞','SD','隧道','BP','边坡') as facility_cat_name,count(1) as repair_num
        from memsdb.DM_FINSP a join memsdb.DM_FINSP_RECORD b on a.FINSP_ID = b.FINSP_ID and a.status = 3
            and a.FACILITY_CAT in ('QL','HD','BP','SD')
            and a.MNT_ORG_ID in
            <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            and to_char(a.INSP_DATE,'yyyy-MM') = #{month}
            and exists( select 1 from memsdb.DM_TASK_DETAIL t where t.DSS_ID = b.DSS_ID)
            join gdgs.FW_RIGHT_ORG o on a.MNT_ORG_ID = o.ORG_CODE
        group by o.ORG_NAME,a.FACILITY_CAT
    </select>
    <select id="listByCycle" resultType="com.hualu.app.module.mems.finsp.entity.DmFinsp">
        select * from memsdb.DM_FINSP where STRUCT_ID = #{structId} and FACILITY_CAT = #{facilityCat} and to_char(INSP_DATE,'yyyy-MM') in
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by INSP_DATE desc
    </select>

</mapper>
