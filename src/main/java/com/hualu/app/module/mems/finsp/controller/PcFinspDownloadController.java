package com.hualu.app.module.mems.finsp.controller;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.entity.PcFinspDownload;
import com.hualu.app.module.mems.finsp.mapper.PcFinspDownloadMapper;
import com.hualu.app.module.mems.finsp.service.PcFinspDownloadService;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结构物排查数据下载表 前端控制器
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Api(tags = "PcFinspDownloadController",description = "结构物排查数据下载表 前端控制器")
@RestController
@RequestMapping("/pcFinspDownload")
public class PcFinspDownloadController extends M_MyBatisController<PcFinspDownload,PcFinspDownloadMapper>{

    @Autowired
    private PcFinspDownloadService service;

    /**
    * 分页查询
    * @return
    */
    @ApiRegister(value = "pcFinspDownload:page",businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    @Override
    public Object selectPage() {
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        queryWrapper.eq("MNT_ORG_ID",CustomRequestContextHolder.getOrgIdString());
        queryWrapper.orderByDesc("create_time");
        IPage iPage = this.baseMapper.selectPage(this.getPage(), queryWrapper);
        return this.pageService.returnPageResult(iPage);
    }


    /**
    * 删除，ids为空时，清除全部
    */
    @ApiRegister(value = "pcFinspDownload:delIds",businessType = BusinessType.DELETE)
    @ApiOperation("删除（ids为字符串，以逗号分割）")
    @PostMapping("delIds")
    public RestResult<String> delIds(@RequestParam(value = "ids")String ids){
        if (StrUtil.isNotBlank(ids)){
            service.removeByIds(StrUtil.split(ids,","));
        }else {
            LambdaQueryWrapper<PcFinspDownload> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PcFinspDownload::getMntOrgId, CustomRequestContextHolder.getOrgIdString());
            service.remove(queryWrapper);
        }
        return RestResult.success("操作成功");
    }
}