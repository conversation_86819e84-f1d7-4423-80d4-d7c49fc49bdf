package com.hualu.app.module.mems.mpc.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MpcItemDto implements Serializable {
    @ApiModelProperty(value = "养护工程项目ID")
    @TableId("MPITEM_ID")
    private String mpitemId;

    @ApiModelProperty(value = "养护工程代码")
    @TableField("MPITEM_CODE")
    private String mpitemCode;

    @ApiModelProperty(value = "养护工程名称")
    @TableField("MPITEM_NAME")
    private String mpitemName;

    @ApiModelProperty(value = "上级养护工程项目编号")
    @TableField("P_MPITEM_ID")
    private String pMpitemId;

    @ApiModelProperty(value = "管养单位编码")
    @TableField("ORG_ID")
    private String orgId;

    @ApiModelProperty(value = "计量单位")
    @TableField("MEASURE_UNIT")
    private String measureUnit;

    @ApiModelProperty(value = "养护工程大类")
    @TableField("MPITEM_BIG_CLASS")
    private String mpitemBigClass;

    @ApiModelProperty(value = "施工工艺")
    @TableField("CONST_TECH")
    private String constTech;

    @ApiModelProperty(value = "施工材料")
    @TableField("CONST_MAT")
    private String constMat;

    @ApiModelProperty(value = "机械设备")
    @TableField("MECH_EQUIP")
    private String mechEquip;

    @ApiModelProperty(value = "现场管理")
    @TableField("FIELD_MGMT")
    private String fieldMgmt;

    @ApiModelProperty(value = "人员配备")
    @TableField("STAFF_CFG")
    private String staffCfg;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "所属修复措施体系ID")
    @TableField("MPITEM_SYS_ID")
    private String mpitemSysId;

    @ApiModelProperty(value = "养护工程简称")
    @TableField("MPITEM_SNAME")
    private String mpitemSname;
}
