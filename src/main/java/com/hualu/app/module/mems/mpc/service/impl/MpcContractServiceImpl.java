package com.hualu.app.module.mems.mpc.service.impl;

import com.hualu.app.module.mems.mpc.dto.MpcContractDto;
import com.hualu.app.module.mems.mpc.entity.MpcContract;
import com.hualu.app.module.mems.mpc.mapper.MpcContractMapper;
import com.hualu.app.module.mems.mpc.service.MpcContractService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-30
 */
@Service
public class MpcContractServiceImpl extends ServiceImpl<MpcContractMapper, MpcContract> implements MpcContractService {

    @Override
    public List<MpcContractDto> selectMpcContracts(String mngOrgId) {
        List<MpcContractDto> dtos = baseMapper.selectMpcContract(mngOrgId);
        return dtos;
    }
}
