package com.hualu.app.module.mems.nminsp.rabbit;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.eos.data.datacontext.DataContextManager;
import com.eos.workflow.api.BPSServiceClientFactory;
import com.hualu.app.config.InspQueueConfig;
import com.hualu.app.module.mems.nminsp.dto.NmDinspEmptDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.service.NmDinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.impl.NmDinspServiceImpl;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.rabbitmq.client.Channel;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DinspMessageConsumer {

    private static final String RETRY_NAME = "retryCount";

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    NmDinspResultService nmDinspResultService;

    @Autowired
    RabbitTemplate rabbitTemplate;

    @RabbitListener(queues = InspQueueConfig.DINSP_QUEUE_NAME,containerFactory = "gpsQueueContainerFactory")
    public void receiveMessage(Message message, Channel channel) throws IOException {

        System.out.println(LocalDateTime.now()+"消息消费中==================================================================");
        try {
            processMessage(message);
        } catch (Exception e) {
            // 消费失败，拒绝消息并将其发送到死信队列
            nackMessage(message);
            System.err.println("Task order message consumption failed: " + new String(message.getBody()) + ", error: " + e.getMessage());
        }
        //ThreadUtil.sleep(1000 * 6);
    }

    @DSTransactional(rollbackFor = Exception.class)
    @BpsTransactionalAnno
    private void processMessage(Message message) {
        // 添加具体的数据库操作等
        NmDinspEmptDto bean = convertToBean(message);
        try(UserContextManager ignore = new UserContextManager(bean.getUserId(), bean.getUserName(), bean.getUserCode(), bean.getNmDinsp().getMntOrgId())){
            try {
                // 实时查询单号，防止单号重复
                NmDinsp tempNmDinsp = bean.getNmDinspList().get(0);
                LocalDate localDate = tempNmDinsp.getInspDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                String orgEn = getOrgEn(tempNmDinsp.getDinspCode());

                // 查询数据库已经生成的结构物ID
                Set<String> dbStructIds = nmDinspService.listStructId(new NmDinspCommon().setMntOrgId(tempNmDinsp.getMntOrgId()).setInspDate(tempNmDinsp.getInspDate()), tempNmDinsp.getFacilityCat())
                        .stream().map(NmDinsp::getStructId).collect(Collectors.toSet());

                // 过滤未生成的结构物表
                List<NmDinsp> noCreatedList = bean.getNmDinspList().stream().filter(e -> !dbStructIds.contains(e.getStructId())).collect(Collectors.toList());

                if (CollectionUtil.isEmpty(noCreatedList)){
                    return;
                }
                final long processInstId = H_WorkFlowHelper.createWorkItem(NmDinspServiceImpl.processDefName, "巡查单", "");
                // 如果同时间段，还有其他的补单，重复的数据是不是不需要添加
                noCreatedList.stream().collect(Collectors.groupingBy(NmDinsp::getFacilityCat)).forEach((facilityCat,v)->{
                    // 重新查询单号顺序，防止单号重复
                    Integer nextSerial = nmDinspService.getNextSerial(facilityCat, orgEn, localDate);
                    AtomicInteger next = new AtomicInteger(nextSerial);
                    v.forEach(item->{
                        String newDinspCode = nmDinspService.buildDinspCode(facilityCat, orgEn, next.incrementAndGet(), localDate);
                        item.setDinspCode(newDinspCode);
                        item.setProcessinstid(processInstId).setCommonDinspId(bean.getCommonDinspId());
                        // todo 记录用户账号，现在有出现流程信息错乱的情况，临时记录
                        item.setRemark(bean.getUserCode());
                    });
                });
                log.info("====================================批量补单成功====================================================================="+tempNmDinsp.getCommonDinspId());
                // 10.批量生成检查结论
                //nmDinspResultService.createBatchNmDinspResult(bean.getNmDinsp(),noCreatedList.stream().map(NmDinsp::getDinspId).collect(Collectors.toList()));
                // 11.批量保存巡查单
                nmDinspService.saveBatch(noCreatedList);
            } catch (Exception e) {
                throw new BaseException("补单失败:" + e.getMessage());
            }
        }
    }

    /**
     * 获取公司英文简称
     * @param dinspCode
     * @return
     */
    private String getOrgEn(String dinspCode){
        // XCD-QL-XBYH-202500302-0001 ，获取XBYH
        List<String> split = StrUtil.split(dinspCode, "-");
        return split.get(2);
    }

    private void nackMessage(Message message){
        int retryCount = getRetryCount(message);
        // 重试次数
        if (InspQueueConfig.RETRY_COUNT >= retryCount) {
            message.getMessageProperties().setHeader(RETRY_NAME, retryCount + 1);
            rabbitTemplate.send(message.getMessageProperties().getReceivedExchange(),
                    message.getMessageProperties().getReceivedRoutingKey(), message);
        }else {
            // 超过重试次数，加入死信队列
            rabbitTemplate.send(InspQueueConfig.DINSP_DEAD_LETTER_EXCHANGE_NAME,InspQueueConfig.DINSP_DEAD_LETTER_ROUTING_KEY,message);
        }
    }

    private int getRetryCount(Message message) {
        Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get(RETRY_NAME);
        return retryCount == null ? 0 : retryCount;
    }

    private NmDinspEmptDto convertToBean(Message message) {
        String content = StrUtil.str(message.getBody(), CharsetUtil.CHARSET_UTF_8);
        NmDinspEmptDto bean = JSONUtil.toBean(content, NmDinspEmptDto.class);
        return bean;
    }

    // 辅助类：用于管理用户上下文的生命周期
    private static class UserContextManager implements AutoCloseable {
        public UserContextManager(String userId, String userName, String userCode, String orgId) {
            CustomRequestContextHolder.setUserId(userId);
            CustomRequestContextHolder.setUserName(userName);
            CustomRequestContextHolder.setUserCode(userCode);
            CustomRequestContextHolder.setOrgId(orgId);
            BPSServiceClientFactory.getLoginManager().setCurrentUser(userCode, userName);
        }

        @Override
        public void close() {
            DataContextManager.current().clear();
            // 如果不清理，会导致线程池复用，导致流程创建人的信息误读
            CustomRequestContextHolder.remove();
            BPSServiceClientFactory.getLoginManager().setCurrentUser(null,null);
        }
    }
}    