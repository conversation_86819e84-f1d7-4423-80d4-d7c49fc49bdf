package com.hualu.app.module.mems.dinsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.config.RedisCacheConfig;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.dinsp.entity.*;
import com.hualu.app.module.mems.dinsp.mapper.DmDinspMapper;
import com.hualu.app.module.mems.dinsp.service.DmDinResultService;
import com.hualu.app.module.mems.dinsp.service.DmDinspRecordService;
import com.hualu.app.module.mems.dinsp.service.DmDinspService;
import com.hualu.app.module.mems.dss.entity.DssImage;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow;
import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;
import com.hualu.app.module.mongo.service.BaseFileEntityService;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.dto.OrderInfo;
import com.hualu.app.module.workflow.service.IWorkItemEventHandler;
import com.hualu.app.module.workflow.service.OrderInfoHandler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.RedisUtils;
import com.hualu.app.utils.mems.MonthlyFieldReader;
import com.hualu.app.utils.mems.MonthlyFinspFieldReader;
import com.hualu.app.utils.mems.NmDinspSituationShowSummarizer;
import com.hualu.app.utils.mems.NmFinspSituationShowSummarizer;
import com.primeton.das.entity.impl.hibernate.type.BigDecimalType;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Service
public class DmDinspServiceImpl extends ServiceImpl<DmDinspMapper, DmDinsp> implements DmDinspService, IWorkItemEventHandler, OrderInfoHandler {

    @Autowired
    private DmDinspRecordService dmDinspRecordService;

    @Autowired
    private DssInfoService dssInfoService;

    @Autowired
    private DmDinResultService dmDinResultService;

    @Autowired
    private DssImageService dssImageService;

    @Autowired
    private BaseFileEntityService baseFileEntityService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private FwRightOrgService orgService;


    @Override
    public DmDinsp getDmDinspByProcessInstId(Long processInstId) {
        LambdaQueryWrapper<DmDinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmDinsp::getProcessinstid,processInstId);
        return baseMapper.selectOne(queryWrapper);
    }

    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void approveDmDinsp(String userCode, String processInstIds, String nextUserId, String apprOpinion, int status) {
        String[] proInstIds = processInstIds.split(",");
        for (String proInstId : proInstIds) {
            long instId = Long.valueOf(proInstId);
            H_WorkFlowHelper.setRelativeData(instId,"status",status);
            H_WorkFlowHelper.finshWorkItem(instId,apprOpinion);
            int iStatus = initStatus(status,instId);
            updateDmDinspStatus(instId,status);

            if (2 == iStatus){
                DmDinsp dmDinsp = getDmDinspByProcessInstId(instId);
                List<DmDinspRecord> dmDinspRecords = dmDinspRecordService.selectRecordByDinspId(dmDinsp.getDinspId());
                saveDssInfo(dmDinsp,dmDinspRecords);
            }
        }
    }

    @Override
    public void updateDmDinspStatus(long processInstId, int status) {
        baseMapper.updateStatus(processInstId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRecord(String[] dssIds, String dinspId) {
        try {
            List<String> ids = Arrays.asList(dssIds);
            List<DmDinspRecord> dmDinspRecords = dmDinspRecordService.selectRecords(dssIds);
            Set<String> facilityCatName = dmDinspRecords.stream().map(DmDinspRecord::getFacilityCatName).filter(Objects::nonNull).collect(Collectors.toSet());
            QueryWrapper<DmDinResult> queryResult = new QueryWrapper();
            queryResult.in("DM_DINSP_ID",dinspId);
            queryResult.in("DM_CONTENT",facilityCatName);
            List<DmDinResult> results = dmDinResultService.list(queryResult);
            for(Iterator<DmDinResult> iterator = results.iterator();iterator.hasNext();){
                dmDinResultService.updateResult(iterator.next(),dmDinspRecords);
            }
            dmDinspRecordService.removeByIds(ids);
            baseMapper.updateDssNum(dinspId);
            QueryWrapper<DssImage> queryImage = new QueryWrapper();
            queryImage.in("DSS_ID",ids);
            List<DssImage> dssImages = dssImageService.list(queryImage);
            if(!CollectionUtils.isEmpty(dssImages)){
                List<String> files = dssImages.stream().map(DssImage::getFileId).filter(Objects::nonNull).collect(Collectors.toList());
                dssImageService.remove(queryImage);
                baseFileEntityService.deleteFileByIds(files);
            }
        }catch (Exception e) {
            throw new BaseException("删除失败");
        }
    }

    @Override
    @BpsTransactionalAnno
    @Transactional(rollbackFor = Exception.class)
    public void deleteDmDinsp(String dinspId) {
        try {
            DmDinsp dmDinsp = baseMapper.selectById(dinspId);
            Long processinstid = dmDinsp.getProcessinstid();
            baseMapper.deleteById(dinspId);
            QueryWrapper<DmDinResult> queryResult = new QueryWrapper();
            queryResult.eq("DM_DINSP_ID",dinspId);
            dmDinResultService.remove(queryResult);
            QueryWrapper<DssImage> queryDmImage = new QueryWrapper();
            queryDmImage.eq("DSS_ID",dinspId);
            dssImageService.remove(queryDmImage);
            QueryWrapper<DmDinspRecord> queryRecord = new QueryWrapper();
            queryRecord.eq("DINSP_ID",dinspId);
            dmDinspRecordService.remove(queryRecord);
            H_WorkFlowHelper.deleteProcessInstance(processinstid);
        } catch (Exception e) {
            throw new BaseException("删除失败");
        }
    }

    @Override
    public void updateResult(DmDinResult result) {
        dmDinResultService.updateById(result);
    }

    private int initStatus(int status,Long instId){
        int iStatus =1;
        boolean finish = H_WorkFlowHelper.isFinish(instId);
        if (1 == status) {
            iStatus = finish ? 2 : 1;
        }else {
            iStatus = 3;
        }
        return iStatus;
    }

    @Override
    public Map<String, String> initAction() {
        return null;
    }

    @Override
    public String getProcessDefName() {
        return "gdcg.emdc.mems.dm.DInspWorkFlow";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savedoEvent(long processInstId, boolean isEnd, String nextAction, String actId) {
        if (isEnd){
            DmDinsp dmDinsp = getDmDinspByProcessInstId(processInstId);
            List<DmDinspRecord> dmDinspRecords = dmDinspRecordService.selectRecordByDinspId(dmDinsp.getDinspId());
            saveDssInfo(dmDinsp,dmDinspRecords);
            updateDmDinspStatus(processInstId,3);
            return;
        }

        if ("manualActivity".equals(actId)){
            updateDmDinspStatus(processInstId,1);
        }else if (nextAction.contains("审核")){
            updateDmDinspStatus(processInstId,2);
        }else if (nextAction.equals("退回")){
            updateDmDinspStatus(processInstId,-1);
        }
    }

    private void saveDssInfo(DmDinsp dmDinsp,List<DmDinspRecord> records){
        if (CollectionUtil.isEmpty(records)){
            return ;
        }
        Map<String,BaseStructDto> map = new HashMap<>();
        records.forEach(item->{
            DssInfo ds = new DssInfo();
            BeanUtils.copyProperties(item,ds);
            //String id = H_KeyWorker.nextIdToString();
            ds.setDssId(item.getDssId());
            ds.setDssCode(item.getDssId());
            String structId = item.getStructId();
            String facilityCat = item.getFacilityCat();
            if(StringUtils.isNotBlank(structId)){
                BaseStructDto dto = null;
                if(map.keySet().contains(structId)){
                    dto = map.get(structId);
                }else{
                    dto = getStructDto(facilityCat,structId);
                    map.put(structId,dto);
                }
                ds.setRpIntrvlId(dto.getRpIntrvlId());
                ds.setRampId(dto.getRampId());
                ds.setStake(dto.getCntrStake());
                ds.setRlStakeNew(dto.getRlCntrStake());
                ds.setRoutecode(dto.getRouteCode());
                ds.setRouteversion(dto.getRouteVersion());
            }else {

                Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth(dmDinsp.getLineCode());
                //设置routeCode 随机获取一个route（未区分方向）
                if (CollectionUtil.isNotEmpty(routeCodes)){
                    ds.setRoutecode(routeCodes.stream().findFirst().get());
                }
            }
            ds.setDssImpFlag(0);
            ds.setRlStakeNew(item.getStake());
            ds.setFindDssUserName(dmDinsp.getPatrolusername());
            ds.setFacilityCat(facilityCat);
            ds.setStructId(structId);
            ds.setRelTaskCode(dmDinsp.getDinspId());
            ds.setDssSource(1);
            ds.setRepairStatus(0);
            ds.setDealStatus(0);
            ds.setFoundDate(dmDinsp.getInspDate());
            ds.setMainRoadId(dmDinsp.getLineCode());
            ds.setLinedirect(item.getLineDirect());
            dssInfoService.saveOrUpdate(ds);
        });
    }

    private BaseStructDto getStructDto(String facilityCat,String structId){
        BaseStructDto structDto = null;
        IBaseStructFace iFacBase = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class).values().stream()
                .filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        if (iFacBase != null){
            structDto = iFacBase.getId(structId);
        }
        return structDto;
    }

    @Override
    public List<OrderInfo> queryOrderInfo(long processInstId) {
        DmDinsp dmDinsp = getDmDinspByProcessInstId(processInstId);
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrderCode(dmDinsp.getDinspCode());
        orderInfo.setOrderType("XCD");
        orderInfo.setOrderId(dmDinsp.getDinspId());
        orderInfo.setStatus(dmDinsp.getStatus());
        orderInfo.setOrgCode(dmDinsp.getMntOrgId());
        return Arrays.asList(orderInfo);
    }

    @Override
    public List<DmCountResult> queryGroupCount(String orgId) {
        List<DmCountResult> results = new ArrayList<>();
        DmCountResult lm = this.baseMapper.queryGroupCount(orgId, "LM");
        DmCountResult ql = this.baseMapper.queryGroupCount(orgId, "QL");
        DmCountResult sd = this.baseMapper.queryGroupCount(orgId, "SD");
        DmCountResult ljbp = this.baseMapper.queryGroupCount(orgId, "BP");
        DmCountResult ja = this.baseMapper.queryGroupCount(orgId, "JA");
        lm.setOrder(1);
        lm.setType("路面");
        ql.setOrder(2);
        ql.setType("桥梁");
        sd.setOrder(3);
        sd.setType("隧道");
        ljbp.setOrder(4);
        ljbp.setType("路基边坡");
        ja.setOrder(5);
        ja.setType("交安");

        //查询缺失的部分
        int year = LocalDate.now().getYear();
        String cacheKey = String.format(RedisCacheConfig.DINSP_SITUATION_CACHE_KEY, "N000001", year);
        List<NmDinspSituationShow> allData = redisUtils.getList(cacheKey, NmDinspSituationShow.class);

        if (allData != null && !allData.isEmpty()) {
            Set<String> orgSet = new HashSet<>(orgService.selectChildOprtOrgCodes(orgId));
            // 使用并行流处理，提高大数据量时的处理速度
            List<NmDinspSituationShow> result = allData.parallelStream()
                    .filter(item -> orgSet.contains(item.getOrgCode()))
                    .collect(Collectors.toList());
            NmDinspSituationShowSummarizer showSummarizer = new NmDinspSituationShowSummarizer();
            NmDinspSituationShow summarize = showSummarizer.summarize(result);
            try {
                Map<String, Object> fieldValues = MonthlyFieldReader.getCurrentMonthFields(summarize);
                int month = LocalDate.now().getMonthValue(); // 当前月份（1-12）
                //应该完全的
                String prefix = MonthlyFieldReader.getMonthPrefix(month);
                BigDecimal lmNums = new BigDecimal(fieldValues.get(prefix + "Lm").toString());
                BigDecimal GenQlNums = new BigDecimal(fieldValues.get(prefix + "GenQl").toString());
                BigDecimal SdNums = new BigDecimal(fieldValues.get(prefix + "Sd").toString());
                BigDecimal BpNums = new BigDecimal(fieldValues.get(prefix + "Bp").toString());
                BigDecimal JaNums = new BigDecimal(fieldValues.get(prefix + "Ja").toString());

                //实际已完成
                BigDecimal lmSum = lm.getSum();
                BigDecimal qlSum = ql.getSum();
                BigDecimal sdSum = sd.getSum();
                BigDecimal bpSum = ljbp.getSum();
                BigDecimal jaSum = ja.getSum();

                BigDecimal lmSubtract = lmNums.subtract(lmSum).max(BigDecimal.ZERO);
                BigDecimal qlSubtract = GenQlNums.subtract(qlSum).max(BigDecimal.ZERO);
                BigDecimal sdSubtract = SdNums.subtract(sdSum).max(BigDecimal.ZERO);
                BigDecimal bpSubtract = BpNums.subtract(bpSum).max(BigDecimal.ZERO);
                BigDecimal jaSubtract = JaNums.subtract(jaSum).max(BigDecimal.ZERO);


                lm.setLack(lmSubtract);
                ql.setLack(qlSubtract);
                sd.setLack(sdSubtract);
                ljbp.setLack(bpSubtract);
                ja.setLack(jaSubtract);

                results.add(lm);
                results.add(ql);
                results.add(sd);
                results.add(ljbp);
                results.add(ja);

            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return results;
    }

    @Override
    public List<DmCountResult> queryFinspGroupCount(String orgId) {
        List<DmCountResult> results = new ArrayList<>();
        DmCountResult lm = this.baseMapper.queryFinspGroupCount(orgId, "LM");
        DmCountResult ql = this.baseMapper.queryFinspGroupCount(orgId, "QL");
        DmCountResult sd = this.baseMapper.queryFinspGroupCount(orgId, "SD");
        DmCountResult ljbp = this.baseMapper.queryFinspGroupCount(orgId, "BP");
        DmCountResult ja = this.baseMapper.queryFinspGroupCount(orgId, "JA");
        lm.setOrder(1);
        lm.setType("路面");
        ql.setOrder(2);
        ql.setType("桥梁");
        sd.setOrder(3);
        sd.setType("隧道");
        ljbp.setOrder(4);
        ljbp.setType("路基边坡");
        ja.setOrder(5);
        ja.setType("交安");

        //查询缺失的部分
        int year = LocalDate.now().getYear();
        String cacheKey = String.format(RedisCacheConfig.FINSP_SITUATION_CACHE_KEY, "N000001", year);
        List<NmFinspSituationShow> allData = redisUtils.getList(cacheKey, NmFinspSituationShow.class);

        if (allData != null && !allData.isEmpty()) {
            Set<String> orgSet = new HashSet<>(orgService.selectChildOprtOrgCodes(orgId));
            // 使用并行流处理，提高大数据量时的处理速度
            List<NmFinspSituationShow> result = allData.parallelStream()
                    .filter(item -> orgSet.contains(item.getOrgCode()))
                    .collect(Collectors.toList());
            NmFinspSituationShowSummarizer showSummarizer = new NmFinspSituationShowSummarizer();
            NmFinspSituationShow summarize = showSummarizer.summarize(result);
            try {
                Map<String, Object> fieldValues = MonthlyFinspFieldReader.getCurrentMonthFields(summarize);
                int month = LocalDate.now().getMonthValue(); // 当前月份（1-12）
                //应该完全的
                String prefix = MonthlyFieldReader.getMonthPrefix(month);
                BigDecimal lmNums = new BigDecimal(fieldValues.get(prefix + "Lm").toString());
                BigDecimal GenQlNums = new BigDecimal(fieldValues.get(prefix + "GenQl").toString());
                BigDecimal SdNums = new BigDecimal(fieldValues.get(prefix + "Sd").toString());
                BigDecimal BpNums = new BigDecimal(fieldValues.get(prefix + "Bp").toString());
                BigDecimal JaNums = new BigDecimal(fieldValues.get(prefix + "Ja").toString());

                //实际已完成
                BigDecimal lmSum = lm.getSum();
                BigDecimal qlSum = ql.getSum();
                BigDecimal sdSum = sd.getSum();
                BigDecimal bpSum = ljbp.getSum();
                BigDecimal jaSum = ja.getSum();

                BigDecimal lmSubtract = lmNums.subtract(lmSum).max(BigDecimal.ZERO);
                BigDecimal qlSubtract = GenQlNums.subtract(qlSum).max(BigDecimal.ZERO);
                BigDecimal sdSubtract = SdNums.subtract(sdSum).max(BigDecimal.ZERO);
                BigDecimal bpSubtract = BpNums.subtract(bpSum).max(BigDecimal.ZERO);
                BigDecimal jaSubtract = JaNums.subtract(jaSum).max(BigDecimal.ZERO);


                lm.setLack(lmSubtract);
                ql.setLack(qlSubtract);
                sd.setLack(sdSubtract);
                ljbp.setLack(bpSubtract);
                ja.setLack(jaSubtract);

                results.add(lm);
                results.add(ql);
                results.add(sd);
                results.add(ljbp);
                results.add(ja);

            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return results;
    }

    @Override
    public IPage<InspectionLedgerDTO> listInspectionLedgerBills(int pageNum, int pageSize, String orgId, String status){
        Page<InspectionLedgerDTO> page = new Page<>(pageNum, pageSize);
        return this.baseMapper.listInspectionLedgerBills(page, orgId, status);
    }

    @Override
    public IPage<InspectionLedgerDTO> listInspectionRCLedgerBills(int pageNum, int pageSize, String orgId, String status)
    {
        Page<InspectionLedgerDTO> page = new Page<>(pageNum, pageSize);
        return this.baseMapper.listInspectionRCLedgerBills(page, orgId, status);
    }

    @Override
    public Map<String, Object> countDailyDefects(String orgId)
    {
        return this.baseMapper.countDailyDefects(orgId);
    }

    @Override
    public Map<String, Object> calculateDefectsByCategory(String orgId)
    {
        return this.baseMapper.calculateDefectsByCategory(orgId);
    }

    @Override
    public IPage<FacilityInspectionDTO> indexDefectRecordsForFastQuery(int pageNum, int pageSize, String orgId)
    {
        Page<InspectionLedgerDTO> page = new Page<>(pageNum, pageSize);
        return this.baseMapper.indexDefectRecordsForFastQuery(page, orgId);
    }

    @Override
    public IPage<FacilityInspectionDTO> indexDefectRecordsRcForFastQuery(int pageNum, int pageSize, String orgId)
    {
        Page<InspectionLedgerDTO> page = new Page<>(pageNum, pageSize);
        return this.baseMapper.indexDefectRecordsRcForFastQuery(page, orgId);
    }

    @Override
    public List<String> getImageFileId(String dssId)
    {
        List<String> imageFileId = this.baseMapper.getImageFileId(dssId);
        List<String> pathAll = new ArrayList<>();
        imageFileId.stream().forEach(v ->
        {
            String fileEntityPath = baseFileEntityService.getFileEntityPath(v);
            pathAll.add(fileEntityPath);
        });
        return pathAll;
    }
    @Override
    public List<InspectionResult> InspectionResult(String orgId, String dssId)
    {
        return this.baseMapper.InspectionResult(orgId, dssId);
    }

    @Override
    public IPage<InspectionRecord> InspectionRecord(int pageNum, int pageSize, String orgId){
        Page<InspectionLedgerDTO> page = new Page<>(pageNum, pageSize);
        return this.baseMapper.InspectionRecord(page, orgId);
    }
}
