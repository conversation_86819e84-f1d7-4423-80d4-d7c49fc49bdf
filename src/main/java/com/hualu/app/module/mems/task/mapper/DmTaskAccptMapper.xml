<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.task.mapper.DmTaskAccptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.task.entity.DmTaskAccpt">
        <id column="MTASK_ACCPT_ID" property="mtaskAccptId" />
        <result column="MTASK_ACCPT_CODE" property="mtaskAccptCode" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNTN_ORG_NAME" property="mntnOrgName" />
        <result column="MNTN_DEPT_NM" property="mntnDeptNm" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="APPLY_USER" property="applyUser" />
        <result column="APPLY_DATE" property="applyDate" />
        <result column="APPLY_COMMENT" property="applyComment" />
        <result column="ACCEPT_USER" property="acceptUser" />
        <result column="ACCEPT_DATE" property="acceptDate" />
        <result column="ACCEPT_COMMENT" property="acceptComment" />
        <result column="IS_IN_TIME" property="isInTime" />
        <result column="REMARK" property="remark" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="STATUS" property="status" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="MTASK_ID" property="mtaskId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MTASK_ACCPT_ID, MTASK_ACCPT_CODE, MNT_ORG_ID, MNTN_ORG_NAME, MNTN_DEPT_NM, BUSINESS_TYPE, APPLY_USER, APPLY_DATE, APPLY_COMMENT, ACCEPT_USER, ACCEPT_DATE, ACCEPT_COMMENT, IS_IN_TIME, REMARK, PROCESSINSTID, STATUS, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, MTASK_ID
    </sql>
    <select id="selectFacilityCat" resultType="java.util.Map">
        select distinct ds.facility_cat from DM_TASK_DETAIL t ,dss_info ds where t.dss_id=ds.dss_id and t.mtask_id = #{mtaskId}
    </select>
    <select id="selectAccptDetails" resultType="com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto">
        select d.repair_status,
               d.main_road_id                                as line_Id,
               d.ramp_id,
               d.facility_cat,
               d.struct_id,
               gl.line_code                                  as line_Code,
               gl.LINE_ALLNAME || '(' || gl.LINE_CODE || ')' as line_Name,
               d.linedirect                                  as line_direct,
               d.dss_l                                       ,
               d.dss_l_unit                                  ,
               d.dss_w                                       ,
               d.dss_w_unit                                  ,
               d.dss_d                                       ,
               d.dss_d_unit                                  ,
               d.dss_n                                       ,
               d.dss_n_unit                                  ,
               d.dss_a                                       ,
               d.dss_a_unit                                  ,
               d.dss_v                                       ,
               d.dss_v_unit                                  ,
               d.dss_p                                       ,
               d.dss_g                                       ,
               d.struct_part_id,
               d.rl_stake_new                                as rl_stake,
               a.*,
               d.stake,
               d.lane,
               d.RP_INTRVL_ID,
               d.DSS_DESC,
               d.DSS_TYPE,
               dt.dss_type_name,
               (select memsdb.zpLZH(d.dss_id, 0) from dual)  as file_ids,
               (select memsdb.zpLZH(d.dss_id, 1) from dual)  as after_file_ids
        from (select distinct t.*, mp.MPITEM_NAME, mmp.mmp_code, dt.contr_id
              from DM_TASK_DETAIL t
                       left join dm_task_accpt_detail mm on mm.mtask_dtl_id = t.mtask_dtl_id
                       left join MPC_MPITEM mp on t.MPITEM_ID = mp.MPITEM_ID
                       left join dm_task dt on dt.mtask_id = t.mtask_id
                       left join MPC_MPITEM_PRICE mmp on mmp.contr_id = dt.contr_id and mmp.MP_ITEM_ID = mp.MPITEM_ID
              where 1 = 1
                and t.mtask_id = #{mtaskId}
             ) a,
             DSS_INFO d,
             DSS_TYPE_NEW dt,
             GDGS.V_BASE_LINE gl
        where a.DSS_ID = d.DSS_ID
          and d.DSS_TYPE = dt.DSS_TYPE
          and d.MAIN_ROAD_ID = gl.LINE_ID
          <choose>
              <when test="facilityCat=='FJGC'">
                  and d.facility_cat = 'FJGC'
              </when>
            <otherwise>
                and d.facility_cat not in ('FJGC')
            </otherwise>
          </choose>
          and (d.MAIN_ROAD_ID = gl.line_id or gl.line_id = d.ramp_id)
        order by gl.line_id, d.linedirect, d.stake, a.mmp_code
    </select>
    <select id="selectNextCode" resultType="java.lang.Integer">
        <bind name="pattern" value="'%' + codePrefix + '%'" />
        select max(substr(MTASK_ACCPT_CODE, length(MTASK_ACCPT_CODE) - 2))
        from DM_TASK_ACCPT t
        where MTASK_ACCPT_CODE like #{pattern}
        and t.MNT_ORG_ID = #{orgId}
    </select>
    <select id="selectLast" resultType="com.hualu.app.module.mems.task.entity.DmTaskAccpt">
        select *
        from (select * from DM_TASK_ACCPT
              where mnt_org_id = #{orgId} and UPDATE_TIME is not null
              order by update_time desc)
        where rownum &lt; 2
    </select>
</mapper>
