package com.hualu.app.module.mems.nminsp.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eos.workflow.api.BPSServiceClientFactory;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.mems.dss.service.DssImageService;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspCommon;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspCommonMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspCommonService;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.hualu.app.module.mems.nminsp.vo.NmDinspFacilityCatGroupVo;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.hualu.app.module.workflow.anno.BpsTransactionalAnno;
import com.hualu.app.module.workflow.controller.WfworkitemController;
import com.hualu.app.module.workflow.dto.WorkActDto;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_FutureHelper;
import com.hualu.app.utils.H_PageHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.enums.BusinessType;
import com.tg.dev.api.util.hp.H_CValidator;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.rest.M_MyBatisController;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 新版日常巡查公共单 前端控制器
 *
 * <AUTHOR>
 * @since 2025-03-19
 */
@Api(tags = "日常巡查公共单",description = "新版日常巡查公共单 前端控制器")
@RestController
@RequestMapping("/nmDinspCommon")
public class NmDinspCommonController extends M_MyBatisController<NmDinspCommon,NmDinspCommonMapper>{

    @Autowired
    private NmDinspCommonService service;

    @Autowired
    NmDinspRecordService recordService;

    @Autowired
    NmDinspService dinspService;

    @Autowired
    FwRightUserService userService;

    @Autowired
    NmDinspController dinspController;

    @Autowired
    WfworkitemController wfworkitemController;

    @Autowired
    DssImageService imageService;

    @Autowired
    BaseLineService lineService;

    /**
    * 分页查询
    * @return
    */
    @ApiRegister(value = "nmDinspCommon:page",businessType = BusinessType.OTHER)
    @ApiOperation("分页查询")
    @PostMapping("page")
    @Override
    public RestResult<List<NmDinspCommon>> selectPage() {
        QueryWrapper queryWrapper = this.initQueryWrapper(this.getReqParam());
        Map reqParam = this.getReqParam();
        if (ObjectUtil.isEmpty((reqParam.get("status")))) {
            reqParam.put("status", "0");
        }
        String status = reqParam.getOrDefault("status", "0").toString();
        String taskSql =
            H_WorkFlowHelper.getUserTaskSql(StrUtil.isBlank(status) ? -1 : Integer.valueOf(status),
                "nm_dinsp");
        IPage<NmDinspCommon> iPage = service.selectPageFor(this.getPage(), queryWrapper, taskSql);
        return H_PageHelper.getPageResult(this.pageService.returnPageResult(iPage));
    }

    @Override
    protected QueryWrapper initQueryWrapper(Map params) {
        QueryWrapper<NmDinspCommon> queryWrapper = new QueryWrapper();
        initQueryParam(queryWrapper,params);
        H_BatisQuery.setFieldValue2In(queryWrapper, params, this.getEntityClass());
        return queryWrapper;
    }

    private  void initQueryParam(QueryWrapper<NmDinspCommon> queryWrapper, Map params){
        // 查询有无病害
        Object hasDss = params.getOrDefault("hasDssStatus", "3");
        if (hasDss.equals("0")){
            queryWrapper.eq("dss_Num",0);
        }else if (hasDss.equals("1")){
            queryWrapper.ge("dss_Num",1);
        }
        //queryWrapper.lambda().eq(NmDinspCommon::getCreateUserId,CustomRequestContextHolder.getUserId());

        Object startDate = params.get("startDate");
        if (!StrUtil.isBlankIfStr(startDate)){
            queryWrapper.apply("insp_date >= to_date({0},'YYYY-MM-dd')",startDate);
            params.remove("startDate");
        }

        Object endDate = params.get("endDate");
        if (!StrUtil.isBlankIfStr(endDate)){
            queryWrapper.apply("insp_date <= to_date({0},'YYYY-MM-dd')",endDate);
            params.remove("endDate");
        }
        params.remove("status");
        queryWrapper.in("mnt_org_id", H_DataAuthHelper.selectOrgIds());
        queryWrapper.lambda().orderByDesc(NmDinspCommon::getUpdateTime);
    }


    /**
     * 初始化巡查单，用于新建单回显默认数据
     * @return
     */
    @GetMapping("init")
    public RestResult<NmDinspCommon> initNmDinsp(){
        NmDinspCommon nmDinsp = service.initNmDinspCommon(CustomRequestContextHolder.getOrgIdString());
        return RestResult.success(nmDinsp);
    }

    /**
    * 添加或者修改主单
    * @param nmDinsp
    * @return
    */
    @ApiRegister(value = "nmDinspCommon:saveOrUpdate",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改主单")
    @PostMapping("saveOrUpdate")
    public RestResult<String> saveOrUpdate(@RequestBody @Valid NmDinspCommon nmDinsp) {
        //如果app小于多少版本提示更新
        boolean isApp = "1".equals(request.getHeader("AppClient"));
        boolean needUpdate = Convert.toInt(request.getHeader("AppCode"), -1) < 48;
        if (isApp && needUpdate){
            return RestResult.error("app版本过低，请更新app版本");
        }
        // 一个路段管理多个路线，当界面新建单为时S51，手动改为G15时，路面名称没有变化，所以每次修改时，需要再次更新路线名称
        String lineName = lineService.getLineName(nmDinsp.getLineCode());
        if (StrUtil.isNotBlank(lineName)){
            nmDinsp.setLineName(lineName);
        }
        boolean isAdd = StrUtil.isBlank(nmDinsp.getDinspId());
        if (isAdd){
            Integer mntPerson = userService.isMntPerson(CustomRequestContextHolder.getUserCode());
            nmDinsp.setXcType(mntPerson);
            nmDinsp.setDinspId(H_KeyWorker.nextIdToString());
            nmDinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
            // 自动创建一个路面的巡查单
            nmDinsp.setFacilityCat("LM");
            // 每一个公单，默认新建一个LM巡查单
            dinspService.createDinsp(nmDinsp);
            //建公用单时，需要查询当前未挂接单，实现与当前公用单进行挂接
            dinspService.bindNoDinspCommonId(nmDinsp);
        }
        service.saveOrUpdate(nmDinsp);
        return RestResult.success("操作成功");
    }

    @DSTransactional(rollbackFor = Exception.class)
    @ApiRegister(value = "nmDinspCommon:saveOrUpdateForApp",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改主单，上传照片")
    @PostMapping("saveOrUpdateForApp")
    public RestResult<String> saveOrUpdateForApp(@RequestParam("data") String data, @RequestParam(value = "files",required = false) MultipartFile[] files){
        NmDinspCommon nmDinsp = JSONUtil.toBean(data, NmDinspCommon.class);
        H_CValidator.validator2Exception(nmDinsp);
        //如果app小于多少版本提示更新
        boolean isApp = "1".equals(request.getHeader("AppClient"));
        boolean needUpdate = Convert.toInt(request.getHeader("AppCode"), -1) < 46;
        if (isApp && needUpdate){
            return RestResult.error("app版本过低，请更新app版本");
        }

        boolean isAdd = StrUtil.isBlank(nmDinsp.getDinspId());
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        String fileId = fileIds.stream().filter(Objects::nonNull).collect(Collectors.joining(","));
        if (isAdd){
            Integer mntPerson = userService.isMntPerson(CustomRequestContextHolder.getUserCode());
            nmDinsp.setXcType(mntPerson);
            nmDinsp.setDinspId(H_KeyWorker.nextIdToString());
            nmDinsp.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
            // 自动创建一个路面的巡查单
            nmDinsp.setFacilityCat("LM");
            nmDinsp.setFileIds(fileId);
            NmDinsp dinsp = dinspService.createDinsp(nmDinsp);
            imageService.saveDssImageForApp(dinsp.getDinspId(),nmDinsp.getFileIds(),6);
        }

        nmDinsp.setFileIds(fileId);
        imageService.saveDssImageForApp(nmDinsp.getDinspId(),nmDinsp.getFileIds(),6);
        service.saveOrUpdate(nmDinsp);
        return RestResult.success("操作成功");
    }


    /**
     * 添加或者修改病害信息
     * @param data
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @ApiRegister(value = "nmDinspCommon:saveOrUpdateRecord",businessType = BusinessType.UPDATE)
    @ApiOperation("添加或者修改病害信息")
    @PostMapping("saveOrUpdateRecord")
    public RestResult<String> saveOrUpdateRecord(
        @RequestParam("data") String data,
        @RequestParam(value = "files",required = false) MultipartFile[] files
    ) {
        NmDinspRecord entity = JSONUtil.toBean(data, NmDinspRecord.class);
        H_CValidator.validator2Exception(entity);
        H_StructHelper.validateNmDinspRecord(entity);
        // 移动端调用
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        entity.setFileIds(fileIds.stream().filter(Objects::nonNull).collect(Collectors.joining(",")));

        // 通用接口调用
        /*if (StrUtil.isBlank(entity.getCommonDinspId())){
            if (StrUtil.isBlank(entity.getDinspId())){
                throw new BaseException("dinspId不能为空");
            }
            recordService.saveOrUpdateRecordForApp(entity);
        }else {
            service.saveOrUpdateRecord(entity);
        }*/
        service.saveOrUpdateRecord(entity);
        return RestResult.success("操作成功");
    }

    /**
     * 病害列表
     * @return
     */
    @PostMapping("listRecord")
    public RestResult<List<NmDinspRecord>> listRecord(String commonDinspId,String facilityCat){
        NmDinspCommon nmDinspCommon = service.getById(commonDinspId);
        if (Objects.isNull(nmDinspCommon)){
            return RestResult.success(Lists.newArrayList());
        }
        nmDinspCommon.setFacilityCat(facilityCat);
        List<NmDinspRecord> nmDinspRecords = recordService.selectRecords(nmDinspCommon);
        return RestResult.success(nmDinspRecords);
    }

    /**
     * 公用单id获取路面巡查单id
     * @param commonDinspId 公用单id
     * @return
     */
    @GetMapping("getPavementDinspId")
    public RestResult<String> getPavementDinspId(String commonDinspId){
        String userId = CustomRequestContextHolder.getUserId();
        LambdaQueryWrapper<NmDinsp> ew = new LambdaQueryWrapper<>();
        ew.eq(NmDinsp::getCommonDinspId, commonDinspId);
        ew.eq(NmDinsp::getFacilityCat, H_BasedataHepler.LM);
        ew.eq(NmDinsp::getDelFlag, 0);
        List<NmDinsp> list = dinspService.list(ew);
        List<NmDinsp> fList =
            list.stream().filter(s -> ObjectUtil.equals(s.getCreateUserId(), userId))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(fList)) {
            return RestResult.success(fList.get(0).getDinspId(), "");
        } else if (CollectionUtil.isNotEmpty(list)){
            return RestResult.success(list.get(0).getDinspId(), "");
        }
        return RestResult.error("没有路面日常巡查单");
    }

    /**
     * 查询关联的路面、交安、桥梁、边坡、隧道等日常巡查单
     * @param commonDinspId
     * @return
     */
    @PostMapping("listNmDinsp")
    public RestResult<List<NmDinsp>> listNmDinsp(String commonDinspId){
        return dinspController.selectPage();
    }

    /**
     * 按照设施分组，查询总数量、待办数量
     * @param commonDinspId 公用单ID
     * @return
     */
    @PostMapping("listNmDinspFacilityGroup")
    public RestResult<List<NmDinspFacilityCatGroupVo>> listNmDinspFacilityGroupVo(@RequestParam(required = true) String commonDinspId){
        List<NmDinspFacilityCatGroupVo> groupVos = service.listNmDinspFacilityGroupVo(commonDinspId);
        return RestResult.success(groupVos);
    }
    /**
     * 查询下一岗
     * @param commonDinspId 公用单ID
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/loadNextActivitys")
    public RestResult<List<WorkActDto>> loadNextActivitys(String commonDinspId) throws Exception {
        // 1.查询待办的流程实例
        Map<Integer,List<Long>> taskMap = dinspService.listProcessTask(commonDinspId,null);
        // 流程创建待办
        List<Long> createTaskList = taskMap.get(0);
        // 业主审核待办
        List<Long> submitTaskList = taskMap.get(1);

        if (CollectionUtil.isNotEmpty(createTaskList)){
            Long proInstId = createTaskList.get(0);
            List<WorkActDto> workActDtos = wfworkitemController.loadNextActivitys(proInstId).getData();
            workActDtos.forEach(workActDto -> {workActDto.setProcessInstId(proInstId);});
            return RestResult.success(workActDtos);
        }
        if (CollectionUtil.isNotEmpty(submitTaskList)){
            Long proInstId = submitTaskList.get(0);
            List<WorkActDto> workActDtos = wfworkitemController.loadNextActivitys(proInstId).getData();
            workActDtos.forEach(workActDto -> {workActDto.setProcessInstId(proInstId);});
            return RestResult.success(workActDtos);
        }
        throw new BaseException(commonDinspId+"不存在待办项");
    }

    /**
     * 公用单批量办理
     * @param commonDinspId 公单ID
     * @param processInstId 当前的流程实例ID
     * @param nextAction 下一节点
     * @param nextUserIds 下一办理人
     * @param apprOpinion 审批意见
     * @param appvOpinion 审批意见
     * @return
     */
    @PostMapping("submitApprove")
    public RestResult<String> submitApprove(String commonDinspId
                                            ,long processInstId
            ,String nextAction, String nextUserIds
            ,@RequestParam(required = false) String apprOpinion
            ,@RequestParam(required = false) String appvOpinion){
        // 1.查询待办的流程实例
        Map<Integer, List<Long>> taskListMap = dinspService.listProcessTask(commonDinspId,null);
        // 当前流程是流程创建人待办
        boolean isCreateFlag = false;
        // 流程创建待办
        List<Long> createTaskList = taskListMap.get(0);
        if (CollectionUtil.isNotEmpty(createTaskList)) {
            isCreateFlag = createTaskList.contains(processInstId);
            String processIdStr = StrUtil.join(",", createTaskList);
            wfworkitemController.submitApprove(processIdStr, nextAction, nextUserIds, apprOpinion, appvOpinion);
        }
        // 业主审核待办（直接病害入库）
        List<Long> submitTaskList = taskListMap.get(1);
        if (CollectionUtil.isNotEmpty(submitTaskList)) {
            String processIdStr = StrUtil.join(",", submitTaskList);
            String tempNextAction = isCreateFlag ? "putDss" : nextAction;
            String tempNextUserIds = isCreateFlag ? null : nextUserIds;
            wfworkitemController.submitApprove(processIdStr, tempNextAction, tempNextUserIds, apprOpinion, appvOpinion);
        }
        return RestResult.success("办理成功");
    }

    /**
     * 删除病害
     */
    @ApiRegister(value = "nmDinspRecord:delIds",businessType = BusinessType.DELETE)
    @ApiOperation("删除（dssIds为字符串，以逗号分割）")
    @PostMapping("delDssIds")
    public RestResult<String> delIds(String dssIds){
        List<String> idList = StrUtil.split(dssIds, ",");
        recordService.deleteRecord(idList);
        return RestResult.success("操作成功");
    }

    /**
     * 补充空单
     * @param entity
     * @return
     */
    @BpsTransactionalAnno
    @DSTransactional(rollbackFor = Exception.class)
    @PostMapping("createEmptyDinsp")
    public RestResult<String> createEmptyDinsp(@RequestBody @Validated NmDinspCommon entity){

        final String userId = CustomRequestContextHolder.getUserId();
        final String userCode = CustomRequestContextHolder.getUserCode();
        final String userName = CustomRequestContextHolder.getUserName();
        final String orgId = CustomRequestContextHolder.getOrgIdString();
        final String orgEn = (String) CustomRequestContextHolder.get("ORG_EN");
        // 提交状态
        entity.setStatus(0);
        // 根据mnt_org_id、line_code、insp_date排查未新建的单，然后重新新建
        // 获取相应的路段权限
        Set<String> routeSet = H_DataAuthHelper.selectRouteCodeAuthByLineCode(entity.getLineCode());
        Map<String, IBaseStructFace> beansOfType = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class);

        // 获取结构集合
        Map<String,List<BaseStructDto>> structMap = new HashMap<String,List<BaseStructDto>>();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        try {
            beansOfType.values().stream().filter(e->!"HD".equals(e.getFacilityCat()))
                    .collect(Collectors.toList()).parallelStream().forEach(row->{
                        RequestContextHolder.setRequestAttributes(attributes,true);
                        //获取结构物基本信息：structId,structName,routeCode
                        List<BaseStructDto> baseStructDtos = row.listByRouteCodes(routeSet);
                        if (CollectionUtil.isNotEmpty(baseStructDtos)){
                            structMap.put(row.getFacilityCat(), baseStructDtos);
                        }
                    });

            // 结构物日常巡查单新建
            List<CompletableFuture<Void>> futureList = Lists.newArrayList();
            structMap.forEach((cat,structDtos)->{
                IBaseStructFace structFace = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class)
                        .values().stream().filter(e -> e.getFacilityCat().equals(cat))
                        .findFirst().orElse(null);
                if (structFace == null) {
                    return;
                }
                List<NmDinsp> nmDinsps = dinspService.listStructId(entity,cat);
                // 获取已经新建巡查单的结构物ID
                Set<String> structIds = CollectionUtil.isEmpty(nmDinsps)? Sets.newHashSet():nmDinsps.stream().map(NmDinsp::getStructId).collect(Collectors.toSet());
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    RequestContextHolder.setRequestAttributes(attributes,true);
                    CustomRequestContextHolder.setUserId(userId);
                    CustomRequestContextHolder.setUserCode(userCode);
                    CustomRequestContextHolder.setUserName(userName);
                    CustomRequestContextHolder.setOrgId(orgId);
                    CustomRequestContextHolder.set("ORG_EN", orgEn);
                    // 异步线程，需要流程登录
                    BPSServiceClientFactory.getLoginManager().setCurrentUser(userCode, userName);
                    // 未新建的结构物
                    List<BaseStructDto> noCreateStructDtos = structDtos.stream().filter(e -> !structIds.contains(e.getStructId())).collect(Collectors.toList());
                    // 自动去创建单
                    NmDinsp newDinsp = BeanUtil.copyProperties(entity, NmDinsp.class,"dinspId","createTime","updateTime","createUserId","updateUserId","dssNum").setFacilityCat(cat);
                    newDinsp.setCommonDinspId(entity.getDinspId());
                    dinspService.createEmptyNmDinspByStruct(noCreateStructDtos, newDinsp,false);
                });
                futureList.add(future);
            });
            H_FutureHelper.sequence(futureList).join();
            // 自动去创建单
            NmDinsp newDinsp = BeanUtil.copyProperties(entity, NmDinsp.class);
            dinspService.createEmpTyNmDinspByLMAndJA(newDinsp,false);
        }finally {
            RequestContextHolder.resetRequestAttributes();
        }
        return RestResult.success("正在执行补单任务，请稍等几分钟再刷新页面查看");
    }

    /**
     * 批量删除巡查单
     * @param dinspIds
     * @return
     */
    @DSTransactional(rollbackFor = Exception.class)
    @PostMapping("delBatch")
    public RestResult<String> delBatch(@RequestParam(required = true) String dinspIds){
        List<String> idList = StrUtil.split(dinspIds, ",");
        service.removeByIds(idList);
        dinspService.removeByCommonDinspIds(idList);
        return RestResult.success("操作成功");
    }
}