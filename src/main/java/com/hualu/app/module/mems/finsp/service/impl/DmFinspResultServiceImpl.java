package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.finsp.mapper.DmFinspResultMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.utils.H_BasedataHepler;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Service
public class DmFinspResultServiceImpl extends ServiceImpl<DmFinspResultMapper, DmFinspResult> implements DmFinspResultService {

    @Override
    public List<DmFinspResult> selectResultByFinspId(String finspId) {
        return baseMapper.selectResultByFinspId(finspId);
    }

    @Override
    public void delDmFinspResult(String finspId) {
        LambdaQueryWrapper<DmFinspResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspResult::getFinspId,finspId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<DmFinspResult> selectLastFinspResult(String finspId) {
        LambdaQueryWrapper<DmFinspResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspResult::getFinspId,finspId);
        return list(queryWrapper);
    }

    @Override
    public void updateFinspResultsByRecords(String finspId, String facilityCat, List<DmFinspRecord> records) {

        if (CollectionUtil.isEmpty(records)){
            return;
        }
        LambdaQueryWrapper<DmFinspResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinspResult::getFinspId,finspId);
        List<DmFinspResult> results = list(queryWrapper);
        if (CollectionUtil.isEmpty(results)){
            return;
        }

        Map<String, List<DmFinspRecord>> recordMap = records.stream().collect(Collectors.groupingBy(DmFinspRecord::getFinspItemId));
        results.forEach(item->{
            List<DmFinspRecord> dmFinspRecords = recordMap.get(item.getFinspResId());
            if (CollectionUtil.isEmpty(dmFinspRecords)){
                return;
            }
            StringBuffer remark = new StringBuffer();
            StringBuffer dssType = new StringBuffer();
            dmFinspRecords.forEach(r->{
                if (H_BasedataHepler.BP.equals(facilityCat)){
                    remark.append("坡级："+r.getLane()).append("级，");
                }
                remark.append("桩号：").append(r.getKStake()).append("，类型：").append(r.getDssTypeName());
                String dssNum = r.getDssNum();
                if(StringUtils.isBlank(dssNum)) {
                    remark.append("；");
                }else {
                    remark.append("，").append(r.getDssNum()).append("；");
                }
                initQlRecord(dssType,facilityCat,r);
                initRes(remark,dssType,facilityCat,item);
            });
        });

        updateBatchById(results);
    }

    /**
     * 初始化需要修改的检查结论
     * @param remark
     * @param dssType
     * @param facilityCat
     * @param item
     */
    private void initRes(StringBuffer remark,StringBuffer dssType,String facilityCat,DmFinspResult item){
        //如果备注为空或者大于2000字节，不进行赋值
        if (!(remark.length() > 0 && remark.toString().getBytes().length < 2000)){
            return;
        }
        if (H_BasedataHepler.SD.equals(facilityCat)){
            item.setIssueDesc(remark.toString()).setInspResult("B");
            return;
        }
        if (H_BasedataHepler.BP.equals(facilityCat)){
            item.setIssueDesc(remark.toString()).setInspResult("1");
        }

        item.setRemark(remark.toString());
        if(dssType.length() > 0 && dssType.toString().contains(",")) {
            dssType.delete(dssType.length() - 1, dssType.length());
            item.setIssueDesc(dssType.toString());
        }
    }

    /**
     * 初始化桥梁结论
     * @param dssType
     * @param facilityCat
     * @param record
     */
    private void initQlRecord(StringBuffer dssType,String facilityCat,DmFinspRecord record){
        if (!H_BasedataHepler.QL.equals(facilityCat)){
            return;
        }
        String dssTypeName = record.getDssTypeName();
        String dssDescQ = record.getDssDescQ();
        String[] split = dssDescQ.split(",");


        for(String s : split) {
            if(dssTypeName.contains(s) && !dssType.toString().contains(s)) {
                dssType.append(s).append(",");
                break;
            }
        }
        for(String s : split) {
            if(!dssTypeName.contains(s) && !dssType.toString().contains("其他")) {
                dssType.append("其他").append(",");
                break;
            }
        }
    }
}
