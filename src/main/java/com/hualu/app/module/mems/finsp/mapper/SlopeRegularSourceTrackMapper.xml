<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.SlopeRegularSourceTrackMapper">
  <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.SlopeRegularSourceTrack">
    <!--@mbg.generated-->
    <!--@Table MEMSDB.SLOPE_REGULAR_SOURCE_TRACK-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SLOPE_ID" jdbcType="VARCHAR" property="slopeId" />
    <result column="LAT" jdbcType="DECIMAL" property="lat" />
    <result column="LNG" jdbcType="DECIMAL" property="lng" />
    <result column="TIME" jdbcType="TIMESTAMP" property="time" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SLOPE_ID, LAT, LNG, "TIME"
  </sql>
</mapper>