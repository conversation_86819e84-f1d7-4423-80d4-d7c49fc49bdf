package com.hualu.app.module.mems.dss.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.dto.DssRepairTimelinessDto;
import com.hualu.app.module.mems.dss.dto.DssWxDto;
import com.hualu.app.module.mems.dss.dto.InfoForSlopeBase;
import com.hualu.app.module.mems.dss.entity.DssInfo;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DssInfoService extends IService<DssInfo> {

    /**
     * 查询交安病害
     * @param dmFinsp
     * @return
     */
    List<DssInfo> selectJADss(DmFinsp dmFinsp);

    /**
     * 修改病害状态
     * @param dssId
     * @param repairStatus
     */
    void updateRepairStatus(List<String> dssId,Integer repairStatus);


    /**
     * 修改病害处置状态
     * @param dssIds
     * @param dealStatus
     */
    void updateDealStatus(List<String> dssIds,Integer dealStatus);

    /**
     * 设置病害处置状态及关联任务单
     * @param dssIds
     * @param mtaskId
     * @param dealStatus
     */
    void updateDealStatus(List<String> dssIds,String mtaskId,Integer dealStatus);

    /**
     * 病害管理列表分页查询
     * @param page
     * @param queryWrapper
     * @param orderSql 排序sql
     * @return
     */
    List<DssInfoDto> selectDssPage(Page page, QueryWrapper queryWrapper,String orderSql);

    /**
     * 查询病害维修信息
     * @param dssIds
     * @return
     */
    Map<String, List<DssWxDto>> selectDssWxList(List<String> dssIds);


    /**
     * 统计病害维修及时率
     * @param month
     * @return
     */
    DssRepairTimelinessDto getRepairTimeliness(String month);


    /**
     * 边坡日常巡查/经常检查
     * @param type
     * @param slopeId
     * @return
     */
    List<InfoForSlopeBase> selectSlopeDm(String type, String slopeId);
}
