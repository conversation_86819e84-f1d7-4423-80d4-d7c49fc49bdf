package com.hualu.app.module.mems.mpc.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MpcMpitemPrice对象", description="")
public class MpcMpitemPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("CONTR_MPITEM_ID")
    private String contrMpitemId;

    @ApiModelProperty(value = "所属养护合同")
    @TableField("CONTR_ID")
    private String contrId;

    @ApiModelProperty(value = "清单编号")
    @TableField("MPITEM_CODE")
    private String mpitemCode;

    @ApiModelProperty(value = "清单名称")
    @TableField("MPITEM_NAME")
    private String mpitemName;

    @ApiModelProperty(value = "单价")
    @TableField("UNIT_PRICE")
    private Double unitPrice;

    @ApiModelProperty(value = "包干金额")
    @TableField("LUMP_PRICE")
    private Double lumpPrice;

    @ApiModelProperty(value = "数量")
    @TableField("UNIT_COUNT")
    private Double unitCount;

    @ApiModelProperty(value = "计量单位")
    @TableField("MEASURE_UNIT")
    private String measureUnit;

    @ApiModelProperty(value = "是否包干 0 否 1 是")
    @TableField("IS_LUMP")
    private Integer isLump;

    @ApiModelProperty(value = "养护频率")
    @TableField("RATE")
    private String rate;

    @ApiModelProperty(value = "合同清单编号")
    @TableField("MMP_CODE")
    private String mmpCode;

    @ApiModelProperty(value = "养护措施ID")
    @TableField("MP_ITEM_ID")
    private String mpItemId;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "预算金额")
    @TableField("PREVENT_PRICE")
    private Double preventPrice;

    @ApiModelProperty(value = "实施金额")
    @TableField("EFFECT_PRICE")
    private Double effectPrice;


}
