<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.task.mapper.DmTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.task.entity.DmTask">
        <id column="MTASK_ID" property="mtaskId" />
        <result column="MTASK_CODE" property="mtaskCode" />
        <result column="MTASK_NAME" property="mtaskName" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNG_DEPT_NM" property="mngDeptNm" />
        <result column="ASKED_START_DATE" property="askedStartDate" />
        <result column="ASKED_COMPL_DATE" property="askedComplDate" />
        <result column="MNTN_ORG_NAME" property="mntnOrgName" />
        <result column="EMERGY_DEGREE" property="emergyDegree" />
        <result column="SEND_USER" property="sendUser" />
        <result column="SEND_TIME" property="sendTime" />
        <result column="RECEIVE_USER" property="receiveUser" />
        <result column="RECEIVE_TIME" property="receiveTime" />
        <result column="CANCEL_OPINION" property="cancelOpinion" />
        <result column="TECH_REQ" property="techReq" />
        <result column="STATUS" property="status" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="MTASK_FILE" property="mtaskFile" />
        <result column="CONTR_ID" property="contrId" />
        <result column="PLACE" property="place" />
        <result column="MAJOR_PROJECT" property="majorProject" />
        <result column="PRJ_ORG_NAME" property="prjOrgName" />
        <result column="PRJ_ORG_CODE" property="prjOrgCode" />
        <result column="TOTAL_COUNT" property="totalCount" />
        <result column="ACCPT_COUNT" property="accptCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        MTASK_ID, MTASK_CODE, MTASK_NAME, MNT_ORG_ID, MNG_DEPT_NM, ASKED_START_DATE, ASKED_COMPL_DATE, MNTN_ORG_NAME, EMERGY_DEGREE, SEND_USER, SEND_TIME, RECEIVE_USER, RECEIVE_TIME, CANCEL_OPINION, TECH_REQ, STATUS, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, PROCESSINSTID, MTASK_FILE, CONTR_ID, PLACE, MAJOR_PROJECT, PRJ_ORG_NAME, PRJ_ORG_CODE, TOTAL_COUNT, ACCPT_COUNT
    </sql>
    <select id="selectPartnames" resultType="com.hualu.app.module.mems.task.dto.DmTaskPartnameDto">
        select * from (select ROW_NUMBER() OVER (PARTITION BY PROCESSINSTID ORDER BY CREATETIME DESC) rn,
        CURRENTSTATE,
        PROCESSINSTID,
        PARTINAME
        from bps.WFWORKITEM ${ew.customSqlSegment}) v where v.CURRENTSTATE != '12' and v.rn = 1
    </select>
    <select id="selectOvers" resultType="com.hualu.app.module.mems.task.dto.DmTaskOverDto">
        select tt.mtask_id,
        max(is_pass) over_time,
        sum(decode(tt.st, 3, 1, 0)) || '' as accpt_num,
        count(tt.mtask_dtl_id) || '' as total_num
        from (SELECT distinct t.mtask_id,
        (case
        when SYSDATE &lt;= t.asked_compl_date then 0
        when to_date(a.repair_date) &lt;= t.asked_compl_date then 0
        else 1 end) as is_pass,
        ac.status as st,
        d.mtask_dtl_id
        FROM memsdb.dm_task t
        left join memsdb.dm_task_detail d on t.mtask_id = d.mtask_id
        left join memsdb.dm_task_accpt_detail a on a.mtask_dtl_id = d.mtask_dtl_id
        left join memsdb.dm_task_accpt ac on ac.mtask_accpt_id = a.mtask_accpt_id
        ${ew.customSqlSegment} ) tt
        group by tt.mtask_id
    </select>
    <select id="selectNextCode" resultType="java.lang.Integer">
        <bind name="pattern" value="'%' + codePrefix + '%'" />
        select max(substr(MTASK_CODE, length(MTASK_CODE) - 2))
        from DM_TASK t
        where MTASK_CODE like #{pattern}
          and t.MNT_ORG_ID = #{orgId}
    </select>
    <select id="selectPrjOrgs" resultType="com.hualu.app.module.mems.task.dto.DmTaskPrjOrgDto">
        select rd.prj_org_code  as org_code,
               org.org_fullname as ORG_NAME,
               org.org_fullname as ORG_FULLNAME,
               ORG.ORG_NAME     as sname
        from GDGS.FW_RIGHT_DATA_PERMISSION rd
                 inner join GDGS.FW_RIGHT_ORG org on org.org_code = rd.PRJ_ORG_CODE
                 inner join gdgs.FW_RIGHT_USER ru on rd.PRJ_ORG_CODE = ru.ORG_ID or rd.OPRT_ORG_CODE = ru.ORG_ID
        where ru.USER_CODE = #{userCode}
          and org.is_deleted = '0'
        group by rd.prj_org_code, org.org_fullname, ORG.ORG_NAME
        order by rd.prj_org_code
    </select>

</mapper>
