<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.notice.mapper.DmNoticeDetailMapper">

  <select id="getNoticeList" resultType="com.hualu.app.module.mems.notice.entity.DmNoticeDetail">
    select (select dic.ATTRIBUTE_VALUE
            from GDGS.BASE_DATATHIRD_DIC dic
            where dic.ATTRIBUTE_ITEM = 'DSS_STATUS'
              and dic.ATTRIBUTE_ACTIVE = 0
              and dic.ATTRIBUTE_CODE = nvl(f.REPAIR_STATUS, 0) and ROWNUM = 1) repairStatusName,
      (select dic.ATTRIBUTE_VALUE
       from GDGS.BASE_DATATHIRD_DIC dic
       where dic.ATTRIBUTE_ITEM = 'LINE_DIRECT'
         and dic.ATTRIBUTE_ACTIVE = 0
         and dic.ATTRIBUTE_CODE = d.LINE_DIRECT and ROWNUM = 1) lineDirectName,
      (select dic.ATTRIBUTE_VALUE
       from GDGS.BASE_DATATHIRD_DIC dic
       where dic.ATTRIBUTE_ITEM = 'FACILITY_CAT'
         and dic.ATTRIBUTE_ACTIVE = 0
         and dic.ATTRIBUTE_CODE = d.FACILITY_CAT and ROWNUM = 1) facilityCatName,
      (select dt.dss_type_name from MEMSDB.DSS_TYPE_NEW dt where dt.DSS_TYPE = d.DSS_TYPE and ROWNUM = 1) dssTypeName,
      (select dic.ATTRIBUTE_VALUE
       from GDGS.BASE_DATATHIRD_DIC dic
       where dic.ATTRIBUTE_ITEM = 'DSS_DEGREE'
         and dic.ATTRIBUTE_ACTIVE = 0
         and dic.ATTRIBUTE_CODE = d.DSS_DEGREE and ROWNUM = 1) dssDegreeName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'LANE_APP'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.LANE and ROWNUM = 1) laneName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'DSS_QUALITY'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.DSS_QUALITY and ROWNUM = 1) dssQualityName,
        (select memsdb.zpLZH(d.dss_id, 0) from dual) as fileIds,
      d.*
    from MEMSDB.DM_NOTICE_DETAIL d
           left join MEMSDB.DSS_INFO f on d.DSS_ID = f.DSS_ID
    where d.NOTICE_ID = #{noticeId} order by d.found_date desc
  </select>

  <select id="getRoadNoticeDetail"
    resultType="com.hualu.app.module.mems.notice.entity.DmNoticeDetail">
    select (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'DSS_STATUS'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = nvl(f.REPAIR_STATUS, 0) and ROWNUM = 1) repairStatusName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'LINE_DIRECT'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.LINE_DIRECT and ROWNUM = 1) lineDirectName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'FACILITY_CAT'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.FACILITY_CAT and ROWNUM = 1) facilityCatName,
    (select dt.dss_type_name from MEMSDB.DSS_TYPE_NEW dt where dt.DSS_TYPE = d.DSS_TYPE and ROWNUM = 1) dssTypeName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'DSS_DEGREE'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.DSS_DEGREE and ROWNUM = 1) dssDegreeName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'LANE_APP'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.LANE and ROWNUM = 1) laneName,
    (select dic.ATTRIBUTE_VALUE
    from GDGS.BASE_DATATHIRD_DIC dic
    where dic.ATTRIBUTE_ITEM = 'DSS_QUALITY'
    and dic.ATTRIBUTE_ACTIVE = 0
    and dic.ATTRIBUTE_CODE = d.DSS_QUALITY and ROWNUM = 1) dssQualityName,
    (select memsdb.zpLZH(d.dss_id, 0) from dual) as fileIds,
    d.*
    from MEMSDB.DM_NOTICE_DETAIL d
    left join MEMSDB.DSS_INFO f on d.DSS_ID = f.DSS_ID
    where d.NOTICE_DTL_ID = #{noticeDetailId} and ROWNUM = 1
  </select>
</mapper>