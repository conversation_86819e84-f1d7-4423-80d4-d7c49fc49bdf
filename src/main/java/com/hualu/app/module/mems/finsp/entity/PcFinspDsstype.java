package com.hualu.app.module.mems.finsp.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * <p>
 * 结构物排查病害表 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PcFinspDsstype对象", description="结构物排查病害表")
public class PcFinspDsstype implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty(value = "父类ID")
    @TableField("PID")
    private String pid;

    @ApiModelProperty(value = "类型(BJ:部件，GJ:构件，DSS:病害类型)")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "设施类型(BP、HD)")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "部件ID")
    @TableField("PART_ID")
    private String partId;
}
