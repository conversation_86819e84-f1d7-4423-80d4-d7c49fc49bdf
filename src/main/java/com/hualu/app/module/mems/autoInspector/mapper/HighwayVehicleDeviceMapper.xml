<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayVehicleDeviceMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayVehicleDevice">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="plateNo" column="PLATE_NO" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="VARCHAR"/>
            <result property="videoUrl" column="VIDEO_URL" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="clientId" column="CLIENT_ID" jdbcType="VARCHAR"/>
            <result property="aiotType" column="AIOT_TYPE" jdbcType="VARCHAR"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="serverStatus" column="SERVER_STATUS" jdbcType="VARCHAR"/>
            <result property="aiotStatus" column="AIOT_STATUS" jdbcType="VARCHAR"/>
            <result property="appId" column="APP_ID" jdbcType="VARCHAR"/>
            <result property="videoStatus" column="VIDEO_STATUS" jdbcType="VARCHAR"/>
            <result property="inspectingFlag" column="INSPECTING_FLAG" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PLATE_NO,STATUS,
        VIDEO_URL,REMARK,CLIENT_ID,
        AIOT_TYPE,CREATED_TIME,UPDATED_TIME,
        SERVER_STATUS,AIOT_STATUS,APP_ID,
        VIDEO_STATUS,INSPECTING_FLAG
    </sql>
</mapper>
