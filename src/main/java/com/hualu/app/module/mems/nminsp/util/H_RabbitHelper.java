package com.hualu.app.module.mems.nminsp.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.mems.nminsp.dto.NmDinspEmptDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.hualu.app.module.mems.nminsp.rabbit.DinspMessageSender;
import com.hualu.app.module.mems.nminsp.rabbit.GpsMessageSender;
import com.tg.dev.api.context.CustomApplicationContextHolder;

public class H_RabbitHelper {

    /**
     * 推送日常巡查单
     * @param emptDto
     */
    public static void sendDinspMessage(NmDinspEmptDto emptDto){
        if (emptDto == null){
            return;
        }
        DinspMessageSender bean = CustomApplicationContextHolder.getBean(DinspMessageSender.class);
        bean.sendMessage(emptDto);
    }

    public static void sendGpsMessage(NmDinspGps dinspGps) {
        if (ObjectUtil.isEmpty(dinspGps)){
            return;
        }
        GpsMessageSender bean = CustomApplicationContextHolder.getBean(GpsMessageSender.class);
        bean.sendMessage(dinspGps);
    }
}
