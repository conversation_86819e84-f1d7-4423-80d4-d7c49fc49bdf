package com.hualu.app.module.mems.nminsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinspRecord;
import com.hualu.app.module.mems.nminsp.vo.NmFinspRel;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 新版经常检查记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface NmFinspRecordService extends IService<NmFinspRecord> {

    void deleteRecordByFinspIds(List<String> inspIds);

    List<NmFinspRecord> selectRecordByFinspId(String finspId);

    List<NmFinspRecord> selectRecordByFinspId(List<String> finspIds);

    void saveOrUpdateRecord(NmFinspRecord entity,boolean isApp);

    void deleteRecord(List<String> idList);

    void showView(List<NmFinspRecord> records, NmFinsp entity);

    String getLastfinspIdByStruct(String structId, String facilityCat);

    void copyFinspFetail2NewOne(String lastfinspIdByStruct, String finspId, String facilityCat);
    Map<String, List<NmFinspRecord>> copyFinspFetail2NewOne(List<NmFinspRel> relList, Map<String,String> structId2finspIdMap);

    void refreshResult4QL();

    List<NmFinspRecord> selectViewRecord(List<String> dssIds);

    List<NmFinspRecord> selectRecordDtlByFinspId(String finspId);

    List<NmFinspRel> getLastfinspIdListByStructList(String collectStructIds, String facilityCat);
}
