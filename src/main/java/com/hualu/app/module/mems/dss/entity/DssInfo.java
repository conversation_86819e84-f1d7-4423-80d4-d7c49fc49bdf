package com.hualu.app.module.mems.dss.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DssInfo对象", description="")
public class DssInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("DSS_ID")
    private String dssId;

    @TableField("DSS_CODE")
    private String dssCode;

    @TableField("RP_INTRVL_ID")
    private String rpIntrvlId;

    @TableField("STAKE")
    private Double stake;

    @NotBlank(message = "设施分类不能为空")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @NotBlank(message = "病害类型不能为空")
    @TableField("DSS_TYPE")
    private String dssType;

    @TableField("DSS_DEGREE")
    private String dssDegree;

    @TableField("MNTN_ADVICE")
    private String mntnAdvice;

    @TableField("STRUCT_ID")
    private String structId;

    @TableField("STRUCT_TYPE")
    private String structType;

    @TableField("STRUCT_PART_ID")
    private String structPartId;

    @TableField("STRUCT_COMP_ID")
    private String structCompId;

    @ApiModelProperty(value = "车道")
    @TableField("LANE")
    private String lane;

    @TableField("DSS_POSITION")
    private String dssPosition;

    @TableField("DSS_CAUSE")
    private String dssCause;

    @TableField("DSS_DESC")
    private String dssDesc;

    @TableField("DSS_L")
    private Double dssL;

    @TableField("DSS_L_UNIT")
    private String dssLUnit;

    @TableField("DSS_W")
    private Double dssW;

    @TableField("DSS_W_UNIT")
    private String dssWUnit;

    @TableField("DSS_D")
    private Double dssD;

    @TableField("DSS_D_UNIT")
    private String dssDUnit;

    @TableField("DSS_N")
    private Double dssN;

    @TableField("DSS_N_UNIT")
    private String dssNUnit;

    @TableField("DSS_A")
    private Double dssA;

    @TableField("DSS_A_UNIT")
    private String dssAUnit;

    @TableField("DSS_V")
    private Double dssV;

    @TableField("DSS_V_UNIT")
    private String dssVUnit;

    @TableField("DSS_P")
    private Double dssP;

    @TableField("DSS_G")
    private Double dssG;

    @TableField("DSS_IMP_FLAG")
    private Integer dssImpFlag;

    @TableField("DSS_QUALITY")
    private Integer dssQuality;

    @TableField("HIS_DSS_ID")
    private String hisDssId;

    @TableField("DSS_SOURCE")
    private Integer dssSource;

    @TableField("REL_TASK_CODE")
    private String relTaskCode;

    //发现日期
    @NotNull(message = "发现日期不能为空")
    @TableField("FOUND_DATE")
    private Date foundDate;

    @TableField("REPAIR_STATUS")
    private Integer repairStatus;

    @TableField("REPAIR_DATE")
    private Date repairDate;

    @TableField("DEAL_STATUS")
    private Integer dealStatus;

    @TableField("DEAL_MEASURE")
    private Integer dealMeasure;

    @TableField("DEAL_OPINION")
    private String dealOpinion;

    @TableField("DEAL_PRSN")
    private String dealPrsn;

    @TableField("DEAL_DATE")
    private LocalDateTime dealDate;

    @TableField("DEAL_BILL_ID")
    private String dealBillId;

    @TableField("X")
    private Double x;

    @TableField("Y")
    private Double y;

    //任务单ID
    @NotBlank(message = "任务单ID不能为空")
    @TableField("MTASK_ID")
    private String mtaskId;

    @TableField("MTASK_ACCPT_ID")
    private String mtaskAccptId;

    //路线ID
    @NotBlank(message = "路线ID不能为空")
    @TableField("MAIN_ROAD_ID")
    private String mainRoadId;

    @TableField("RAMP_ID")
    private String rampId;

    @TableField("ISIGN")
    private String isign;

    @TableField("FIND_DSS_USER_NAME")
    private String findDssUserName;

    @ApiModelProperty(value = "隧道部件")
    @TableField("TUNNEL_MOUTH")
    private String tunnelMouth;

    @ApiModelProperty(value = "起点高度")
    @TableField("START_HIGH")
    private Double startHigh;

    @ApiModelProperty(value = "止点高度")
    @TableField("END_HIGH")
    private Double endHigh;

    @ApiModelProperty(value = "起点桩号")
    @TableField("START_STAKE_NUM")
    private Double startStakeNum;

    @ApiModelProperty(value = "止点桩号")
    @TableField("END_STAKE_NUM")
    private Double endStakeNum;

    @ApiModelProperty(value = "桩号高度")
    @TableField("STAKE_HIGH")
    private Double stakeHigh;

    @ApiModelProperty(value = "衬砌部位类型")
    @TableField("LINING_STRUCTURE")
    private String liningStructure;

    @ApiModelProperty(value = "标线部位类型")
    @TableField("MARKING_LINE_POSITION")
    private String markingLinePosition;

    @ApiModelProperty(value = "检测手段 0为手动检测 1为自动检测")
    @TableField("MEANS")
    private String means;

    @TableField("FINISH_STAKE")
    private Double finishStake;

    @NotBlank(message = "路线方向不能为空")
    @ApiModelProperty(value = "方向")
    @TableField("LANE_DIRECTION")
    private String laneDirection;

    @TableField("YEAR")
    private Integer year;

    @TableField("UNIT_MARGE_ID")
    private String unitMargeId;

    @ApiModelProperty(value = "病害描述")
    @TableField("STRUCT_DESC")
    private String structDesc;

    @TableField("X_GD")
    private Double xGd;

    @TableField("Y_GD")
    private Double yGd;

    @TableField("DIR")
    private Double dir;

    @TableField("ROUTECODE")
    private String routecode;

    @TableField("ROUTEVERSION")
    private String routeversion;

    @TableField("RL_STAKE_NEW")
    private Double rlStakeNew;

    @TableField("LINEDIRECT")
    private String linedirect;

    @TableField("RL_FINISH_STAKE")
    private Double rlFinishStake;

    @ApiModelProperty(value = "病害修补不足；裂缝修补差")
    @TableField("CRACKPOOR")
    private Double crackpoor;

    @TableField("OPERT_ORG_ID")
    private String opertOrgId;

    @ApiModelProperty(value = "闭合方式（1：任务单闭合，2：专项闭合）")
    @TableField("CLOSE_TYPE")
    private String closeType;
}
