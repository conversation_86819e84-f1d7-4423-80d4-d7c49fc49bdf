<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.finsp.mapper.PcFinspDsstypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.finsp.entity.PcFinspDsstype">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="PID" property="pid" />
        <result column="TYPE" property="type" />
        <result column="FACILITY_CAT" property="facilityCat" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, NAME, PID, TYPE, FACILITY_CAT
    </sql>
    <select id="recursionChilds" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto">
        SELECT
            A.ID AS dss_Type_Id,
            A.NAME AS DSS_TYPE_NAME,
            DECODE(P.ID, PART.ID, NULL,P.ID) AS GROUP_ID,
            DECODE(P.NAME, PART.NAME, NULL,P.NAME) AS GROUP_NAME,
            PART.NAME AS PART_TYPE_NAME,
            A.part_id as part_type,
            A.pid
        FROM
            PC_FINSP_DSSTYPE A
                JOIN PC_FINSP_DSSTYPE P ON A.PID = P.ID
                JOIN PC_FINSP_DSSTYPE PART ON A.PART_ID = PART.ID
        WHERE A.PART_ID = #{partId} order by P.ID,A.ID
    </select>
    <select id="listByDsstypeIds" resultType="com.hualu.app.module.mems.finsp.dto.PcFinspDssTypeDto">
        SELECT
            A.ID AS dss_Type_Id,
            A.NAME AS DSS_TYPE_NAME,
            DECODE(P.ID, PART.ID, NULL,P.ID) AS GROUP_ID,
            DECODE(P.NAME, PART.NAME, NULL,P.NAME) AS GROUP_NAME,
            PART.NAME AS PART_TYPE_NAME,
            A.part_id as part_type
        FROM
            PC_FINSP_DSSTYPE A
            JOIN PC_FINSP_DSSTYPE P ON A.PID = P.ID
            JOIN PC_FINSP_DSSTYPE PART ON A.PART_ID = PART.ID
        WHERE A.ID in
        <foreach collection="dsstypeIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>
