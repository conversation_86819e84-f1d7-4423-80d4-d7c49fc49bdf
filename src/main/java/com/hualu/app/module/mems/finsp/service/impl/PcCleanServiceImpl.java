package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.mems.finsp.entity.PcClean;
import com.hualu.app.module.mems.finsp.entity.PcFinspImage;
import com.hualu.app.module.mems.finsp.mapper.PcCleanMapper;
import com.hualu.app.module.mems.finsp.service.PcCleanService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.mems.finsp.service.PcFinspGpsService;
import com.hualu.app.module.mems.finsp.service.PcFinspImageService;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 打草清疏 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Service
public class PcCleanServiceImpl extends ServiceImpl<PcCleanMapper, PcClean> implements PcCleanService {

    @Autowired
    private PcFinspImageService imageService;

    @Autowired
    PcFinspGpsService gpsService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateClean(PcClean bean, MultipartFile[] beforeFiles, MultipartFile[] afterFiles) {
        boolean isAdd = StrUtil.isBlank(bean.getCleanId()) ? true : false;
        if (isAdd) {
            bean.setCleanId(H_KeyWorker.nextIdToString());
            bean.setMntOrgId(CustomRequestContextHolder.getOrgIdString());
            bean.setInspDate(new Date());
        }
        //维修前照片
        List<String> bfFiles = H_UploadHelper.uploadFile(beforeFiles);
        if (CollectionUtil.isNotEmpty(bfFiles)){
            List<PcFinspImage> images = Lists.newArrayList();
            for (String bfFile : bfFiles) {
                PcFinspImage image = new PcFinspImage();
                image.setRecordId(bean.getCleanId()).setImageType("1").setImageId(bfFile);
                images.add(image);
            }
            imageService.saveBatch(images);
        }

        //维修后照片
        List<String> afFiles = H_UploadHelper.uploadFile(afterFiles);
        if (CollectionUtil.isNotEmpty(afFiles)){
            List<PcFinspImage> images = Lists.newArrayList();
            for (String afFile : afFiles) {
                PcFinspImage image = new PcFinspImage();
                image.setRecordId(bean.getCleanId()).setImageType("3").setImageId(afFile);
                images.add(image);
            }
            imageService.saveBatch(images);
        }
        this.saveOrUpdate(bean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeClean(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)){
            return;
        }
        imageService.removeByRecordIds(ids);
        gpsService.removeByFinspIds(ids);
        this.removeByIds(ids);
    }
}
