<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mems.autoInspector.mapper.HighwayDefectInfoMapper">

    <resultMap id="BaseResultMap" type="com.hualu.app.module.mems.autoInspector.entity.HighwayDefectInfo">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="defectType" column="DEFECT_TYPE" jdbcType="VARCHAR"/>
            <result property="equipmentType" column="EQUIPMENT_TYPE" jdbcType="VARCHAR"/>
            <result property="createdTime" column="CREATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="UPDATED_TIME" jdbcType="TIMESTAMP"/>
            <result property="state" column="STATE" jdbcType="VARCHAR"/>
            <result property="stakeStart" column="STAKE_START" jdbcType="VARCHAR"/>
            <result property="stakeEnd" column="STAKE_END" jdbcType="VARCHAR"/>
            <result property="geomType" column="GEOM_TYPE" jdbcType="VARCHAR"/>
            <result property="longitude" column="LONGITUDE" jdbcType="DECIMAL"/>
            <result property="latitude" column="LATITUDE" jdbcType="DECIMAL"/>
            <result property="targetArea" column="TARGET_AREA" jdbcType="DECIMAL"/>
            <result property="targetLen" column="TARGET_LEN" jdbcType="DECIMAL"/>
            <result property="snapshotId" column="SNAPSHOT_ID" jdbcType="VARCHAR"/>
            <result property="taskId" column="TASK_ID" jdbcType="VARCHAR"/>
            <result property="segmentId" column="SEGMENT_ID" jdbcType="VARCHAR"/>
            <result property="segmentName" column="SEGMENT_NAME" jdbcType="VARCHAR"/>
            <result property="inspectDirection" column="INSPECT_DIRECTION" jdbcType="VARCHAR"/>
            <result property="dataFrom" column="DATA_FROM" jdbcType="VARCHAR"/>
            <result property="warningFlag" column="WARNING_FLAG" jdbcType="DECIMAL"/>
            <result property="equipmentId" column="EQUIPMENT_ID" jdbcType="VARCHAR"/>
            <result property="transformation" column="TRANSFORMATION" jdbcType="DECIMAL"/>
            <result property="dataStatus" column="DATA_STATUS" jdbcType="DECIMAL"/>
            <result property="objDistance" column="OBJ_DISTANCE" jdbcType="DECIMAL"/>
            <result property="isCalibrated" column="IS_CALIBRATED" jdbcType="DECIMAL"/>
            <result property="laneNum" column="LANE_NUM" jdbcType="VARCHAR"/>
            <result property="objectType" column="OBJECT_TYPE" jdbcType="VARCHAR"/>
            <result property="objectSubtype" column="OBJECT_SUBTYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,DEFECT_TYPE,EQUIPMENT_TYPE,
        CREATED_TIME,UPDATED_TIME,STATE,
        STAKE_START,STAKE_END,GEOM_TYPE,
        LONGITUDE,LATITUDE,TARGET_AREA,
        TARGET_LEN,SNAPSHOT_ID,TASK_ID,
        SEGMENT_ID,SEGMENT_NAME,INSPECT_DIRECTION,
        DATA_FROM,WARNING_FLAG,EQUIPMENT_ID,
        TRANSFORMATION,DATA_STATUS,OBJ_DISTANCE,
        IS_CALIBRATED,LANE_NUM,OBJECT_TYPE,
        OBJECT_SUBTYPE
    </sql>
</mapper>
