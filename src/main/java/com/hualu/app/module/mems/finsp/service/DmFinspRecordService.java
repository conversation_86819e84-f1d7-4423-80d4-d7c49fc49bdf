package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-14
 */
public interface DmFinspRecordService extends IService<DmFinspRecord> {

    /**
     * 获取病害信息
     * @param id
     * @return
     */
    DmFinspRecord getDmFinspRecordById(String id);

    /**
     * 删除经常检查病害
     * @param dssIds
     */
    void deleteFinspRecordByIds(String[] dssIds);

    /**
     * 添加或修改经常检查病害
     * @param dmFinspRecord
     */
    DmFinspRecord saveOrUpdateFinspRecord(DmFinspRecord dmFinspRecord);

    /**
     * 删除病害数据
     * @param finspId
     */
    void delDmFinspRecord(String finspId);

    /**
     * 上次经常检查单未维修病害，添加当下一张单中
     * @param dmFinsp
     * @param lastFinspId
     */
    void insertRecord(DmFinsp dmFinsp, String lastFinspId);

    /**
     * 查询历史记录，用于拉取上个经常检查的病害，导入到当前经常检查单中
     * @param finspId
     * @return
     */
    List<DmFinspRecord> queryHisRecordByFinspId(String finspId, String facilityCat);

    /**
     * 查询病害明细（用于前端病害显示）
     * @param finspId
     * @return
     */
    IPage<DmFinspRecord> selectViewRecordByFinspId(String finspId);


    /**
     * 查询病害明细（用于检查病害录入到病害库中）
     * @param finspId
     * @return
     */
    List<DmFinspRecord> selectDssRecordByFinspId(String finspId);

    /**
     * 查询结构物养护历史
     * @param structId
     * @param facilityCat
     * @return
     */
    IPage selectHisRecordByStructId(String structId, String facilityCat);
}
