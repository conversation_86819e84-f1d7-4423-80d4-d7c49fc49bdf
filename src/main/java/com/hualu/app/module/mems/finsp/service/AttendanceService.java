package com.hualu.app.module.mems.finsp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.mems.finsp.dto.SlopeBatchAttentionDto;
import com.hualu.app.module.mems.finsp.entity.Attendance;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-03
 */
public interface AttendanceService extends IService<Attendance> {

    /**
     * 检查是否打卡
     * @param userId
     * @param structId
     * @return
     */
    boolean checkIsAttendance(String userId,String structId);

    /**
     * 打卡签到
     * @param attendance
     */
    void attendance(Attendance attendance);

    /**
     * 上一次打卡信息
     * @param userId
     * @param structId
     * @return
     */
    Attendance getLastAttendance(String userId,String structId);

    /**
     * 获取打卡信息
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param type      设施分类 QL,HD,BP,SD 分别表示桥梁、涵洞、边坡、隧道
     * @param page
     * @param pageSize
     * @return
     */
    IPage<Attendance> getAttentionList(String startDate, String endDate, String type, int page,
        int pageSize);

    RestResult<List<SlopeBatchAttentionDto>> batchSaveAttendance(String batchId,
        String validLocation, Double lat, Double lng);
}
