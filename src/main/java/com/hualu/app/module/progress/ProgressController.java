package com.hualu.app.module.progress;

import com.hualu.app.comm.RestResult;
import com.tg.dev.excelimport.logger.ImportLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/progress")
public class ProgressController {

    @Autowired
    ImportLogger importLogger;

    /**
     * 展示进度信息
     * @return
     */
    @GetMapping(value = "/percent")
    public RestResult getProgress() {
        return RestResult.success(importLogger.getProgressInfo());
    }
}
