package com.hualu.app.module.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.entity.FwRightUser;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface FwRightUserService extends IService<FwRightUser> {


    /**
     * 是否业主单位 1:是 ，0：否
     * @param userCode
     * @return
     */
    Integer isMntPerson(String userCode);

    /**
     * 查询当前部门名称
     * @param userId
     * @return
     */
    String getDeptName(String userId);
    /**
     * 是否三级单位系统管理员
     * @return
     */
    boolean isSystemManager();

    UserLoginDto loginUser(String userCode);

    /**
     * 根据用户账号，查询组织机构
     * @param userCode
     * @return
     */
    String selectOrgId(String userCode);

    /**
     * 查询用户集合
     * @param userIds
     * @return
     */
    List<FwRightUser> selectUsersByIds(List<String> userIds);

    List<FwRightUser> selectUsersByCode(List<String> userCodes);

    /**
     * 查询用户角色
     * @param userCode
     * @return
     */
    List<FwRightUser> selectRoleTypeByUserCode(String userCode);

    /**
     * 查询流程审批用户信息
     * @param orgId
     * @param roleName
     * @param userName`
     * @return
     */
    List<FwRightUser> selectBpsUserByRoleName(String orgId, String roleName, String userName);

    /**
     * 根据组织机构及用户名称查询用户信息
     * @param orgId
     * @param userName
     * @return
     */
    List<FwRightUser> selectBpsUserByOrgId(String orgId, String userName);

    /**
     * 查询集团范围内的用户账号
     * @param orgIds
     * @return
     */
    List<FwRightUser> selectJtUserByOrgIds(List<String> orgIds);
}
