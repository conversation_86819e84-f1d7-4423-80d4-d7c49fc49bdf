package com.hualu.app.module.platform.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.platform.entity.FwPlSystemParams;
import com.hualu.app.module.platform.mapper.FwPlSystemParamsMapper;
import com.hualu.app.module.platform.service.FwPlSystemParamsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 系统参数表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@DS("gdgsDs")
@Service
public class FwPlSystemParamsServiceImpl extends ServiceImpl<FwPlSystemParamsMapper, FwPlSystemParams> implements FwPlSystemParamsService {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public String selectSystemParamByParamCode(String paramCode) {
        LambdaQueryWrapper<FwPlSystemParams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FwPlSystemParams::getParamCode,paramCode);
        FwPlSystemParams params = baseMapper.selectOne(queryWrapper);
        if (params != null){
            return params.getParamValue();
        }
        return null;
    }

    @Override
    public boolean isAllDataUser(String userCode) {
        String alldataUser = selectSystemParamByParamCode("FW_ALLDATA_USER");
        if (StrUtil.isNotBlank(alldataUser) && StrUtil.isNotBlank(userCode)){
            String[] split = userCode.split(",");
            for (String s : split) {
                if (userCode.equalsIgnoreCase(s)){
                    return true;
                }
            }
        }
        return false;
    }
}
