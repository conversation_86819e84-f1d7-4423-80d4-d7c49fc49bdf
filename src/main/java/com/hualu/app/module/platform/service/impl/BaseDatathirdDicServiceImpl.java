package com.hualu.app.module.platform.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.mapper.BaseDatathirdDicMapper;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.tg.dev.api.context.CustomHttpRequestLocal;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 信息资源规划C类字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@DS("gdgsDs")
@Service
public class BaseDatathirdDicServiceImpl extends ServiceImpl<BaseDatathirdDicMapper, BaseDatathirdDic> implements BaseDatathirdDicService {

    private static final String DIC = "pt:dic:";
    @Override
    public List<BaseDatathirdDic> listDicByItem(String item) {
        String cacheKey = DIC + item;
        synchronized (cacheKey) {
            List<BaseDatathirdDic> datas = (List<BaseDatathirdDic>) CustomHttpRequestLocal.getRequestValue(cacheKey);
            if (CollectionUtil.isEmpty(datas)) {
                LambdaQueryWrapper<BaseDatathirdDic> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(BaseDatathirdDic::getAttributeItem, item);
                queryWrapper.orderByAsc(BaseDatathirdDic::getSeqNo);
                queryWrapper.select(BaseDatathirdDic::getAttributeCode, BaseDatathirdDic::getAttributeValue);
                List<BaseDatathirdDic> dics = list(queryWrapper);
                if (CollectionUtil.isNotEmpty(dics)) {
                    datas = dics;
                    CustomHttpRequestLocal.setRequestValue(cacheKey, datas);
                }
            }
            if (CollectionUtil.isEmpty(datas)){
                datas = Lists.newArrayList();
            }
            return datas;
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public String getDicName(String item, String code) {
        if (StrUtil.isBlank(code)){
            return "";
        }
        List<BaseDatathirdDic> dics = listDicByItem(item);
        String dicName = code;
        if (CollectionUtil.isNotEmpty(dics)){
            for (BaseDatathirdDic dic : dics) {
                if (dic.getAttributeCode().equals(code)){
                    dicName = dic.getAttributeValue();
                    break;
                }
            }
        }
        return dicName;
    }

    @Override
    public String getDicName(List<BaseDatathirdDic> dics, String code) {
        String dicName = code;
        if (CollectionUtil.isNotEmpty(dics)){
            for (BaseDatathirdDic dic : dics) {
                if (dic.getAttributeCode().equals(code)){
                    dicName = dic.getAttributeValue();
                    break;
                }
            }
        }
        return dicName;
    }
}
