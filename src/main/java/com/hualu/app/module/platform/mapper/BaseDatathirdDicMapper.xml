<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.platform.mapper.BaseDatathirdDicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.platform.entity.BaseDatathirdDic">
        <id column="DATATHIRD_DIC_ID" property="datathirdDicId" />
        <result column="PARENT_ID" property="parentId" />
        <result column="ATTRIBUTE_ITEM" property="attributeItem" />
        <result column="ATTRIBUTE_DESC" property="attributeDesc" />
        <result column="ATTRIBUTE_CODE" property="attributeCode" />
        <result column="ATTRIBUTE_VALUE" property="attributeValue" />
        <result column="ATTRIBUTE_ACTIVE" property="attributeActive" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="SEQ_NO" property="seqNo" />
        <result column="XQ_VALUE" property="xqValue" />
        <result column="YR_CODE" property="yrCode" />
        <result column="YR_VALUE" property="yrValue" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DATATHIRD_DIC_ID, PARENT_ID, ATTRIBUTE_ITEM, ATTRIBUTE_DESC, ATTRIBUTE_CODE, ATTRIBUTE_VALUE, ATTRIBUTE_ACTIVE, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, SEQ_NO, XQ_VALUE, YR_CODE, YR_VALUE
    </sql>

</mapper>
