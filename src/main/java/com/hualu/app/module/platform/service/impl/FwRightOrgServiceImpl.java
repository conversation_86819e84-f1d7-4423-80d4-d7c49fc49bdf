package com.hualu.app.module.platform.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.platform.dto.PtOrgItemDto;
import com.hualu.app.module.platform.entity.FwRightOrg;
import com.hualu.app.module.platform.mapper.FwRightOrgMapper;
import com.hualu.app.module.platform.service.FwRightOrgService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * <p>
 * 组织机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@DS("gdgsDs")
@Service
public class FwRightOrgServiceImpl extends ServiceImpl<FwRightOrgMapper, FwRightOrg> implements FwRightOrgService {


    @Override
    public Map<String, String> selectByOrgCodes(List<String> orgCodes) {
        LambdaQueryWrapper<FwRightOrg> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(FwRightOrg::getOrgCode, orgCodes);
        wrapper.eq(FwRightOrg::getIsEnable,1).eq(FwRightOrg::getIsDeleted,0);
        wrapper.select(FwRightOrg::getOrgCode,FwRightOrg::getOrgName);
        List<FwRightOrg> list = this.list(wrapper);
        Map<String, String> orgMap = list.stream().collect(Collectors.toMap(FwRightOrg::getOrgCode, FwRightOrg::getOrgName));
        return orgMap;
    }

    @Override
    public List<String> selectChildOprtOrgCodes(String orgCode) {
        List<String> orgCodes = baseMapper.selectChildOprtOrgCodes(orgCode);
        return orgCodes;
    }

    @Override
    public boolean isAuth(String orgCode) {
        List<String> list = baseMapper.queryAuthOrg(orgCode);
        if(list != null && list.size() > 0){
            return true;
        }
        return false;
    }

    @Override
    public List<Map<String, String>> queryOprtOrgCodes(String orgCode) {
        List<FwRightOrg> list = this.baseMapper.queryOprtOrgCodes(orgCode);
        return convertAndDistinct(list);
    }

    @Override
    public List<Map<String,String>> queryCheckOrgCodes(String orgCode) {
        return this.baseMapper.queryCheckOrgCodes(orgCode);
    }

    @Override
    public List<String> queryRoute(String orgCode) {
        List<FwRightOrg> list = this.baseMapper.queryOprtOrgCodes(orgCode);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        
        return list.stream()
            .map(FwRightOrg::getRemark)
            .filter(remark -> remark != null && !remark.isEmpty())
            .distinct()
            .collect(Collectors.toList());
    }

    public List<Map<String, String>> convertAndDistinct(List<FwRightOrg> list) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        return list.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(org -> org.getOrgCode() + ":" + org.getOrgName()))
                        ),
                        orgs -> orgs.stream()
                                .map(org -> {
                                    Map<String, String> map = new HashMap<>(2);
                                    map.put("orgCode", org.getOrgCode());
                                    map.put("orgName", org.getOrgName());
                                    return map;
                                })
                                .collect(Collectors.toList())
                ));
    }

    @Override
    public List<TreeNode> buildTree() {
        List<PtOrgItemDto> items = baseMapper.listRouteOrg();
        // 按 P_ORG_NAME 分组
        Map<String, List<PtOrgItemDto>> groupMap = new HashMap<>();
        for (PtOrgItemDto item : items) {
            groupMap.computeIfAbsent(item.getPorgName(), k -> new ArrayList<>())
                    .add(item);
        }

        // 构建树结构
        List<TreeNode> result = new ArrayList<>();
        for (Map.Entry<String, List<PtOrgItemDto>> entry : groupMap.entrySet()) {
            String pOrgName = entry.getKey();
            String porgCode = entry.getValue().get(0).getPorgCode();
            TreeNode parentNode = new TreeNode(pOrgName, porgCode); // 父节点

            // 添加子节点
            for (PtOrgItemDto item : entry.getValue()) {
                TreeNode childNode = new TreeNode(
                        item.getOrgName(),  // label 使用 orgName
                        item.getId()        // value 使用 id
                );
                parentNode.addChild(childNode);
            }

            result.add(parentNode);
        }

        return result;
    }


    // 树节点类
    public static class TreeNode {
        private String label;
        private String value;
        private List<TreeNode> children;

        public TreeNode(String label, String value) {
            this.label = label;
            this.value = value;
            this.children = new ArrayList<>();
        }

        public void addChild(TreeNode child) {
            this.children.add(child);
        }

        // Getters and setters
        public String getLabel() { return label; }
        public void setLabel(String label) { this.label = label; }
        public String getValue() { return value; }
        public void setValue(String value) { this.value = value; }
        public List<TreeNode> getChildren() { return children; }
        public void setChildren(List<TreeNode> children) { this.children = children; }
    }


}
