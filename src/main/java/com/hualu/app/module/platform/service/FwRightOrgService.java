package com.hualu.app.module.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.basedata.entity.BaseRouteLogic;
import com.hualu.app.module.platform.entity.FwRightOrg;
import com.hualu.app.module.platform.service.impl.FwRightOrgServiceImpl;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 组织机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface FwRightOrgService extends IService<FwRightOrg> {

    /**
     * 查询机构编码和机构名称
     * @param orgCodes
     * @return
     */
    Map<String,String> selectByOrgCodes(List<String> orgCodes);

    List<String> selectChildOprtOrgCodes(String orgCode);

    boolean isAuth(String orgCode);

    List<Map<String,String>> queryOprtOrgCodes(String orgCode);

    List<Map<String,String>> queryCheckOrgCodes(String orgCode);

    List<String> queryRoute(String orgCode);

    List<FwRightOrgServiceImpl.TreeNode> buildTree();

}
