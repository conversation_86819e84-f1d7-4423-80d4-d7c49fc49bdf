package com.hualu.app.module.platform.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;

import java.util.List;

/**
 * <p>
 * 信息资源规划C类字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
public interface BaseDatathirdDicService extends IService<BaseDatathirdDic> {

    List<BaseDatathirdDic> listDicByItem(String item);
    /**
     * 获取字典中文名称
     * @param item 字典分类
     * @param code 字典编码
     * @return
     */
    String getDicName(String item,String code);

    String getDicName(List<BaseDatathirdDic> datas,String code);
}
