package com.hualu.app.module.platform.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统参数表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="FwPlSystemParams对象", description="系统参数表")
public class FwPlSystemParams implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "参数代码")
    @TableField("PARAM_CODE")
    private String paramCode;

    @ApiModelProperty(value = "参数名称")
    @TableField("PARAM_NAME")
    private String paramName;

    @ApiModelProperty(value = "参数值")
    @TableField("PARAM_VALUE")
    private String paramValue;

    @ApiModelProperty(value = "参数描述")
    @TableField("PARAM_DESC")
    private String paramDesc;

    @ApiModelProperty(value = "参数类型")
    @TableField("PARAM_TYPE")
    private String paramType;

    @ApiModelProperty(value = "备用类型1")
    @TableField("REM_TYPE1")
    private String remType1;

    @ApiModelProperty(value = "备用类型2")
    @TableField("REM_TYPE2")
    private String remType2;

    @ApiModelProperty(value = "备用类型3")
    @TableField("REM_TYPE3")
    private String remType3;

    @ApiModelProperty(value = "备用字典4")
    @TableField("REM_TYPE4")
    private String remType4;

    @ApiModelProperty(value = "备用字符串字段1")
    @TableField("REM1_STRING")
    private String rem1String;

    @ApiModelProperty(value = "备用字符串字段2")
    @TableField("REM2_STRING")
    private String rem2String;

    @ApiModelProperty(value = "备用字符串字段3")
    @TableField("REM3_STRING")
    private String rem3String;

    @ApiModelProperty(value = "备用字符串字段4")
    @TableField("REM4_STRING")
    private String rem4String;

    @ApiModelProperty(value = "备用字符串字段")
    @TableField("REM5_STRING")
    private String rem5String;

    @ApiModelProperty(value = "备用字符串字段6")
    @TableField("REM6_STRING")
    private String rem6String;

    @ApiModelProperty(value = "备用时间字段7")
    @TableField("REM7_DATETIME")
    private LocalDateTime rem7Datetime;

    @ApiModelProperty(value = "备用时间字段8")
    @TableField("REM8_DATETIME")
    private LocalDateTime rem8Datetime;

    @ApiModelProperty(value = "备用整数字段9")
    @TableField("REM9_INT")
    private Double rem9Int;

    @ApiModelProperty(value = "备用整数字段10")
    @TableField("REM10_INT")
    private Double rem10Int;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;


}
