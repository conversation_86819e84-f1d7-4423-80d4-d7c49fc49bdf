package com.hualu.app.module.platform.controller;


import com.hualu.app.comm.RestResult;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * C类字典接口
 * <AUTHOR>
 * @since 2023-04-16
 */
@RestController
@RequestMapping("/baseDatathirdDic")
public class BaseDatathirdDicController {

    @Autowired
    BaseDatathirdDicService dicService;

    /**
     * 查询隧道车道（用于经常检查）
     * @return
     */
    @GetMapping("selectSdLane")
    public RestResult<List<BaseDatathirdDic>> selectSdLane(){
        List<BaseDatathirdDic> lane12 = dicService.listDicByItem("LANE12");
        return RestResult.success(lane12);
    }

    /**
     * 查询边坡类型
     * @return
     */
    @GetMapping("selectSlopeType")
    public RestResult<List<BaseDatathirdDic>> selectBpType(){
        List<BaseDatathirdDic> types = dicService.listDicByItem(H_BasedataHepler.DIC_BP_TYPE);
        return RestResult.success(types);
    }

    /**
     * 查询边坡位置
     * @return
     */
    @GetMapping("selectSlopePosition")
    public RestResult<List<BaseDatathirdDic>> selectBpPosition(){
        List<BaseDatathirdDic> types = dicService.listDicByItem(H_BasedataHepler.DIC_BP_POSITION);
        return RestResult.success(types);
    }

    /**
     * 查询边坡风险等级
     * @return
     */
    @GetMapping("selectBpFxdj")
    public RestResult<List<BaseDatathirdDic>> selectBpFxdj(){
        List<BaseDatathirdDic> types = dicService.listDicByItem(H_BasedataHepler.DIC_BP_FXDJ);
        return RestResult.success(types);
    }

    /**
     * 查询边坡灾害类型
     * @return
     */
    @GetMapping("selectBpZhType")
    public RestResult<List<BaseDatathirdDic>> selectBpZhType(){
        List<BaseDatathirdDic> types = dicService.listDicByItem(H_BasedataHepler.DIC_BP_ZHTYPE);
        return RestResult.success(types);
    }

    /**
     * 查询C类字典（通用）
     * @param item 字典分类
     * @return
     */
    @GetMapping("selectDics")
    public RestResult<List<BaseDatathirdDic>> selectDics(String item){
        List<BaseDatathirdDic> dics = dicService.listDicByItem(item);
        return RestResult.success(dics);
    }
}
