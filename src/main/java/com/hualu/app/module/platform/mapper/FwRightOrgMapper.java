package com.hualu.app.module.platform.mapper;

import com.hualu.app.module.platform.dto.PtOrgItemDto;
import com.hualu.app.module.platform.entity.FwRightOrg;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 组织机构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
public interface FwRightOrgMapper extends BaseMapper<FwRightOrg> {

    List<String> selectChildOprtOrgCodes(@Param("orgCode") String orgCode);

    List<String> queryAuthOrg(@Param("orgCode") String orgCode);

    List<FwRightOrg> queryOprtOrgCodes(String orgCode);

    List<Map<String,String>> queryCheckOrgCodes(String orgCode);

    List<PtOrgItemDto> listRouteOrg();

}
