package com.hualu.app.module.platform.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.entity.FwRightUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
public interface FwRightUserMapper extends BaseMapper<FwRightUser> {

    List<FwRightUser> selectRoleTypeByUserCode(@Param("userCode") String userCode);

    /**
     * 查询流程用户
     * @param orgId
     * @param roleName
     * @param userName
     * @return
     */
    List<FwRightUser> selectBpsUsers(@Param("orgId") String orgId,@Param("roleName") String roleName,@Param("userName") String userName);

    /**
     * 查询数据中心审核角色用户
     * @return
     */
    List<FwRightUser> selectSjzxUsers();

    /**
     * 用户登录
     * @param userCode
     * @return
     */
    List<UserLoginDto> loginUser(@Param("userCode") String userCode);

    int isSystemManager(@Param("userId") String userId);

    /**
     * 是否业主单位
     * @param userCode
     * @return
     */
    Integer isMntPerson(@Param("userCode") String userCode);

    List<FwRightUser> selectJtUserByOrgIds(List<String> orgIds);
}
