package com.hualu.app.module.platform.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 信息资源规划C类字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseDatathirdDic对象", description="信息资源规划C类字典表")
public class BaseDatathirdDic implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("DATATHIRD_DIC_ID")
    private String datathirdDicId;

    @ApiModelProperty(value = "上级ID")
    @TableField("PARENT_ID")
    private String parentId;

    @ApiModelProperty(value = "属性项目")
    @TableField("ATTRIBUTE_ITEM")
    private String attributeItem;

    @ApiModelProperty(value = "属性描述")
    @TableField("ATTRIBUTE_DESC")
    private String attributeDesc;

    @ApiModelProperty(value = "属性编码")
    @TableField("ATTRIBUTE_CODE")
    private String attributeCode;

    @ApiModelProperty(value = "属性值")
    @TableField("ATTRIBUTE_VALUE")
    private String attributeValue;

    @ApiModelProperty(value = "停用标志（0未停用1停用）")
    @TableField("ATTRIBUTE_ACTIVE")
    private Double attributeActive;

    @ApiModelProperty(value = "创建用户ID")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改用户ID")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "顺序号")
    @TableField("SEQ_NO")
    private Integer seqNo;

    @ApiModelProperty(value = "上报新桥的字典")
    @TableField("XQ_VALUE")
    private String xqValue;

    @ApiModelProperty(value = "年报编码")
    @TableField("YR_CODE")
    private String yrCode;

    @ApiModelProperty(value = "年报值")
    @TableField("YR_VALUE")
    private String yrValue;


}
