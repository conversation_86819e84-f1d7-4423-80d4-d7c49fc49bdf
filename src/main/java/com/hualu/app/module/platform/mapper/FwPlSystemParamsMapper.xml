<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.platform.mapper.FwPlSystemParamsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.platform.entity.FwPlSystemParams">
        <id column="ID" property="id" />
        <result column="PARAM_CODE" property="paramCode" />
        <result column="PARAM_NAME" property="paramName" />
        <result column="PARAM_VALUE" property="paramValue" />
        <result column="PARAM_DESC" property="paramDesc" />
        <result column="PARAM_TYPE" property="paramType" />
        <result column="REM_TYPE1" property="remType1" />
        <result column="REM_TYPE2" property="remType2" />
        <result column="REM_TYPE3" property="remType3" />
        <result column="REM_TYPE4" property="remType4" />
        <result column="REM1_STRING" property="rem1String" />
        <result column="REM2_STRING" property="rem2String" />
        <result column="REM3_STRING" property="rem3String" />
        <result column="REM4_STRING" property="rem4String" />
        <result column="REM5_STRING" property="rem5String" />
        <result column="REM6_STRING" property="rem6String" />
        <result column="REM7_DATETIME" property="rem7Datetime" />
        <result column="REM8_DATETIME" property="rem8Datetime" />
        <result column="REM9_INT" property="rem9Int" />
        <result column="REM10_INT" property="rem10Int" />
        <result column="REMARK" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PARAM_CODE, PARAM_NAME, PARAM_VALUE, PARAM_DESC, PARAM_TYPE, REM_TYPE1, REM_TYPE2, REM_TYPE3, REM_TYPE4, REM1_STRING, REM2_STRING, REM3_STRING, REM4_STRING, REM5_STRING, REM6_STRING, REM7_DATETIME, REM8_DATETIME, REM9_INT, REM10_INT, REMARK
    </sql>

</mapper>
