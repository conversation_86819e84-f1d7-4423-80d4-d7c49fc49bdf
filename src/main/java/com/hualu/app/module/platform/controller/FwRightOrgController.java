package com.hualu.app.module.platform.controller;


import cn.hutool.core.util.StrUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.platform.entity.FwRightUser;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.hualu.app.module.platform.service.impl.FwRightOrgServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 组织机构表 前端控制器
 * <AUTHOR>
 * @since 2023-04-27
 */
@RestController
@RequestMapping("/fwRightOrg")
public class FwRightOrgController {

    @Autowired
    FwRightOrgService fwRightOrgService;

    @Autowired
    FwRightUserService userService;

    /**
     * 集团路段公司树形结构
     * @return
     */
    @GetMapping("tree")
    public Object buildTree(){
        List<FwRightOrgServiceImpl.TreeNode> treeNodes = fwRightOrgService.buildTree();
        return RestResult.success(treeNodes);
    }

    /**
     * 根据机构查询对应的用户信息
     * @param orgIds
     * @return
     */
    @GetMapping("/selectJtUsers")
    public RestResult<List<FwRightUser>> selectJtUsers(@RequestParam("orgIds") String orgIds){
        List<String> orgArr = StrUtil.split(orgIds, ",");
        List<FwRightUser> fwRightUsers = userService.selectJtUserByOrgIds(orgArr);
        return RestResult.success(fwRightUsers);
    }
}
