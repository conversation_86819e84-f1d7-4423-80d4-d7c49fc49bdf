package com.hualu.app.module.platform.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="FwRightUser对象", description="用户表")
public class FwRightUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "用户账号")
    @TableField("USER_CODE")
    private String userCode;

    @ApiModelProperty(value = "密码")
    @TableField("PASSWORD")
    private String password;

    @ApiModelProperty(value = "(普通用户、超级管理员、企业管理员)")
    @TableField("USER_TYPE")
    private String userType;

    @ApiModelProperty(value = "邮箱")
    @TableField("EMAIL")
    private String email;

    @ApiModelProperty(value = "所属机构")
    @TableField("ORG_ID")
    private String orgId;

    @ApiModelProperty(value = "用户姓名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty(value = "所属岗位")
    @TableField("POST_ID")
    private String postId;

    @ApiModelProperty(value = "是否启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "是否在线")
    @TableField("IS_ONLINE")
    private String isOnline;

    @ApiModelProperty(value = "是否锁定")
    @TableField("IS_LOCKED")
    private String isLocked;

    @ApiModelProperty(value = "登陆次数")
    @TableField("LOGIN_TIMES")
    private Double loginTimes;

    @ApiModelProperty(value = "登陆IP")
    @TableField("LOGIN_IP")
    private String loginIp;

    @ApiModelProperty(value = "最近登陆时间")
    @TableField("LAST_LOGIN_TIME")
    private LocalDateTime lastLoginTime;

    @ApiModelProperty(value = "本次登陆时间")
    @TableField("LOGIN_TIME")
    private LocalDateTime loginTime;

    @ApiModelProperty(value = "注册时间")
    @TableField("REG_TIME")
    private LocalDateTime regTime;

    @ApiModelProperty(value = "创建人账号")
    @TableField(value = "CREATE_USER_CODE", fill = FieldFill.INSERT)
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "CREATE_USER_NAME", fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人账号")
    @TableField(value = "UPDATE_USER_CODE", fill = FieldFill.INSERT_UPDATE)
    private String updateUserCode;

    @ApiModelProperty(value = "更新人姓名")
    @TableField(value = "UPDATE_USER_NAME", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "所属部门id")
    @TableField("DEPART_ID")
    private String departId;

    @ApiModelProperty(value = "电话号码")
    @TableField("TELEPHONE")
    private String telephone;

    @ApiModelProperty(value = "密码过期时间")
    @TableField("EXPIRE_DATE")
    private LocalDateTime expireDate;

    //角色类型
    @TableField(exist = false)
    private String roleType;

}
