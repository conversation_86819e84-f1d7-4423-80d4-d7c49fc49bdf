package com.hualu.app.module.platform.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.platform.dto.UserLoginDto;
import com.hualu.app.module.platform.entity.FwRightOrg;
import com.hualu.app.module.platform.entity.FwRightUser;
import com.hualu.app.module.platform.mapper.FwRightUserMapper;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@DS("gdgsDs")
@Service
public class FwRightUserServiceImpl extends ServiceImpl<FwRightUserMapper, FwRightUser> implements FwRightUserService {

    @Lazy
    @Autowired
    FwRightOrgService fwRightOrgService;

    @Override
    public Integer isMntPerson(String userCode) {
        Integer flag = baseMapper.isMntPerson(userCode);
        return flag;
    }

    @Override
    public String getDeptName(String userId) {
        FwRightUser userInfo = getById(userId);
        if (userInfo == null || StrUtil.isBlank(userInfo.getDepartId())){
            return CustomRequestContextHolder.getOrgName();
        }
        FwRightOrg orgInfo = fwRightOrgService.getById(userInfo.getDepartId());
        if (orgInfo == null){
            return CustomRequestContextHolder.getOrgName();
        }
        return StrUtil.format("{}",CustomRequestContextHolder.getOrgName(),orgInfo.getOrgFullname());
    }

    @Override
    public boolean isSystemManager() {
        int count = baseMapper.isSystemManager(CustomRequestContextHolder.getUserId());
        return count == 0 ? false : true;
    }

    @Override
    public UserLoginDto loginUser(String userCode) {
        List<UserLoginDto> users = baseMapper.loginUser(userCode);
        if (CollectionUtil.isNotEmpty(users)){
            return users.get(0);
        }
        return null;
    }

    @Override
    public String selectOrgId(String userCode) {
        LambdaQueryWrapper<FwRightUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FwRightUser::getIsDeleted,0);
        queryWrapper.eq(FwRightUser::getIsEnable,1);
        queryWrapper.eq(FwRightUser::getUserCode,userCode);

        FwRightUser user = baseMapper.selectOne(queryWrapper);
        if (user != null){
            return user.getOrgId();
        }
        return null;
    }

    @Override
    public List<FwRightUser> selectUsersByIds(List<String> userIds) {

        if (CollectionUtil.isEmpty(userIds)){
            throw new BaseException("用户ID不能为空");
        }
        LambdaQueryWrapper<FwRightUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FwRightUser::getIsDeleted,0);
        queryWrapper.eq(FwRightUser::getIsEnable,1);
        queryWrapper.in(FwRightUser::getId,userIds);

        queryWrapper.select(FwRightUser::getId,FwRightUser::getUserCode,FwRightUser::getUserName,FwRightUser::getOrgId,FwRightUser::getPostId);
        return list(queryWrapper);
    }

    @Override
    public List<FwRightUser> selectUsersByCode(List<String> userCodes) {
        if (CollectionUtil.isEmpty(userCodes)){
            throw new BaseException("用户ID不能为空");
        }
        LambdaQueryWrapper<FwRightUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FwRightUser::getIsDeleted,0);
        queryWrapper.eq(FwRightUser::getIsEnable,1);
        queryWrapper.and(wrapper->wrapper.in(FwRightUser::getUserCode,userCodes).or().in(FwRightUser::getId,userCodes));
        //queryWrapper.in(FwRightUser::getUserCode,userCodes);
        queryWrapper.select(FwRightUser::getId,FwRightUser::getUserCode,FwRightUser::getUserName,FwRightUser::getOrgId,FwRightUser::getPostId);
        return list(queryWrapper);
    }

    @Override
    public List<FwRightUser> selectRoleTypeByUserCode(String userCode) {
        return baseMapper.selectRoleTypeByUserCode(userCode);
    }

    @Override
    public List<FwRightUser> selectBpsUserByRoleName(String orgId, String roleName, String userName) {

        if ("数据中心审核".equals(roleName)){
            List<FwRightUser> users = baseMapper.selectSjzxUsers();
            return users;
        }
        String tempUserName = StringUtils.hasLength(userName)?userName.trim():"";
        List<FwRightUser> bpsUsers = baseMapper.selectBpsUsers(orgId, roleName, tempUserName);
        if (CollectionUtil.isNotEmpty(bpsUsers)){
            return bpsUsers;
        }
        return selectBpsUserByOrgId(orgId,tempUserName);
    }

    @Override
    public List<FwRightUser> selectBpsUserByOrgId(String orgId, String userName) {
        LambdaQueryWrapper<FwRightUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(FwRightUser::getId,FwRightUser::getUserCode,FwRightUser::getUserName,FwRightUser::getOrgId);
        queryWrapper.eq(FwRightUser::getOrgId,orgId).eq(FwRightUser::getIsDeleted,0).eq(FwRightUser::getIsEnable,1);

        if (StrUtil.isNotBlank(userName)){
            queryWrapper.like(FwRightUser::getUserName,userName);
        }
        List<FwRightUser> users = list(queryWrapper);
        users.forEach(item->{
            item.setUserName(item.getUserName()+"("+item.getUserCode()+")");
        });
        return users;
    }

    @Override
    public List<FwRightUser> selectJtUserByOrgIds(List<String> orgIds) {
        if (CollectionUtil.isEmpty(orgIds)){
            return Lists.newArrayList();
        }
        List<FwRightUser> users = baseMapper.selectJtUserByOrgIds(orgIds);
        return users;
    }
}
