package com.hualu.app.module.platform.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 组织机构表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="FwRightOrg对象", description="组织机构表")
public class FwRightOrg implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "组织机构名称")
    @TableField("ORG_NAME")
    private String orgName;

    @ApiModelProperty(value = "组织机构编号")
    @TableField("ORG_CODE")
    private String orgCode;

    @ApiModelProperty(value = "父组织机构ID（不再使用）")
    @TableField("PARENT_ID")
    private String parentId;

    @ApiModelProperty(value = "组织机构级别（不再使用）")
    @TableField("ORG_LEVEL")
    private String orgLevel;

    @ApiModelProperty(value = "地址")
    @TableField("ORG_ADDRESS")
    private String orgAddress;

    @ApiModelProperty(value = "经理")
    @TableField("ORG_MANAGER")
    private String orgManager;

    @ApiModelProperty(value = "联系人")
    @TableField("LINK_MAN")
    private String linkMan;

    @ApiModelProperty(value = "联系电话")
    @TableField("LINK_PHONE")
    private String linkPhone;

    @ApiModelProperty(value = "是否部门")
    @TableField("IS_DEPT")
    private String isDept;

    @ApiModelProperty(value = "邮编")
    @TableField("ZIP_CODE")
    private String zipCode;

    @ApiModelProperty(value = "是否启用")
    @TableField("IS_ENABLE")
    private String isEnable;

    @ApiModelProperty(value = "是否删除")
    @TableField("IS_DELETED")
    private String isDeleted;

    @ApiModelProperty(value = "创建人账号")
    @TableField(value = "CREATE_USER_CODE", fill = FieldFill.INSERT)
    private String createUserCode;

    @ApiModelProperty(value = "创建人姓名")
    @TableField(value = "CREATE_USER_NAME", fill = FieldFill.INSERT)
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人账号")
    @TableField(value = "UPDATE_USER_CODE", fill = FieldFill.INSERT_UPDATE)
    private String updateUserCode;

    @ApiModelProperty(value = "更新人姓名")
    @TableField(value = "UPDATE_USER_NAME", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "排序")
    @TableField("DEFAULT_SEQ_NO")
    private Long defaultSeqNo;

    @ApiModelProperty(value = "组织机构全名")
    @TableField("ORG_FULLNAME")
    private String orgFullname;

    @ApiModelProperty(value = "（不再使用）")
    @TableField("ORG_REPORT_ID")
    private Double orgReportId;

    @ApiModelProperty(value = "组织机构类型")
    @TableField("ORG_TYPE")
    private String orgType;

    @ApiModelProperty(value = "是否集团内组织")
    @TableField("ORG_GRP_FLAG")
    private String orgGrpFlag;

    @ApiModelProperty(value = "组织机构繁体名称")
    @TableField("ORG_NAME_FT")
    private String orgNameFt;

    @ApiModelProperty(value = "组织机构英文名称")
    @TableField("ORG_NAME_EN")
    private String orgNameEn;

    @ApiModelProperty(value = "企业性质")
    @TableField("ORG_NATURE")
    private String orgNature;

    @ApiModelProperty(value = "行业类别")
    @TableField("ORG_CATEGORY")
    private String orgCategory;

    @ApiModelProperty(value = "机构状态")
    @TableField("ORG_STATUS")
    private String orgStatus;

    @ApiModelProperty(value = "法人代表")
    @TableField("ORG_LEGAL")
    private String orgLegal;

    @ApiModelProperty(value = "组织机构证号")
    @TableField("ORG_CERTI_NO")
    private String orgCertiNo;

    @ApiModelProperty(value = "组织机构注册地址")
    @TableField("ORG_REG_ADDR")
    private String orgRegAddr;

    @ApiModelProperty(value = "工商行政管理注册号")
    @TableField("ORG_ICA_REG_NO")
    private String orgIcaRegNo;

    @ApiModelProperty(value = "税务登记证号")
    @TableField("ORG_TAX_REG_NO")
    private String orgTaxRegNo;

    @ApiModelProperty(value = "传真号")
    @TableField("FAX")
    private String fax;

    @ApiModelProperty(value = "公路管理养护单位代码")
    @TableField("CBMS_ORG_CODE")
    private String cbmsOrgCode;

    @ApiModelProperty(value = "组织机构资产范围最大经度")
    @TableField("MAX_X")
    private Double maxX;

    @ApiModelProperty(value = "组织机构资产范围最大维度")
    @TableField("MAX_Y")
    private Double maxY;

    @ApiModelProperty(value = "组织机构资产范围最小经度")
    @TableField("MIN_X")
    private Double minX;

    @ApiModelProperty(value = "组织机构资产范围最小维度")
    @TableField("MIN_Y")
    private Double minY;

    @ApiModelProperty(value = "经度")
    @TableField("GIS_X")
    private Double gisX;

    @ApiModelProperty(value = "纬度")
    @TableField("GIS_Y")
    private Double gisY;

    @ApiModelProperty(value = "是否营运公司（0不是，1是）")
    @TableField("IS_OPRTORG")
    private Integer isOprtorg;

    @ApiModelProperty(value = "是否项目公司（0不是，1是）")
    @TableField("IS_PRJORG")
    private Integer isPrjorg;

    @ApiModelProperty(value = "排序")
    @TableField("SEQ_NO")
    private Integer seqNo;


}
