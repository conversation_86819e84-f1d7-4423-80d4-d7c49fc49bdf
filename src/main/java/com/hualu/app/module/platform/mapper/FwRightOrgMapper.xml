<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.platform.mapper.FwRightOrgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.platform.entity.FwRightOrg">
        <id column="ID" property="id" />
        <result column="ORG_NAME" property="orgName" />
        <result column="ORG_CODE" property="orgCode" />
        <result column="PARENT_ID" property="parentId" />
        <result column="ORG_LEVEL" property="orgLevel" />
        <result column="ORG_ADDRESS" property="orgAddress" />
        <result column="ORG_MANAGER" property="orgManager" />
        <result column="LINK_MAN" property="linkMan" />
        <result column="LINK_PHONE" property="linkPhone" />
        <result column="IS_DEPT" property="isDept" />
        <result column="ZIP_CODE" property="zipCode" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="CREATE_USER_CODE" property="createUserCode" />
        <result column="CREATE_USER_NAME" property="createUserName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_CODE" property="updateUserCode" />
        <result column="UPDATE_USER_NAME" property="updateUserName" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="DEFAULT_SEQ_NO" property="defaultSeqNo" />
        <result column="ORG_FULLNAME" property="orgFullname" />
        <result column="ORG_REPORT_ID" property="orgReportId" />
        <result column="ORG_TYPE" property="orgType" />
        <result column="ORG_GRP_FLAG" property="orgGrpFlag" />
        <result column="ORG_NAME_FT" property="orgNameFt" />
        <result column="ORG_NAME_EN" property="orgNameEn" />
        <result column="ORG_NATURE" property="orgNature" />
        <result column="ORG_CATEGORY" property="orgCategory" />
        <result column="ORG_STATUS" property="orgStatus" />
        <result column="ORG_LEGAL" property="orgLegal" />
        <result column="ORG_CERTI_NO" property="orgCertiNo" />
        <result column="ORG_REG_ADDR" property="orgRegAddr" />
        <result column="ORG_ICA_REG_NO" property="orgIcaRegNo" />
        <result column="ORG_TAX_REG_NO" property="orgTaxRegNo" />
        <result column="FAX" property="fax" />
        <result column="CBMS_ORG_CODE" property="cbmsOrgCode" />
        <result column="MAX_X" property="maxX" />
        <result column="MAX_Y" property="maxY" />
        <result column="MIN_X" property="minX" />
        <result column="MIN_Y" property="minY" />
        <result column="GIS_X" property="gisX" />
        <result column="GIS_Y" property="gisY" />
        <result column="IS_OPRTORG" property="isOprtorg" />
        <result column="IS_PRJORG" property="isPrjorg" />
        <result column="SEQ_NO" property="seqNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, ORG_NAME, ORG_CODE, PARENT_ID, ORG_LEVEL, ORG_ADDRESS, ORG_MANAGER, LINK_MAN, LINK_PHONE, IS_DEPT, ZIP_CODE, IS_ENABLE, IS_DELETED, CREATE_USER_CODE, CREATE_USER_NAME, CREATE_TIME, UPDATE_USER_CODE, UPDATE_USER_NAME, UPDATE_TIME, REMARK, DEFAULT_SEQ_NO, ORG_FULLNAME, ORG_REPORT_ID, ORG_TYPE, ORG_GRP_FLAG, ORG_NAME_FT, ORG_NAME_EN, ORG_NATURE, ORG_CATEGORY, ORG_STATUS, ORG_LEGAL, ORG_CERTI_NO, ORG_REG_ADDR, ORG_ICA_REG_NO, ORG_TAX_REG_NO, FAX, CBMS_ORG_CODE, MAX_X, MAX_Y, MIN_X, MIN_Y, GIS_X, GIS_Y, IS_OPRTORG, IS_PRJORG, SEQ_NO
    </sql>
    <select id="selectChildOprtOrgCodes" resultType="java.lang.String">
        select v.id from (
        SELECT o.id,o.ORG_NAME
        FROM gdgs.FW_RIGHT_ORG o
        START WITH
        o.id = #{orgCode}
        CONNECT BY PRIOR o.ID = o.PARENT_ID
        and LEVEL &lt; 4
        and IS_ENABLE = 1
        and is_deleted = 0) v join gdgs.BASE_ROUTE_LOGIC o on v.id = o.OPRT_ORG_CODE group by v.id
    </select>
    <select id="queryAuthOrg" resultType="java.lang.String">
        select ORG_CODE from MEMSDB.USE_SIGN_CONGFIG where ORG_CODE = #{orgCode}
    </select>
    <select id="queryOprtOrgCodes" resultType="com.hualu.app.module.platform.entity.FwRightOrg" parameterType="java.lang.String">
        with orgtable as (select o.ORG_CODE,o.ORG_FULLNAME,o.PARENT_ID from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1 and o.IS_DELETED = 0
        start with o.ID = #{orgCode} connect by prior o.ID = o.PARENT_ID)
        select distinct PARENT_ID,ROUTE_NAME as REMARK,b.ORG_FULLNAME as ORG_NAME,b.ORG_CODE
        from gdgs.BASE_ROUTE_LOGIC a inner join orgtable b on a.OPRT_ORG_CODE = b.ORG_CODE and a.IS_ENABLE = 1 and a.LINE_DIRECT = 1
        order by PARENT_ID,b.ORG_CODE
    </select>
    <select id="queryCheckOrgCodes" resultType="java.util.Map" parameterType="java.lang.String">
        select ORG_FULLNAME as ORG_NAME,ORG_CODE
        from gdgs.FW_RIGHT_ORG where PARENT_ID = 'd8cf7213-d1cc-4d71-995a-6d1f1211da20'
        and IS_ENABLE = 1 and IS_DELETED = 0
    </select>
    <select id="listRouteOrg" resultType="com.hualu.app.module.platform.dto.PtOrgItemDto">
        select o.ORG_NAME, o.id, po.ORG_NAME as porg_name, po.ORG_CODE as porg_code
        from (select OPRT_ORG_CODE
              from gdgs.BASE_ROUTE_LOGIC
              group by OPRT_ORG_CODE) a
                 join gdgs.FW_RIGHT_ORG o
                      on a.OPRT_ORG_CODE = o.ORG_CODE and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and o.ORG_GRP_FLAG = 1
                 join gdgs.FW_RIGHT_ORG po on o.PARENT_ID = po.ID
        order by po.ORG_NAME, o.ORG_NAME
    </select>
</mapper>
