<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.platform.mapper.FwRightUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.platform.entity.FwRightUser">
        <id column="ID" property="id" />
        <result column="USER_CODE" property="userCode" />
        <result column="PASSWORD" property="password" />
        <result column="USER_TYPE" property="userType" />
        <result column="EMAIL" property="email" />
        <result column="ORG_ID" property="orgId" />
        <result column="USER_NAME" property="userName" />
        <result column="POST_ID" property="postId" />
        <result column="IS_ENABLE" property="isEnable" />
        <result column="IS_DELETED" property="isDeleted" />
        <result column="IS_ONLINE" property="isOnline" />
        <result column="IS_LOCKED" property="isLocked" />
        <result column="LOGIN_TIMES" property="loginTimes" />
        <result column="LOGIN_IP" property="loginIp" />
        <result column="LAST_LOGIN_TIME" property="lastLoginTime" />
        <result column="LOGIN_TIME" property="loginTime" />
        <result column="REG_TIME" property="regTime" />
        <result column="CREATE_USER_CODE" property="createUserCode" />
        <result column="CREATE_USER_NAME" property="createUserName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_CODE" property="updateUserCode" />
        <result column="UPDATE_USER_NAME" property="updateUserName" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="DEPART_ID" property="departId" />
        <result column="TELEPHONE" property="telephone" />
        <result column="EXPIRE_DATE" property="expireDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, USER_CODE, PASSWORD, USER_TYPE, EMAIL, ORG_ID, USER_NAME, POST_ID, IS_ENABLE, IS_DELETED, IS_ONLINE, IS_LOCKED, LOGIN_TIMES, LOGIN_IP, LAST_LOGIN_TIME, LOGIN_TIME, REG_TIME, CREATE_USER_CODE, CREATE_USER_NAME, CREATE_TIME, UPDATE_USER_CODE, UPDATE_USER_NAME, UPDATE_TIME, REMARK, DEPART_ID, TELEPHONE, EXPIRE_DATE
    </sql>
    <select id="selectRoleTypeByUserCode" resultType="com.hualu.app.module.platform.entity.FwRightUser">
        select distinct u.id as user_id, u.user_code, u.user_name, r.role_business_type as role_type
        from GDGS.Fw_Right_role r,
             GDGS.Fw_Right_Role_User_Ref ru,
             GDGS.Fw_Right_User u
        where r.id = ru.role_id
          and u.id = ru.user_id
          and u.user_code = #{userCode}
    </select>
    <select id="selectBpsUsers" resultType="com.hualu.app.module.platform.entity.FwRightUser">
        <bind name="pattern" value="'%'+userName+'%'" />
        select distinct u.user_code, u.user_name || '(' || u.user_code || ')' as user_name, u.id
        from gdgs.fw_right_user u,
             gdgs.fw_right_bpsrole r,
             gdgs.fw_right_bpsrole_user_ref ref
        where u.id = ref.user_id
          and r.id = ref.bpsrole_id
          and u.is_enable = '1'
          and u.is_deleted = '0'
          and u.org_id = #{orgId}
          and r.role_name = #{roleName}
        <if test="userName != null">
            and u.user_name like #{pattern}
        </if>
    </select>
    <select id="selectSjzxUsers" resultType="com.hualu.app.module.platform.entity.FwRightUser">
        select distinct u.user_code, u.user_name || '(' || u.user_code || ')' as user_name, u.id
        from gdgs.fw_right_user u inner join
             gdgs.fw_right_role_user_ref r on u.id = r.user_id and r.role_id = '78ff43b0-9a51-4519-b467-77dc129b0b1c'
    </select>
    <select id="loginUser" resultType="com.hualu.app.module.platform.dto.UserLoginDto">
        select u.id as user_id, u.USER_CODE, u.USER_NAME, o.ORG_FULLNAME, o.ID as org_id,o.ORG_NAME_EN as org_en, dc.DCCODE
        from gdgs.FW_RIGHT_USER u
        join gdgs.FW_RIGHT_ORG o on u.ORG_ID = o.ID
        left join gdgs.MG_DCCODE_ORG dc on o.ID = dc.ORG_ID
        where LOWER(user_code) = LOWER(#{userCode}) and u.IS_ENABLE = 1 and u.IS_DELETED=0
    </select>
    <select id="isSystemManager" resultType="java.lang.Integer">
        select count(1) from gdgs.FW_RIGHT_ROLE_USER_REF where role_id = '0276685a-8120-4572-acbb-250e0bfd6dbc' and user_id=#{userId}
    </select>
    <select id="isMntPerson" resultType="java.lang.Integer">
        select  o.ORG_GRP_FLAG from gdgs.FW_RIGHT_ORG o join gdgs.FW_RIGHT_USER u on o.id = nvl(u.DEPART_ID, u.ORG_ID) where u.USER_CODE = #{userCode}
    </select>
    <select id="selectJtUserByOrgIds" resultType="com.hualu.app.module.platform.entity.FwRightUser">
        select u.id,u.USER_CODE,u.USER_NAME || '(' || u.USER_CODE ||')' as USER_NAME
        from gdgs.FW_RIGHT_USER u
                 join gdgs.FW_RIGHT_ROLE_USER_REF ur on u.id = ur.USER_ID
                 join gdgs.FW_RIGHT_ROLE r on ur.ROLE_ID = r.ID
        where u.ORG_ID in
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
          and u.IS_ENABLE = 1
          and u.IS_DELETED = 0
          and r.IS_DELETED = 0 and r.IS_ENABLE = 1 and r.ROLE_BUSINESS_TYPE = '06'
        group by u.id,u.USER_CODE,u.USER_NAME
        order by u.USER_NAME
    </select>
</mapper>
