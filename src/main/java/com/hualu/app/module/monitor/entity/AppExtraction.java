package com.hualu.app.module.monitor.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AppExtraction对象", description="")
public class AppExtraction implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId("ID")
    private String id;

    @ApiModelProperty(value = "二级公司ID")
    @TableField("PARENT_ID")
    private String parentId;

    @Excel(name = "二级公司名称",orderNum = "0")
    @ApiModelProperty(value = "二级公司名称")
    @TableField("PARENT_ORG_NAME")
    private String parentOrgName;

    @ApiModelProperty(value = "公司ID")
    @TableField("ORG_CODE")
    private String orgCode;

    @Excel(name = "所属路段",orderNum = "1")
    @ApiModelProperty(value = "公司名称")
    @TableField("ORG_NAME")
    private String orgName;

    @Excel(name = "上报",orderNum = "3",groupName = "日常巡查",numFormat = "#.##",type = 10)
    @ApiModelProperty(value = "日常巡查上报")
    @TableField("DINSP_REPORT")
    private Long dinspReport;

    @Excel(name = "查询",orderNum = "2",groupName = "日常巡查",type = 10)
    @ApiModelProperty(value = "日常巡查查询次数")
    @TableField("DINSP_QUERY")
    private Long dinspQuery;

    @Excel(name = "流程办理",orderNum = "8",type = 10)
    @ApiModelProperty(value = "流程办理次数")
    @TableField("PROCESS")
    private Long process;

    @Excel(name = "任务单管理",orderNum = "5",type = 10)
    @ApiModelProperty(value = "任务单查询次数")
    @TableField("TASK_QUERY")
    private Long taskQuery;

    @Excel(name = "验收管理",orderNum = "7",type = 10)
    @ApiModelProperty(value = "验收单次数")
    @TableField("TASK_ACCPT_QUERY")
    private Long taskAccptQuery;

    @Excel(name = "里程（公里）",orderNum = "4",groupName = "日常巡查",type = 10,width = 15)
    @ApiModelProperty(value = "巡查里程数")
    @TableField("TRACK_LENGTH")
    private Double trackLength;

    @Excel(name = "维修登记",orderNum = "6",type = 10)
    @ApiModelProperty(value = "维修登记")
    @TableField("WXDJDWX_QUERY")
    private Long wxdjdwxQuery;

    @ApiModelProperty(value = "日期")
    @TableField("DATES")
    private String dates;


    /**
     * 路段里程
     */
    @TableField(exist = false)
    private double routeLength = 0D;

    /**
     * 合格率
     */
    @TableField(exist = false)
    private double hgPrecent = 0;
}
