package com.hualu.app.module.monitor.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.monitor.dto.AppExtractionQueryDto;
import com.hualu.app.module.monitor.entity.AppExtraction;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface AppExtractionMapper extends BaseMapper<AppExtraction> {

    List<AppExtraction> listAppExtraction(@Param("queryDto")AppExtractionQueryDto queryDto,@Param("orgIds") List<String> orgIds);

    List<AppExtraction> listTrackLength(@Param(Constants.WRAPPER)Wrapper wrapper);
}
