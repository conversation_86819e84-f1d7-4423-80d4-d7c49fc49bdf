package com.hualu.app.module.monitor.dto;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
public class AppExtractionQueryDto {

    private String orgname;//机构名称
    private String orgcode;//机构编码
    private String orgcodes;//机构编码集合
    private String startDate;//开始日期 20230101
    private String endDate;//结束日期 20230602
    private List<String> orgList = null;

    private String startDateStr;
    private String endDateStr;

    public void setStartDate(String startDate) {
        this.startDate = getDate(startDate,true);
        this.startDateStr = getDate(startDate,false);;
    }

    public void setEndDate(String endDate) {
        this.endDate = getDate(endDate,true);
        this.endDateStr = getDate(endDate,false);
    }

    public void setOrgcodes(String orgcodes) {
        this.orgcodes = orgcodes;
        if (StrUtil.isNotBlank(orgcodes)){
            String[] split = orgcodes.split(",");
            orgList = Arrays.asList(split);
        }
    }

    private static String getDate(String date, boolean isNumber){
        String tempDate = date;
        if (StrUtil.isBlank(tempDate)){
            tempDate = DateUtil.formatDate(new Date());
        }
        if (isNumber){
            tempDate = tempDate.replaceAll("-", "");
        }
        return tempDate;
    }
}
