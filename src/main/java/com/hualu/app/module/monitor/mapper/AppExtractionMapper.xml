<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.monitor.mapper.AppExtractionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.monitor.entity.AppExtraction">
        <id column="ID" property="id" />
        <result column="PARENT_ID" property="parentId" />
        <result column="PARENT_ORG_NAME" property="parentOrgName" />
        <result column="ORG_CODE" property="orgCode" />
        <result column="ORG_NAME" property="orgName" />
        <result column="DINSP_REPORT" property="dinspReport" />
        <result column="DINSP_QUERY" property="dinspQuery" />
        <result column="PROCESS" property="process" />
        <result column="TASK_QUERY" property="taskQuery" />
        <result column="TASK_ACCPT_QUERY" property="taskAccptQuery" />
        <result column="TRACK_LENGTH" property="trackLength" />
        <result column="WXDJDWX_QUERY" property="wxdjdwxQuery" />
        <result column="DATES" property="dates" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, PARENT_ID, PARENT_ORG_NAME, ORG_CODE, ORG_NAME, DINSP_REPORT, DINSP_QUERY, PROCESS, TASK_QUERY, TASK_ACCPT_QUERY, TRACK_LENGTH, WXDJDWX_QUERY, DATES
    </sql>
    <select id="listAppExtraction" resultType="com.hualu.app.module.monitor.entity.AppExtraction">
        SELECT
        porg.org_code as parent_org_code,porg.org_name as parent_org_name,org.org_code,org.org_name,a.ROUTE_LENGTH
        FROM
        ( SELECT oprt_org_code, sum( ROUTE_LENGTH ) as ROUTE_LENGTH FROM BASE_ROUTE_LOGIC WHERE LINE_DIRECT = '1' GROUP BY oprt_org_code
        union all
        select OPRT_ORG_CODE,sum(END_STAKE-START_STAKE) as ROUTE_LENGTH  from FW_RIGHT_DATA_PERMISSION where OPRT_ORG_CODE in ('N000250','N000059','629280F3A0A35ED9E0500180278050D5') and LINE_DIRECT = '1' group by OPRT_ORG_CODE ) a
        JOIN FW_RIGHT_ORG org ON a.oprt_org_code = org.org_code
        left join gdgs.fw_right_org porg on org.parent_id=porg.id
        where 1=1
        <if test="queryDto.orgname !=null and queryDto.orgname != ''">
            and (org.org_name like #{queryDto.orgname} or porg.org_name like #{queryDto.orgname} )
        </if>
        <if test="queryDto.orgcode !=null and queryDto.orgcode != '' and queryDto.orgcode != 'N000001'">
            and (org.org_code = #{queryDto.orgcode} or porg.org_code = #{queryDto.orgcode} )
        </if>
        <if test="orgIds !=null">
            and org.org_code in
            <foreach collection="orgIds" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by porg.org_name,org.org_name
    </select>
    <select id="listTrackLength" resultType="com.hualu.app.module.monitor.entity.AppExtraction">
        select dm.MNT_ORG_ID as org_code,sum(at.TRACK_LENGTH) / 1000 as track_length from gdgs.APP_TRACK at join memsdb.DM_DINSP dm on at.ID = dm.DINSP_ID ${ew.customSqlSegment} group by dm.MNT_ORG_ID
    </select>

</mapper>
