package com.hualu.app.module.monitor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.monitor.dto.AppExtractionQueryDto;
import com.hualu.app.module.monitor.entity.AppExtraction;
import com.hualu.app.module.monitor.mapper.AppExtractionMapper;
import com.hualu.app.module.monitor.service.AppExtractionService;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@DS("gdgsDs")
@Service
public class AppExtractionServiceImpl extends ServiceImpl<AppExtractionMapper, AppExtraction> implements AppExtractionService {

    @Override
    public List<AppExtraction> queryappusage(Map<String,String> params) {

        AppExtractionQueryDto queryDto = new AppExtractionQueryDto();
        BeanUtil.fillBeanWithMap(params,queryDto,true);
        if (StrUtil.isBlank(queryDto.getStartDate())){
            return Lists.newArrayList();
        }

        if (StrUtil.isBlank(queryDto.getEndDateStr())){
            queryDto.setEndDate(DateUtil.formatDate(new Date()));
        }

        List<AppExtraction> extractions = baseMapper.listAppExtraction(queryDto,queryDto.getOrgList());

        Set<String> orgCodes = extractions.stream().filter(e-> StrUtil.isNotBlank(e.getOrgCode())).map(AppExtraction::getOrgCode).collect(Collectors.toSet());

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.apply("dm.insp_date >= to_date({0},'YYYY-MM-dd')",queryDto.getStartDateStr());
        queryWrapper.apply("dm.insp_date <= to_date({0},'YYYY-MM-dd')",queryDto.getEndDateStr());
        queryWrapper.in("dm.MNT_ORG_ID",orgCodes);

        //根据机构，获取对应的轨迹记录
        List<AppExtraction> trackList = baseMapper.listTrackLength(queryWrapper);
        Map<String, Double> trackMap = trackList.stream().filter(e->StrUtil.isNotBlank(e.getOrgCode()) && e.getTrackLength() != null)
                .collect(Collectors.toMap(AppExtraction::getOrgCode, AppExtraction::getTrackLength));
        long dayNum = getDayNum(queryDto.getStartDateStr(), queryDto.getEndDateStr());
        extractions.forEach(item->{
            item.setTrackLength(trackMap.getOrDefault(item.getOrgCode(),0D));
            //计算每个公司合格率=巡查里程/（主线里程*天数），合格率最多到100%
            if (item.getRouteLength() != 0){
                double v = (item.getTrackLength() / (item.getRouteLength() * dayNum)) * 100;
                if (v > 100){
                    item.setHgPrecent(100);
                    return;
                }
                BigDecimal temp = new BigDecimal(v).setScale(2, RoundingMode.HALF_UP);
                item.setHgPrecent(temp.doubleValue());
            }
        });
        return extractions;
    }

    /**
     * 计算间隔天数
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    private long getDayNum(String startDateStr,String endDateStr){
        DateTime startDate = DateUtil.parse(startDateStr);
        DateTime endDate = DateUtil.parse(endDateStr);

        long dayNum = DateUtil.betweenDay(startDate.toJdkDate(), endDate.toJdkDate(), true);
        return dayNum + 1;
    }
}
