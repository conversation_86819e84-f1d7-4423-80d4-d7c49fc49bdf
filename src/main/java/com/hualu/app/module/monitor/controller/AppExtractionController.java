package com.hualu.app.module.monitor.controller;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.monitor.entity.AppExtraction;
import com.hualu.app.module.monitor.service.AppExtractionService;
import com.hualu.app.utils.H_BasedataHepler;
import com.tg.dev.excelimport.util.H_ExcelDownloadHelper;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@RestController
@RequestMapping(value = {"/appExtraction", H_BasedataHepler.HUALU_API+"/appExtraction"})
public class AppExtractionController {

    @Autowired
    AppExtractionService appExtractionService;

    @PostMapping("queryappusage")
    public RestResult<List<AppExtraction>> queryappusage(HttpServletRequest request){
        List<AppExtraction> datas = appExtractionService.queryappusage(ServletUtil.getParamMap(request));
        return RestResult.success(datas);
    }

    @RequestMapping("exportExcel")
    public void downloadExcel(HttpServletRequest request,HttpServletResponse response){
        String excelData = request.getParameter("excelData");
        JSONObject excelJson = JSONUtil.parseObj(excelData);
        Map params = JSONUtil.toBean(excelJson, Map.class);
        List<AppExtraction> appExtractions = appExtractionService.queryappusage(params);
        ExportParams exportParams = new ExportParams("移动端使用情况","sheet1");
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AppExtraction.class, appExtractions);
        H_ExcelDownloadHelper.downLoad("移动端使用情况.xlsx",response,workbook);
    }
}
