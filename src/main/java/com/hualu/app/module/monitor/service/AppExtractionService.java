package com.hualu.app.module.monitor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.app.module.monitor.entity.AppExtraction;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
public interface AppExtractionService extends IService<AppExtraction> {

    /**
     * 查询移动端使用情况
     * @param params
     * @return
     */
    public List<AppExtraction> queryappusage(Map<String,String> params);
}
