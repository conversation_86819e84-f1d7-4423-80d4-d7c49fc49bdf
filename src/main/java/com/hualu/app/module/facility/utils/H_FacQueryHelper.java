package com.hualu.app.module.facility.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.hualu.app.module.facility.dto.FacQueryDto;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.service.DmFinspService;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class H_FacQueryHelper {

    private static final String MONTH_FORMAT = "yyyy-MM";
    /**
     * 初始化查询实体
     * @param reqMap
     */
    public static FacQueryDto initFacQueryDto(Map reqMap){
        FacQueryDto dto = new FacQueryDto();
        BeanUtil.fillBeanWithMap(reqMap,dto,true);
        if (StrUtil.isNotBlank(dto.getFinspId())){
            DmFinspService service = CustomApplicationContextHolder.getBean(DmFinspService.class);
            DmFinsp dmFinsp = service.getById(dto.getFinspId());
            if (dmFinsp != null){
                //throw new BaseException("经常检查单不存在");
                dto.setFinVersion(dmFinsp.getFinVersion());
            }
        }
        return dto;
    }

    /**
     * 是否到提醒时间
     * @param facCat
     * @param grade
     * @param month
     * @return
     */
    public static boolean isNotice(String facCat,String grade,String month){
        List<String> months = getMonths(facCat, grade, month);
        String max = CollectionUtil.max(months);
        DateTime maxDate = DateUtil.parse(max,"yyyy-MM");
        //某个月的结束日期
        DateTime endOfMonth = DateUtil.endOfMonth(maxDate);
        //long betweenDay = DateUtil.betweenDay(endOfMonth.toJdkDate(), DateUtil.date().toJdkDate(),false);

        long betweenDay = (endOfMonth.toTimestamp().getTime() - System.currentTimeMillis())/(1000 * 60 * 60 * 24);

        //间隔天数，需要提醒
        int intervalDay = months.size() * 15;

        if (intervalDay >= betweenDay){
            return true;
        }
        return false;
    }


    /**
     * 返回范围日期
     * @param startMonth
     * @param endMonth
     * @return
     */
    public static List<String> rangeList(String startMonth,String endMonth){
        String tempStartMonth = startMonth + "-01";
        String tempEndMonth = endMonth + "-01";
        List<DateTime> dateTimes = DateUtil.rangeToList( DateUtil.parseDate(tempStartMonth),DateUtil.parseDate(tempEndMonth), DateField.MONTH);
        List<String> monthList = Lists.newArrayList();
        dateTimes.forEach(item->{
            monthList.add(DateUtil.format(item,MONTH_FORMAT));
        });
        return monthList;
    }

    /**
     * 根据当前日期，返回季度包含的月份，例如四季度：2024-10,2024-11,2024-12
     * @param xcDate
     * @return
     */
    public static List<String> rangeList(Date xcDate){
        DateTime startDate = DateUtil.beginOfQuarter(xcDate);
        DateTime endDate = DateUtil.endOfQuarter(xcDate);
        List<DateTime> dateTimes = DateUtil.rangeToList(startDate, endDate, DateField.MONTH);
        List<String> monthList = Lists.newArrayList();
        dateTimes.forEach(item->{
            monthList.add(DateUtil.format(item,MONTH_FORMAT));
        });
        return monthList;
    }

    /**
     * 根据结构物类型，获取对应的月份
     * @param facCat QL、HD、SD、BP
     * @param grade
     * @param month 2024-08
     * @return
     */
    @SneakyThrows
    public static List<String> getMonths(String facCat, String grade, String month){
        if (facCat.toUpperCase().equals("QL")){
            return qlMonth(grade, month);
        }
        if (facCat.toUpperCase().equals("HD")){
            return hdMonth(grade, month);
        }
        if (facCat.toUpperCase().equals("SD")){
            return sdMonth(grade, month);
        }
        if (facCat.toUpperCase().equals("BP")){
            return bpMonth(grade, month);
        }
        return null;
    }

    private static List<String> qlMonth(String grade,String month){
        //获取当前月份
        DateTime parse = DateUtil.parse(month, MONTH_FORMAT);
        if ("1".equals(grade)){
            return Lists.newArrayList(DateUtil.format(parse,MONTH_FORMAT));
        }
        //月份从0开始计算
        int monthNum = parse.month() + 1;
        // 如果为偶数，需要减1，否则加1，例如传过来10月份，需要得到9月份
        DateTime dateTime = NumberUtil.isEven(monthNum)?DateUtil.offsetMonth(parse, -1):DateUtil.offsetMonth(parse, 1);
        return Lists.newArrayList(DateUtil.format(parse,MONTH_FORMAT),DateUtil.format(dateTime,MONTH_FORMAT));
    }

    private static List<String> hdMonth(String grade,String month){
        DateTime parse = DateUtil.parse(month, MONTH_FORMAT);
        DateTime startQuarter = DateUtil.beginOfQuarter(parse);
        DateTime twoMonth = DateUtil.offsetMonth(startQuarter, 1);
        DateTime threeMonth = DateUtil.offsetMonth(startQuarter, 2);
        return Lists.newArrayList(DateUtil.format(startQuarter,MONTH_FORMAT),DateUtil.format(twoMonth,MONTH_FORMAT),DateUtil.format(threeMonth,MONTH_FORMAT));
    }


    private static List<String> sdMonth(String grade,String month){
        if ("1".equals(grade) || "2".equals(grade)){
            return qlMonth(grade, month);
        }else {
            return hdMonth(grade, month);
        }
    }

    private static List<String> bpMonth(String grade,String month){
        DateTime parse = DateUtil.parse(month, MONTH_FORMAT);
        int quarter = DateUtil.quarter(parse);
        List<String> months = Lists.newArrayList();
        int startIdx = quarter<=2?1:7;
        int endIdx = quarter<=2?6:12;
        for (int i = startIdx ; i <= endIdx ; i++){
            months.add(parse.year()+"-"+StrUtil.padPre(""+i,2,'0'));
        }
        return months;
    }

    public static void main(String[] args) {
        List<String> strings = getMonths("bp","2", "2022-9");
        System.out.println(strings);
    }
}
