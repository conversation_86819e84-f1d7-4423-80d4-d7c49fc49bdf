package com.hualu.app.module.facility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.MtmsTunnelBasic;
import com.hualu.app.module.facility.dto.FacCompTypeDto;
import com.hualu.app.module.facility.dto.FacDssTypeDto;
import com.hualu.app.module.facility.dto.FacPartTypeDto;
import com.hualu.app.module.facility.dto.FacilityNumDto;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FacSdMapper extends BaseMapper<MtmsTunnelBasic> {

    List<FacDssTypeDto> selectDssType(@Param("structCompId") String structCompId);

    List<FacPartTypeDto> selectPartType(@Param("structPartId") String structPartId);

    List<FacCompTypeDto> selectCompType(IPage<FacCompTypeDto> page,@Param("structPartId") String structPartId);

    //根据路段编码，查询桥梁的个数，及对应的养护等级
    List<FinspStatFacDto> selectStruct(@Param("routeCodes") List<String> routeCodes, @Param("paramDto") GeometryParamDto paramDto);

    //结构物数量
    List<FacilityNumDto> countNum(@Param("routeCodes") List<String> routeCodes);

    /**
     * 根据路段编码，查询已经巡检的桥梁结构物	(1级=1次/1月，2级=1次/2月)，判断是否包含1级和2级
     * 一年6次（【1月,2月】、【3月,4月】、【5月,6月】、根据该规则推算）
     */
    List<FinspStatFacDto> selectFinspNumByGrade(@Param("routeCodes") List<String> routeCodes
            ,@Param("grade") String grade,@Param("months") List<String> months);
}
