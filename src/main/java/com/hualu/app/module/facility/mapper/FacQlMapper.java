package com.hualu.app.module.facility.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface FacQlMapper extends BaseMapper<TBrdgBrdgrecog>,IFinspMapper {

    @Select("select distinct d.dss_type,d.dss_type_name,d.dss_order_number from memsdb.dss_type d inner join memsdb.common_ql_dsstype dt on d.dss_type = dt.dss_type order by d.dss_order_number desc")
    List<FacDssTypeDto> selectDssType();

    @Select("select ti.bear_type from  BCTCMSDB.t_brdg_techindex ti where ti.brdgrecog_id=#{structId}")
    String selectBearType(String structId);

    List<SupportFormDto> selectSupportForm(@Param("structId") String structId, @Param("structPartId") String structPartId, @Param("structCompId") String structCompId);

    /**
     * 查询构件病害
     * @return
     */
    List<FacDssTypeDto> selectDssTypeByWrapper(@Param(Constants.WRAPPER) Wrapper wrapper);

    List<FacDssTypeDto> selectDssTypeByQl(@Param(Constants.WRAPPER) Wrapper wrapper);

    @Select("(select t.partstype_name,t.parts_code,t.parts_code as parts_Id from bctcmsdb.V_PARTSTYPE t where t.brdgrecog_id = #{structId}  group by t.partstype_name,t.parts_code ) union all (select t.PARTSTYPE_NAME,t.PARTS_CODE,t.PARTS_CODE as parts_ID from BCTCMSDB.T_BRDG_PARTSTYPE t where t.REMARK = '新版梁式桥') order by parts_code")
    List<FacPartTypeDto> selectPartType(String structId);

    List<FacCompTypeDto> selectCompType(IPage page,@Param("queryDto") FacQueryDto queryDto);


    List<FacPartpostDto> selectBoxroom(@Param("queryDto") FacQueryDto queryDto);


    List<FacPartpostDto> selectPartpost(@Param("boxRoom") String boxRoom,@Param("brdgType") String brdgType,@Param("structPartId") String structPartId);

    List<FacPartpostDto> selectPartpostByD(@Param("postDs") Set<String> postDs);


    //根据路段编码，查询桥梁的个数，及对应的养护等级
    List<FinspStatFacDto> selectStruct(@Param("routeCodes") List<String> routeCodes, @Param("paramDto") GeometryParamDto paramDto);

    //结构物数量
    List<FacilityNumDto> countNum(@Param("routeCodes") List<String> routeCodes);

    /**
     * 根据路段编码，查询已经巡检的桥梁结构物	(1级=1次/1月，2级=1次/2月)，判断是否包含1级和2级
     * 一年6次（【1月,2月】、【3月,4月】、【5月,6月】、根据该规则推算）
     */
    List<FinspStatFacDto> selectFinspNumByGrade(@Param("routeCodes") List<String> routeCodes
            ,@Param("grade") String grade,@Param("months") List<String> months);

    String selectExtraNewDssTyoeByQlPartCode(@Param("partsCode") String partsCode);

    List<FacDssTypeDto> selectDssTypeByCode(String code);
}
