<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.facility.mapper.FacSdMapper">

    <select id="selectDssType" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        select distinct dssgroup.group_id as dss_type, dssgroup.group_nm as dss_type_name, null as dss_type_pid
        from memsdb.dss_type dsstype
                 inner join mtmsdb.dss_type_group dssgroup on dsstype.dss_type = dssgroup.dss_type
        where dsstype.STRUCT_COMP_ID = #{structCompId}
          and exists(select 1
                     from mtmsdb.comp_dsstype_group cg
                     where cg.struct_comp_id = dsstype.STRUCT_COMP_ID
                       and cg.group_id = dssgroup.group_id)
        union
        select distinct dsstype.dss_type      as dss_type,
                        dsstype.DSS_TYPE_NAME as dss_type_name,
                        cgp.group_id          as dss_type_pid
        from memsdb.dss_type dsstype
                 left join (select distinct cg.struct_comp_id, cg.group_id, dssgroup.dss_type
                            from mtmsdb.comp_dsstype_group cg
                                     inner join mtmsdb.dss_type_group dssgroup on cg.group_id = dssgroup.group_id
        ) cgp on cgp.struct_comp_id = dsstype.STRUCT_COMP_ID and dsstype.dss_type = cgp.dss_type
        where dsstype.STRUCT_COMP_ID = #{structCompId}
    </select>
    <select id="selectPartType" resultType="com.hualu.app.module.facility.dto.FacPartTypeDto">
        select comp.struct_comp_id as parts_Id,comp.struct_comp_code as parts_code,
        comp.struct_comp_name as partstype_name,comp.p_struct_comp_id as parts_Pid
        from memsdb.base_struct_comp comp
        <if test="structPartId != null and structPartId != ''">
            start with comp.p_struct_comp_id = #{structPartId}
            connect by NOCYCLE prior comp.struct_comp_id=comp.p_struct_comp_id
        </if>
        <if test="structPartId == null || structPartId == ''">
            where comp.p_struct_comp_id='B216847AC44D4CE5BC238EDC22E4F8E3'
        </if>
    </select>
    <select id="selectCompType" resultType="com.hualu.app.module.facility.dto.FacCompTypeDto">
        select tc.part_id as code_Id, tc.comp_name as compattr_Code
        from mtmsdb.mtms_tunnel_compattr tc
        where tc.part_id = #{structPartId}
    </select>
    <select id="selectStruct" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        select tunnel_id as STRUCT_ID, nvl(TUNNEL_MAINTAIN_GRADE, 3) as grade,tunnel_name as struct_name,gis_x,gis_y
        from MTMSDB.MTMS_TUNNEL_BASIC
        where IS_ENABLE = 1
          and IS_DELETED = 0
          and ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="paramDto!=null">
            and gis_x >= #{paramDto.minX} and gis_x &lt;= #{paramDto.maxX} and gis_y >= #{paramDto.minY} and gis_y &lt;= #{paramDto.maxY}
        </if>
    </select>
    <select id="selectFinspNumByGrade" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        SELECT
            m.tunnel_id AS struct_id,
            m.TUNNEL_MAINTAIN_GRADE AS grade
        FROM
            MTMSDB.MTMS_TUNNEL_BASIC m
                JOIN memsdb.DM_FINSP f ON m.tunnel_id = f.STRUCT_ID
                AND m.IS_ENABLE = 1 and m.IS_DELETED = 0
        WHERE
            f.FACILITY_CAT = 'SD'
          AND TO_CHAR( f.INSP_DATE, 'yyyy-MM' ) IN
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
          AND m.ROUTE_CODE IN
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <choose>
            <when test="grade == 1">
                and m.TUNNEL_MAINTAIN_GRADE=1
            </when>
            <when test="grade == 2">
                and m.TUNNEL_MAINTAIN_GRADE=2
            </when>
            <otherwise>
                and (m.TUNNEL_MAINTAIN_GRADE=1 or m.TUNNEL_MAINTAIN_GRADE is null)
            </otherwise>
        </choose>
        GROUP BY m.tunnel_id,m.TUNNEL_MAINTAIN_GRADE
    </select>
    <select id="countNum" resultType="com.hualu.app.module.facility.dto.FacilityNumDto">
        select count(1) as num,route_code,'SD' as facility_cat
        from MTMSDB.MTMS_TUNNEL_BASIC
        where IS_ENABLE = 1
        and IS_DELETED = 0
        and ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by route_code
    </select>
</mapper>