<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.facility.mapper.FacQlMapper">


    <select id="selectSupportForm" resultType="com.hualu.app.module.facility.dto.SupportFormDto">
        select p.support_form, c.brdgtype_id
        from BCTCMSDB.T_BRDG_COMPATTR c,
        BCTCMSDB.T_BRDG_CHILD_PARTS p
        where c.child_parts_id = p.child_parts_id
        and c.brdgrecog_id = #{structId}
        and c.parts_code = #{structPartId}
        and c.valid_flag = 1
        <if test="structCompId != null and structCompId !=''">
            and c.compattr_code=#{structCompId}
        </if>
    </select>

    <select id="selectDssTypeByWrapper" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        select struct_comp_id as dss_type, struct_comp_name as dss_type_name, p_struct_comp_id
        from BASE_STRUCT_COMP
        ${ew.customSqlSegment}
    </select>

    <select id="selectDssTypeByQl" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        select  t1.dss_type, t1.dss_type_name, t1.struct_comp_id p_struct_comp_id
        from dss_type t1 ${ew.customSqlSegment}
    </select>
    <select id="selectCompType" resultType="com.hualu.app.module.facility.dto.FacCompTypeDto">
        select vc.compattr_code as code_id, vc.compattr_code, vc.brdgtype_id
        from bctcmsdb.T_BRDG_COMPATTR vc
        where parts_code = #{queryDto.structPartId}
          and brdgrecog_id = #{queryDto.structId}
          and vc.valid_flag = 1
        <if test="queryDto.compattrCode != null and queryDto.compattrCode !=''">
            and vc.compattr_code like #{queryDto.compattrCode}
        </if>
        order by subStr(vc.compattr_code,0,1),TO_NUMBER(translate(vc.compattr_code, '#' || translate(vc.compattr_code, '0123456789', '#'), '/'))
    </select>
    <select id="selectBoxroom" resultType="com.hualu.app.module.facility.dto.FacPartpostDto">
        select d.box_room, c.brdgtype_id
        from BCTCMSDB.t_brdg_compattr c
                 left join BCTCMSDB.t_brdg_child_parts d on c.child_parts_id = d.child_parts_id
        where c.brdgrecog_id = #{queryDto.structId}
          and c.parts_code = #{queryDto.structPartId}
          and c.compattr_code = #{queryDto.structCompId}
    </select>
    <select id="selectPartpost" resultType="com.hualu.app.module.facility.dto.FacPartpostDto">
        select u.partpost_d, u.post_name
        from BCTCMSDB.T_BRDG_PARTPOST u
        where
        1=1
        <if test="structPartId != null">
            and (u.part_code = #{structPartId} or u.subpart_code = #{structPartId})
            and (u.brdg_code = #{brdgType} or u.brdg_code is null)
        </if>
        <if test="boxRoom != null">
            and ((substr(u.post_code, 1, 1) &lt;= substr(#{boxRoom}, 1, 1)
            and substr(u.post_code, 3, 1) &lt;= substr(#{boxRoom}, 2, 1)) or length(u.post_code) &lt; 3)
        </if>
    </select>
    <select id="selectPartpostByD" resultType="com.hualu.app.module.facility.dto.FacPartpostDto">
        select u.partpost_d, u.post_name
        from BCTCMSDB.T_BRDG_PARTPOST u
        where u.partpost_d in
        <foreach collection="postDs" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


    <select id="selectStruct" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        SELECT
            br.BRDGRECOG_ID as struct_id,nvl( m.MAINTAIN_GRADE, 2 ) as grade,br.brdg_name as struct_name,br.latitude as gis_y,br.longitude as gis_x
        FROM
            BCTCMSDB.T_BRDG_RECOGMANAGE m
                JOIN BCTCMSDB.t_brdg_brdgrecog br ON m.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
                AND m.MAIN_BRDGRECOG_ID = m.BRDGRECOG_ID and br.VALID_FLAG = 1 and m.VALID_FLAG = 1
        where br.ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="paramDto!=null">
            and longitude >= #{paramDto.minX} and longitude &lt;= #{paramDto.maxX} and latitude >= #{paramDto.minY} and latitude &lt;= #{paramDto.maxY}
        </if>
    </select>
    <select id="selectFinspNumByGrade" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        SELECT
            br.BRDGRECOG_ID as struct_id,nvl( m.MAINTAIN_GRADE, 2 )  as grade
        FROM
            memsdb.DM_FINSP f
                JOIN BCTCMSDB.T_BRDG_RECOGMANAGE m ON f.STRUCT_ID = m.MAIN_BRDGRECOG_ID AND m.MAIN_BRDGRECOG_ID = m.BRDGRECOG_ID AND m.VALID_FLAG = 1
                JOIN BCTCMSDB.t_brdg_brdgrecog br ON m.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and br.VALID_FLAG = 1
        WHERE
            TO_CHAR( f.INSP_DATE, 'yyyy-MM' ) in
            <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
          AND f.FACILITY_CAT = 'QL' and br.ROUTE_CODE in
            <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>

            <choose>
                <when test="grade == 1">
                    and m.MAINTAIN_GRADE=1
                </when>
                <otherwise>
                    and m.MAINTAIN_GRADE&lt;>1
                </otherwise>
            </choose>
        GROUP BY br.BRDGRECOG_ID,m.MAINTAIN_GRADE
    </select>
    <select id="countNum" resultType="com.hualu.app.module.facility.dto.FacilityNumDto">
        SELECT count(1) as num,route_code,'QL' as facility_cat
        FROM
        BCTCMSDB.T_BRDG_RECOGMANAGE m
        JOIN BCTCMSDB.t_brdg_brdgrecog br ON m.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
        AND m.MAIN_BRDGRECOG_ID = m.BRDGRECOG_ID and br.VALID_FLAG = 1 and m.VALID_FLAG = 1
        where br.ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by route_code
    </select>

    <select id="selectExtraNewDssTyoeByQlPartCode" resultType="java.lang.String">
        select x.DSS_TYPE from MEMSDB.NM_INSP_CONTENT x where x.ID in (
            select t.PART_CODE_NEW from BCTCMSDB.BRIDGE_PART_REL t where t.PART_CODE_OLD=#{partsCode}) and ROWNUM=1
    </select>

    <select id="selectDssTypeByCode" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        select dt.DSS_TYPE,dt.DSS_TYPE_NAME from MEMSDB.DSS_TYPE_NEW dt where dt.DSS_TYPE=#{code}
    </select>
</mapper>