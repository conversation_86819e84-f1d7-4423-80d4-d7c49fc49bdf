package com.hualu.app.module.facility.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.basedata.service.TBrdgBrdgrecogService;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatDto;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import com.hualu.app.module.facility.mapper.FacQlMapper;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.comm.entity.BaseStructComp;
import com.hualu.app.module.mems.comm.service.BaseStructCompService;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.dss.service.DsstypeDssdegreeService;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_FutureHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.util.hp.H_UniqueConstraintHelper;
import com.tg.dev.mybatisplus.service.PageService;
import javax.annotation.Resource;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 桥梁
 * <AUTHOR>
 */
@Component
public class FacQlImpl extends ServiceImpl<FacQlMapper, TBrdgBrdgrecog> implements IFacBase, IFinspStatBase {

    @Autowired
    BaseStructCompService compService;

    @Autowired
    DsstypeDssdegreeService dssdegreeService;

    @Resource
    private TBrdgBrdgrecogService brdgBrdgrecogService;

    @Autowired
    PageService pageService;

    @Autowired
    HttpServletRequest request;

    @Override
    public List<BaseStructDto> getNearBaseStruct(List<String> routeCodes, String facilityCat, Double lat, Double lng) {
        LambdaQueryWrapper<TBrdgBrdgrecog> wrapper = new LambdaQueryWrapper<>();
        double nearLength = 0.00005;
        double latitude = ObjectUtil.defaultIfNull(lat, 0d);
        double longitude = ObjectUtil.defaultIfNull(lng, 0d);
        wrapper.in(TBrdgBrdgrecog::getRouteCode, routeCodes);
        wrapper.eq(TBrdgBrdgrecog::getValidFlag, 1);
        wrapper.between(TBrdgBrdgrecog::getLongitude,latitude-nearLength,latitude + nearLength)
            .between(TBrdgBrdgrecog::getLatitude,longitude-nearLength,longitude + nearLength);
        List<TBrdgBrdgrecog> bridgeList = brdgBrdgrecogService.list(wrapper);
        return bridgeList.stream().limit(400).map(s -> {
            BaseStructDto dto = new BaseStructDto();
            dto.setStructId(s.getBrdgrecogId());
            dto.setStructName(s.getBrdgName());
            dto.setCntrStake(s.getLogicCntrStakeNum());
            dto.setFrameNum(s.getFrameNum());
            dto.setX(s.getLongitude());
            dto.setY(s.getLatitude());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void setDmTaskAccptDetailView(DmTaskAccptDetailDto dto) {
        BaseStructDto item = H_BasedataHepler.getStructDto(dto.getStructId(), H_BasedataHepler.QL);
        if (item != null){
            dto.setLaneNm(item.getStructName());
        }
    }

    @Override
    public void initDmFinspRecordView(DmFinspRecord item) {
        item.setDssDegreeName(dssdegreeService.getDssdegressName(item.getDssType(),item.getDssDegree()));
    }

    /**
     * 获取桥梁病害部位名称
     * @param partpostD
     * @return
     */
    public String getDssPositionName(Set<String> partpostDs,String partpostD){
        if (CollectionUtil.isEmpty(partpostDs)){
            return "";
        }
        String postKey = H_UniqueConstraintHelper.createUnique(partpostDs);
        List<FacPartpostDto> dtos = (List<FacPartpostDto>) CustomRequestContextHolder.get(postKey);
        if (CollectionUtil.isEmpty(dtos)){
            dtos = baseMapper.selectPartpostByD(partpostDs);
            CustomRequestContextHolder.set(postKey,dtos);
        }
        return dtos.stream().filter(e->partpostD.equals(e.getPartpostD())).map(FacPartpostDto::getPostName).findFirst().orElse(null);
    }

    @Override
    public List<FacPartpostDto> selectPartpost(FacQueryDto queryDto) {
        List<FacPartpostDto> boxRooms = baseMapper.selectBoxroom(queryDto);
        boxRooms.removeAll(Collections.singleton(null));
        FacPartpostDto dto = new FacPartpostDto();
        if (CollectionUtil.isNotEmpty(boxRooms) && boxRooms.size() > 0){
            dto = boxRooms.get(0);
        }
        if (StrUtil.isBlank(dto.getBrdgtypeId())){
            return Lists.newArrayList();
        }
        List<FacPartpostDto> facPartpostDtos = baseMapper.selectPartpost(dto.getBoxRoom(), dto.getBrdgtypeId(), queryDto.getStructPartId());
        return facPartpostDtos;
    }

    @Override
    public List<DsstypeDssdegree> selectDssDegress(String dssType) {
        return dssdegreeService.selectDegressByDssType(dssType);
    }

    @Override
    public List<DmFinspResult> initDmFinspItems(List<DmFinspItem> items, DmFinsp dmFinsp) {
        List<DmFinspResult> dtos = Lists.newArrayList();
        items.forEach(item->{
            DmFinspResult dto = new DmFinspResult();
            dto.setFinspId(dmFinsp.getFinspId());
            dto.setFinspItemId(item.getFinspItemId());
            dto.setIssueDesc("正常");
            dto.setInspResult("");
            dto.setMntnAdvice("日常保养");
            dto.setRemark(item.getFinRemark());
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public String getFacilityCat() {
        return H_BasedataHepler.QL;
    }

    @Override
    public List<FacDssTypeDto> selectDssType(FacQueryDto queryDto) {
        if (StrUtil.isBlank(queryDto.getStructPartId())){
            List<FacDssTypeDto> dtos = baseMapper.selectDssType();
            return dtos;
        }

        List<SupportFormDto> formList = baseMapper.selectSupportForm(queryDto.getStructId(), queryDto.getStructPartId(), queryDto.getStructCompId());
        //数据存在空的情况，直接移除null数据
        formList.removeAll(Collections.singleton(null));
        String brdgTypeId = "";
        if (CollectionUtil.isNotEmpty(formList)){
            brdgTypeId = formList.get(0).getBrdgtypeId();
        }

        String supportType = "";
        if ("11".equals(queryDto.getStructPartId()) && formList !=null && !formList.isEmpty()){
            supportType = formList.get(0).getSupportForm();
            if (StrUtil.isBlank(supportType)){
                supportType = baseMapper.selectBearType(queryDto.getStructId());
            }
        }
        return selectDssTypeQl(supportType,brdgTypeId,queryDto.getStructPartId());
    }

    @Override
    public List<FacPartTypeDto> selectPartTypes(FacQueryDto queryDto) {
        return baseMapper.selectPartType(queryDto.getStructId());
    }

    @Override
    public IPage<FacCompTypeDto> selectCompTypes(FacQueryDto queryDto) {
        Page<FacCompTypeDto> page = pageService.parsePageParam(request);
        List<FacCompTypeDto> typeDtos = baseMapper.selectCompType(page, queryDto);
        page.setRecords(typeDtos);
        return page;
    }

    public List<FacDssTypeDto> selectDssTypeQl(String supportType, String brdgType, String partsCode){

        List<FacDssTypeDto> dssTypeDtos = Lists.newArrayList();
        List<BaseStructComp> comps = compService.selectStrcutCompByQL(partsCode);

        String structCompId = "";
        for (BaseStructComp item : comps) {
            List<BaseStructComp> dbStructs = compService.selectStructCompByTj(item.getPStructCompId());
            if (CollectionUtil.isEmpty(dbStructs)){
                continue;
            }
            BaseStructComp dbRow = dbStructs.get(0);
            String partId = dbRow.getPStructCompId();
            String partCode = dbRow.getPartCode();
            if (brdgType.equals(partCode)){
                structCompId = item.getStructCompId();
                break;
            }

            if ("0".equals(partId)){
                structCompId = item.getStructCompId();
            }
        }

        List<FacDssTypeDto> typeList = null;
        List<String> partCodeParam = null;

        QueryWrapper queryWrapper = getQueryWrapper(structCompId);
        //查询支座病害
        if ("11".equals(partsCode)){
            if (StrUtil.isNotBlank(supportType)){
                String[] sTypeArrs = supportType.split(",");
                partCodeParam = Arrays.asList(sTypeArrs);

                QueryWrapper qw = ObjectUtil.clone(queryWrapper);
                qw.in("part_code",partCodeParam);
                List<FacDssTypeDto> supportTypes = baseMapper.selectDssTypeByWrapper(qw);
                if (CollectionUtil.isEmpty(supportTypes)){
                    partCodeParam = Lists.newArrayList("8");
                }else {
                    typeList = supportTypes;
                }
            }else {
                partCodeParam = Lists.newArrayList("8");
            }
        }

        if (typeList == null){
            QueryWrapper qw = ObjectUtil.clone(queryWrapper);
            if (CollectionUtil.isNotEmpty(partCodeParam)){
                qw.in("part_code",partCodeParam);
            }
            typeList = baseMapper.selectDssTypeByWrapper(qw);
        }

        List<String> compIds = Lists.newArrayList();
        if (typeList.size() == 0){
            compIds.add(structCompId);
        }else {
            typeList.forEach(item->{
                item.setPStructCompId("");
                dssTypeDtos.add(item);
                compIds.add(item.getDssType());
            });
        }

        QueryWrapper dssQueryWrapper = new QueryWrapper();
        dssQueryWrapper.in("t1.struct_comp_id",compIds);
        dssQueryWrapper.exists("select 1 from dsstype_in_ry r where r.code=t1.dss_type");
        dssQueryWrapper.orderByAsc("t1.dss_order_number");
        List<FacDssTypeDto> zzDtos = baseMapper.selectDssTypeByQl(dssQueryWrapper);
        if (CollectionUtil.isNotEmpty(zzDtos)){
            dssTypeDtos.addAll(zzDtos);
        }
        String extraNewDssTyoeByQlPartCode =  baseMapper.selectExtraNewDssTyoeByQlPartCode(partsCode);
        if (extraNewDssTyoeByQlPartCode!=null&&!extraNewDssTyoeByQlPartCode.isEmpty()){
            String[] dssTypeArr = extraNewDssTyoeByQlPartCode.split(",");
            for(String code:dssTypeArr) {
                dssTypeDtos.addAll(baseMapper.selectDssTypeByCode(code));
            }
        }
        return dssTypeDtos;
    }

    private QueryWrapper getQueryWrapper(String structCompId){
        QueryWrapper qw = new QueryWrapper();
        qw.eq("p_struct_comp_id",structCompId);
        return qw;
    }

    @Override
    public FinspStatDto getFinspStat(List<String> routeCodes,String month) {

        //获取结构ID及养护等级
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,null);
        //根据养护等级分组
        Map<String, List<FinspStatFacDto>> gradeMap = finspStatFacDtos.stream().collect(Collectors.groupingBy(FinspStatFacDto::getGrade));
        List<CompletableFuture<List<FinspStatFacDto>>> futureList = new ArrayList<>();
        gradeMap.forEach((key, value) -> {
            //异步查询sql
            CompletableFuture<List<FinspStatFacDto>> future = CompletableFuture.supplyAsync(() -> {
                List<String> months = H_FacQueryHelper.getMonths(getFacilityCat(), key, month);
                List<FinspStatFacDto> dtos = baseMapper.selectFinspNumByGrade(routeCodes, key, months);
                return dtos;
            });
            futureList.add(future);
        });
        List<FinspStatFacDto> dtoList = H_FutureHelper.sequenceList(futureList).join();
        //已检查的结构物
        List<String> checkStructIds = dtoList.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
        //用户管养的所有结构物ID
        List<String> allStructIds = finspStatFacDtos.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());

        int total = finspStatFacDtos.size();
        allStructIds.removeAll(checkStructIds);
        int noCheckNum = allStructIds.size();

        String msg = getNoticeMsg(gradeMap,allStructIds,month);
        // checkNum = total - noCheckNum
        return new FinspStatDto().setFacilityCat(getFacilityCat()).setCheckNum(total-noCheckNum).setStructNum(total).setNoticeMsg(msg);
    }

    @Override
    public List<FinspStatFacDto> getGeometryStat(List<String> routeCodes, GeometryParamDto paramDto) {
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,paramDto);
        finspStatFacDtos.forEach(item->{
            item.setFacilityCat(getFacilityCat());
        });
        return finspStatFacDtos;
    }

    @Override
    public List<FacilityNumDto> countNum(Set<String> routeCodes) {
        return baseMapper.countNum(Lists.newArrayList(routeCodes));
    }

    private String getNoticeMsg(Map<String, List<FinspStatFacDto>> gradeMap,List<String> noCheckList,String month){
        List<String> noticeMsg = Lists.newArrayList();
        //判断未检查的桥梁各养护等级的个数
        gradeMap.forEach((key, value) -> {
            if (!H_FacQueryHelper.isNotice(getFacilityCat(),key,month)){
                return;
            }
            List<String> structList = value.stream().filter(e -> noCheckList.contains(e.getStructId())).collect(Collectors.toList()).stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
            if (structList != null && structList.size() > 0){
                String cnNum = NumberChineseFormatter.format(Long.valueOf(key), false);
                String format = StrUtil.format("{}级检查桥梁(1次/{}月)：剩余{}座未检查", cnNum,key, structList.size());
                noticeMsg.add(format);
            }
        });
        String msg = noticeMsg.stream().collect(Collectors.joining("；"));
        return msg;
    }
}
