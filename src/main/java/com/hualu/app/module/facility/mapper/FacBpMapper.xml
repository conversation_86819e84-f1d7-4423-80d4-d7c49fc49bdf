<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.facility.mapper.FacBpMapper">


    <select id="selectPartType" resultType="com.hualu.app.module.facility.dto.FacPartTypeDto">
        <choose>
            <when test="queryDto.finVersion=='201801'">
                SELECT bpsc.STRUCT_COMP_ID   as parts_Id,
                bpsc.STRUCT_COMP_ID   as parts_code,
                bpsc.P_STRUCT_COMP_ID as parts_Pid,
                bpsc.STRUCT_COMP_NAME as partstype_name
                from MEMSDB.BASE_STRUCT_COMP bpsc START
                WITH bpsc.STRUCT_COMP_ID='70C46495CA0B01E0E05380012EC801E0'
                CONNECT BY PRIOR bpsc.STRUCT_COMP_ID=bpsc.P_STRUCT_COMP_ID
            </when>
            <otherwise>
                select STRUCT_COMP_ID   as parts_Id,
                STRUCT_COMP_ID   as parts_code,
                P_STRUCT_COMP_ID as parts_Pid,
                STRUCT_COMP_NAME as partstype_name from MEMSDB.BASE_STRUCT_COMP where P_STRUCT_COMP_ID='20230518'
            </otherwise>
        </choose>
    </select>
    <select id="selectDssType" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        SELECT DSS_TYPE,
               STRUCT_COMP_ID AS p_struct_Comp_Id,
               DSS_TYPE_NAME
        FROM MEMSDB.DSS_TYPE d1
        where STRUCT_COMP_ID = #{structPartId}
          and not exists(select 1
                         from MEMSDB.DSS_TYPE d
                         where d.STRUCT_COMP_ID = '7591554E4B39444B987E5583762FC661' and d.dss_type = d1.dss_type)
    </select>
    <select id="selectCompType" resultType="com.hualu.app.module.facility.dto.FacCompTypeDto">
        <choose>
            <when test="finVersion=='201801'">
                select t.id as code_Id, t.position_name as compattr_Code
                from hsmsdb.hsms_slope_dss_position t
                where t.struct_comp_id = #{structPartId}
            </when>
            <otherwise>
                select STRUCT_COMP_ID   as code_Id,
                STRUCT_COMP_NAME as compattr_Code from MEMSDB.BASE_STRUCT_COMP where P_STRUCT_COMP_ID=#{structPartId}
            </otherwise>
        </choose>
    </select>
    <select id="selectStruct" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">

        select slope_id as struct_id, '1' as grade,slope_name as struct_name,GIS_X,GIS_Y
        from hsmsdb.HSMS_SLOPE_INFO
        where IS_DELETED = 0
          and route_code in
            <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
            </foreach>
            <if test="isImportant == true">
          and is_important = 1
            </if>

            <if test="paramDto!=null">
          and gis_x >= #{paramDto.minX} and gis_x &lt;= #{paramDto.maxX} and gis_y >= #{paramDto.minY} and gis_y &lt;= #{paramDto.maxY}
            </if>

    </select>
    <select id="selectFinspNumByGrade" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        SELECT
            m.slope_id AS struct_id
        FROM
            hsmsdb.HSMS_SLOPE_INFO m
                JOIN memsdb.${tableName} f ON m.slope_id = f.STRUCT_ID
                AND m.IS_DELETED = 0
        WHERE
            f.FACILITY_CAT = 'BP'
          AND TO_CHAR( f.INSP_DATE, 'yyyy-MM' ) IN
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
          AND m.ROUTE_CODE IN
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY m.slope_id
    </select>
    <select id="selectDssTypeByPrefix" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        select DSS_TYPE, STRUCT_COMP_ID AS p_struct_Comp_Id, DSS_TYPE_NAME from memsdb.DSS_TYPE_NEW where DSS_TYPE like CONCAT(#{dssTypePrefix}, '%')
    </select>
    <select id="countNum" resultType="com.hualu.app.module.facility.dto.FacilityNumDto">
        select count(1) as num,route_code,'BP' as facility_cat
        from hsmsdb.HSMS_SLOPE_INFO
        where IS_DELETED = 0
        and route_code in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY route_code
    </select>
</mapper>