package com.hualu.app.module.facility.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseTollgate;
import com.hualu.app.module.basedata.mapper.BaseTollgateMapper;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.dto.FacDssTypeDto;
import com.hualu.app.module.facility.dto.FacPartTypeDto;
import com.hualu.app.module.facility.dto.FacQueryDto;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FacSfzImpl extends ServiceImpl<BaseTollgateMapper, BaseTollgate> implements IFacBase {

    @Autowired
    private BaseDatathirdDicService dicService;

    @Override
    public void setDmTaskAccptDetailView(DmTaskAccptDetailDto dto) {
        BaseStructDto item = H_BasedataHepler.getStructDto(dto.getStructId(), H_BasedataHepler.SFZ);
        if (item != null){
            String dicName = dicService.getDicName("SFZ_QYHF", dto.getStructPartId());
            dto.setLaneNm(item.getStructName()+"【"+dicName+"】");
        }
    }

    @Override
    public List<DsstypeDssdegree> selectDssDegress(String dssType) {
        return null;
    }

    @Override
    public List<DmFinspResult> initDmFinspItems(List<DmFinspItem> items, DmFinsp dmFinsp) {
        return null;
    }

    @Override
    public String getFacilityCat() {
        return "SFZ";
    }

    @Override
    public List<FacDssTypeDto> selectDssType(FacQueryDto queryDto) {
        return null;
    }

    @Override
    public List<FacPartTypeDto> selectPartTypes(FacQueryDto queryDto) {
        List<BaseDatathirdDic> list = dicService.listDicByItem("SFZ_QYHF");
        List<FacPartTypeDto> listDtos = new ArrayList<>();
        for(BaseDatathirdDic dic : list){
            FacPartTypeDto dto = new FacPartTypeDto();
            dto.setPartsId(dic.getAttributeCode());
            dto.setPartstypeName(dic.getAttributeValue());
            listDtos.add(dto);
        }
        return listDtos;
    }
}
