package com.hualu.app.module.facility.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 设施信息查询公用类
 */
@Data
public class FacQueryDto implements Serializable {

    //结构物ID
    private String structId;

    //部件ID
    private String structPartId;

    //构建ID
    private String structCompId;

    //设施类型
    private String facilityCat;

    //构件信息
    private String compattrCode;

    //用于桥梁和涵洞 JCJC
    private String action;

    //经常检查版本，用于区分不同版本来使用
    private String finVersion;

    //经常检查单ID
    private String finspId;

    //南粤边坡经常检查前缀：BPJC-NY
    private String dssTypePrefix;
}
