package com.hualu.app.module.facility;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import java.util.Collections;
import lombok.SneakyThrows;

import java.util.List;

/**
 * 设施类型的日常、经常、定检公用基类
 */
public interface IFacBase {

    default List<BaseStructDto> getNearBaseStruct(List<String> routeCodes, String facilityCat, Double lat, Double lng) {
        return Collections.emptyList();
    }

    /**
     * 回显病害管理界面显示值
     * @param dto
     */
    default void setDssInfoView(DssInfoDto dto){}

    /**
     * 回显维修工程记录
     * @param dto
     */
    default void setDmTaskAccptDetailView(DmTaskAccptDetailDto dto){};

    /**
     * 导出excel数据
     * @param finsp
     * @param records
     * @param results
     * @param fileReposity
     * @return
     */
    @SneakyThrows
    default String exportExcel(DmFinsp finsp,List<DmFinspRecord> records,List<DmFinspResult> results,String fileReposity,Integer signType){return "";}

    /**
     * 设置病害的finspItemId，用于检查结论自动生成
     * @param record 检查病害
     * @param dmFinsp 检查主单
     */
    default void setFinspItemId(DmFinspRecord record,DmFinsp dmFinsp){};

    /**
     * 设置经常检查病害页面回显值
     * @param item
     */
    default void initDmFinspRecordView(DmFinspRecord item){
        //默认不设置，有特殊要求重写该服务
    };
    /**
     * 查询病害严重程度
     * @param dssType
     * @return
     */
    List<DsstypeDssdegree> selectDssDegress(String dssType);

    /**
     * 初始化检查结论
     * @param items
     * @return
     */
    List<DmFinspResult> initDmFinspItems(List<DmFinspItem> items, DmFinsp dmFinsp);

    /**
     * 设施类型
     * @return
     */
    String getFacilityCat();

    /**
     * 查询病害类型
     * @param queryDto
     * @return
     */
    List<FacDssTypeDto> selectDssType(FacQueryDto queryDto);


    /**
     * 查询结构物部件信息
     * @param queryDto
     * @return
     */
    List<FacPartTypeDto> selectPartTypes(FacQueryDto queryDto);

    /**
     * 查询构件信息（桥梁和涵洞专用）
     * @param queryDto
     * @return
     */
    default IPage<FacCompTypeDto> selectCompTypes(FacQueryDto queryDto){return null;}

    /**
     * 查询病害部位
     * @param queryDto
     * @return
     */
    default List<FacPartpostDto> selectPartpost(FacQueryDto queryDto){return null;}
}
