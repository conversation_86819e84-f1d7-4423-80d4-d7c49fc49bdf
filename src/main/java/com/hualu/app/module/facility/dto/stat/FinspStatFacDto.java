package com.hualu.app.module.facility.dto.stat;

import lombok.Data;
import lombok.experimental.Accessors;
import net.minidev.json.annotate.JsonIgnore;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class FinspStatFacDto implements Serializable {

    @JsonIgnore
    //结构类型
    private String facilityCat;

    //结构ID
    private String structId;

    //路段编码
    //private String routeCode;

    //养护等级
    private String grade;

    //结构物名称
    private String structName;

    //经度
    private Double gisX;

    //纬度
    private Double gisY;
}
