package com.hualu.app.module.facility.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.MtmsTunnelBasic;
import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.hualu.app.module.basedata.service.MtmsTunnelBasicService;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatDto;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import com.hualu.app.module.facility.mapper.FacSdMapper;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_FutureHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.mybatisplus.service.PageService;
import javax.annotation.Resource;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class FacSdImpl extends ServiceImpl<FacSdMapper, MtmsTunnelBasic> implements IFacBase, IFinspStatBase {

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    PageService pageService;

    @Resource
    private MtmsTunnelBasicService tunnelService;

    @Autowired
    HttpServletRequest request;

    private static final String SD = "SD";

    @Override
    public List<BaseStructDto> getNearBaseStruct(List<String> routeCodes, String facilityCat, Double lat, Double lng) {
        LambdaQueryWrapper<MtmsTunnelBasic> wrapper = new LambdaQueryWrapper<>();
        double nearLength = 0.00005;
        double latitude = ObjectUtil.defaultIfNull(lat, 0d);
        double longitude = ObjectUtil.defaultIfNull(lng, 0d);
        wrapper.in(MtmsTunnelBasic::getRouteCode, routeCodes);
        wrapper.eq(MtmsTunnelBasic::getIsEnable, "1");
        wrapper.eq(MtmsTunnelBasic::getIsDeleted, "0");
        wrapper.between(MtmsTunnelBasic::getGisX,latitude-nearLength,latitude + nearLength)
            .between(MtmsTunnelBasic::getGisY,longitude-nearLength,longitude + nearLength);
        List<MtmsTunnelBasic> bridgeList = tunnelService.list(wrapper);
        return bridgeList.stream().limit(400).map(s -> {
            BaseStructDto dto = new BaseStructDto();
            dto.setStructId(s.getTunnelId());
            dto.setStructName(s.getTunnelName());
            dto.setCntrStake(s.getLogicCntrStake());
            dto.setX(s.getGisX());
            dto.setY(s.getGisY());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void setDmTaskAccptDetailView(DmTaskAccptDetailDto dto) {
        BaseStructDto item = H_BasedataHepler.getStructDto(dto.getStructId(), H_BasedataHepler.SD);
        if (item!= null){
            dto.setLaneNm(item.getStructName());
        }
    }

    @Override
    public void initDmFinspRecordView(DmFinspRecord item) {
        item.setLaneName(dicService.getDicName("LANE12",item.getLane()));
        item.setDssDegree(dicService.getDicName("DSS_DEGREE",item.getDssDegree()));
        item.setStructCompName(getCompName(item.getTunnelMouth(),item.getStructCompId()));
        item.setTunnelMouthName(getPartName(item.getTunnelMouth()));
        item.setStructPartName(getChildPartName(item.getTunnelMouth(),item.getStructPartId()));
    }

    @Override
    public List<DsstypeDssdegree> selectDssDegress(String dssType) {
        List<DsstypeDssdegree> results = Lists.newArrayList();
        List<BaseDatathirdDic> datathirdDics = dicService.listDicByItem(dssType);
        datathirdDics.forEach(item->{
            DsstypeDssdegree row = new DsstypeDssdegree();
            row.setDssDegree(item.getAttributeCode());
            row.setDssDegreeName(item.getAttributeValue());
            results.add(row);
        });
        return results;
    }

    @Override
    public List<DmFinspResult> initDmFinspItems(List<DmFinspItem> items, DmFinsp dmFinsp) {
        List<DmFinspResult> dtos = Lists.newArrayList();
        items.forEach(item->{
            DmFinspResult dto = new DmFinspResult();
            dto.setFinspId(dmFinsp.getFinspId());
            dto.setFinspItemId(item.getFinspItemId());
            dto.setIssueDesc("未发现病害");
            dto.setInspResult("");
            dto.setRemark("");
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public String getFacilityCat() {
        return H_BasedataHepler.SD;
    }

    @Override
    public List<FacDssTypeDto> selectDssType(FacQueryDto queryDto) {
        return baseMapper.selectDssType(queryDto.getStructPartId());
    }

    @Override
    public List<FacPartTypeDto> selectPartTypes(FacQueryDto queryDto) {
        return baseMapper.selectPartType(queryDto.getStructPartId());
    }

    @Override
    public IPage<FacCompTypeDto> selectCompTypes(FacQueryDto queryDto) {
        Page<FacCompTypeDto> page = pageService.parsePageParam(request);
        List<FacCompTypeDto> typeDtos = baseMapper.selectCompType(page,queryDto.getStructPartId());
        page.setRecords(typeDtos);
        return page;
    }


    /**
     * 获取部件名称
     * @param tunnelMouth 隧道部件ID
     * @return
     */
    private String getPartName(String tunnelMouth){
        if (StrUtil.isBlank(tunnelMouth)){
            return null;
        }
        FacQueryDto dto = new FacQueryDto();
        List<FacPartTypeDto> partTypeDtos = selectPartTypes(dto);
        if (CollectionUtil.isEmpty(partTypeDtos)){
            return null;
        }
        return partTypeDtos.stream().filter(e->e.getPartsId().equals(tunnelMouth))
                .map(FacPartTypeDto::getPartstypeName).findFirst().orElse(null);
    }

    /**
     * 获取子部件名称
     * @param tunnelMouth
     * @param structPartId
     * @return
     */
    private String getChildPartName(String tunnelMouth,String structPartId){
        if (StrUtil.isBlank(tunnelMouth)){
            return null;
        }
        FacQueryDto dto = new FacQueryDto();
        dto.setStructPartId(tunnelMouth);
        List<FacPartTypeDto> partTypeDtos = selectPartTypes(dto);
        if (CollectionUtil.isEmpty(partTypeDtos)){
            return null;
        }
        return partTypeDtos.stream().filter(e->e.getPartsId().equals(structPartId))
                .map(FacPartTypeDto::getPartstypeName).findFirst().orElse(null);
    }

    /**
     * 构件显示名称
     * @param structPartId
     * @param compId
     * @return
     */
    private String getCompName(String structPartId,String compId){
        if (StrUtil.isBlank(structPartId) || StrUtil.isBlank(compId)){
            return null;
        }
        String key = "sd:comp:"+structPartId;
        List<FacCompTypeDto> typeDtos = (List<FacCompTypeDto>) CustomRequestContextHolder.get(key);
        if (CollectionUtil.isEmpty(typeDtos)){
            Page<FacCompTypeDto> page = new Page<>();
            page.setCurrent(0);
            page.setSize(200);
            typeDtos = baseMapper.selectCompType(page, structPartId);
            CustomRequestContextHolder.set(key,typeDtos);
        }
        if (CollectionUtil.isEmpty(typeDtos)){
            return null;
        }
        return typeDtos.stream().filter(e->e.getCodeId().equals(compId)).map(FacCompTypeDto::getCompattrCode).findFirst().orElse(null);
    }

    @Override
    public FinspStatDto getFinspStat(List<String> routeCodes, String month) {

        //获取结构ID及养护等级
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,null);
        //根据养护等级分组
        Map<String, List<FinspStatFacDto>> gradeMap = finspStatFacDtos.stream().collect(Collectors.groupingBy(FinspStatFacDto::getGrade));
        List<CompletableFuture<List<FinspStatFacDto>>> futureList = new ArrayList<>();
        gradeMap.forEach((key, value) -> {
            //异步查询sql
            CompletableFuture<List<FinspStatFacDto>> future = CompletableFuture.supplyAsync(() -> {
                List<String> months = H_FacQueryHelper.getMonths(getFacilityCat(), key, month);
                List<FinspStatFacDto> dtos = baseMapper.selectFinspNumByGrade(routeCodes, key, months);
                return dtos;
            });
            futureList.add(future);
        });
        List<FinspStatFacDto> dtoList = H_FutureHelper.sequenceList(futureList).join();
        //已检查的结构物
        List<String> checkStructIds = dtoList.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
        //用户管养的所有结构物ID
        List<String> allStructIds = finspStatFacDtos.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());

        int total = finspStatFacDtos.size();
        allStructIds.removeAll(checkStructIds);
        int noCheckNum = allStructIds.size();
        String msg = getNoticeMsg(gradeMap,allStructIds,month);
        // checkNum = total - noCheckNum
        return new FinspStatDto().setFacilityCat(getFacilityCat()).setCheckNum(total-noCheckNum).setStructNum(total).setNoticeMsg(msg);
    }

    @Override
    public List<FinspStatFacDto> getGeometryStat(List<String> routeCodes, GeometryParamDto paramDto) {
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,paramDto);
        finspStatFacDtos.forEach(item->{
            item.setFacilityCat(getFacilityCat());
        });
        return finspStatFacDtos;
    }

    @Override
    public List<FacilityNumDto> countNum(Set<String> routeCodes) {
        return baseMapper.countNum(Lists.newArrayList(routeCodes));
    }

    private String getNoticeMsg(Map<String, List<FinspStatFacDto>> gradeMap,List<String> noCheckList,String month){
        List<String> noticeMsg = Lists.newArrayList();
        //判断未检查的桥梁各养护等级的个数
        gradeMap.forEach((key, value) -> {
            if (!H_FacQueryHelper.isNotice(getFacilityCat(),key,month)){
                return;
            }
            List<String> structList = value.stream().filter(e -> noCheckList.contains(e.getStructId())).collect(Collectors.toList()).stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
            if (structList != null && structList.size() > 0){
                String cnNum = NumberChineseFormatter.format(Long.valueOf(key), false);
                String format = StrUtil.format("{}级隧道(1次/{}月)：剩余{}洞未检查", cnNum,key, structList.size());
                noticeMsg.add(format);
            }
        });
        String msg = noticeMsg.stream().collect(Collectors.joining("；"));
        return msg;
    }
}
