package com.hualu.app.module.facility.impl;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.TBrdgBrdgrecog;
import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.hualu.app.module.basedata.service.TClvrtClvrtrecogService;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatDto;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import com.hualu.app.module.facility.mapper.FacHdMapper;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.dss.service.DsstypeDssdegreeService;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.nminsp.entity.NmInspContent;
import com.hualu.app.module.mems.nminsp.service.NmInspContentService;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_FutureHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.mybatisplus.service.PageService;
import javax.annotation.Resource;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 涵洞
 * <AUTHOR>
 */
@Service
public class FacHdImpl extends ServiceImpl<FacHdMapper, TClvrtClvrtrecog> implements IFacBase, IFinspStatBase {

    @Autowired
    DsstypeDssdegreeService dssdegreeService;

    @Autowired
    PageService pageService;

    @Resource
    private TClvrtClvrtrecogService clvrtrecogService;

    @Resource
    private NmInspContentService contentService;

    @Autowired
    HttpServletRequest request;

    @Override
    public List<BaseStructDto> getNearBaseStruct(List<String> routeCodes, String facilityCat, Double lat, Double lng) {
        LambdaQueryWrapper<TClvrtClvrtrecog> wrapper = new LambdaQueryWrapper<>();
        double nearLength = 0.00005;
        double latitude = ObjectUtil.defaultIfNull(lat, 0d);
        double longitude = ObjectUtil.defaultIfNull(lng, 0d);
        wrapper.in(TClvrtClvrtrecog::getRouteCode, routeCodes);
        wrapper.eq(TClvrtClvrtrecog::getValidFlag, 1);
        wrapper.between(TClvrtClvrtrecog::getLongitude,latitude-nearLength,latitude + nearLength)
            .between(TClvrtClvrtrecog::getLatitude,longitude-nearLength,longitude + nearLength);
        List<TClvrtClvrtrecog> bridgeList = clvrtrecogService.list(wrapper);
        return bridgeList.stream().limit(400).map(s -> {
            BaseStructDto dto = new BaseStructDto();
            dto.setStructId(s.getClvrtrecogId());
            dto.setStructName(s.getClvrtName());
            dto.setCntrStake(s.getLogicCntrStakeNum());
            dto.setX(s.getLongitude());
            dto.setY(s.getLatitude());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void setDmTaskAccptDetailView(DmTaskAccptDetailDto dto) {
        BaseStructDto item = H_BasedataHepler.getStructDto(dto.getStructId(), H_BasedataHepler.HD);
        if (item != null){
            dto.setLaneNm(item.getStructName());
        }
    }

    @Override
    public void initDmFinspRecordView(DmFinspRecord item) {
        item.setDssDegreeName(dssdegreeService.getDssdegressName(item.getDssType(),item.getDssDegree()));
    }

    @Override
    public List<DsstypeDssdegree> selectDssDegress(String dssType) {
        return dssdegreeService.selectDegressByDssType(dssType);
    }

    @Override
    public List<DmFinspResult> initDmFinspItems(List<DmFinspItem> items, DmFinsp dmFinsp) {
        boolean isNew = "20221101".equals(dmFinsp.getFinVersion())?true:false;
        List<DmFinspResult> dtos = Lists.newArrayList();
        items.forEach(item->{
            DmFinspResult dto = new DmFinspResult();
            dto.setFinspId(dmFinsp.getFinspId());
            dto.setFinspItemId(item.getFinspItemId());
            if (isNew){
                dto.setIssueDesc("正常");
                dto.setMntnAdvice("日常保养");
            }else {
                dto.setInspResult(item.getFinRemark());
                dto.setIssueDesc("");
            }
            dto.setRemark(item.getFinRemark());
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public String getFacilityCat() {
        return H_BasedataHepler.HD;
    }

    @Override
    public List<FacDssTypeDto> selectDssType(FacQueryDto queryDto) {
        if (StrUtil.isBlank(queryDto.getStructPartId())){
            List<FacDssTypeDto> dssTypeDtos = baseMapper.selectDssType();
            return dssTypeDtos;
        }
        if (StrUtil.isBlank(queryDto.getStructId())){
            throw new BaseException("涵洞ID不能为空");
        }
        String dbForm = baseMapper.selectStructForm(queryDto.getStructId());
        List<String> sForms = Lists.newArrayList();
        if (StrUtil.isNotBlank(dbForm)){
            String[] split = dbForm.split(",");
            sForms = Arrays.asList(split);
        }
        List<FacDssTypeDto> dssTypeDtos = baseMapper.selectDssTypeByPartId(queryDto.getStructPartId(), sForms);
        return dssTypeDtos;
    }

    @Override
    public List<FacPartTypeDto> selectPartTypes(FacQueryDto queryDto) {
        return baseMapper.selectPartType(queryDto.getStructId());
    }

    @Override
    public IPage<FacCompTypeDto> selectCompTypes(FacQueryDto queryDto) {
        Page<FacCompTypeDto> page = pageService.parsePageParam(request);
        List<FacCompTypeDto> records = Collections.emptyList();
        boolean needConvertOldPartId = "toOldPartId".equals(queryDto.getFinVersion());
        if (needConvertOldPartId){
            //根据structPartId查出旧版的partId
            LambdaQueryWrapper<NmInspContent> ew = new LambdaQueryWrapper<>();
            ew.eq(NmInspContent::getFacilityCat, H_BasedataHepler.HD);
            ew.eq(NmInspContent::getId, queryDto.getStructPartId());
            NmInspContent inspContent = contentService.getOne(ew, false);
            if (inspContent != null && StrUtil.isNotBlank(inspContent.getOldPartId())){
                String oldPartId = inspContent.getOldPartId();
                String[] partIds = oldPartId.split(",");
                records = baseMapper.selectCompTyesInPartCode(page, queryDto.getStructId(),
                    Lists.newArrayList(partIds));
            }
        } else {
            records = baseMapper.selectCompTyes(page, queryDto);
        }
        page.setRecords(records);
        return page;
    }

    @Override
    public FinspStatDto getFinspStat(List<String> routeCodes, String month) {

        //获取结构ID及养护等级
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,null);
        //根据养护等级分组
        Map<String, List<FinspStatFacDto>> gradeMap = finspStatFacDtos.stream().collect(Collectors.groupingBy(FinspStatFacDto::getGrade));
        List<CompletableFuture<List<FinspStatFacDto>>> futureList = new ArrayList<>();
        gradeMap.forEach((key, value) -> {
            //异步查询sql
            CompletableFuture<List<FinspStatFacDto>> future = CompletableFuture.supplyAsync(() -> {
                List<String> months = H_FacQueryHelper.getMonths(getFacilityCat(), key, month);
                List<FinspStatFacDto> dtos = baseMapper.selectFinspNumByGrade(routeCodes, key, months);
                return dtos;
            });
            futureList.add(future);
        });
        List<FinspStatFacDto> dtoList = H_FutureHelper.sequenceList(futureList).join();
        //已检查的结构物
        List<String> checkStructIds = dtoList.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
        //用户管养的所有结构物ID
        List<String> allStructIds = finspStatFacDtos.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());

        int total = finspStatFacDtos.size();
        allStructIds.removeAll(checkStructIds);
        int noCheckNum = allStructIds.size();
        String msg = getNoticeMsg(gradeMap,allStructIds,month);
        // checkNum = total - noCheckNum
        return new FinspStatDto().setFacilityCat(getFacilityCat()).setCheckNum(total-noCheckNum).setStructNum(total).setNoticeMsg(msg);
    }



    @Override
    public List<FinspStatFacDto> getGeometryStat(List<String> routeCodes, GeometryParamDto paramDto) {
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,paramDto);
        finspStatFacDtos.forEach(item->{
            item.setFacilityCat(getFacilityCat());
        });
        return finspStatFacDtos;
    }

    @Override
    public List<FacilityNumDto> countNum(Set<String> routeCodes) {
        return baseMapper.countNum(Lists.newArrayList(routeCodes));
    }

    private String getNoticeMsg(Map<String, List<FinspStatFacDto>> gradeMap,List<String> noCheckList,String month){
        List<String> noticeMsg = Lists.newArrayList();
        //判断未检查的桥梁各养护等级的个数
        gradeMap.forEach((key, value) -> {
            if (!H_FacQueryHelper.isNotice(getFacilityCat(),key,month)){
                return;
            }
            List<String> structList = value.stream().filter(e -> noCheckList.contains(e.getStructId())).collect(Collectors.toList()).stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
            if (structList != null && structList.size() > 0){
                String cnNum = NumberChineseFormatter.format(Long.valueOf(key), false);
                String format = StrUtil.format("涵洞(1次/季)：剩余{}座未检查",structList.size());
                noticeMsg.add(format);
            }
        });
        String msg = noticeMsg.stream().collect(Collectors.joining("；"));
        return msg;
    }
}
