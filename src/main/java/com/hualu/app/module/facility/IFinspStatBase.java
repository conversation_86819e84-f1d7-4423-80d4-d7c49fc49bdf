package com.hualu.app.module.facility;

import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.facility.dto.FacilityNumDto;
import com.hualu.app.module.facility.dto.stat.FinspStatDto;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;

import java.util.List;
import java.util.Set;

/**
 * 经常检查单统计
 */
public interface IFinspStatBase {

    /**
     * 根据路段编码，统计经常检查数据
     * @param routeCodes
     * @return
     */
    public FinspStatDto getFinspStat(List<String> routeCodes,String month);

    /**
     * 视野范围内结构物查询
     * @param routeCodes
     * @param paramDto
     * @return
     */
    List<FinspStatFacDto> getGeometryStat(List<String> routeCodes, GeometryParamDto paramDto);

    List<FacilityNumDto> countNum(Set<String> routeCodes);
}
