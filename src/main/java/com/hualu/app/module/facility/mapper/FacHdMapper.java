package com.hualu.app.module.facility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.TClvrtClvrtrecog;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface FacHdMapper extends BaseMapper<TClvrtClvrtrecog> {

    @Select("select distinct d.dss_type,d.DSS_TYPE_NAME,d.dss_order_number from memsdb.dss_type d inner join memsdb.common_hd_dsstype dt on d.dss_type = dt.dss_type order by d.dss_order_number desc")
    List<FacDssTypeDto> selectDssType();


    List<FacDssTypeDto> selectDssTypeByPartId(String structPartId, @Param("sForms") List<String> sForms);

    @Select("select cr.struct_form from bctcmsdb.t_clvrt_clvrtrecog cr where cr.clvrtrecog_id = #{structId}")
    String selectStructForm(@Param("structId") String structId);

    @Select("SELECT x.parts_code, y.part_name as partstype_name,x.parts_code as parts_Id FROM bctcmsdb.T_CLVRT_PARTSCONS x, bctcmsdb.T_CLVRT_PARTTYPE y WHERE x.parts_code = y.part_code AND x.CLVRTRECOG_ID = #{structId} AND x.valid_flag = 1 GROUP BY x.parts_code, y.part_name order by x.parts_code ")
    List<FacPartTypeDto> selectPartType(@Param("structId") String structId);

    List<FacCompTypeDto> selectCompTyes(IPage page, @Param("queryDto") FacQueryDto queryDto);

    List<FacCompTypeDto> selectCompTyesInPartCode(@Param("page") IPage page,
        @Param("structId") String structId, @Param("partIds") List<String> partIds);

    List<FinspStatFacDto> selectStruct(@Param("routeCodes") List<String> routeCodes, @Param("paramDto") GeometryParamDto paramDto);

    //结构物数量
    List<FacilityNumDto> countNum(@Param("routeCodes") List<String> routeCodes);

    List<FinspStatFacDto> selectFinspNumByGrade(@Param("routeCodes") List<String> routeCodes
            ,@Param("grade") String grade,@Param("months") List<String> months);}
