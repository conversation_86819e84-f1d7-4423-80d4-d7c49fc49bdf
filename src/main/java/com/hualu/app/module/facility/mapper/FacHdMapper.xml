<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.facility.mapper.FacHdMapper">

    <select id="selectDssTypeByPartId" resultType="com.hualu.app.module.facility.dto.FacDssTypeDto">
        WITH PAR AS (SELECT * FROM MEMSDB.BASE_STRUCT_COMP DI
        START WITH DI.STRUCT_COMP_ID = '200'
        CONNECT BY PRIOR DI.STRUCT_COMP_ID = DI.P_STRUCT_COMP_ID)
        SELECT max(T1.DSS_TYPE)       as dss_type,
               T1.DSS_TYPE_NAME       as dss_type_name,
               max(T1.STRUCT_COMP_ID) as P_STRUCT_COMP_ID
        FROM MEMSDB.DSS_TYPE T1
        WHERE T1.STRUCT_COMP_ID IN (SELECT P.STRUCT_COMP_ID
                                    FROM PAR P
                                    WHERE P.PART_CODE = #{structPartId}
                                      AND EXISTS(SELECT 1
                                                 FROM PAR X
                                                 WHERE X.PART_CODE IN
                                                <foreach collection="sForms" item="id" index="index" open="(" close=")" separator=",">
                                                    #{id}
                                                </foreach>
                                                   AND P.P_STRUCT_COMP_ID = X.STRUCT_COMP_ID))
        group by t1.dss_type_name
        ORDER BY max(T1.DSS_TYPE)
    </select>
    <select id="selectCompTyes" resultType="com.hualu.app.module.facility.dto.FacCompTypeDto">
        select distinct c.comp_code as code_id, c.comp_code as compattr_code, h.comp_code as brdgtype_id
        from bctcmsdb.T_CLVRT_LOGICCOMP c,
             bctcmsdb.T_CLVRT_LOGIC_PHY_COMP l,
             bctcmsdb.T_CLVRT_PHYCOMP h
        where c.parts_code = h.parts_code
          and h.parts_code = #{queryDto.structPartId}
          and l.logiccomp_id = c.LOGICCOMP_ID
          and l.phycomp_id = h.phycomp_id
          and c.clvrtrecog_id = h.clvrtrecog_id
          and h.clvrtrecog_id = #{queryDto.structId}
          and c.valid_flag = h.valid_flag
          and h.valid_flag = '1'
          order by substr(c.COMP_CODE,2,instr(c.COMP_CODE,'(')-2)
    </select>

  <select id="selectCompTyesInPartCode" resultType="com.hualu.app.module.facility.dto.FacCompTypeDto">
    select distinct c.comp_code as code_id, c.comp_code as compattr_code, h.comp_code as brdgtype_id
    from bctcmsdb.T_CLVRT_LOGICCOMP c,
    bctcmsdb.T_CLVRT_LOGIC_PHY_COMP l,
    bctcmsdb.T_CLVRT_PHYCOMP h
    where c.parts_code = h.parts_code
    and h.parts_code in
    <foreach collection="partIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and l.logiccomp_id = c.LOGICCOMP_ID
    and l.phycomp_id = h.phycomp_id
    and c.clvrtrecog_id = h.clvrtrecog_id
    and h.clvrtrecog_id = #{structId}
    and c.valid_flag = h.valid_flag
    and h.valid_flag = '1'
    order by substr(c.COMP_CODE,2,instr(c.COMP_CODE,'(')-2)
  </select>
    <select id="selectStruct" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        select CLVRTRECOG_ID as struct_id,'1' as grade,clvrt_name as struct_name,latitude as gis_y,longitude as gis_x from BCTCMSDB.T_CLVRT_CLVRTRECOG
            where VALID_FLAG = 1 and ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="paramDto!=null">
            and longitude >= #{paramDto.minX} and longitude &lt;= #{paramDto.maxX} and latitude >= #{paramDto.minY} and latitude &lt;= #{paramDto.maxY}
        </if>
    </select>
    <select id="selectFinspNumByGrade" resultType="com.hualu.app.module.facility.dto.stat.FinspStatFacDto">
        SELECT
            m.CLVRTRECOG_ID AS struct_id,
            '1' AS grade
        FROM
            BCTCMSDB.T_CLVRT_CLVRTRECOG m
                JOIN memsdb.DM_FINSP f ON m.CLVRTRECOG_ID = f.STRUCT_ID
                AND m.VALID_FLAG = 1
        WHERE
            f.FACILITY_CAT = 'HD'
          AND TO_CHAR( f.INSP_DATE, 'yyyy-MM' ) IN
        <foreach collection="months" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
          AND m.ROUTE_CODE IN
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        GROUP BY m.CLVRTRECOG_ID
    </select>

    <select id="countNum" resultType="com.hualu.app.module.facility.dto.FacilityNumDto">
        select count(1) as num,route_code,'HD' as facility_cat from BCTCMSDB.T_CLVRT_CLVRTRECOG
        where VALID_FLAG = 1 and ROUTE_CODE in
        <foreach collection="routeCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by route_code
    </select>
</mapper>