package com.hualu.app.module.facility.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FacBpMapper extends BaseMapper<HsmsSlopeInfo> {


    List<FacPartTypeDto> selectPartType(@Param("queryDto") FacQueryDto queryDto);

    List<FacDssTypeDto> selectDssType(@Param("structPartId") String structPartId);

    List<FacCompTypeDto> selectCompType(IPage page,@Param("structPartId") String structPartId,@Param("finVersion") String finVersion);

    //根据路段编码，查询桥梁的个数，及对应的养护等级
    List<FinspStatFacDto> selectStruct(@Param("routeCodes") List<String> routeCodes, @Param("isImportant") Boolean isImportant, @Param("paramDto")GeometryParamDto paramDto);

    //结构物数量
    List<FacilityNumDto> countNum(@Param("routeCodes") List<String> routeCodes);

    /**
     * 根据路段编码，查询已经巡检的桥梁结构物	(1级=1次/1月，2级=1次/2月)，判断是否包含1级和2级
     * 一年6次（【1月,2月】、【3月,4月】、【5月,6月】、根据该规则推算）
     */
    List<FinspStatFacDto> selectFinspNumByGrade(@Param("routeCodes") List<String> routeCodes
            ,@Param("grade") String grade,@Param("months") List<String> months,@Param("tableName") String tableName);

    /**
     * 根据病害类型前缀，查询病害列表
     * @param dssTypePrefix
     * @return
     */
    List<FacDssTypeDto> selectDssTypeByPrefix(@Param("dssTypePrefix") String dssTypePrefix);
}
