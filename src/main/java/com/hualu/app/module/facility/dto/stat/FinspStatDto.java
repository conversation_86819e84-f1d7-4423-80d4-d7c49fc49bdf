package com.hualu.app.module.facility.dto.stat;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class FinspStatDto implements Serializable {

    private static final long serialVersionUID = 1L;

    //机构名称
    private String orgName;

    //机构编码
    private String orgCode;

    //结构物类型
    private String facilityCat;

    //结构物数量
    private int structNum;

    //检查单已检数量
    private int checkNum;

    //业务提醒
    private String noticeMsg;
}
