package com.hualu.app.module.facility.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 统计各路段的结构物数量
 */
@Data
@Accessors(chain = true)
public class FacilityNumDto implements Serializable {

    private static final long serialVersionUID = 1L;
    //路段编号
    private String routeCode;
    //结构物数量
    private Integer num;
    //设施类型
    private String facilityCat;
}
