package com.hualu.app.module.facility.dto;

import lombok.experimental.Accessors;

@Accessors(chain = true)
public class BridgeTypeDto implements java.io.Serializable{
  private String bridgeTypeCode;
  private String bridgeTypeName;

  public String getBridgeTypeCode() {
    return bridgeTypeCode;
  }

  public void setBridgeTypeCode(String bridgeTypeCode) {
    this.bridgeTypeCode = bridgeTypeCode;
  }

  public String getBridgeTypeName() {
    return bridgeTypeName;
  }

  public void setBridgeTypeName(String bridgeTypeName) {
    this.bridgeTypeName = bridgeTypeName;
  }
}
