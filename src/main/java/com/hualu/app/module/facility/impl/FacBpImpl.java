package com.hualu.app.module.facility.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.dto.GeometryParamDto;
import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.hualu.app.module.basedata.dto.near.BaseNearStructStatDto;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.module.basedata.entity.MtmsTunnelBasic;
import com.hualu.app.module.basedata.service.impl.HsmsSlopeInfoServiceImpl;
import com.hualu.app.module.facility.IFacBase;
import com.hualu.app.module.facility.IFinspStatBase;
import com.hualu.app.module.facility.dto.*;
import com.hualu.app.module.facility.dto.stat.FinspStatDto;
import com.hualu.app.module.facility.dto.stat.FinspStatFacDto;
import com.hualu.app.module.facility.mapper.FacBpMapper;
import com.hualu.app.module.facility.utils.H_FacQueryHelper;
import com.hualu.app.module.mems.dss.entity.DsstypeDssdegree;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.finsp.service.DmFinspResultService;
import com.hualu.app.module.mems.finsp.service.DmFinspService;
import com.hualu.app.module.mems.finsp.service.SlopeRegularSourceTrackService;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.platform.entity.BaseDatathirdDic;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.module.workflow.service.WfworkitemService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_FutureHelper;
import com.hualu.app.utils.H_RestResultHelper;
import com.hualu.app.utils.mems.StringUtil;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.mybatisplus.service.PageService;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class FacBpImpl extends ServiceImpl<FacBpMapper, HsmsSlopeInfo> implements IFacBase, IFinspStatBase {

    @Autowired
    PageService pageService;

    @Autowired
    HttpServletRequest request;

    @Lazy
    @Autowired
    DmFinspService finspService;

    @Lazy
    @Autowired
    DmFinspResultService resultService;

    @Autowired
    BaseDatathirdDicService dicService;

    @Autowired
    HsmsSlopeInfoServiceImpl hsmsSlopeInfoService;

    @Autowired
    WfworkitemService wfworkitemService;

    @Lazy
    @Autowired
    SlopeRegularSourceTrackService trackService;

    @Override
    public List<BaseStructDto> getNearBaseStruct(List<String> routeCodes, String facilityCat, Double lat, Double lng) {
        LambdaQueryWrapper<HsmsSlopeInfo> wrapper = new LambdaQueryWrapper<>();
        double nearLength = 0.00005;
        double latitude = ObjectUtil.defaultIfNull(lat, 0d);
        double longitude = ObjectUtil.defaultIfNull(lng, 0d);
        wrapper.in(HsmsSlopeInfo::getRouteCode, routeCodes);
        wrapper.eq(HsmsSlopeInfo::getIsDeleted, "0");
        wrapper.between(HsmsSlopeInfo::getGisX,latitude-nearLength,latitude + nearLength)
            .between(HsmsSlopeInfo::getGisY,longitude-nearLength,longitude + nearLength);
        List<HsmsSlopeInfo> bridgeList = hsmsSlopeInfoService.list(wrapper);
        return bridgeList.stream().limit(400).map(s -> {
            BaseStructDto dto = new BaseStructDto();
            dto.setStructId(s.getSlopeId());
            dto.setStructName(s.getSlopeName());
            dto.setCntrStake(s.getLogicCntrStake());
            dto.setX(s.getGisX());
            dto.setY(s.getGisY());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 查询附近边坡（结构物排查）
     * @param dto
     * @return
     */
    public RestResult<Map<String,Object>> getNearByPcFinsp(BaseNearStructDto dto){
        Page page = pageService.parsePageParam(request);
        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        LambdaQueryWrapper<HsmsSlopeInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HsmsSlopeInfo::getRouteCode, routeCodes).eq(HsmsSlopeInfo::getIsDeleted,0);

        BaseNearStructStatDto statDto = getCheckStructIdsByFinsp(Lists.newArrayList(routeCodes),"PC_FINSP");

        //附近1公里
        double nearLength = 0.00005;
        if (dto.getIsNear().equals(1)){
            queryWrapper.between(HsmsSlopeInfo::getGisX,dto.getX()-nearLength,dto.getX() + nearLength)
                    .between(HsmsSlopeInfo::getGisY,dto.getY()-nearLength,dto.getY() + nearLength);
        }
        //模糊查询结构物名称
        if (StrUtil.isNotBlank(dto.getStructName())){
            String likeName = StrUtil.replace(dto.getStructName()," ","%");
            queryWrapper.like(HsmsSlopeInfo::getSlopeName,likeName);
        }

        // 已检查单
        if (dto.getChecked().equals(1)){
            queryWrapper.exists(getSqlByFinsp("PC_FINSP","hsms_slope_info"));
        }
        //未检查
        if (dto.getChecked().equals(0)){
            queryWrapper.notExists(getSqlByFinsp("PC_FINSP","hsms_slope_info"));
        }

        //边坡检查统计数量
        Map<String,Object> checkedMap = new HashMap<>();
        checkedMap.put("checkNum",statDto.getCheckedNum());
        checkedMap.put("noCheckedNum",statDto.getNotCheckedNum());

        //边坡集合
        IPage ipage = hsmsSlopeInfoService.page(page,queryWrapper);
        List<BaseNearStructDto> structDtos = toNearStructDto(page.getRecords());
        ipage.setRecords(structDtos);
        structDtos.forEach(item->{
            if (statDto.getStructIds().contains(item.getStructId())){
                item.setChecked(1);
            }
        });

        if (CollectionUtil.isEmpty(structDtos)){
            RestResult<List<BaseNearStructDto>> result = H_RestResultHelper.returnPage(ipage);
            checkedMap.put("data", result.getData());
            return RestResult.success(checkedMap,result.getTotal(),result.getPage(),result.getPageSize());

        }
        RestResult<List<BaseNearStructDto>> result = H_RestResultHelper.returnPage(ipage);
        checkedMap.put("data", result.getData());
        return RestResult.success(checkedMap,result.getTotal(),result.getPage(),result.getPageSize());
    }

    /**
     * 查询附近边坡（经常检查单）
     * @param dto
     * @return
     */
    public RestResult<Map<String,Object>> getNearByDmFinsp(BaseNearStructDto dto){
        Page page = pageService.parsePageParam(request);
        Set<String> routeCodes = H_DataAuthHelper.selectRouteCodeAuth();
        LambdaQueryWrapper<HsmsSlopeInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HsmsSlopeInfo::getRouteCode, routeCodes).eq(HsmsSlopeInfo::getIsDeleted,0);

        BaseNearStructStatDto statDto = getCheckStructIdsByFinsp(Lists.newArrayList(routeCodes),"DM_FINSP");

        //附近1公里
        double nearLength = 0.00005;
        if (dto.getIsNear().equals(1)){
            queryWrapper.between(HsmsSlopeInfo::getGisX,dto.getX()-nearLength,dto.getX() + nearLength)
                    .between(HsmsSlopeInfo::getGisY,dto.getY()-nearLength,dto.getY() + nearLength);
        }
        //模糊查询结构物名称
        if (StrUtil.isNotBlank(dto.getStructName())){
            String likeName = StrUtil.replace(dto.getStructName()," ","%");
            queryWrapper.like(HsmsSlopeInfo::getSlopeName,likeName);
        }

        // 已检查单
        if (dto.getChecked().equals(1)){
            queryWrapper.exists(getSqlByFinsp("DM_FINSP","hsms_slope_info"));
        }
        //未检查
        if (dto.getChecked().equals(0)){
            queryWrapper.notExists(getSqlByFinsp("DM_FINSP","hsms_slope_info"));
        }

        //边坡检查统计数量
        Map<String,Object> checkedMap = new HashMap<>();
        checkedMap.put("checkNum",statDto.getCheckedNum());
        checkedMap.put("noCheckedNum",statDto.getNotCheckedNum());

        //边坡集合
        IPage ipage = hsmsSlopeInfoService.page(page,queryWrapper);
        List<BaseNearStructDto> structDtos = toNearStructDto(page.getRecords());
        ipage.setRecords(structDtos);
        if (CollectionUtil.isEmpty(structDtos)){
            RestResult<List<BaseNearStructDto>> result = H_RestResultHelper.returnPage(ipage);
            checkedMap.put("data", result.getData());
            return RestResult.success(checkedMap,result.getTotal(),result.getPage(),result.getPageSize());

        }
        //检查是否有原始轨迹
        List<String> slopeIds = structDtos.stream().map(BaseNearStructDto::getStructId).collect(Collectors.toList());
        Set<String> dbSlopeIds = trackService.hasSourceTack(slopeIds);

        structDtos.forEach(item->{
            if (statDto.getStructIds().contains(item.getStructId())){
                item.setChecked(1);
            }
            if (dbSlopeIds.contains(item.getStructId())){
                item.setHasSourceTrack(1);
            }
        });
        RestResult<List<BaseNearStructDto>> result = H_RestResultHelper.returnPage(ipage);
        checkedMap.put("data", result.getData());
        return RestResult.success(checkedMap,result.getTotal(),result.getPage(),result.getPageSize());
    }

    /**
     * 拼接sql语句
     * @param tableName  DM_FINSP,PC_FINSP
     * @param alias
     * @return
     */
    private String getSqlByFinsp(String tableName,String alias){
        //按照半年一次
        //String month = DateUtil.format(new Date(), "yyyy-MM");
        //List<String> months = H_FacQueryHelper.getMonths(getFacilityCat(), "1", month);
        // 按照季度统计
        List<String> months = H_FacQueryHelper.rangeList(new Date());
        String sql = "select 1 from memsdb.{} d where d.FACILITY_CAT = 'BP' and d.STRUCT_ID = {}.SLOPE_ID  AND TO_CHAR( d.INSP_DATE, 'yyyy-MM' ) IN ({})";
        String monthStr = StringUtil.arry2Str(months.toArray(new String[0]));
        return StrUtil.format(sql,tableName,alias,monthStr);
    }


    /**
     * 根据当前日期，查询已检查的边坡
     * @param routeCodes
     * @param tableName 表名称
     * @return
     */
    private BaseNearStructStatDto getCheckStructIdsByFinsp(List<String> routeCodes,String tableName) {
        //String month = DateUtil.format(new Date(), "yyyy-MM");
        //获取结构ID及养护等级
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(Lists.newArrayList(routeCodes),false,null);
        //根据养护等级分组
        Map<String, List<FinspStatFacDto>> gradeMap = finspStatFacDtos.stream().collect(Collectors.groupingBy(FinspStatFacDto::getGrade));
        List<CompletableFuture<List<FinspStatFacDto>>> futureList = new ArrayList<>();
        gradeMap.forEach((key, value) -> {
            //异步查询sql
            CompletableFuture<List<FinspStatFacDto>> future = CompletableFuture.supplyAsync(() -> {
                //List<String> months = H_FacQueryHelper.getMonths(getFacilityCat(), key, month);
                List<String> months = H_FacQueryHelper.rangeList(new Date());
                List<FinspStatFacDto> dtos = baseMapper.selectFinspNumByGrade(routeCodes, key, months,tableName);
                return dtos;
            });
            futureList.add(future);
        });
        List<FinspStatFacDto> dtoList = H_FutureHelper.sequenceList(futureList).join();
        //已检查的结构物
        List<String> checkStructIds = dtoList.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());

        //用户管养的所有结构物ID
        List<String> allStructIds = finspStatFacDtos.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());

        allStructIds.removeAll(checkStructIds);
        int noCheckNum = allStructIds.size();

        BaseNearStructStatDto statDto = new BaseNearStructStatDto();
        statDto.setStructIds(checkStructIds).setCheckedNum(checkStructIds.size()).setNotCheckedNum(noCheckNum);
        return statDto;
    }

    /**
     * 转换对象
     * @param slopeInfoList
     * @return
     */
    private List<BaseNearStructDto> toNearStructDto(List<HsmsSlopeInfo> slopeInfoList){
        List<BaseNearStructDto> structDtos = Lists.newArrayList();
        for (HsmsSlopeInfo item : slopeInfoList) {
            BaseNearStructDto dto = new BaseNearStructDto();
            dto.setLineId(item.getLineId());
            dto.setStructId(item.getSlopeId());
            dto.setStructCode(item.getSlopeCode());
            dto.setRlCntrStake(item.getLogicCntrStake());
            //todo 边坡方向需要显示成中文
            dto.setStructName(item.getSlopeName()+"("+dto.getDisplayStake()+")");
            dto.setRpIntrvlId(item.getRpIntrvlId());
            dto.setStructLength(item.getSlopeLength());
            dto.setCntrStake(item.getLogicCntrStake());
            dto.setLogicStartStake(item.getLogicStartStake());
            dto.setLogicEndStake(item.getLogicEndStake());
            dto.setRouteCode(item.getRouteCode());
            dto.setRouteVersion(item.getRouteVersion());
            dto.setSlopeLevel(item.getSlopeLevel());
            dto.setSlopePosition(item.getSlopePosition());
            dto.setSlopePositionName(dicService.getDicName(H_BasedataHepler.DIC_BP_POSITION,dto.getSlopePosition()));
            dto.setSlopeType(item.getSlopeType());
            dto.setSlopeTypeName(dicService.getDicName(H_BasedataHepler.DIC_BP_TYPE,dto.getSlopeType()));
            dto.setX(item.getGisX());
            dto.setY(item.getGisY());
            dto.setOprtOrgCode(item.getOptOrgId());
            structDtos.add(dto);
        }
        return structDtos;
    }

    @Override
    public void setDmTaskAccptDetailView(DmTaskAccptDetailDto dto) {
        BaseStructDto item = H_BasedataHepler.getStructDto(dto.getStructId(), H_BasedataHepler.BP);
        if (item != null){
            dto.setLaneNm(item.getStructName());
        }
    }

    @SneakyThrows
    @Override
    public String exportExcel(DmFinsp finsp, List<DmFinspRecord> records, List<DmFinspResult> results, String fileReposity,Integer signType) {
        BaseStructDto structDto = hsmsSlopeInfoService.getId(finsp.getStructId());
        Map<String, Object> dmFinspMap = BeanUtil.beanToMap(finsp);
        String inspDate = DateUtil.formatDate(finsp.getInspDate());
        String cnLevel = NumberChineseFormatter.format(structDto.getSlopeLevel(), false, false)+"级";
        dmFinspMap.put("slopeLevel",cnLevel);
        dmFinspMap.put("slopePositionName",structDto.getSlopePositionName());
        dmFinspMap.put("slopeTypeName",structDto.getSlopeTypeName());
        dmFinspMap.put("inspDate",inspDate);
        initDic(finsp,dmFinspMap);
        //复核人
        String approvalPerson = "";
        if (finsp.getStatus() == 3){
            approvalPerson = wfworkitemService.selectApprovalPerson(finsp.getProcessinstid());
        }
        dmFinspMap.put("approvalPerson",approvalPerson);
        initRes(finsp.getFinVersion(),results);
        dmFinspMap.put("resList",results);

        Map<String,Object> recordMap = Maps.newHashMap();
        recordMap.put("recList", records);
        if(signType != null){
            dmFinspMap.put("approvalPerson","    fz1         fz2");
            dmFinspMap.put("inspPerson","jl");
        }
        Map<Integer, Map<String,Object>> dataParams = Maps.newHashMap();
        dataParams.put(0,dmFinspMap);
        dataParams.put(1,recordMap);
        boolean isRisk = false;
        Integer projectType = finsp.getProjectType();
        if(2 == projectType){
            isRisk = true;
        }
        String excelTemplatPath = excelTemplatPath(finsp.getFinVersion(),isRisk);
        TemplateExportParams exportParams = new TemplateExportParams(excelTemplatPath,true,null);
        Workbook workbook = ExcelExportUtil.exportExcel(dataParams, exportParams);
        String excelPath = fileReposity+"\\"+finsp.getFinspCode()+".xlsx";
        workbook.write(new FileOutputStream(excelPath));
        workbook.close();
        return excelPath;
    }

    private String excelTemplatPath(String finVersion,boolean isRisk){
        if(isRisk){
            if (H_BasedataHepler.BPJC_VERSION.equals(finVersion)){
                return "excel/bpRisk202305.xlsx";
            }
            return "excel/bpRisk201801.xlsx";
        }else{
            if (H_BasedataHepler.BPJC_VERSION.equals(finVersion)){
                return "excel/bp202305.xlsx";
            }
            return "excel/bp201801.xlsx";
        }
    }

    /**
     * 初始化导出显示结果
     * @param results
     */
    private void initRes(String finVersion,List<DmFinspResult> results){
        results.forEach(item->{

            if (H_BasedataHepler.BPJC_VERSION.equals(finVersion)){
                item.setInspResult("日常养护");
                return;
            }

            if ("1".equals(item.getInspResult())){
                item.setResYes("√");
            }else {
                item.setResNo("√");
            }
        });
    }

    /**
     * 初始化字典
     * @param dmFinsp
     */
    private void initDic(DmFinsp dmFinsp,Map paramMap){
        paramMap.put("weatherName",dicService.getDicName(H_BasedataHepler.DIC_WEATHER,dmFinsp.getWeather()));
        paramMap.put("positionName",dicService.getDicName(H_BasedataHepler.DIC_BP_POSITION,dmFinsp.getSlopePosition()));
        paramMap.put("slopeFxdjName",dicService.getDicName(H_BasedataHepler.DIC_BP_FXDJ,dmFinsp.getSlopeFxdj()));
        paramMap.put("slopeZhlxName",dicService.getDicName(H_BasedataHepler.DIC_BP_ZHTYPE,dmFinsp.getSlopeZhlx()));
    }

    @Override
    public void setFinspItemId(DmFinspRecord record, DmFinsp dmFinsp) {

        if (!H_BasedataHepler.BPJC_VERSION.equals(dmFinsp.getFinVersion())){
            return;
        }
        List<DmFinspResult> results = resultService.selectResultByFinspId(dmFinsp.getFinspId());
        if (StrUtil.isBlank(record.getStructPartName())){
            return;
        }
        //匹配部件内容
        results = results.stream().filter(e -> e.getInspCom().contains(record.getStructPartName())).collect(Collectors.toList());
        if (StrUtil.isNotBlank(record.getStructCompName())){
            //匹配构件内容
            results = results.stream().filter(e->e.getInspCont().contains(record.getStructCompName())).collect(Collectors.toList());
        }

        DmFinspResult result = results.stream().filter(e -> e.getInspCont().contains(record.getDssTypeName())).findFirst().orElse(null);
        if (result != null){
            record.setFinspItemId(result.getFinspResId());
        }
    }

    @Override
    public void initDmFinspRecordView(DmFinspRecord item) {

        String finVersion = null;
        DmFinsp dmFinsp = finspService.getById(item.getFinspId());
        if (dmFinsp != null){
            finVersion = dmFinsp.getFinVersion();
        }
        //设置边坡级数
        item.setLaneName(H_BasedataHepler.bpLevel.get(item.getLane()));
        item.setDssDegreeName(dicService.getDicName("DSS_DEGREE",item.getDssDegree()));
        //设置构件名称
        item.setStructCompName(getCompName(item.getStructPartId(),item.getStructCompId(),finVersion));
    }

    @Override
    public List<DsstypeDssdegree> selectDssDegress(String dssType) {
        List<DsstypeDssdegree> results = Lists.newArrayList();
        List<BaseDatathirdDic> datathirdDics = dicService.listDicByItem("DSS_DEGREE");
        datathirdDics.forEach(item->{
            DsstypeDssdegree row = new DsstypeDssdegree();
            row.setDssDegree(item.getAttributeCode());
            row.setDssDegreeName(item.getAttributeValue());
            results.add(row);
        });
        return results;
    }

    @Override
    public List<DmFinspResult> initDmFinspItems(List<DmFinspItem> items, DmFinsp dmFinsp) {
        List<DmFinspResult> dtos = Lists.newArrayList();
        String inspRes = H_BasedataHepler.BPJC_VERSION.equals(dmFinsp.getFinVersion())?"":"0";
        items.forEach(item->{
            DmFinspResult dto = new DmFinspResult();
            dto.setFinspId(dmFinsp.getFinspId());
            dto.setFinspItemId(item.getFinspItemId());
            dto.setIssueDesc("未发现病害");
            dto.setInspResult(inspRes);
            dto.setRemark("");
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public String getFacilityCat() {
        return H_BasedataHepler.BP;
    }

    @Override
    public List<FacDssTypeDto> selectDssType(FacQueryDto queryDto) {
        List<FacDssTypeDto> typeDtos = baseMapper.selectDssType(queryDto.getStructPartId());
        if (CollectionUtil.isEmpty(typeDtos) && StrUtil.isNotBlank(queryDto.getStructCompId())){
            typeDtos = baseMapper.selectDssType(queryDto.getStructCompId());
        }
        if (StrUtil.isNotBlank(queryDto.getDssTypePrefix())){
            typeDtos = baseMapper.selectDssTypeByPrefix(queryDto.getDssTypePrefix());
        }
        return typeDtos;
    }

    @Override
    public List<FacPartTypeDto> selectPartTypes(FacQueryDto queryDto) {
        return baseMapper.selectPartType(queryDto);
    }

    @Override
    public IPage<FacCompTypeDto> selectCompTypes(FacQueryDto queryDto) {
        Page<FacCompTypeDto> page = pageService.parsePageParam(request);
        List<FacCompTypeDto> typeDtos = baseMapper.selectCompType(page, queryDto.getStructPartId(), queryDto.getFinVersion());
        page.setRecords(typeDtos);
        return page;
    }

    /**
     * 构件显示名称
     * @param structPartId
     * @param compId
     * @return
     */
    private String getCompName(String structPartId,String compId,String finVersion){
        if (StrUtil.isBlank(structPartId)){
            return null;
        }
        String key = "bp:comp:"+structPartId;
        List<FacCompTypeDto> typeDtos = (List<FacCompTypeDto>) CustomRequestContextHolder.get(key);
        if (CollectionUtil.isEmpty(typeDtos)){
            Page<FacCompTypeDto> page = new Page<>();
            page.setCurrent(0);
            page.setSize(200);
            typeDtos = baseMapper.selectCompType(page, structPartId,finVersion);
            CustomRequestContextHolder.set(key,typeDtos);
        }
        if (CollectionUtil.isEmpty(typeDtos)){
            return null;
        }
        return typeDtos.stream().filter(e->e.getCodeId().equals(compId)).map(FacCompTypeDto::getCompattrCode).findFirst().orElse(null);
    }

    public static void main(String[] args) {
        String cnLevel = NumberChineseFormatter.format(1, false, false)+"级";
        System.out.println(cnLevel);
    }

    @Override
    public FinspStatDto getFinspStat(List<String> routeCodes, String month) {

        Boolean isImportant = CustomRequestContextHolder.get("dmfinsp:importantSlope")==null?false:Boolean.valueOf(CustomRequestContextHolder.get("dmfinsp:importantSlope").toString());
        //获取结构ID及养护等级
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,isImportant,null);
        //根据养护等级分组
        Map<String, List<FinspStatFacDto>> gradeMap = finspStatFacDtos.stream().collect(Collectors.groupingBy(FinspStatFacDto::getGrade));
        List<CompletableFuture<List<FinspStatFacDto>>> futureList = new ArrayList<>();
        gradeMap.forEach((key, value) -> {
            //异步查询sql
            CompletableFuture<List<FinspStatFacDto>> future = CompletableFuture.supplyAsync(() -> {
                List<String> months = H_FacQueryHelper.getMonths(getFacilityCat(), key, month);
                List<FinspStatFacDto> dtos = baseMapper.selectFinspNumByGrade(routeCodes, key, months,"DM_FINSP");
                return dtos;
            });
            futureList.add(future);
        });
        List<FinspStatFacDto> dtoList = H_FutureHelper.sequenceList(futureList).join();
        //已检查的结构物
        List<String> checkStructIds = dtoList.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
        //用户管养的所有结构物ID
        List<String> allStructIds = finspStatFacDtos.stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());

        int total = finspStatFacDtos.size();
        allStructIds.removeAll(checkStructIds);
        int noCheckNum = allStructIds.size();
        String msg = getNoticeMsg(gradeMap,allStructIds,month);
        // checkNum = total - noCheckNum
        return new FinspStatDto().setFacilityCat(getFacilityCat()).setCheckNum(total-noCheckNum).setStructNum(total).setNoticeMsg(msg);
    }


    @Override
    public List<FinspStatFacDto> getGeometryStat(List<String> routeCodes, GeometryParamDto paramDto) {
        Boolean isImportant = CustomRequestContextHolder.get("dmfinsp:importantSlope")==null?false:Boolean.valueOf(CustomRequestContextHolder.get("dmfinsp:importantSlope").toString());
        List<FinspStatFacDto> finspStatFacDtos = baseMapper.selectStruct(routeCodes,isImportant,paramDto);
        finspStatFacDtos.forEach(item->{
            item.setFacilityCat(getFacilityCat());
        });
        return finspStatFacDtos;
    }

    @Override
    public List<FacilityNumDto> countNum(Set<String> routeCodes) {
        return baseMapper.countNum(Lists.newArrayList(routeCodes));
    }

    private String getNoticeMsg(Map<String, List<FinspStatFacDto>> gradeMap,List<String> noCheckList,String month){
        List<String> noticeMsg = Lists.newArrayList();
        //判断未检查的桥梁各养护等级的个数
        gradeMap.forEach((key, value) -> {
            if (!H_FacQueryHelper.isNotice(getFacilityCat(),key,month)){
                return;
            }
            List<String> structList = value.stream().filter(e -> noCheckList.contains(e.getStructId())).collect(Collectors.toList()).stream().map(FinspStatFacDto::getStructId).collect(Collectors.toList());
            if (structList != null && structList.size() > 0){
                //String cnNum = NumberChineseFormatter.format(Long.valueOf(key), false);
                String format = StrUtil.format("边坡(1次/6月)：剩余{}处未检查",structList.size());
                noticeMsg.add(format);
            }
        });
        String msg = noticeMsg.stream().collect(Collectors.joining("；"));
        return msg;
    }
}
