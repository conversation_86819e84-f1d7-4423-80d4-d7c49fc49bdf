package com.hualu.app.module.autoRoad.entiity;

import lombok.Data;
import java.util.Date;

@Data
public class InspectionWork {
    // 管养单位
    private String managementUnit;

    // 路段
    private String roadSection;

    // 巡检路线
    private String inspectionRoute;

    // 起止桩号
    private String startEndStakeNumber;

    // 路段里程（km）
    private double sectionMileage;

    // 巡检类型
    private String inspectionType;

    // 巡检里程（km）
    private double inspectionMileage;

    // 巡检单位
    private String inspectionUnit;

    // 巡检人员
    private String inspectionPersonnel;

    // 巡检设备
    private String inspectionEquipment;

    // 巡检日期
    private Date inspectionDate;
}
