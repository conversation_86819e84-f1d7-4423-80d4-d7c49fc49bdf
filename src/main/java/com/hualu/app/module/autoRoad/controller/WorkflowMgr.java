package com.hualu.app.module.autoRoad.controller;

import afu.org.checkerframework.checker.oigj.qual.O;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.autoRoad.entiity.InspectionWork;
import com.hualu.app.module.autoRoad.vo.LineBarEChartsVo;
import com.hualu.app.module.autoRoad.vo.PieVo;
import com.hualu.app.module.autoRoad.vo.WorkflowMgrEChartsVo;
import com.hualu.app.module.mems.comm.entity.AppUpdateVersion;
import com.hualu.app.utils.H_PageHelper;
import com.tg.dev.mybatisplus.service.PageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 工作管理
 */
@Api(tags = "工作管理")
@RestController
@RequestMapping("/WorkflowMgr")
public class WorkflowMgr {

    @Resource
    PageService pageService;


    @ApiOperation("巡查工作基本信息")
    @PostMapping("/getBasicInfo")
    public Object getBasicInfo(String orgCode) {
        IPage<InspectionWork> page = new Page<>(1, 10);

        // 设置总记录数
        page.setTotal(100L);

        // 设置记录列表
        page.setRecords(generateTestData(100));

        return H_PageHelper.getPageResult(this.pageService.returnPageResult(page));
    }

    public static List<InspectionWork> generateTestData(int count) {
        List<InspectionWork> dataList = new ArrayList<>();
        Random random = new Random();

        // 基础数据池
        String[] managementUnits = {
                "京珠北", "广乐北"
        };

        String[][] roadSections = {
                {"G0423", "乐昌-广州高速公路(G0423)"},
                {"S84", "韶关环城高速环线(S84)"},
                {"G4", "G4京港澳高速"}
        };

        String[] inspectionTypes = {"日常巡检", "经常检查"};
        String[] suffixes = {"京珠北","广乐北"};

        for (int i = 0; i < count; i++) {
            InspectionWork work = new InspectionWork();

            // 随机选择基础数据
            int unitIndex = random.nextInt(managementUnits.length);
            int sectionIndex = random.nextInt(roadSections.length);

            // 设置固定字段
            work.setManagementUnit(managementUnits[unitIndex]);
            work.setRoadSection(roadSections[sectionIndex][0]);
            work.setInspectionRoute(roadSections[sectionIndex][1]);

            // 生成随机桩号
            int start = 500 + random.nextInt(9500);
            int end = start + 500 + random.nextInt(10000);
            work.setStartEndStakeNumber(String.format("K%d+%03d-K%d+%03d",
                    random.nextInt(50), start, random.nextInt(50), end));

            // 随机里程（8.0-30.0之间）
            double mileage = 8.0 + random.nextDouble() * 22.0;
            mileage = Math.round(mileage * 10) / 10.0;
            work.setSectionMileage(mileage);
            work.setInspectionMileage(mileage - random.nextDouble() * 2.0);

            // 随机类型和单位
            work.setInspectionType(inspectionTypes[random.nextInt(inspectionTypes.length)]);
            work.setInspectionUnit(managementUnits[unitIndex].replace("管理", "")
                    .replace("委员会", "").replace("集团", "") + suffixes[random.nextInt(suffixes.length)]);

            // 随机人员（2-3人）
            String[] surnames = {"张", "王", "李", "赵", "刘", "陈", "杨", "周"};
            int peopleCount = 2 + random.nextInt(2);
            StringBuilder personnel = new StringBuilder();
            for (int j = 0; j < peopleCount; j++) {
                if (j > 0) personnel.append(",");
                personnel.append(surnames[random.nextInt(surnames.length)])
                        .append(random.nextBoolean() ? "三" : "四");
            }
            work.setInspectionPersonnel(personnel.toString());

            // 随机设备
            String[] cities = {"粤"};
            String[] devices = {"巡检车"};
            work.setInspectionEquipment(String.format("%s-%s%c%05d,%s-DJI%03d",
                    devices[random.nextInt(devices.length)],
                    cities[random.nextInt(cities.length)],
                    (char)('A' + random.nextInt(26)),
                    random.nextInt(100000),
                    devices[random.nextInt(devices.length)],
                    random.nextInt(1000)));

            // 随机日期（最近30天内）
            long randomDay = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(random.nextInt(30));
            work.setInspectionDate(new Date(randomDay));

            dataList.add(work);
        }

        return dataList;
    }

    @ApiOperation("巡查情况图")
    @PostMapping("/getEChartInfo")
    public Object getEChartInfo(String orgCode) {

        WorkflowMgrEChartsVo vo = new WorkflowMgrEChartsVo();

        // 生成 numberChart 数据 (假设有5个分类)
        LineBarEChartsVo numberChart = new LineBarEChartsVo();
        numberChart.setCategories(Arrays.asList("1月", "2月", "3月", "4月", "5月"));
        numberChart.setCompleted(Arrays.asList(120.0, 132.0, 101.0, 134.0, 90.0));
        numberChart.setUncompleted(Arrays.asList(20.0, 32.0, 11.0, 34.0, 10.0));
        numberChart.setPercent(Arrays.asList(85.7, 80.5, 90.2, 79.8, 90.0));
        vo.setNumberChart(numberChart);

        // 生成 kmChart 数据 (与numberChart相同的分类)
        LineBarEChartsVo kmChart = new LineBarEChartsVo();
        kmChart.setCategories(numberChart.getCategories()); // 使用相同的分类
        kmChart.setCompleted(Arrays.asList(450.0, 520.0, 380.0, 600.0, 350.0));
        kmChart.setUncompleted(Arrays.asList(50.0, 80.0, 20.0, 100.0, 50.0));
        kmChart.setPercent(Arrays.asList(90.0, 86.7, 95.0, 85.7, 87.5));
        vo.setKmChart(kmChart);

        // 生成 pieList 数据
        List<PieVo> pieList = Arrays.asList(
                new PieVo("已完成", 850),
                new PieVo("未完成", 150)
        );
        vo.setPieList(pieList);

        return vo;
    }


}
