package com.hualu.app.module.minio;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

public interface MinioService {

    public String upload(String bucketName,String fileClass,String fileName, String ObjectName, InputStream inputStream, long size, Map<String, String> headerMap, String contentType);

    public String upload(String bucketName,String fileClass, String ObjectName, InputStream inputStream, long size, Map<String, String> headerMap, String contentType);

    public String upload(String bucketName,String fileClass,String ObjectName,File file);

    public String upload(String bucketName,String fileClass, File file);

    public void download(HttpServletResponse response,String bucketName, String objectName);

    /**
     * 下载缩略图
     * @param response
     * @param bucketName
     * @param objectName
     * @param width
     * @param height
     */
    public void downloadThuPic(HttpServletResponse response,String bucketName,String objectName,int width,int height);

    public void download(OutputStream outputStream,String bucketName, String objectName);

    public void delete(String bucketName,String objectName);

    public String getObjectUrl(String bucketName,String ObjectName);
}
