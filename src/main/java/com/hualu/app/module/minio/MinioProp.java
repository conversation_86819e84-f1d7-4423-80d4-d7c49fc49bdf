package com.hualu.app.module.minio;

import org.assertj.core.util.Lists;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@ConfigurationProperties(prefix = "platform.minio")
@Component
public class MinioProp {

    private String endpoint;//连接url
    private String accesskey;//用户名
    private String secretKey;//密码
    private String bucketName;//桶名称
    private String isBucketNameMatch;

    private String outerHost;//对外ip
    private Integer outerPort;//对外端口

    private String fileMoveDir;//目前迁移地址

    // 后台预览地址
    private String imagePreviewPrefixUrl;

    // 文件上传白名单
    private List<String> whiteList = Lists.newArrayList();

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccesskey() {
        return accesskey;
    }

    public void setAccesskey(String accesskey) {
        this.accesskey = accesskey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public Boolean getIsBucketNameMatch() {
        return "true".equals(this.isBucketNameMatch);
    }

    public void setIsBucketNameMatch(String isBucketNameMatch) {
        this.isBucketNameMatch = isBucketNameMatch;
    }

    public String getOuterHost() {
        return outerHost;
    }

    public void setOuterHost(String outerHost) {
        this.outerHost = outerHost;
    }

    public Integer getOuterPort() {
        return outerPort;
    }

    public void setOuterPort(Integer outerPort) {
        this.outerPort = outerPort;
    }

    public Boolean isMatch(String bucketName){
        ArrayList<String> strings = Lists.newArrayList(bucketName.split(","));
        return strings.contains(bucketName);
    }

    /**
     *  获取默认桶名
     * @return
     */
    public String getDefaultBucketName(){
        String[] split = bucketName.split(",");
        if (split.length != 0){
            return split[0];
        }
        return null;
    }

    public String getFileMoveDir() {
        return fileMoveDir;
    }

    public void setFileMoveDir(String fileMoveDir) {
        this.fileMoveDir = fileMoveDir;
    }

    public String getImagePreviewPrefixUrl() {
        return imagePreviewPrefixUrl;
    }

    public void setImagePreviewPrefixUrl(String imagePreviewPrefixUrl) {
        this.imagePreviewPrefixUrl = imagePreviewPrefixUrl;
    }

    public List<String> getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(List<String> whiteList) {
        this.whiteList = whiteList;
    }
}
