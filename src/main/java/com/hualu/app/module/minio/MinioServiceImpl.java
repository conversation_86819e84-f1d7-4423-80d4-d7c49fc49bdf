package com.hualu.app.module.minio;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.tg.dev.api.core.global.exception.BaseException;
import io.minio.MinioClient;
import io.minio.ObjectStat;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;

import javax.activation.MimetypesFileTypeMap;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Map;

public class MinioServiceImpl implements MinioService {

    private static final Logger LOGGER = LoggerFactory.getLogger(MinioServiceImpl.class);

    @Resource
    MinioClient minioClient;

    @Override
    public String upload(String bucketName, String fileClass, String fileName, String ObjectName, InputStream inputStream, long size, Map<String, String> headerMap, String contentType) {
        // 202005
        try {
            int year = DateUtil.date().year();
            int month = DateUtil.date().month() + 1;
            int today = DateUtil.date().dayOfMonth();
            int hour = DateUtil.date().hour(true);
            String suffix = ObjectName.substring(ObjectName.lastIndexOf("."));
            //生成唯一名称
            String sourceName = "";
            if (StringUtils.isBlank(fileName)) {
                //目录名称规则 mgcs/信用代码/年月/传的值
                sourceName = year + "/" + month + "/" + today + "/"+ hour + "/" + IdUtil.fastSimpleUUID() + suffix;
            } else {
                sourceName = fileName;
            }
            minioClient.putObject(bucketName, sourceName, inputStream, size, headerMap, null, contentType);
            return bucketName + "/" + sourceName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return null;
    }

    @Override
    public String upload(String bucketName, String fileClass, String ObjectName, InputStream inputStream, long size, Map<String, String> headerMap, String contentType) {
        return upload(bucketName, fileClass, null, ObjectName, inputStream, size, headerMap, contentType);
    }

    @Override
    public String upload(String bucketName, String fileClass, String ObjectName, File file) {
        FileInputStream fileInputStream = null;
        try {
            String contentType = new MimetypesFileTypeMap().getContentType(file);
            fileInputStream = new FileInputStream(file);
            return upload(bucketName, fileClass, ObjectName, file.getName(), fileInputStream, file.length(), null, contentType);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public String upload(String bucketName, String fileClass, File file) {
        FileInputStream fileInputStream = null;
        try {
            //String contentType = new MimetypesFileTypeMap().getContentType(file);
            String contentType = MediaTypeFactory.getMediaType(file.getName()).orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
            fileInputStream = new FileInputStream(file);
            return upload(bucketName, fileClass, file.getName(), fileInputStream, file.length(), null, contentType);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            throw new BaseException(e.getMessage());
        }
    }

    @Override
    public void download(HttpServletResponse response, String bucketName, String objectName) {
        InputStream stream = null;
        try {
            ObjectStat stat = minioClient.statObject(bucketName, objectName);
            response.setContentType(stat.contentType());
            stream = minioClient.getObject(bucketName, objectName);
            IOUtils.copy(stream, response.getOutputStream());
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        } finally {
            IOUtils.closeQuietly(stream);
        }
    }

    @Override
    public void downloadThuPic(HttpServletResponse response, String bucketName, String objectName, int width, int height) {
        InputStream stream = null;
        try {
            ObjectStat stat = minioClient.statObject(bucketName, objectName);
            response.setContentType(stat.contentType());
            stream = minioClient.getObject(bucketName, objectName);
            Thumbnails.of(stream).size(width, height).toOutputStream(response.getOutputStream());
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        } finally {
            IOUtils.closeQuietly(stream);
        }
    }

    @Override
    public void download(OutputStream outputStream, String bucketName, String objectName) {
        InputStream stream = null;
        try {
            //ObjectStat stat = minioClient.statObject(prop.getBucketName(), objectName);
            stream = minioClient.getObject(bucketName, objectName);
            IOUtils.copy(stream, outputStream);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException(e.getMessage());
        } finally {
            IOUtils.closeQuietly(outputStream);
            IOUtils.closeQuietly(stream);
        }
    }

    @Override
    public void delete(String bucketName, String objectName) {
        try {
            minioClient.removeObject(bucketName, objectName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getObjectUrl(String bucketName, String ObjectName) {
        String objectUrl = null;
        try {
            //objectUrl = minioClient.getPresignedObjectUrl(Method.GET,bucketName,ObjectName,3600,null);
            objectUrl = minioClient.getObjectUrl(bucketName, ObjectName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return objectUrl;
    }

    public static void main(String[] args) {
        String s = MediaTypeFactory.getMediaType("/home/<USER>/sss.png").orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
        System.out.println(s);
    }
}
