package com.hualu.app.module.minio;

import io.minio.MinioClient;
import io.minio.errors.InvalidEndpointException;
import io.minio.errors.InvalidPortException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MinioConfiguration {

    @Autowired
    private MinioProp minioProp;

    @Bean
    public MinioClient minioClient() throws InvalidPortException, InvalidEndpointException {
        MinioClient  client =new MinioClient(minioProp.getEndpoint(),minioProp.getAccesskey(),minioProp.getSecretKey());
        return  client;
    }

    @Bean
    public MinioService minioService(){
        return new MinioServiceImpl();
    }
}
