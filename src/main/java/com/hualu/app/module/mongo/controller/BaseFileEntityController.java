package com.hualu.app.module.mongo.controller;


import cn.hutool.core.util.StrUtil;
import com.hualu.app.comm.RestResult;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.mongo.dto.MgFileDto;
import com.hualu.app.module.mongo.service.BaseFileEntityService;
import com.hualu.app.module.mongo.utils.H_ImageHelper;
import com.hualu.app.utils.H_UploadHelper;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 图片、附件接口
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@RestController
@RequestMapping("/baseFileEntity")
public class BaseFileEntityController {

    @Autowired
    BaseFileEntityService entityService;

    @Autowired
    MinioProp minioProp;

    /**
     * 图片预览
     * @param fileId
     * @param response
     */
    @GetMapping("/previewImage/{fileId}")
    public void previewImage(@PathVariable("fileId") String fileId, HttpServletResponse response){
        /*String mgFileId = entityService.getMgFileId(fileId);
        H_ImageHelper.previewImage("filename",mgFileId,true,response);*/
        String fileEntityPath = entityService.getFileEntityPath(fileId);
        H_ImageHelper.previewImageByMinio(fileEntityPath,response);
    }
    /**
     * 文件预览（浏览器支持的格式）
     * @param fileId
     * @param response
     */
    @SneakyThrows
    @GetMapping("/previewFile/{fileId}")
    public void previewFile(@PathVariable("fileId") String fileId, HttpServletRequest request,HttpServletResponse response){
        String fileEntityPath = entityService.getFileEntityPath(fileId);
        if (StrUtil.isBlank(fileEntityPath)){
            throw new BaseException("找不到对应的文件");
        }
        String contentType = MediaTypeFactory.getMediaType(fileEntityPath).orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
        String redirectUrl = "//" + request.getServerName() + ":" + request.getServerPort() + "/minio/" + fileEntityPath;
        response.setContentType(contentType);
        response.sendRedirect(redirectUrl);
    }
    /**
     * 图片上传
     * @param files
     * @return
     */
    @PostMapping("uploadImage")
    public RestResult<List<String>> uploadImage(@RequestParam(value = "files",required = false) MultipartFile[] files){
        List<String> fileIds = H_UploadHelper.uploadFile(files);
        return RestResult.success(fileIds,"上传成功");
    }


    /**
     * 图片上传（返回ID和照片名称）
     * @param files
     * @return
     */
    @PostMapping("uploadFile")
    public RestResult<List<MgFileDto>> uploadFile(@RequestParam(value = "files",required = false) MultipartFile[] files){
        List<MgFileDto> mgFileDtos = H_UploadHelper.uploadFile(files, true);
        return RestResult.success(mgFileDtos,"上传成功");
    }
}
