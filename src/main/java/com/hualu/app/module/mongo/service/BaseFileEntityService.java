package com.hualu.app.module.mongo.service;

import com.hualu.app.module.mongo.dto.MgFileDto;
import com.hualu.app.module.mongo.entity.BaseFileEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 多媒体文件实体表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface BaseFileEntityService extends IService<BaseFileEntity> {

    /**
     * 获取mongo 文件ID
     * @param fileId
     * @return
     */
    String getMgFileId(String fileId);

    /**
     * 获取文件存储路径
     * @param fileId
     * @return
     */
    String getFileEntityPath(String fileId);

    void saveByMgFileDto(MgFileDto dto);

    void deleteFileByIds(List<String> ids);
}
