package com.hualu.app.module.mongo.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.minio.MinioService;
import com.hualu.app.module.mongo.dto.MgFileDto;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

@Slf4j
public class H_MongoHelper {

    public static MgFileDto uploadFile(File file){
        try {
            /*long fileSize = file.length();
            String dccode = (String) CustomRequestContextHolder.get(C_Constant.DCCODE);
            if (StrUtil.isBlank(dccode)){
                throw new BaseException(C_Constant.DCCODE+"不能为空");
            }
            Document medata = new Document(C_Constant.DCCODE, dccode);
            GridFsTemplate picFsTemplate = (GridFsTemplate) CustomApplicationContextHolder.getBean(C_Constant.PIC_TEMPLATE);
            FileInputStream picStream = new FileInputStream(file);
            //medata 如果存在pdfImagId，直接使用该作为主键ID
            ObjectId objectId = picFsTemplate.store(picStream, file.getName(), medata);
            IoUtil.close(picStream);
            H_ImageHelper.commpresspicForScale(file.getAbsolutePath(),file.getAbsolutePath(),1024,0.9);
            medata.put("sourceId",objectId.toString());

            GridFsTemplate thumTemplate = (GridFsTemplate) CustomApplicationContextHolder.getBean(C_Constant.THUM_TEMPLATE);
            FileInputStream thumSteam = new FileInputStream(file.getAbsolutePath());
            thumTemplate.store(thumSteam,objectId.toString(),medata);
            IoUtil.close(thumSteam);
            MgFileDto dto = new MgFileDto(objectId.toString(), file.getName(), null,String.valueOf(fileSize));*/
            MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
            MinioService minioService = CustomApplicationContextHolder.getBean(MinioService.class);
            String uploadPath = minioService.upload(minioProp.getBucketName(), null, file);
            MgFileDto mgFile = new MgFileDto(IdUtil.fastUUID(),file.getName(),"",String.valueOf(file.length()));
            mgFile.setFileEntityPath(uploadPath);
            log.info("文件上传成功:"+ JSONUtil.toJsonStr(mgFile));
            return mgFile;
        }catch (Exception e){
            throw new BaseException("文件上传失败",e.getCause());
        }
    }
}
