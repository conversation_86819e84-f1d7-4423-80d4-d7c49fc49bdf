<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.mongo.mapper.BaseFileEntityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.mongo.entity.BaseFileEntity">
        <id column="FILE_ENTITY_ID" property="fileEntityId" />
        <result column="FILE_ENTITY_TAG" property="fileEntityTag" />
        <result column="FILE_ENTITY_CODE" property="fileEntityCode" />
        <result column="FILE_ENTITY_NAME" property="fileEntityName" />
        <result column="FILE_ENTITY_DUTY" property="fileEntityDuty" />
        <result column="FILE_ENTITY_DATE" property="fileEntityDate" />
        <result column="FILE_ENTITY_USER" property="fileEntityUser" />
        <result column="FILE_ENTITY_DEPT" property="fileEntityDept" />
        <result column="CREA_SYSDATE" property="creaSysdate" />
        <result column="FILE_ENTITY_PATH" property="fileEntityPath" />
        <result column="FILE_ENTITY_FORMAT" property="fileEntityFormat" />
        <result column="FILE_ENTITY_SIZE" property="fileEntitySize" />
        <result column="REMARK" property="remark" />
        <result column="FILE_ENTITY_MD5" property="fileEntityMd5" />
        <result column="FILE_ENTITY_VERSION_NUM" property="fileEntityVersionNum" />
        <result column="IS_SWF_CONVERSION" property="isSwfConversion" />
        <result column="IS_PDF_CONVERSION" property="isPdfConversion" />
        <result column="FILE_ENTITY_TITLE" property="fileEntityTitle" />
        <result column="MG_FILE_ID" property="mgFileId" />
        <result column="MG_IS_EXISTS" property="mgIsExists" />
        <result column="MG_USER_CODE" property="mgUserCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        FILE_ENTITY_ID, FILE_ENTITY_TAG, FILE_ENTITY_CODE, FILE_ENTITY_NAME, FILE_ENTITY_DUTY, FILE_ENTITY_DATE, FILE_ENTITY_USER, FILE_ENTITY_DEPT, CREA_SYSDATE, FILE_ENTITY_PATH, FILE_ENTITY_FORMAT, FILE_ENTITY_SIZE, REMARK, FILE_ENTITY_MD5, FILE_ENTITY_VERSION_NUM, IS_SWF_CONVERSION, IS_PDF_CONVERSION, FILE_ENTITY_TITLE, MG_FILE_ID, MG_IS_EXISTS, MG_USER_CODE
    </sql>

</mapper>
