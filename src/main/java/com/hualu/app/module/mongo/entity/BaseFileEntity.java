package com.hualu.app.module.mongo.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 多媒体文件实体表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BaseFileEntity对象", description="多媒体文件实体表")
public class BaseFileEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId("FILE_ENTITY_ID")
    private String fileEntityId;

    @ApiModelProperty(value = "文档标签TAG")
    @TableField("FILE_ENTITY_TAG")
    private String fileEntityTag;

    @ApiModelProperty(value = "文档编号")
    @TableField("FILE_ENTITY_CODE")
    private String fileEntityCode;

    @ApiModelProperty(value = "文件名称")
    @TableField("FILE_ENTITY_NAME")
    private String fileEntityName;

    @ApiModelProperty(value = "责任者")
    @TableField("FILE_ENTITY_DUTY")
    private String fileEntityDuty;

    @ApiModelProperty(value = "文档成文日期")
    @TableField("FILE_ENTITY_DATE")
    private String fileEntityDate;

    @ApiModelProperty(value = "上传用户")
    @TableField("FILE_ENTITY_USER")
    private String fileEntityUser;

    @ApiModelProperty(value = "编制单位")
    @TableField("FILE_ENTITY_DEPT")
    private String fileEntityDept;

    @ApiModelProperty(value = "上传日期")
    @TableField("CREA_SYSDATE")
    private String creaSysdate;

    @ApiModelProperty(value = "文档位置")
    @TableField("FILE_ENTITY_PATH")
    private String fileEntityPath;

    @ApiModelProperty(value = "文档格式")
    @TableField("FILE_ENTITY_FORMAT")
    private String fileEntityFormat;

    @ApiModelProperty(value = "文档大小")
    @TableField("FILE_ENTITY_SIZE")
    private Long fileEntitySize;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "MD5文档指纹码")
    @TableField("FILE_ENTITY_MD5")
    private String fileEntityMd5;

    @ApiModelProperty(value = "文件版本号")
    @TableField("FILE_ENTITY_VERSION_NUM")
    private String fileEntityVersionNum;

    @ApiModelProperty(value = "是否转换成功(0转换失败，1转换成功)")
    @TableField("IS_SWF_CONVERSION")
    private String isSwfConversion;

    @ApiModelProperty(value = "是否转换成功(0转换失败，1转换成功)")
    @TableField("IS_PDF_CONVERSION")
    private String isPdfConversion;

    @ApiModelProperty(value = "文档题名")
    @TableField("FILE_ENTITY_TITLE")
    private String fileEntityTitle;

    @ApiModelProperty(value = "Mongdb文件ID")
    @TableField("MG_FILE_ID")
    private String mgFileId;

    @ApiModelProperty(value = "是否迁移到Mongodb数据库中(1：已迁移，0：未迁移)")
    @TableField("MG_IS_EXISTS")
    private String mgIsExists;

    @ApiModelProperty(value = "上传用户账号")
    @TableField("MG_USER_CODE")
    private String mgUserCode;


}
