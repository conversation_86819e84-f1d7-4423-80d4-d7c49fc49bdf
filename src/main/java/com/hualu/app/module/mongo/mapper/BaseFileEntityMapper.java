package com.hualu.app.module.mongo.mapper;

import com.hualu.app.module.mongo.entity.BaseFileEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 多媒体文件实体表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface BaseFileEntityMapper extends BaseMapper<BaseFileEntity> {

    @Delete("<script>delete from gdgs.base_file_entity where FILE_ENTITY_ID in " +
            "<foreach collection='ids' item='item' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach></script>")
    void deleteFileByIds(@Param("ids") List<String> ids);
}
