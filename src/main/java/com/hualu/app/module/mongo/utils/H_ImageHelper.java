package com.hualu.app.module.mongo.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.minio.MinioService;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;

@Slf4j
public class H_ImageHelper {

    @SneakyThrows
    public static void previewImageByMinio(String filePath,HttpServletResponse response){
        MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
        MinioService minioService = CustomApplicationContextHolder.getBean(MinioService.class);
        String objectName = filePath.replaceAll(minioProp.getBucketName(), "");
        minioService.download(response.getOutputStream(), minioProp.getBucketName(), objectName);
    }

    /**
     *
     * @param srcPath 原图片地址
     * @param desPath 目标图片地址
     * @param desFileSize 指定图片大小,单位kb
     * @param accuracy 精度,递归压缩的比率,建议小于0.9
     * @return
     */
    @SneakyThrows
    public static String commpresspicForScale(String srcPath,String desPath,long desFileSize,double accuracy){

        File srcFile = new File(srcPath);
        long srcFilesize = srcFile.length();
        log.info("原图片:"+srcPath + ",大小:" + srcFilesize/1024 + "kb");
        long l = System.currentTimeMillis();
        //递归压缩,直到目标文件大小小于desFileSize
        commpresspicCycle(desPath,desFileSize,accuracy);

        File desFile = new File(desPath);
        log.info("目标图片:" + desPath + ",大小" + desFile.length()/1024 + "kb");
        log.info("图片压缩完成:耗时"+(System.currentTimeMillis()-l)+"毫秒");
        return desPath;
    }


    public static void commpresspicCycle(String desPath,long desFileSize,double accuracy) throws IOException{
        File imgFile = new File(desPath);
        long fileSize = imgFile.length();
        //判断大小,如果小于500k,不压缩,如果大于等于500k,压缩
        if(fileSize <= desFileSize * 1024){
            return;
        }
        //计算宽高
        BufferedImage bim = ImageIO.read(imgFile);
        int imgWidth = bim.getWidth();
        int imgHeight = bim.getHeight();
        int desWidth = new BigDecimal(imgWidth).multiply(
                new BigDecimal(accuracy)).intValue();
        int desHeight = new BigDecimal(imgHeight).multiply(
                new BigDecimal(accuracy)).intValue();
        Thumbnails.of(desPath).size(desWidth,desHeight).outputQuality(accuracy).toFile(desPath);
        //如果不满足要求,递归直至满足小于1M的要求
        commpresspicCycle(desPath,desFileSize,accuracy);
    }

    public static void main(String[] args) throws Exception {
        File file = new File("D:\\upload\\temp\\7a16222e067d490484299be08b0d48b7.jpg");
        FileInputStream stream = new FileInputStream(file);
        IoUtil.close(stream);
        commpresspicForScale(file.getAbsolutePath(),file.getAbsolutePath(),1024,0.9);
        FileUtil.del(file);
        System.out.println("11111111111");
    }
}
