package com.hualu.app.module.mongo.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.hualu.app.module.mongo.dto.MgFileDto;
import com.hualu.app.module.mongo.entity.BaseFileEntity;
import com.hualu.app.module.mongo.mapper.BaseFileEntityMapper;
import com.hualu.app.module.mongo.service.BaseFileEntityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 多媒体文件实体表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@DS("gdgsDs")
@Service
public class BaseFileEntityServiceImpl extends ServiceImpl<BaseFileEntityMapper, BaseFileEntity> implements BaseFileEntityService {

    @Override
    public String getMgFileId(String fileId) {

        BaseFileEntity entity = getById(fileId);
        if (entity == null){
            throw new BaseException("文件不存在");
        }
        return entity.getMgFileId();
    }

    @Override
    public String getFileEntityPath(String fileId) {
        BaseFileEntity entity = getById(fileId);
        if (entity == null || StrUtil.isBlank(entity.getFileEntityPath())){
            throw new BaseException("文件不存在");
        }
        return entity.getFileEntityPath();
    }

    @Override
    public void saveByMgFileDto(MgFileDto dto) {
        BaseFileEntity entity = new BaseFileEntity();
        entity.setFileEntityId(dto.getId());
        entity.setFileEntityName(dto.getFilename());
        entity.setMgFileId(dto.getId());
        entity.setMgIsExists("1");
        entity.setCreaSysdate(DateUtil.now());
        entity.setFileEntityDate(DateUtil.now());
        entity.setMgUserCode(CustomRequestContextHolder.getUserCode());
        entity.setFileEntityUser(CustomRequestContextHolder.getUserCode());
        entity.setFileEntitySize(Long.valueOf(dto.getChunkSize()));
        entity.setFileEntityFormat(dto.getFileType());
        entity.setFileEntityPath(dto.getFileEntityPath());
        save(entity);
    }

    @Override
    public void deleteFileByIds(List<String> ids) {
        baseMapper.deleteFileByIds(ids);
    }
}
