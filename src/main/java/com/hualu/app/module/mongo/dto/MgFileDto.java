package com.hualu.app.module.mongo.dto;

import java.io.Serializable;
import java.util.Map;

public class MgFileDto implements Serializable {

    private String id;
    private String filename;
    private String md5;
    private String chunkSize;
    private String fileType;

    private String fileEntityPath;

    private Map paramMap;


    public MgFileDto() {
        super();
    }


    public MgFileDto(String id, String filename, String md5, String chunkSize) {
        super();
        this.id = id;
        this.filename = filename;
        this.md5 = md5;
        this.chunkSize = chunkSize;
        this.setFileType(filename);
    }


    public String getId() {
        return id;
    }


    public void setId(String id) {
        this.id = id;
    }


    public String getFilename() {
        return filename;
    }


    public void setFilename(String filename) {
        this.filename = filename;
        this.setFileType(filename);
    }


    public String getMd5() {
        return md5;
    }


    public void setMd5(String md5) {
        this.md5 = md5;
    }


    public String getChunkSize() {
        return chunkSize;
    }


    public void setChunkSize(String chunkSize) {
        this.chunkSize = chunkSize;
    }


    public String getFileType() {
        return fileType;
    }


    public void setFileType(String fileType) {
        int idx = filename.lastIndexOf(".");
        if (idx != -1){
            this.fileType = filename.substring(idx+1);
        }else{
            this.fileType = fileType;
        }
    }

    public Map getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map paramMap) {
        this.paramMap = paramMap;
    }

    public String getFileEntityPath() {
        return fileEntityPath;
    }

    public void setFileEntityPath(String fileEntityPath) {
        this.fileEntityPath = fileEntityPath;
    }
}
