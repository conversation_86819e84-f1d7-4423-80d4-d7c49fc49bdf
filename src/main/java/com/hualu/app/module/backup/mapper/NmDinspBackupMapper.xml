<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.app.module.backup.mapper.NmDinspBackupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hualu.app.module.backup.entity.NmDinspBackup">
        <id column="DINSP_ID" property="dinspId" />
        <result column="DINSP_CODE" property="dinspCode" />
        <result column="PROCESSINSTID" property="processinstid" />
        <result column="FACILITY_CAT" property="facilityCat" />
        <result column="STRUCT_ID" property="structId" />
        <result column="STRUCT_NAME" property="structName" />
        <result column="MNT_ORG_ID" property="mntOrgId" />
        <result column="MNT_ORG_NM" property="mntOrgNm" />
        <result column="SEARCH_DEPT" property="searchDept" />
        <result column="INSP_PERSON" property="inspPerson" />
        <result column="INSP_DATE" property="inspDate" />
        <result column="INSP_TIME" property="inspTime" />
        <result column="INSP_FREQUENCY" property="inspFrequency" />
        <result column="LINE_CODE" property="lineCode" />
        <result column="LINE_NAME" property="lineName" />
        <result column="LINE_DIRECT" property="lineDirect" />
        <result column="ROUTE_NAME" property="routeName" />
        <result column="STAKE_NAME" property="stakeName" />
        <result column="WEATHER" property="weather" />
        <result column="CREATE_USER_ID" property="createUserId" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_USER_ID" property="updateUserId" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="REMARK" property="remark" />
        <result column="STATUS" property="status" />
        <result column="DSS_NUM" property="dssNum" />
        <result column="ROUTE_CODE" property="routeCode" />
        <result column="DEL_FLAG" property="delFlag" />
        <result column="INSP_TIME_END" property="inspTimeEnd" />
        <result column="XC_TYPE" property="xcType" />
        <result column="COMMON_DINSP_ID" property="commonDinspId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        DINSP_ID, DINSP_CODE, PROCESSINSTID, FACILITY_CAT, STRUCT_ID, STRUCT_NAME, MNT_ORG_ID, MNT_ORG_NM, SEARCH_DEPT, INSP_PERSON, INSP_DATE, INSP_TIME, INSP_FREQUENCY, LINE_CODE, LINE_NAME, LINE_DIRECT, ROUTE_NAME, STAKE_NAME, WEATHER, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, REMARK, STATUS, DSS_NUM, ROUTE_CODE, DEL_FLAG, INSP_TIME_END, XC_TYPE, COMMON_DINSP_ID
    </sql>

</mapper>
