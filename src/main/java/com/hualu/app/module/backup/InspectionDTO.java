package com.hualu.app.module.backup;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@Data
public class InspectionDTO {
    private String mntOrgId;
    @EqualsAndHashCode.Include
    private String dinspId;
    private String structId;
    @EqualsAndHashCode.Include
    private Long processinstid;
    private String structName;
    private String facilityCat;
    private Date inspDate;
    private String createUserId;
    private String dinspCode;
}