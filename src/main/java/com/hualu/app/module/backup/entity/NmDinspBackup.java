package com.hualu.app.module.backup.entity;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.tg.dev.api.annotation.DbColumnLength;
/**
 * <p>
 * 新版日常巡查 数据库实体
 * </p>
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="NmDinspBackup对象", description="新版日常巡查")
public class NmDinspBackup implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableField("DINSP_ID")
    private String dinspId;

    @ApiModelProperty(value = "检查单号")
    @TableField("DINSP_CODE")
    private String dinspCode;

    @ApiModelProperty(value = "流程实例ID")
    @TableField("PROCESSINSTID")
    private Long processinstid;

    @ApiModelProperty(value = "设施类型;LM、QL、HD、BP、SD、JA")
    @TableField("FACILITY_CAT")
    private String facilityCat;

    @ApiModelProperty(value = "结构物ID")
    @TableField("STRUCT_ID")
    private String structId;

    @ApiModelProperty(value = "结构物名称")
    @TableField("STRUCT_NAME")
    private String structName;

    @ApiModelProperty(value = "管养单位ID")
    @TableField("MNT_ORG_ID")
    private String mntOrgId;

    @ApiModelProperty(value = "管养单位名称")
    @TableField("MNT_ORG_NM")
    private String mntOrgNm;

    @ApiModelProperty(value = "养护单位")
    @TableField("SEARCH_DEPT")
    private String searchDept;

    @ApiModelProperty(value = "巡查人")
    @TableField("INSP_PERSON")
    private String inspPerson;

    @ApiModelProperty(value = "巡查日期")
    @TableField("INSP_DATE")
    private LocalDateTime inspDate;

    @ApiModelProperty(value = "巡查/检查时间;时分秒：10:01:34")
    @TableField("INSP_TIME")
    private String inspTime;

    @ApiModelProperty(value = "巡查频率;1：白天、2：夜间")
    @TableField("INSP_FREQUENCY")
    private Integer inspFrequency;

    @ApiModelProperty(value = "路线编码")
    @TableField("LINE_CODE")
    private String lineCode;

    @ApiModelProperty(value = "路线名称")
    @TableField("LINE_NAME")
    private String lineName;

    @ApiModelProperty(value = "巡查方向;上行、下行、全线")
    @TableField("LINE_DIRECT")
    private String lineDirect;

    @ApiModelProperty(value = "路段名称;权限范围内的路段下拉框选择")
    @TableField("ROUTE_NAME")
    private String routeName;

    @ApiModelProperty(value = "桩号范围")
    @TableField("STAKE_NAME")
    private String stakeName;

    @ApiModelProperty(value = "天气情况")
    @TableField("WEATHER")
    private String weather;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "CREATE_USER_ID", fill = FieldFill.INSERT)
    private String createUserId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新人")
    @TableField(value = "UPDATE_USER_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "数据状态;0:未提交，1：已提交，2：审核中，3：已审核，-1：退回")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty(value = "病害个数")
    @TableField("DSS_NUM")
    private Integer dssNum;

    @ApiModelProperty(value = "路段编码")
    @TableField("ROUTE_CODE")
    private String routeCode;

    @ApiModelProperty(value = "是否已删除（1：已删除，0：未删除）")
    @TableField(value = "DEL_FLAG", fill = FieldFill.INSERT)
    private Integer delFlag;

    @ApiModelProperty(value = "巡查结束时间")
    @TableField("INSP_TIME_END")
    private String inspTimeEnd;

    @ApiModelProperty(value = "巡查类型（1：养护单位，0：路段公司）")
    @TableField("XC_TYPE")
    private Integer xcType;

    @ApiModelProperty(value = "公用主单ID")
    @TableField("COMMON_DINSP_ID")
    private String commonDinspId;

    @ApiModelProperty(value = "主键")
    @TableId("UP_ID")
    private String upId;


}
