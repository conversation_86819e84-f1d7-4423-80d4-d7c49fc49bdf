package com.hualu.app.comm;

/**
 * <p>
 * restapi返回实体封装类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10 10:54
 */
public class RestResult<T> {

  private int code;

  private String message;

  private T data;

  private long total;

  private long page;

  private long pageSize;

  public long getPageSize() {
    return pageSize;
  }

  public void setPageSize(long pageSize) {
    this.pageSize = pageSize;
  }

  public long getTotal() {
    return total;
  }

  public void setTotal(long total) {
    this.total = total;
  }

  public long getPage() {
    return page;
  }

  public void setPage(long page) {
    this.page = page;
  }

  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public T getData() {
    return data;
  }

  public void setData(T data) {
    this.data = data;
  }

  public static final int SUCCESS = 1;
  public static final int ERROR = 0;
  public static final int WARN = -1;

  public RestResult(int code) {
    this.code = code;
  }

  public RestResult(int code, String message, T data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public RestResult(int code, String message, T data, long total, long page, long pageSize) {
    this.code = code;
    this.message = message;
    this.data = data;
    this.total = total;
    this.page = page;
    this.pageSize = pageSize;
  }

  public static <T> RestResult<T> success(String message) {
    return success(null, message);
  }

  public static <T> RestResult<T> success(T data) {
    return success(data, "操作成功");
  }

  public static <T> RestResult<T> success(T data, long total, long page, long pageSize) {
    return new RestResult<>(SUCCESS, "操作成功", data, total, page, pageSize);
  }

  public static <T> RestResult<T> success(T data, String message) {
    return new RestResult<>(SUCCESS, message, data);
  }

  public static <T> RestResult<T> error(String message) {
    return error(null, message);
  }

  public static <T> RestResult<T> error() {
    return error(null, "操作失败");
  }

  public static <T> RestResult<T> warn(String message) {
    return warn(null, message);
  }

  public static <T> RestResult<T> error(T data, String message) {
    return new RestResult<>(ERROR, message, data);
  }

  public static <T> RestResult<T> warn(T data, String message) {
    return new RestResult<>(WARN, message, data);
  }

}
