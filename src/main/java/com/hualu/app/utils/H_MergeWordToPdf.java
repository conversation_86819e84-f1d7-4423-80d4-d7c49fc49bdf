package com.hualu.app.utils;


import com.aspose.words.*;

import java.util.ArrayList;
import java.util.List;

public class H_MergeWordToPdf {

    /**
     * word转pdf
     *
     * @param wordPath word文件保存的路径
     * @param pdfPath  转换后pdf文件保存的路径
     * <AUTHOR>
     * @date 2020/12/25 13:51
     */
    public static void wordToPdf(List<String> wordPath, String pdfPath) throws Exception {
        wordPath.sort(String::compareTo);
        // 创建一个新的文档作为容器
        Document mergedDoc = new Document();
        if (mergedDoc.getSections().getCount() > 0) {
            mergedDoc.getSections().removeAt(0);
        }
        for (String path : wordPath) {
            Document srcDoc = new Document(path);
            normalizeParagraphFormat(srcDoc);
            for (Section section : srcDoc.getSections()) {
                // 获取节的页面设置
                PageSetup pageSetup = section.getPageSetup();
                // 调整上边距，这里将上边距设置为 1 厘米
                pageSetup.setTopMargin(1 * 28.345);
                pageSetup.setBottomMargin(1 * 28.345);
                Node importNode = mergedDoc.importNode(section, true, ImportFormatMode.KEEP_SOURCE_FORMATTING);
                if (!isNodeEmpty(importNode)){
                    mergedDoc.appendChild(importNode);
                }
            }
        }
        mergedDoc.save(pdfPath, SaveFormat.PDF);
    }

    private static void normalizeParagraphFormat(Document doc) {
        NodeCollection<Paragraph> paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
        for (Paragraph paragraph : paragraphs) {
            // 设置左缩进为 0
            paragraph.getParagraphFormat().setLeftIndent(0);
            // 设置首行缩进为 0
            paragraph.getParagraphFormat().setFirstLineIndent(0);
        }
    }

    private static boolean isNodeEmpty(Node node) {
        // 若节点不是 Section 类型，直接判定为非空
        if (node.getNodeType() != NodeType.SECTION) {
            return false;
        }
        // 将节点转换为 Section 类型
        Section section = (Section) node;
        // 获取 Section 的 Body
        Body body = section.getBody();

        // 若 Body 中没有子节点，判定为空
        if (body.getChildNodes().getCount() == 0) {
            return true;
        }

        // 遍历 Body 中的子节点
        for (int i = 0; i < body.getChildNodes().getCount(); i++) {
            Node child = body.getChildNodes().get(i);
            // 若子节点是段落类型
            if (child.getNodeType() == NodeType.PARAGRAPH) {
                Paragraph paragraph = (Paragraph) child;
                // 若段落文本去除首尾空格后不为空，判定为非空
                if (!paragraph.getText().trim().isEmpty()) {
                    return false;
                }
            }
        }
        // 若遍历完所有段落都为空，判定为空
        return true;
    }

    public static void main(String[] args){
        List<String> wordFiles = new ArrayList<String>();
        wordFiles.add("D:\\upload\\temp\\NmFinsp\\BP\\2025-02-27\\1894995034227478528\\JCJC-BP-XBYH-20250210-0001_JjoakI.docx");
        wordFiles.add("D:\\upload\\temp\\NmFinsp\\BP\\2025-02-27\\1894995642422530048\\JCJC-BP-XBYH-20250210-0001_qYA8Jt.docx");


        //List<String> wordFiles = FileUtil.loopFiles("D:\\upload\\temp\\NmFinsp\\BP\\2025-02-27").stream().map(File::getAbsolutePath).collect(Collectors.toList());

        try {
            wordToPdf(wordFiles,"D:\\upload\\test.pdf");
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("shengc");
    }
}