package com.hualu.app.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.entity.FwRightDataPermission;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.FwRightDataPermissionService;
import com.hualu.app.module.platform.service.FwRightUserService;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import org.assertj.core.util.Sets;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户路段权限
 */
public class H_DataAuthHelper {

    public static String orgAuthSql = "SELECT o.id FROM gdgs.FW_RIGHT_ORG o START WITH o.id = ( SELECT u.org_id FROM gdgs.fw_right_user u WHERE u.user_code = '{}'  AND u.IS_DELETEd = 0 AND u.IS_ENABLE = 1 ) CONNECT BY PRIOR o.ID = o.PARENT_ID and LEVEL<4 and IS_ENABLE=1 and is_deleted=0";
    public static String orgSql = "SELECT 1 FROM ( "+ orgAuthSql +") WHERE OPRT_ORG_CODE = id OR PRJ_ORG_CODE = id";


    /**
     * 是否三级单位系统管理员
     * @return
     */
    public static boolean isSystemManager(){
        FwRightUserService bean = CustomApplicationContextHolder.getBean(FwRightUserService.class);
        return bean.isSystemManager();
    }

    /**
     * 逻辑桩号转物理桩号
     * @param lineId
     * @param lineDirect
     * @param rlStake
     * @return
     */
    public static BaseRouteDto getRouteDto(String lineId,String lineDirect,Double rlStake){
        FwRightDataPermissionService bean = CustomApplicationContextHolder.getBean(FwRightDataPermissionService.class);
        return bean.getRouteRpStake(lineId, lineDirect, rlStake);
    }

    /**
     * 用户授权路线
     * @return
     */
    public static Set<String> selectGrantLineIds(){
        String userCode = CustomRequestContextHolder.getUserCode();
        if (StrUtil.isBlank(userCode)){
            throw new BaseException("用户账号不能为空");
        }
        String orgSql = StrUtil.format(H_DataAuthHelper.orgSql, userCode);
        LambdaQueryWrapper<FwRightDataPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.exists(orgSql);
        queryWrapper.select(FwRightDataPermission::getLineId);
        queryWrapper.groupBy(FwRightDataPermission::getLineId);

        FwRightDataPermissionService bean = CustomApplicationContextHolder.getBean(FwRightDataPermissionService.class);
        List<FwRightDataPermission> dataPermissions = bean.list(queryWrapper);

        if (CollectionUtil.isEmpty(dataPermissions)){
            return Sets.newHashSet();
        }
        return dataPermissions.stream().map(FwRightDataPermission::getLineId).collect(Collectors.toSet());
    }

    /**
     * 查询当前用户本身及下级机构
     * @return
     */
    public static Set<String> selectOrgIds(){
        return selectOrgIdsByUserCode(CustomRequestContextHolder.getUserCode(),true);
    }

    public static Set<String> selectOrgIdsByUserCode(String userCode,boolean isOprtOrg){
        if (StrUtil.isBlank(userCode)){
            throw new BaseException("用户账号不能为空");
        }
        Set<String> orgSet = Sets.newHashSet();

        LambdaQueryWrapper<FwRightDataPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.exists(StrUtil.format(H_DataAuthHelper.orgSql, userCode));
        queryWrapper.select(FwRightDataPermission::getPrjOrgCode,FwRightDataPermission::getOprtOrgCode);
        //queryWrapper.groupBy(FwRightDataPermission::getOprtOrgCode,FwRightDataPermission::getOprtOrgCode);
        FwRightDataPermissionService bean = CustomApplicationContextHolder.getBean(FwRightDataPermissionService.class);
        List<FwRightDataPermission> dataPermissions = bean.list(queryWrapper);

        if (CollectionUtil.isNotEmpty(dataPermissions)){
            dataPermissions.forEach(item->{
                if (isOprtOrg){
                    orgSet.add(item.getOprtOrgCode());
                }else {
                    orgSet.add(item.getPrjOrgCode());
                    orgSet.add(item.getOprtOrgCode());
                }
            });
        }
        if (orgSet.size() == 0){
            throw new BaseException("当前用户没有组织机构，请联系管理员");
        }
        FwRightUserService userService = CustomApplicationContextHolder.getBean(FwRightUserService.class);
        String selfOrgId = userService.selectOrgId(userCode);
        if (StrUtil.isNotBlank(selfOrgId)){
            orgSet.add(selfOrgId);
        }
        return orgSet;
    }

    public static Set<String> selectRouteCodeAuth(){
        return selectRouteCodeAuth(null);
    }

    /**
     * 根据路线编码查询对应的路段信息
     * @param lineCode
     * @return
     */
    public static Set<String> selectRouteCodeAuthByLineCode(String lineCode){
        BaseLineService baseLineService = CustomApplicationContextHolder.getBean(BaseLineService.class);
        LambdaQueryWrapper<BaseLine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(BaseLine::getIsDeleted,0)
            .eq(BaseLine::getIsEnable,1)
            .eq(BaseLine::getIsNewGgw,1)
            .and(innerWrapper -> innerWrapper
                .eq(BaseLine::getLineCode, lineCode)
                .or()
                .eq(BaseLine::getLineId, lineCode)
            );
        List<BaseLine> list = baseLineService.list(queryWrapper);
        if (CollectionUtil.isEmpty(list)){
            return Sets.newHashSet();
        }
        return selectRouteCodeAuth(list.get(0).getLineId());
    }


    /**
     * 查询路段权限
     * @param lineId
     * @return
     */
    public static Set<String> selectRouteCodeAuth(String lineId){
        return selectRouteCodeAuth(CustomRequestContextHolder.getUserCode(),lineId);
    }

    /**
     * 查询路段权限
     * @param userCode
     * @param lineId
     * @return
     */
    public static Set<String> selectRouteCodeAuth(String userCode,String lineId){

        List<FwRightDataPermission> dataPermissions = selectRightDataPermission(userCode, lineId);
        if (CollectionUtil.isEmpty(dataPermissions)){
            return Sets.newHashSet();
        }
        return dataPermissions.stream().map(FwRightDataPermission::getRouteCode).collect(Collectors.toSet());
    }

    /**
     * 查询单位管养的路段编码集合
     * @param userCode
     * @return
     */
    public static Map<String,Set<String>> selectOrgGroupByRouteCode(String userCode){
        List<FwRightDataPermission> dataPermissions = selectRightDataPermission(userCode, null);
        if (CollectionUtil.isEmpty(dataPermissions)){
            return Maps.newHashMap();
        }
        Map<String, Set<String>> orgRouteMap = dataPermissions.stream().collect(Collectors.groupingBy(FwRightDataPermission::getOprtOrgCode,
                Collectors.mapping(FwRightDataPermission::getRouteCode, Collectors.toSet())));
        return orgRouteMap;

    }

    public static List<FwRightDataPermission> selectRightDataPermission(String userCode,String lineId) {
        if (StrUtil.isBlank(userCode)){
            throw new BaseException("用户账号不能为空");
        }
        String orgSql = StrUtil.format(H_DataAuthHelper.orgSql, userCode);
        LambdaQueryWrapper<FwRightDataPermission> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.exists(orgSql);

        if (StrUtil.isNotBlank(lineId)){
            queryWrapper.eq(FwRightDataPermission::getLineId,lineId);
        }
        queryWrapper.select(FwRightDataPermission::getRouteCode,FwRightDataPermission::getOprtOrgCode);
        queryWrapper.groupBy(FwRightDataPermission::getRouteCode,FwRightDataPermission::getOprtOrgCode);

        FwRightDataPermissionService bean = CustomApplicationContextHolder.getBean(FwRightDataPermissionService.class);
        List<FwRightDataPermission> dataPermissions = bean.selectPermission(queryWrapper);
        return dataPermissions;
    }

    /**
     * 查询路段名称、管养单位等信息
     * @param routeCodes
     * @return
     */
    public static Map<String,BaseRouteDto> selectRouteInfo(Set<String> routeCodes){
        FwRightDataPermissionService bean = CustomApplicationContextHolder.getBean(FwRightDataPermissionService.class);
        List<BaseRouteDto> routeDtos = bean.selectRouteInfo(routeCodes);
        if (CollectionUtil.isEmpty(routeDtos)){
            return Maps.newHashMap();
        }
        Map<String, BaseRouteDto> routeMap = routeDtos.stream().collect(Collectors.toMap(BaseRouteDto::getRouteCode, Function.identity()));
        return routeMap;
    }

    /**
     * 查询管养单位，管理的路段编号
     * @return
     */
    public static Map<String,Set<String>> selectOprtOrgCodeRouteInfo(){
        List<FwRightDataPermission> fwRightDataPermissions = selectRightDataPermission(CustomRequestContextHolder.getUserCode(), null);
        Map<String, List<FwRightDataPermission>> oprtMap = fwRightDataPermissions.stream().collect(Collectors.groupingBy(FwRightDataPermission::getOprtOrgCode));
        Map<String,Set<String>> resMap = new HashMap<>();
        oprtMap.forEach((key,value)->{
            resMap.put(key,value.stream().map(FwRightDataPermission::getRouteCode).collect(Collectors.toSet()));
        });
        return resMap;
    }

    public static void main(String[] args) {
        System.out.println(StrUtil.format(orgSql,"sjzx-tangtg"));
    }
}
