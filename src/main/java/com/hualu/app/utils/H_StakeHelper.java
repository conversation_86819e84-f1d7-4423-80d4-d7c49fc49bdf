package com.hualu.app.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;

/**
 * 桩号工具类
 */
public class H_StakeHelper {

    private final static int GL_UNIT = 1000;

    /**
     * 获取中心桩号
     * @param startStake
     * @param endStake
     * @return
     */
    public static Double getGlCenterStake(String startStake,String endStake){

        if (StrUtil.isBlank(startStake) || StrUtil.isBlank(endStake)){
            return null;
        }

        String qdzh = converGlStake(startStake);
        String zdzh = converGlStake(endStake);

        if (StrUtil.isBlank(qdzh) || StrUtil.isBlank(zdzh)){
            return null;
        }

        //获取中桩号
        Double centerZh = (Double.parseDouble(qdzh) + Double.valueOf(zdzh)) / 2;
        return centerZh;
    }

    /**
     * 判断是否属于区间范围内
     * @param startStake
     * @param endStake
     * @param centerStake
     * @return
     */
    public static Boolean isBh(Double startStake,Double endStake,Double centerStake){
        if (startStake == null || endStake == null || centerStake == null){
            return false;
        }

        if ((startStake - centerStake) * (endStake - centerStake) <= 0){
            return true;
        }
        return false;
    }

    /**
     * 中文中心桩号转换成数字桩号，例如：K100+20 -> 100200
     * @param cnStake
     * @return
     */
    public static String convertStake(String cnStake){
        if (StrUtil.isNotBlank(cnStake) && StrUtil.startWithIgnoreCase(cnStake,"K")){
            String temp = cnStake.trim().replaceAll("K", "");
            String[] split = temp.split("\\+");

            try{
                Double km = Double.valueOf(split[0]) * GL_UNIT;
                if (split.length == 2){
                    Double bm = Double.valueOf(split[1]);
                    km+=bm;
                }
                return km.toString();
            }catch (Exception e){
                return "";
            }
        }
        // cnStake 如果是数字，直接返回
        if (StrUtil.isNotBlank(cnStake) && NumberUtil.isNumber(cnStake)){
            return cnStake;
        }
        return "";
    }

    /**
     * 中文中心桩号转换成公里数字桩号   K100+20 -> 100.200
     * @param cnStake
     * @return
     */
    public static String converGlStake(String cnStake){
        String temp = convertStake(cnStake);
        if (StrUtil.isBlank(temp)){
            return temp;
        }

        return String.valueOf((Double.valueOf(temp)/GL_UNIT));
    }

    /**
     * 数字桩号转换成中文桩号  1234.23 => K1234+230
     * @param stake
     * @return
     */
    public static String convertCnStake(String stake){
        if (StrUtil.isBlank(stake)){
            return "";
        }
        String tempStake = stake.trim().toUpperCase();
        if (NumberUtil.isNumber(stake)){
            Double bmStake = Double.valueOf(stake) * 1000;
            tempStake = getStakeByDouble2Str(bmStake/GL_UNIT,false);
            return tempStake;
        }
        if (StrUtil.isNotBlank(tempStake) && tempStake.startsWith("K")){
            return tempStake;
        }
        return "";
    }

    /**
     * 数字桩号转换成中文桩号  1234.2323 => K1234+230.3
     * @param stake
     * @param scale
     * @return
     */
    public static String convertCnStakeByScale(String stake,int scale){
        if (StrUtil.isBlank(stake)){
            return "";
        }
        String tempStake = stake.trim().toUpperCase();
        if (NumberUtil.isNumber(stake)){
            Double bmStake = new BigDecimal(Double.valueOf(stake) * 1000).setScale(scale,BigDecimal.ROUND_HALF_UP).doubleValue();
            tempStake = getStakeByDouble2Str(bmStake/GL_UNIT,false);
            //说明存在小数点位
            if (bmStake % 1 != 0){
                String[] split = bmStake.toString().split("\\.");
                tempStake = tempStake+"."+StrUtil.subPre(split[split.length-1],scale);
            }
        }
        if (StrUtil.isNotBlank(tempStake) && tempStake.startsWith("K")){
            return tempStake;
        }
        return "";
    }


    private static String getStakeByDouble2Str(Double stake, boolean flag){
        if (stake == null){
            return null;
        }
        Double glStake = new BigDecimal(stake).setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue();
        Double bmStake = new BigDecimal(stake*GL_UNIT%GL_UNIT).setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue();
        //如果上千公里，前面补4个0，否则补3个零
        String glFormat = glStake.doubleValue()/GL_UNIT>0?"%04d":"%03d";
        if (flag){
            return "K"+String.format(glFormat,glStake.intValue())+"+"+String.format("%03d",bmStake.intValue());
        }
        return "K"+glStake.intValue()+"+"+String.format("%03d",bmStake.intValue());
    }

    public static void main(String[] args) {

        /*System.out.println(convertCnStake("43004.333"));
        //System.out.println(getCurrentMonth());



        System.out.println(NumberUtil.isNumber("K345.123"));


        String[] str = new String[]{"2","3","5","6"};

        String join = StrUtil.join(",", str);
        System.out.println(join);*/

        System.out.println(convertCnStake("1234.2399"));

        String s = StrUtil.subPre("123456", 2);
        System.out.println(s);

    }
}
