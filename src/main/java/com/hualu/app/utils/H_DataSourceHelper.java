package com.hualu.app.utils;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.provider.DynamicDataSourceProvider;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;

import javax.sql.DataSource;

/**
 * 数据源，结合Hutool DbUtil.use()使用
 */
public class H_DataSourceHelper {
    /**
     * 获取数据源
     * @param dsName 数据源名称
     * @return
     */
    public static DataSource getDataSource(String dsName){
        //获取数据源集合
        DynamicDataSourceProvider dsBean = CustomApplicationContextHolder.getBean(DynamicDataSourceProvider.class);

        DataSource dataSource = dsBean.loadDataSources().get(dsName);
        if (dataSource == null){
            throw new BaseException(dsName+"数据源不存在");
        }
        return dataSource;
    }

    public static DataSource getDefaulDataSource(){
        String dsName = DynamicDataSourceContextHolder.peek();
        return getDataSource(StrUtil.isBlank(dsName)?"master":dsName);
    }
}
