package com.hualu.app.utils;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF盖章检测工具类
 */
public class PDFUtils {

    /**
     * 检测PDF文件是否包含印章
     *
     * @param pdfFile PDF文件
     * @return 是否包含印章
     */
    public static boolean hasStamp(File pdfFile) throws IOException {
        try (InputStream is = new FileInputStream(pdfFile);
             PDDocument document = PDDocument.load(is)) {

            PDFRenderer renderer = new PDFRenderer(document);

            // 遍历PDF的每一页
            for (int i = 0; i < document.getNumberOfPages(); i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, 150); // 使用适当的DPI

                // 检测该页是否含有印章
                if (detectStampInImage(image)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 使用颜色分析和区域连通性检测图像中是否包含印章
     */
    private static boolean detectStampInImage(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 创建二值化图像，标记可能的印章像素
        boolean[][] stampPixels = new boolean[height][width];

        // 遍历图像像素，查找符合印章颜色特征的像素
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                Color color = new Color(image.getRGB(x, y));

                // 检测红色或蓝色印章颜色
                if (isStampColor(color)) {
                    stampPixels[y][x] = true;
                }
            }
        }

        // 查找连通区域（可能的印章区域）
        List<Integer> regionSizes = findConnectedRegions(stampPixels);

        // 分析区域大小，判断是否存在印章
        for (int regionSize : regionSizes) {
            // 如果连通区域足够大，且不太大（避免整页都是某种颜色的情况）
            if (regionSize > 500 && regionSize < width * height * 0.1) {
                return true;
            }
        }

        return false;
    }

    /**
     * 查找连通区域并返回每个区域的像素数量
     */
    private static List<Integer> findConnectedRegions(boolean[][] pixels) {
        int height = pixels.length;
        int width = pixels[0].length;
        boolean[][] visited = new boolean[height][width];
        List<Integer> regionSizes = new ArrayList<>();

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                if (pixels[y][x] && !visited[y][x]) {
                    // 发现新区域，执行深度优先搜索
                    int regionSize = dfs(pixels, visited, x, y, width, height);
                    regionSizes.add(regionSize);
                }
            }
        }

        return regionSizes;
    }

    /**
     * 使用深度优先搜索查找连通区域大小
     */
    private static int dfs(boolean[][] pixels, boolean[][] visited, int x, int y, int width, int height) {
        if (x < 0 || y < 0 || x >= width || y >= height || !pixels[y][x] || visited[y][x]) {
            return 0;
        }

        visited[y][x] = true;
        int size = 1;

        // 遍历八个方向
        size += dfs(pixels, visited, x+1, y, width, height);
        size += dfs(pixels, visited, x-1, y, width, height);
        size += dfs(pixels, visited, x, y+1, width, height);
        size += dfs(pixels, visited, x, y-1, width, height);
        size += dfs(pixels, visited, x+1, y+1, width, height);
        size += dfs(pixels, visited, x-1, y-1, width, height);
        size += dfs(pixels, visited, x+1, y-1, width, height);
        size += dfs(pixels, visited, x-1, y+1, width, height);

        return size;
    }

    /**
     * 判断颜色是否为印章颜色（红色或蓝色）
     */
    private static boolean isStampColor(Color color) {
        return isRedSealColor(color) || isBlueSealColor(color);
    }

    /**
     * 判断颜色是否为红色印章颜色
     */
    private static boolean isRedSealColor(Color color) {
        int r = color.getRed();
        int g = color.getGreen();
        int b = color.getBlue();

        // 红色印章颜色判断（红色分量明显高于其他分量）
        return r > 150 && r > g * 1.5 && r > b * 1.5;
    }

    /**
     * 判断颜色是否为蓝色印章颜色
     */
    private static boolean isBlueSealColor(Color color) {
        int r = color.getRed();
        int g = color.getGreen();
        int b = color.getBlue();

        // 蓝色印章颜色判断（蓝色分量明显高于其他分量）
        return b > 150 && b > r * 1.5 && b > g * 1.5;
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        try {
            File pdfFile = new File("C:\\Users\\<USER>\\Desktop\\附件21数据中心系统维护申请表模板20250402 - .pdf");
            boolean hasStamp = hasStamp(pdfFile);
            System.out.println("PDF文件是否包含印章: " + hasStamp);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}