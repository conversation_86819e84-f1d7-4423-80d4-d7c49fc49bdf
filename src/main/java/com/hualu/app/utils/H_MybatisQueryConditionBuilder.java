package com.hualu.app.utils;

import com.baomidou.mybatisplus.core.conditions.AbstractLambdaWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class H_MybatisQueryConditionBuilder {

    // 缓存字段名到 SFunction 的映射
    private static final Map<Class<?>, Map<String, SFunction<?, ?>>> FIELD_FUNCTION_CACHE = new HashMap<>();

    /**
     * 根据实体类的字段属性自动组装查询条件
     *
     * @param wrapper 抽象 Lambda 查询包装器
     * @param entity  实体类对象
     * @param <T>     实体类类型
     */
    public static <T> void buildQueryConditions(AbstractLambdaWrapper<T, ?> wrapper, T entity) {
        if (Objects.isNull(entity)) {
            return;
        }
        Class entityClass = entity.getClass();
        Field[] fields = entityClass.getDeclaredFields();
        for (Field field : fields) {
            // 排除 serialVersionUID 字段
            if ("serialVersionUID".equals(field.getName())) {
                continue;
            }
            field.setAccessible(true);
            try {
                Object value = field.get(entity);
                if (Objects.nonNull(value)) {
                    String fieldName = field.getName();
                    SFunction<T, Object> column = H_SFunctionHelper.generateSFunction(entityClass,fieldName);
                    if (column != null) {
                        if (value instanceof String) {
                            wrapper.like(column, value);
                        } else if (value instanceof Collection) {
                            wrapper.in(column, (Collection<?>) value);
                        } else if (value instanceof Number) {
                            handleNumberCondition(wrapper, column, entity, fieldName);
                        } else {
                            wrapper.eq(column, value);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 处理数字类型的查询条件
     *
     * @param wrapper   抽象 Lambda 查询包装器
     * @param column    列对应的 SFunction
     * @param entity    实体类对象
     * @param fieldName 字段名
     * @param <T>       实体类类型
     */
    private static <T> void handleNumberCondition(AbstractLambdaWrapper<T, ?> wrapper, SFunction<T, ?> column, T entity, String fieldName) throws Exception {
        String gtFieldName = fieldName + "Gt";
        String ltFieldName = fieldName + "Lt";
        String eqFieldName = fieldName + "Eq";

        Field gtField = getField(entity.getClass(), gtFieldName);
        Field ltField = getField(entity.getClass(), ltFieldName);
        Field eqField = getField(entity.getClass(), eqFieldName);

        if (gtField != null) {
            Object gtValue = gtField.get(entity);
            if (gtValue != null) {
                wrapper.gt(column, gtValue);
                return;
            }
        }
        if (ltField != null) {
            Object ltValue = ltField.get(entity);
            if (ltValue != null) {
                wrapper.lt(column, ltValue);
                return;
            }
        }
        if (eqField != null) {
            Object eqValue = eqField.get(entity);
            if (eqValue != null) {
                wrapper.eq(column, eqValue);
                return;
            }
        }
        wrapper.eq(column, getField(entity.getClass(), fieldName).get(entity));
    }

    /**
     * 获取实体类中指定名称的字段
     *
     * @param entityClass 实体类的 Class 对象
     * @param fieldName   字段名
     * @return 字段对象，如果不存在则返回 null
     */
    private static Field getField(Class<?> entityClass, String fieldName) {

        try {
            return entityClass.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            return null;
        }
    }
}
