package com.hualu.app.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.List;

/**
 * 新版巡查单号工具类
 */
public class H_InspCodeHelper {

    /**
     * 检查编号是否符合规则
     * @param inspCode
     * @param inspDate
     * @return
     */
    public static boolean checkCode(String inspCode, Date inspDate){
        if (StrUtil.isBlank(inspCode) || inspDate == null){
            return false;
        }
        List<String> split = StrUtil.split(inspCode, "-");
        // JCJC-HD-GH-20250309-0001
        if (split.size() != 5){
            return false;
        }
        String fromDateStr = split.get(3);
        String inspDateStr = DateUtil.format(inspDate, "yyyyMMdd");

        if (fromDateStr.equals(inspDateStr)){
            return true;
        }
        return false;
    }

    public static void main(String[] args) {
        boolean b = checkCode("JCJC-HD-GH-20250413-0001", DateUtil.date());
        System.out.println(b);
    }
}
