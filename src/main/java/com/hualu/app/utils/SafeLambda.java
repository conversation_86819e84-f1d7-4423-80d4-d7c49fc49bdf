package com.hualu.app.utils;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;

import java.io.Serializable;
import java.lang.reflect.Field;

public class SafeLambda {

    /**
     * 生成支持序列化的 SFunction
     */
    public static <T, R> SFunction<T, R> of(Class<T> clazz, String fieldName) {
        // 获取字段元信息
        Field field = getField(clazz, fieldName);
        
        // 构建可序列化 Lambda
        return (SFunction<T, R> & Serializable) (t) -> {
            try {
                return (R) field.get(t);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        };
    }

    private static Field getField(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field;
        } catch (NoSuchFieldException e) {
            throw new IllegalArgumentException("字段不存在: " + fieldName);
        }
    }

    public static void main(String[] args) {
        SFunction<NmFinsp, Object> finspId = of(NmFinsp.class, "finspId");
        Object apply = finspId.apply(new NmFinsp().setFinspId("1111"));
        System.out.println(apply.toString());
    }
}