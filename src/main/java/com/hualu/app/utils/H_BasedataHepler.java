package com.hualu.app.utils;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.hualu.app.module.basedata.dto.BaseDssSzDto;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.basedata.entity.BaseLine;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.basedata.service.FwRightDataPermissionService;
import com.hualu.app.module.basedata.service.IBaseStructFace;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.util.hp.H_KeyWorker;
import java.util.List;
import java.util.Map;
import org.assertj.core.util.Lists;

/**
 * 基础数据工具类
 */
public class H_BasedataHepler {

    public static final String HUALU_API = "hlApi";
    public static final String JA = "JA";
    public static final String QL = "QL";
    public static final String HD = "HD";
    public static final String SD = "SD";
    public static final String BP = "BP";
    public static final String SFZ = "SFZ";
    public static final String FWQ = "FWQ";
    public static final String LM = "LM";
    public static final String RGD = "RGD";

    public static final String BPJC_VERSION = "202305";

    public static final String DIC_BP_TYPE = "SLOPE_TYPE";
    public static final String DIC_BP_POSITION = "SLOPE_LOCATION";
    public static final String DIC_BP_FXDJ = "BP_FXDJ";
    public static final String DIC_BP_ZHTYPE = "BP_ZH_TYPE";
    public static final String DIC_WEATHER = "WEATHER";


    public static Map<String,String> facilityCatMap = Maps.newHashMap();

    public static Map<String,String> brdgMap = Maps.newHashMap();

    public static Map<String,String> directMap = Maps.newHashMap();

    public static List<BaseDssSzDto> baseDssSzDtos = Lists.newArrayList();

    public static Map<String,String> bpLevel = Maps.newHashMap();

    public static Map<String,String> dssSourceMap = Maps.newHashMap();

    //任务单修复状态
    public static Map<Integer,String> taskRepairStatusMap = Maps.newHashMap();

    //验收单修复状态
    public static Map<Integer,String> acceptStatusMap = Maps.newHashMap();

    public static Map<Integer,String> acceptIsTimeMap = Maps.newHashMap();

    //路政通知书修复状态
    public static Map<Integer,String> roadNoticeStatusMap = Maps.newHashMap();

    static {

        facilityCatMap.put(BP,"边坡");
        facilityCatMap.put(SD,"隧道");
        facilityCatMap.put(QL,"桥梁");
        facilityCatMap.put(HD,"涵洞");
        facilityCatMap.put(LM,"路面");
        facilityCatMap.put(JA,"交安");
        facilityCatMap.put(RGD,"人工岛");

        brdgMap.put("L","左幅");
        brdgMap.put("R","右幅");
        brdgMap.put("K","跨线");
        brdgMap.put("Z","匝道");

        directMap.put("1","上行");
        directMap.put("2","下行");
        directMap.put("4","匝道");

        baseDssSzDtos.add(new BaseDssSzDto("新病害","0"));
        baseDssSzDtos.add(new BaseDssSzDto("旧病害","1"));
        baseDssSzDtos.add(new BaseDssSzDto("修复后损坏","2"));
        baseDssSzDtos.add(new BaseDssSzDto("修复后良好","3"));

        bpLevel.put("1","一级");
        bpLevel.put("2","二级");
        bpLevel.put("3","三级");
        bpLevel.put("4","四级");
        bpLevel.put("5","五级");
        bpLevel.put("6","六级");
        bpLevel.put("7","七级");
        bpLevel.put("8","八级");
        bpLevel.put("9","九级");
        bpLevel.put("10","十级");

        dssSourceMap.put("1","日常巡查");
        dssSourceMap.put("2","经常检查");
        dssSourceMap.put("3","定期检查");
        dssSourceMap.put("4","路政巡查");
        dssSourceMap.put("5","其他");

        taskRepairStatusMap.put(-1,"被退回");
        taskRepairStatusMap.put(0,"起草");
        taskRepairStatusMap.put(1,"已派单");
        taskRepairStatusMap.put(2,"已接收");
        taskRepairStatusMap.put(3,"已生成验收单");
        taskRepairStatusMap.put(4,"部分验收");
        taskRepairStatusMap.put(5,"已验收");
        taskRepairStatusMap.put(6,"审核中");
        taskRepairStatusMap.put(7,"提交验收申请");


        acceptStatusMap.put(0,"申请验收");
        acceptStatusMap.put(1,"提交申请");
        acceptStatusMap.put(2,"审核中");
        acceptStatusMap.put(3,"已验收");
        acceptStatusMap.put(-1,"已退回");

        roadNoticeStatusMap.put(0,"起草");
        roadNoticeStatusMap.put(1,"复核中");
        roadNoticeStatusMap.put(2,"已接收(已复核)");
        roadNoticeStatusMap.put(3,"已审核");
        roadNoticeStatusMap.put(4,"已生成任务单");
        roadNoticeStatusMap.put(-1,"被退回");

        acceptIsTimeMap.put(0,"否");
        acceptIsTimeMap.put(1,"是");
        acceptIsTimeMap.put(2,"");
    }

    /**
     * 获取结构物信息
     * @param structId
     * @param facilityCat
     * @return
     */
    public static BaseStructDto getStructDto(String structId,String facilityCat){
        if (StrUtil.isBlank(facilityCat)){
            return null;
        }
        BaseStructDto baseStructDto = null;
        Map<String, IBaseStructFace> beansOfType = CustomApplicationContextHolder.getBeansOfType(IBaseStructFace.class);
        IBaseStructFace bean = beansOfType.values().stream().filter(e -> e.getFacilityCat().equals(facilityCat)).findFirst().orElse(null);
        try{
            if (bean != null){
                baseStructDto = bean.getId(structId);
            }
        }catch (Exception e){}
        return baseStructDto;
    }

    /**
     * 获取物理桩号及路段信息
     * @param lineId
     * @param lineDirect
     * @param rlStake
     * @return
     */
    public static BaseRouteDto getRouteRpStake(String lineId, String lineDirect, Double rlStake){
        FwRightDataPermissionService bean = CustomApplicationContextHolder.getBean(FwRightDataPermissionService.class);
        BaseRouteDto rpStake = bean.getRouteRpStake(lineId, lineDirect, rlStake);
        return rpStake;
    }

    /**
     * 获取病害性质名称
     * @param dssXzCode
     * @return
     */
    public static String getDssXzName(Integer dssXzCode){
        if (dssXzCode == null){
            return null;
        }
        return baseDssSzDtos.stream().filter(e->e.getCode().equals(dssXzCode.toString())).map(BaseDssSzDto::getName).findFirst().orElse(null);
    }

    /**
     * 组装桥梁名称
     * @param queryType 查询类型
     * @param brdgLineType 桥梁方向
     * @param brdgName 桥梁名称
     * @return
     */
    public static String zzBrdgName(String queryType, String brdgLineType, String brdgName){
        if (StrUtil.isBlank(queryType)){
            String name = brdgMap.getOrDefault(brdgLineType, "其它");
            return brdgName+"（"+name+"）";
        }
        return brdgName;
    }

    /**
     * 组装桥梁幅数
     * @param brdgLineType 桥梁方向
     * @param frameNum 桥梁幅号
     * @return
     */
    public static String zzBrdgFrameNum(String brdgLineType,String frameNum){
        return brdgMap.getOrDefault(brdgLineType,"")+frameNum;
    }

    /**
     * 方向显示中文
     * @param direct
     * @return
     */
    public static String getDirectCnName(String direct){
        return directMap.getOrDefault(direct,"未知");
    }

    /**
     * 获取路线名称
     * @param lineId
     * @return
     */
    public static String getLineName(String lineId){
        BaseLineService bean = CustomApplicationContextHolder.getBean(BaseLineService.class);
        Map<String, BaseLine> lineMap = bean.getAllLineMap();
        BaseLine baseLine = lineMap.get(lineId);
        if (baseLine != null){
            return baseLine.getLineAllname()+"("+baseLine.getLineCode()+")";
        }
        return null;
    }

    /**
     * 获取字典名称
     * @param item
     * @param code
     * @return
     */
    public static String getDicName(String item, String code){
        BaseDatathirdDicService bean = CustomApplicationContextHolder.getBean(BaseDatathirdDicService.class);
        return bean.getDicName(item, code);
    }

    public static void main(String[] args) {
        System.out.println(H_KeyWorker.nextIdToString());
    }
}
