package com.hualu.app.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.app.comm.RestResult;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.mybatisplus.service.PageService;

import java.util.List;

public class H_RestResultHelper {

    public static <T> RestResult<List<T>> returnPage(IPage page){
        PageService pageService = CustomApplicationContextHolder.getBean(PageService.class);
        return H_PageHelper.getPageResult(pageService.returnPageResult(page));
    }
}
