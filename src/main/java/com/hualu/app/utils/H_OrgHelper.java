package com.hualu.app.utils;

import com.hualu.app.module.platform.entity.FwRightOrg;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.tg.dev.api.context.CustomApplicationContextHolder;

import java.util.List;

public class H_OrgHelper {

    /**
     * 获取父类机构ID
     * @param orgId
     * @return
     */
    public static String getParentOrgId(String orgId){
        FwRightOrgService bean = CustomApplicationContextHolder.getBean(FwRightOrgService.class);
        FwRightOrg byId = bean.getById(orgId);
        if (byId != null){
            return byId.getParentId();
        }
        return "";
    }

    /**
     * 查询下级及本身的营运单位ID
     * @param orgId
     * @return
     */
    public static List<String> getChildOprtOrgCodes(String orgId){
        FwRightOrgService bean = CustomApplicationContextHolder.getBean(FwRightOrgService.class);
        List<String> orgIds = bean.selectChildOprtOrgCodes(orgId);
        return orgIds;
    }
}
