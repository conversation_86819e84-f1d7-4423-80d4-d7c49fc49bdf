package com.hualu.app.utils.nminsp;

import cn.hutool.core.date.DateUtil;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.service.NmFinspResultService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.service.struct.BpStructImp;
import com.hualu.app.utils.H_BasedataHepler;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import org.assertj.core.util.Lists;

import java.util.Date;

/**
 * 新版日常巡查工具栏
 */
public class H_NmInspHelper {

    /**
     * 修改边坡经常检查单时，若前台传入检查日期与数据库存储的月份不符，后台需自动更新检查结论表。
     * @param pageFinsp
     */
    public static void updateNmFinspResult(NmFinsp pageFinsp){
        // 不是边坡不需要调整
        if (!H_BasedataHepler.BP.equals(pageFinsp.getFacilityCat())){
            return;
        }
        NmFinspService finspService = CustomApplicationContextHolder.getBean(NmFinspService.class);
        NmFinsp dbFinsp = finspService.getById(pageFinsp.getFinspId());
        if (dbFinsp == null){
            return;
        }
        String pageInsp = buildInspFrequencyByFinsp(pageFinsp.getFacilityCat(), pageFinsp.getInspDate(), pageFinsp.getInspFrequency());
        String dbInsp = buildInspFrequencyByFinsp(dbFinsp.getFacilityCat(), dbFinsp.getInspDate(), pageFinsp.getInspFrequency());

        // 如果检查内容相同，不更新
        if (pageInsp.equals(dbInsp)){
            return;
        }
        // 先清除历史错误的检查结论
        NmFinspResultService resultService = CustomApplicationContextHolder.getBean(NmFinspResultService.class);
        resultService.delBatchByDinspIds(Lists.newArrayList(pageFinsp.getFinspId()));
        BpStructImp bpStructImp = CustomApplicationContextHolder.getBean(BpStructImp.class);
        // 再刷新检查结论
        bpStructImp.refreshNmFinspResult(pageFinsp);
    }

    /**
     * 自动构建边坡经常检查类型
     * @param facilityCat 设施类型
     * @param inspDate 检查日期
     * @param inspFrequency 巡查频率;1：边坡旱季，2：边坡雨季前后，3：边坡雨季中，1：梁式桥，2：拱桥（吊杆拱），5：斜拉桥，6：悬索桥
     * @return
     */
    public static String buildInspFrequencyByFinsp(String facilityCat, Date inspDate, String inspFrequency){
        // 不是边坡，原路返回值
        if (!H_BasedataHepler.BP.equals(facilityCat)){
            return inspFrequency;
        }
        // 雨季中：5、6、7、8、9，雨季前后：3、4、10、11，旱季：1、2、12
        // 获取月份
        int month = DateUtil.month(inspDate) + 1;
        String season = "";
        switch (month) {
            case 1:
            case 2:
            case 12:
                season = "1";
                break;
            case 3:
            case 4:
            case 10:
            case 11:
                season = "2";
                break;
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
                season = "3";
                break;
            default:
                System.out.println("输入的月份有误，请输入1 - 12之间的数字。");
        }
        return season;
    }
}
