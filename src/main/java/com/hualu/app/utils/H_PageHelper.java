package com.hualu.app.utils;

import com.hualu.app.comm.RestResult;
import com.tg.dev.mybatisplus.utils.MiniuiPage;

public class H_PageHelper {

    public static RestResult getPageResult(Object obj){
        MiniuiPage page = (MiniuiPage) obj;
        RestResult result = RestResult.success("操作成功");
        result.setData(page.getData());
        result.setPage(page.getPageIndex());
        result.setPageSize(page.getPageSize());
        result.setTotal(page.getTotal());
        return result;
    }
}
