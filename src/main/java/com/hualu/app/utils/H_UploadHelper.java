package com.hualu.app.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hualu.app.module.minio.MinioProp;
import com.hualu.app.module.mongo.dto.MgFileDto;
import com.hualu.app.module.mongo.service.BaseFileEntityService;
import com.hualu.app.module.mongo.utils.H_MongoHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.core.global.exception.BaseException;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;
import org.springframework.core.env.Environment;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件上传
 */
public class H_UploadHelper {

    //public static String fileReposity = "D:\\upload\\temp\\";

    @SneakyThrows
    public static List<String> uploadFile(MultipartFile[] files){
        List<MgFileDto> mgFileDtos = uploadFile(files, false);
        List<String> fileIds = mgFileDtos.stream().map(MgFileDto::getId).collect(Collectors.toList());
        return fileIds;
    }

    @SneakyThrows
    public static List<MgFileDto> uploadFile(MultipartFile[] files,boolean returnFileName){
        if (files == null || files.length == 0){
            return Lists.newArrayList();
        }
        boolean flag = areFilesInWhitelist(files);
        if (!flag){
            throw new BaseException("系统检测到您上传的文件类型不符合要求");
        }
        if (files.length > 20){
            throw new BaseException("最多上传20张照片");
        }
        Environment bean = SpringUtil.getBean(Environment.class);
        String fileReposity = bean.getProperty("fileReposity");
        if (StrUtil.isBlank(fileReposity)){
            throw new BaseException("fileReposity文件上传目录不能为空");
        }
        List<MgFileDto> fileIds = Lists.newArrayList();
        for (MultipartFile file : files) {
            String filename = file.getOriginalFilename();
            File destFile = new File(fileReposity +filename);
            file.transferTo(destFile);
            //图片上传到mongodb中
            MgFileDto dto = H_MongoHelper.uploadFile(new File(destFile.getAbsolutePath()));
            BaseFileEntityService service = CustomApplicationContextHolder.getBean(BaseFileEntityService.class);
            service.saveByMgFileDto(dto);
            fileIds.add(dto);
            FileUtil.del(destFile);
        }
        return fileIds;
    }

    /**
     * 判断文件数组中的文件是否都在白名单内
     * @param files 文件数组
     * @return 如果所有文件都在白名单内返回 true，否则返回 false
     */
    public static boolean areFilesInWhitelist(MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return false;
        }
        for (MultipartFile file : files) {
            if (!isFileInWhitelist(file.getOriginalFilename())) {
                return false;
            }
        }
        return true;
    }

    public static boolean areFilesInWhitelist(List<File> files) {
        if (files == null || files.size() == 0) {
            return false;
        }
        for (File file : files) {
            if (!isFileInWhitelist(file.getName())) {
                return false;
            }
        }
        return true;
    }


    /**
     * 判断单个文件是否在白名单内
     * @param fileName 单个文件
     * @return 如果文件在白名单内返回 true，否则返回 false
     */
    private static boolean isFileInWhitelist(String fileName) {
        MinioProp minioProp = CustomApplicationContextHolder.getBean(MinioProp.class);
        if (fileName != null && !fileName.isEmpty()) {
            String fileExtension = getFileExtension(fileName);
            return minioProp.getWhiteList().contains(fileExtension.toLowerCase());
        }
        return false;
    }

    /**
     * 获取文件的扩展名
     * @param fileName 文件名
     * @return 文件扩展名
     */
    private static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1);
        }
        return "";
    }
}
