package com.hualu.app.utils.mems;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.dto.BaseStructDto;
import com.hualu.app.module.mems.dss.dto.DssCheckInfo;
import com.hualu.app.module.mems.dss.dto.DssInfoDto;
import com.hualu.app.module.mems.dss.dto.DssRepairInfoDto;
import com.hualu.app.module.mems.dss.dto.DssRepairTimelinessDto;
import com.hualu.app.module.mems.dss.mapper.DssInfoMapper;
import com.hualu.app.module.platform.service.BaseDatathirdDicService;
import com.hualu.app.utils.H_BasedataHepler;
import com.hualu.app.utils.H_DataAuthHelper;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.mybatisplus.utils.H_BatisBatchQuery;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 病害详情工具类
 */
public class H_DssHelper {


    /**
     * 显示病害
     * @param entity
     */
    private static void initDssDisplayNum(DssInfoDto entity){
        StringBuffer str = new StringBuffer();
        Double dssL = entity.getDssL();
        if(dssL!= null && StrUtil.isNotBlank(entity.getDssLUnit()) && BigDecimal.valueOf(dssL).compareTo(BigDecimal.ZERO) > 0){
            str.append("长："+ dssL + entity.getDssLUnit()+" ");
        }
        Double dssW = entity.getDssW();
        if(dssW!= null && StrUtil.isNotBlank(entity.getDssWUnit()) && BigDecimal.valueOf(dssW).compareTo(BigDecimal.ZERO) > 0){
            str.append("宽："+ dssW + entity.getDssWUnit()+" ");
        }
        Double dssD = entity.getDssD();
        if(dssD!= null && StrUtil.isNotBlank(entity.getDssDUnit()) && BigDecimal.valueOf(dssD).compareTo(BigDecimal.ZERO) > 0){
            str.append("深："+ dssD + entity.getDssDUnit()+" ");
        }
        Double dssA = entity.getDssA();
        if(dssA!= null && StrUtil.isNotBlank(entity.getDssAUnit()) && BigDecimal.valueOf(dssA).compareTo(BigDecimal.ZERO) > 0){
            str.append("面积："+ dssA + entity.getDssAUnit()+" ");
        }
        Double dssV = entity.getDssV();
        if(dssV!= null && StrUtil.isNotBlank(entity.getDssVUnit()) && BigDecimal.valueOf(dssV).compareTo(BigDecimal.ZERO) > 0){
            str.append("体积："+ dssV + entity.getDssVUnit()+" ");
        }
        Double dssN = entity.getDssN();
        if(dssN!= null && StrUtil.isNotBlank(entity.getDssNUnit()) && BigDecimal.valueOf(dssN).compareTo(BigDecimal.ZERO) > 0){
            str.append("数量："+ dssN + entity.getDssNUnit()+" ");
        }
        Double dssP = entity.getDssP();
        if(dssP!= null && BigDecimal.valueOf(dssP).compareTo(BigDecimal.ZERO) > 0){
            str.append("百分比："+ dssP +"% ");
        }
        Double dssG = entity.getDssG();
        if(dssG!= null && BigDecimal.valueOf(dssG).compareTo(BigDecimal.ZERO) > 0){
            str.append("角度："+ dssG +"度 ");
        }
        entity.setDssNum(str.toString());
    }

    /**
     * 填充任务单及验收单号
     * @param dssInfoDtos
     */
    public static void setMtaskCode(List<DssInfoDto> dssInfoDtos) {
        DssInfoMapper baseMapper = CustomApplicationContextHolder.getBean(DssInfoMapper.class);
        H_BatisQuery.protectBatchIn(new H_BatisBatchQuery() {
            @Override
            public List selectListForIn(List list) {
                List<DssInfoDto> dssRows = list;
                // 获取病害ID
                List<String> dssIds = dssRows.stream().map(DssInfoDto::getDssId).collect(Collectors.toList());
                List<DssInfoDto> mtaskDtos = baseMapper.selectMtaskCode(dssIds);
                Map<String, List<DssInfoDto>> mtaskMap = mtaskDtos.stream().collect(Collectors.groupingBy(DssInfoDto::getDssId));
                //填充单号
                dssRows.forEach(item->{
                    List<DssInfoDto> row = mtaskMap.get(item.getDssId());
                    if (row != null){
                        //任务单编号
                        List<String> mtaskCodeList = row.stream().filter(e -> StrUtil.isNotBlank(e.getMtaskCode())).map(DssInfoDto::getMtaskCode).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(mtaskCodeList)){
                            item.setMtaskCode(mtaskCodeList.get(0));
                        }else if(StringUtils.isNotBlank(item.getDealOpinion()) && 1 == item.getDealStatus()){
                            item.setMtaskCode(item.getDealOpinion());
                        }
                        //验收单编号
                        List<String> acceptCodeList = row.stream().filter(e -> StrUtil.isNotBlank(e.getMtaskAccptCode())).map(DssInfoDto::getMtaskAccptCode).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(acceptCodeList)){
                            item.setMtaskAccptCode(acceptCodeList.get(0));
                        }
                    }
                });
                return mtaskDtos;
            }
        },dssInfoDtos,200);
    }

    /**
     * 设置巡查相关信息
     * @param dssInfoDtos 病害集合
     */
    public static void setCheckInfo(List<DssInfoDto> dssInfoDtos){
        if (CollectionUtil.isEmpty(dssInfoDtos)){
            return;
        }
        BaseDatathirdDicService dicService = CustomApplicationContextHolder.getBean(BaseDatathirdDicService.class);
        Map<Integer, List<DssInfoDto>> dssSourceMap = dssInfoDtos.stream().collect(Collectors.groupingBy(DssInfoDto::getDssSource));
        dssSourceMap.forEach((key,value)->{
            setViewInfo(value,dicService);
            //巡查来源为空的数据
            Set<String> dssIds = value.stream().filter(e->StrUtil.isBlank(e.getCodeSource())).collect(Collectors.groupingBy(DssInfoDto::getDssId)).keySet();
            H_BatisQuery.protectBatchIn(new H_BatisBatchQuery() {
                @Override
                public List selectListForIn(List params) {
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.in("tr.dss_id",params);
                    List<DssCheckInfo> checkInfos = getCheckInfos(queryWrapper, key);
                    if (CollectionUtil.isNotEmpty(checkInfos)){
                        Map<String, DssCheckInfo> checkMap = checkInfos.stream().collect(Collectors.toMap(DssCheckInfo::getDssId, Function.identity()));
                        value.forEach(dssItem->{
                            DssCheckInfo dssCheckInfo = checkMap.get(dssItem.getDssId());
                            BeanUtil.copyProperties(dssCheckInfo,dssItem);
                        });
                    }
                    return checkInfos;
                }
            }, Lists.newArrayList(dssIds),500);
        });
        Set<String> routeCodes = dssInfoDtos.stream().map(DssInfoDto::getRoutecode).collect(Collectors.toSet());
        Map<String, BaseRouteDto> routeDtoMap = H_DataAuthHelper.selectRouteInfo(routeCodes);
        //设置路段及管养信息
        dssInfoDtos.forEach(item->{
            BaseRouteDto baseRouteDto = routeDtoMap.get(item.getRoutecode());
            if (baseRouteDto != null){
                item.setRouteName(baseRouteDto.getRouteName());
                item.setOprtOrgName(baseRouteDto.getOprtOrgName());
            }
        });
    }

    /**
     * 设置前端页面显示值
     * @param dtos
     */
    private static void setViewInfo(List<DssInfoDto> dtos,BaseDatathirdDicService dicService){
        dtos.forEach(item->{
            BaseStructDto structDto = H_BasedataHepler.getStructDto(item.getStructId(), item.getFacilityCat());
            if (structDto != null){
                item.setDssPosition(structDto.getStructName());
            }
            /*if (H_BasedataHepler.FWQ.equals(item.getFacilityCat())){
                String fwq_qyhf = dicService.getDicName("FWQ_QYHF", item.getStructPartId());
                String dssPosition = item.getDssPosition();
                item.setDssPosition(dssPosition+"【"+fwq_qyhf+"】");
            }
            if (H_BasedataHepler.SFZ.equals(item.getFacilityCat())){
                String fwq_qyhf = dicService.getDicName("SFZ_QYHF", item.getStructPartId());
                String dssPosition = item.getDssPosition();
                item.setDssPosition(dssPosition+"【"+fwq_qyhf+"】");
            }*/
            if (StrUtil.isBlank(item.getDssPosition())){
                String lane = StrUtil.isNotBlank(item.getLane()) ? item.getLane()+" " : "";
                // item.getRlStake() 为空会出现
                item.setDssPosition(lane+ H_StakeHelper.convertCnStake(item.getRlStake()==null?"":item.getRlStake().toString()));
            }
            if (StrUtil.isNotBlank(item.getDssDegree())){
                item.setDssDegree(dicService.getDicName("DSS_DEGREE",item.getDssDegree()));
            }
            item.setDssSourceName(H_BasedataHepler.dssSourceMap.get(item.getDssSource()));
            item.setLineDirectNm(H_BasedataHepler.directMap.get(item.getLinedirect()));
            item.setFacilityCatName(dicService.getDicName("FACILITY_CAT",item.getFacilityCat()));
            H_DssHelper.setDssunitAndNum(item);
            H_DssHelper.setRepairName(item);
            initDssDisplayNum(item);
        });
    }

    /**
     * 查询巡查信息
     * @param queryWrapper
     * @param dssSource
     * @return
     */
    private static List<DssCheckInfo> getCheckInfos(QueryWrapper queryWrapper,Integer dssSource){
        DssInfoMapper baseMapper = CustomApplicationContextHolder.getBean(DssInfoMapper.class);
        if (1 == dssSource || 8 == dssSource){
            return baseMapper.selectByDinsp(queryWrapper);
        }else if (2 == dssSource){
            return baseMapper.selectByFinsp(queryWrapper);
        }else if (4 == dssSource){
            return baseMapper.selectByNotice(queryWrapper);
        }
        return Lists.newArrayList();
    }

    /**
     * 判断已维修的数据，包含：正常维修，超时已维修
     * @param repairInfoDtos
     */
    public static void doRepaired(List<DssRepairInfoDto> repairInfoDtos, DssRepairTimelinessDto repairTimelinessDto) {
        int normalNum = 0;
        int timeoutNum = 0;
        for (DssRepairInfoDto item : repairInfoDtos) {
            //表示正常维修
            if (DateUtil.compare(item.getComplDate(),item.getRepairDate()) >= 0){
                ++normalNum;
            }else {
                ++timeoutNum;
            }
        }
        repairTimelinessDto.setNormalNum(normalNum).setTimeoutRepairNum(timeoutNum);
    }

    /**
     * //设置计量单位及数量
     * @param entity
     */
    private static void setDssunitAndNum(DssInfoDto entity){
        if(StrUtil.isNotBlank(entity.getHaveDssColom())){
            if(entity.getHaveDssColom().equals("DSS_L")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssL())) ;
                entity.setDssUnit(entity.getDssLUnit());
            }else if(entity.getHaveDssColom().equals("DSS_W")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssW()));
                entity.setDssUnit(entity.getDssWUnit());
            }else if(entity.getHaveDssColom().equals("DSS_D")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssD()));
                entity.setDssUnit(entity.getDssDUnit());
            }else if(entity.getHaveDssColom().equals("DSS_N")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssN())) ;
                entity.setDssUnit(entity.getDssNUnit());
            }else if(entity.getHaveDssColom().equals("DSS_A")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssA()));
                entity.setDssUnit(entity.getDssAUnit());
            }else if(entity.getHaveDssColom().equals("DSS_V")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssV())) ;
                entity.setDssUnit(entity.getDssVUnit());
            }else if(entity.getHaveDssColom().equals("DSS_P")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssP())) ;
                entity.setDssUnit("%");
            }else if(entity.getHaveDssColom().equals("DSS_G")){
                entity.setHaveDssColom(StrUtil.toStringOrNull(entity.getDssG())) ;
                entity.setDssUnit("度");
            }
        }
    }

    /**
     * 设置修复状态
     * @param dto
     */
    private static void setRepairName(DssInfoDto dto){
       /* String repairName = "未修复";
        if (StrUtil.isNotBlank(dto.getMtaskCode())){
            repairName = "修复中";
        }
        if (StrUtil.isNotBlank(dto.getMtaskAccptCode())){
            repairName = "已修复";
        }*/

        String repairName = "未修复";
        if (ObjectUtil.isEmpty(dto.getRepairStatus())){
            dto.setRepairStatus(0);
        }
        if (dto.getRepairStatus().equals(1)){
            repairName = "修复中";
        }
        if (dto.getRepairStatus().equals(2)){
            repairName = "已修复";
        }
        if ("QL".equals(dto.getFacilityCat()) && dto.getRepairStatus().equals(3)){
            repairName = "历史修复";
        }
        if ("QL".equals(dto.getFacilityCat()) && dto.getRepairStatus().equals(4)){
            repairName = "新增修复";
        }
        dto.setRepairStatusName(repairName);
    }
}
