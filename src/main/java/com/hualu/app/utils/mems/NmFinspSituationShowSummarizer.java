package com.hualu.app.utils.mems;

import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;

import java.lang.reflect.Method;
import java.util.List;

public class NmFinspSituationShowSummarizer {

    public static NmFinspSituationShow summarize(List<NmFinspSituationShow> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        // 创建汇总对象并设置基础字段
        NmFinspSituationShow summary = new NmFinspSituationShow();
        NmFinspSituationShow first = dataList.get(0);
        copyBaseFields(first, summary);

        // 处理每个月份的字段
        processAllMonthFields(dataList, summary);

        return summary;
    }

    private static void copyBaseFields(NmFinspSituationShow source, NmFinspSituationShow target) {
        target.setYear(source.getYear());
        target.setSecondOrgCode(source.getSecondOrgCode());
        target.setSecondOrgName(source.getSecondOrgName());
        target.setOrgName(source.getOrgName());
        target.setOrgCode(source.getOrgCode());
        target.setRouteName(source.getRouteName());
        target.setRouteCode(source.getRouteCode());
    }

    private static void processAllMonthFields(List<NmFinspSituationShow> dataList, NmFinspSituationShow summary) {
        String[] months = {"jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"};
        for (String month : months) {
            processMonthFields(dataList, summary, month);
        }
    }

    private static void processMonthFields(List<NmFinspSituationShow> dataList, NmFinspSituationShow summary, String month) {
        String[] suffixes = {"Lm", "Bp", "OneSd", "TwoSd", "OneQl", "TwoQl", "Ja"};
        for (String suffix : suffixes) {
            String fieldName = month + suffix;
            String sumValue = sumField(dataList, fieldName);
            setFieldByReflection(summary, fieldName, sumValue);
        }
    }

    private static String sumField(List<NmFinspSituationShow> dataList, String fieldName) {
        int sum = dataList.stream()
                .mapToInt(item -> parseFieldValue(getFieldValueByReflection(item, fieldName)))
                .sum();
        return String.valueOf(sum);
    }

    private static String getFieldValueByReflection(NmFinspSituationShow item, String fieldName) {
        try {
            String getterName = "get" + capitalize(fieldName);
            System.out.println(getterName);
            Method getter = item.getClass().getMethod(getterName);
            return (String) getter.invoke(item);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    private static void setFieldByReflection(NmFinspSituationShow summary, String fieldName, String value) {
        try {
            String setterName = "set" + capitalize(fieldName);
            Method setter = summary.getClass().getMethod(setterName, String.class);
            setter.invoke(summary, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String capitalize(String str) {
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    private static int parseFieldValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return 0;
        }
        String[] parts = value.split("\\|");
        if (parts.length < 2) {
            return 0;
        }
        String data = parts[1].trim();
        if (data.contains("x")) {
            String[] factors = data.split("x");
            if (factors.length != 2) {
                return 0;
            }
            try {
                int a = Integer.parseInt(factors[0].trim());
                int b = Integer.parseInt(factors[1].trim());
                return a * b;
            } catch (NumberFormatException e) {
                return 0;
            }
        } else {
            try {
                return Integer.parseInt(data);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
    }
}
