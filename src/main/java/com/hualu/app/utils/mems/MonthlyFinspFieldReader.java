package com.hualu.app.utils.mems;

import com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow;
import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

public class MonthlyFinspFieldReader {

    //获取当前月份的字段数据（字段名和值的映射）
    public static Map<String, Object> getCurrentMonthFields(NmFinspSituationShow obj) throws Exception {
        int month = LocalDate.now().getMonthValue(); // 当前月份（1-12）
        String prefix = getMonthPrefix(month);
        return getFieldsByPrefix(obj, prefix);
    }

    //根据月份数字获取字段前缀（如：1 -> "jan"）
    public static String getMonthPrefix(int month) {
        String[] monthPrefixes = {"jan", "feb", "mar", "apr", "may", "jun",
                "jul", "aug", "sep", "oct", "nov", "dec"};
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("Invalid month: " + month);
        }
        return monthPrefixes[month - 1];
    }

    //通过反射获取以指定前缀开头的字段值
    private static Map<String, Object> getFieldsByPrefix(NmFinspSituationShow obj, String prefix)
            throws Exception {

        Map<String, Object> result = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field OneSd = clazz.getDeclaredField(prefix + "OneSd");
        Field TwoSd = clazz.getDeclaredField(prefix + "TwoSd");
        OneSd.setAccessible(true);
        TwoSd.setAccessible(true);
        BigDecimal valueOne = new BigDecimal(OneSd.get(obj).toString());
        BigDecimal valueTwo = new BigDecimal(TwoSd.get(obj).toString());
        result.put(prefix + "Sd", valueOne.add(valueTwo));

        Field OneQl = clazz.getDeclaredField(prefix + "OneQl");
        Field TwoQl = clazz.getDeclaredField(prefix + "TwoQl");
        OneQl.setAccessible(true);
        TwoQl.setAccessible(true);
        BigDecimal valueOneQl = new BigDecimal(OneQl.get(obj).toString());
        BigDecimal valueTwoQl = new BigDecimal(TwoQl.get(obj).toString());
        result.put(prefix + "GenQl", valueOneQl.add(valueTwoQl));

        for (Field field : clazz.getDeclaredFields()) {
            String fieldName = field.getName();
            if (fieldName.startsWith(prefix)) {
                field.setAccessible(true); // 允许访问私有字段
                if (fieldName.contains("Sd") || fieldName.contains("Ql") )
                {
                    continue;
                }
                Object value = field.get(obj);
                result.put(fieldName, value);
            }
        }

        return result;
    }

    //获取当前月份字段的中文描述（通过@ApiModelProperty）
    public static Map<String, String> getCurrentMonthFieldDescriptions() throws Exception {
        int month = LocalDate.now().getMonthValue();
        String prefix = getMonthPrefix(month);
        return getFieldDescriptionsByPrefix(NmDinspSituationShow.class, prefix);
    }

    private static Map<String, String> getFieldDescriptionsByPrefix(Class<?> clazz, String prefix)
            throws Exception {

        Map<String, String> result = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.getName().startsWith(prefix)) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                if (annotation != null) {
                    result.put(field.getName(), annotation.value());
                }
            }
        }
        return result;
    }
}
