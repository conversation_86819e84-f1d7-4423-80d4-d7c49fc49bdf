package com.hualu.app.utils.mems;

import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

public class DateTimeUtil {

	public static String Format_Of_Date ="yyyy-MM-dd";
	
	public static String Format_Of_DateTime ="yyyy-MM-dd HH:mm:ss";
	
	/**
	  * <p>功能描述：获取当前年度。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:54:34。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static int getCurYear(){
		
		int curYear =0;
		Calendar calendar = Calendar.getInstance();
		curYear = calendar.get(Calendar.YEAR);
		 
		return curYear;
	}
	
	/**
	  * <p>功能描述：获取当前月份。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:56:03。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static int getCurMonth(){
		int curMonth =0;
		Calendar calendar = Calendar.getInstance();
		curMonth = calendar.get(Calendar.MONTH)+1;
		return curMonth;
	}
	
	/**
	  * <p>功能描述：获取指定日期的月份。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:56:03。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][黄强熙][变更描述]。</p>
	 */
	public static int getMonth(Date date){
		int month =0;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		month = calendar.get(Calendar.MONTH)+1;
		return month;
	}
	
	/**
	  * <p>功能描述：获取当月第几天。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:56:03。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static int getCurDay(){
		int curday =0;
		Calendar calendar = Calendar.getInstance();
		curday = calendar.get(Calendar.DAY_OF_MONTH);
		return curday;
	}
	
	
	public static Date getCurDate(){
		Calendar calendar = Calendar.getInstance();
		Date time = calendar.getTime();
		time.setHours(0);
		time.setMinutes(0);
		time.setSeconds(0);
		return time;
	}
	/**
	  * <p>功能描述：。</p>	
	  * @param date
	  * @param format
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:58:08。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String formatDate(Date date,String format){
		String strDate ="";
		SimpleDateFormat df=new SimpleDateFormat(format);
		strDate = df.format(date);
		return strDate;
	}
	
	/**
	  * <p>功能描述：获取当前年度的第一天。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-17 下午2:07:28。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getFirstDayOfYear(){
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.MONTH, 0);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		
		return formatDate(calendar.getTime(),Format_Of_Date);
	}
	/**
	  * <p>功能描述：获取当前年度的推移。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-17 下午2:07:28。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getFirstDayOfADYear(int doy,int dom,int doOFd){
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.YEAR, doy);
		calendar.set(Calendar.MONTH, dom);
		calendar.set(Calendar.DAY_OF_MONTH, doOFd);
		
		return formatDate(calendar.getTime(),Format_Of_Date);
	}
	public static int getYear(Date date){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		
		return calendar.get(Calendar.YEAR);
	}
	
	/**
	 * 
	  * <p>功能描述：获取当前日期的前一周。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-7-20 上午10:43:46。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getStatetime(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();  
        c.add(Calendar.DATE, - 7);  
        Date monday = c.getTime();
        String preMonday = sdf.format(monday);
        return preMonday;
	}
	
	
	/**
	 * 
	  * <p>功能描述：获取当前日期的前一个月。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-7-22 上午9:32:07。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getMonthTime(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();  
        c.add(Calendar.DATE, - 30);  
        Date monday = c.getTime();
        String preMonday = sdf.format(monday);
        return preMonday;
		
	}
	
	/**
	 * 前N个月
	 * @param n 第几月
	 * @return
	 */
	public static String getCustomizeMonthTime(int n) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();  
        c.add(Calendar.MONTH, - n);  
        Date monday = c.getTime();
        String preMonday = sdf.format(monday);
        return preMonday;
	}
	
	/**
	 * 
	  * <p>功能描述：获取下月的第一天。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-8-23 下午7:35:00。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getNextMonthFirstDay(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar cale = Calendar.getInstance(); 
		cale.add(Calendar.MONTH, 1);
		cale.set(Calendar.DAY_OF_MONTH, 1);
		String firstday = sdf.format(cale.getTime());
		return firstday;
	}
	
	/**
	 * 
	  * <p>功能描述：获取下月的最后一天。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-8-23 下午7:35:15。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getNextMonthLastDay(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar cale = Calendar.getInstance(); 
		int month = cale.get(Calendar.MONTH);
		cale.set(Calendar.MONTH, month+2);
		cale.set(Calendar.DAY_OF_MONTH, 0);
		String lastday = sdf.format(cale.getTime());
		return lastday;
		
	}
	
	
	/**
	 * 
	  * <p>功能描述：获取当前日期。</p>	
	  * @return nowDate 
	  * @since JDK1.6。
	  * <p>创建日期:2015-7-20 上午10:49:09。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getNowDate(){
		Date now = new Date(); 
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
		String nowDate = dateFormat.format(now); 
		return nowDate;
	}
	/**
	 * 
	  * <p>功能描述：根据字符串获取日期。</p>	
	  * @return nowDate 
	  * @since JDK1.6。
	  * <p>创建日期:2015-7-20 上午10:49:09。</p>
	  * <p>更新日期:[日期2019-12-09][黄强熙][if语句判断逻辑错误]。</p>
	 */
	public static Date getNowDate(String newDateStr){
		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		 Date nowdate=null;
		 if(!StringUtil.isNullOrEmpty(newDateStr)){
			 try {
				nowdate=sdf.parse(newDateStr);
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		 }
		return nowdate;
	}
	
	/**
	 * 
	 * <p>功能描述：获取上个月日期。</p>
	 * @return 获取上个月的日期 
	 * @since JDK1.6。
	 * <p>创建日期:2019-12-09 上午10:49:09。</p>
	 * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	*/
	public static Date getLastYearMonth() {
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.add(Calendar.MONTH, -1);
		return cal.getTime();
	}
	
	/**
	 * 
	 * <p>功能描述：将传入的日期的年月得到一个对比值。</p>
	 * @param date 日期
	 * @return 获取一个唯一的年月数值 
	 * @since JDK1.6。
	 * <p>创建日期:2019-12-09 上午10:49:09。</p>
	 * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	*/
	public static int getYearMonth(Date date){
       Calendar calder = Calendar.getInstance();
       calder.setTime(date);//设置时间
       int year = calder.get(Calendar.YEAR);//获取年份
       int month = calder.get(Calendar.MONTH);//获取月份
       //返回年份乘以100加上月份的值，因为月份最多2位数，
       // 所以年份乘以100可以获取一个唯一的年月数值
       return year*100 + month;
	}
	
	/**
	 * 
	 * <p>功能描述：将传入的日期的年月得到第一天或者最后一天。</p>
	 * @param date 日期
	 * @param flag first/last 表示第一天或最后一天
	 * @return 获取日期字符串(yyyy-MM-dd)
	 * @since JDK1.6。
	 * <p>创建日期:2019-12-09 上午10:49:09。</p>
	 * <p>更新日期:[日期YYYY-MM-DD][黄强熙][变更描述]。</p>
	*/
	public static String getFirstOrLastDateStr(Date date, String flag) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int time = 0;
		if(StringUtils.isBlank(flag) || !"last".equals(flag)) {
			time = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
		}else {
			time = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
		}
		cal.set(Calendar.DAY_OF_MONTH, time);
		return formatDate(cal.getTime(), "yyyy-MM-dd");
	}
	
	/**
	 * 
	 * <p>功能描述：将传入的日期的年月得到第一天或者最后一天。</p>
	 * @param date 日期
	 * @param flag first/last 表示第一天或最后一天
	 * @return 获取日期(date)
	 * @since JDK1.6。
	 * <p>创建日期:2019-12-09 上午10:49:09。</p>
	 * <p>更新日期:[日期YYYY-MM-DD][黄强熙][变更描述]。</p>
	*/
	public static Date getFirstOrLastDate(Date date, String flag) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int time = 0;
		if(StringUtils.isBlank(flag) || !"last".equals(flag)) {
			time = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
		}else {
			time = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
		}
		cal.set(Calendar.DAY_OF_MONTH, time);
		return cal.getTime();
	}
	
	public static int getDaysByYear(String yearStr) {
		int year = Integer.parseInt(yearStr);
		if(year % 4 == 0 && year % 100 != 0 || year % 400 == 0){
		   return 366;
		}
		return 365;
	}

	/**
	 * Date 转 LocalDate
	 * @param date
	 * @return
	 */
	public static LocalDate toLocalDate(Date date) {
		return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	}
}
