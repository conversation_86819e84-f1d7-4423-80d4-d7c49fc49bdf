package com.hualu.app.utils.mems;

import com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow;
import com.hualu.app.module.mems.nminsp.entity.NmFinspSituationShow;
import io.swagger.annotations.ApiModelProperty;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MonthlyFieldReader {

    //获取当前月份的字段数据（字段名和值的映射）
    public static Map<String, Object> getCurrentMonthFields(NmDinspSituationShow obj) throws Exception {
        int month = LocalDate.now().getMonthValue(); // 当前月份（1-12）
        String prefix = getMonthPrefix(month);
        return getFieldsByPrefix(obj, prefix);
    }

    //根据月份数字获取字段前缀（如：1 -> "jan"）
    public static String getMonthPrefix(int month) {
        String[] monthPrefixes = {"jan", "feb", "mar", "apr", "may", "jun",
                "jul", "aug", "sep", "oct", "nov", "dec"};
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("Invalid month: " + month);
        }
        return monthPrefixes[month - 1];
    }

    //通过反射获取以指定前缀开头的字段值
    private static Map<String, Object> getFieldsByPrefix(Object obj, String prefix)
            throws Exception {

        Map<String, Object> result = new HashMap<>();
        Class<?> clazz = obj.getClass();

        for (Field field : clazz.getDeclaredFields()) {
            String fieldName = field.getName();
            if (fieldName.startsWith(prefix)) {
                field.setAccessible(true); // 允许访问私有字段
                Object value = field.get(obj);
                result.put(fieldName, value);
            }
        }

        return result;
    }

    //获取当前月份字段的中文描述（通过@ApiModelProperty）
    public static Map<String, String> getCurrentMonthFieldDescriptions() throws Exception {
        int month = LocalDate.now().getMonthValue();
        String prefix = getMonthPrefix(month);
        return getFieldDescriptionsByPrefix(NmDinspSituationShow.class, prefix);
    }

    private static Map<String, String> getFieldDescriptionsByPrefix(Class<?> clazz, String prefix)
            throws Exception {

        Map<String, String> result = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.getName().startsWith(prefix)) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                if (annotation != null) {
                    result.put(field.getName(), annotation.value());
                }
            }
        }
        return result;
    }
}
