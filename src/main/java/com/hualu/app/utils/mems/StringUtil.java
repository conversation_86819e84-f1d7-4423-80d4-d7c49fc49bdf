package com.hualu.app.utils.mems;

import cn.hutool.core.util.StrUtil;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.UUID;

public class StringUtil {
	
	/**
	  * <p>功能描述：判断字符串是否为空字符串。</p>	
	  * @param str
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:53:02。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static boolean isNullOrEmpty(String str){
		if(str == null){
			return true;
		}
		if(str.isEmpty()){
			return true;
		}
		return false;
	}
	
	/**
	  * <p>功能描述：验证各种单的编号。</p>	
	  * @param str
	  * @return
	 * @throws Exception 
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 上午9:53:02。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static boolean checkCode(String aCode,int length) throws Exception{

		if (StrUtil.isBlank(aCode)) {
			return false;
		}
		String[] code = aCode.split("-");
		try {
		    String str=code[code.length-1];
		    Integer.valueOf(str);//把字符串强制转换为数字
		   } catch (Exception e) {
			   throw new Exception("后"+length+"位为序号（0-9）,请勿加入其它字符！");
		   }
		
		if (code[code.length-1].length()!=length){
			throw new Exception("序号长度为"+length+"位,请勿修改序列号长度");
		}
		return true;
	}
	/**
	  * <p>功能描述：编码补位。</p>	
	  * @param source	
	  * @param specialChar
	  * @param len
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 下午4:18:34。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String generateCode(String source,char specialChar,int len){
		
		String str ="";
		
		if(isNullOrEmpty(source)){
			str ="";
		}else{
			source = source.trim();
			if(source.length()>len){
				str = source.substring(0,len);
			}else{
				str = source;
			}
			
			len = len - str.length();
		}
		str = copyChar(specialChar,len)+ str;
		return str;
		
	}
	
	/**
	  * <p>功能描述：字符复制。</p>	
	  * @param specialChar
	  * @param len
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-14 下午4:18:12。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String copyChar(char specialChar,int len){
		
		String str ="";
		for(int i=0;i<len;i++){
			str += specialChar;
		}
		return str;
	}
	
	/**
	 * 
	  * <p>功能描述：为字符串中每个字段添加引号。</p>	
	  * @param str
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-8-18 上午11:54:13。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String addToString(String str){
		String s = "";
		String[] split = str.split(",");
		s += "'"+split[0]+"'";
		for (int i = 1; i < split.length; i++) {
			s += ",'"+split[i]+"'";
		}
		return s;
	}
	
	/**
	 * 
	  * <p>功能描述：将字符数组添加单引号并用逗号隔开拼接成字符串。</p>	
	  * @param str 字符数组 例 [a,b,c] → ‘a’,‘b’,‘c’
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2014-9-19 上午11:43:43。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String arry2Str(String[] str){
		String s = "";
		if(str.length>0){
			s = "'"+str[0]+"'";
			for (int i = 1; i < str.length; i++) {
				s+=",'"+str[i]+"'";
			}
		}
		return s;
	}
	
	/**
	 * 
	  * <p>功能描述：判断字符串是否为数字。</p>	
	  * @param value
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-1-6 下午4:38:30。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static boolean isDouble(String value) {
		try {
			Double.parseDouble(value);
			if (value.contains("."))
				return true;
			return false;
		} catch (NumberFormatException e) {
			return false;
		}
	}
	
	public static String getDisplayStake(Double stake){
		
		String str ="";
		
		if(stake != null){
		DecimalFormat df = new DecimalFormat("#.000");   
		
		String strValue =df.format(stake);
		int iPos =strValue.indexOf(".");
		String s = strValue.substring(0,iPos);
			if(StringUtil.isNullOrEmpty(s)){
				str = "K0+"+ strValue.substring(iPos+1,strValue.length());
			}else{
				str = "K"+ strValue.substring(0,iPos)+ "+"+ strValue.substring(iPos+1,strValue.length());
			}
		}
		return str;
	}
	/**
	 * 
	  * <p>功能描述：四舍五入保留两位小数。</p>	
	  * @param num
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-1-24 下午5:11:54。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static double toDoubleRound(Double num){
		BigDecimal bg = new BigDecimal(num);
		double n = bg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return n;
	}
	/**
	 * 
	  * <p>功能描述：四舍五入保留位小数。</p>	
	  * @param num
	  * @param o位数
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-1-24 下午5:11:54。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static double toDoubleRound(Double num,int o){
		BigDecimal bg = new BigDecimal(num);
		double n = bg.setScale(o, BigDecimal.ROUND_HALF_UP).doubleValue();
		return n;
	}
	
	/**
	 * 
	  * <p>功能描述：生成UUID。</p>	
	  * @return
	  * @since JDK1.6。
	  * <p>创建日期:2015-7-20 下午3:57:45。</p>
	  * <p>更新日期:[日期YYYY-MM-DD][更改人姓名][变更描述]。</p>
	 */
	public static String getUUID() {
		UUID uuid = UUID.randomUUID();
		return uuid.toString();
	}
	
	public static boolean contrastString(String str1,String str2){
		if(str1==null&&str2==null)return true;
		if((isNullOrEmpty(str1)&&!isNullOrEmpty(str2))||(!isNullOrEmpty(str1)&&isNullOrEmpty(str2)))return false;
		if(str1.equals(str2))return true;else return false;
	}
	public static Double str2double(String str){
		if(isNullOrEmpty(str))return 0.0;else return Double.parseDouble(str);
	}
	public static String strdoublefmt(String s){
		if(Double.parseDouble(s)<1&&s.indexOf(".")==0){
			s="0"+s;
		}
		return s;
	}
}
