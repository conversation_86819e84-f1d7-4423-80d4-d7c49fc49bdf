package com.hualu.app.utils.mems;

import lombok.Data;

import java.io.Serializable;

@Data
public class DssTypeDto implements Serializable {

    private String dssTypeName;
    private String dssColumn;
    private String btColumn;
    private String qultDescWay;


    public DssTypeDto() {
    }

    public DssTypeDto(String dssTypeName, String dssColumn, String btColumn) {
        this.dssTypeName = dssTypeName;
        this.dssColumn = dssColumn;
        this.btColumn = btColumn;
    }

    public DssTypeDto(String dssTypeName, String dssColumn, String btColumn, String qultDescWay) {
        this.dssTypeName = dssTypeName;
        this.dssColumn = dssColumn;
        this.btColumn = btColumn;
        this.qultDescWay = qultDescWay;
    }
}
