package com.hualu.app.utils.mems;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.hualu.app.module.mems.finsp.dto.export.CulvertWordDto;
import com.hualu.app.module.mems.finsp.dto.export.DicDto;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Map;

public class H_PcFinspCulvertHelper {

    //涵洞类型
    private static List<String> structType = Lists.newArrayList("盖板涵","箱涵","拱涵","圆管涵（□倒虹吸）","其他（   ）");

    //墩台类型
    private static List<String> partType = Lists.newArrayList("混凝土","钢筋混凝土","浆砌片（块）石（已加固措施：              ）","波纹管","其他（  ）");

    // 涵洞功能
    private static List<String> functionType = Lists.newArrayList("过水","过人","过车","过缆线","其他（      ）");

    // 排查结论
    private static List<String> attr17 = Lists.newArrayList("基本无风险（四级）","低风险（三级）","中风险（二级）","高风险（一级）");

    private static List<String> yesOrNoTypes = Lists.newArrayList("是","否");


    private static List<String> fieldNames = Lists.newArrayList("structType","partType","functionType","attr1","attr3","attr5","attr7"
            ,"attr9","attr11","attr13","attr15","attr17","attr18","attr19");

    public static void initWord(PcProjectExportDto dto){
        CulvertWordDto wordDto = dto.getWordDto();
        for (String fieldName : fieldNames) {
            Object finspValue = ReflectUtil.getFieldValue(dto, fieldName);
            List<String> values = getValues(fieldName);

            List<DicDto> dics = Lists.newArrayList();
            for (String value : values) {
                DicDto row = new DicDto().setName(fieldName);
                if (ObjectUtil.isNotEmpty(finspValue) && value.equals(finspValue)) {
                    row.setValue("☑"+value);
                }else {
                    row.setValue("□"+value);
                }
                dics.add(row);
            }
            ReflectUtil.setFieldValue(wordDto, fieldName, dics);
        }
    }

    private static List<String> getValues(String fileName){

        if (fileName.equals("structType")){
            return structType;
        }
        if (fileName.equals("partType")){
            return partType;
        }
        if (fileName.equals("functionType")){
            return functionType;
        }
        if (fileName.equals("attr17")){
            return attr17;
        }
        return yesOrNoTypes;
    }

    public static void main(String[] args) {

        PcProjectExportDto pcProjectExportDto = new PcProjectExportDto().setAttr1("是");
        H_PcFinspCulvertHelper.initWord(pcProjectExportDto);
        Map bean = BeanUtil.toBean(pcProjectExportDto, Map.class);
        System.out.println(pcProjectExportDto);
    }
}
