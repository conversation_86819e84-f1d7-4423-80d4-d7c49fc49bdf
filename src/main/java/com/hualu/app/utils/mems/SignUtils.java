package com.hualu.app.utils.mems;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tg.dev.api.context.CustomRequestContextHolder;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SignUtils {

    //private static final String SIGN_PATH = "http://127.0.0.1:8080/highwayApp/signature/pdfSignByUserTokenWithout";
    private static final String SIGN_PATH = "http://172.29.0.20:7001/highwayAppSign/signature/pdfSignByUserTokenWithout";
    private static final int CONNECT_TIMEOUT = 10;
    private static final int READ_TIMEOUT = 30;
    private static final int WRITE_TIMEOUT = 30;
    public static final int MAX_RETRY_TIMES = 3;
    private static final long RETRY_INTERVAL = 1000L; // 重试间隔1秒

    // RabbitMQ相关配置
    public static final String SIGN_TASK_QUEUE = "sign.task.queue";    // 签章任务队列
    public static final String SIGN_RETRY_QUEUE = "sign.retry.queue";  // 重试队列
    public static final String SIGN_DEAD_QUEUE = "sign.dead.queue";    // 死信队列
    
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Data
    public static class SignTask {
        private String orderId;
        private String facilityCat;
        private String orderType;
        private String status;
        private String userCode;
        private int retryCount;
        private long timestamp;

        public static SignTask create(String orderId, String facilityCat, 
                                    String orderType, String status, String userCode) {
            SignTask task = new SignTask();
            task.setOrderId(orderId);
            task.setFacilityCat(facilityCat);
            task.setOrderType(orderType);
            task.setStatus(status);
            task.setUserCode(userCode);
            task.setRetryCount(0);
            task.setTimestamp(System.currentTimeMillis());
            return task;
        }
    }

    /**
     * 同步执行单个PDF签章任务（带重试机制）
     */
    @SneakyThrows
    public void syncPdfSign(String orderId, String facilityCat, String orderType, String status) {
        String userCode = CustomRequestContextHolder.getUserCode();
        Exception lastException = null;

        for (int i = 0; i < MAX_RETRY_TIMES; i++) {
            try {
                executePdfSign(orderId, facilityCat, orderType, status, userCode);
                log.info("同步签章成功，订单ID: {}", orderId);
                return;
            } catch (Exception e) {
                lastException = e;
                if (i < MAX_RETRY_TIMES - 1) {
                    log.warn("同步签章重试第{}次失败: {}", i + 1, e.getMessage());
                    Thread.sleep(RETRY_INTERVAL);
                }
            }
        }
        
        String errorMsg = "同步签章失败（重试" + MAX_RETRY_TIMES + "次）: " + 
            (lastException != null ? lastException.getMessage() : "未知错误");
        log.error(errorMsg);
        throw new Exception(errorMsg);
    }

    /**
     * 异步执行批量PDF签章任务（通过MQ队列）
     */
    @Async("taskExecutor")
    public void asyncBatchPdfSign(String orderId, String facilityCat, String orderType, String status, String userCode) {
        // 创建签章任务并发送到队列
        SignTask task = SignTask.create(orderId, facilityCat, orderType, status, userCode);
        rabbitTemplate.convertAndSend(SIGN_TASK_QUEUE, task);
        log.info("批量签章任务已提交到队列，订单ID: {}", orderId);
    }

    /**
     * 处理主任务队列
     */
    @RabbitListener(queues = SIGN_TASK_QUEUE)
    public void handleTaskQueue(SignTask task) {
        try {
            executePdfSign(task.getOrderId(), task.getFacilityCat(), 
                task.getOrderType(), task.getStatus(), task.getUserCode());
            log.info("队列签章成功，订单ID: {}", task.getOrderId());
        } catch (Exception e) {
            log.error("队列签章失败，订单ID: {}, 错误: {}", task.getOrderId(), e.getMessage());
            handleSignFailure(task);
        }
    }

    /**
     * 处理重试队列
     */
    @RabbitListener(queues = SIGN_RETRY_QUEUE)
    public void handleRetryQueue(SignTask task) {
        try {
            executePdfSign(task.getOrderId(), task.getFacilityCat(), 
                task.getOrderType(), task.getStatus(), task.getUserCode());
            log.info("重试签章成功，订单ID: {}, 重试次数: {}", task.getOrderId(), task.getRetryCount());
        } catch (Exception e) {
            log.error("重试签章失败，订单ID: {}, 重试次数: {}, 错误: {}", 
                task.getOrderId(), task.getRetryCount(), e.getMessage());
            handleSignFailure(task);
        }
    }

    /**
     * 处理死信队列
     */
    @RabbitListener(queues = SIGN_DEAD_QUEUE)
    public void handleDeadQueue(SignTask task) {
        log.error("签章任务最终失败，订单ID: {}, 重试次数: {}", task.getOrderId(), task.getRetryCount());
        // TODO: 这里可以添加告警通知、邮件通知等失败处理逻辑
    }

    /**
     * 处理签章失败的情况
     */
    private void handleSignFailure(SignTask task) {
        if (task.getRetryCount() >= MAX_RETRY_TIMES) {
            log.error("签章任务重试次数超限，转入死信队列。订单ID: {}, 重试次数: {}", 
                task.getOrderId(), task.getRetryCount());
            sendToDeadQueue(task);
            return;
        }

        // 增加重试次数
        task.setRetryCount(task.getRetryCount() + 1);
        // 发送到重试队列
        sendToRetryQueue(task);
        log.info("签章任务已加入重试队列，订单ID: {}, 重试次数: {}", 
            task.getOrderId(), task.getRetryCount());
    }

    /**
     * 发送任务到重试队列
     */
    private void sendToRetryQueue(SignTask task) {
        rabbitTemplate.convertAndSend(SIGN_RETRY_QUEUE, task);
    }

    /**
     * 发送任务到死信队列
     */
    private void sendToDeadQueue(SignTask task) {
        rabbitTemplate.convertAndSend(SIGN_DEAD_QUEUE, task);
    }

    /**
     * 执行PDF签章
     */
    private void executePdfSign(String orderId, String facilityCat, String orderType, 
                                     String status, String userCode) throws Exception {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                .build();

        FormBody.Builder formBuilder = new FormBody.Builder()
                .add("orderId", orderId)
                .add("orderType", orderType)
                .add("status", status);
        if (StringUtils.isNotBlank(facilityCat)) {
            formBuilder.add("facilityCat", facilityCat);
        }
        RequestBody body = formBuilder.build();

        Request request = new Request.Builder()
                .url(SIGN_PATH)
                .addHeader("userCode", userCode)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new Exception("请求失败，HTTP状态码: " + response.code());
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new Exception("响应体为空");
            }

            // 获取正确的字符集（优先使用服务器指定的编码）
            MediaType contentType = responseBody.contentType();
            Charset charset = contentType != null ? contentType.charset(StandardCharsets.UTF_8) : StandardCharsets.UTF_8;

            // 3. 直接解析字节流（避免二次转换）
            BufferedSource source = responseBody.source();
            JSONObject jsonObject;
            try {
                jsonObject = JSON.parseObject(source.inputStream(), charset, JSONObject.class);
            } finally {
                source.close(); // 确保流关闭
            }

            // 4. 防御性检查关键字段
            if (!jsonObject.containsKey("code")) {
                throw new Exception("code不存在");
            }

            // 5. 类型安全取值
            Integer code = jsonObject.getInteger("code");
            if (code == null) {
                throw new Exception("code非整形");
            }
            
            if (1 != code) {
                throw new Exception("签章失败: " + jsonObject.getString("message"));
            }
        }
    }
} 