package com.hualu.app.utils.mems;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.tg.dev.api.core.global.exception.BaseException;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class H_DssTypeHelper {

    private static String dssField = "DSS_{}";
    private static String dssUnit = "dss{}Unit";
    private static String BP_STRUCT_COMP_ID = "20230518";
    private static String ORG_ID = "N000001";
    public static Map<String,String> params = Maps.newHashMap();
    //L：长；w:宽；d:深；a:面积；v:体积；n：数量；p:百分比；g：度

    static {
        params.put("l","m");
        params.put("w","m");
        params.put("d","m");
        params.put("n","个/处");
        params.put("a","m2");
        params.put("v","m³");
        params.put("p","%");
        params.put("g","度");
    }


    public static List<DssTypeNew> getDssTypes(List<DssTypeDto> dtos){
        List<DssTypeNew> datas = Lists.newArrayList();
        Field[] fields = ReflectUtil.getFields(DssTypeNew.class);
        dtos.forEach(item->{
            String dssColumn = item.getDssColumn();
            String btColumn = item.getBtColumn();

            if (!dssColumn.contains(btColumn)){
                throw new BaseException("必填项缺失："+ JSONUtil.toJsonStr(item));
            }
            DssTypeNew typeNew = new DssTypeNew();
            BeanUtils.copyProperties(item,typeNew);

            List<String> column = Arrays.asList(dssColumn.split(","));
            //填充定量单位
            for (Field field : fields) {
                String name = field.getName();
                if (name.contains("haveDss")){
                    String haveDss = StrUtil.subAfter(name, "haveDss", true).toLowerCase();
                    if (column.contains(haveDss)){
                        ReflectUtil.setFieldValue(typeNew,name,1);
                        String dssUnitName = StrUtil.format(dssUnit, haveDss.toUpperCase());
                        if (ReflectUtil.hasField(DssTypeNew.class,dssUnitName)){
                            ReflectUtil.setFieldValue(typeNew,dssUnitName,params.get(haveDss));
                        }
                    }
                }
                if (name.contains("haveDssColom")){
                    String dssUnitName = StrUtil.format(dssField, btColumn.toUpperCase());
                    typeNew.setHaveDssColom(dssUnitName);
                    typeNew.setHaveDssUnit(params.get(btColumn));
                }
            }

            //设置其他的定量单位为0
            for (Field field : fields) {
                String name = field.getName();
                if (name.contains("haveDss")){
                    Object fieldValue = ReflectUtil.getFieldValue(typeNew, name);
                    if (fieldValue == null){
                        ReflectUtil.setFieldValue(typeNew,name,0);
                    }
                }
            }
            //组织病害编码
            if (StrUtil.isBlank(typeNew.getDssType())){
                String dssTypeCode = "BPJC-DSS-"+StrUtil.padPre(""+(datas.size()+1),3,"00");
                typeNew.setDssType(dssTypeCode);
            }
            typeNew.setStructCompId(BP_STRUCT_COMP_ID);
            typeNew.setOrgId(ORG_ID);
            typeNew.setDssOrderNumber(datas.size() + 1);
            datas.add(typeNew);
        });

        return datas;
    }

    public static void main(String[] args) {
        List<DssTypeDto> dtos = Lists.newArrayList(new DssTypeDto("崩塌落石等堆积路面","l,w,d,v","v","路面工程"));
        List<DssTypeNew> dssTypes = getDssTypes(dtos);
        System.out.println(111);
    }
}
