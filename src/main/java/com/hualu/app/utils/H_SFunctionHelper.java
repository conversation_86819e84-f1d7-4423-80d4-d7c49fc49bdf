package com.hualu.app.utils;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class H_SFunctionHelper {
    // 缓存实体类的属性名到 SFunction 的映射
    private static final Map<Class<?>, Map<String, SFunction<?, ?>>> CACHE = new HashMap<>();

    /**
     * 动态生成 SFunction
     * @param entityClass 实体类的 Class 对象
     * @param fieldName 属性名
     * @param <T> 实体类类型
     * @param <R> 属性类型
     * @return 生成的 SFunction 实例
     */
    @SuppressWarnings("unchecked")
    public static <T, R> SFunction<T, R> generateSFunction(Class<T> entityClass, String fieldName) {
        // 从缓存中获取 SFunction
        Map<String, SFunction<?, ?>> functionMap = CACHE.computeIfAbsent(entityClass, k -> new HashMap<>());
        SFunction<T, R> function = (SFunction<T, R>) functionMap.get(fieldName);
        if (function != null) {
            return function;
        }

        // 构建 getter 方法名
        String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        try {
            // 获取 getter 方法
            Method getterMethod = entityClass.getMethod(getterMethodName);
            // 构建 SFunction 实例
            function = (SFunction<T, R> & Serializable) entity -> {
                try {
                    return (R) getterMethod.invoke(entity);
                } catch (IllegalAccessException | InvocationTargetException e) {
                    throw new RuntimeException(e);
                }
            };
            // 将生成的 SFunction 存入缓存
            functionMap.put(fieldName, function);
            return function;
        } catch (NoSuchMethodException e) {
            throw new IllegalArgumentException("实体类 " + entityClass.getName() + " 中不存在属性 " + fieldName + " 的 getter 方法", e);
        }
    }

    public static void main(String[] args) {
        SFunction<NmFinsp, Object> finspId = generateSFunction(NmFinsp.class, "finspId");

        NmFinsp nmFinsp = new NmFinsp().setFinspId("111");
        Object apply = finspId.apply(nmFinsp);
        System.out.println(finspId);
    }
}