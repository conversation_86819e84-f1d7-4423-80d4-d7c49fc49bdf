//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.tg.dev.mybatisplus.handler;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.tg.dev.api.context.CustomRequestContextHolder;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.function.Supplier;

public class MyMetaObjectHandler implements MetaObjectHandler {
    private static String CREATE_ORG_ID = "createOrgId";
    private static String CREATE_ORG_CODE = "createOrgCode";
    private static String CREATE_ORG_NAME = "createOrgName";
    private static String UPDATE_ORG_ID = "updateOrgId";
    private static String UPDATE_ORG_CODE = "updateOrgCode";
    private static String UPDATE_ORG_NAME = "updateOrgName";
    private static String CREATE_TIME = "createTime";
    private static String UPDATE_TIME = "updateTime";
    private static String CREATE_USER_ID = "createUserId";
    private static String CREATE_USER_NAME = "createUserName";
    private static String UPDATE_USER_ID = "updateUserId";
    private static String UPDATE_USER_NAME = "updateUserName";
    private static String DEL_FLAG = "delFlag";
    private static String SORT = "sort";
    private static String STATUS = "state";
    private static String TENANT_ID = "tenantId";

    public MyMetaObjectHandler() {
    }

    public void insertFill(MetaObject metaObject) {
        this.fillValue(metaObject, CREATE_TIME, () -> {
            return this.getDateValue(metaObject.getSetterType(CREATE_TIME));
        });
        this.fillValue(metaObject, UPDATE_TIME, () -> {
            return this.getDateValue(metaObject.getSetterType(UPDATE_TIME));
        });
        this.fillValue(metaObject, CREATE_USER_ID, () -> {
            return CustomRequestContextHolder.getUserId();
        });
        this.fillValue(metaObject, CREATE_USER_NAME, () -> {
            return CustomRequestContextHolder.getUserName();
        });
        this.fillValue(metaObject, UPDATE_USER_ID, () -> {
            return CustomRequestContextHolder.getUserId();
        });
        this.fillValue(metaObject, UPDATE_USER_NAME, () -> {
            return CustomRequestContextHolder.getUserName();
        });
        this.fillValue(metaObject, DEL_FLAG, () -> {
            return 0;
        });
        this.fillValue(metaObject, SORT, () -> {
            return 0;
        });
        this.fillValue(metaObject, STATUS, () -> {
            return 1;
        });
        this.fillValue(metaObject, TENANT_ID, () -> {
            return CustomRequestContextHolder.getTenantId();
        });
        this.fillValue(metaObject, CREATE_ORG_ID, () -> {
            return CustomRequestContextHolder.getOrgId();
        });
        this.fillValue(metaObject, CREATE_ORG_NAME, () -> {
            return CustomRequestContextHolder.getOrgName();
        });
        this.fillValue(metaObject, UPDATE_ORG_ID, () -> {
            return CustomRequestContextHolder.getOrgId();
        });
        this.fillValue(metaObject, UPDATE_ORG_NAME, () -> {
            return CustomRequestContextHolder.getOrgName();
        });
    }

    public void updateFill(MetaObject metaObject) {
        this.fillValue(metaObject, "et." + UPDATE_TIME, () -> {
            return this.getDateValue(metaObject.getSetterType("et." + UPDATE_TIME));
        });
        this.fillValue(metaObject, "et." + UPDATE_USER_ID, () -> {
            return CustomRequestContextHolder.getUserId();
        });
        this.fillValue(metaObject, "et." + UPDATE_USER_NAME, () -> {
            return CustomRequestContextHolder.getUserName();
        });
        this.fillValue(metaObject, "et." + UPDATE_ORG_ID, () -> {
            return CustomRequestContextHolder.getOrgId();
        });
        this.fillValue(metaObject, "et." + UPDATE_ORG_NAME, () -> {
            return CustomRequestContextHolder.getOrgName();
        });
        //this.setUpdateFieldValByName(UPDATE_TIME,getDateValue(metaObject.getSetterType("et." + UPDATE_TIME)), metaObject);
    }

    private void fillValue(MetaObject metaObject, String fieldName, Supplier<Object> valueSupplier) {
        if (metaObject.hasGetter(fieldName)) {
            Object sidObj = metaObject.getValue(fieldName);
            if (sidObj == null && metaObject.hasSetter(fieldName)) {
                Class<?> setterType = metaObject.getSetterType(fieldName);
                Object value = this.getIdValue(setterType, valueSupplier.get());
                this.setFieldValByName(fieldName, value, metaObject);
            }

        }
    }

    private Object getDateValue(Class<?> setterType) {
        if (Date.class.equals(setterType)) {
            return new Date();
        } else if (LocalDateTime.class.equals(setterType)) {
            return LocalDateTime.now();
        } else {
            return Long.class.equals(setterType) ? System.currentTimeMillis() : null;
        }
    }

    private Object getIdValue(Class<?> setterType, Object value) {
        if (String.class.equals(setterType)) {
            return Convert.convert(setterType, value);
        } else {
            return Long.class.equals(setterType) ? Convert.convert(setterType, value) : value;
        }
    }
}
