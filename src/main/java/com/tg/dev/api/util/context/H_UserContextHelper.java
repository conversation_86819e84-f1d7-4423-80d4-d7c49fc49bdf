//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.tg.dev.api.util.context;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.tg.dev.api.context.CustomRequestContextHolder;

import java.io.Serializable;

public class H_UserContextHelper implements Serializable {
    public H_UserContextHelper() {
    }

    public static void initUserContext() {
        if (StpUtil.isLogin()) {
            SaSession tokenSession = StpUtil.getTokenSession();
            String tenantId = tokenSession.get("tenantId", "");
            String userId = tokenSession.get("currentUserId", "");
            String userCode = tokenSession.get("currentUserCode", "");
            String userName = tokenSession.get("currentUserName", "");
            String orgId = tokenSession.get("currentOrgId", "");
            String orgEn = tokenSession.get("ORG_EN", "");
            String organizationid = tokenSession.get("currentOrganizationid", "0");
            String orgName = tokenSession.get("currentOrgName", "");
            CustomRequestContextHolder.setUserId(userId);
            CustomRequestContextHolder.setUserCode(userCode);
            CustomRequestContextHolder.setUserName(userName);
            CustomRequestContextHolder.setOrgId(orgId);
            CustomRequestContextHolder.setOrganizationid(organizationid);
            CustomRequestContextHolder.setOrgName(orgName);
            CustomRequestContextHolder.setTenantId(tenantId);
            CustomRequestContextHolder.set("ORG_EN",orgEn);
        }

    }

    public static void setUserId(Long userId) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set("currentUserId", userId);
        CustomRequestContextHolder.setUserId(String.valueOf(userId));
    }

    public static void setUserId(String userId) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set("currentUserId", userId);
        CustomRequestContextHolder.setUserId(userId);
    }

    public static void setUserCode(String userCode) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set("currentUserCode", userCode);
        CustomRequestContextHolder.setUserCode(userCode);
    }

    public static void setUserName(String userName) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set("currentUserName", userName);
        CustomRequestContextHolder.setUserName(userName);
    }

    public static void setOrgId(Long orgId) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set("currentOrgId", orgId);
        CustomRequestContextHolder.setOrgId(orgId);
    }

    public static void setOrgName(String orgName) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set("currentOrgName", orgName);
        CustomRequestContextHolder.setOrgName(orgName);
    }

    public static void clear() {
        CustomRequestContextHolder.remove();
    }

    public static void set(String key, Object value) {
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set(key, value);
        CustomRequestContextHolder.set(key, value);
    }

    public static Object get(String key) {
        SaSession tokenSession = StpUtil.getTokenSession();
        return tokenSession.get(key);
    }
}
