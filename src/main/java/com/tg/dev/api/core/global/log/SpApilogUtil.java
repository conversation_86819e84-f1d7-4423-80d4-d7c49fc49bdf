package com.tg.dev.api.core.global.log;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tg.dev.api.annotation.ApiRegister;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomHttpRequestLocal;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.context.constant.CommonConstants;
import com.tg.dev.api.core.global.GlobalException;
import com.tg.dev.api.core.global.request.HttpRequestWrapperUtil;
import com.tg.dev.api.core.msg.ApiResult;
import com.tg.dev.api.util.sp.LogUtil;
import com.tg.dev.api.util.sp.WebNbUtil;
import jdk.nashorn.internal.ir.debug.ObjectSizeCalculator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 工具类：api请求记录表
 * <AUTHOR>
 */
public class SpApilogUtil {

	static final String apilog_obj_save_key = "apilog_obj_save_key";
	static final String apilog_obj_save_id_key = "apilog_obj_save_id_key";
	static final String content_type = "application/json";
	static final String USER_ID = "-1";

	/**
	 * 请求开始时调用，开始计时，调用默认的带有参数的startRequest方法
	 */
	public static void startRequest() {
		startRequest(null, null);
	}

	/**
	 * 请求开始时调用，开始计时
	 *
	 * @param apiOperation API操作描述
	 * @param apiRegister  API注册注解对象
	 */
	public static void startRequest(String apiOperation, ApiRegister apiRegister) {
		if (!isWeb()) {
			return;
		}

		HttpServletRequest request = getRequest();
		SpApilog a = new SpApilog();
		a.setReqId(getCurrReqId());        // 随机一个请求id
		a.setReqIp(WebNbUtil.getIP(request));        // 请求ip
		a.setReqApi(request.getRequestURI());        // 请求接口
		a.setReqOperation(apiOperation);  // 请求接口描述
		a.setReqParame(getReqParam(request));
		a.setTenantId(CustomRequestContextHolder.getTenantId());

		if (apiRegister != null) {
			a.setReqOperation(StringUtils.isBlank(apiRegister.title())? apiOperation : apiRegister.title());  // 请求接口描述
			a.setReqBus(apiRegister.businessType().name());
			a.setAudit(apiRegister.isAudit());
		}

		if (StpUtil.isLogin()) {
			a.setReqToken(StpUtil.getTokenValue());         // 请求token
		}
		a.setReqHeader(JSON.toJSONString(WebNbUtil.getHeaderMap(request)));         // 请求header
		a.setReqType(request.getMethod());         // 请求类型
		//a.setAdminId(StpUtil.getLoginId(USER_ID));    // 本次请求admin_id
		a.setStartTime(new Date());                // 请求开始时间
		request.setAttribute(apilog_obj_save_key, a);
		LogUtil.infoReq(a.getReqId(), a.getReqIp(), CustomRequestContextHolder.getUserId(), a.getReqApi(), a.getReqParame());
		CustomHttpRequestLocal.setRequestValue(GlobalException.REQ_INFO, a);
	}

	/**
	 * 请求结束时调用，结束计时
	 *
	 * @param aj API结果对象
	 */
	public static void endRequest(ApiResult aj) {
		if (!isWeb()) {
			return;
		}

		HttpServletRequest request = getRequest();
		SpApilog a = (SpApilog) request.getAttribute(apilog_obj_save_key);
		if (a == null) {
			startRequest();
			a = (SpApilog) request.getAttribute(apilog_obj_save_key);
		}

		try {
			handleRequestEndData(a, aj);
			LogUtil.info(a);
			saveApilog(a);
		} catch (Exception e) {
			LogUtil.errorMsg("请求结束处理异常", e);
		}
	}

	/**
	 * 处理请求结束时的数据，包括设置用户ID、响应码、消息、错误信息、响应数据等
	 *
	 * @param a  SpApilog对象
	 * @param aj API结果对象
	 */
	private static void handleRequestEndData(SpApilog a, ApiResult aj) {
		if (StpUtil.isLogin()) {
			a.setUserId(StpUtil.getTokenSession().get(CommonConstants.CONTEXT_KEY_USER_ID, USER_ID));
		} else {
			a.setUserId(USER_ID);
		}

		a.setResCode(aj.getCode());    // res 状态码
		a.setResMsg(aj.getMsg());      // res 描述信息
		a.setResErrorMsg(aj.getExceptionMsg());

		handleResponseData(a, aj);

		a.setEndTime(new Date());         // 请求结束时间
		a.setCostTime((int) (a.getEndTime().getTime() - a.getStartTime().getTime()));   // 请求消耗时长，单位ms
	}

	/**
	 * 处理响应数据，根据不同类型设置响应字符串
	 *
	 * @param a  SpApilog对象
	 * @param aj API结果对象
	 */
	private static void handleResponseData(SpApilog a, ApiResult aj) {
		// 获取 API 结果中的数据
		Object data = aj.getData();
		if (data != null) {
			// 如果数据是 List 类型
			if (data instanceof List<?>) {
				handleListResponseData(a, (List<?>) data);
			}
			// 如果数据是 HashMap 类型
			else if (data instanceof HashMap<?, ?>) {
				handleHashMapResponseData(a, (HashMap<?, ?>) data);
			}
			// 如果数据是其他类型
			else {
				handleOtherResponseData(a, data);
			}
		}
	}

	/**
	 * 处理 API 结果中数据为 List 类型的情况，设置响应字符串为数据条数和描述
	 *
	 * @param a       SpApilog 对象，用于设置响应相关信息
	 * @param dataList 包含数据的 List 对象
	 */
	private static void handleListResponseData(SpApilog a, List<?> dataList) {
		a.setResString(dataList.size() + "条数据（集合数据）");
	}

	/**
	 * 处理 API 结果中数据为 HashMap 类型的情况，根据数据大小决定是否记录详细数据
	 *
	 * @param a      SpApilog 对象，用于设置响应相关信息
	 * @param dataMap 包含数据的 HashMap 对象
	 */
	private static void handleHashMapResponseData(SpApilog a, HashMap<?, ?> dataMap) {
		// 获取 HashMap 对象的大小

		if (isOutOfRange(dataMap,1024)) {
			// 如果数据大小超过 10KB，设置响应字符串为提示信息
			a.setResString("HashMap数据量超过1KB，数据不展示详情");
		} else {
			try {
				// 如果数据大小不超过 10KB，将 HashMap 转换为字符串并设置为响应字符串
				a.setResString(new ObjectMapper().writeValueAsString(dataMap));
			} catch (IOException e) {
				// 捕获转换过程中的异常并记录错误信息
				LogUtil.errorMsg("处理HashMap响应数据异常", e);
			}
		}
	}

	private static boolean isOutOfRange(Object data, long maxSize){
		long objectSize = ObjectSizeCalculator.getObjectSize(data);
		return objectSize > maxSize;
	}

	/**
	 * 处理 API 结果中数据为其他类型的情况，根据数据类型的简单名称决定是否记录详细数据
	 *
	 * @param a    SpApilog 对象，用于设置响应相关信息
	 * @param data 其他类型的数据对象
	 */
	private static void handleOtherResponseData(SpApilog a, Object data) {
		// 获取数据对象的简单类名
		String simpleName = data.getClass().getSimpleName();
		if (!"soulPage".equalsIgnoreCase(simpleName) && !simpleName.contains("Page") && !isOutOfRange(data,1024)) {
			try {
				// 如果类名不是特定的 "soulPage" 或包含 "Page"，将数据转换为字符串并设置为响应字符串
				a.setResString(new ObjectMapper().writeValueAsString(data));
			} catch (IOException e) {
				// 捕获转换过程中的异常并记录错误信息
				LogUtil.errorMsg("处理其他类型响应数据异常", e);
			}
		} else {
			// 如果类名是特定的 "soulPage" 或包含 "Page"，设置响应字符串为提示信息
			a.setResString("page分页查询，数据暂不展示详情");
		}
	}

	/**
	 * 当前是否为web环境
	 *
	 * @return 是否为web环境
	 */
	public static boolean isWeb() {
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();// 大善人SpringMVC提供的封装
		return servletRequestAttributes != null;
	}

	/**
	 * 获取当前的HttpServletRequest对象
	 *
	 * @return HttpServletRequest对象
	 */
	private static HttpServletRequest getRequest() {
		ServletRequestAttributes cast = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (cast == null) {
			throw new IllegalStateException("RequestAttributes is null");
		}
		return cast.getRequest();
	}

	/**
	 * 设置请求参数
	 *
	 * @param request HttpServletRequest对象
	 * @return 请求参数字符串
	 */
	private static String getReqParam(HttpServletRequest request) {
		String contentType = request.getContentType();
		if (StringUtils.isNotBlank(contentType) && contentType.toLowerCase().indexOf(content_type) != -1) {
			try {
				return HttpRequestWrapperUtil.getBodyString(request);
			} catch (IOException e) {
				LogUtil.errorMsg("获取请求参数异常", e);
			}
		} else {
			return JSON.toJSONString(WebNbUtil.getParamsMap2(request));    // 请求参数
		}
		return "";
	}

	/**
	 * 获取当前请求的req_id
	 *
	 * @return 请求的req_id
	 */
	public static String getCurrReqId() {
		HttpServletRequest request = getRequest();
		String req_id = (String) request.getAttribute(apilog_obj_save_id_key);
		if (req_id == null) {
			req_id = IdUtil.simpleUUID();
			request.setAttribute(apilog_obj_save_id_key, req_id);
		}
		return req_id;
	}

	/**
	 * 操作日志保存
	 *
	 * @param apilog SpApilog对象
	 */
	public static void saveApilog(SpApilog apilog) {
		try {
			ApplicationContext context = CustomApplicationContextHolder.getApplicationContext();
			Environment env = CustomApplicationContextHolder.getBean(Environment.class);
			// 是否保存业务日志
			Boolean saveLog = env.getProperty("platform.saveLog", Boolean.class);

			String applicationName = env.getProperty("spring.application.name", "");
			apilog.setApplicationName(applicationName);
			// 检查 ApiLogDao Bean 是否存在
			boolean hasApiLogDao = context.containsBeanDefinition(ApiLogDao.class.getName());
			if (hasApiLogDao && saveLog != null && saveLog) {
				ApiLogDao bean = context.getBean(ApiLogDao.class);
				bean.saveDb(apilog);
			}
		} catch (NoSuchBeanDefinitionException e) {
			LogUtil.errorMsg("保存日志时获取bean异常", e);
		}
	}
}
