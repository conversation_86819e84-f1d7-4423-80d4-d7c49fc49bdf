//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.tg.dev.redis.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@ConditionalOnProperty(
    name = {"platform.cache.redis.enabled"},
    havingValue = "true",
    matchIfMissing = false
)
@Configuration
public class RedissonConfig {
    @Resource
    RedisProperties redisProperties;

    public RedissonConfig() {
    }

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        String redisUrl = String.format("redis://%s:%s", this.redisProperties.getHost() + "", this.redisProperties.getPort() + "");
        config.useSingleServer().setAddress(redisUrl).setPassword(this.redisProperties.getPassword());
        config.useSingleServer().setDatabase(this.redisProperties.getDatabase());
        config.useSingleServer().setConnectionMinimumIdleSize(2);
        config.useSingleServer().setTimeout(10000);
        return Redisson.create(config);
    }
}
