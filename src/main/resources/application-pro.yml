server:
  servlet:
    context-path: /hwaymems
spring:
  datasource:
    druid:
      # 1. 调大闲置连接保留时间（单位：秒，默认 60 秒，可适当调大，如 300 秒）
      max-evictable-idle-time-millis: 300000
      # 2. 开启空闲连接校验，及时剔除失效连接（避免堆积无效连接）
      test-while-idle: true
      # 3. 降低校验频率（单位：秒，默认 60 秒，可设为 120 秒，减少资源消耗）
      time-between-eviction-runs-millis: 120000
      # 4. 替换保活方式（低活跃场景推荐，用 SELECT 1 代替 ping，主动刷新连接活跃时间）
      validation-query: SELECT 1
      # 5. 关闭 ping 方式（避免因 ping 不更新连接活跃时间，导致被误判闲置）
      # druid.mysql.usePingMethod=false（JVM 启动参数或 Druid 配置属性）
      max-active: 50
      initial-size: 5
      min-idle: 5
    dynamic:
      datasource:
        master:
          username: <PERSON><PERSON>(iItuoPCRsTrT9MfyRENzYw==)
          password: ENC(fdwPsW5HISZSDaaw7ITm5kvX+MhNT9+H)
          url: ENC(0cCtmxIrhP0NfCePckJdAxEz6ob5h0FwCrG0Jywv7OYh/DFa+43Llc417T+7PSfNpc05O7sTFrk=)
          type: com.alibaba.druid.pool.DruidDataSource
        hsmsDs:
          username: ENC(Y+2NM9R6q+FsF7RfMP+JXQ==)
          password: ENC(4zX7VnzU/HJjEyd62GSpeGar8jtZNLvS)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(0cCtmxIrhP0NfCePckJdAxEz6ob5h0FwCrG0Jywv7OYh/DFa+43Llc417T+7PSfNpc05O7sTFrk=)
        bctcmsDs:
          username: ENC(N2Yxg3//1Pfc+uMODaPuJsSTW2/wwKGq)
          password: ENC(guQaALVLyh/9p1n5sEvpVP5hJEQTYr9J)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(0cCtmxIrhP0NfCePckJdAxEz6ob5h0FwCrG0Jywv7OYh/DFa+43Llc417T+7PSfNpc05O7sTFrk=)
        gdgsDs:
          username: ENC(WCAIqXZjBUHdG9UnEC+FAA==)
          password: ENC(YSo+Jfwbq3ROW4WgvSaUFAgFB0w9D7nI)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(0cCtmxIrhP0NfCePckJdAxEz6ob5h0FwCrG0Jywv7OYh/DFa+43Llc417T+7PSfNpc05O7sTFrk=)
        mtmsDs:
          username: ENC(WvxUZETLwpevDiRe0BrV9A==)
          password: ENC(RlU5fu4z93IvTF5gcL3yu/Mu6T3zO9xM)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(0cCtmxIrhP0NfCePckJdAxEz6ob5h0FwCrG0Jywv7OYh/DFa+43Llc417T+7PSfNpc05O7sTFrk=)
        bpsDs:
          username: ENC(uteI5nyeHV7sRkttIHw+zQ==)
          password: ENC(5fY8Uw5RBvqbxo0hNrx2GagJ3haLX4jj)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(0cCtmxIrhP0NfCePckJdAxEz6ob5h0FwCrG0Jywv7OYh/DFa+43Llc417T+7PSfNpc05O7sTFrk=)
  redis:
    port: 6379
    host: ************
    password: ENC(r+J84BOhP1hK+Kdz315tOmefBUl7ujlL)
  rabbitmq:
    host: ************
    port: 5672
    username: ENC(l9ac1070o9SBh7X7RcDN1w==)
    password: ENC(XXgwvlwNM4loxKPgDFzTOBmlcQGbRQTV)
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: auto
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 5
          max-interval: 10000
          multiplier: 2.0
  boot:
    admin:
      client:
        url: http://************:9000/admin
        username: admin
        password: Admin!@#2025
        instance:
          prefer-ip: true
platform:
  minio:
    endpoint: http://************:9001
    accesskey: ENC(RBi7tdzGy1GX3BlQtMNKAAXYjRnbSRuJ)
    secretKey: ENC(q7h18WVZ2eko8C8BJPQ33xFDqSxLENYn)
    bucketName: home # minio集合
    # 后台图片预览地址，用于新版日常巡查路面word导出
    image-preview-prefix-url: http://***********:7001/hwayapp/baseFileEntity/previewImage/
    white-list: jpg,png,pdf,docx,doc,xls,xlsx,jpeg

hualu:
  cas:
    # 动态获取请求地址
    dynamic-server: true
    #是否启用单点登录配置
    casEnable: true
    # 前端vue链接地址
    callBackPagePrefixUrl: /memsui
    # cas 登录地址
    serverPrefixUrl: http://***********:7001/cas
    # 后端java项目地址
    clientPrefixUrl: /hwaymems
autoInspect:
  enable: true
  domain: cgcloud.qxwz.com
  version: v2
  sik: aacea4846576797bb2089198d6925734
  sis: d5ed872c5d45535b462b359c91bb69aeabff46af2c15f70254a7f920f4afae07
