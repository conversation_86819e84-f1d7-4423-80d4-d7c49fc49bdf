spring:
  profiles:
    active: dev
  application:
    name: highway-mems
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-request-size: 500MB
      max-file-size: 500MB
  boot:
    admin:
      client:
        url: http://localhost:9000/admin
        username: admin
        password: Admin!@#2025
        instance:
          prefer-ip: true
server:
  port: 8182
  servlet:
    context-path:
  tomcat:
    connection-timeout: 300000  # 5分钟连接超时
    max-http-post-size: 500MB   # 最大POST请求大小
  connection-timeout: 300000    # 5分钟连接超时
mybatis-plus:
  global-config:
    db-config:
      id-type: id_worker_str
      field-strategy: not_empty
      table-underline: true
      db-type: oracle
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    datacenter-id: 0
    worker-id: 1  #datacenter-id和worker-id为解决分布式ID生成重复
    #sql-parser-cache: true
  #  mapper-locations: classpath:mapper/xml/*.xml
  mapper-locations: classpath:**/mapper/*.xml
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.hualu.app.**.entity
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    jdbc-type-for-null: 'null'#oracle 的属性字段为空时会报错，需要设置当前配置
    default-fetch-size: 1000
#是否验证密码
platform:
  shiro:
    verifyPwd: true
  pageType : miniui
  security:
    csrf: false
  cache:
    redis:
      enabled: true  #需要使用redis缓存时，必须启动该配置
#异步事务配置
async:
  enable: true
  corePoolSize: 2
  maxPoolSize: 10
  queueCapacity: 2

resource:
  # 临时文件会定时清理
  path: D:\\upload\\filetemp\\
  url:

fileReposity: D:\\upload\\temp\\

#配置文件加密处理
#jasypt:
#  encryptor:
#    password: yhpt
# application.yml (Client)
management:
  endpoints:
    web:
      exposure:
        include: "*"  # 暴露所有端点
      base-path: /actuator
  endpoint:
    health:
      show-details: always  # 显示详细健康信息
sa-token:
  token-style: random-64
  activity-timeout: 3600
  is-read-cookie: false

jasypt:
  encryptor:
    password: **********