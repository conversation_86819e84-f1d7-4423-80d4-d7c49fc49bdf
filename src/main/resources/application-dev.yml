spring:
  datasource:
    druid:
      stat-view-servlet:
        loginUsername: admin
        loginPassword: 123456
        enabled: false
    dynamic:
      datasource:
        master:
          username: <PERSON><PERSON>(iItuoPCRsTrT9MfyRENzYw==)
          password: ENC(fdwPsW5HISZSDaaw7ITm5kvX+MhNT9+H)
          url: ENC(ifkWHas6ax9+cLVEKocOeIwwoCWcv1asRClJBgVeIZCaMwfN0whkUN+t4cbf421Gmig0BO856uw=)
          type: com.alibaba.druid.pool.DruidDataSource
        hsmsDs:
          username: E<PERSON>(Y+2NM9R6q+FsF7RfMP+JXQ==)
          password: ENC(4zX7VnzU/HJjEyd62GSpeGar8jtZNLvS)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(ifkWHas6ax9+cLVEKocOeIwwoCWcv1asRClJBgVeIZCaMwfN0whkUN+t4cbf421Gmig0BO856uw=)
        bctcmsDs:
          username: <PERSON><PERSON>(N2Yxg3//1Pfc+uMODaPuJsSTW2/wwKGq)
          password: ENC(guQaALVLyh/9p1n5sEvpVP5hJEQTYr9J)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(ifkWHas6ax9+cLVEKocOeIwwoCWcv1asRClJBgVeIZCaMwfN0whkUN+t4cbf421Gmig0BO856uw=)
        gdgsDs:
          username: ENC(WCAIqXZjBUHdG9UnEC+FAA==)
          password: ENC(YSo+Jfwbq3ROW4WgvSaUFAgFB0w9D7nI)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(ifkWHas6ax9+cLVEKocOeIwwoCWcv1asRClJBgVeIZCaMwfN0whkUN+t4cbf421Gmig0BO856uw=)
        mtmsDs:
          username: ENC(WvxUZETLwpevDiRe0BrV9A==)
          password: ENC(RlU5fu4z93IvTF5gcL3yu/Mu6T3zO9xM)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(ifkWHas6ax9+cLVEKocOeIwwoCWcv1asRClJBgVeIZCaMwfN0whkUN+t4cbf421Gmig0BO856uw=)
        bpsDs:
          username: ENC(uteI5nyeHV7sRkttIHw+zQ==)
          password: ENC(5fY8Uw5RBvqbxo0hNrx2GagJ3haLX4jj)
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ENC(ifkWHas6ax9+cLVEKocOeIwwoCWcv1asRClJBgVeIZCaMwfN0whkUN+t4cbf421Gmig0BO856uw=)
  redis:
    port: 6379
    host: ************
    password: ENC(pp0092Nvo7xlIYuHdaWSJb/Sc07NdzEE)
    lettuce:
      pool:
        min-idle: 2
        max-idle: 4
  rabbitmq:
    host: ************
    port: 5672
    username: ENC(l9ac1070o9SBh7X7RcDN1w==)
    password: ENC(XXgwvlwNM4loxKPgDFzTOBmlcQGbRQTV)
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: auto
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 5
          max-interval: 10000
          multiplier: 2.0
  mvc:
    async:
      request-timeout: 360000
platform:
  minio:
    endpoint: http://127.0.0.1:9001
    accesskey: ENC(RBi7tdzGy1GX3BlQtMNKAAXYjRnbSRuJ)
    secretKey: ENC(sWhro32hDNCDsFqVKF2wbWyD3tB00HQ8)
    bucketName: home # minio集合
    # 后台图片预览地址，用于新版日常巡查路面word导出
    image-preview-prefix-url: http://***********:7001/hwayapp/baseFileEntity/previewImage/
    white-list: jpg,png,pdf,docx,doc,xls,xlsx
hualu:
  cas:
    #是否启用单点登录配置
    casEnable: true
    # 前端vue链接地址
    callBackPagePrefixUrl: http://localhost:3001/
    # cas 登录地址
    serverPrefixUrl: http://***********:7001/cas
    # 后端java项目地址
    clientPrefixUrl: http://localhost:8182
autoInspect:
  enable: true
  domain: cgcloud.qxwz.com
  version: v2
  sik: aacea4846576797bb2089198d6925734
  sis: d5ed872c5d45535b462b359c91bb69aeabff46af2c15f70254a7f920f4afae07
server:
  servlet:
    session:
      timeout: 360