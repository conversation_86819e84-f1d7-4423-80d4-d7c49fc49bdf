package com.hualu.app.module.mongo.utils;

import cn.hutool.json.JSONUtil;
import com.hualu.app.module.minio.MinioService;
import com.hualu.app.module.mongo.constant.C_Constant;
import com.hualu.app.module.mongo.dto.MgFileDto;
import com.tg.dev.api.context.CustomApplicationContextHolder;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;

import java.io.File;

public class H_MongoHelperTest extends BaseApplicationTest {

    @Test
    public void uploadFile() {

        /*CustomRequestContextHolder.set(C_Constant.DCCODE,"10001");
        File file = new File("D://filemove");
        File[] files = file.listFiles();
        for (File f : files) {
            MgFileDto mgFileDto = H_MongoHelper.uploadFile(f);
            System.out.println(JSONUtil.toJsonStr(mgFileDto));
        }*/

        MinioService minioService = CustomApplicationContextHolder.getBean(MinioService.class);
        String uploadPath = minioService.upload("home", null, new File("D:\\0d27fc81-110e-4cd7-a5fa-72e341753b0c_正面照.png"));
        System.out.println(uploadPath);
    }
}