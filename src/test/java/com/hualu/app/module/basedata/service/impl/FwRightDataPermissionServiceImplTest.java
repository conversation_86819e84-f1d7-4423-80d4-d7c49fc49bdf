package com.hualu.app.module.basedata.service.impl;

import com.eos.workflow.api.BPSServiceClientFactory;
import com.hualu.app.module.basedata.dto.BaseRouteDto;
import com.hualu.app.module.basedata.service.FwRightDataPermissionService;
import com.hualu.app.utils.H_DataAuthHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;

public class FwRightDataPermissionServiceImplTest extends BaseApplicationTest {


    @Autowired
    FwRightDataPermissionService permissionService;

    @Before
    public void init(){
        CustomRequestContextHolder.setUserCode("T-linpr");
        CustomRequestContextHolder.setUserName("测试人员");
        CustomRequestContextHolder.setOrgId("N000025");
        BPSServiceClientFactory.getLoginManager().setCurrentUser(CustomRequestContextHolder.getUserCode(), CustomRequestContextHolder.getUserName());
    }

    @Test
    public void getRouteRpStake() {
        BaseRouteDto rpStake = permissionService.getRouteRpStake("G4511", "1", 28.00);
        System.out.println(rpStake);
    }

    @Test
    public void getRouteCode(){
        Map<String, Set<String>> stringSetMap = H_DataAuthHelper.selectOrgGroupByRouteCode("lq-gs-zhangy");
        System.out.println(stringSetMap);
    }
}