package com.hualu.app.module.basedata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.basedata.entity.HsmsSlopeInfo;
import com.hualu.app.utils.H_StakeHelper;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class HsmsSlopeInfoServiceImplTest extends BaseApplicationTest {


    @Autowired
    HsmsSlopeInfoServiceImpl slopeInfoService;

    @Before
    public void init(){
        //CustomRequestContextHolder.setUserCode("sjzx-tangtg");
    }

    @Test
    public void selectStrcut(){
        Object o = slopeInfoService.selectStructPage();
        System.out.println(o);
    }

    @Test
    public void selectList(){

        LambdaQueryWrapper<HsmsSlopeInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(HsmsSlopeInfo::getDesignEndStake).isNotNull(HsmsSlopeInfo::getDesignStartStake);
        queryWrapper.isNotNull(HsmsSlopeInfo::getDesignStartStrstake).isNull(HsmsSlopeInfo::getDesignEndStrstake);
        List<HsmsSlopeInfo> infos = slopeInfoService.list(queryWrapper);

        infos.forEach(item->{
            String cnStake = H_StakeHelper.convertCnStake(item.getDesignEndStake().toString());
            System.out.println(item.getSlopeLength() + "=>" + item.getDesignStartStrstake() + "   ~    "+cnStake);
        });
    }
}