package com.hualu.app.module.basedata.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.basedata.entity.YhCfgInfo;
import com.hualu.app.module.basedata.service.YhCfgInfoService;
import com.hualu.app.utils.H_CoordinateTransformUtil;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class YhCfgInfoServiceImplTest extends BaseApplicationTest {


    @Autowired
    YhCfgInfoService yhCfgInfoService;

    @Test
    public void initData(){

        String url = "https://gjdt.private.gdcg.cn/sde_geoserver/dugis/wfs";

        Map<String,Object> params = new HashMap<String,Object>();
        params.put("outputFormat","application/json");
        params.put("request","GetFeature");
        params.put("service","WFS");
        params.put("version","1.0.0");
        params.put("typeName","dugis10:dugis10_sflddx");
        //params.put("maxFeatures","20");
        //params.put("cql_filter","RoadNo in ('S0059440010')");

        String body = HttpRequest.post(url).header("Authorization", "Basic Z2x5aDpjYWlqaWExMjM=").form(params).execute().body();
        JSON parse = JSONUtil.parse(body);
        JSONArray features = parse.getByPath("features", JSONArray.class);

        List<YhCfgInfo> infoList = Lists.newArrayList();
        for (Object feature : features) {
            JSON featureObj = (JSON) feature;
            JSONArray coordinates = featureObj.getByPath("geometry.coordinates",JSONArray.class);
            JSON properties = featureObj.getByPath("properties", JSON.class);
            YhCfgInfo yhCfgInfo = BeanUtil.toBeanIgnoreCase(properties, YhCfgInfo.class, true);
            yhCfgInfo.setRoadPath(JSONUtil.toJsonStr(gcj02tobd09(coordinates)));
            infoList.add(yhCfgInfo);
        }
        yhCfgInfoService.remove(new LambdaQueryWrapper<>());
        yhCfgInfoService.saveBatch(infoList);
    }

    /**
     * 02转换成09坐标
     * @param roadPath
     * @return
     */
    private List<Object> gcj02tobd09(JSONArray roadPath){
        List<Object> result = Lists.newArrayList();
        roadPath.forEach(item->{
            JSONArray row = (JSONArray) item;
            List<Object> arr = Lists.newArrayList();
            row.forEach(k->{
                JSONArray kArray = (JSONArray) k;
                double[] doubles = H_CoordinateTransformUtil.gcj02tobd09(Double.valueOf(kArray.get(0).toString()),
                        Double.valueOf(kArray.get(1).toString()));
                arr.add(doubles);
            });
            result.add(arr);
        });
        return result;
    }


    @Test
    public void geoSpatial(){
        double minX = 116.0;
        double minY = 39.0;
        double maxX = 117.0;
        double maxY = 40.0;

        // 创建GeometryFactory用于生成几何对象
        GeometryFactory geometryFactory = new GeometryFactory();

        // 创建矩形范围的Polygon（使用经纬度作为二维坐标）
        Coordinate[] coords = new Coordinate[] {
                new Coordinate(minX, minY),
                new Coordinate(maxX, minY),
                new Coordinate(maxX, maxY),
                new Coordinate(minX, maxY),
                new Coordinate(minX, minY) // 闭合多边形
        };
        Polygon rectangle = geometryFactory.createPolygon(coords);

        // 测试点
        List<Point> points = new ArrayList<>();
        points.add(geometryFactory.createPoint(new Coordinate(116.5, 39.5))); // 在矩形内
        points.add(geometryFactory.createPoint(new Coordinate(116.5, 39.1))); // 在矩形外

        // 筛选在矩形内的点
        for (Point point : points) {
            if (rectangle.contains(point)) {
                System.out.println("点在矩形内: " + point.getCoordinate());
            } else {
                System.out.println("点在矩形外: " + point.getCoordinate());
            }
        }
    }
}