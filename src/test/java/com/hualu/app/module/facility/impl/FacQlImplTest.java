package com.hualu.app.module.facility.impl;

import com.google.common.collect.Lists;
import com.hualu.app.module.mems.dss.dto.DssRepairTimelinessDto;
import com.hualu.app.module.mems.dss.service.DssInfoService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class FacQlImplTest extends BaseApplicationTest {

    @Autowired
    FacBpImpl service;

    @Autowired
    DssInfoService dssInfoService;

    @Test
    public void getFinspStat() {

        //service.getFinspStat(Lists.newArrayList("001","002","098","099","212","213"),"2024-06");

        List<String> routeCodes =  Lists.newArrayList("001","002","098","099","212","213");
        String month = "2024-06";
        service.getFinspStat(routeCodes,month);
        /*Map<String, IFinspStatBase> beansOfType = CustomApplicationContextHolder.getBeansOfType(IFinspStatBase.class);
        List<CompletableFuture<FinspStatDto>> futureList = new ArrayList<>();
        beansOfType.values().forEach(bean->{
            CompletableFuture<FinspStatDto> future = CompletableFuture.supplyAsync(() -> {
                FinspStatDto finspStat = bean.getFinspStat(routeCodes, month);
                return finspStat;
            });
            futureList.add(future);
        });
        List<FinspStatDto> dtoList = H_FutureHelper.sequence(futureList).join();
        System.out.println(dtoList);*/
    }


    @Test
    public void getDssRepair(){
        CustomRequestContextHolder.setUserCode("sg-jzb-admin");
        DssRepairTimelinessDto repairTimeliness = dssInfoService.getRepairTimeliness("2024-08");
        System.out.println(repairTimeliness);
    }
}