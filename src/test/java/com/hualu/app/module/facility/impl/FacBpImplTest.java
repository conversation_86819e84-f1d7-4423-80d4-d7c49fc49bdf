package com.hualu.app.module.facility.impl;

import com.hualu.app.module.basedata.dto.near.BaseNearStructDto;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class FacBpImplTest extends BaseApplicationTest {


    @Autowired
    FacBpImpl facBp;

    @Test
    public void getNear() {

        CustomRequestContextHolder.setUserCode("ny-xb-admin");
        BaseNearStructDto dto =new BaseNearStructDto();
        dto.setFacilityCat("BP");
        dto.setIsNear(1);
        dto.setX(114.26872874).setY(23.29224833);
        //IPage<BaseNearStructDto> near = facBp.getNear(dto);
        //System.out.println(near);
    }
}