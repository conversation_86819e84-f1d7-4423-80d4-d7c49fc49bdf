package com.hualu.app.module.facility.impl;

import com.hualu.app.module.facility.dto.FacQueryDto;
import com.hualu.app.module.facility.dto.FacDssTypeDto;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class FacHdImplTest extends BaseApplicationTest {

    @Autowired
    FacHdImpl hdService;

    @Autowired
    FacQlImpl qlService;

    @Autowired
    FacSdImpl sdService;

    @Test
    public void selectDssType() {

        FacQueryDto dto = new FacQueryDto();
        dto.setStructPartId("2");
        dto.setStructId("3611");
        List<FacDssTypeDto> dssTypeDtos = hdService.selectDssType(dto);
        System.out.println(dssTypeDtos);
    }

    @Test
    public void selectQlDssType(){
        FacQueryDto dto = new FacQueryDto();
        dto.setStructId("20905");
        dto.setStructPartId("61");
        dto.setStructCompId("L1-1-1");
        List<FacDssTypeDto> dssTypeDtos = qlService.selectDssType(dto);
        System.out.println(dssTypeDtos);
    }

    @Test
    public void selectSdDssType(){
        FacQueryDto dto = new FacQueryDto();
        dto.setStructCompId("f8fb5cdb0f9646d0b429ae88eedd24c4");
        List<FacDssTypeDto> dssTypeDtos = sdService.selectDssType(dto);
        System.out.println(dssTypeDtos);
    }
}