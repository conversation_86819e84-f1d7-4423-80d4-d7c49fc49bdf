package com.hualu.app.module.mems.mpc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.mems.mpc.dto.MpitemSystemDto;
import com.hualu.app.module.mems.mpc.entity.MpcDssMeasure;
import com.hualu.app.module.mems.mpc.entity.MpcMpitem;
import com.hualu.app.module.mems.mpc.service.MpcDssMeasureService;
import com.hualu.app.module.mems.mpc.service.MpcMpitemService;
import com.tg.dev.api.core.test.BaseApplicationTest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@Slf4j
public class MpcDssMeasureServiceImplTest extends BaseApplicationTest {

    private static List<MpItemDto> mpItemDtoList = Lists.newArrayList();

    static {
        /*mpItemDtoList.add(new MpItemDto("BPXC",Lists.newArrayList("Y1102","Y1109","1102","1109")));
        mpItemDtoList.add(new MpItemDto("SDXC",Lists.newArrayList("Y1105","1105")));
        mpItemDtoList.add(new MpItemDto("HDXC",Lists.newArrayList("Y1104","1104")));
        mpItemDtoList.add(new MpItemDto("QLXC",Lists.newArrayList("Y1104","11044")));*/
        mpItemDtoList.add(new MpItemDto("LMXC",Lists.newArrayList("Y1103","Y1101","1103","1101")));
        mpItemDtoList.add(new MpItemDto("JAXC",Lists.newArrayList("Y1106","Y105","1106","105")));

        // 桥梁经常检查未添加
        /*mpItemDtoList.add(new MpItemDto("QLXC",Lists.newArrayList("Y1104")));*/
    }

    @Autowired
    MpcDssMeasureService measureService;

    @Autowired
    MpcMpitemService mpitemService;

    @Autowired
    DssTypeNewService dssTypeNewService;

    /**
     * 全量数据添加
     */
    @Test
    public void fetchMpitemSystems() {
        List<MpitemSystemDto> systemDtos = measureService.fetchMpitemSystems(2025);
        assertNotNull(systemDtos);
        Map<String, List<String>> dssTypeMap = listDssTypeToMap(mpItemDtoList.stream().map(MpItemDto::getDssTypePrefix).collect(Collectors.toList()));

        Map<String, MpitemSystemDto> systemDtoMap = systemDtos.stream().collect(Collectors.toMap(e -> e.getMntOrgId() + "_" + e.getMpitemSysId(), Function.identity(), (newValue, oldValue) -> newValue));

        AtomicInteger atomicInteger = new AtomicInteger(0);
        systemDtoMap.forEach((k,v)->{
            List<MpcDssMeasure> allMeasures = Lists.newArrayList();
            mpItemDtoList.forEach(row->{
                // 查询措施信息
                List<MpcMpitem> mpcMpitems = mpitemService.selectMpitemsByMpSysIdAndMpitemCode(v.getMpitemSysId(), row.getMpitemCodePrefixs());
                List<MpcDssMeasure> dssMeasureList = buildMeasures(v,row,dssTypeMap,mpcMpitems);
                allMeasures.addAll(dssMeasureList);
            });
            try
            {
                measureService.saveBatch(allMeasures);
            }catch (Exception e){
                //System.out.println(k);
                log.error(k+"数据库已存在数据");
            }
            System.out.println("=============================="+atomicInteger.incrementAndGet());
        });
        System.out.println("执行完成============================");
    }

    /**
     * 针对有部分数据在数据库中，需要先过滤后再添加
     */
    @Test
    public void execRepatData(){
        List<MpitemSystemDto> systemDtos = measureService.fetchMpitemSystems(2025);
        assertNotNull(systemDtos);
        Map<String, List<String>> dssTypeMap = listDssTypeToMap(mpItemDtoList.stream().map(MpItemDto::getDssTypePrefix).collect(Collectors.toList()));

        Map<String, MpitemSystemDto> systemDtoMap = systemDtos.stream().collect(Collectors.toMap(e -> e.getMntOrgId() + "_" + e.getMpitemSysId(), Function.identity(), (newValue, oldValue) -> newValue));

        AtomicInteger atomicInteger = new AtomicInteger(0);
        systemDtoMap.forEach((k,v)->{
            //N000010  0e4e0835-5c58-4fee-b1e3-107780b5ea92
            //if ("95b38078-bb66-43f5-9d38-7d6afb973808".equals(v.getMntOrgId())) {
                List<MpcDssMeasure> allMeasures = Lists.newArrayList();
                List<String> dbDssTypes = measureService.listDssType(v.getMntOrgId(), v.getMpitemSysId());
                mpItemDtoList.forEach(row->{
                    // 现有的病害类型
                    List<String> dssTypes = dssTypeMap.get(row.getDssTypePrefix());
                    // 查询措施信息
                    List<MpcMpitem> mpcMpitems = mpitemService.selectMpitemsByMpSysIdAndMpitemCode(v.getMpitemSysId(), row.getMpitemCodePrefixs());
                    // 数据库中不存在的病害类型
                    List<String> noDssTypes = dssTypes.stream().filter(e -> !dbDssTypes.contains(e)).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(noDssTypes)){
                        System.out.println(row + "      " + noDssTypes.size() + "    " + noDssTypes);
                        allMeasures.addAll(buildDssMeasures(v,mpcMpitems,noDssTypes));
                    }
                });
                System.out.println("汇总："+allMeasures.size());
                //measureService.saveBatch(allMeasures);
                try
                {
                    if (CollectionUtil.isNotEmpty(allMeasures)){
                        measureService.saveBatch(allMeasures);
                    }
                }catch (Exception e){
                    //System.out.println(k);
                    log.error(k+"数据库已存在数据");
                }
            //}

            System.out.println("=============================="+atomicInteger.incrementAndGet());
        });
        System.out.println("执行完成============================");
    }

    /**
     * 构件病害与措施关联信息
     * @param systemDto
     * @param row
     * @param dssTypeMap
     * @param mpcMpitems
     * @return
     */
    private List<MpcDssMeasure> buildMeasures(MpitemSystemDto systemDto,MpItemDto row, Map<String, List<String>> dssTypeMap, List<MpcMpitem> mpcMpitems) {
        // 病害类型
        List<String> dssTypes = dssTypeMap.get(row.getDssTypePrefix());
        return buildDssMeasures(systemDto, mpcMpitems, dssTypes);
    }

    private static List<MpcDssMeasure> buildDssMeasures(MpitemSystemDto systemDto, List<MpcMpitem> mpcMpitems, List<String> dssTypes) {
        List<MpcDssMeasure> measureList = Lists.newArrayList();
        dssTypes.forEach(dss->{
            mpcMpitems.forEach(mpcMpitem -> {
                MpcDssMeasure measure = new MpcDssMeasure()
                        .setDssType(dss)
                        .setMpitemId(mpcMpitem.getMpitemId())
                        .setMpitemSysId(systemDto.getMpitemSysId())
                        .setOrgId(systemDto.getMntOrgId())
                        .setDataFrom(1);
                measureList.add(measure);
            });
        });
        return measureList;
    }


    public Map<String,List<String>> listDssTypeToMap(List<String> dssTypes){
        Map<String,List<String>> dssTypeMap = Maps.newHashMap();
        dssTypes.forEach(dt->{
            LambdaQueryWrapper<DssTypeNew> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.likeRight(DssTypeNew::getDssType,dt).select(DssTypeNew::getDssType);
            List<String> dssTypeList = dssTypeNewService.list(queryWrapper).stream().map(DssTypeNew::getDssType).collect(Collectors.toList());
            dssTypeMap.put(dt,dssTypeList);
        });
        return dssTypeMap;
    }

    @Accessors(chain = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class MpItemDto implements Serializable {
        private String dssTypePrefix;
        private List<String> mpitemCodePrefixs;
    }
}