package com.hualu.app.module.mems.comm.service.impl;

import com.hualu.app.config.RedisCacheConfig;
import com.hualu.app.module.mems.comm.entity.BaseStructComp;
import com.hualu.app.module.mems.comm.service.BaseStructCompService;
import com.hualu.app.module.mems.dss.entity.DssTypeNew;
import com.hualu.app.module.mems.dss.service.DssTypeNewService;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.service.DmFinspItemService;
import com.hualu.app.module.mems.nminsp.entity.NmDinspSituationShow;
import com.hualu.app.module.platform.service.FwRightOrgService;
import com.hualu.app.utils.RedisUtils;
import com.hualu.app.utils.mems.DssTypeDto;
import com.hualu.app.utils.mems.H_DssTypeHelper;
import com.hualu.app.utils.mems.MonthlyFieldReader;
import com.hualu.app.utils.mems.NmDinspSituationShowSummarizer;
import com.tg.dev.api.core.test.BaseApplicationTest;
import com.tg.dev.api.util.hp.H_KeyWorker;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        "-Djasypt.encryptor.password=1234567890"
})
public class BaseStructCompServiceImplTest extends BaseApplicationTest {


    @Autowired
    BaseStructCompService compService;

    @Autowired
    DmFinspItemService itemService;

    @Autowired
    DssTypeNewService newService;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private FwRightOrgService orgService;

    @Test
    public void importData() {

        String BPJC = "20230518";

        BaseStructComp b1 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-PT","平台",BPJC);
        BaseStructComp b11 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC","防排水工程",BPJC);
        BaseStructComp b111 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-001","边沟",b11.getStructCompId());
        BaseStructComp b112 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-002","平台排水沟",b11.getStructCompId());
        BaseStructComp b113 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-003","急流槽",b11.getStructCompId());
        BaseStructComp b114 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-004","截水沟",b11.getStructCompId());
        BaseStructComp b115 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-005","坡面泄水孔",b11.getStructCompId());
        BaseStructComp b116 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-006","深层泄水孔",b11.getStructCompId());
        BaseStructComp b117 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-007","集水井",b11.getStructCompId());
        BaseStructComp b118 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-FPSGC-008","排水隧洞",b11.getStructCompId());

        BaseStructComp b2 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BPFH","普通防护工程",BPJC);
        BaseStructComp b21 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BPFH-001","护面墙或框格等防护工程",b2.getStructCompId());
        BaseStructComp b22 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BPFH-002","坡面",b2.getStructCompId());
        BaseStructComp b23 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BPFH-003","基础",b2.getStructCompId());

        BaseStructComp b3 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-RXFH","柔性防护工程",BPJC);
        BaseStructComp b31 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-RXFH-001","网内", b3.getStructCompId());
        BaseStructComp b32 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-RXFH-002","主动防护网", b3.getStructCompId());
        BaseStructComp b33 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-RXFH-003","被动防护网", b3.getStructCompId());
        BaseStructComp b34 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-RXFH-004","锚头", b3.getStructCompId());
        BaseStructComp b35 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-RXFH-005","锚固点", b3.getStructCompId());

        BaseStructComp b4 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-PJFH","喷浆防护工程",BPJC);
        BaseStructComp b41 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-PJFH-001","喷锚面",b4.getStructCompId());

        BaseStructComp b5 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZBFH","植被防护工程",BPJC);
        BaseStructComp b51 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZBFH-001","绿化、植草或防护工程覆盖", b5.getStructCompId());
        BaseStructComp b52 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZBFH-002","其他", b5.getStructCompId());

        BaseStructComp b6 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC","支挡工程",BPJC);
        BaseStructComp b61 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC-001","挡墙",b6.getStructCompId());
        BaseStructComp b62 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC-002","抗滑桩墙",b6.getStructCompId());
        BaseStructComp b63 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC-003","桩板墙",b6.getStructCompId());
        BaseStructComp b64 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC-004","墙体",b6.getStructCompId());
        BaseStructComp b65 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC-005","基础",b6.getStructCompId());
        BaseStructComp b66 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-ZDGC-006","其他",b6.getStructCompId());

        BaseStructComp b7 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC","锚固工程",BPJC);
        BaseStructComp b71 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC-001","混凝土外锚墩",b7.getStructCompId());
        BaseStructComp b72 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC-002","框架",b7.getStructCompId());
        BaseStructComp b73 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC-003","锚具",b7.getStructCompId());
        BaseStructComp b74 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC-004","锚垫",b7.getStructCompId());
        BaseStructComp b75 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC-005","锚头",b7.getStructCompId());
        BaseStructComp b76 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-MGGC-006","锚垫板",b7.getStructCompId());

        BaseStructComp b8 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BT","坡体",BPJC);
        BaseStructComp b81 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BT-001","坡面",b8.getStructCompId());
        BaseStructComp b82 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-BT-002","坡顶",b8.getStructCompId());

        BaseStructComp b9 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-QT","其他",BPJC);
        BaseStructComp b91 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-QT-001","检修道",b9.getStructCompId());
        BaseStructComp b92 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-QT-002","扶手",b9.getStructCompId());

        BaseStructComp b10 = new BaseStructComp(H_KeyWorker.nextIdToString(),"BPJC-LMGC","路面工程",BPJC);

        List<BaseStructComp> datas = Lists.newArrayList(b1,b11,b111,b112,b113,b114,b115,b116,b117,b118
                ,b2,b21,b22,b23,b3,b31,b32,b33,b34,b35,b4,b41,b5,b51,b52,b6,b61,b62,b63,b64,b65,b66,b7,b71,b72,b73,b74,b75,b76,b8,b81,b82,b9,b91,b92,b10);
        compService.saveBatch(datas);
    }

    @Test
    public void importFinspItem(){
        DmFinspItem item =   new DmFinspItem("BP","1","平台","推移、堆积物、危石、开裂、下错、塌陷","N000001","0","0","202305");
        DmFinspItem item1 =  new DmFinspItem("BP","2","防排水工程"," 边沟、平台排水沟、急流槽、截水沟：破损、堵塞、开裂、杂物堆积、变形、排水设施不完善","N000001","0","0","202305");
        DmFinspItem item2 =  new DmFinspItem("BP","3","防排水工程","坡面泄水孔、深层泄水孔：堵塞、周边渗漏水","N000001","0","0","202305");
        DmFinspItem item3 =  new DmFinspItem("BP","4","防排水工程","集水井或排水隧洞：破损、堵塞、周边渗漏水","N000001","0","0","202305");
        DmFinspItem item4 =  new DmFinspItem("BP","5","普通防护工程","护面墙或框格等防护工程：破损、开裂、倾斜、下沉、 滑移、空鼓、变形，压顶破损、勾缝脱落、脱落","N000001","0","0","202305");
        DmFinspItem item5 =  new DmFinspItem("BP","6","普通防护工程","坡面：渗漏水","N000001","0","0","202305");
        DmFinspItem item6 =  new DmFinspItem("BP","7","普通防护工程","基础：冲刷、下沉","N000001","0","0","202305");
        DmFinspItem item7 =  new DmFinspItem("BP","8","柔性防护工程","主动防护网及被动防护网：破损","N000001","0","0","202305");
        DmFinspItem item8 =  new DmFinspItem("BP","9","柔性防护工程","网内：危石","N000001","0","0","202305");
        DmFinspItem item9 =  new DmFinspItem("BP","10","柔性防护工程","锚头或锚固点：松动、锈蚀","N000001","0","0","202305");
        DmFinspItem item10 = new DmFinspItem("BP","11","喷浆防护工程","喷锚面：开裂","N000001","0","0","202305");
        DmFinspItem item11 = new DmFinspItem("BP","12","喷浆防护工程","喷锚面：碎落崩塌、局部坍塌、鼓胀隆起","N000001","0","0","202305");
        DmFinspItem item12 = new DmFinspItem("BP","13","喷浆防护工程","喷锚面：渗漏水","N000001","0","0","202305");
        DmFinspItem item13 = new DmFinspItem("BP","14","植被防护工程","绿化、植草或防护工程覆盖：破损，局部坍塌、植被脱落、掏空","N000001","0","0","202305");
        DmFinspItem item14 = new DmFinspItem("BP","15","植被防护工程","坡面冲刷、渗漏水、植被不良","N000001","0","0","202305");
        DmFinspItem item15 = new DmFinspItem("BP","16","支挡工程","挡墙、抗滑桩墙、桩板墙等：开裂、下错、倾斜、滑移、倒塌","N000001","0","0","202305");
        DmFinspItem item16 = new DmFinspItem("BP","17","支挡工程","空鼓、下沉，压顶破损、勾缝脱落、变形、墙体脱空、轻度裂缝","N000001","0","0","202305");
        DmFinspItem item17 = new DmFinspItem("BP","18","支挡工程","墙体：渗漏水","N000001","0","0","202305");
        DmFinspItem item18 = new DmFinspItem("BP","19","支挡工程","基础：掏空、冲刷、下沉","N000001","0","0","202305");
        DmFinspItem item19 = new DmFinspItem("BP","20","锚固工程","混凝土外锚墩：变形、开裂、钢筋（丝）外露","N000001","0","0","202305");
        DmFinspItem item20 = new DmFinspItem("BP","21","锚固工程","框架：开裂、滑移、倾斜、下错","N000001","0","0","202305");
        DmFinspItem item21 = new DmFinspItem("BP","22","锚固工程","锚垫：滑移；锚具：脱落、松动","N000001","0","0","202305");
        DmFinspItem item22 = new DmFinspItem("BP","23","锚固工程","锚头：渗漏水、锈蚀","N000001","0","0","202305");
        DmFinspItem item23 = new DmFinspItem("BP","24","锚固工程","锚垫板：锈蚀","N000001","0","0","202305");
        DmFinspItem item24 = new DmFinspItem("BP","25","坡体病害"," 坡面及坡顶：坡面冲刷、碎落崩塌、局部坍塌、危石、滑坡、开裂、变形、鼓胀隆起、塌陷","N000001","0","0","202305");
        DmFinspItem item25 = new DmFinspItem("BP","26","其他","检修道及扶手：破损、缺失","N000001","0","0","202305");
        DmFinspItem item26 = new DmFinspItem("BP","27","路面工程"," 滑移、开裂、下错、鼓胀隆起、异常响声，流泥、流砂、流水、崩塌落石等堆积路面，不均匀沉降，路面翻浆、路肩破损、阻挡路面排水、路肩不洁、杂物堆积","N000001","0","0","202305");

        List<DmFinspItem> datas = Lists.newArrayList(item,item1,item2,item3,item4,item5,item6,item7,item8,item9,item10,item11,item12,item13,item14,item15,item16,
                item17,item18,item19,item20,item21,item22,item23,item24,item25,item26);
        itemService.saveBatch(datas);
    }

    //导入边坡经常检查病害信息
    @Test
    public void importDssType(){
        DssTypeDto dto1 = new DssTypeDto("崩塌落石等堆积路面","v","v","路面工程");
        DssTypeDto dto2 = new DssTypeDto("变形","l","l");
        DssTypeDto dto3 = new DssTypeDto("变形","l,a","a");
        DssTypeDto dto4 = new DssTypeDto("变形","n","n");
        DssTypeDto dto5 = new DssTypeDto("不均匀沉降","l,w,a","a","路面工程");
        DssTypeDto dto6 = new DssTypeDto("冲刷","l,a","l","普通防护工程");
        DssTypeDto dto7 = new DssTypeDto("冲刷","a","a","支挡工程");
        DssTypeDto dto8 = new DssTypeDto("倒塌","l,v","v","支挡工程");
        DssTypeDto dto9 = new DssTypeDto("堵塞","l,n","n","防排水工程");
        DssTypeDto dto10 = new DssTypeDto("堵塞","n,p","n","防排水工程");
        DssTypeDto dto11 = new DssTypeDto("堵塞","l","l","防排水工程");
        DssTypeDto dto12 = new DssTypeDto("堆积物","v","v","平台");
        DssTypeDto dto13 = new DssTypeDto("钢筋（丝）外露","n","n","锚固工程");
        DssTypeDto dto14 = new DssTypeDto("勾缝脱落","n,a","a","");
        DssTypeDto dto15 = new DssTypeDto("鼓胀隆起","l,a","a","");
        DssTypeDto dto16 = new DssTypeDto("滑坡","l,w,d,v","v","");
        DssTypeDto dto17 = new DssTypeDto("滑移","a","a","护面墙或框格等防护工程");
        DssTypeDto dto18 = new DssTypeDto("滑移","n","n","护面墙或框格等防护工程");
        DssTypeDto dto19 = new DssTypeDto("滑移","l,v","v","支挡工程,路面工程");
        DssTypeDto dto20 = new DssTypeDto("局部坍塌","d,a,v","v","");
        DssTypeDto dto21 = new DssTypeDto("局部坍塌","d,a","a","");
        DssTypeDto dto22 = new DssTypeDto("局部坍塌","a,v","v","");
        DssTypeDto dto23 = new DssTypeDto("开裂","n","n","");
        DssTypeDto dto24 = new DssTypeDto("开裂","l,n","l","");
        DssTypeDto dto25 = new DssTypeDto("空鼓","a","a","");
        DssTypeDto dto26 = new DssTypeDto("流泥","a","a","");
        DssTypeDto dto27 = new DssTypeDto("流砂","a","a","");
        DssTypeDto dto28 = new DssTypeDto("流水","a","a","");
        DssTypeDto dto29 = new DssTypeDto("路肩不洁","a","a","");
        DssTypeDto dto30 = new DssTypeDto("路肩破损","l","l","");
        DssTypeDto dto31 = new DssTypeDto("路面翻浆","a","a","");
        DssTypeDto dto32 = new DssTypeDto("排水设施不完善","a","a","");
        DssTypeDto dto33 = new DssTypeDto("坡面冲刷","a","a","");
        DssTypeDto dto34 = new DssTypeDto("破损","n,a","a","");
        DssTypeDto dto35 = new DssTypeDto("破损","a","a","");
        DssTypeDto dto36 = new DssTypeDto("破损","n","n","");
        DssTypeDto dto37 = new DssTypeDto("墙体脱空","a","a","");
        DssTypeDto dto38 = new DssTypeDto("轻度裂缝","l","l","");
        DssTypeDto dto39 = new DssTypeDto("倾斜","l","l","");
        DssTypeDto dto40 = new DssTypeDto("缺失","l","l","");
        DssTypeDto dto41 = new DssTypeDto("渗漏水","a","a","");
        DssTypeDto dto42 = new DssTypeDto("渗漏水","n","n","");
        DssTypeDto dto43 = new DssTypeDto("松动","n","n","");
        DssTypeDto dto44 = new DssTypeDto("碎落崩塌","a,v","v","");
        DssTypeDto dto45 = new DssTypeDto("塌陷","d,a,v","v","");
        DssTypeDto dto46 = new DssTypeDto("掏空","d,a","a","");
        DssTypeDto dto47 = new DssTypeDto("推移","l","l","");
        DssTypeDto dto48 = new DssTypeDto("脱落","n,a","a","");
        DssTypeDto dto49 = new DssTypeDto("脱落","n","n","");
        DssTypeDto dto50 = new DssTypeDto("危石","v","v","");
        DssTypeDto dto51 = new DssTypeDto("下沉","l,d","l","");
        DssTypeDto dto52 = new DssTypeDto("下沉","l","l","");
        DssTypeDto dto53 = new DssTypeDto("下沉","l,d,v","l","");
        DssTypeDto dto54 = new DssTypeDto("下错","l","l","");
        DssTypeDto dto55 = new DssTypeDto("锈蚀","n,p","n","");
        DssTypeDto dto56 = new DssTypeDto("锈蚀","p","p","");
        DssTypeDto dto57 = new DssTypeDto("压顶破损","n,a","a","");
        DssTypeDto dto58 = new DssTypeDto("异常响声","n","n","");
        DssTypeDto dto59 = new DssTypeDto("杂物堆积","v","v","");
        DssTypeDto dto60 = new DssTypeDto("杂物堆积","l,v","v","");
        DssTypeDto dto61 = new DssTypeDto("植被不良","a","a","");
        DssTypeDto dto62 = new DssTypeDto("植被脱落","a","a","");
        DssTypeDto dto63 = new DssTypeDto("周边渗漏水","a","a","");
        DssTypeDto dto64 = new DssTypeDto("阻挡路面排水","l","l","");



        ArrayList<DssTypeDto> typeDtos = Lists.newArrayList(dto1, dto2, dto3, dto4, dto5, dto6, dto7, dto8, dto9, dto10,
                dto11, dto12, dto13, dto14, dto15, dto16, dto17, dto18, dto19, dto20,
                dto21, dto22, dto23, dto24, dto25, dto26, dto27, dto28, dto29, dto30,
                dto31, dto32, dto33, dto34, dto35, dto36, dto37, dto38, dto39, dto40,
                dto41, dto42, dto43, dto44, dto45, dto46, dto47, dto48, dto49, dto50,
                dto51, dto52, dto53, dto54, dto55, dto56, dto57, dto58, dto59, dto60,
                dto61, dto62, dto63,dto64);

        List<DssTypeNew> dssTypes = H_DssTypeHelper.getDssTypes(typeDtos);
        newService.saveBatch(dssTypes);
        //System.out.println(dssTypes.size());
    }
    @Test
    public void importDssType2(){
        //查询缺失的部分
        String cacheKey = String.format(RedisCacheConfig.DINSP_SITUATION_CACHE_KEY, "N000001", 2025);
        List<NmDinspSituationShow> allData = redisUtils.getList(cacheKey, NmDinspSituationShow.class);

        if (allData != null && !allData.isEmpty()) {
            Set<String> orgSet = new HashSet<>(orgService.selectChildOprtOrgCodes("N000002"));
            // 使用并行流处理，提高大数据量时的处理速度
            List<NmDinspSituationShow> result = allData.parallelStream()
                    .filter(item -> orgSet.contains(item.getOrgCode()))
                    .collect(Collectors.toList());
            NmDinspSituationShowSummarizer showSummarizer = new NmDinspSituationShowSummarizer();
            NmDinspSituationShow summarize = showSummarizer.summarize(result);
            try {
                Map<String, Object> fieldValues = MonthlyFieldReader.getCurrentMonthFields(summarize);
                System.out.println(fieldValues);
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println(summarize);
        }
    }

}
