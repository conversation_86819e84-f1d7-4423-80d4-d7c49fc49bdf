package com.hualu.app.module.mems.nminsp.rabbit;

import cn.hutool.core.thread.ThreadUtil;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class DinspMessageSenderTest extends BaseApplicationTest {

    @Autowired
    DinspMessageSender dinspMessageSender;

    @Autowired
    NmDinspService service;

    @Test
    public void sendMessage() {

        dinspMessageSender.sendMessage("你真可以");
        ThreadUtil.sleep(2000);
    }

    @Test
    public void getDssInfoImageExport() {

       service.getDssInfoImageExport("1898925189534715904", "LM", "");
    }
}
