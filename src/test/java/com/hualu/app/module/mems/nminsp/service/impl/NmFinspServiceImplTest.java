package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eos.workflow.api.BPSServiceClientFactory;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;

public class NmFinspServiceImplTest extends BaseApplicationTest {

    private static String processDefName="gdcg.emdc.mems.dm.NFinspWorkFlow";

    @Autowired
    NmFinspService nmFinspService;

    @Test
    public void updateGzjQl() throws Exception {

        BPSServiceClientFactory.getLoginManager().setCurrentUser("ny-gzj-zhangjh","张锦河");

        LambdaQueryWrapper<NmFinsp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NmFinsp::getMntOrgId,"3a9773db-72e0-42be-a4a5-74505b40f948")
                .eq(NmFinsp::getFacilityCat,"QL");

        List<NmFinsp> nmFinsps = nmFinspService.list(wrapper);
        for (NmFinsp item : nmFinsps) {
            long processInstId = H_WorkFlowHelper.createWorkItem(processDefName, "经常检查", "");
            item.setProcessinstid(processInstId);
            item.setStatus(0);
        }
        nmFinspService.updateBatchById(nmFinsps);
    }

    // 重新编号
    @Test
    public void updateFinspCode(){
        LambdaQueryWrapper<NmFinsp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(NmFinsp::getMntOrgId,"78c0f3ef-37ce-4121-bcc9-bee5ebd14ade")
                .eq(NmFinsp::getFacilityCat,"BP")
                .apply("to_char(insp_date,'yyyy-MM')={0}","2025-06")
                .orderByAsc(NmFinsp::getLineCode,NmFinsp::getStructStakeNum);
        List<NmFinsp> nmFinsps = nmFinspService.list(wrapper);
        AtomicInteger ac = new AtomicInteger(0);
        nmFinsps.forEach(item->{
            // JCJC-BP-NYLY-20250606-0032
            String finspCode = item.getFinspCode();
            int lastIndex = finspCode.lastIndexOf("-");
            String codePrefix = finspCode.substring(0, lastIndex + 1);
            String paddedNumber = StrUtil.padPre(String.valueOf(ac.incrementAndGet()), 4, '0');

            String newCode = codePrefix + paddedNumber;
            item.setFinspCode(newCode);
            System.out.println(newCode);
        });
        nmFinspService.updateBatchById(nmFinsps);
        System.out.println(nmFinsps.size());
    }
}