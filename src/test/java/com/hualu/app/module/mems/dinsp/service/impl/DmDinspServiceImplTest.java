package com.hualu.app.module.mems.dinsp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.hualu.app.module.mems.dinsp.entity.*;
import com.hualu.app.module.mems.nminsp.dto.WorkNmDinspDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspMapper;
import com.hualu.app.module.mems.nminsp.service.impl.NmFinspServiceImpl;
import com.hualu.app.utils.SafeLambda;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class DmDinspServiceImplTest extends BaseApplicationTest {


    @Autowired
    DmDinspServiceImpl service;

    @Autowired
    NmFinspServiceImpl finspService;


    @Test
    public void savedoEvent() {
        /*CustomRequestContextHolder.setUserId("c97fe0a8-9320-462c-be40-d42a9a945c96");
        CustomRequestContextHolder.setUserCode("sg-hy-wangwb");
        BPSServiceClientFactory.getLoginManager().setCurrentUser("sg-hy-wangwb", "王文波");
        service.savedoEvent(5750824,true,"病害入库",null);*/

        LambdaQueryWrapper<NmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        //SFunction<NmFinsp, Object> finspId = H_SFunctionHelper.generateSFunction(NmFinsp.class, "finspId");
        SFunction<NmFinsp, Object> finspId = SafeLambda.of(NmFinsp.class, "finspId");
        queryWrapper.eq(finspId,"1888906973144027136");

        //List<NmFinsp> list = finspService.list(queryWrapper);
        //System.out.println(list.size());
    }
    @Test
    public void testAllServiceMethods() {
        testQueryGroupCount();
        testQueryFinspGroupCount();
        testListInspectionLedgerBills();
        testListInspectionRCLedgerBills();
        testCountDailyDefects();
        testCalculateDefectsByCategory();
        testIndexDefectRecordsForFastQuery();
        testGetImageFileId();
        testInspectionResult();
        testInspectionRecord();
    }

    private void testQueryGroupCount() {
        // 测试正常查询
        List<DmCountResult> results = service.queryGroupCount("N000035");
        System.out.println("results"+ results);
    }

    private void testQueryFinspGroupCount() {
        List<DmCountResult> results = service.queryFinspGroupCount("N000035");
        System.out.println("testQueryFinspGroupCount"+ results);
    }

    private void testListInspectionLedgerBills() {
        // 分页查询测试
        IPage<InspectionLedgerDTO> page = service.listInspectionLedgerBills(1, 10, "N000035", "1");
        System.out.println("testListInspectionLedgerBills"+ page);
    }

    private void testListInspectionRCLedgerBills() {
        IPage<InspectionLedgerDTO> page = service.listInspectionRCLedgerBills(2, 20, "N000035", "2");
        System.out.println("testListInspectionRCLedgerBills"+ page);
    }

    private void testCountDailyDefects() {
        Map<String, Object> stats = service.countDailyDefects("N000035");
        System.out.println("testCountDailyDefects"+ stats);
    }

    private void testCalculateDefectsByCategory() {
        Map<String, Object> result = service.calculateDefectsByCategory("N000035");
        System.out.println("testCalculateDefectsByCategory"+ result);
    }

    private void testIndexDefectRecordsForFastQuery() {
        IPage<FacilityInspectionDTO> page = service.indexDefectRecordsForFastQuery(1, 5, "N000035");
        System.out.println("testIndexDefectRecordsForFastQuery"+ page);
    }

    private void testGetImageFileId() {
        List<String> fileIds = service.getImageFileId("1919958565267836928");
        System.out.println("testGetImageFileId"+ fileIds);
    }

    private void testInspectionResult() {
        List<InspectionResult> results = service.InspectionResult("38b22f19-891d-4075-811a-12482d403586", "1902686546604199936");
        System.out.println("testInspectionResult"+ results);
    }

    private void testInspectionRecord() {
        IPage<InspectionRecord> page = service.InspectionRecord(1, 10, "N000035");
        System.out.println("testInspectionRecord"+ page);
    }
}
