package com.hualu.app.module.mems.task.service.impl;

import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskDetailDto;
import com.hualu.app.module.mems.task.service.DmTaskAccptDetailService;
import com.hualu.app.module.mems.task.service.DmTaskDetailService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class DmTaskAccptDetailServiceImplTest extends BaseApplicationTest {

    @Autowired
    DmTaskDetailService taskService;

    @Autowired
    DmTaskAccptDetailService detailService;

    @Before
    public void setUp() throws Exception {
        CustomRequestContextHolder.setUserCode("test");
    }

    @Test
    public void create(){
        String dmTaskId = "4af94ca7-b9b4-4755-a8d7-efeffd55aedf";
        List<DmTaskDetailDto> dmTaskDetailDtos = taskService.selectTaskDetailsByTaskId(dmTaskId);
        detailService.createDmTaskAccptDetails(dmTaskId,dmTaskDetailDtos);
    }
}