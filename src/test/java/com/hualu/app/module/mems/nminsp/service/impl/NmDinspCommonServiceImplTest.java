package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hualu.app.module.mems.dinsp.entity.DmDinsp;
import com.hualu.app.module.mems.nminsp.dto.FailDinspDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.service.NmDinspCommonService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.workflow.service.WfworkitemService;
import com.tg.dev.api.core.test.BaseApplicationTest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * 修复错乱的补空单
 */
public class NmDinspCommonServiceImplTest extends BaseApplicationTest {

    @Autowired
    NmDinspCommonService commonService;

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    WfworkitemService wfworkitemService;

    @Test
    public void initNmDinspCommon() {

        List<FailDinspDto> dinspDtos = commonService.listFailDinsp();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        dinspDtos.forEach(dto->{

            System.out.println("正在执行第"+atomicInteger.incrementAndGet()+"单");
            List<NmDinsp> nmDinsps = getDinsps(dto);
            Set<Long> processInstIds = nmDinsps.stream().filter(e -> ObjectUtil.isNotNull(e.getProcessinstid())).map(NmDinsp::getProcessinstid).collect(Collectors.toSet());
            List<WfProcessDto> dtoList  = Lists.newArrayList();
            processInstIds.forEach(instId->{
                WfProcessDto wfProcessDto = new WfProcessDto(instId, dto.getComUserCode(), dto.getComUserName());
                dtoList.add(wfProcessDto);
            });

            dtoList.forEach(item->{
                wfworkitemService.updateUserInfo(item.getProcessinstid(),item.getUserCode(),item.getUserName());
                updateUser(item.processinstid,dto);
            });
        });
        System.out.println(dinspDtos.size());
    }

    private List<NmDinsp> getDinsps(FailDinspDto dto) {
        LambdaQueryWrapper<NmDinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NmDinsp::getCommonDinspId, dto.getCommonDinspId())
                .eq(NmDinsp::getMntOrgId, dto.getComOrg())
                .eq(NmDinsp::getFacilityCat, dto.getFacilityCat())
                .groupBy(NmDinsp::getProcessinstid)
                .select(NmDinsp::getProcessinstid);
        List<NmDinsp> nmDinsps = nmDinspService.list(queryWrapper);

        return nmDinsps;
    }

    public void updateUser(Long processinstid, FailDinspDto dto) {
        LambdaUpdateWrapper<NmDinsp> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(NmDinsp::getProcessinstid, processinstid);
        wrapper.set(NmDinsp::getCreateUserId,dto.getComUserId());
        nmDinspService.update(wrapper);
    }


    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @Data
    static class WfProcessDto implements Serializable {

        private Long processinstid;

        private String userCode;

        private String userName;
    }
}