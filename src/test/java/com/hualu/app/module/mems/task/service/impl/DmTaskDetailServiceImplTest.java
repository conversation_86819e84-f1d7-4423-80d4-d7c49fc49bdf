package com.hualu.app.module.mems.task.service.impl;

import com.hualu.app.module.mems.task.dto.taskdetail.DmTaskHzDto;
import com.hualu.app.module.mems.task.service.DmTaskDetailService;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class DmTaskDetailServiceImplTest extends BaseApplicationTest {

    @Autowired
    DmTaskDetailService service;

    @Test
    public void getTaskHzDto() {

        DmTaskHzDto taskHzDto = service.getTaskHzDto("3eb9a62b-8ecd-45b3-a4d6-05132943e31d");
        System.out.println(taskHzDto);
    }
}