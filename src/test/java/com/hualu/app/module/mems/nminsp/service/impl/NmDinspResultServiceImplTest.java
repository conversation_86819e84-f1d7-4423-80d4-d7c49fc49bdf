package com.hualu.app.module.mems.nminsp.service.impl;

import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.hualu.app.module.mems.nminsp.service.NmFinspService;
import com.hualu.app.module.mems.nminsp.util.H_StructHelper;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.*;

public class NmDinspResultServiceImplTest extends BaseApplicationTest {

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    NmFinspService nmFinspService;

    @Test
    public void createNmDinspResult() {

        List<NmDinsp> nmDinsps = nmDinspService.lambdaQuery()
                .eq(NmDinsp::getFacilityCat, "JA")
                .gt(NmDinsp::getDssNum, 1)
                // .eq(NmDinsp::getDinspId,"1888907147425746944")
                .list();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        nmDinsps.forEach(item->{
            System.out.println("正在执行"+atomicInteger.incrementAndGet());
            H_StructHelper.refreshNmDinspResult(item);
        });
        System.out.println(nmDinsps.size());
        //H_StructHelper.refreshNmDinspResult(nmDinsp);
    }

    @Test
    public void createNmFinspResult(){
        List<NmFinsp> nmDinsps = nmFinspService.lambdaQuery()
                .eq(NmFinsp::getFacilityCat, "JA")
                .gt(NmFinsp::getDssNum, 1)
                //.eq(NmFinsp::getFinspId,"1906956861328986112")
                .list();
        AtomicInteger atomicInteger = new AtomicInteger(0);
        nmDinsps.forEach(item->{
            System.out.println("正在执行"+atomicInteger.incrementAndGet());
            H_StructHelper.refreshNmFinspResult(item);
        });
        System.out.println(nmDinsps.size());
    }
}