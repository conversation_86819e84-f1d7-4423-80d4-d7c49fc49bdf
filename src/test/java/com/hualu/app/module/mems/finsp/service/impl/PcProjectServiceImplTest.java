package com.hualu.app.module.mems.finsp.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.app.module.mems.finsp.controller.PcProjectController;
import com.hualu.app.module.mems.finsp.dto.PcProjectDto;
import com.hualu.app.module.mems.finsp.entity.PcFinsp;
import com.hualu.app.module.mems.finsp.entity.PcProject;
import com.hualu.app.module.mems.finsp.entity.PcProjectScope;
import com.hualu.app.module.mems.finsp.service.PcFinspService;
import com.hualu.app.module.mems.finsp.service.PcProjectScopeService;
import com.hualu.app.module.mems.finsp.service.PcProjectService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import com.tg.dev.api.util.hp.H_UniqueConstraintHelper;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

public class PcProjectServiceImplTest extends BaseApplicationTest {

    @Autowired
    PcProjectService pcProjectService;

    @Autowired
    PcFinspService pcFinspService;

    @Autowired
    PcProjectScopeService pcProjectScopeService;

    @Autowired
    PcProjectController projectController;

    @Test
    public void listProjectByLast() throws IOException {

        /*List<PcProject> list = pcProjectService.list();
        System.out.println(list.size());

        PcProject pcProject = new PcProject();
        pcProject.setPrjId("1");
        pcProject.setPrjYear(2021);

        pcProjectService.save(pcProject);*/

        //pcProjectService.removeByIds(Lists.newArrayList("1"));

        //List<PcProjectScope> pcProjectScopes = pcProjectScopeService.listByMd5(Lists.newArrayList("08a821e4ee2d6328759250f44bb64fd1"), "1869562806899183616");

        //System.out.println(pcProjectScopes.size());

        CustomRequestContextHolder.setOrgId("4d8828fb-cda3-43cd-8305-b4868b4805d0");
        MultipartFile file = new MockMultipartFile("file","工点清单模板（龙连）1.8.xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", FileUtil.getInputStream(new File("D:\\工点清单模板（龙连）1.8.xlsx")));

        file.transferTo(new File("D:\\text.xlsx"));
        // "mntType":"1","facilityCat":"BP","prjName":"龙连高速","inspOrgName":"广东交科检测"
        PcProjectDto dto = new PcProjectDto();
        dto.setMntType("1").setFacilityCat("BP").setPrjName("龙连高速").setInspOrgName("广东交科检测");
        projectController.createProject(dto,file);
    }


    @Test
    public void replaceMd5(){
        List<PcProject> list = pcProjectService.list();
        for (PcProject pcProject : list) {
            pcProject.setPrjYear(2025);
            String unique = H_UniqueConstraintHelper.createUnique(pcProject);
            pcProject.setMd5(unique);
        }
        pcProjectService.updateBatchById(list);
    }

    @Test
    public void replaceProjectScope(){
        // 已经检查的边坡数据
        LambdaQueryWrapper<PcFinsp> finspWrapper = new LambdaQueryWrapper<>();
        finspWrapper.exists("select 1 from memsdb.PC_PROJECT_SCOPE a where PC_FINSP.STRUCT_ID = a.struct_id and a.prj_id = '1868232603778289664'");
        List<PcFinsp> finsps = pcFinspService.list(finspWrapper);

        Map<String, PcFinsp> finspMap = finsps.stream().collect(Collectors.toMap(PcFinsp::getStructName, Function.identity()));

        // 现有新博的边坡数据
        LambdaQueryWrapper<PcProjectScope> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PcProjectScope::getPrjId,"1868232603778289664");
        List<PcProjectScope> scopeList = pcProjectScopeService.list(lambdaQueryWrapper);

        Set<String> tSet = new HashSet<>();
        Set<String> dbStructs = finspMap.keySet();
        int idx = 0;
        for (PcProjectScope item : scopeList) {
            for (String key : dbStructs) {
                String temp = key.replaceAll("[\\u4e00-\\u9fa5]", "");
                if (item.getStructName().contains(temp)){
                    ++idx;
                    tSet.add(key+"-"+item.getStructName());
                    PcFinsp pcFinsp = finspMap.get(key);
                    pcFinsp.setStructName(item.getStructName()).setStructId(item.getStructId());
                }
            }
        }
        System.out.println(111);
        pcFinspService.updateBatchById(finsps);
    }
}