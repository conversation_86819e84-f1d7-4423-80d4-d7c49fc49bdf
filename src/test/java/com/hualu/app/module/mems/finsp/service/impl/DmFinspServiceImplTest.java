package com.hualu.app.module.mems.finsp.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.eos.workflow.api.BPSServiceClientFactory;
import com.google.common.collect.Maps;
import com.hualu.app.module.mems.finsp.entity.DmFinsp;
import com.hualu.app.module.mems.finsp.entity.DmFinspItem;
import com.hualu.app.module.mems.finsp.entity.DmFinspRecord;
import com.hualu.app.module.mems.finsp.entity.DmFinspResult;
import com.hualu.app.module.mems.finsp.mapper.DmFinspMapper;
import com.hualu.app.module.mems.finsp.service.DmFinspItemService;
import com.hualu.app.module.mems.finsp.service.DmFinspRecordService;
import com.hualu.app.module.mems.finsp.service.DmFinspResultService;
import com.hualu.app.module.workflow.H_WorkFlowHelper;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import com.tg.dev.api.util.hp.H_KeyWorker;
import com.tg.dev.mybatisplus.utils.H_BatisBatchQuery;
import com.tg.dev.mybatisplus.utils.H_BatisQuery;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DmFinspServiceImplTest extends BaseApplicationTest {

    @Autowired
    DmFinspServiceImpl service;

    @Autowired
    DmFinspRecordService recordService;

    @Autowired
    DmFinspResultService resultService;

    @Autowired
    DmFinspItemService itemService;

    @Autowired
    DmFinspMapper dmFinspMapper;


    @Before
    public void init(){
        CustomRequestContextHolder.setUserCode("T-linpr");
        CustomRequestContextHolder.setUserName("测试人员");
        CustomRequestContextHolder.setOrgId("N000025");
        BPSServiceClientFactory.getLoginManager().setCurrentUser(CustomRequestContextHolder.getUserCode(), CustomRequestContextHolder.getUserName());
    }


    @Test
    public void selectFinspId(){
        DateTime dateTime = DateUtil.parseDate("2024-10-21");
        String finspId = service.getFinspId("ae04fa8c-b9d6-b03c-36e5-74c0442cc116", "BP", dateTime);
        System.out.println(finspId);
    }

    @Test
    public void createRes(){
        LambdaQueryWrapper<DmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DmFinsp::getFinspId);
        queryWrapper.eq(DmFinsp::getFacilityCat,"BP");
        queryWrapper.notExists("select 1 from DM_FINSP_RESULT res where res.FINSP_ID = dm_finsp.FINSP_ID");

        List<DmFinsp> dmFinspList = service.list(queryWrapper);
        List<String> finspIds = dmFinspList.stream().map(DmFinsp::getFinspId).collect(Collectors.toList());

        //3.获取201801的检查项
        LambdaQueryWrapper<DmFinspItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(DmFinspItem::getFinVersion,"201801").eq(DmFinspItem::getFacilityCat,"BP");
        List<DmFinspItem> itemList = itemService.list(itemQuery);
        H_BatisQuery.protectBatchIn(new H_BatisBatchQuery() {
            @Override
            public List selectListForIn(List list) {
                LambdaQueryWrapper<DmFinspResult> resQuery = new LambdaQueryWrapper<>();
                resQuery.in(DmFinspResult::getFinspId,list);
                resultService.remove(resQuery);

                list.forEach(id->{
                    //4.根据主单ID,重新赋值添加
                    List<DmFinspResult> resultList = Lists.newArrayList();
                    itemList.forEach(item->{
                        DmFinspResult row = new DmFinspResult();
                        BeanUtil.copyProperties(item,row);
                        row.setFinspResId(H_KeyWorker.nextIdToString());
                        row.setFinspId(id.toString());
                        row.setIssueDesc("未发现病害");
                        row.setInspResult("0");
                        row.setRemark("/");
                        resultList.add(row);
                    });
                    if (resultList.size() != 0){
                        resultService.saveBatch(resultList);
                    }
                    System.out.println("11111");
                });
                return list;
            }
        },finspIds,100);
    }

    /**
     * 恢复边坡201801的检查结论
     */
    @Test
    public void resetData(){

        /*LambdaQueryWrapper<DmFinsp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmFinsp::getFacilityCat,"BP");
        queryWrapper.eq(DmFinsp::getFinVersion,"202305");
        queryWrapper.eq(DmFinsp::getMntOrgId,"2cad9217-2783-4460-92c5-9c32a93f505b");
        queryWrapper.select(DmFinsp::getFinspId);

        //1.查询202305版的经常检查单ID
        List<DmFinsp> dmFinspList = service.list(queryWrapper);
        List<String> finspIds = dmFinspList.stream().map(DmFinsp::getFinspId).collect(Collectors.toList());*/

        //还原中开数据
        List<DmFinsp> dmFinspList = dmFinspMapper.getFinVersionBy202305("2cad9217-2783-4460-92c5-9c32a93f505b");
        List<String> finspIds = dmFinspList.stream().map(DmFinsp::getFinspId).collect(Collectors.toList());

        //3.获取201801的检查项
        LambdaQueryWrapper<DmFinspItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(DmFinspItem::getFinVersion,"201801").eq(DmFinspItem::getFacilityCat,"BP");
        List<DmFinspItem> itemList = itemService.list(itemQuery);

        //2.删除对应的检查结论
        H_BatisQuery.protectBatchIn(new H_BatisBatchQuery() {
            @Override
            public List selectListForIn(List list) {
                LambdaQueryWrapper<DmFinspResult> resQuery = new LambdaQueryWrapper<>();
                resQuery.in(DmFinspResult::getFinspId,list);
                resultService.remove(resQuery);

                list.forEach(id->{
                    //4.根据主单ID,重新赋值添加
                    List<DmFinspResult> resultList = Lists.newArrayList();
                    itemList.forEach(item->{
                        DmFinspResult row = new DmFinspResult();
                        BeanUtil.copyProperties(item,row);
                        row.setFinspResId(H_KeyWorker.nextIdToString());
                        row.setFinspId(id.toString());
                        row.setIssueDesc("未发现病害");
                        row.setInspResult("0");
                        row.setRemark("/");
                        resultList.add(row);
                    });
                    if (resultList.size() != 0){
                        resultService.saveBatch(resultList);
                    }
                    System.out.println(resultList.size());

                    //修复经常检查单版本
                    LambdaUpdateWrapper<DmFinsp> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.in(DmFinsp::getFinspId,list);
                    updateWrapper.set(DmFinsp::getFinVersion,"201801");
                    service.update(updateWrapper);

                    System.out.println("11111");
                });
                return list;
            }
        },finspIds,100);

        System.out.println("执行结束");
    }

    @Test
    public void saveOrUpdateFinsp() throws Exception {
        DmFinsp dmFinsp = new DmFinsp();

        //马山顶隧道（下行）
        dmFinsp.setStructId("B14A71AE-35E5-4741-8C70-302915191637");
        dmFinsp.setSearchDept("唐测试");
        dmFinsp.setFinspCode("JCJC-SD-TG-20230416-0001");
        dmFinsp.setFacilityCat("SD");
        dmFinsp.setLineCode("G4511");
        dmFinsp.setInspPerson("唐唐唐");
        dmFinsp.setMntnOrgNm("测试公司");
        dmFinsp.setInspDate(new Date());
        dmFinsp.setStatus(1);

        service.saveOrUpdateFinsp(dmFinsp);
    }

    @Test
    public void del(){
        service.delDmFinsp("1648862039881420800");
    }

    @Test
    public void getNextActivity() throws Exception {

        //List<Map> nextActivitys = H_WorkFlowHelper.getNextActivitys(5331993);
        //System.out.println(nextActivitys);

        int i = H_WorkFlowHelper.getHideStatus();
        System.out.println(i);
    }

    @SneakyThrows
    @Test
    public void exportExcel(){
        //String finspId = "9cd9a9a0-ac8c-43a7-a840-4648ddb4047e";
        String finspId = "9209e353-84bb-41d0-9ea0-53d99ac18511";
        TemplateExportParams exportParams = new TemplateExportParams("excel/bp201801.xlsx",true,null);
        Map<Integer, Map<String,Object>> dataParams = Maps.newHashMap();

        DmFinsp dmFinsp = service.getById(finspId);
        Map<String, Object> dmFinspMap = BeanUtil.beanToMap(dmFinsp);

        List<DmFinspResult> dmFinspResults = resultService.selectResultByFinspId(finspId);
        dmFinspMap.put("resList",dmFinspResults);

        IPage<DmFinspRecord> recordIPage = recordService.selectViewRecordByFinspId(finspId);
        List<DmFinspRecord> records = recordIPage.getRecords();

        Map<String,Object> recordMap = Maps.newHashMap();
        recordMap.put("recList", records);

        dataParams.put(0,dmFinspMap);
        dataParams.put(1,recordMap);

        Workbook workbook = ExcelExportUtil.exportExcel(dataParams, exportParams);
        workbook.write(new FileOutputStream("D:\\bp.xlsx"));
        workbook.close();
    }
}