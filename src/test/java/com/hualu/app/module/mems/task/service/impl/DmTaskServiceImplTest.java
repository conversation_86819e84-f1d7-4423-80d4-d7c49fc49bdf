package com.hualu.app.module.mems.task.service.impl;

import com.hualu.app.module.mems.task.dto.DmTaskDto;
import com.hualu.app.module.mems.task.service.DmTaskService;
import com.tg.dev.api.context.CustomRequestContextHolder;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class DmTaskServiceImplTest extends BaseApplicationTest {

    @Autowired
    DmTaskService taskService;

    @Before
    public void init(){
        CustomRequestContextHolder.set("ORG_EN","ZKGS");
        CustomRequestContextHolder.setOrgId("2cad9217-2783-4460-92c5-9c32a93f505b");
    }

    @Test
    public void getNextCode() {
        DmTaskDto nextCode = taskService.getNextCode();
        System.out.println(nextCode);
    }
}