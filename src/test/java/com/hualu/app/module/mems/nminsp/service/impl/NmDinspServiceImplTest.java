package com.hualu.app.module.mems.nminsp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hualu.app.module.basedata.service.BaseLineService;
import com.hualu.app.module.mems.nminsp.dto.WorkNmDinspDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspRecord;
import com.hualu.app.module.mems.nminsp.mapper.NmDinspMapper;
import com.hualu.app.module.mems.nminsp.service.NmDinspRecordService;
import com.hualu.app.module.mems.nminsp.service.NmDinspService;
import com.tg.dev.api.core.global.exception.BaseException;
import com.tg.dev.api.core.test.BaseApplicationTest;
import lombok.Data;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

public class NmDinspServiceImplTest extends BaseApplicationTest {

    @Autowired
    NmDinspService nmDinspService;

    @Autowired
    NmDinspServiceImpl nmDinspServiceImpl;

    @Autowired
    NmDinspMapper dinspMapper;

    @Autowired
    BaseLineService lineService;

    @Test
    public void replaceProcessInstId(){
        // 一致性  2025-02，2025-03,2025-04,2025-05
        List<WorkNmDinspDto> nmDinspDtos = dinspMapper.listWorkDinspByInspDate("2025-06");
        AtomicInteger atomicInteger = new AtomicInteger(0);

        for (WorkNmDinspDto dto : nmDinspDtos) {
            System.out.println("===========================正在执行第"+atomicInteger.incrementAndGet());
            System.out.println(JSONUtil.toJsonStr(dto));
            LambdaQueryWrapper<NmDinsp> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(NmDinsp::getMntOrgId,dto.getMntOrgId())
                    .eq(NmDinsp::getFacilityCat,dto.getFacilityCat())
                    .apply("to_char(insp_date,'yyyy-MM-dd')={0}",dto.getInspDate())
                    .eq(NmDinsp::getStatus,0)
                    .eq(StrUtil.isNotBlank(dto.getCreateUserId()),NmDinsp::getCreateUserId,dto.getCreateUserId())
                    .select(NmDinsp::getDinspId,NmDinsp::getProcessinstid);

            List<NmDinsp> nmDinsps = nmDinspService.list(wrapper);
            if (CollectionUtil.isEmpty(nmDinsps)){
                continue;
            }
            Long processinstid = nmDinsps.get(0).getProcessinstid();
            if (processinstid == null){
                throw new BaseException("流程为空");
            }

            // 统一修改数据库

            List<String> dinspIds = nmDinsps.stream().map(NmDinsp::getDinspId).collect(Collectors.toList());
            System.out.println(dinspIds.size());
            ListUtil.partition(dinspIds,200).forEach(items->{
                LambdaUpdateWrapper<NmDinsp> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(NmDinsp::getDinspId,items);
                updateWrapper.set(NmDinsp::getProcessinstid,processinstid);
                nmDinspService.update(updateWrapper);
            });
        }
    }


    @Test
    public void saveDss(){
        List<NmDinsp> nmDinsps = nmDinspService.lambdaQuery()
                .eq(NmDinsp::getMntOrgId, "N000035")
                .eq(NmDinsp::getFacilityCat, "JA")
                //.gt(NmDinsp::getDssNum, 0)
                .eq(NmDinsp::getStatus,1)
                /*.inSql(NmDinsp::getProcessinstid,"select inst.PROCESSINSTID from bps.WFACTIVITYINST act join bps.WFPROCESSINST inst on act.PROCESSINSTID = inst.PROCESSINSTID and inst.PROCESSDEFNAME='gdcg.emdc.mems.dm.NDInspWorkFlow' " +
                        "    where ACTIVITYDEFID = 'finishActivity' and to_char(act.ENDTIME,'yyyy-MM-dd') = '2025-07-28'")*/
                .inSql(NmDinsp::getProcessinstid,"select inst.PROCESSINSTID from bps.WFACTIVITYINST act join bps.WFPROCESSINST inst on act.PROCESSINSTID = inst.PROCESSINSTID and inst.PROCESSDEFNAME='gdcg.emdc.mems.dm.NDInspWorkFlow' " +
                        "    where ACTIVITYDEFID = 'finishActivity'")
                .list();
        System.out.println(nmDinsps.size());
        //List<NmDinsp> nmDinsps = nmDinspService.lambdaQuery().eq(NmDinsp::getDinspId,"1942128909244043264").list();
        nmDinsps.forEach(item->{
            nmDinspServiceImpl.savedoEvent(item.getProcessinstid(), true,"","11");
        });
        System.out.println(nmDinsps.size());
    }



    @Test
    public void selectLineName(){
        String lineName = lineService.getLineName("00S51");
        System.out.println(lineName);
    }
}