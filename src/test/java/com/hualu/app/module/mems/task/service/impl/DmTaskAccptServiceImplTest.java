package com.hualu.app.module.mems.task.service.impl;

import com.hualu.app.module.mems.mpc.service.MpcMpitemService;
import com.hualu.app.module.mems.task.dto.accpt.DmTaskAccptDetailDto;
import com.hualu.app.module.mems.task.service.DmTaskAccptService;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class DmTaskAccptServiceImplTest extends BaseApplicationTest {

    @Autowired
    DmTaskAccptService accptService;

    @Autowired
    MpcMpitemService mpitemService;

    @Test
    public void selectAccptDetails() {
        List<DmTaskAccptDetailDto> detailDtos = accptService.selectAccptDetails("98ebe760-c81a-483e-8a08-a99fda3d23fd", "06b158d7-c6d9-4bd6-b5f7-fa48208f5e69");
        System.out.println(detailDtos);
    }



}