package com.hualu.app.module.monitor.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.google.common.collect.Maps;
import com.hualu.app.module.monitor.entity.AppExtraction;
import com.hualu.app.module.monitor.service.AppExtractionService;
import com.tg.dev.api.core.test.BaseApplicationTest;
import org.apache.poi.ss.usermodel.Workbook;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public class AppExtractionServiceImplTest extends BaseApplicationTest {

    @Autowired
    AppExtractionService service;

    @Test
    public void queryappusage() throws IOException {

        Map<String, String> params = Maps.newHashMap();
        params.put("startDate","2023-01-01");
        params.put("orgcode","N000002");
        List queryappusage = service.queryappusage(params);
        ExportParams exportParams = new ExportParams("移动端使用情况","sheet1");
        Workbook workbook = ExcelExportUtil.exportBigExcel(exportParams, AppExtraction.class, new IExcelExportServer() {
            @Override
            public List<Object> selectListForExcelExport(Object o, int i) {
                if (i == 2){
                    return Lists.newArrayList();
                }
                return queryappusage;
            }
        }, 500);
        workbook.write(new FileOutputStream("D:/移动端使用情况.xlsx"));
        workbook.close();
    }


}