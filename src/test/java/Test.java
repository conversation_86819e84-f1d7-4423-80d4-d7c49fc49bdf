import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.data.style.Style;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Function;
import com.hualu.app.module.mems.finsp.dto.export.PcProjectExportDto;
import com.hualu.app.module.mems.nminsp.entity.NmDinsp;
import com.hualu.app.module.mems.nminsp.entity.NmDinspGps;
import com.hualu.app.module.mems.nminsp.entity.NmFinsp;
import com.hualu.app.module.mems.nminsp.plugin.ExistingLoopRowTableRenderPolicy;
import com.hualu.app.module.mems.nminsp.util.H_WordHelper;
import com.hualu.app.utils.mems.H_PcFinspCulvertHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.assertj.core.util.Lists;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public class Test {

    public static void main(String[] args) throws Exception {
        NmDinsp bean = new NmDinsp();
        bean.setInspDate(new Date(2025,4,2));
        Date defaultDate = Optional.ofNullable(bean.getInspDate())
                .orElseGet(() -> {
                    Date now = new Date();
                    bean.setInspDate(now);  // 设置值
                    return now;             // 返回Date类型
                });

        String format = DateUtil.format(bean.getInspDate(), "yyyy-MM-dd");
        System.out.println(format);

        String finspCode = "JCJC-BP-NYLY-20250606-0032";

        int lastIndex = finspCode.lastIndexOf("-");
        String codePrefix = finspCode.substring(0, lastIndex + 1);
        String paddedNumber = StrUtil.padPre(String.valueOf(1), 4, '0');
        System.out.println(codePrefix+paddedNumber);

    }

    private static void mergeWord() throws IOException {

        Configure config = Configure.builder()
                .bind("recList", new ExistingLoopRowTableRenderPolicy())
                .build();

        XWPFTemplate compile = XWPFTemplate.compile("D:\\ql_mb.docx",config);
        List<XWPFTable> allTables = compile.getXWPFDocument().getAllTables();
        for (XWPFTable table : allTables) {
            H_WordHelper.dynamicMerge(table, Lists.newArrayList(0,2));
        }
        compile.writeAndClose(new FileOutputStream("D:\\word.docx"));
    }


    private void functionTest(){
        Function<NmFinsp, String> id = NmFinsp::getFinspId;
        System.out.println(id);
    }

    private static void exportHdWord() throws IOException {
        ClassPathResource classPathResource = new ClassPathResource("excel/pcFinsp/culvert.docx");
        PcProjectExportDto pcProjectExportDto = new PcProjectExportDto()
                .setAttr1("是").setLineName("京港澳高速").setLineCode("G4").setInspPerson("张三");
        H_PcFinspCulvertHelper.initWord(pcProjectExportDto);
        Map xxMap = BeanUtil.toBean(pcProjectExportDto, Map.class);
        //ConfigureBuilder builder = Configure.builder().useSpringEL(false);
        ConfigureBuilder builder = Configure.builder();
        XWPFTemplate template = XWPFTemplate.compile(new File(classPathResource.getURL().getPath()), builder.build()).render(xxMap);
        try(OutputStream outputStream = Files.newOutputStream(new File("D:\\bridge-szz1111.docx").toPath())){
            template.writeAndClose(outputStream);
        }
    }

    private static Object getRadioStr(boolean isTrue){
        if(isTrue){
            return new TextRenderData("8",new Style("Wingdings 2",12));
        }
        return "○";
    }

    private static Object getCheckStr(boolean isTrue){
        if(isTrue){
            return new TextRenderData("R",new Style("Wingdings 2",12));
        }
        return new TextRenderData("T",new Style("Wingdings 2",12));
    }
}
