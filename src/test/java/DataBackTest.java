import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hualu.app.module.backup.InspectionDTO;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

public class DataBackTest {

    // 定义过滤条件常量
    private static final String BASE_DIR = "C:\\syslog\\highway-mems\\";
    private static final String LOG_INFO = "log_info";
    private static final String RETURN_KEY = "返回：";
    private static final String[] FILTER_KEYS = {"dinspId", "structId", "structName", "返回"};

    public static void main(String[] args) {
        try {
            // 递归扫描并过滤路径包含 "log_info" 的文件
            List<File> files = FileUtil.loopFiles(BASE_DIR, file -> file.getAbsolutePath().toLowerCase().contains(LOG_INFO));
            System.out.println(files.size());
            // 使用 Stream API 处理文件
            Set<InspectionDTO> all = files.stream()
                    .filter(file -> file.getName().contains(LOG_INFO))
                    .peek(file -> System.out.println("正在解析文件: " + file.getAbsolutePath()))
                    .flatMap(file -> FileUtil.readLines(file, CharsetUtil.CHARSET_UTF_8).stream())
                    .filter(DataBackTest::isValidLine)
                    .map(line -> StrUtil.subAfter(line, RETURN_KEY, false))
                    .flatMap(json -> parseInspectionData(json).stream())
                    .collect(Collectors.toSet());
            System.out.println(all.size());
        } catch (Exception e) {
            System.err.println("处理文件时发生错误: " + e.getMessage());
        }
    }

    // 检查行是否有效
    private static boolean isValidLine(String line) {
        return StrUtil.isNotBlank(line) && Arrays.stream(FILTER_KEYS).allMatch(line::contains);
    }

    public static Set<InspectionDTO> parseInspectionData(String jsonStr) {
        Set<InspectionDTO> result = new HashSet<>();
        try {
            // 解析JSON根对象
            JSONObject rootObj = JSONUtil.parseObj(jsonStr);
            // 定位到data数组
            JSONArray dataArray = rootObj.getByPath("data.data", JSONArray.class);
            if (dataArray != null) {
                // 遍历处理每条数据
                for (Object item : dataArray) {
                    JSONObject obj = JSONUtil.parseObj(item);
                    InspectionDTO dto = new InspectionDTO();

                    // 提取基础字段
                    dto.setMntOrgId(obj.getStr("mntOrgId"));
                    dto.setDinspId(obj.getStr("dinspId"));
                    dto.setStructId(obj.getStr("structId"));
                    dto.setStructName(obj.getStr("structName"));
                    dto.setFacilityCat(obj.getStr("facilityCat"));
                    dto.setProcessinstid(obj.getLong("processinstid"));

                    // 处理时间戳转换
                    Long inspDateLong = obj.getLong("inspDate");
                    if (inspDateLong != null) {
                        dto.setInspDate(new Date(inspDateLong));
                    }

                    // 获取创建人ID
                    dto.setCreateUserId(obj.getByPath("createUserId", String.class));

                    result.add(dto);
                }
            }
        } catch (Exception e) {
            System.err.println("解析 JSON 数据时发生错误: " + e.getMessage());
        }
        return result;
    }
}
