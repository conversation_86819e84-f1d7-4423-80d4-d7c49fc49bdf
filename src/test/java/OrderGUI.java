import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import javax.swing.*;
import javax.swing.Timer;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.text.DefaultCaret;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 病害数据同步工具GUI
 */
public class OrderGUI extends JFrame {
    private JList<RouteItem> routeList;
    private DefaultListModel<RouteItem> routeListModel; // 主列表模型
    private DefaultListModel<RouteItem> filteredListModel; // 过滤后的列表模型
    private JScrollPane routeScrollPane;
    private JTextField startDateField;
    private JTextField endDateField;
    private JTextField searchField; // 搜索框
    private JButton syncButton;
    private JProgressBar progressBar;
    private JTextArea logTextArea;
    private JButton clearLogButton;
    private JButton exportLogButton;
    
    private String baseUrl = "http://127.0.0.1:8182"; // 默认服务器地址
    private boolean isSyncing = false;
    private List<RouteItem> routes = new ArrayList<>();
    private ExecutorService executor;
    
    // 用户凭证
    private static final String USER_CODE = "yfzx-huangzj";

    /**
     * 路段下拉框项
     */
    static class RouteItem {
        private String id;
        private String name;

        public RouteItem(String id, String name) {
            this.id = id;
            this.name = name;
        }

        public String getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    /**
     * 构造函数
     */
    public OrderGUI() {
        setTitle("病害数据同步工具");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(900, 700);
        setLocationRelativeTo(null);
        
        initComponents();
        loadRoutes();
    }

    /**
     * 初始化组件
     */
    private void initComponents() {
        // 设置整体字体更美观
        setFont(new Font("Microsoft YaHei", Font.PLAIN, 14));
        
        // 设置窗口背景色
        getContentPane().setBackground(new Color(240, 240, 245));
        
        // 主面板
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout(15, 15));
        mainPanel.setBorder(new EmptyBorder(15, 15, 15, 15));
        mainPanel.setBackground(new Color(240, 240, 245)); // 浅灰背景
        setContentPane(mainPanel);

        // 顶部参数面板
        JPanel paramsPanel = new JPanel();
        paramsPanel.setLayout(new BoxLayout(paramsPanel, BoxLayout.Y_AXIS));
        paramsPanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createTitledBorder(
                    BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(180, 180, 180), 1),
                        BorderFactory.createEmptyBorder(1, 1, 1, 1)
                    ),
                    "同步参数设置", 
                    TitledBorder.LEFT, 
                    TitledBorder.TOP,
                    new Font("Microsoft YaHei", Font.BOLD, 14),
                    new Color(60, 60, 60)
                ),
                BorderFactory.createEmptyBorder(15, 15, 15, 15)));
        paramsPanel.setBackground(new Color(250, 250, 255)); // 白色背景
        
        // 路段选择面板 - 使用卡片式设计
        JPanel routePanel = new JPanel(new BorderLayout(5, 5));
        routePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createEtchedBorder(),
            BorderFactory.createEmptyBorder(8, 8, 8, 8)
        ));
        routePanel.setBackground(new Color(245, 245, 250)); // 浅灰蓝色背景
        
        // 路段选择标题和搜索面板
        JPanel routeHeaderPanel = new JPanel();
        routeHeaderPanel.setLayout(new BoxLayout(routeHeaderPanel, BoxLayout.Y_AXIS));
        routeHeaderPanel.setOpaque(false);
        
        // 路段选择标题面板
        JPanel routeLabelPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        routeLabelPanel.setOpaque(false);
        JLabel routeLabel = new JLabel("路段选择");
        routeLabel.setFont(new Font("Microsoft YaHei", Font.BOLD, 13));
        routeLabel.setForeground(new Color(70, 70, 70));
        routeLabelPanel.add(routeLabel);
        routeHeaderPanel.add(routeLabelPanel);
        
        // 搜索框面板
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 2, 2));
        searchPanel.setOpaque(false);
        searchPanel.setToolTipText("输入路段名称进行搜索，不影响已选中项");
        
        // 搜索图标
        JLabel searchIcon = new JLabel("\uD83D\uDD0D"); // Unicode放大镜符号
        searchIcon.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        searchPanel.add(searchIcon);
        
        // 搜索文本框
        searchField = new JTextField(10); // 使用类成员变量
        searchField.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        searchField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(180, 180, 180)),
            BorderFactory.createEmptyBorder(2, 5, 2, 5)
        ));
        searchField.setPreferredSize(new Dimension(80, 22));
        searchPanel.add(searchField);
        
        // 清除搜索按钮
        JButton clearSearchButton = new JButton("×");
        clearSearchButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        clearSearchButton.setMargin(new Insets(0, 2, 0, 2));
        clearSearchButton.setPreferredSize(new Dimension(20, 22));
        clearSearchButton.setToolTipText("清除搜索");
        clearSearchButton.addActionListener(e -> {
            searchField.setText("");
            routeList.setModel(routeListModel); // 恢复原始列表
            routeList.requestFocus(); // 将焦点转回列表
        });
        searchPanel.add(clearSearchButton);
        
        routeHeaderPanel.add(searchPanel);
        routeHeaderPanel.add(Box.createVerticalStrut(3));
        routePanel.add(routeHeaderPanel, BorderLayout.NORTH);
        
        // 路段列表和模型
        routeListModel = new DefaultListModel<>(); // 使用类成员变量
        filteredListModel = new DefaultListModel<>(); // 使用类成员变量
        routeList = new JList<>(routeListModel);
        
        // 确保设置为多选模式
        routeList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        
        // 为搜索框添加监听器
        searchField.getDocument().addDocumentListener(new javax.swing.event.DocumentListener() {
            @Override
            public void insertUpdate(javax.swing.event.DocumentEvent e) {
                filterRouteList();
            }

            @Override
            public void removeUpdate(javax.swing.event.DocumentEvent e) {
                filterRouteList();
            }

            @Override
            public void changedUpdate(javax.swing.event.DocumentEvent e) {
                filterRouteList();
            }
            
            // 过滤路段列表
            private void filterRouteList() {
                String searchText = searchField.getText().toLowerCase().trim();
                
                // 保存当前选中的项
                int[] selectedIndices = routeList.getSelectedIndices();
                List<RouteItem> selectedItems = new ArrayList<>();
                for (int index : selectedIndices) {
                    if (index < routeList.getModel().getSize()) {
                        selectedItems.add((RouteItem) routeList.getModel().getElementAt(index));
                    }
                }
                
                // 清空并重新填充过滤后的列表
                filteredListModel.clear();
                
                for (int i = 0; i < routeListModel.getSize(); i++) {
                    RouteItem item = routeListModel.getElementAt(i);
                    if (item.getName().toLowerCase().contains(searchText)) {
                        filteredListModel.addElement(item);
                    }
                }
                
                // 应用过滤后的模型
                routeList.setModel(filteredListModel);
                
                // 恢复之前选中的项（如果它们仍然在过滤后的列表中）
                List<Integer> newSelectedIndices = new ArrayList<>();
                for (int i = 0; i < filteredListModel.getSize(); i++) {
                    RouteItem item = filteredListModel.getElementAt(i);
                    if (selectedItems.contains(item)) {
                        newSelectedIndices.add(i);
                    }
                }
                
                int[] indices = newSelectedIndices.stream().mapToInt(i -> i).toArray();
                routeList.setSelectedIndices(indices);
                
                // 如果搜索框为空，恢复完整列表
                if (searchText.isEmpty()) {
                    routeList.setModel(routeListModel);
                    routeList.setSelectedIndices(selectedIndices);
                }
            }
        });
        routeList.setVisibleRowCount(6);
        routeList.setFont(new Font("Microsoft YaHei", Font.PLAIN, 13));
        // 设置选中项的背景色和前景色
        routeList.setSelectionBackground(new Color(135, 206, 250)); // 浅蓝色背景
        routeList.setSelectionForeground(Color.BLACK); // 黑色文字
        routeList.setCellRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value, int index, boolean isSelected, boolean cellHasFocus) {
                Component comp = super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
                setPreferredSize(new Dimension(getWidth(), 25));
                // 为选中项添加特殊标记
                if (isSelected) {
                    setText("✓ " + value.toString());
                }
                
                // 设置边框和内边距
                setBorder(BorderFactory.createEmptyBorder(2, 8, 2, 8));
                
                // 交替行背景色
                if (!isSelected) {
                    setBackground(index % 2 == 0 ? new Color(250, 250, 255) : new Color(240, 240, 245));
                }
                
                return comp;
            }
        });
        
        // 路段列表滚动面板
        routeScrollPane = new JScrollPane(routeList);
        routeScrollPane.setBorder(BorderFactory.createLineBorder(new Color(200, 200, 200)));
        routeScrollPane.setPreferredSize(new Dimension(180, 150));
        JPanel listContainerPanel = new JPanel(new BorderLayout());
        listContainerPanel.setOpaque(false);
        listContainerPanel.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        listContainerPanel.add(routeScrollPane, BorderLayout.CENTER);
        routePanel.add(listContainerPanel, BorderLayout.CENTER);
        
        // 路段选择提示
        JPanel helpPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        helpPanel.setOpaque(false);
        JLabel selectHelp = new JLabel("(按住Ctrl键可多选路段)");
        selectHelp.setFont(new Font("Microsoft YaHei", Font.ITALIC, 12));
        selectHelp.setForeground(new Color(100, 100, 150));
        helpPanel.add(selectHelp);
        routePanel.add(helpPanel, BorderLayout.SOUTH);
        
        // 日期选择面板 - 更加紧凑
        JPanel datePanel = new JPanel();
        datePanel.setLayout(new BoxLayout(datePanel, BoxLayout.Y_AXIS));
        datePanel.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createEtchedBorder(),
            BorderFactory.createEmptyBorder(5, 10, 5, 10) // 减少内边距
        ));
        datePanel.setBackground(new Color(245, 245, 250)); // 浅灰蓝色背景
        
        // 日期面板标题 - 更小更紧凑
        JLabel datePanelTitle = new JLabel("日期范围");
        datePanelTitle.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        datePanelTitle.setForeground(new Color(70, 70, 70));
        datePanelTitle.setAlignmentX(Component.CENTER_ALIGNMENT);
        datePanel.add(datePanelTitle);
        datePanel.add(Box.createVerticalStrut(3)); // 减少标题和输入框之间的间距
        
        // 日期输入区域面板 - 更紧凑的间距
        JPanel dateInputsPanel = new JPanel(new GridLayout(2, 1, 0, 3)); // 减少行间距
        dateInputsPanel.setOpaque(false);
        
        // 开始日期
        JPanel startDatePanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 5, 0));
        startDatePanel.setOpaque(false);
        JLabel startDateLabel = new JLabel("开始日期:");
        startDateLabel.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        startDatePanel.add(startDateLabel);
        
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        startDateField = new JTextField(dateFormat.format(new Date()));
        startDateField.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        startDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(180, 180, 180)),
            BorderFactory.createEmptyBorder(3, 5, 3, 5)));
        startDateField.setPreferredSize(new Dimension(90, 24)); // 设置日期输入框的固定大小
        startDateField.setColumns(10); // 限制输入框显示的字符数
        startDatePanel.add(startDateField);
        dateInputsPanel.add(startDatePanel);
        
        // 结束日期
        JPanel endDatePanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 5, 0));
        endDatePanel.setOpaque(false);
        JLabel endDateLabel = new JLabel("结束日期:");
        endDateLabel.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        endDatePanel.add(endDateLabel);
        
        endDateField = new JTextField(dateFormat.format(new Date()));
        endDateField.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        endDateField.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(180, 180, 180)),
            BorderFactory.createEmptyBorder(3, 5, 3, 5)));
        endDateField.setPreferredSize(new Dimension(90, 24)); // 设置日期输入框的固定大小
        endDateField.setColumns(10); // 限制输入框显示的字符数
        endDatePanel.add(endDateField);
        dateInputsPanel.add(endDatePanel);
        
        datePanel.add(dateInputsPanel);
        
        // 布局顶部面板
        JPanel topPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.BOTH;
        
        // 路段选择区
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.weightx = 0.4; // 减小路段选择区的比例
        gbc.weighty = 1.0;
        topPanel.add(routePanel, gbc);
        
        // 右侧面板 - 紧凑布局
        JPanel rightPanel = new JPanel(new BorderLayout(0, 0)); // 使用BorderLayout减少空白
        
        // 右侧内容面板 - 垂直排列日期和按钮
        JPanel rightContentPanel = new JPanel();
        rightContentPanel.setLayout(new BoxLayout(rightContentPanel, BoxLayout.Y_AXIS));
        
        // 日期面板容器 - 没有额外边距
        JPanel dateContainerPanel = new JPanel(new FlowLayout(FlowLayout.CENTER, 0, 0));
        dateContainerPanel.add(datePanel);
        dateContainerPanel.setBorder(BorderFactory.createEmptyBorder(0, 0, 3, 0)); // 减少底部边距
        rightContentPanel.add(dateContainerPanel);
        
        // 按钮面板 - 紧凑布局
        JPanel buttonPanel = new JPanel();
        buttonPanel.setLayout(new BoxLayout(buttonPanel, BoxLayout.Y_AXIS));
        buttonPanel.setBorder(BorderFactory.createEmptyBorder(0, 10, 0, 10)); // 减小边距
        
        // 按钮标题 - 更小
        JLabel buttonTitle = new JLabel("操作选项");
        buttonTitle.setFont(new Font("Microsoft YaHei", Font.BOLD, 12));
        buttonTitle.setForeground(new Color(70, 70, 70));
        buttonTitle.setAlignmentX(Component.CENTER_ALIGNMENT);
        buttonPanel.add(buttonTitle);
        buttonPanel.add(Box.createVerticalStrut(3)); // 减少间距
        
        // 按钮容器 - 紧凑布局
        JPanel buttonsContainer = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 3)); // 减少内边距
        buttonsContainer.setOpaque(false);
        
        rightContentPanel.add(buttonPanel);
        rightPanel.add(rightContentPanel, BorderLayout.CENTER);
        
        // 同步病害按钮 - 使用圆角按钮
        syncButton = new JButton("同步病害") {
            {
                setContentAreaFilled(false);
            }
            
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                if (getModel().isPressed()) {
                    g2.setColor(new Color(60, 110, 150));
                } else if (getModel().isRollover()) {
                    g2.setColor(new Color(80, 140, 190));
                } else {
                    g2.setColor(new Color(70, 130, 180));
                }
                g2.fillRoundRect(0, 0, getWidth(), getHeight(), 10, 10);
                super.paintComponent(g);
                g2.dispose();
            }
        };
        syncButton.setPreferredSize(new Dimension(90, 28)); // 减小按钮大小
        syncButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 12)); // 减小字体
        syncButton.setForeground(Color.WHITE);
        syncButton.setFocusPainted(false);
        syncButton.setBorderPainted(false);
        syncButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        syncButton.addActionListener(this::onSyncButtonClick);
        buttonsContainer.add(syncButton);
        
        // 生成检查单按钮 - 使用圆角按钮
        JButton generateReportButton = new JButton("生成检查单") {
            {
                setContentAreaFilled(false);
            }
            
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                if (getModel().isPressed()) {
                    g2.setColor(new Color(50, 150, 100));
                } else if (getModel().isRollover()) {
                    g2.setColor(new Color(70, 190, 120));
                } else {
                    g2.setColor(new Color(60, 179, 113));
                }
                g2.fillRoundRect(0, 0, getWidth(), getHeight(), 10, 10);
                super.paintComponent(g);
                g2.dispose();
            }
        };
        generateReportButton.setPreferredSize(new Dimension(90, 28)); // 减小按钮大小
        generateReportButton.setFont(new Font("Microsoft YaHei", Font.BOLD, 12)); // 减小字体
        generateReportButton.setForeground(Color.WHITE);
        generateReportButton.setFocusPainted(false);
        generateReportButton.setBorderPainted(false);
        generateReportButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        generateReportButton.addActionListener(this::onGenerateReportButtonClick);
        buttonsContainer.add(generateReportButton);
        
        buttonPanel.add(buttonsContainer);
        // 按钮面板已经在前面添加到了rightContentPanel中
        
        gbc.gridx = 1;
        gbc.gridy = 0;
        gbc.weightx = 0.6; // 增加右侧区域的比例
        topPanel.add(rightPanel, gbc);
        
        paramsPanel.add(topPanel);
        mainPanel.add(paramsPanel, BorderLayout.NORTH);
        
        // 进度条面板
        JPanel progressPanel = new JPanel(new BorderLayout());
        progressPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 0, 0));
        progressPanel.setOpaque(false);
        
        // 进度条
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("就绪");
        progressBar.setPreferredSize(new Dimension(getWidth(), 25));
        progressBar.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0));
        progressBar.setForeground(new Color(70, 130, 180)); // 进度条颜色
        progressBar.setBackground(new Color(240, 240, 245)); // 背景色
        progressBar.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        progressPanel.add(progressBar, BorderLayout.CENTER);
        
        mainPanel.add(progressPanel, BorderLayout.SOUTH);
        
        // 日志面板
        JPanel logPanel = new JPanel(new BorderLayout(5, 5));
        logPanel.setBorder(BorderFactory.createCompoundBorder(
                BorderFactory.createTitledBorder(
                    BorderFactory.createCompoundBorder(
                        BorderFactory.createLineBorder(new Color(180, 180, 180), 1),
                        BorderFactory.createEmptyBorder(1, 1, 1, 1)
                    ),
                    "同步日志", 
                    TitledBorder.LEFT, 
                    TitledBorder.TOP,
                    new Font("Microsoft YaHei", Font.BOLD, 14),
                    new Color(60, 60, 60)
                ),
                BorderFactory.createEmptyBorder(10, 10, 10, 10)));
        logPanel.setBackground(new Color(250, 250, 255)); // 白色背景
        
        // 日志文本区域
        logTextArea = new JTextArea();
        logTextArea.setEditable(false);
        logTextArea.setFont(new Font("Microsoft YaHei", Font.PLAIN, 13));
        logTextArea.setBackground(new Color(252, 252, 255));
        logTextArea.setForeground(new Color(50, 50, 50));
        logTextArea.setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        logTextArea.setMargin(new Insets(5, 5, 5, 5));
        
        // 自动滚动
        DefaultCaret logCaret = (DefaultCaret)logTextArea.getCaret();
        logCaret.setUpdatePolicy(DefaultCaret.ALWAYS_UPDATE);
        
        // 滚动面板
        JScrollPane logScrollPane = new JScrollPane(logTextArea);
        logScrollPane.setBorder(BorderFactory.createLineBorder(new Color(200, 200, 200)));
        logScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        logPanel.add(logScrollPane, BorderLayout.CENTER);
        
        // 日志按钮面板
        JPanel logButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT, 10, 5));
        logButtonPanel.setOpaque(false);
        
        // 清空日志按钮 - 使用圆角按钮
        clearLogButton = new JButton("清空日志") {
            {
                setContentAreaFilled(false);
            }
            
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                if (getModel().isPressed()) {
                    g2.setColor(new Color(200, 200, 200));
                } else if (getModel().isRollover()) {
                    g2.setColor(new Color(230, 230, 230));
                } else {
                    g2.setColor(new Color(220, 220, 220));
                }
                g2.fillRoundRect(0, 0, getWidth(), getHeight(), 8, 8);
                super.paintComponent(g);
                g2.dispose();
            }
        };
        clearLogButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        clearLogButton.setForeground(new Color(60, 60, 60));
        clearLogButton.setFocusPainted(false);
        clearLogButton.setBorderPainted(false);
        clearLogButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        clearLogButton.setPreferredSize(new Dimension(90, 28));
        clearLogButton.addActionListener(e -> logTextArea.setText(""));
        logButtonPanel.add(clearLogButton);
        
        // 导出日志按钮 - 使用圆角按钮
        exportLogButton = new JButton("导出日志") {
            {
                setContentAreaFilled(false);
            }
            
            @Override
            protected void paintComponent(Graphics g) {
                Graphics2D g2 = (Graphics2D) g.create();
                g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                if (getModel().isPressed()) {
                    g2.setColor(new Color(200, 200, 200));
                } else if (getModel().isRollover()) {
                    g2.setColor(new Color(230, 230, 230));
                } else {
                    g2.setColor(new Color(220, 220, 220));
                }
                g2.fillRoundRect(0, 0, getWidth(), getHeight(), 8, 8);
                super.paintComponent(g);
                g2.dispose();
            }
        };
        exportLogButton.setFont(new Font("Microsoft YaHei", Font.PLAIN, 12));
        exportLogButton.setForeground(new Color(60, 60, 60));
        exportLogButton.setFocusPainted(false);
        exportLogButton.setBorderPainted(false);
        exportLogButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        exportLogButton.setPreferredSize(new Dimension(90, 28));
        exportLogButton.addActionListener(this::onExportLogButtonClick);
        logButtonPanel.add(exportLogButton);
        
        logPanel.add(logButtonPanel, BorderLayout.SOUTH);
        mainPanel.add(logPanel, BorderLayout.CENTER);
    }

    /**
     * 加载路段数据
     */
    private void loadRoutes() {
        loadRoutesWithRetry(0);
    }
    
    /**
     * 带重试机制的路段数据加载
     * @param retryCount 当前重试次数
     */
    private void loadRoutesWithRetry(int retryCount) {
        final int MAX_RETRY = 5; // 最大重试次数
        final int RETRY_DELAY = 3000; // 重试延迟，单位毫秒
        
        // 在状态栏显示正在加载
        progressBar.setString("正在加载路段数据...");
        
        new SwingWorker<List<RouteItem>, Void>() {
            @Override
            protected List<RouteItem> doInBackground() throws Exception {
                // 调试：记录加载开始
                System.out.println("开始加载路段数据，尝试次数: " + (retryCount + 1));
                List<RouteItem> result = new ArrayList<>();
                
                // 调用接口获取路段数据
                try {
                    // 调试：记录请求URL
                    String requestUrl = baseUrl + "/data/sync/queryRoute";
                    System.out.println("请求URL: " + requestUrl);
                    
                    URL url = new URL(requestUrl);
                    HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                    conn.setRequestMethod("GET");
                    conn.setRequestProperty("userCode", USER_CODE);
                    
                    // 调试：记录请求头
                    System.out.println("请求头 userCode: " + USER_CODE);
                    
                    // 设置连接和读取超时
                    conn.setConnectTimeout(15000);  // 15秒连接超时
                    conn.setReadTimeout(30000);     // 30秒读取超时
                    conn.connect();
                    
                    // 调试：记录响应码
                    int responseCode = conn.getResponseCode();
                    System.out.println("响应状态码: " + responseCode);
                    
                    BufferedReader reader;
                    if (responseCode >= 200 && responseCode < 300) {
                        reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
                    } else {
                        reader = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8"));
                    }
                    
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    // 调试：记录完整响应
                    String responseStr = response.toString();
                    System.out.println("API响应: " + responseStr);
                    
                    // 解析响应数据
                    if (responseStr.isEmpty()) {
                        throw new Exception("API返回空响应");
                    }

                    JSONObject jsonObject = JSON.parseObject(responseStr);
                    Integer code = jsonObject.getInteger("code");
                    System.out.println("响应code: " + code);
                    
                    if (code != null && code == 1) { // 修改：返回值的成功代码为1
                        JSONArray data = jsonObject.getJSONArray("data");
                        if (data != null) {
                            System.out.println("获取到路段数量: " + data.size());
                            
                            for (int i = 0; i < data.size(); i++) {
                                JSONObject route = data.getJSONObject(i);
                                // 修改：使用routeName作为键和值
                                String routeName = route.getString("routeName");
                                System.out.println("解析到路段: " + routeName);
                                
                                if (!StringUtils.isEmpty(routeName)) {
                                    result.add(new RouteItem(routeName, routeName));
                                }
                            }
                            System.out.println("成功添加路段数量: " + result.size());
                        } else {
                            System.out.println("响应data为null");
                        }
                    } else {
                        // 获取错误信息
                        String errorMsg = jsonObject.getString("message");
                        System.out.println("API返回错误: " + errorMsg);
                        throw new Exception("加载路段数据失败: " + errorMsg); // 修改：使用message字段
                    }
                } catch (Exception e) {
                    // 调试：记录详细异常信息
                    System.out.println("加载路段异常: " + e.getMessage());
                    e.printStackTrace();
                    throw e;
                }
                
                return result;
            }

            @Override
            protected void done() {
                try {
                    routes = get();
                    System.out.println("获取到路段数量: " + routes.size());
                    
                    // 在EDT线程中更新UI
                    SwingUtilities.invokeLater(() -> {
                        // 先保存对主要列表模型的引用，因为routeList.getModel()在搜索过滤时可能返回过滤后的模型
                        routeListModel.clear();
                        
                        // 检查JList和模型状态
                        System.out.println("清空后列表模型大小: " + routeListModel.getSize());
                        
                        // 添加所有路段数据到主列表模型
                        for (RouteItem route : routes) {
                            routeListModel.addElement(route);
                            System.out.println("添加路段到UI: " + route.getName());
                        }
                        
                        // 再次检查数据
                        System.out.println("添加后列表模型大小: " + routeListModel.getSize());
                        
                        // 确保列表使用主列表模型（未过滤）
                        routeList.setModel(routeListModel);
                        System.out.println("JList显示数量: " + routeList.getModel().getSize());
                        
                        // 强制UI刷新
                        routeScrollPane.revalidate();
                        routeScrollPane.repaint();
                    });
                    
                    // 更新状态栏显示加载成功
                    progressBar.setString("就绪 (已加载 " + routes.size() + " 条路段数据)");
                } catch (Exception e) {
                    // 调试：记录异常
                    System.out.println("处理路段数据异常: " + e.getMessage());
                    e.printStackTrace();
                    
                    // 如果加载失败，尝试重试
                    if (retryCount < MAX_RETRY) {
                        // 状态栏显示重试信息
                        progressBar.setString("路段数据加载失败，" + (RETRY_DELAY/1000) + "秒后重试 (" + (retryCount + 1) + "/" + MAX_RETRY + ")");
                        
                        // 延迟一段时间后重试
                        Timer timer = new Timer(RETRY_DELAY, event -> {
                            progressBar.setString("正在重试加载路段数据...");
                            loadRoutesWithRetry(retryCount + 1);
                        });
                        timer.setRepeats(false);
                        timer.start();
                    } else {
                        // 达到最大重试次数
                        progressBar.setString("路段数据加载失败，请检查网络连接");
                    }
                }
            }
        }.execute();
    }

    /**
     * 同步按钮点击事件
     */
    private void onSyncButtonClick(ActionEvent e) {
        if (isSyncing) {
            // 停止同步
            stopSync();
        } else {
            // 开始同步
            startSync();
        }
    }

    /**
     * 开始同步
     */
    private void startSync() {
        List<RouteItem> selectedRoutes = routeList.getSelectedValuesList();
        if (selectedRoutes.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请选择至少一个路段", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String startDate = startDateField.getText();
        String endDate = endDateField.getText();
        
        if (startDate.isEmpty() || endDate.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入开始日期和结束日期", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            
            if (start.isAfter(end)) {
                JOptionPane.showMessageDialog(this, "开始日期不能大于结束日期", "提示", JOptionPane.WARNING_MESSAGE);
                return;
            }
            
            // 准备执行同步
            syncButton.setText("停止同步");
            progressBar.setValue(0);
            progressBar.setString("准备同步病害...");
            isSyncing = true;
            
            log("开始同步病害数据");
            log("选择的路段: " + selectedRoutes.stream().map(RouteItem::getName).collect(Collectors.joining(", ")));
            log("日期区间: " + startDate + " 至 " + endDate);
            
            // 计算总任务数（路段数 * 日期数）
            List<String> dateRange = getDateRange(start, end);
            int totalTasks = selectedRoutes.size() * dateRange.size();
            AtomicInteger completedTasks = new AtomicInteger(0);
            AtomicInteger totalSyncCount = new AtomicInteger(0); // 添加总同步数量计数器
            
            // 创建单线程执行器，确保串行执行
            executor = Executors.newSingleThreadExecutor();

            // 创建任务列表，用于串行执行
            List<Runnable> syncTasks = new ArrayList<>();

            // 为每个路段和日期创建同步任务
            for (RouteItem route : selectedRoutes) {
                for (String date : dateRange) {
                    final String routeName = route.getName();
                    final String currentDate = date;

                    syncTasks.add(() -> {
                        try {
                            log("同步 " + routeName + " 的 " + currentDate + " 数据...");
                            
                            // URL编码参数
                            String encodedRouteName = URLEncoder.encode(routeName, "UTF-8");
                            String encodedDate = URLEncoder.encode(currentDate, "UTF-8");
                            
                            // 构建请求URL
                            String requestUrl = baseUrl + "/data/sync/syncDss?dateStr=" + encodedDate + "&routeName=" + encodedRouteName;
                            System.out.println("同步请求URL: " + requestUrl);
                            
                            URL url = new URL(requestUrl);
                            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                            conn.setRequestMethod("GET");
                            conn.setRequestProperty("userCode", USER_CODE);
                            conn.setConnectTimeout(120000); // 2分钟连接超时
                            conn.setReadTimeout(300000);    // 5分钟读取超时
                            conn.connect();
                            
                            // 获取响应状态码
                            int responseCode = conn.getResponseCode();
                            System.out.println("同步响应状态码: " + responseCode);
                            
                            // 读取响应内容
                            StringBuilder response = new StringBuilder();
                            try (BufferedReader reader = new BufferedReader(
                                    new InputStreamReader(
                                            responseCode >= 200 && responseCode < 300 
                                            ? conn.getInputStream() 
                                            : conn.getErrorStream(), "UTF-8"))) {
                                String line;
                                while ((line = reader.readLine()) != null) {
                                    response.append(line);
                                }
                            }
                            
                            String responseText = response.toString();
                            System.out.println("同步API响应: " + responseText);
                            
                            // 解析响应JSON
                            if (!responseText.isEmpty()) {
                                try {
                                    JSONObject jsonObject = JSON.parseObject(responseText);
                                    Integer code = jsonObject.getInteger("code");
                                    if (code != null && code == 1) {
                                        String message = jsonObject.getString("message");
                                        log("成功同步 " + routeName + " 的 " + currentDate + " 数据: " + message);
                                        
                                        // 尝试从消息中提取数量
                                        try {
                                            String countStr = message.replaceAll("[^0-9]", "");
                                            if (!countStr.isEmpty()) {
                                                int count = Integer.parseInt(countStr);
                                                totalSyncCount.addAndGet(count); // 累加同步数量
                                            }
                                        } catch (Exception e) {
                                            // 如果提取数量失败，忽略错误
                                            System.out.println("无法从消息中提取数量: " + message);
                                        }
                                    } else {
                                        String message = jsonObject.getString("message");
                                        log("同步 " + routeName + " 的 " + currentDate + " 数据失败: " + message);
                                    }
                                } catch (Exception e) {
                                    log("解析响应失败: " + e.getMessage());
                                }
                            } else {
                                log("同步 " + routeName + " 的 " + currentDate + " 数据: 0条");
                            }
                        } catch (Exception ex) {
                            System.err.println("同步任务异常: " + ex.getMessage());
                            ex.printStackTrace();
                            log("同步 " + routeName + " 的 " + currentDate + " 数据异常: " + ex.getMessage());
                        } finally {
                            // 更新进度
                            int completed = completedTasks.incrementAndGet();
                            int progressValue = (int)((completed / (double)totalTasks) * 100);
                            
                            SwingUtilities.invokeLater(() -> {
                                progressBar.setValue(progressValue);
                                progressBar.setString(String.format("%d%% (%d/%d)", progressValue, completed, totalTasks));
                                
                                // 如果所有任务完成，重置按钮状态
                                if (completed >= totalTasks) {
                                    log("同步任务全部完成，共同步 " + totalSyncCount.get() + " 条数据");
                                    progressBar.setValue(100); // 确保进度条显示100%
                                    progressBar.setString("100% 完成，共同步 " + totalSyncCount.get() + " 条数据");
                                    stopSync();
                                }
                            });
                        }
                    });
                }
            }

            // 串行执行所有任务
            executeTasksSequentially(syncTasks, totalTasks, completedTasks, totalSyncCount);
            
        } catch (Exception ex) {
            log("解析日期失败: " + ex.getMessage());
            JOptionPane.showMessageDialog(this, "日期格式不正确，请使用yyyy-MM-dd格式", "错误", JOptionPane.ERROR_MESSAGE);
            stopSync();
        }
    }

    /**
     * 串行执行同步任务
     */
    private void executeTasksSequentially(List<Runnable> tasks, int totalTasks,
                                        AtomicInteger completedTasks, AtomicInteger totalSyncCount) {
        if (tasks.isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                log("没有需要执行的同步任务");
                stopSync();
            });
            return;
        }

        // 递归执行任务
        executeNextTask(tasks, 0, totalTasks, completedTasks, totalSyncCount);
    }

    /**
     * 执行下一个任务
     */
    private void executeNextTask(List<Runnable> tasks, int currentIndex, int totalTasks,
                               AtomicInteger completedTasks, AtomicInteger totalSyncCount) {
        if (currentIndex >= tasks.size() || !isSyncing) {
            // 所有任务完成或同步被停止
            SwingUtilities.invokeLater(() -> {
                if (currentIndex >= tasks.size()) {
                    log("同步任务全部完成，共同步 " + totalSyncCount.get() + " 条数据");
                    progressBar.setValue(100);
                    progressBar.setString("100% 完成，共同步 " + totalSyncCount.get() + " 条数据");
                }
                stopSync();
            });
            return;
        }

        // 提交当前任务
        executor.submit(() -> {
            try {
                // 执行当前任务
                tasks.get(currentIndex).run();
            } catch (Exception e) {
                log("任务执行异常: " + e.getMessage());
            } finally {
                // 执行下一个任务
                if (isSyncing) {
                    executeNextTask(tasks, currentIndex + 1, totalTasks, completedTasks, totalSyncCount);
                }
            }
        });
    }

    /**
     * 停止同步
     */
    private void stopSync() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdownNow();
        }
        
        isSyncing = false;
        syncButton.setText("同步病害");
        
        if (progressBar.getValue() < 100) {
            progressBar.setString("已停止 - " + progressBar.getValue() + "%");
            log("同步任务已停止");
        } else {
            progressBar.setValue(100); // 确保进度条显示100%
            progressBar.setString("同步完成 - 100%");
            log("同步任务全部完成");
        }
    }
    
    /**
     * 生成检查单按钮点击事件
     */
    private void onGenerateReportButtonClick(ActionEvent e) {
        List<RouteItem> selectedRoutes = routeList.getSelectedValuesList();
        if (selectedRoutes.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请选择至少一个路段", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String startDate = startDateField.getText();
        String endDate = endDateField.getText();
        
        if (startDate.isEmpty() || endDate.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入开始日期和结束日期", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            
            if (start.isAfter(end)) {
                JOptionPane.showMessageDialog(this, "开始日期不能大于结束日期", "提示", JOptionPane.WARNING_MESSAGE);
                return;
            }
            
            // 显示生成检查单进度
            progressBar.setValue(0);
            progressBar.setString("准备生成检查单...");
            
            log("开始生成检查单");
            log("选择的路段: " + selectedRoutes.stream().map(RouteItem::getName).collect(Collectors.joining(", ")));
            log("日期区间: " + startDate + " 至 " + endDate);
            
            // 创建进度对话框
            JDialog progressDialog = new JDialog(this, "生成检查单", true);
            progressDialog.setLayout(new BorderLayout());
            progressDialog.setSize(300, 150);
            progressDialog.setLocationRelativeTo(this);
            
            JProgressBar dialogProgressBar = new JProgressBar(0, 100);
            dialogProgressBar.setStringPainted(true);
            dialogProgressBar.setString("准备生成检查单...");
            dialogProgressBar.setIndeterminate(true);
            
            JPanel progressPanel = new JPanel(new BorderLayout(10, 10));
            progressPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
            progressPanel.add(new JLabel("正在生成检查单，请稍候..."), BorderLayout.NORTH);
            progressPanel.add(dialogProgressBar, BorderLayout.CENTER);
            
            progressDialog.add(progressPanel);
            
            // 创建任务
            final SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
                @Override
                protected Void doInBackground() throws Exception {
                    try {
                        // 更新进度条状态
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setValue(10);
                            progressBar.setString("准备生成检查单...");
                            dialogProgressBar.setString("准备生成检查单...");
                        });
                        
                        // 准备请求URL
                        URL url = new URL(baseUrl + "/mems/auto/defect-to-finsp/batch-convert-byInfo");
                        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                        conn.setRequestMethod("POST");
                        conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
                        conn.setRequestProperty("userCode", USER_CODE);
                        conn.setDoOutput(true);
                        conn.setConnectTimeout(60000);  // 增加到1分钟连接超时
                        conn.setReadTimeout(300000);    // 增加到5分钟读取超时
                        
                        // 构建请求参数
                        JSONObject requestBody = new JSONObject();
                        requestBody.put("startDate", startDate);
                        requestBody.put("endDate", endDate);
                        
                        // 添加路段列表
                        JSONArray routeArray = new JSONArray();
                        for (RouteItem route : selectedRoutes) {
                            routeArray.add(route.getName());
                        }
                        requestBody.put("routes", routeArray);
                        
                        log("发送生成检查单请求: " + requestBody.toJSONString());
                        
                        // 发送请求
                        try (java.io.OutputStream os = conn.getOutputStream()) {
                            byte[] input = requestBody.toJSONString().getBytes("UTF-8");
                            os.write(input, 0, input.length);
                        }
                        
                        // 更新进度
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setValue(30);
                            progressBar.setString("正在生成检查单...");
                            dialogProgressBar.setString("正在生成检查单...");
                        });
                        
                        // 获取响应
                        int responseCode = conn.getResponseCode();
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(
                                        responseCode >= 200 && responseCode < 300 
                                        ? conn.getInputStream() 
                                        : conn.getErrorStream(), "UTF-8"))) {
                            String line;
                            while ((line = reader.readLine()) != null) {
                                response.append(line);
                            }
                        }
                        
                        // 更新进度
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setValue(80);
                            progressBar.setString("处理检查单结果...");
                            dialogProgressBar.setString("处理检查单结果...");
                        });
                        
                        String responseText = response.toString();
                        log("生成检查单响应: " + responseText);
                        
                        // 解析响应
                        String resultMessage;
                        boolean isSuccess = false;
                        if (!responseText.isEmpty()) {
                            try {
                                JSONObject jsonObject = JSON.parseObject(responseText);
                                Integer code = jsonObject.getInteger("code");
                                String message = jsonObject.getString("message");
                                
                                // 检查响应格式
                                // 如果是 {"code":200,"msg":"操作成功","data":{...}} 格式
                                if (code != null && code == 200 && jsonObject.containsKey("data")) {
                                    JSONObject data = jsonObject.getJSONObject("data");
                                    if (data != null) {
                                        // 使用data中的message
                                        String dataMessage = data.getString("message");
                                        resultMessage = dataMessage != null ? dataMessage : "操作成功";
                                    } else {
                                        resultMessage = "操作成功";
                                    }
                                    isSuccess = true;
                                }
                                // 如果是 {"code":1,"message":"已生成1条检查单..."} 格式
                                else if (code != null && code == 1 && message != null) {
                                    resultMessage = message;
                                    isSuccess = true;
                                }
                                // 其他情况视为失败
                                else {
                                    String msg = jsonObject.getString("msg");
                                    resultMessage = "失败: " + (msg != null ? msg : (message != null ? message : "未知错误"));
                                    isSuccess = false;
                                }
                            } catch (Exception e) {
                                log("解析响应失败: " + e.getMessage());
                                resultMessage = "解析响应失败: " + e.getMessage();
                                isSuccess = false;
                            }
                        } else {
                            resultMessage = "失败: 服务器没有返回数据";
                            isSuccess = false;
                        }
                        
                        // 更新最终结果
                        final boolean finalIsSuccess = isSuccess;
                        final String finalResultMessage = resultMessage;
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setValue(100);
                            progressBar.setString("检查单生成完成");
                            dialogProgressBar.setString("检查单生成完成");
                            dialogProgressBar.setValue(100);
                            dialogProgressBar.setIndeterminate(false);
                            log("检查单生成结果: " + finalResultMessage);
                            
                            // 关闭进度对话框
                            progressDialog.dispose();
                            
                            // 显示结果对话框
                            if (finalIsSuccess) {
                                JOptionPane.showMessageDialog(OrderGUI.this, finalResultMessage, "成功", JOptionPane.INFORMATION_MESSAGE);
                            } else {
                                JOptionPane.showMessageDialog(OrderGUI.this, finalResultMessage, "错误", JOptionPane.ERROR_MESSAGE);
                            }
                        });
                    } catch (java.net.SocketTimeoutException e) {
                        log("生成检查单超时: " + e.getMessage());
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setString("生成检查单超时");
                            dialogProgressBar.setString("生成检查单超时");
                            dialogProgressBar.setIndeterminate(false);
                            progressDialog.dispose();
                            JOptionPane.showMessageDialog(OrderGUI.this, "生成检查单超时，请稍后重试", "超时", JOptionPane.WARNING_MESSAGE);
                        });
                    } catch (Exception e) {
                        log("生成检查单异常: " + e.getMessage());
                        e.printStackTrace();
                        
                        SwingUtilities.invokeLater(() -> {
                            progressBar.setString("生成检查单失败");
                            dialogProgressBar.setString("生成检查单失败");
                            dialogProgressBar.setIndeterminate(false);
                            progressDialog.dispose();
                            JOptionPane.showMessageDialog(OrderGUI.this, "生成检查单失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                        });
                    }
                    return null;
                }
            };
            
            // 启动任务
            worker.execute();
            
            // 显示进度对话框
            progressDialog.setVisible(true);
            
        } catch (Exception ex) {
            log("解析日期失败: " + ex.getMessage());
            JOptionPane.showMessageDialog(this, "日期格式不正确，请使用yyyy-MM-dd格式", "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 获取两个日期之间的所有日期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期列表，格式为yyyy-MM-dd
     */
    private List<String> getDateRange(LocalDate startDate, LocalDate endDate) {
        List<String> dates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        
        return dates;
    }

    /**
     * 导出日志按钮点击事件
     */
    private void onExportLogButtonClick(ActionEvent e) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("保存日志文件");
        int userSelection = fileChooser.showSaveDialog(this);
        
        if (userSelection == JFileChooser.APPROVE_OPTION) {
            try {
                java.io.FileWriter writer = new java.io.FileWriter(fileChooser.getSelectedFile());
                writer.write(logTextArea.getText());
                writer.close();
                JOptionPane.showMessageDialog(this, "日志已导出", "成功", JOptionPane.INFORMATION_MESSAGE);
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(this, "导出日志失败: " + ex.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 添加日志
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            logTextArea.append(timestamp + " - " + message + "\n");
        });
    }

    /**
     * 程序入口
     */
    public static void main(String[] args) {
        try {
            // 使用跨平台外观，确保自定义颜色生效
            UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());
            
            // 修改默认按钮UI表现
            UIManager.put("Button.background", new Color(240, 240, 240));
            UIManager.put("Button.foreground", Color.BLACK);
            UIManager.put("Button.border", BorderFactory.createRaisedBevelBorder());
            
            // 设置全局字体
            setUIFont(new javax.swing.plaf.FontUIResource("Microsoft YaHei", Font.PLAIN, 13));
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        SwingUtilities.invokeLater(() -> {
            OrderGUI gui = new OrderGUI();
            gui.setVisible(true);
        });
    }
    
    /**
     * 设置全局UI字体
     */
    public static void setUIFont(javax.swing.plaf.FontUIResource f) {
        Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof javax.swing.plaf.FontUIResource) {
                UIManager.put(key, f);
            }
        }
    }
    
    /**
     * 简单的字符串工具类
     */
    static class StringUtils {
        public static boolean isEmpty(String str) {
            return str == null || str.trim().isEmpty();
        }
    }
}
