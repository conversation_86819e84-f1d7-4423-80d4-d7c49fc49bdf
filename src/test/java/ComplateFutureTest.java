import com.baomidou.dynamic.datasource.annotation.DSTransactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class ComplateFutureTest {



    @DSTransactional(rollbackFor = Exception.class)
    public void addDdd(){


        List<CompletableFuture> futures = new ArrayList<CompletableFuture>();
        for (int i = 0; i < 10; i++) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 执行数据库保存操作
            });
            futures.add(future);
        }

        futures.stream().map(CompletableFuture::join);

        // 另外的数据库操作
    }
}
